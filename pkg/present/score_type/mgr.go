package score_type

import (
	"context"
	anchorcontractgo "golang.52tt.com/clients/anchorcontract-go"
	channelLiveMgr "golang.52tt.com/clients/channel-live-mgr"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/present/score_type/guildMaster"
	anchorcontract_go "golang.52tt.com/protocol/services/anchorcontract-go"
	"golang.52tt.com/protocol/services/channellivemgr"
	"google.golang.org/grpc"
	"sync"
	"time"
)

type ScoreTypeMgr struct {
	guildMasterMgr   *guildMaster.Mgr
	anchorContactCli anchorcontractgo.IClient
	channelLiveCli   channelLiveMgr.IClient
}

func NewScoreTypeMgr() (*ScoreTypeMgr, error) {
	mgr := &ScoreTypeMgr{}
	guildMasterMgr, err := guildMaster.NewMgr()
	if err != nil {
		log.ErrorWithCtx(context.Background(), "NewScoreTypeMgr guildMaster mgr faild , %v", err)
		return mgr, err
	}

	channelLiveCli := channelLiveMgr.NewIClient(grpc.WithBlock())
	anchorContactCli := anchorcontractgo.NewIClient(grpc.WithBlock())

	mgr.guildMasterMgr = guildMasterMgr
	mgr.channelLiveCli = channelLiveCli
	mgr.anchorContactCli = anchorContactCli

	return mgr, nil
}

func (s *ScoreTypeMgr) GetScoreTypeMap(ctx context.Context, itemId, sendUid uint32, realUidList []uint32) (scoreTypeMap map[uint32]uint32, identityTypeMap map[uint32]uint32, err error) {
	//签约信息
	wgAfter := sync.WaitGroup{}
	scoreTypeMap = make(map[uint32]uint32)
	identityTypeMap = make(map[uint32]uint32)

	wgAfter.Add(1)
	contractResp := &anchorcontract_go.BatchGetUserContractCacheInfoResp{}
	liveResp := &channellivemgr.BatGetChannelLiveInfoResp{}

	go func() {
		defer wgAfter.Done()

		var sErr error

		//
		contractResp, sErr = s.anchorContactCli.BatchGetUserContractCacheInfo(ctx, &anchorcontract_go.BatchGetUserContractCacheInfoReq{
			Uids: realUidList,
		})
		if sErr != nil {
			log.ErrorWithCtx(ctx, "preSendPresent - GetUsersMap fail , uid: %+v", sendUid)
			err = sErr
		}

	}()

	//直播信息
	wgAfter.Add(1)
	go func() {
		defer wgAfter.Done()

		var sErr error
		liveResp, sErr = s.channelLiveCli.BatGetChannelLiveInfo(ctx, &channellivemgr.BatGetChannelLiveInfoReq{UidList: realUidList})
		if sErr != nil {
			log.ErrorWithCtx(ctx, "preSendPresent - GetUsersMap fail , uid: %+v", sendUid)
			err = sErr
		}

	}()

	wgAfter.Wait()

	// 先给所有人默认赋值1

	for _, item := range realUidList {
		scoreTypeMap[item] = 1
	}

	// 签约
	for toUid, item := range contractResp.GetUid2Contractinfo() {
		if item.GetContract().GetExpireTime() > uint32(time.Now().Unix()) {
			scoreTypeMap[item.GetContract().GetActorUid()] = 0
			// 1,任意一种签约成员
			identityTypeMap[item.GetContract().GetActorUid()] = 1
			log.DebugWithCtx(ctx, "GetScoreTypeMap - contract uid: %v", item.GetContract().GetActorUid())
		}

		for _, info := range item.GetAnchorIdentityList() {
			if info == uint32(anchorcontract_go.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) {
				scoreTypeMap[toUid] = 0
				log.DebugWithCtx(ctx, "GetScoreTypeMap - doyen uid: %v", item.GetContract().GetActorUid())
			}

			if info == uint32(anchorcontract_go.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) && identityTypeMap[item.GetContract().GetActorUid()] == 0 {
				// 2,签的是达人
				identityTypeMap[toUid] = 2
			}
		}
	}

	for _, item := range liveResp.GetInfoList() {
		if item.GetEndTime() > uint32(time.Now().Unix()) {
			scoreTypeMap[item.GetUid()] = 0
			// 3，有直播权限
			identityTypeMap[item.GetUid()] = 3
			log.DebugWithCtx(ctx, "GetScoreTypeMap - live uid: %v", item.GetUid())
		}
	}

	// 合作库公会会长
	for _, item := range realUidList {
		if s.guildMasterMgr.IsGuildMaster(item) {
			scoreTypeMap[item] = 0
			// 4，是会长
			identityTypeMap[item] = 4
			log.DebugWithCtx(ctx, "GetScoreTypeMap - live uid: %v", item)
		}
	}

	return scoreTypeMap, identityTypeMap, err
}

func (s *ScoreTypeMgr) GetScoreTypeMapWithBusinessType(ctx context.Context, sendUid uint32, realUidList []uint32, businessType uint32) (scoreTypeMap map[uint32]uint32, identityTypeMap map[uint32]uint32, err error) {
	//签约信息
	wgAfter := sync.WaitGroup{}
	scoreTypeMap = make(map[uint32]uint32)
	identityTypeMap = make(map[uint32]uint32)

	wgAfter.Add(1)
	contractResp := &anchorcontract_go.BatchGetUserContractCacheInfoResp{}
	liveResp := &channellivemgr.BatGetChannelLiveInfoResp{}

	go func() {
		defer wgAfter.Done()

		var sErr error

		//
		contractResp, sErr = s.anchorContactCli.BatchGetUserContractCacheInfo(ctx, &anchorcontract_go.BatchGetUserContractCacheInfoReq{
			Uids: realUidList,
		})
		if sErr != nil {
			log.ErrorWithCtx(ctx, "preSendPresent - GetUsersMap fail , uid: %+v", sendUid)
			err = sErr
		}

	}()

	//直播信息
	wgAfter.Add(1)
	go func() {
		defer wgAfter.Done()

		var sErr error
		liveResp, sErr = s.channelLiveCli.BatGetChannelLiveInfo(ctx, &channellivemgr.BatGetChannelLiveInfoReq{UidList: realUidList})
		if sErr != nil {
			log.ErrorWithCtx(ctx, "preSendPresent - GetUsersMap fail , uid: %+v", sendUid)
			err = sErr
		}

	}()

	wgAfter.Wait()

	// 先给所有人默认赋值1

	for _, item := range realUidList {
		scoreTypeMap[item] = 1
	}

	for toUid, item := range contractResp.GetUid2Contractinfo() {
		if item.GetContract().GetExpireTime() > uint32(time.Now().Unix()) {
			scoreTypeMap[item.GetContract().GetActorUid()] = 0
			// 1,任意一种签约成员
			identityTypeMap[item.GetContract().GetActorUid()] = 1
			log.DebugWithCtx(ctx, "GetScoreTypeMap - contract uid: %v", item.GetContract().GetActorUid())
		}

		for _, info := range item.GetAnchorIdentityList() {
			if info == uint32(anchorcontract_go.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) && businessType == 0 {
				scoreTypeMap[toUid] = 0
				log.DebugWithCtx(ctx, "GetScoreTypeMap - doyen uid: %v", item.GetContract().GetActorUid())
			}
			if info == uint32(anchorcontract_go.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) && identityTypeMap[item.GetContract().GetActorUid()] == 0 {
				// 2,没签别的，签的是达人
				identityTypeMap[toUid] = 2
			}
		}
	}

	for _, item := range liveResp.GetInfoList() {
		if item.GetEndTime() > uint32(time.Now().Unix()) {
			scoreTypeMap[item.GetUid()] = 0
			// 3，有直播权限
			identityTypeMap[item.GetUid()] = 3
			log.DebugWithCtx(ctx, "GetScoreTypeMap - live uid: %v", item.GetUid())
		}
	}

	// 合作库公会会长
	for _, item := range realUidList {
		if s.guildMasterMgr.IsGuildMaster(item) {
			scoreTypeMap[item] = 0
			// 4，是会长
			identityTypeMap[item] = 4
			log.DebugWithCtx(ctx, "GetScoreTypeMap - live uid: %v", item)
		}
	}

	return scoreTypeMap, identityTypeMap, err
}
