sbin/*
.DS_Store
.AppleDouble
.LSOverride

.vscode/*

*.exe
*.exe~
*.dll
*.out
.idea
*.iml
Thumbs.db
ehthumbs.db
ehthumbs_vista.db
*.lnk

1/*
1c/*

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

.pytest_cache

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover

# Translations
*.mo
*.pot

# Django stuff:
*.log

# Sphinx documentation
docs/_build/

#submodule
#.gitmodules
#third-party/tt-protocol/

# PyBuilder
target/
services/account-appeal/account-appeal
services/account-appeal-http-logic/account-appeal-http-logic
services/account-appeal-http-logic/tool/auth/auth
services/account-appeal-http-logic/test
services/datacenter-report-http-logic/datacenter-report-http-logic
services/muti-cmd-tools/muti-cmd-tools
atools/what/main.go
atools/what/source/scanner.go
atools/what/utils/func.go
atools/what/what
services/gift-data-statistics/modules/modules.test
services/mypath/channel-live-mission/main.go
cmd/mergee-bot/main
services/channel-core-shield/channel-core-shield
services/channel-core-logic/channel-core-logic
services/channel-mic-logic-go/channel-mic-logic-go
services/official-live-channel/official-live-channel
clients/channel-msg-api/tools/tools
.stignore
.nocalhost/service/virtual-image-resource.yaml
custom-gcl
report.xml