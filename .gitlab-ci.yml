variables:
  GIT_DEPTH: 0 # 设置 GIT_DEPTH 为 0 以获取完整的提交历史

before_script:
  - |
    export LC_ALL=en_US.UTF-8
    export LANG="en_US.UTF-8"

stages:
  - lint

tt-lint:
  stage: lint
  script:
    - cd ${CI_PROJECT_DIR}
    - echo $(pwd)
    - /home/<USER>/golangci-lint.sh
    - echo "var $?"
  tags:
    - lint
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH != "release/tt_rel_prod"'
  artifacts:
    expire_in: 1 day
    when: always
    paths:
      - .ci_result_file        
