// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/present-middleware/present-middleware.proto

package present_middleware

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockPresentMiddlewareClient is a mock of PresentMiddlewareClient interface.
type MockPresentMiddlewareClient struct {
	ctrl     *gomock.Controller
	recorder *MockPresentMiddlewareClientMockRecorder
}

// MockPresentMiddlewareClientMockRecorder is the mock recorder for MockPresentMiddlewareClient.
type MockPresentMiddlewareClientMockRecorder struct {
	mock *MockPresentMiddlewareClient
}

// NewMockPresentMiddlewareClient creates a new mock instance.
func NewMockPresentMiddlewareClient(ctrl *gomock.Controller) *MockPresentMiddlewareClient {
	mock := &MockPresentMiddlewareClient{ctrl: ctrl}
	mock.recorder = &MockPresentMiddlewareClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPresentMiddlewareClient) EXPECT() *MockPresentMiddlewareClientMockRecorder {
	return m.recorder
}

// AiSendPresent mocks base method.
func (m *MockPresentMiddlewareClient) AiSendPresent(ctx context.Context, in *AiSendPresentReq, opts ...grpc.CallOption) (*AiSendPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AiSendPresent", varargs...)
	ret0, _ := ret[0].(*AiSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AiSendPresent indicates an expected call of AiSendPresent.
func (mr *MockPresentMiddlewareClientMockRecorder) AiSendPresent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AiSendPresent", reflect.TypeOf((*MockPresentMiddlewareClient)(nil).AiSendPresent), varargs...)
}

// AllMicSendPresent mocks base method.
func (m *MockPresentMiddlewareClient) AllMicSendPresent(ctx context.Context, in *AllMicSendPresentReq, opts ...grpc.CallOption) (*AllMicSendPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AllMicSendPresent", varargs...)
	ret0, _ := ret[0].(*AllMicSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllMicSendPresent indicates an expected call of AllMicSendPresent.
func (mr *MockPresentMiddlewareClientMockRecorder) AllMicSendPresent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllMicSendPresent", reflect.TypeOf((*MockPresentMiddlewareClient)(nil).AllMicSendPresent), varargs...)
}

// BatchSendPresent mocks base method.
func (m *MockPresentMiddlewareClient) BatchSendPresent(ctx context.Context, in *BatchSendPresentReq, opts ...grpc.CallOption) (*BatchSendPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchSendPresent", varargs...)
	ret0, _ := ret[0].(*BatchSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchSendPresent indicates an expected call of BatchSendPresent.
func (mr *MockPresentMiddlewareClientMockRecorder) BatchSendPresent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSendPresent", reflect.TypeOf((*MockPresentMiddlewareClient)(nil).BatchSendPresent), varargs...)
}

// DelPresentConfig mocks base method.
func (m *MockPresentMiddlewareClient) DelPresentConfig(ctx context.Context, in *DelPresentConfigReq, opts ...grpc.CallOption) (*DelPresentConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelPresentConfig", varargs...)
	ret0, _ := ret[0].(*DelPresentConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentConfig indicates an expected call of DelPresentConfig.
func (mr *MockPresentMiddlewareClientMockRecorder) DelPresentConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentConfig", reflect.TypeOf((*MockPresentMiddlewareClient)(nil).DelPresentConfig), varargs...)
}

// FellowSendPresent mocks base method.
func (m *MockPresentMiddlewareClient) FellowSendPresent(ctx context.Context, in *FellowSendPresentReq, opts ...grpc.CallOption) (*FellowSendPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FellowSendPresent", varargs...)
	ret0, _ := ret[0].(*FellowSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FellowSendPresent indicates an expected call of FellowSendPresent.
func (mr *MockPresentMiddlewareClientMockRecorder) FellowSendPresent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FellowSendPresent", reflect.TypeOf((*MockPresentMiddlewareClient)(nil).FellowSendPresent), varargs...)
}

// FixBackpackOrder mocks base method.
func (m *MockPresentMiddlewareClient) FixBackpackOrder(ctx context.Context, in *FixBackpackOrderReq, opts ...grpc.CallOption) (*FixBackpackOrderResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FixBackpackOrder", varargs...)
	ret0, _ := ret[0].(*FixBackpackOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FixBackpackOrder indicates an expected call of FixBackpackOrder.
func (mr *MockPresentMiddlewareClientMockRecorder) FixBackpackOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FixBackpackOrder", reflect.TypeOf((*MockPresentMiddlewareClient)(nil).FixBackpackOrder), varargs...)
}

// GetChannelPresentBox mocks base method.
func (m *MockPresentMiddlewareClient) GetChannelPresentBox(ctx context.Context, in *GetChannelPresentBoxReq, opts ...grpc.CallOption) (*GetChannelPresentBoxResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelPresentBox", varargs...)
	ret0, _ := ret[0].(*GetChannelPresentBoxResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelPresentBox indicates an expected call of GetChannelPresentBox.
func (mr *MockPresentMiddlewareClientMockRecorder) GetChannelPresentBox(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelPresentBox", reflect.TypeOf((*MockPresentMiddlewareClient)(nil).GetChannelPresentBox), varargs...)
}

// ImSendPresent mocks base method.
func (m *MockPresentMiddlewareClient) ImSendPresent(ctx context.Context, in *ImSendPresentReq, opts ...grpc.CallOption) (*ImSendPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ImSendPresent", varargs...)
	ret0, _ := ret[0].(*ImSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImSendPresent indicates an expected call of ImSendPresent.
func (mr *MockPresentMiddlewareClientMockRecorder) ImSendPresent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImSendPresent", reflect.TypeOf((*MockPresentMiddlewareClient)(nil).ImSendPresent), varargs...)
}

// MagicSendPresent mocks base method.
func (m *MockPresentMiddlewareClient) MagicSendPresent(ctx context.Context, in *MagicSendPresentReq, opts ...grpc.CallOption) (*MagicSendPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MagicSendPresent", varargs...)
	ret0, _ := ret[0].(*MagicSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MagicSendPresent indicates an expected call of MagicSendPresent.
func (mr *MockPresentMiddlewareClientMockRecorder) MagicSendPresent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MagicSendPresent", reflect.TypeOf((*MockPresentMiddlewareClient)(nil).MagicSendPresent), varargs...)
}

// SendPresent mocks base method.
func (m *MockPresentMiddlewareClient) SendPresent(ctx context.Context, in *SendPresentReq, opts ...grpc.CallOption) (*SendPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendPresent", varargs...)
	ret0, _ := ret[0].(*SendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPresent indicates an expected call of SendPresent.
func (mr *MockPresentMiddlewareClientMockRecorder) SendPresent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPresent", reflect.TypeOf((*MockPresentMiddlewareClient)(nil).SendPresent), varargs...)
}

// SetSendPresent mocks base method.
func (m *MockPresentMiddlewareClient) SetSendPresent(ctx context.Context, in *SetSendPresentReq, opts ...grpc.CallOption) (*SetSendPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetSendPresent", varargs...)
	ret0, _ := ret[0].(*SetSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSendPresent indicates an expected call of SetSendPresent.
func (mr *MockPresentMiddlewareClientMockRecorder) SetSendPresent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSendPresent", reflect.TypeOf((*MockPresentMiddlewareClient)(nil).SetSendPresent), varargs...)
}

// UnpackPresentBox mocks base method.
func (m *MockPresentMiddlewareClient) UnpackPresentBox(ctx context.Context, in *UnpackPresentBoxReq, opts ...grpc.CallOption) (*UnpackPresentBoxResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnpackPresentBox", varargs...)
	ret0, _ := ret[0].(*UnpackPresentBoxResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnpackPresentBox indicates an expected call of UnpackPresentBox.
func (mr *MockPresentMiddlewareClientMockRecorder) UnpackPresentBox(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnpackPresentBox", reflect.TypeOf((*MockPresentMiddlewareClient)(nil).UnpackPresentBox), varargs...)
}

// MockPresentMiddlewareServer is a mock of PresentMiddlewareServer interface.
type MockPresentMiddlewareServer struct {
	ctrl     *gomock.Controller
	recorder *MockPresentMiddlewareServerMockRecorder
}

// MockPresentMiddlewareServerMockRecorder is the mock recorder for MockPresentMiddlewareServer.
type MockPresentMiddlewareServerMockRecorder struct {
	mock *MockPresentMiddlewareServer
}

// NewMockPresentMiddlewareServer creates a new mock instance.
func NewMockPresentMiddlewareServer(ctrl *gomock.Controller) *MockPresentMiddlewareServer {
	mock := &MockPresentMiddlewareServer{ctrl: ctrl}
	mock.recorder = &MockPresentMiddlewareServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPresentMiddlewareServer) EXPECT() *MockPresentMiddlewareServerMockRecorder {
	return m.recorder
}

// AiSendPresent mocks base method.
func (m *MockPresentMiddlewareServer) AiSendPresent(ctx context.Context, in *AiSendPresentReq) (*AiSendPresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AiSendPresent", ctx, in)
	ret0, _ := ret[0].(*AiSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AiSendPresent indicates an expected call of AiSendPresent.
func (mr *MockPresentMiddlewareServerMockRecorder) AiSendPresent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AiSendPresent", reflect.TypeOf((*MockPresentMiddlewareServer)(nil).AiSendPresent), ctx, in)
}

// AllMicSendPresent mocks base method.
func (m *MockPresentMiddlewareServer) AllMicSendPresent(ctx context.Context, in *AllMicSendPresentReq) (*AllMicSendPresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AllMicSendPresent", ctx, in)
	ret0, _ := ret[0].(*AllMicSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllMicSendPresent indicates an expected call of AllMicSendPresent.
func (mr *MockPresentMiddlewareServerMockRecorder) AllMicSendPresent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllMicSendPresent", reflect.TypeOf((*MockPresentMiddlewareServer)(nil).AllMicSendPresent), ctx, in)
}

// BatchSendPresent mocks base method.
func (m *MockPresentMiddlewareServer) BatchSendPresent(ctx context.Context, in *BatchSendPresentReq) (*BatchSendPresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSendPresent", ctx, in)
	ret0, _ := ret[0].(*BatchSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchSendPresent indicates an expected call of BatchSendPresent.
func (mr *MockPresentMiddlewareServerMockRecorder) BatchSendPresent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSendPresent", reflect.TypeOf((*MockPresentMiddlewareServer)(nil).BatchSendPresent), ctx, in)
}

// DelPresentConfig mocks base method.
func (m *MockPresentMiddlewareServer) DelPresentConfig(ctx context.Context, in *DelPresentConfigReq) (*DelPresentConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPresentConfig", ctx, in)
	ret0, _ := ret[0].(*DelPresentConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentConfig indicates an expected call of DelPresentConfig.
func (mr *MockPresentMiddlewareServerMockRecorder) DelPresentConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentConfig", reflect.TypeOf((*MockPresentMiddlewareServer)(nil).DelPresentConfig), ctx, in)
}

// FellowSendPresent mocks base method.
func (m *MockPresentMiddlewareServer) FellowSendPresent(ctx context.Context, in *FellowSendPresentReq) (*FellowSendPresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FellowSendPresent", ctx, in)
	ret0, _ := ret[0].(*FellowSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FellowSendPresent indicates an expected call of FellowSendPresent.
func (mr *MockPresentMiddlewareServerMockRecorder) FellowSendPresent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FellowSendPresent", reflect.TypeOf((*MockPresentMiddlewareServer)(nil).FellowSendPresent), ctx, in)
}

// FixBackpackOrder mocks base method.
func (m *MockPresentMiddlewareServer) FixBackpackOrder(ctx context.Context, in *FixBackpackOrderReq) (*FixBackpackOrderResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FixBackpackOrder", ctx, in)
	ret0, _ := ret[0].(*FixBackpackOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FixBackpackOrder indicates an expected call of FixBackpackOrder.
func (mr *MockPresentMiddlewareServerMockRecorder) FixBackpackOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FixBackpackOrder", reflect.TypeOf((*MockPresentMiddlewareServer)(nil).FixBackpackOrder), ctx, in)
}

// GetChannelPresentBox mocks base method.
func (m *MockPresentMiddlewareServer) GetChannelPresentBox(ctx context.Context, in *GetChannelPresentBoxReq) (*GetChannelPresentBoxResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelPresentBox", ctx, in)
	ret0, _ := ret[0].(*GetChannelPresentBoxResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelPresentBox indicates an expected call of GetChannelPresentBox.
func (mr *MockPresentMiddlewareServerMockRecorder) GetChannelPresentBox(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelPresentBox", reflect.TypeOf((*MockPresentMiddlewareServer)(nil).GetChannelPresentBox), ctx, in)
}

// ImSendPresent mocks base method.
func (m *MockPresentMiddlewareServer) ImSendPresent(ctx context.Context, in *ImSendPresentReq) (*ImSendPresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImSendPresent", ctx, in)
	ret0, _ := ret[0].(*ImSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImSendPresent indicates an expected call of ImSendPresent.
func (mr *MockPresentMiddlewareServerMockRecorder) ImSendPresent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImSendPresent", reflect.TypeOf((*MockPresentMiddlewareServer)(nil).ImSendPresent), ctx, in)
}

// MagicSendPresent mocks base method.
func (m *MockPresentMiddlewareServer) MagicSendPresent(ctx context.Context, in *MagicSendPresentReq) (*MagicSendPresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MagicSendPresent", ctx, in)
	ret0, _ := ret[0].(*MagicSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MagicSendPresent indicates an expected call of MagicSendPresent.
func (mr *MockPresentMiddlewareServerMockRecorder) MagicSendPresent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MagicSendPresent", reflect.TypeOf((*MockPresentMiddlewareServer)(nil).MagicSendPresent), ctx, in)
}

// SendPresent mocks base method.
func (m *MockPresentMiddlewareServer) SendPresent(ctx context.Context, in *SendPresentReq) (*SendPresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendPresent", ctx, in)
	ret0, _ := ret[0].(*SendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPresent indicates an expected call of SendPresent.
func (mr *MockPresentMiddlewareServerMockRecorder) SendPresent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPresent", reflect.TypeOf((*MockPresentMiddlewareServer)(nil).SendPresent), ctx, in)
}

// SetSendPresent mocks base method.
func (m *MockPresentMiddlewareServer) SetSendPresent(ctx context.Context, in *SetSendPresentReq) (*SetSendPresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSendPresent", ctx, in)
	ret0, _ := ret[0].(*SetSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSendPresent indicates an expected call of SetSendPresent.
func (mr *MockPresentMiddlewareServerMockRecorder) SetSendPresent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSendPresent", reflect.TypeOf((*MockPresentMiddlewareServer)(nil).SetSendPresent), ctx, in)
}

// UnpackPresentBox mocks base method.
func (m *MockPresentMiddlewareServer) UnpackPresentBox(ctx context.Context, in *UnpackPresentBoxReq) (*UnpackPresentBoxResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnpackPresentBox", ctx, in)
	ret0, _ := ret[0].(*UnpackPresentBoxResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnpackPresentBox indicates an expected call of UnpackPresentBox.
func (mr *MockPresentMiddlewareServerMockRecorder) UnpackPresentBox(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnpackPresentBox", reflect.TypeOf((*MockPresentMiddlewareServer)(nil).UnpackPresentBox), ctx, in)
}
