// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-live-http-logic/channel-live-http-logic.proto

package channel_live_http_logic // import "golang.52tt.com/protocol/services/channel-live-http-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AnchorCheckUpgradeType int32

const (
	AnchorCheckUpgradeType_AnchorCheckUpgradeType_Disable AnchorCheckUpgradeType = 0
	AnchorCheckUpgradeType_AnchorCheckUpgradeType_Enable  AnchorCheckUpgradeType = 1
	AnchorCheckUpgradeType_AnchorCheckUpgradeType_Used    AnchorCheckUpgradeType = 2
)

var AnchorCheckUpgradeType_name = map[int32]string{
	0: "AnchorCheckUpgradeType_Disable",
	1: "AnchorCheckUpgradeType_Enable",
	2: "AnchorCheckUpgradeType_Used",
}
var AnchorCheckUpgradeType_value = map[string]int32{
	"AnchorCheckUpgradeType_Disable": 0,
	"AnchorCheckUpgradeType_Enable":  1,
	"AnchorCheckUpgradeType_Used":    2,
}

func (x AnchorCheckUpgradeType) String() string {
	return proto.EnumName(AnchorCheckUpgradeType_name, int32(x))
}
func (AnchorCheckUpgradeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{0}
}

// 报名状态类型
type RegisterStatus int32

const (
	RegisterStatus_RegisterStatusInvalid      RegisterStatus = 0
	RegisterStatus_RegisterStatusCan          RegisterStatus = 1
	RegisterStatus_RegisterStatusSuccess      RegisterStatus = 2
	RegisterStatus_RegisterStatusAlreadyOther RegisterStatus = 3
	RegisterStatus_RegisterStatusNoAct        RegisterStatus = 4
	RegisterStatus_RegisterStatusCancel       RegisterStatus = 5
	RegisterStatus_RegisterStatusNoOnTimeLive RegisterStatus = 6
	RegisterStatus_RegisterStatusReview       RegisterStatus = 7
)

var RegisterStatus_name = map[int32]string{
	0: "RegisterStatusInvalid",
	1: "RegisterStatusCan",
	2: "RegisterStatusSuccess",
	3: "RegisterStatusAlreadyOther",
	4: "RegisterStatusNoAct",
	5: "RegisterStatusCancel",
	6: "RegisterStatusNoOnTimeLive",
	7: "RegisterStatusReview",
}
var RegisterStatus_value = map[string]int32{
	"RegisterStatusInvalid":      0,
	"RegisterStatusCan":          1,
	"RegisterStatusSuccess":      2,
	"RegisterStatusAlreadyOther": 3,
	"RegisterStatusNoAct":        4,
	"RegisterStatusCancel":       5,
	"RegisterStatusNoOnTimeLive": 6,
	"RegisterStatusReview":       7,
}

func (x RegisterStatus) String() string {
	return proto.EnumName(RegisterStatus_name, int32(x))
}
func (RegisterStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{1}
}

// 获取主页信息 POST /channel-live/anchor-cert/anchorCertInit
type AnchorCertInitInfo struct {
	LastCheckLevel       string   `protobuf:"bytes,1,opt,name=last_check_level,json=lastCheckLevel,proto3" json:"last_check_level"`
	IsShowCertInfo       bool     `protobuf:"varint,2,opt,name=is_show_cert_info,json=isShowCertInfo,proto3" json:"is_show_cert_info"`
	IsCertKeepUpgrade    bool     `protobuf:"varint,3,opt,name=is_cert_keep_upgrade,json=isCertKeepUpgrade,proto3" json:"is_cert_keep_upgrade"`
	ItemName             string   `protobuf:"bytes,4,opt,name=item_name,json=itemName,proto3" json:"item_name"`
	BaseImgurl           string   `protobuf:"bytes,5,opt,name=base_imgurl,json=baseImgurl,proto3" json:"base_imgurl"`
	ShadowColor          string   `protobuf:"bytes,6,opt,name=shadow_color,json=shadowColor,proto3" json:"shadow_color"`
	AnchorTag            string   `protobuf:"bytes,7,opt,name=anchor_tag,json=anchorTag,proto3" json:"anchor_tag"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorCertInitInfo) Reset()         { *m = AnchorCertInitInfo{} }
func (m *AnchorCertInitInfo) String() string { return proto.CompactTextString(m) }
func (*AnchorCertInitInfo) ProtoMessage()    {}
func (*AnchorCertInitInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{0}
}
func (m *AnchorCertInitInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCertInitInfo.Unmarshal(m, b)
}
func (m *AnchorCertInitInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCertInitInfo.Marshal(b, m, deterministic)
}
func (dst *AnchorCertInitInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCertInitInfo.Merge(dst, src)
}
func (m *AnchorCertInitInfo) XXX_Size() int {
	return xxx_messageInfo_AnchorCertInitInfo.Size(m)
}
func (m *AnchorCertInitInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCertInitInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCertInitInfo proto.InternalMessageInfo

func (m *AnchorCertInitInfo) GetLastCheckLevel() string {
	if m != nil {
		return m.LastCheckLevel
	}
	return ""
}

func (m *AnchorCertInitInfo) GetIsShowCertInfo() bool {
	if m != nil {
		return m.IsShowCertInfo
	}
	return false
}

func (m *AnchorCertInitInfo) GetIsCertKeepUpgrade() bool {
	if m != nil {
		return m.IsCertKeepUpgrade
	}
	return false
}

func (m *AnchorCertInitInfo) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *AnchorCertInitInfo) GetBaseImgurl() string {
	if m != nil {
		return m.BaseImgurl
	}
	return ""
}

func (m *AnchorCertInitInfo) GetShadowColor() string {
	if m != nil {
		return m.ShadowColor
	}
	return ""
}

func (m *AnchorCertInitInfo) GetAnchorTag() string {
	if m != nil {
		return m.AnchorTag
	}
	return ""
}

// 获取考核信息 POST /channel-live/anchor-cert/getAnchorCheckInfo
type AnchorCheckInfo struct {
	CheckInfoList        []*AnchorCheckInfo_CheckData `protobuf:"bytes,1,rep,name=check_info_list,json=checkInfoList,proto3" json:"check_info_list"`
	CheckUpgradeStatus   uint32                       `protobuf:"varint,2,opt,name=check_upgrade_status,json=checkUpgradeStatus,proto3" json:"check_upgrade_status"`
	RemainDay            uint32                       `protobuf:"varint,3,opt,name=remain_day,json=remainDay,proto3" json:"remain_day"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *AnchorCheckInfo) Reset()         { *m = AnchorCheckInfo{} }
func (m *AnchorCheckInfo) String() string { return proto.CompactTextString(m) }
func (*AnchorCheckInfo) ProtoMessage()    {}
func (*AnchorCheckInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{1}
}
func (m *AnchorCheckInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCheckInfo.Unmarshal(m, b)
}
func (m *AnchorCheckInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCheckInfo.Marshal(b, m, deterministic)
}
func (dst *AnchorCheckInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCheckInfo.Merge(dst, src)
}
func (m *AnchorCheckInfo) XXX_Size() int {
	return xxx_messageInfo_AnchorCheckInfo.Size(m)
}
func (m *AnchorCheckInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCheckInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCheckInfo proto.InternalMessageInfo

func (m *AnchorCheckInfo) GetCheckInfoList() []*AnchorCheckInfo_CheckData {
	if m != nil {
		return m.CheckInfoList
	}
	return nil
}

func (m *AnchorCheckInfo) GetCheckUpgradeStatus() uint32 {
	if m != nil {
		return m.CheckUpgradeStatus
	}
	return 0
}

func (m *AnchorCheckInfo) GetRemainDay() uint32 {
	if m != nil {
		return m.RemainDay
	}
	return 0
}

type AnchorCheckInfo_CheckData struct {
	CheckLevel           string   `protobuf:"bytes,1,opt,name=check_level,json=checkLevel,proto3" json:"check_level"`
	CreateTime           string   `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorCheckInfo_CheckData) Reset()         { *m = AnchorCheckInfo_CheckData{} }
func (m *AnchorCheckInfo_CheckData) String() string { return proto.CompactTextString(m) }
func (*AnchorCheckInfo_CheckData) ProtoMessage()    {}
func (*AnchorCheckInfo_CheckData) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{1, 0}
}
func (m *AnchorCheckInfo_CheckData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCheckInfo_CheckData.Unmarshal(m, b)
}
func (m *AnchorCheckInfo_CheckData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCheckInfo_CheckData.Marshal(b, m, deterministic)
}
func (dst *AnchorCheckInfo_CheckData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCheckInfo_CheckData.Merge(dst, src)
}
func (m *AnchorCheckInfo_CheckData) XXX_Size() int {
	return xxx_messageInfo_AnchorCheckInfo_CheckData.Size(m)
}
func (m *AnchorCheckInfo_CheckData) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCheckInfo_CheckData.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCheckInfo_CheckData proto.InternalMessageInfo

func (m *AnchorCheckInfo_CheckData) GetCheckLevel() string {
	if m != nil {
		return m.CheckLevel
	}
	return ""
}

func (m *AnchorCheckInfo_CheckData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

// 申请考核升级 POST /channel-live/anchor-cert/applyAnchorCheckUpgrade
type AnchorCheckUpgradeInfo struct {
	RemainDay            uint32   `protobuf:"varint,1,opt,name=remain_day,json=remainDay,proto3" json:"remain_day"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorCheckUpgradeInfo) Reset()         { *m = AnchorCheckUpgradeInfo{} }
func (m *AnchorCheckUpgradeInfo) String() string { return proto.CompactTextString(m) }
func (*AnchorCheckUpgradeInfo) ProtoMessage()    {}
func (*AnchorCheckUpgradeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{2}
}
func (m *AnchorCheckUpgradeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCheckUpgradeInfo.Unmarshal(m, b)
}
func (m *AnchorCheckUpgradeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCheckUpgradeInfo.Marshal(b, m, deterministic)
}
func (dst *AnchorCheckUpgradeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCheckUpgradeInfo.Merge(dst, src)
}
func (m *AnchorCheckUpgradeInfo) XXX_Size() int {
	return xxx_messageInfo_AnchorCheckUpgradeInfo.Size(m)
}
func (m *AnchorCheckUpgradeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCheckUpgradeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCheckUpgradeInfo proto.InternalMessageInfo

func (m *AnchorCheckUpgradeInfo) GetRemainDay() uint32 {
	if m != nil {
		return m.RemainDay
	}
	return 0
}

// 获取认证等级信息 POST /channel-live/anchor-cert/getAnchorCertInfo
type AnchorCertInfo struct {
	AnchorTag             string                 `protobuf:"bytes,1,opt,name=anchor_tag,json=anchorTag,proto3" json:"anchor_tag"`
	ItemName              string                 `protobuf:"bytes,2,opt,name=item_name,json=itemName,proto3" json:"item_name"`
	BaseImgurl            string                 `protobuf:"bytes,3,opt,name=base_imgurl,json=baseImgurl,proto3" json:"base_imgurl"`
	ShadowColor           string                 `protobuf:"bytes,4,opt,name=shadow_color,json=shadowColor,proto3" json:"shadow_color"`
	MonthStats            *MonthStats            `protobuf:"bytes,5,opt,name=month_stats,json=monthStats,proto3" json:"month_stats"`
	AnchorCertUpgradeInfo *AnchorCertUpgradeInfo `protobuf:"bytes,6,opt,name=anchor_cert_upgrade_info,json=anchorCertUpgradeInfo,proto3" json:"anchor_cert_upgrade_info"`
	XXX_NoUnkeyedLiteral  struct{}               `json:"-"`
	XXX_unrecognized      []byte                 `json:"-"`
	XXX_sizecache         int32                  `json:"-"`
}

func (m *AnchorCertInfo) Reset()         { *m = AnchorCertInfo{} }
func (m *AnchorCertInfo) String() string { return proto.CompactTextString(m) }
func (*AnchorCertInfo) ProtoMessage()    {}
func (*AnchorCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{3}
}
func (m *AnchorCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCertInfo.Unmarshal(m, b)
}
func (m *AnchorCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCertInfo.Marshal(b, m, deterministic)
}
func (dst *AnchorCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCertInfo.Merge(dst, src)
}
func (m *AnchorCertInfo) XXX_Size() int {
	return xxx_messageInfo_AnchorCertInfo.Size(m)
}
func (m *AnchorCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCertInfo proto.InternalMessageInfo

func (m *AnchorCertInfo) GetAnchorTag() string {
	if m != nil {
		return m.AnchorTag
	}
	return ""
}

func (m *AnchorCertInfo) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *AnchorCertInfo) GetBaseImgurl() string {
	if m != nil {
		return m.BaseImgurl
	}
	return ""
}

func (m *AnchorCertInfo) GetShadowColor() string {
	if m != nil {
		return m.ShadowColor
	}
	return ""
}

func (m *AnchorCertInfo) GetMonthStats() *MonthStats {
	if m != nil {
		return m.MonthStats
	}
	return nil
}

func (m *AnchorCertInfo) GetAnchorCertUpgradeInfo() *AnchorCertUpgradeInfo {
	if m != nil {
		return m.AnchorCertUpgradeInfo
	}
	return nil
}

type MonthStats struct {
	MonthActiveDays      uint32   `protobuf:"varint,1,opt,name=month_active_days,json=monthActiveDays,proto3" json:"month_active_days"`
	MonthPlatformDays    uint32   `protobuf:"varint,2,opt,name=month_platform_days,json=monthPlatformDays,proto3" json:"month_platform_days"`
	MonthNewFansCnt      uint32   `protobuf:"varint,3,opt,name=month_new_fans_cnt,json=monthNewFansCnt,proto3" json:"month_new_fans_cnt"`
	MonthConsumerCnt     uint32   `protobuf:"varint,4,opt,name=month_consumer_cnt,json=monthConsumerCnt,proto3" json:"month_consumer_cnt"`
	MonthGiftValue       uint32   `protobuf:"varint,5,opt,name=month_gift_value,json=monthGiftValue,proto3" json:"month_gift_value"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MonthStats) Reset()         { *m = MonthStats{} }
func (m *MonthStats) String() string { return proto.CompactTextString(m) }
func (*MonthStats) ProtoMessage()    {}
func (*MonthStats) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{4}
}
func (m *MonthStats) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MonthStats.Unmarshal(m, b)
}
func (m *MonthStats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MonthStats.Marshal(b, m, deterministic)
}
func (dst *MonthStats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MonthStats.Merge(dst, src)
}
func (m *MonthStats) XXX_Size() int {
	return xxx_messageInfo_MonthStats.Size(m)
}
func (m *MonthStats) XXX_DiscardUnknown() {
	xxx_messageInfo_MonthStats.DiscardUnknown(m)
}

var xxx_messageInfo_MonthStats proto.InternalMessageInfo

func (m *MonthStats) GetMonthActiveDays() uint32 {
	if m != nil {
		return m.MonthActiveDays
	}
	return 0
}

func (m *MonthStats) GetMonthPlatformDays() uint32 {
	if m != nil {
		return m.MonthPlatformDays
	}
	return 0
}

func (m *MonthStats) GetMonthNewFansCnt() uint32 {
	if m != nil {
		return m.MonthNewFansCnt
	}
	return 0
}

func (m *MonthStats) GetMonthConsumerCnt() uint32 {
	if m != nil {
		return m.MonthConsumerCnt
	}
	return 0
}

func (m *MonthStats) GetMonthGiftValue() uint32 {
	if m != nil {
		return m.MonthGiftValue
	}
	return 0
}

type AnchorCertUpgradeInfo struct {
	TaskImgUrl           string   `protobuf:"bytes,1,opt,name=task_img_url,json=taskImgUrl,proto3" json:"task_img_url"`
	TaskName             string   `protobuf:"bytes,2,opt,name=task_name,json=taskName,proto3" json:"task_name"`
	TaskPlace            string   `protobuf:"bytes,3,opt,name=task_place,json=taskPlace,proto3" json:"task_place"`
	TaskInitialLevel     string   `protobuf:"bytes,4,opt,name=task_initial_level,json=taskInitialLevel,proto3" json:"task_initial_level"`
	TaskCheckType        string   `protobuf:"bytes,5,opt,name=task_check_type,json=taskCheckType,proto3" json:"task_check_type"`
	TaskCheckTime        string   `protobuf:"bytes,6,opt,name=task_check_time,json=taskCheckTime,proto3" json:"task_check_time"`
	TaskJumpUrl          string   `protobuf:"bytes,7,opt,name=task_jump_url,json=taskJumpUrl,proto3" json:"task_jump_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorCertUpgradeInfo) Reset()         { *m = AnchorCertUpgradeInfo{} }
func (m *AnchorCertUpgradeInfo) String() string { return proto.CompactTextString(m) }
func (*AnchorCertUpgradeInfo) ProtoMessage()    {}
func (*AnchorCertUpgradeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{5}
}
func (m *AnchorCertUpgradeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCertUpgradeInfo.Unmarshal(m, b)
}
func (m *AnchorCertUpgradeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCertUpgradeInfo.Marshal(b, m, deterministic)
}
func (dst *AnchorCertUpgradeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCertUpgradeInfo.Merge(dst, src)
}
func (m *AnchorCertUpgradeInfo) XXX_Size() int {
	return xxx_messageInfo_AnchorCertUpgradeInfo.Size(m)
}
func (m *AnchorCertUpgradeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCertUpgradeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCertUpgradeInfo proto.InternalMessageInfo

func (m *AnchorCertUpgradeInfo) GetTaskImgUrl() string {
	if m != nil {
		return m.TaskImgUrl
	}
	return ""
}

func (m *AnchorCertUpgradeInfo) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *AnchorCertUpgradeInfo) GetTaskPlace() string {
	if m != nil {
		return m.TaskPlace
	}
	return ""
}

func (m *AnchorCertUpgradeInfo) GetTaskInitialLevel() string {
	if m != nil {
		return m.TaskInitialLevel
	}
	return ""
}

func (m *AnchorCertUpgradeInfo) GetTaskCheckType() string {
	if m != nil {
		return m.TaskCheckType
	}
	return ""
}

func (m *AnchorCertUpgradeInfo) GetTaskCheckTime() string {
	if m != nil {
		return m.TaskCheckTime
	}
	return ""
}

func (m *AnchorCertUpgradeInfo) GetTaskJumpUrl() string {
	if m != nil {
		return m.TaskJumpUrl
	}
	return ""
}

// *****  官频报名 *****//
// 检查用户的报名入口信息
type CheckUserRegisterEntryReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserRegisterEntryReq) Reset()         { *m = CheckUserRegisterEntryReq{} }
func (m *CheckUserRegisterEntryReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserRegisterEntryReq) ProtoMessage()    {}
func (*CheckUserRegisterEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{6}
}
func (m *CheckUserRegisterEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserRegisterEntryReq.Unmarshal(m, b)
}
func (m *CheckUserRegisterEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserRegisterEntryReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserRegisterEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserRegisterEntryReq.Merge(dst, src)
}
func (m *CheckUserRegisterEntryReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserRegisterEntryReq.Size(m)
}
func (m *CheckUserRegisterEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserRegisterEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserRegisterEntryReq proto.InternalMessageInfo

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type CheckUserRegisterEntryResp struct {
	IsHasRegisEntry      bool     `protobuf:"varint,1,opt,name=isHasRegisEntry,proto3" json:"isHasRegisEntry"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserRegisterEntryResp) Reset()         { *m = CheckUserRegisterEntryResp{} }
func (m *CheckUserRegisterEntryResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserRegisterEntryResp) ProtoMessage()    {}
func (*CheckUserRegisterEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{7}
}
func (m *CheckUserRegisterEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserRegisterEntryResp.Unmarshal(m, b)
}
func (m *CheckUserRegisterEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserRegisterEntryResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserRegisterEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserRegisterEntryResp.Merge(dst, src)
}
func (m *CheckUserRegisterEntryResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserRegisterEntryResp.Size(m)
}
func (m *CheckUserRegisterEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserRegisterEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserRegisterEntryResp proto.InternalMessageInfo

func (m *CheckUserRegisterEntryResp) GetIsHasRegisEntry() bool {
	if m != nil {
		return m.IsHasRegisEntry
	}
	return false
}

// 场次排班信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type MatchSchedule struct {
	BeginTs              uint32   `protobuf:"varint,1,opt,name=beginTs,proto3" json:"beginTs"`
	EndTs                uint32   `protobuf:"varint,2,opt,name=endTs,proto3" json:"endTs"`
	IsCanRegister        bool     `protobuf:"varint,3,opt,name=isCanRegister,proto3" json:"isCanRegister"`
	IsAlreadyRegister    bool     `protobuf:"varint,4,opt,name=isAlreadyRegister,proto3" json:"isAlreadyRegister"`
	ScheduleId           uint32   `protobuf:"varint,5,opt,name=scheduleId,proto3" json:"scheduleId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MatchSchedule) Reset()         { *m = MatchSchedule{} }
func (m *MatchSchedule) String() string { return proto.CompactTextString(m) }
func (*MatchSchedule) ProtoMessage()    {}
func (*MatchSchedule) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{8}
}
func (m *MatchSchedule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MatchSchedule.Unmarshal(m, b)
}
func (m *MatchSchedule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MatchSchedule.Marshal(b, m, deterministic)
}
func (dst *MatchSchedule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MatchSchedule.Merge(dst, src)
}
func (m *MatchSchedule) XXX_Size() int {
	return xxx_messageInfo_MatchSchedule.Size(m)
}
func (m *MatchSchedule) XXX_DiscardUnknown() {
	xxx_messageInfo_MatchSchedule.DiscardUnknown(m)
}

var xxx_messageInfo_MatchSchedule proto.InternalMessageInfo

func (m *MatchSchedule) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *MatchSchedule) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *MatchSchedule) GetIsCanRegister() bool {
	if m != nil {
		return m.IsCanRegister
	}
	return false
}

func (m *MatchSchedule) GetIsAlreadyRegister() bool {
	if m != nil {
		return m.IsAlreadyRegister
	}
	return false
}

func (m *MatchSchedule) GetScheduleId() uint32 {
	if m != nil {
		return m.ScheduleId
	}
	return 0
}

// 场次信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type MatchInfo struct {
	MatchTs              uint32           `protobuf:"varint,1,opt,name=matchTs,proto3" json:"matchTs"`
	MatchName            string           `protobuf:"bytes,2,opt,name=matchName,proto3" json:"matchName"`
	ScheduleList         []*MatchSchedule `protobuf:"bytes,3,rep,name=schedule_list,json=scheduleList,proto3" json:"schedule_list"`
	MatchId              uint32           `protobuf:"varint,4,opt,name=matchId,proto3" json:"matchId"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *MatchInfo) Reset()         { *m = MatchInfo{} }
func (m *MatchInfo) String() string { return proto.CompactTextString(m) }
func (*MatchInfo) ProtoMessage()    {}
func (*MatchInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{9}
}
func (m *MatchInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MatchInfo.Unmarshal(m, b)
}
func (m *MatchInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MatchInfo.Marshal(b, m, deterministic)
}
func (dst *MatchInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MatchInfo.Merge(dst, src)
}
func (m *MatchInfo) XXX_Size() int {
	return xxx_messageInfo_MatchInfo.Size(m)
}
func (m *MatchInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MatchInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MatchInfo proto.InternalMessageInfo

func (m *MatchInfo) GetMatchTs() uint32 {
	if m != nil {
		return m.MatchTs
	}
	return 0
}

func (m *MatchInfo) GetMatchName() string {
	if m != nil {
		return m.MatchName
	}
	return ""
}

func (m *MatchInfo) GetScheduleList() []*MatchSchedule {
	if m != nil {
		return m.ScheduleList
	}
	return nil
}

func (m *MatchInfo) GetMatchId() uint32 {
	if m != nil {
		return m.MatchId
	}
	return 0
}

// 官频信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type OfficialChannelInfo struct {
	ChannelId            uint32     `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId"`
	ActName              string     `protobuf:"bytes,2,opt,name=actName,proto3" json:"actName"`
	ActTime              string     `protobuf:"bytes,3,opt,name=actTime,proto3" json:"actTime"`
	RegisterTs           int64      `protobuf:"varint,4,opt,name=registerTs,proto3" json:"registerTs"`
	RegisterCond         string     `protobuf:"bytes,5,opt,name=registerCond,proto3" json:"registerCond"`
	RegisterStatus       uint32     `protobuf:"varint,6,opt,name=registerStatus,proto3" json:"registerStatus"`
	RegisterMatch        *MatchInfo `protobuf:"bytes,7,opt,name=registerMatch,proto3" json:"registerMatch"`
	ActionExample        string     `protobuf:"bytes,8,opt,name=actionExample,proto3" json:"actionExample"`
	AudioExample         string     `protobuf:"bytes,9,opt,name=audioExample,proto3" json:"audioExample"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *OfficialChannelInfo) Reset()         { *m = OfficialChannelInfo{} }
func (m *OfficialChannelInfo) String() string { return proto.CompactTextString(m) }
func (*OfficialChannelInfo) ProtoMessage()    {}
func (*OfficialChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{10}
}
func (m *OfficialChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfficialChannelInfo.Unmarshal(m, b)
}
func (m *OfficialChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfficialChannelInfo.Marshal(b, m, deterministic)
}
func (dst *OfficialChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfficialChannelInfo.Merge(dst, src)
}
func (m *OfficialChannelInfo) XXX_Size() int {
	return xxx_messageInfo_OfficialChannelInfo.Size(m)
}
func (m *OfficialChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OfficialChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OfficialChannelInfo proto.InternalMessageInfo

func (m *OfficialChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OfficialChannelInfo) GetActName() string {
	if m != nil {
		return m.ActName
	}
	return ""
}

func (m *OfficialChannelInfo) GetActTime() string {
	if m != nil {
		return m.ActTime
	}
	return ""
}

func (m *OfficialChannelInfo) GetRegisterTs() int64 {
	if m != nil {
		return m.RegisterTs
	}
	return 0
}

func (m *OfficialChannelInfo) GetRegisterCond() string {
	if m != nil {
		return m.RegisterCond
	}
	return ""
}

func (m *OfficialChannelInfo) GetRegisterStatus() uint32 {
	if m != nil {
		return m.RegisterStatus
	}
	return 0
}

func (m *OfficialChannelInfo) GetRegisterMatch() *MatchInfo {
	if m != nil {
		return m.RegisterMatch
	}
	return nil
}

func (m *OfficialChannelInfo) GetActionExample() string {
	if m != nil {
		return m.ActionExample
	}
	return ""
}

func (m *OfficialChannelInfo) GetAudioExample() string {
	if m != nil {
		return m.AudioExample
	}
	return ""
}

// 获取报名页的官频列表
type GetRegisterOfficialChListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRegisterOfficialChListReq) Reset()         { *m = GetRegisterOfficialChListReq{} }
func (m *GetRegisterOfficialChListReq) String() string { return proto.CompactTextString(m) }
func (*GetRegisterOfficialChListReq) ProtoMessage()    {}
func (*GetRegisterOfficialChListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{11}
}
func (m *GetRegisterOfficialChListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRegisterOfficialChListReq.Unmarshal(m, b)
}
func (m *GetRegisterOfficialChListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRegisterOfficialChListReq.Marshal(b, m, deterministic)
}
func (dst *GetRegisterOfficialChListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRegisterOfficialChListReq.Merge(dst, src)
}
func (m *GetRegisterOfficialChListReq) XXX_Size() int {
	return xxx_messageInfo_GetRegisterOfficialChListReq.Size(m)
}
func (m *GetRegisterOfficialChListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRegisterOfficialChListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRegisterOfficialChListReq proto.InternalMessageInfo

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetRegisterOfficialChListResp struct {
	InfoList             []*OfficialChannelInfo `protobuf:"bytes,1,rep,name=infoList,proto3" json:"infoList"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetRegisterOfficialChListResp) Reset()         { *m = GetRegisterOfficialChListResp{} }
func (m *GetRegisterOfficialChListResp) String() string { return proto.CompactTextString(m) }
func (*GetRegisterOfficialChListResp) ProtoMessage()    {}
func (*GetRegisterOfficialChListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{12}
}
func (m *GetRegisterOfficialChListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRegisterOfficialChListResp.Unmarshal(m, b)
}
func (m *GetRegisterOfficialChListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRegisterOfficialChListResp.Marshal(b, m, deterministic)
}
func (dst *GetRegisterOfficialChListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRegisterOfficialChListResp.Merge(dst, src)
}
func (m *GetRegisterOfficialChListResp) XXX_Size() int {
	return xxx_messageInfo_GetRegisterOfficialChListResp.Size(m)
}
func (m *GetRegisterOfficialChListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRegisterOfficialChListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRegisterOfficialChListResp proto.InternalMessageInfo

func (m *GetRegisterOfficialChListResp) GetInfoList() []*OfficialChannelInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 获取官频的场次信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetOfficialChMatchInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOfficialChMatchInfoReq) Reset()         { *m = GetOfficialChMatchInfoReq{} }
func (m *GetOfficialChMatchInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetOfficialChMatchInfoReq) ProtoMessage()    {}
func (*GetOfficialChMatchInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{13}
}
func (m *GetOfficialChMatchInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOfficialChMatchInfoReq.Unmarshal(m, b)
}
func (m *GetOfficialChMatchInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOfficialChMatchInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetOfficialChMatchInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOfficialChMatchInfoReq.Merge(dst, src)
}
func (m *GetOfficialChMatchInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetOfficialChMatchInfoReq.Size(m)
}
func (m *GetOfficialChMatchInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOfficialChMatchInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOfficialChMatchInfoReq proto.InternalMessageInfo

func (m *GetOfficialChMatchInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetOfficialChMatchInfoResp struct {
	MatchInfo            []*MatchInfo `protobuf:"bytes,1,rep,name=matchInfo,proto3" json:"matchInfo"`
	ActionExample        string       `protobuf:"bytes,2,opt,name=actionExample,proto3" json:"actionExample"`
	AudioExample         string       `protobuf:"bytes,3,opt,name=audioExample,proto3" json:"audioExample"`
	IntroExemple         string       `protobuf:"bytes,4,opt,name=introExemple,proto3" json:"introExemple"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetOfficialChMatchInfoResp) Reset()         { *m = GetOfficialChMatchInfoResp{} }
func (m *GetOfficialChMatchInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetOfficialChMatchInfoResp) ProtoMessage()    {}
func (*GetOfficialChMatchInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{14}
}
func (m *GetOfficialChMatchInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOfficialChMatchInfoResp.Unmarshal(m, b)
}
func (m *GetOfficialChMatchInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOfficialChMatchInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetOfficialChMatchInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOfficialChMatchInfoResp.Merge(dst, src)
}
func (m *GetOfficialChMatchInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetOfficialChMatchInfoResp.Size(m)
}
func (m *GetOfficialChMatchInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOfficialChMatchInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOfficialChMatchInfoResp proto.InternalMessageInfo

func (m *GetOfficialChMatchInfoResp) GetMatchInfo() []*MatchInfo {
	if m != nil {
		return m.MatchInfo
	}
	return nil
}

func (m *GetOfficialChMatchInfoResp) GetActionExample() string {
	if m != nil {
		return m.ActionExample
	}
	return ""
}

func (m *GetOfficialChMatchInfoResp) GetAudioExample() string {
	if m != nil {
		return m.AudioExample
	}
	return ""
}

func (m *GetOfficialChMatchInfoResp) GetIntroExemple() string {
	if m != nil {
		return m.IntroExemple
	}
	return ""
}

// 报名官频
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type RegisterOfficialChannelReq struct {
	ChannelId            uint32     `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId"`
	Introduction         string     `protobuf:"bytes,2,opt,name=introduction,proto3" json:"introduction"`
	MatchInfo            *MatchInfo `protobuf:"bytes,3,opt,name=matchInfo,proto3" json:"matchInfo"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *RegisterOfficialChannelReq) Reset()         { *m = RegisterOfficialChannelReq{} }
func (m *RegisterOfficialChannelReq) String() string { return proto.CompactTextString(m) }
func (*RegisterOfficialChannelReq) ProtoMessage()    {}
func (*RegisterOfficialChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{15}
}
func (m *RegisterOfficialChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegisterOfficialChannelReq.Unmarshal(m, b)
}
func (m *RegisterOfficialChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegisterOfficialChannelReq.Marshal(b, m, deterministic)
}
func (dst *RegisterOfficialChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegisterOfficialChannelReq.Merge(dst, src)
}
func (m *RegisterOfficialChannelReq) XXX_Size() int {
	return xxx_messageInfo_RegisterOfficialChannelReq.Size(m)
}
func (m *RegisterOfficialChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RegisterOfficialChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_RegisterOfficialChannelReq proto.InternalMessageInfo

func (m *RegisterOfficialChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RegisterOfficialChannelReq) GetIntroduction() string {
	if m != nil {
		return m.Introduction
	}
	return ""
}

func (m *RegisterOfficialChannelReq) GetMatchInfo() *MatchInfo {
	if m != nil {
		return m.MatchInfo
	}
	return nil
}

type RegisterOfficialChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegisterOfficialChannelResp) Reset()         { *m = RegisterOfficialChannelResp{} }
func (m *RegisterOfficialChannelResp) String() string { return proto.CompactTextString(m) }
func (*RegisterOfficialChannelResp) ProtoMessage()    {}
func (*RegisterOfficialChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{16}
}
func (m *RegisterOfficialChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegisterOfficialChannelResp.Unmarshal(m, b)
}
func (m *RegisterOfficialChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegisterOfficialChannelResp.Marshal(b, m, deterministic)
}
func (dst *RegisterOfficialChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegisterOfficialChannelResp.Merge(dst, src)
}
func (m *RegisterOfficialChannelResp) XXX_Size() int {
	return xxx_messageInfo_RegisterOfficialChannelResp.Size(m)
}
func (m *RegisterOfficialChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RegisterOfficialChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_RegisterOfficialChannelResp proto.InternalMessageInfo

// 取消报名
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type CancelRegisterOfficialChReq struct {
	ChannelId            uint32     `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId"`
	RegisterTs           int64      `protobuf:"varint,2,opt,name=registerTs,proto3" json:"registerTs"`
	MatchInfo            *MatchInfo `protobuf:"bytes,3,opt,name=matchInfo,proto3" json:"matchInfo"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CancelRegisterOfficialChReq) Reset()         { *m = CancelRegisterOfficialChReq{} }
func (m *CancelRegisterOfficialChReq) String() string { return proto.CompactTextString(m) }
func (*CancelRegisterOfficialChReq) ProtoMessage()    {}
func (*CancelRegisterOfficialChReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{17}
}
func (m *CancelRegisterOfficialChReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelRegisterOfficialChReq.Unmarshal(m, b)
}
func (m *CancelRegisterOfficialChReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelRegisterOfficialChReq.Marshal(b, m, deterministic)
}
func (dst *CancelRegisterOfficialChReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelRegisterOfficialChReq.Merge(dst, src)
}
func (m *CancelRegisterOfficialChReq) XXX_Size() int {
	return xxx_messageInfo_CancelRegisterOfficialChReq.Size(m)
}
func (m *CancelRegisterOfficialChReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelRegisterOfficialChReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelRegisterOfficialChReq proto.InternalMessageInfo

func (m *CancelRegisterOfficialChReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CancelRegisterOfficialChReq) GetRegisterTs() int64 {
	if m != nil {
		return m.RegisterTs
	}
	return 0
}

func (m *CancelRegisterOfficialChReq) GetMatchInfo() *MatchInfo {
	if m != nil {
		return m.MatchInfo
	}
	return nil
}

type CancelRegisterOfficialChResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelRegisterOfficialChResp) Reset()         { *m = CancelRegisterOfficialChResp{} }
func (m *CancelRegisterOfficialChResp) String() string { return proto.CompactTextString(m) }
func (*CancelRegisterOfficialChResp) ProtoMessage()    {}
func (*CancelRegisterOfficialChResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{18}
}
func (m *CancelRegisterOfficialChResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelRegisterOfficialChResp.Unmarshal(m, b)
}
func (m *CancelRegisterOfficialChResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelRegisterOfficialChResp.Marshal(b, m, deterministic)
}
func (dst *CancelRegisterOfficialChResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelRegisterOfficialChResp.Merge(dst, src)
}
func (m *CancelRegisterOfficialChResp) XXX_Size() int {
	return xxx_messageInfo_CancelRegisterOfficialChResp.Size(m)
}
func (m *CancelRegisterOfficialChResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelRegisterOfficialChResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelRegisterOfficialChResp proto.InternalMessageInfo

// 获取可申报时段, uri: /channel-live-http-logic/channel-live-show-list/GetShowTime
type GetShowTimeRequest struct {
	Datetime             uint32   `protobuf:"varint,1,opt,name=datetime,proto3" json:"datetime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShowTimeRequest) Reset()         { *m = GetShowTimeRequest{} }
func (m *GetShowTimeRequest) String() string { return proto.CompactTextString(m) }
func (*GetShowTimeRequest) ProtoMessage()    {}
func (*GetShowTimeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{19}
}
func (m *GetShowTimeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowTimeRequest.Unmarshal(m, b)
}
func (m *GetShowTimeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowTimeRequest.Marshal(b, m, deterministic)
}
func (dst *GetShowTimeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowTimeRequest.Merge(dst, src)
}
func (m *GetShowTimeRequest) XXX_Size() int {
	return xxx_messageInfo_GetShowTimeRequest.Size(m)
}
func (m *GetShowTimeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowTimeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowTimeRequest proto.InternalMessageInfo

func (m *GetShowTimeRequest) GetDatetime() uint32 {
	if m != nil {
		return m.Datetime
	}
	return 0
}

type GetShowTimeResponse struct {
	ShowTimeList           []*GetShowTimeResponse_ShowTime `protobuf:"bytes,1,rep,name=show_time_list,json=showTimeList,proto3" json:"show_time_list"`
	EarliestSelectableTime uint32                          `protobuf:"varint,2,opt,name=earliest_selectable_time,json=earliestSelectableTime,proto3" json:"earliest_selectable_time"`
	XXX_NoUnkeyedLiteral   struct{}                        `json:"-"`
	XXX_unrecognized       []byte                          `json:"-"`
	XXX_sizecache          int32                           `json:"-"`
}

func (m *GetShowTimeResponse) Reset()         { *m = GetShowTimeResponse{} }
func (m *GetShowTimeResponse) String() string { return proto.CompactTextString(m) }
func (*GetShowTimeResponse) ProtoMessage()    {}
func (*GetShowTimeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{20}
}
func (m *GetShowTimeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowTimeResponse.Unmarshal(m, b)
}
func (m *GetShowTimeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowTimeResponse.Marshal(b, m, deterministic)
}
func (dst *GetShowTimeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowTimeResponse.Merge(dst, src)
}
func (m *GetShowTimeResponse) XXX_Size() int {
	return xxx_messageInfo_GetShowTimeResponse.Size(m)
}
func (m *GetShowTimeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowTimeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowTimeResponse proto.InternalMessageInfo

func (m *GetShowTimeResponse) GetShowTimeList() []*GetShowTimeResponse_ShowTime {
	if m != nil {
		return m.ShowTimeList
	}
	return nil
}

func (m *GetShowTimeResponse) GetEarliestSelectableTime() uint32 {
	if m != nil {
		return m.EarliestSelectableTime
	}
	return 0
}

type GetShowTimeResponse_ShowTime struct {
	ShowStartTime        uint32   `protobuf:"varint,1,opt,name=show_start_time,json=showStartTime,proto3" json:"show_start_time"`
	ShowEndTime          uint32   `protobuf:"varint,2,opt,name=show_end_time,json=showEndTime,proto3" json:"show_end_time"`
	RemainCnt            uint32   `protobuf:"varint,3,opt,name=remain_cnt,json=remainCnt,proto3" json:"remain_cnt"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShowTimeResponse_ShowTime) Reset()         { *m = GetShowTimeResponse_ShowTime{} }
func (m *GetShowTimeResponse_ShowTime) String() string { return proto.CompactTextString(m) }
func (*GetShowTimeResponse_ShowTime) ProtoMessage()    {}
func (*GetShowTimeResponse_ShowTime) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{20, 0}
}
func (m *GetShowTimeResponse_ShowTime) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowTimeResponse_ShowTime.Unmarshal(m, b)
}
func (m *GetShowTimeResponse_ShowTime) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowTimeResponse_ShowTime.Marshal(b, m, deterministic)
}
func (dst *GetShowTimeResponse_ShowTime) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowTimeResponse_ShowTime.Merge(dst, src)
}
func (m *GetShowTimeResponse_ShowTime) XXX_Size() int {
	return xxx_messageInfo_GetShowTimeResponse_ShowTime.Size(m)
}
func (m *GetShowTimeResponse_ShowTime) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowTimeResponse_ShowTime.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowTimeResponse_ShowTime proto.InternalMessageInfo

func (m *GetShowTimeResponse_ShowTime) GetShowStartTime() uint32 {
	if m != nil {
		return m.ShowStartTime
	}
	return 0
}

func (m *GetShowTimeResponse_ShowTime) GetShowEndTime() uint32 {
	if m != nil {
		return m.ShowEndTime
	}
	return 0
}

func (m *GetShowTimeResponse_ShowTime) GetRemainCnt() uint32 {
	if m != nil {
		return m.RemainCnt
	}
	return 0
}

// 获取节目标签 uri: /channel-live-http-logic/channel-live-show-list/GetShowTag
type GetShowTagRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShowTagRequest) Reset()         { *m = GetShowTagRequest{} }
func (m *GetShowTagRequest) String() string { return proto.CompactTextString(m) }
func (*GetShowTagRequest) ProtoMessage()    {}
func (*GetShowTagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{21}
}
func (m *GetShowTagRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowTagRequest.Unmarshal(m, b)
}
func (m *GetShowTagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowTagRequest.Marshal(b, m, deterministic)
}
func (dst *GetShowTagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowTagRequest.Merge(dst, src)
}
func (m *GetShowTagRequest) XXX_Size() int {
	return xxx_messageInfo_GetShowTagRequest.Size(m)
}
func (m *GetShowTagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowTagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowTagRequest proto.InternalMessageInfo

type GetShowTagResponse struct {
	VoiceTagList         []*GetShowTagResponse_TagNode `protobuf:"bytes,1,rep,name=voice_tag_list,json=voiceTagList,proto3" json:"voice_tag_list"`
	ContentTagList       []*GetShowTagResponse_TagNode `protobuf:"bytes,2,rep,name=content_tag_list,json=contentTagList,proto3" json:"content_tag_list"`
	ServerTimestamp      uint32                        `protobuf:"varint,3,opt,name=server_timestamp,json=serverTimestamp,proto3" json:"server_timestamp"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetShowTagResponse) Reset()         { *m = GetShowTagResponse{} }
func (m *GetShowTagResponse) String() string { return proto.CompactTextString(m) }
func (*GetShowTagResponse) ProtoMessage()    {}
func (*GetShowTagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{22}
}
func (m *GetShowTagResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowTagResponse.Unmarshal(m, b)
}
func (m *GetShowTagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowTagResponse.Marshal(b, m, deterministic)
}
func (dst *GetShowTagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowTagResponse.Merge(dst, src)
}
func (m *GetShowTagResponse) XXX_Size() int {
	return xxx_messageInfo_GetShowTagResponse.Size(m)
}
func (m *GetShowTagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowTagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowTagResponse proto.InternalMessageInfo

func (m *GetShowTagResponse) GetVoiceTagList() []*GetShowTagResponse_TagNode {
	if m != nil {
		return m.VoiceTagList
	}
	return nil
}

func (m *GetShowTagResponse) GetContentTagList() []*GetShowTagResponse_TagNode {
	if m != nil {
		return m.ContentTagList
	}
	return nil
}

func (m *GetShowTagResponse) GetServerTimestamp() uint32 {
	if m != nil {
		return m.ServerTimestamp
	}
	return 0
}

type GetShowTagResponse_TagNode struct {
	TagId                uint32                        `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id"`
	TagName              string                        `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name"`
	ChildList            []*GetShowTagResponse_TagNode `protobuf:"bytes,3,rep,name=child_list,json=childList,proto3" json:"child_list"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetShowTagResponse_TagNode) Reset()         { *m = GetShowTagResponse_TagNode{} }
func (m *GetShowTagResponse_TagNode) String() string { return proto.CompactTextString(m) }
func (*GetShowTagResponse_TagNode) ProtoMessage()    {}
func (*GetShowTagResponse_TagNode) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{22, 0}
}
func (m *GetShowTagResponse_TagNode) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowTagResponse_TagNode.Unmarshal(m, b)
}
func (m *GetShowTagResponse_TagNode) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowTagResponse_TagNode.Marshal(b, m, deterministic)
}
func (dst *GetShowTagResponse_TagNode) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowTagResponse_TagNode.Merge(dst, src)
}
func (m *GetShowTagResponse_TagNode) XXX_Size() int {
	return xxx_messageInfo_GetShowTagResponse_TagNode.Size(m)
}
func (m *GetShowTagResponse_TagNode) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowTagResponse_TagNode.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowTagResponse_TagNode proto.InternalMessageInfo

func (m *GetShowTagResponse_TagNode) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *GetShowTagResponse_TagNode) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *GetShowTagResponse_TagNode) GetChildList() []*GetShowTagResponse_TagNode {
	if m != nil {
		return m.ChildList
	}
	return nil
}

// 申报节目 uri: /channel-live-http-logic/channel-live-show-list/DeclareShow
type DeclareShowRequest struct {
	ShowName              string   `protobuf:"bytes,1,opt,name=show_name,json=showName,proto3" json:"show_name"`
	ShowStartTime         uint32   `protobuf:"varint,2,opt,name=show_start_time,json=showStartTime,proto3" json:"show_start_time"`
	ShowEndTime           uint32   `protobuf:"varint,3,opt,name=show_end_time,json=showEndTime,proto3" json:"show_end_time"`
	VoiceTagId            uint32   `protobuf:"varint,4,opt,name=voice_tag_id,json=voiceTagId,proto3" json:"voice_tag_id"`
	ContentTagId          uint32   `protobuf:"varint,5,opt,name=content_tag_id,json=contentTagId,proto3" json:"content_tag_id"`
	ShowDescAudio         string   `protobuf:"bytes,6,opt,name=show_desc_audio,json=showDescAudio,proto3" json:"show_desc_audio"`
	ShowDescAudioDuration uint32   `protobuf:"varint,7,opt,name=show_desc_audio_duration,json=showDescAudioDuration,proto3" json:"show_desc_audio_duration"`
	ShowCoverImg          string   `protobuf:"bytes,8,opt,name=show_cover_img,json=showCoverImg,proto3" json:"show_cover_img"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *DeclareShowRequest) Reset()         { *m = DeclareShowRequest{} }
func (m *DeclareShowRequest) String() string { return proto.CompactTextString(m) }
func (*DeclareShowRequest) ProtoMessage()    {}
func (*DeclareShowRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{23}
}
func (m *DeclareShowRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeclareShowRequest.Unmarshal(m, b)
}
func (m *DeclareShowRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeclareShowRequest.Marshal(b, m, deterministic)
}
func (dst *DeclareShowRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeclareShowRequest.Merge(dst, src)
}
func (m *DeclareShowRequest) XXX_Size() int {
	return xxx_messageInfo_DeclareShowRequest.Size(m)
}
func (m *DeclareShowRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeclareShowRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeclareShowRequest proto.InternalMessageInfo

func (m *DeclareShowRequest) GetShowName() string {
	if m != nil {
		return m.ShowName
	}
	return ""
}

func (m *DeclareShowRequest) GetShowStartTime() uint32 {
	if m != nil {
		return m.ShowStartTime
	}
	return 0
}

func (m *DeclareShowRequest) GetShowEndTime() uint32 {
	if m != nil {
		return m.ShowEndTime
	}
	return 0
}

func (m *DeclareShowRequest) GetVoiceTagId() uint32 {
	if m != nil {
		return m.VoiceTagId
	}
	return 0
}

func (m *DeclareShowRequest) GetContentTagId() uint32 {
	if m != nil {
		return m.ContentTagId
	}
	return 0
}

func (m *DeclareShowRequest) GetShowDescAudio() string {
	if m != nil {
		return m.ShowDescAudio
	}
	return ""
}

func (m *DeclareShowRequest) GetShowDescAudioDuration() uint32 {
	if m != nil {
		return m.ShowDescAudioDuration
	}
	return 0
}

func (m *DeclareShowRequest) GetShowCoverImg() string {
	if m != nil {
		return m.ShowCoverImg
	}
	return ""
}

type DeclareShowResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeclareShowResponse) Reset()         { *m = DeclareShowResponse{} }
func (m *DeclareShowResponse) String() string { return proto.CompactTextString(m) }
func (*DeclareShowResponse) ProtoMessage()    {}
func (*DeclareShowResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{24}
}
func (m *DeclareShowResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeclareShowResponse.Unmarshal(m, b)
}
func (m *DeclareShowResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeclareShowResponse.Marshal(b, m, deterministic)
}
func (dst *DeclareShowResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeclareShowResponse.Merge(dst, src)
}
func (m *DeclareShowResponse) XXX_Size() int {
	return xxx_messageInfo_DeclareShowResponse.Size(m)
}
func (m *DeclareShowResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeclareShowResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeclareShowResponse proto.InternalMessageInfo

type ShowSearchOption struct {
	VoiceTagId           uint32   `protobuf:"varint,1,opt,name=voice_tag_id,json=voiceTagId,proto3" json:"voice_tag_id"`
	ContentTagId         uint32   `protobuf:"varint,2,opt,name=content_tag_id,json=contentTagId,proto3" json:"content_tag_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowSearchOption) Reset()         { *m = ShowSearchOption{} }
func (m *ShowSearchOption) String() string { return proto.CompactTextString(m) }
func (*ShowSearchOption) ProtoMessage()    {}
func (*ShowSearchOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{25}
}
func (m *ShowSearchOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowSearchOption.Unmarshal(m, b)
}
func (m *ShowSearchOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowSearchOption.Marshal(b, m, deterministic)
}
func (dst *ShowSearchOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowSearchOption.Merge(dst, src)
}
func (m *ShowSearchOption) XXX_Size() int {
	return xxx_messageInfo_ShowSearchOption.Size(m)
}
func (m *ShowSearchOption) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowSearchOption.DiscardUnknown(m)
}

var xxx_messageInfo_ShowSearchOption proto.InternalMessageInfo

func (m *ShowSearchOption) GetVoiceTagId() uint32 {
	if m != nil {
		return m.VoiceTagId
	}
	return 0
}

func (m *ShowSearchOption) GetContentTagId() uint32 {
	if m != nil {
		return m.ContentTagId
	}
	return 0
}

type GetShowListRequest struct {
	Datetime             uint32   `protobuf:"varint,1,opt,name=datetime,proto3" json:"datetime"`
	VoiceTagId           []uint32 `protobuf:"varint,2,rep,packed,name=voice_tag_id,json=voiceTagId,proto3" json:"voice_tag_id"`
	ContentTagId         []uint32 `protobuf:"varint,3,rep,packed,name=content_tag_id,json=contentTagId,proto3" json:"content_tag_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShowListRequest) Reset()         { *m = GetShowListRequest{} }
func (m *GetShowListRequest) String() string { return proto.CompactTextString(m) }
func (*GetShowListRequest) ProtoMessage()    {}
func (*GetShowListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{26}
}
func (m *GetShowListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowListRequest.Unmarshal(m, b)
}
func (m *GetShowListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowListRequest.Marshal(b, m, deterministic)
}
func (dst *GetShowListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowListRequest.Merge(dst, src)
}
func (m *GetShowListRequest) XXX_Size() int {
	return xxx_messageInfo_GetShowListRequest.Size(m)
}
func (m *GetShowListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowListRequest proto.InternalMessageInfo

func (m *GetShowListRequest) GetDatetime() uint32 {
	if m != nil {
		return m.Datetime
	}
	return 0
}

func (m *GetShowListRequest) GetVoiceTagId() []uint32 {
	if m != nil {
		return m.VoiceTagId
	}
	return nil
}

func (m *GetShowListRequest) GetContentTagId() []uint32 {
	if m != nil {
		return m.ContentTagId
	}
	return nil
}

type ShowItem struct {
	ShowId               uint32   `protobuf:"varint,1,opt,name=show_id,json=showId,proto3" json:"show_id"`
	ShowName             string   `protobuf:"bytes,2,opt,name=show_name,json=showName,proto3" json:"show_name"`
	ShowDescAudio        string   `protobuf:"bytes,3,opt,name=show_desc_audio,json=showDescAudio,proto3" json:"show_desc_audio"`
	ShowCoverImg         string   `protobuf:"bytes,4,opt,name=show_cover_img,json=showCoverImg,proto3" json:"show_cover_img"`
	LiveStatus           uint32   `protobuf:"varint,5,opt,name=live_status,json=liveStatus,proto3" json:"live_status"`
	Account              string   `protobuf:"bytes,6,opt,name=account,proto3" json:"account"`
	Nickname             string   `protobuf:"bytes,7,opt,name=nickname,proto3" json:"nickname"`
	Avatar               string   `protobuf:"bytes,8,opt,name=avatar,proto3" json:"avatar"`
	ChannelId            uint32   `protobuf:"varint,9,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	AudioDuration        uint32   `protobuf:"varint,10,opt,name=audio_duration,json=audioDuration,proto3" json:"audio_duration"`
	ContentId            uint32   `protobuf:"varint,11,opt,name=content_id,json=contentId,proto3" json:"content_id"`
	VoiceId              uint32   `protobuf:"varint,12,opt,name=voice_id,json=voiceId,proto3" json:"voice_id"`
	HotValue             int64    `protobuf:"varint,13,opt,name=hot_value,json=hotValue,proto3" json:"hot_value"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowItem) Reset()         { *m = ShowItem{} }
func (m *ShowItem) String() string { return proto.CompactTextString(m) }
func (*ShowItem) ProtoMessage()    {}
func (*ShowItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{27}
}
func (m *ShowItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowItem.Unmarshal(m, b)
}
func (m *ShowItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowItem.Marshal(b, m, deterministic)
}
func (dst *ShowItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowItem.Merge(dst, src)
}
func (m *ShowItem) XXX_Size() int {
	return xxx_messageInfo_ShowItem.Size(m)
}
func (m *ShowItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowItem.DiscardUnknown(m)
}

var xxx_messageInfo_ShowItem proto.InternalMessageInfo

func (m *ShowItem) GetShowId() uint32 {
	if m != nil {
		return m.ShowId
	}
	return 0
}

func (m *ShowItem) GetShowName() string {
	if m != nil {
		return m.ShowName
	}
	return ""
}

func (m *ShowItem) GetShowDescAudio() string {
	if m != nil {
		return m.ShowDescAudio
	}
	return ""
}

func (m *ShowItem) GetShowCoverImg() string {
	if m != nil {
		return m.ShowCoverImg
	}
	return ""
}

func (m *ShowItem) GetLiveStatus() uint32 {
	if m != nil {
		return m.LiveStatus
	}
	return 0
}

func (m *ShowItem) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ShowItem) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ShowItem) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *ShowItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ShowItem) GetAudioDuration() uint32 {
	if m != nil {
		return m.AudioDuration
	}
	return 0
}

func (m *ShowItem) GetContentId() uint32 {
	if m != nil {
		return m.ContentId
	}
	return 0
}

func (m *ShowItem) GetVoiceId() uint32 {
	if m != nil {
		return m.VoiceId
	}
	return 0
}

func (m *ShowItem) GetHotValue() int64 {
	if m != nil {
		return m.HotValue
	}
	return 0
}

type GetShowListResponse struct {
	ShowList             []*GetShowListResponse_ShowGroupItem `protobuf:"bytes,1,rep,name=show_list,json=showList,proto3" json:"show_list"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *GetShowListResponse) Reset()         { *m = GetShowListResponse{} }
func (m *GetShowListResponse) String() string { return proto.CompactTextString(m) }
func (*GetShowListResponse) ProtoMessage()    {}
func (*GetShowListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{28}
}
func (m *GetShowListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowListResponse.Unmarshal(m, b)
}
func (m *GetShowListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowListResponse.Marshal(b, m, deterministic)
}
func (dst *GetShowListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowListResponse.Merge(dst, src)
}
func (m *GetShowListResponse) XXX_Size() int {
	return xxx_messageInfo_GetShowListResponse.Size(m)
}
func (m *GetShowListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowListResponse proto.InternalMessageInfo

func (m *GetShowListResponse) GetShowList() []*GetShowListResponse_ShowGroupItem {
	if m != nil {
		return m.ShowList
	}
	return nil
}

type GetShowListResponse_ShowGroupItem struct {
	TimeRange            string      `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range"`
	ShowList             []*ShowItem `protobuf:"bytes,2,rep,name=show_list,json=showList,proto3" json:"show_list"`
	IsEnd                bool        `protobuf:"varint,3,opt,name=is_end,json=isEnd,proto3" json:"is_end"`
	HasMore              bool        `protobuf:"varint,4,opt,name=has_more,json=hasMore,proto3" json:"has_more"`
	BeginTime            uint32      `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	EndTime              uint32      `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetShowListResponse_ShowGroupItem) Reset()         { *m = GetShowListResponse_ShowGroupItem{} }
func (m *GetShowListResponse_ShowGroupItem) String() string { return proto.CompactTextString(m) }
func (*GetShowListResponse_ShowGroupItem) ProtoMessage()    {}
func (*GetShowListResponse_ShowGroupItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{28, 0}
}
func (m *GetShowListResponse_ShowGroupItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowListResponse_ShowGroupItem.Unmarshal(m, b)
}
func (m *GetShowListResponse_ShowGroupItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowListResponse_ShowGroupItem.Marshal(b, m, deterministic)
}
func (dst *GetShowListResponse_ShowGroupItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowListResponse_ShowGroupItem.Merge(dst, src)
}
func (m *GetShowListResponse_ShowGroupItem) XXX_Size() int {
	return xxx_messageInfo_GetShowListResponse_ShowGroupItem.Size(m)
}
func (m *GetShowListResponse_ShowGroupItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowListResponse_ShowGroupItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowListResponse_ShowGroupItem proto.InternalMessageInfo

func (m *GetShowListResponse_ShowGroupItem) GetTimeRange() string {
	if m != nil {
		return m.TimeRange
	}
	return ""
}

func (m *GetShowListResponse_ShowGroupItem) GetShowList() []*ShowItem {
	if m != nil {
		return m.ShowList
	}
	return nil
}

func (m *GetShowListResponse_ShowGroupItem) GetIsEnd() bool {
	if m != nil {
		return m.IsEnd
	}
	return false
}

func (m *GetShowListResponse_ShowGroupItem) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

func (m *GetShowListResponse_ShowGroupItem) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetShowListResponse_ShowGroupItem) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// 展开时间段列表
type UnfoldShowListRequest struct {
	BeginTime            uint32   `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	VoiceTagId           []uint32 `protobuf:"varint,3,rep,packed,name=voice_tag_id,json=voiceTagId,proto3" json:"voice_tag_id"`
	ContentTagId         []uint32 `protobuf:"varint,4,rep,packed,name=content_tag_id,json=contentTagId,proto3" json:"content_tag_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnfoldShowListRequest) Reset()         { *m = UnfoldShowListRequest{} }
func (m *UnfoldShowListRequest) String() string { return proto.CompactTextString(m) }
func (*UnfoldShowListRequest) ProtoMessage()    {}
func (*UnfoldShowListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{29}
}
func (m *UnfoldShowListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfoldShowListRequest.Unmarshal(m, b)
}
func (m *UnfoldShowListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfoldShowListRequest.Marshal(b, m, deterministic)
}
func (dst *UnfoldShowListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfoldShowListRequest.Merge(dst, src)
}
func (m *UnfoldShowListRequest) XXX_Size() int {
	return xxx_messageInfo_UnfoldShowListRequest.Size(m)
}
func (m *UnfoldShowListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfoldShowListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UnfoldShowListRequest proto.InternalMessageInfo

func (m *UnfoldShowListRequest) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *UnfoldShowListRequest) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *UnfoldShowListRequest) GetVoiceTagId() []uint32 {
	if m != nil {
		return m.VoiceTagId
	}
	return nil
}

func (m *UnfoldShowListRequest) GetContentTagId() []uint32 {
	if m != nil {
		return m.ContentTagId
	}
	return nil
}

// 展开时间段列表
type UnfoldShowListResponse struct {
	ShowList             []*ShowItem `protobuf:"bytes,1,rep,name=show_list,json=showList,proto3" json:"show_list"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UnfoldShowListResponse) Reset()         { *m = UnfoldShowListResponse{} }
func (m *UnfoldShowListResponse) String() string { return proto.CompactTextString(m) }
func (*UnfoldShowListResponse) ProtoMessage()    {}
func (*UnfoldShowListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{30}
}
func (m *UnfoldShowListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfoldShowListResponse.Unmarshal(m, b)
}
func (m *UnfoldShowListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfoldShowListResponse.Marshal(b, m, deterministic)
}
func (dst *UnfoldShowListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfoldShowListResponse.Merge(dst, src)
}
func (m *UnfoldShowListResponse) XXX_Size() int {
	return xxx_messageInfo_UnfoldShowListResponse.Size(m)
}
func (m *UnfoldShowListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfoldShowListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UnfoldShowListResponse proto.InternalMessageInfo

func (m *UnfoldShowListResponse) GetShowList() []*ShowItem {
	if m != nil {
		return m.ShowList
	}
	return nil
}

// 获取节目单用户基础信息 uri: /channel-live-http-logic/channel-live-show-list/GetShowListUserBaseInfo
type GetShowListUserBaseInfoRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShowListUserBaseInfoRequest) Reset()         { *m = GetShowListUserBaseInfoRequest{} }
func (m *GetShowListUserBaseInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetShowListUserBaseInfoRequest) ProtoMessage()    {}
func (*GetShowListUserBaseInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{31}
}
func (m *GetShowListUserBaseInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowListUserBaseInfoRequest.Unmarshal(m, b)
}
func (m *GetShowListUserBaseInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowListUserBaseInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetShowListUserBaseInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowListUserBaseInfoRequest.Merge(dst, src)
}
func (m *GetShowListUserBaseInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetShowListUserBaseInfoRequest.Size(m)
}
func (m *GetShowListUserBaseInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowListUserBaseInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowListUserBaseInfoRequest proto.InternalMessageInfo

type GetShowListUserBaseInfoResponse struct {
	RemainDeclareCnt     uint32   `protobuf:"varint,1,opt,name=remain_declare_cnt,json=remainDeclareCnt,proto3" json:"remain_declare_cnt"`
	ShowApplyEntry       bool     `protobuf:"varint,2,opt,name=show_apply_entry,json=showApplyEntry,proto3" json:"show_apply_entry"`
	UnableReason         string   `protobuf:"bytes,3,opt,name=unable_reason,json=unableReason,proto3" json:"unable_reason"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShowListUserBaseInfoResponse) Reset()         { *m = GetShowListUserBaseInfoResponse{} }
func (m *GetShowListUserBaseInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetShowListUserBaseInfoResponse) ProtoMessage()    {}
func (*GetShowListUserBaseInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_6b3368981edc6b55, []int{32}
}
func (m *GetShowListUserBaseInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowListUserBaseInfoResponse.Unmarshal(m, b)
}
func (m *GetShowListUserBaseInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowListUserBaseInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetShowListUserBaseInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowListUserBaseInfoResponse.Merge(dst, src)
}
func (m *GetShowListUserBaseInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetShowListUserBaseInfoResponse.Size(m)
}
func (m *GetShowListUserBaseInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowListUserBaseInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowListUserBaseInfoResponse proto.InternalMessageInfo

func (m *GetShowListUserBaseInfoResponse) GetRemainDeclareCnt() uint32 {
	if m != nil {
		return m.RemainDeclareCnt
	}
	return 0
}

func (m *GetShowListUserBaseInfoResponse) GetShowApplyEntry() bool {
	if m != nil {
		return m.ShowApplyEntry
	}
	return false
}

func (m *GetShowListUserBaseInfoResponse) GetUnableReason() string {
	if m != nil {
		return m.UnableReason
	}
	return ""
}

func init() {
	proto.RegisterType((*AnchorCertInitInfo)(nil), "channel_live_http_logic.AnchorCertInitInfo")
	proto.RegisterType((*AnchorCheckInfo)(nil), "channel_live_http_logic.AnchorCheckInfo")
	proto.RegisterType((*AnchorCheckInfo_CheckData)(nil), "channel_live_http_logic.AnchorCheckInfo.CheckData")
	proto.RegisterType((*AnchorCheckUpgradeInfo)(nil), "channel_live_http_logic.AnchorCheckUpgradeInfo")
	proto.RegisterType((*AnchorCertInfo)(nil), "channel_live_http_logic.AnchorCertInfo")
	proto.RegisterType((*MonthStats)(nil), "channel_live_http_logic.MonthStats")
	proto.RegisterType((*AnchorCertUpgradeInfo)(nil), "channel_live_http_logic.AnchorCertUpgradeInfo")
	proto.RegisterType((*CheckUserRegisterEntryReq)(nil), "channel_live_http_logic.CheckUserRegisterEntryReq")
	proto.RegisterType((*CheckUserRegisterEntryResp)(nil), "channel_live_http_logic.CheckUserRegisterEntryResp")
	proto.RegisterType((*MatchSchedule)(nil), "channel_live_http_logic.MatchSchedule")
	proto.RegisterType((*MatchInfo)(nil), "channel_live_http_logic.MatchInfo")
	proto.RegisterType((*OfficialChannelInfo)(nil), "channel_live_http_logic.OfficialChannelInfo")
	proto.RegisterType((*GetRegisterOfficialChListReq)(nil), "channel_live_http_logic.GetRegisterOfficialChListReq")
	proto.RegisterType((*GetRegisterOfficialChListResp)(nil), "channel_live_http_logic.GetRegisterOfficialChListResp")
	proto.RegisterType((*GetOfficialChMatchInfoReq)(nil), "channel_live_http_logic.GetOfficialChMatchInfoReq")
	proto.RegisterType((*GetOfficialChMatchInfoResp)(nil), "channel_live_http_logic.GetOfficialChMatchInfoResp")
	proto.RegisterType((*RegisterOfficialChannelReq)(nil), "channel_live_http_logic.RegisterOfficialChannelReq")
	proto.RegisterType((*RegisterOfficialChannelResp)(nil), "channel_live_http_logic.RegisterOfficialChannelResp")
	proto.RegisterType((*CancelRegisterOfficialChReq)(nil), "channel_live_http_logic.CancelRegisterOfficialChReq")
	proto.RegisterType((*CancelRegisterOfficialChResp)(nil), "channel_live_http_logic.CancelRegisterOfficialChResp")
	proto.RegisterType((*GetShowTimeRequest)(nil), "channel_live_http_logic.GetShowTimeRequest")
	proto.RegisterType((*GetShowTimeResponse)(nil), "channel_live_http_logic.GetShowTimeResponse")
	proto.RegisterType((*GetShowTimeResponse_ShowTime)(nil), "channel_live_http_logic.GetShowTimeResponse.ShowTime")
	proto.RegisterType((*GetShowTagRequest)(nil), "channel_live_http_logic.GetShowTagRequest")
	proto.RegisterType((*GetShowTagResponse)(nil), "channel_live_http_logic.GetShowTagResponse")
	proto.RegisterType((*GetShowTagResponse_TagNode)(nil), "channel_live_http_logic.GetShowTagResponse.TagNode")
	proto.RegisterType((*DeclareShowRequest)(nil), "channel_live_http_logic.DeclareShowRequest")
	proto.RegisterType((*DeclareShowResponse)(nil), "channel_live_http_logic.DeclareShowResponse")
	proto.RegisterType((*ShowSearchOption)(nil), "channel_live_http_logic.ShowSearchOption")
	proto.RegisterType((*GetShowListRequest)(nil), "channel_live_http_logic.GetShowListRequest")
	proto.RegisterType((*ShowItem)(nil), "channel_live_http_logic.ShowItem")
	proto.RegisterType((*GetShowListResponse)(nil), "channel_live_http_logic.GetShowListResponse")
	proto.RegisterType((*GetShowListResponse_ShowGroupItem)(nil), "channel_live_http_logic.GetShowListResponse.ShowGroupItem")
	proto.RegisterType((*UnfoldShowListRequest)(nil), "channel_live_http_logic.UnfoldShowListRequest")
	proto.RegisterType((*UnfoldShowListResponse)(nil), "channel_live_http_logic.UnfoldShowListResponse")
	proto.RegisterType((*GetShowListUserBaseInfoRequest)(nil), "channel_live_http_logic.GetShowListUserBaseInfoRequest")
	proto.RegisterType((*GetShowListUserBaseInfoResponse)(nil), "channel_live_http_logic.GetShowListUserBaseInfoResponse")
	proto.RegisterEnum("channel_live_http_logic.AnchorCheckUpgradeType", AnchorCheckUpgradeType_name, AnchorCheckUpgradeType_value)
	proto.RegisterEnum("channel_live_http_logic.RegisterStatus", RegisterStatus_name, RegisterStatus_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-live-http-logic/channel-live-http-logic.proto", fileDescriptor_channel_live_http_logic_6b3368981edc6b55)
}

var fileDescriptor_channel_live_http_logic_6b3368981edc6b55 = []byte{
	// 2227 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x58, 0x5f, 0x6f, 0x1b, 0xc7,
	0x11, 0xcf, 0x91, 0xb2, 0x44, 0x8e, 0x44, 0x9a, 0x5e, 0x59, 0x36, 0x2d, 0xd9, 0xb2, 0x7c, 0x49,
	0x0d, 0xc5, 0xb5, 0xe5, 0xc0, 0x41, 0x90, 0xa6, 0x05, 0x82, 0x2a, 0x94, 0x63, 0xb3, 0x89, 0xe5,
	0xe0, 0x24, 0xf7, 0x8f, 0x8b, 0xe2, 0xb0, 0xbe, 0x5b, 0x91, 0x5b, 0xdf, 0x3f, 0xdf, 0x2e, 0x65,
	0xeb, 0xad, 0xef, 0x45, 0xd1, 0xc7, 0xa2, 0x01, 0x5a, 0x14, 0xe8, 0x5b, 0x5f, 0xfb, 0x01, 0xfa,
	0x01, 0x9a, 0x4f, 0xd0, 0x2f, 0x50, 0xa0, 0x40, 0xbf, 0x43, 0x31, 0xb3, 0x7b, 0xe4, 0x1d, 0x29,
	0x5a, 0x6a, 0xf2, 0x76, 0xfb, 0x9b, 0xd9, 0xd9, 0xd9, 0xdf, 0xcc, 0xce, 0xce, 0x1e, 0xec, 0x69,
	0x7d, 0xff, 0xd5, 0x48, 0x06, 0x2f, 0x95, 0x8c, 0x8e, 0x45, 0x7e, 0x3f, 0x18, 0xf2, 0x24, 0x11,
	0xd1, 0xbd, 0x48, 0x1e, 0x8b, 0x7b, 0x43, 0xad, 0xb3, 0x7b, 0x51, 0x3a, 0x90, 0xc1, 0x3c, 0x7c,
	0x27, 0xcb, 0x53, 0x9d, 0xb2, 0xab, 0x56, 0xec, 0xa3, 0xd8, 0x47, 0xb1, 0x4f, 0xe2, 0xf5, 0xd6,
	0x80, 0xfb, 0x2f, 0xb8, 0x12, 0x46, 0xcf, 0xfd, 0xba, 0x06, 0x6c, 0x37, 0x09, 0x86, 0x69, 0xde,
	0x13, 0xb9, 0xee, 0x27, 0x52, 0xf7, 0x93, 0xa3, 0x94, 0x6d, 0x43, 0x27, 0xe2, 0x4a, 0xfb, 0xc1,
	0x50, 0x04, 0x2f, 0xfd, 0x48, 0x1c, 0x8b, 0xa8, 0xeb, 0x6c, 0x39, 0xdb, 0x4d, 0xaf, 0x8d, 0x78,
	0x0f, 0xe1, 0x2f, 0x11, 0x65, 0xef, 0xc3, 0x25, 0xa9, 0x7c, 0x35, 0x4c, 0x5f, 0xfb, 0x81, 0xc8,
	0xb5, 0x2f, 0x93, 0xa3, 0xb4, 0x5b, 0xdb, 0x72, 0xb6, 0x1b, 0x5e, 0x5b, 0xaa, 0x83, 0x61, 0xfa,
	0xda, 0x18, 0x3e, 0x4a, 0xd9, 0x7d, 0xb8, 0x2c, 0x95, 0xd1, 0x7a, 0x29, 0x44, 0xe6, 0x8f, 0xb2,
	0x41, 0xce, 0x43, 0xd1, 0xad, 0x93, 0xf6, 0x25, 0xa9, 0x50, 0xf3, 0x0b, 0x21, 0xb2, 0x67, 0x46,
	0xc0, 0x36, 0xa0, 0x29, 0xb5, 0x88, 0xfd, 0x84, 0xc7, 0xa2, 0xbb, 0x40, 0xcb, 0x37, 0x10, 0xd8,
	0xe7, 0xb1, 0x60, 0x37, 0x61, 0x19, 0xf7, 0xe1, 0xcb, 0x78, 0x30, 0xca, 0xa3, 0xee, 0x05, 0x12,
	0x03, 0x42, 0x7d, 0x42, 0xd8, 0x2d, 0x58, 0x51, 0x43, 0x1e, 0xa2, 0x63, 0x69, 0x94, 0xe6, 0xdd,
	0x45, 0xd2, 0x58, 0x36, 0x58, 0x0f, 0x21, 0x76, 0x03, 0x80, 0xd3, 0xe6, 0x7d, 0xcd, 0x07, 0xdd,
	0x25, 0x52, 0x68, 0x1a, 0xe4, 0x90, 0x0f, 0xdc, 0xdf, 0xd5, 0xe0, 0xa2, 0x25, 0x07, 0x37, 0x4c,
	0x9b, 0x78, 0x0e, 0x17, 0x0d, 0x29, 0xb8, 0x51, 0x3f, 0x92, 0x4a, 0x77, 0x9d, 0xad, 0xfa, 0xf6,
	0xf2, 0x83, 0x07, 0x3b, 0x73, 0x28, 0xdf, 0x99, 0x32, 0xb1, 0x43, 0x5f, 0x7b, 0x5c, 0x73, 0xaf,
	0x15, 0x14, 0xe0, 0x97, 0x52, 0x69, 0xf6, 0x01, 0x5c, 0x36, 0xb6, 0x2d, 0x33, 0xbe, 0xd2, 0x5c,
	0x8f, 0x14, 0xd1, 0xd9, 0xf2, 0x18, 0xc9, 0x2c, 0x37, 0x07, 0x24, 0xc1, 0x0d, 0xe4, 0x22, 0xe6,
	0x32, 0xf1, 0x43, 0x7e, 0x42, 0x44, 0xb6, 0xbc, 0xa6, 0x41, 0xf6, 0xf8, 0xc9, 0xfa, 0x13, 0x68,
	0x8e, 0x17, 0x43, 0xc2, 0x66, 0xc3, 0x09, 0xc1, 0x24, 0x94, 0xa8, 0x90, 0x0b, 0xae, 0x85, 0xaf,
	0x65, 0x2c, 0x68, 0x55, 0x54, 0x20, 0xe8, 0x50, 0xc6, 0xc2, 0xfd, 0x18, 0xae, 0x94, 0xf6, 0x62,
	0x3d, 0x21, 0x56, 0xaa, 0x7e, 0x38, 0x53, 0x7e, 0xb8, 0xff, 0xa8, 0x41, 0xbb, 0x9c, 0x65, 0x66,
	0x46, 0x89, 0x7a, 0x67, 0x8a, 0xfa, 0x6a, 0xe8, 0x6b, 0x6f, 0x0f, 0x7d, 0xfd, 0xcc, 0xd0, 0x2f,
	0xcc, 0x86, 0x7e, 0x0f, 0x96, 0xe3, 0x34, 0xd1, 0x43, 0xe2, 0x58, 0x51, 0xfa, 0x2c, 0x3f, 0x78,
	0x77, 0x6e, 0x0c, 0x9f, 0xa0, 0x2e, 0x92, 0xae, 0x3c, 0x88, 0xc7, 0xdf, 0x6c, 0x00, 0x5d, 0xbb,
	0x0b, 0x4a, 0xeb, 0x22, 0x6e, 0x74, 0x08, 0x16, 0xc9, 0xe4, 0xce, 0x59, 0x69, 0x21, 0x72, 0x5d,
	0x62, 0xd2, 0x5b, 0xe3, 0xa7, 0xc1, 0xee, 0x7f, 0x1d, 0x80, 0x89, 0x0f, 0xec, 0x0e, 0x5c, 0x32,
	0xde, 0xf3, 0x40, 0xa3, 0xd9, 0x90, 0x9f, 0x28, 0x4b, 0xfb, 0x45, 0x12, 0xec, 0x12, 0xbe, 0xc7,
	0x4f, 0x14, 0xdb, 0x81, 0x55, 0xa3, 0x9b, 0x45, 0x5c, 0x1f, 0xa5, 0x79, 0x6c, 0xb4, 0x4d, 0x52,
	0x19, 0x33, 0x5f, 0x59, 0x09, 0xe9, 0x7f, 0x1f, 0x98, 0xd1, 0x4f, 0xc4, 0x6b, 0xff, 0x88, 0x27,
	0xca, 0x0f, 0x12, 0x6d, 0x73, 0xcb, 0x18, 0xdf, 0x17, 0xaf, 0x3f, 0xe7, 0x89, 0xea, 0x25, 0x9a,
	0xdd, 0x2d, 0x94, 0x83, 0x34, 0x51, 0xa3, 0x58, 0xe4, 0xa4, 0xbc, 0x40, 0xca, 0x1d, 0x92, 0xf4,
	0xac, 0x00, 0xb5, 0xb7, 0xc1, 0x60, 0xfe, 0x40, 0x1e, 0x69, 0xff, 0x98, 0x47, 0x23, 0x41, 0xcc,
	0xb7, 0xbc, 0x36, 0xe1, 0x8f, 0xe4, 0x91, 0xfe, 0x29, 0xa2, 0xee, 0x1f, 0x6a, 0xb0, 0x76, 0x2a,
	0x41, 0x6c, 0x0b, 0x56, 0x34, 0x57, 0x2f, 0x31, 0xf8, 0x3e, 0x46, 0xdf, 0xe6, 0x31, 0x62, 0xfd,
	0x78, 0xf0, 0x2c, 0x8f, 0x30, 0x77, 0x48, 0xa3, 0x9c, 0x3b, 0x08, 0x50, 0xee, 0xdc, 0x00, 0x52,
	0x45, 0x32, 0x02, 0x61, 0x53, 0x87, 0xd4, 0xbf, 0x42, 0x00, 0xf7, 0x63, 0xac, 0x27, 0x52, 0x4b,
	0x1e, 0xd9, 0xb3, 0x62, 0xf2, 0xa7, 0x43, 0x6b, 0x18, 0x81, 0x39, 0x31, 0xb7, 0xe1, 0x22, 0x69,
	0x9b, 0x73, 0xa5, 0x4f, 0x32, 0x61, 0xeb, 0x50, 0x0b, 0x61, 0x3a, 0x25, 0x87, 0x27, 0x99, 0x98,
	0xd6, 0xc3, 0xd3, 0xb5, 0x38, 0xad, 0x27, 0x63, 0xc1, 0x5c, 0x20, 0xc0, 0xff, 0xf5, 0x28, 0xce,
	0x68, 0x73, 0xa6, 0x24, 0x2d, 0x23, 0xf8, 0x93, 0x51, 0x9c, 0x3d, 0xcb, 0x23, 0x77, 0x03, 0xae,
	0x99, 0xe3, 0xa7, 0x44, 0xee, 0x89, 0x81, 0x54, 0x5a, 0xe4, 0x0f, 0x13, 0x9d, 0x9f, 0x78, 0xe2,
	0x95, 0xfb, 0x39, 0xac, 0xcf, 0x13, 0xaa, 0x8c, 0x6d, 0xc3, 0x45, 0xa9, 0x1e, 0x73, 0x45, 0x12,
	0x82, 0x89, 0xbd, 0x86, 0x37, 0x0d, 0xbb, 0x7f, 0x77, 0xa0, 0xf5, 0x84, 0xeb, 0x60, 0x78, 0x10,
	0x0c, 0x45, 0x38, 0x8a, 0x04, 0xeb, 0xc2, 0xd2, 0x0b, 0x31, 0x90, 0xc9, 0x61, 0x91, 0x67, 0xc5,
	0x90, 0x5d, 0x86, 0x0b, 0x22, 0x09, 0x0f, 0x8b, 0x8c, 0x32, 0x03, 0xf6, 0x1e, 0xb4, 0xa4, 0xea,
	0xf1, 0xa4, 0xf0, 0xc2, 0x56, 0xf9, 0x2a, 0xc8, 0xee, 0xe2, 0xed, 0xb1, 0x1b, 0xe5, 0x82, 0x87,
	0x27, 0x63, 0xcd, 0x85, 0xe2, 0x3e, 0x98, 0x12, 0xb0, 0x4d, 0x00, 0x65, 0xfd, 0xe9, 0x87, 0x36,
	0x71, 0x4a, 0x88, 0xfb, 0x37, 0x07, 0x9a, 0xe4, 0x35, 0x25, 0x4a, 0x17, 0x96, 0x62, 0x1c, 0x4c,
	0x3c, 0xb6, 0x43, 0x76, 0x1d, 0x9a, 0xf4, 0xb9, 0x3f, 0x49, 0x90, 0x09, 0xc0, 0xbe, 0x80, 0x56,
	0x61, 0xd3, 0xd4, 0xf7, 0x3a, 0xd5, 0xf7, 0xdb, 0xf3, 0x6b, 0x43, 0x99, 0x28, 0x6f, 0xa5, 0x98,
	0x4c, 0x25, 0xbd, 0x70, 0xa2, 0x1f, 0xda, 0x43, 0x51, 0x0c, 0xdd, 0x7f, 0xd7, 0x60, 0xf5, 0xe9,
	0xd1, 0x91, 0x0c, 0x24, 0x8f, 0x7a, 0xc6, 0x32, 0xb9, 0x7d, 0x1d, 0x9a, 0x76, 0xa1, 0x7e, 0x58,
	0x54, 0xd2, 0x31, 0x80, 0xf6, 0x78, 0xa0, 0x4b, 0x8e, 0x17, 0x43, 0x2b, 0xc1, 0x34, 0xb2, 0x59,
	0x5d, 0x0c, 0x91, 0xb6, 0xdc, 0x52, 0x78, 0xa8, 0xc8, 0x8d, 0xba, 0x57, 0x42, 0x98, 0x0b, 0x2b,
	0xc5, 0xa8, 0x97, 0x26, 0xa1, 0x4d, 0xe1, 0x0a, 0xc6, 0x6e, 0x43, 0xbb, 0x18, 0x9b, 0xab, 0x87,
	0x12, 0xb8, 0xe5, 0x4d, 0xa1, 0xec, 0x31, 0xb4, 0x0a, 0x84, 0x68, 0xa1, 0x0c, 0x5e, 0x7e, 0xe0,
	0xbe, 0x9d, 0x3c, 0xaa, 0x7c, 0xd5, 0x89, 0x98, 0x40, 0x58, 0xdc, 0xd2, 0xe4, 0xe1, 0x1b, 0x1e,
	0x67, 0x91, 0xe8, 0x36, 0xcc, 0x89, 0xa9, 0x80, 0xe8, 0x3b, 0x1f, 0x85, 0x32, 0x2d, 0x94, 0x9a,
	0xc6, 0xf7, 0x32, 0xe6, 0x6e, 0xc2, 0xf5, 0x47, 0x42, 0x17, 0x59, 0x34, 0xe1, 0x1c, 0x03, 0x84,
	0x87, 0x46, 0xc2, 0x8d, 0xb7, 0xc8, 0x55, 0xc6, 0x1e, 0x43, 0x43, 0xda, 0x3b, 0xda, 0x5e, 0xf6,
	0x77, 0xe7, 0xee, 0xe7, 0x94, 0x90, 0x7a, 0xe3, 0xd9, 0xee, 0x27, 0x70, 0xed, 0x91, 0xd0, 0x13,
	0x9d, 0xc9, 0xee, 0xc5, 0xab, 0xb7, 0x47, 0xde, 0xfd, 0xc6, 0x81, 0xf5, 0x79, 0x73, 0x55, 0xc6,
	0x7e, 0x6c, 0x73, 0x1a, 0x01, 0xeb, 0xe4, 0x79, 0x48, 0x9f, 0x4c, 0x9a, 0x25, 0xbc, 0x76, 0x1e,
	0xc2, 0xeb, 0xb3, 0x84, 0xa3, 0x8e, 0x4c, 0x74, 0x9e, 0x3e, 0x7c, 0x23, 0x48, 0xc7, 0x94, 0xcf,
	0x0a, 0xe6, 0xfe, 0xc5, 0x81, 0xf5, 0x59, 0xca, 0xc9, 0xdd, 0x33, 0xb9, 0x18, 0x2f, 0x10, 0x8e,
	0xc8, 0x37, 0xeb, 0x69, 0x05, 0xab, 0x12, 0x52, 0x3f, 0x77, 0x16, 0x4e, 0x26, 0xb9, 0x37, 0x60,
	0x63, 0xae, 0x87, 0x2a, 0x73, 0xff, 0xe4, 0xc0, 0x46, 0x8f, 0x27, 0x01, 0x0e, 0xa7, 0xb5, 0xce,
	0xde, 0x42, 0xf5, 0x50, 0xd6, 0x66, 0x0e, 0xe5, 0x77, 0x77, 0x7f, 0x13, 0xae, 0xcf, 0x77, 0x4f,
	0x65, 0xee, 0x07, 0xc0, 0x1e, 0x09, 0x8d, 0x1d, 0x3a, 0x56, 0x09, 0x4f, 0xbc, 0x1a, 0x09, 0xa5,
	0xd9, 0x3a, 0x34, 0x42, 0xae, 0x05, 0xdd, 0x51, 0xc6, 0xe9, 0xf1, 0xd8, 0xfd, 0x6b, 0x0d, 0x56,
	0x2b, 0x53, 0x54, 0x96, 0x26, 0x4a, 0xb0, 0x5f, 0x42, 0x9b, 0x1e, 0x00, 0xa8, 0x54, 0x6e, 0x89,
	0x3f, 0x9a, 0xeb, 0xf0, 0x29, 0x56, 0x76, 0xc6, 0xc0, 0x8a, 0xb2, 0x5f, 0x54, 0x41, 0x7f, 0x00,
	0x5d, 0xc1, 0xf3, 0x48, 0x0a, 0xa5, 0x7d, 0x25, 0x22, 0x11, 0x68, 0xfe, 0x22, 0x2a, 0xb5, 0xa8,
	0x2d, 0xef, 0x4a, 0x21, 0x3f, 0x18, 0x8b, 0x71, 0xf6, 0xfa, 0x08, 0x1a, 0x85, 0x4d, 0xbc, 0x81,
	0xc9, 0x45, 0xa5, 0x79, 0xae, 0xfd, 0xd2, 0xee, 0x5a, 0x08, 0x1f, 0x20, 0x5a, 0xdc, 0xc0, 0xa4,
	0x27, 0x92, 0xb0, 0xbc, 0xc4, 0x32, 0x82, 0x0f, 0x93, 0x90, 0x74, 0x26, 0xcd, 0xee, 0xa4, 0x31,
	0xb2, 0xcd, 0x6e, 0x2f, 0xd1, 0xee, 0x2a, 0x5c, 0x2a, 0xb6, 0xc7, 0x07, 0x96, 0x56, 0xf7, 0xb7,
	0xf5, 0x09, 0xdb, 0x88, 0x5a, 0xe6, 0x7e, 0x01, 0xed, 0xe3, 0x54, 0x06, 0x02, 0x9b, 0xe0, 0x32,
	0x73, 0x1f, 0x9e, 0xc9, 0xdc, 0xc4, 0xc8, 0xce, 0x21, 0x1f, 0xec, 0xa7, 0xa1, 0xf0, 0x56, 0xc8,
	0xd4, 0x21, 0x1f, 0x10, 0x6f, 0xbf, 0x82, 0x4e, 0x90, 0x26, 0x5a, 0x24, 0x7a, 0x62, 0xbc, 0xf6,
	0xed, 0x8d, 0xb7, 0xad, 0xb1, 0xc2, 0xfc, 0xfb, 0xd0, 0x51, 0x22, 0x3f, 0x16, 0x39, 0xd1, 0xa4,
	0x34, 0x8f, 0xb3, 0xa2, 0x47, 0x34, 0xf8, 0x61, 0x01, 0xaf, 0xff, 0xde, 0x81, 0x25, 0x6b, 0x86,
	0xad, 0xc1, 0x22, 0x7a, 0x23, 0x8b, 0x13, 0x71, 0x41, 0xf3, 0x41, 0x3f, 0x64, 0xd7, 0xa0, 0x81,
	0x70, 0xa9, 0x63, 0x5b, 0xd2, 0x7c, 0x40, 0xf7, 0x9a, 0x07, 0x10, 0x0c, 0x65, 0x14, 0x96, 0xef,
	0xe2, 0x6f, 0xb5, 0x83, 0x26, 0x99, 0xa1, 0x32, 0xfc, 0x4d, 0x0d, 0xd8, 0x9e, 0x08, 0x22, 0x9e,
	0x0b, 0xd4, 0x2e, 0x72, 0x7f, 0x03, 0x9a, 0x14, 0x7c, 0x72, 0xc3, 0xf4, 0x95, 0x0d, 0x04, 0xc8,
	0x8f, 0x53, 0x32, 0xa8, 0x76, 0xae, 0x0c, 0xaa, 0xcf, 0x66, 0xd0, 0x16, 0xac, 0x4c, 0xc2, 0x2e,
	0x8b, 0xd6, 0x00, 0x8a, 0xf8, 0xf5, 0x43, 0xf6, 0x1e, 0xb4, 0xcb, 0xd1, 0x93, 0x45, 0xbb, 0xb3,
	0x32, 0x09, 0x43, 0x3f, 0x1c, 0xfb, 0x14, 0x0a, 0x15, 0xf8, 0x54, 0x82, 0x8b, 0xbe, 0x12, 0xe1,
	0x3d, 0xa1, 0x82, 0x5d, 0x04, 0xd9, 0xc7, 0xd0, 0x9d, 0xd2, 0xf3, 0xc3, 0x51, 0xce, 0xa9, 0x76,
	0x2e, 0x91, 0xdd, 0xb5, 0xca, 0x84, 0x3d, 0x2b, 0x44, 0x37, 0xcc, 0xd3, 0x3e, 0xc5, 0x48, 0xcb,
	0x78, 0x60, 0x6f, 0x61, 0x3a, 0xa2, 0x3d, 0x04, 0xfb, 0xf1, 0xc0, 0x5d, 0x83, 0xd5, 0x0a, 0x9b,
	0x86, 0x78, 0xf7, 0x39, 0x74, 0x70, 0x7c, 0x20, 0x78, 0x1e, 0x0c, 0x9f, 0x66, 0x64, 0x70, 0x7a,
	0xe7, 0xce, 0x39, 0x76, 0x5e, 0x9b, 0xdd, 0xb9, 0xfb, 0x66, 0x7c, 0x9c, 0xec, 0x2d, 0x7e, 0x56,
	0xf1, 0x9a, 0x59, 0x19, 0xcf, 0xc2, 0x59, 0x2b, 0xd7, 0x49, 0xa7, 0xba, 0xf2, 0xd7, 0x75, 0x53,
	0x56, 0xfa, 0x5a, 0xc4, 0xec, 0x2a, 0x2c, 0x11, 0x3f, 0xe3, 0x9d, 0x2c, 0xe2, 0xb0, 0x1f, 0x56,
	0x53, 0xa9, 0x36, 0x27, 0x95, 0x4a, 0x61, 0xab, 0x9f, 0x16, 0xb6, 0x59, 0xf6, 0x17, 0x66, 0xd9,
	0xc7, 0xd7, 0x30, 0x9d, 0x02, 0xfb, 0xb3, 0xc0, 0xb6, 0xc5, 0x08, 0xd9, 0x9e, 0x8c, 0x3a, 0xc3,
	0x20, 0x1d, 0x25, 0xda, 0x66, 0x47, 0x31, 0x44, 0xbe, 0x12, 0x19, 0xbc, 0x24, 0x27, 0xcd, 0x53,
	0x63, 0x3c, 0x66, 0x57, 0x60, 0x91, 0x1f, 0x73, 0xcd, 0x73, 0x1b, 0x72, 0x3b, 0xc2, 0xea, 0x57,
	0x1c, 0x3e, 0x19, 0x52, 0xbf, 0x55, 0xb9, 0xd7, 0xbe, 0x07, 0xed, 0xa9, 0x04, 0x03, 0x73, 0x4a,
	0x78, 0x25, 0xb1, 0xd0, 0x8a, 0xe5, 0x5a, 0x86, 0xdd, 0x65, 0x6b, 0xc5, 0x20, 0xa6, 0x1e, 0x98,
	0x60, 0xc9, 0xb0, 0xbb, 0x62, 0xfa, 0x66, 0x1a, 0x1b, 0x66, 0x87, 0x69, 0xf1, 0x78, 0x6c, 0xd1,
	0xbd, 0xd9, 0x18, 0xa6, 0xf6, 0xd9, 0xf8, 0xcf, 0xc9, 0x0d, 0x55, 0x74, 0x6f, 0x54, 0x67, 0x7f,
	0x66, 0xc3, 0x51, 0x2a, 0xb1, 0x3f, 0x3c, 0xab, 0x86, 0x94, 0x0d, 0xd0, 0xe5, 0xf4, 0x28, 0x4f,
	0x47, 0x19, 0x86, 0xdd, 0x84, 0x12, 0xe5, 0xeb, 0xff, 0x72, 0xa0, 0x55, 0x91, 0xd1, 0x03, 0x13,
	0xef, 0xc1, 0x9c, 0x27, 0x83, 0xa2, 0x8a, 0x34, 0x11, 0xf1, 0x10, 0x60, 0x9f, 0x96, 0x3d, 0x31,
	0xf5, 0xf8, 0xd6, 0x5c, 0x4f, 0x8a, 0x3c, 0x9b, 0x2c, 0x88, 0x05, 0x54, 0x2a, 0x2c, 0x2e, 0xf6,
	0x41, 0x75, 0x01, 0x9f, 0x6c, 0x44, 0xd8, 0x90, 0x2b, 0x3f, 0x4e, 0x73, 0x61, 0xdf, 0x4f, 0x4b,
	0x43, 0xae, 0x9e, 0xa4, 0x39, 0x5d, 0x57, 0xf4, 0x54, 0x33, 0xd5, 0xc8, 0xa4, 0x47, 0xd3, 0x3c,
	0xde, 0xf0, 0x5c, 0x5c, 0x83, 0xc6, 0xb8, 0x54, 0x99, 0x9e, 0x7e, 0x49, 0x98, 0x32, 0xe5, 0xfe,
	0xd1, 0x81, 0xb5, 0x67, 0xc9, 0x51, 0x1a, 0x85, 0xd3, 0x07, 0xad, 0x6a, 0xd3, 0x79, 0x9b, 0xcd,
	0x5a, 0xc5, 0xe6, 0xcc, 0x31, 0xac, 0x9f, 0xe3, 0x18, 0x2e, 0x9c, 0x72, 0x0c, 0x7f, 0x0e, 0x57,
	0xa6, 0x5d, 0xb3, 0xb1, 0xfe, 0x74, 0x36, 0xd6, 0xff, 0x0f, 0xc3, 0xee, 0x16, 0x6c, 0x96, 0x32,
	0x00, 0x5f, 0xd2, 0x9f, 0x71, 0x25, 0x6c, 0x93, 0x4e, 0x97, 0xf9, 0x9f, 0x1d, 0xb8, 0x39, 0x57,
	0xc5, 0x7a, 0x71, 0x17, 0x58, 0xf1, 0x47, 0xcc, 0x94, 0x46, 0x6a, 0x16, 0x0c, 0x53, 0x1d, 0xfb,
	0x67, 0xcc, 0x08, 0xec, 0x8f, 0x11, 0xf2, 0x99, 0x67, 0x59, 0x74, 0xe2, 0x0b, 0x7a, 0x9a, 0xdb,
	0x9f, 0xa8, 0x88, 0xef, 0x22, 0x4c, 0x2f, 0x73, 0xf6, 0x2e, 0xb4, 0x46, 0x09, 0x75, 0x40, 0xb9,
	0xe0, 0x2a, 0x4d, 0x8a, 0x06, 0xdc, 0x80, 0x1e, 0x61, 0x77, 0x7e, 0xe3, 0x9c, 0xf6, 0xa7, 0x8e,
	0x7e, 0x45, 0xb8, 0xb0, 0x79, 0xba, 0xc4, 0xdf, 0x93, 0x0a, 0x4d, 0x74, 0xde, 0x61, 0xb7, 0xe0,
	0xc6, 0x1c, 0x9d, 0x87, 0xb4, 0x4a, 0xc7, 0x61, 0x37, 0x61, 0x63, 0x8e, 0xca, 0x33, 0x25, 0xc2,
	0x4e, 0xed, 0xce, 0x7f, 0x1c, 0x68, 0x7b, 0xd5, 0xb7, 0xe1, 0x35, 0x58, 0xab, 0x22, 0xfd, 0xe4,
	0x98, 0x47, 0x32, 0xec, 0xbc, 0xc3, 0xd6, 0xe0, 0x52, 0x55, 0xd4, 0xe3, 0x49, 0xc7, 0x99, 0x9d,
	0x71, 0x30, 0x0a, 0x02, 0xa1, 0x54, 0xa7, 0xc6, 0x36, 0x27, 0xcf, 0x07, 0x23, 0xb2, 0x3f, 0x0b,
	0x9e, 0xea, 0xa1, 0xc8, 0x3b, 0x75, 0x76, 0x15, 0x56, 0xab, 0xf2, 0xfd, 0x74, 0x37, 0xd0, 0x9d,
	0x05, 0xd6, 0x85, 0xcb, 0x33, 0x4b, 0x05, 0x22, 0xea, 0x5c, 0x98, 0x35, 0xb9, 0x9f, 0x3e, 0x4d,
	0x4c, 0x1f, 0x7a, 0x2c, 0x3a, 0x8b, 0xb3, 0x33, 0x3d, 0x71, 0x2c, 0xc5, 0xeb, 0xce, 0xd2, 0x67,
	0x3f, 0x7a, 0xfe, 0xc9, 0x20, 0x8d, 0x78, 0x32, 0xd8, 0xf9, 0xe8, 0x81, 0xd6, 0x3b, 0x41, 0x1a,
	0xdf, 0xa7, 0xdf, 0xeb, 0x41, 0x1a, 0xdd, 0xc7, 0x6e, 0x48, 0x06, 0x42, 0xcd, 0xfb, 0x61, 0xff,
	0x62, 0x91, 0x54, 0x3f, 0xfc, 0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x39, 0x9e, 0x8d, 0x7c, 0xf9,
	0x17, 0x00, 0x00,
}
