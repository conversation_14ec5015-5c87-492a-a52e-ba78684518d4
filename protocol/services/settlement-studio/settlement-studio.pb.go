// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/settlement-studio/settlement-studio.proto

package settlement_studio // import "golang.52tt.com/protocol/services/settlement-studio"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 工作室入驻类型
type JoinT int32

const (
	JoinT_JOIN_T_UNSPECIFIED      JoinT = 0
	JoinT_JOIN_T_SELF             JoinT = 1
	JoinT_JOIN_T_SERVICE_PROVIDER JoinT = 2
)

var JoinT_name = map[int32]string{
	0: "JOIN_T_UNSPECIFIED",
	1: "JOIN_T_SELF",
	2: "JOIN_T_SERVICE_PROVIDER",
}
var JoinT_value = map[string]int32{
	"JOIN_T_UNSPECIFIED":      0,
	"JOIN_T_SELF":             1,
	"JOIN_T_SERVICE_PROVIDER": 2,
}

func (x JoinT) String() string {
	return proto.EnumName(JoinT_name, int32(x))
}
func (JoinT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{0}
}

// 工作室申请类型
type ApplyT int32

const (
	ApplyT_APPLY_T_UNSPECIFIED               ApplyT = 0
	ApplyT_APPLY_T_SELF_JOIN                 ApplyT = 1
	ApplyT_APPLY_T_SERVICE_PROVIDER_JOIN     ApplyT = 2
	ApplyT_APPLY_T_SELF_TERMINAL             ApplyT = 3
	ApplyT_APPLY_T_SERVICE_PROVIDER_TERMINAL ApplyT = 4
)

var ApplyT_name = map[int32]string{
	0: "APPLY_T_UNSPECIFIED",
	1: "APPLY_T_SELF_JOIN",
	2: "APPLY_T_SERVICE_PROVIDER_JOIN",
	3: "APPLY_T_SELF_TERMINAL",
	4: "APPLY_T_SERVICE_PROVIDER_TERMINAL",
}
var ApplyT_value = map[string]int32{
	"APPLY_T_UNSPECIFIED":               0,
	"APPLY_T_SELF_JOIN":                 1,
	"APPLY_T_SERVICE_PROVIDER_JOIN":     2,
	"APPLY_T_SELF_TERMINAL":             3,
	"APPLY_T_SERVICE_PROVIDER_TERMINAL": 4,
}

func (x ApplyT) String() string {
	return proto.EnumName(ApplyT_name, int32(x))
}
func (ApplyT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{1}
}

// 发票类型
type InvokeT int32

const (
	InvokeT_INVOKE_T_UNSPECIFIED InvokeT = 0
	InvokeT_INVOKE_T_REGULAR     InvokeT = 1
	InvokeT_INVOKE_T_VAT_SPECIAL InvokeT = 2
)

var InvokeT_name = map[int32]string{
	0: "INVOKE_T_UNSPECIFIED",
	1: "INVOKE_T_REGULAR",
	2: "INVOKE_T_VAT_SPECIAL",
}
var InvokeT_value = map[string]int32{
	"INVOKE_T_UNSPECIFIED": 0,
	"INVOKE_T_REGULAR":     1,
	"INVOKE_T_VAT_SPECIAL": 2,
}

func (x InvokeT) String() string {
	return proto.EnumName(InvokeT_name, int32(x))
}
func (InvokeT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{2}
}

// （对外）工作室入驻申请状态
type JoinApplyStatus int32

const (
	JoinApplyStatus_JOIN_APPLY_STATUS_UNSPECIFIED       JoinApplyStatus = 0
	JoinApplyStatus_JOIN_APPLY_STATUS_SUBMITTED         JoinApplyStatus = 1
	JoinApplyStatus_JOIN_APPLY_STATUS_APPROVED          JoinApplyStatus = 2
	JoinApplyStatus_JOIN_APPLY_STATUS_REJECTED          JoinApplyStatus = 3
	JoinApplyStatus_JOIN_APPLY_STATUS_USER_SIGNED       JoinApplyStatus = 4
	JoinApplyStatus_JOIN_APPLY_STATUS_USER_REJECTED     JoinApplyStatus = 5
	JoinApplyStatus_JOIN_APPLY_STATUS_PLATFORM_SIGNED   JoinApplyStatus = 6
	JoinApplyStatus_JOIN_APPLY_STATUS_PLATFORM_REJECTED JoinApplyStatus = 7
)

var JoinApplyStatus_name = map[int32]string{
	0: "JOIN_APPLY_STATUS_UNSPECIFIED",
	1: "JOIN_APPLY_STATUS_SUBMITTED",
	2: "JOIN_APPLY_STATUS_APPROVED",
	3: "JOIN_APPLY_STATUS_REJECTED",
	4: "JOIN_APPLY_STATUS_USER_SIGNED",
	5: "JOIN_APPLY_STATUS_USER_REJECTED",
	6: "JOIN_APPLY_STATUS_PLATFORM_SIGNED",
	7: "JOIN_APPLY_STATUS_PLATFORM_REJECTED",
}
var JoinApplyStatus_value = map[string]int32{
	"JOIN_APPLY_STATUS_UNSPECIFIED":       0,
	"JOIN_APPLY_STATUS_SUBMITTED":         1,
	"JOIN_APPLY_STATUS_APPROVED":          2,
	"JOIN_APPLY_STATUS_REJECTED":          3,
	"JOIN_APPLY_STATUS_USER_SIGNED":       4,
	"JOIN_APPLY_STATUS_USER_REJECTED":     5,
	"JOIN_APPLY_STATUS_PLATFORM_SIGNED":   6,
	"JOIN_APPLY_STATUS_PLATFORM_REJECTED": 7,
}

func (x JoinApplyStatus) String() string {
	return proto.EnumName(JoinApplyStatus_name, int32(x))
}
func (JoinApplyStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{3}
}

// （对外）工作室解除申请状态
type TerminalApplyStatus int32

const (
	TerminalApplyStatus_TERMINAL_APPLY_STATUS_UNSPECIFIED TerminalApplyStatus = 0
	TerminalApplyStatus_TERMINAL_APPLY_STATUS_SUBMITTED   TerminalApplyStatus = 1
	TerminalApplyStatus_TERMINAL_APPLY_STATUS_APPROVED    TerminalApplyStatus = 2
	TerminalApplyStatus_TERMINAL_APPLY_STATUS_REJECTED    TerminalApplyStatus = 3
)

var TerminalApplyStatus_name = map[int32]string{
	0: "TERMINAL_APPLY_STATUS_UNSPECIFIED",
	1: "TERMINAL_APPLY_STATUS_SUBMITTED",
	2: "TERMINAL_APPLY_STATUS_APPROVED",
	3: "TERMINAL_APPLY_STATUS_REJECTED",
}
var TerminalApplyStatus_value = map[string]int32{
	"TERMINAL_APPLY_STATUS_UNSPECIFIED": 0,
	"TERMINAL_APPLY_STATUS_SUBMITTED":   1,
	"TERMINAL_APPLY_STATUS_APPROVED":    2,
	"TERMINAL_APPLY_STATUS_REJECTED":    3,
}

func (x TerminalApplyStatus) String() string {
	return proto.EnumName(TerminalApplyStatus_name, int32(x))
}
func (TerminalApplyStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{4}
}

// （对外）第三方入驻申请状态51
type ServiceProviderJoinStatus int32

const (
	ServiceProviderJoinStatus_SERVICE_PROVIDER_JOIN_STATUS_UNSPECIFIED ServiceProviderJoinStatus = 0
)

var ServiceProviderJoinStatus_name = map[int32]string{
	0: "SERVICE_PROVIDER_JOIN_STATUS_UNSPECIFIED",
}
var ServiceProviderJoinStatus_value = map[string]int32{
	"SERVICE_PROVIDER_JOIN_STATUS_UNSPECIFIED": 0,
}

func (x ServiceProviderJoinStatus) String() string {
	return proto.EnumName(ServiceProviderJoinStatus_name, int32(x))
}
func (ServiceProviderJoinStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{5}
}

// （对外）第三方解除申请状态51
type ServiceProviderTerminalStatus int32

const (
	ServiceProviderTerminalStatus_SERVICE_PROVIDER_TERMINAL_STATUS_UNSPECIFIED ServiceProviderTerminalStatus = 0
)

var ServiceProviderTerminalStatus_name = map[int32]string{
	0: "SERVICE_PROVIDER_TERMINAL_STATUS_UNSPECIFIED",
}
var ServiceProviderTerminalStatus_value = map[string]int32{
	"SERVICE_PROVIDER_TERMINAL_STATUS_UNSPECIFIED": 0,
}

func (x ServiceProviderTerminalStatus) String() string {
	return proto.EnumName(ServiceProviderTerminalStatus_name, int32(x))
}
func (ServiceProviderTerminalStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{6}
}

// （内部）申请流程主状态
type ApplyStatus int32

const (
	ApplyStatus_APPLY_STATUS_UNSPECIFIED                 ApplyStatus = 0
	ApplyStatus_APPLY_STATUS_SUBMITTED                   ApplyStatus = 1
	ApplyStatus_APPLY_STATUS_SERVICE_PROVIDER_PROCESSING ApplyStatus = 2
	ApplyStatus_APPLY_STATUS_SERVICE_PROVIDER_COMPLETED  ApplyStatus = 3
	ApplyStatus_APPLY_STATUS_MATERIAL_AUDITING           ApplyStatus = 4
	ApplyStatus_APPLY_STATUS_MATERIAL_REJECTED           ApplyStatus = 5
	ApplyStatus_APPLY_STATUS_SIGNING                     ApplyStatus = 6
	ApplyStatus_APPLY_STATUS_USER_REJECTED               ApplyStatus = 7
	ApplyStatus_APPLY_STATUS_PLATFORM_SIGNED             ApplyStatus = 8
	ApplyStatus_APPLY_STATUS_PLATFORM_REJECTED           ApplyStatus = 9
	ApplyStatus_APPLY_STATUS_COMPLETED                   ApplyStatus = 10
)

var ApplyStatus_name = map[int32]string{
	0:  "APPLY_STATUS_UNSPECIFIED",
	1:  "APPLY_STATUS_SUBMITTED",
	2:  "APPLY_STATUS_SERVICE_PROVIDER_PROCESSING",
	3:  "APPLY_STATUS_SERVICE_PROVIDER_COMPLETED",
	4:  "APPLY_STATUS_MATERIAL_AUDITING",
	5:  "APPLY_STATUS_MATERIAL_REJECTED",
	6:  "APPLY_STATUS_SIGNING",
	7:  "APPLY_STATUS_USER_REJECTED",
	8:  "APPLY_STATUS_PLATFORM_SIGNED",
	9:  "APPLY_STATUS_PLATFORM_REJECTED",
	10: "APPLY_STATUS_COMPLETED",
}
var ApplyStatus_value = map[string]int32{
	"APPLY_STATUS_UNSPECIFIED":                 0,
	"APPLY_STATUS_SUBMITTED":                   1,
	"APPLY_STATUS_SERVICE_PROVIDER_PROCESSING": 2,
	"APPLY_STATUS_SERVICE_PROVIDER_COMPLETED":  3,
	"APPLY_STATUS_MATERIAL_AUDITING":           4,
	"APPLY_STATUS_MATERIAL_REJECTED":           5,
	"APPLY_STATUS_SIGNING":                     6,
	"APPLY_STATUS_USER_REJECTED":               7,
	"APPLY_STATUS_PLATFORM_SIGNED":             8,
	"APPLY_STATUS_PLATFORM_REJECTED":           9,
	"APPLY_STATUS_COMPLETED":                   10,
}

func (x ApplyStatus) String() string {
	return proto.EnumName(ApplyStatus_name, int32(x))
}
func (ApplyStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{7}
}

// 提现订单状态
type WithdrawalStatus int32

const (
	WithdrawalStatus_WITHDRAWAL_STATUS_UNSPECIFIED  WithdrawalStatus = 0
	WithdrawalStatus_WITHDRAWAL_STATUS_WAIT_INVOICE WithdrawalStatus = 1
	WithdrawalStatus_WITHDRAWAL_STATUS_AUDITING     WithdrawalStatus = 2
	WithdrawalStatus_WITHDRAWAL_STATUS_REJECTED     WithdrawalStatus = 3
	WithdrawalStatus_WITHDRAWAL_STATUS_PENDING      WithdrawalStatus = 4
	WithdrawalStatus_WITHDRAWAL_STATUS_PAID         WithdrawalStatus = 5
)

var WithdrawalStatus_name = map[int32]string{
	0: "WITHDRAWAL_STATUS_UNSPECIFIED",
	1: "WITHDRAWAL_STATUS_WAIT_INVOICE",
	2: "WITHDRAWAL_STATUS_AUDITING",
	3: "WITHDRAWAL_STATUS_REJECTED",
	4: "WITHDRAWAL_STATUS_PENDING",
	5: "WITHDRAWAL_STATUS_PAID",
}
var WithdrawalStatus_value = map[string]int32{
	"WITHDRAWAL_STATUS_UNSPECIFIED":  0,
	"WITHDRAWAL_STATUS_WAIT_INVOICE": 1,
	"WITHDRAWAL_STATUS_AUDITING":     2,
	"WITHDRAWAL_STATUS_REJECTED":     3,
	"WITHDRAWAL_STATUS_PENDING":      4,
	"WITHDRAWAL_STATUS_PAID":         5,
}

func (x WithdrawalStatus) String() string {
	return proto.EnumName(WithdrawalStatus_name, int32(x))
}
func (WithdrawalStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{8}
}

// 纳税主体类型
type TaxpayerEntityT int32

const (
	TaxpayerEntityT_TAXPAYER_ENTITY_T_UNSPECIFIED TaxpayerEntityT = 0
	TaxpayerEntityT_TAXPAYER_ENTITY_T_GENERAL     TaxpayerEntityT = 1
	TaxpayerEntityT_TAXPAYER_ENTITY_T_SMALL_SCALE TaxpayerEntityT = 2
)

var TaxpayerEntityT_name = map[int32]string{
	0: "TAXPAYER_ENTITY_T_UNSPECIFIED",
	1: "TAXPAYER_ENTITY_T_GENERAL",
	2: "TAXPAYER_ENTITY_T_SMALL_SCALE",
}
var TaxpayerEntityT_value = map[string]int32{
	"TAXPAYER_ENTITY_T_UNSPECIFIED": 0,
	"TAXPAYER_ENTITY_T_GENERAL":     1,
	"TAXPAYER_ENTITY_T_SMALL_SCALE": 2,
}

func (x TaxpayerEntityT) String() string {
	return proto.EnumName(TaxpayerEntityT_name, int32(x))
}
func (TaxpayerEntityT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{9}
}

type StudioApplyHistoryInfo_ChangeT int32

const (
	StudioApplyHistoryInfo_CHANGE_T_UNSPECIFIED StudioApplyHistoryInfo_ChangeT = 0
	StudioApplyHistoryInfo_CHANGE_T_JOIN        StudioApplyHistoryInfo_ChangeT = 1
	StudioApplyHistoryInfo_CHANGE_T_TERMINAL    StudioApplyHistoryInfo_ChangeT = 2
)

var StudioApplyHistoryInfo_ChangeT_name = map[int32]string{
	0: "CHANGE_T_UNSPECIFIED",
	1: "CHANGE_T_JOIN",
	2: "CHANGE_T_TERMINAL",
}
var StudioApplyHistoryInfo_ChangeT_value = map[string]int32{
	"CHANGE_T_UNSPECIFIED": 0,
	"CHANGE_T_JOIN":        1,
	"CHANGE_T_TERMINAL":    2,
}

func (x StudioApplyHistoryInfo_ChangeT) String() string {
	return proto.EnumName(StudioApplyHistoryInfo_ChangeT_name, int32(x))
}
func (StudioApplyHistoryInfo_ChangeT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{13, 0}
}

// 获取用户当前工作室状态
type GetUserStudioStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserStudioStatusReq) Reset()         { *m = GetUserStudioStatusReq{} }
func (m *GetUserStudioStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetUserStudioStatusReq) ProtoMessage()    {}
func (*GetUserStudioStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{0}
}
func (m *GetUserStudioStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserStudioStatusReq.Unmarshal(m, b)
}
func (m *GetUserStudioStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserStudioStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetUserStudioStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserStudioStatusReq.Merge(dst, src)
}
func (m *GetUserStudioStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetUserStudioStatusReq.Size(m)
}
func (m *GetUserStudioStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserStudioStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserStudioStatusReq proto.InternalMessageInfo

func (m *GetUserStudioStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserStudioStatusResp struct {
	IsStudioSettle       bool     `protobuf:"varint,1,opt,name=is_studio_settle,json=isStudioSettle,proto3" json:"is_studio_settle,omitempty"`
	IsApplying           bool     `protobuf:"varint,2,opt,name=is_applying,json=isApplying,proto3" json:"is_applying,omitempty"`
	ApplyType            ApplyT   `protobuf:"varint,3,opt,name=apply_type,json=applyType,proto3,enum=settlement_studio.ApplyT" json:"apply_type,omitempty"`
	ApplyStatus          uint32   `protobuf:"varint,4,opt,name=apply_status,json=applyStatus,proto3" json:"apply_status,omitempty"`
	ApplyId              string   `protobuf:"bytes,5,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserStudioStatusResp) Reset()         { *m = GetUserStudioStatusResp{} }
func (m *GetUserStudioStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetUserStudioStatusResp) ProtoMessage()    {}
func (*GetUserStudioStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{1}
}
func (m *GetUserStudioStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserStudioStatusResp.Unmarshal(m, b)
}
func (m *GetUserStudioStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserStudioStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetUserStudioStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserStudioStatusResp.Merge(dst, src)
}
func (m *GetUserStudioStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetUserStudioStatusResp.Size(m)
}
func (m *GetUserStudioStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserStudioStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserStudioStatusResp proto.InternalMessageInfo

func (m *GetUserStudioStatusResp) GetIsStudioSettle() bool {
	if m != nil {
		return m.IsStudioSettle
	}
	return false
}

func (m *GetUserStudioStatusResp) GetIsApplying() bool {
	if m != nil {
		return m.IsApplying
	}
	return false
}

func (m *GetUserStudioStatusResp) GetApplyType() ApplyT {
	if m != nil {
		return m.ApplyType
	}
	return ApplyT_APPLY_T_UNSPECIFIED
}

func (m *GetUserStudioStatusResp) GetApplyStatus() uint32 {
	if m != nil {
		return m.ApplyStatus
	}
	return 0
}

func (m *GetUserStudioStatusResp) GetApplyId() string {
	if m != nil {
		return m.ApplyId
	}
	return ""
}

// 检查是否符合工作室申请条件
type CheckStudioJoinApplyValidReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckStudioJoinApplyValidReq) Reset()         { *m = CheckStudioJoinApplyValidReq{} }
func (m *CheckStudioJoinApplyValidReq) String() string { return proto.CompactTextString(m) }
func (*CheckStudioJoinApplyValidReq) ProtoMessage()    {}
func (*CheckStudioJoinApplyValidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{2}
}
func (m *CheckStudioJoinApplyValidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckStudioJoinApplyValidReq.Unmarshal(m, b)
}
func (m *CheckStudioJoinApplyValidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckStudioJoinApplyValidReq.Marshal(b, m, deterministic)
}
func (dst *CheckStudioJoinApplyValidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckStudioJoinApplyValidReq.Merge(dst, src)
}
func (m *CheckStudioJoinApplyValidReq) XXX_Size() int {
	return xxx_messageInfo_CheckStudioJoinApplyValidReq.Size(m)
}
func (m *CheckStudioJoinApplyValidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckStudioJoinApplyValidReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckStudioJoinApplyValidReq proto.InternalMessageInfo

func (m *CheckStudioJoinApplyValidReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

type CheckStudioJoinApplyValidResp struct {
	IsValid              bool     `protobuf:"varint,1,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	Reason               string   `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckStudioJoinApplyValidResp) Reset()         { *m = CheckStudioJoinApplyValidResp{} }
func (m *CheckStudioJoinApplyValidResp) String() string { return proto.CompactTextString(m) }
func (*CheckStudioJoinApplyValidResp) ProtoMessage()    {}
func (*CheckStudioJoinApplyValidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{3}
}
func (m *CheckStudioJoinApplyValidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckStudioJoinApplyValidResp.Unmarshal(m, b)
}
func (m *CheckStudioJoinApplyValidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckStudioJoinApplyValidResp.Marshal(b, m, deterministic)
}
func (dst *CheckStudioJoinApplyValidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckStudioJoinApplyValidResp.Merge(dst, src)
}
func (m *CheckStudioJoinApplyValidResp) XXX_Size() int {
	return xxx_messageInfo_CheckStudioJoinApplyValidResp.Size(m)
}
func (m *CheckStudioJoinApplyValidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckStudioJoinApplyValidResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckStudioJoinApplyValidResp proto.InternalMessageInfo

func (m *CheckStudioJoinApplyValidResp) GetIsValid() bool {
	if m != nil {
		return m.IsValid
	}
	return false
}

func (m *CheckStudioJoinApplyValidResp) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

// 提交工作室申请
type SubmitStudioJoinApplyReq struct {
	Uid                  uint32      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	StudioApplyInfo      *StudioInfo `protobuf:"bytes,2,opt,name=studio_apply_info,json=studioApplyInfo,proto3" json:"studio_apply_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SubmitStudioJoinApplyReq) Reset()         { *m = SubmitStudioJoinApplyReq{} }
func (m *SubmitStudioJoinApplyReq) String() string { return proto.CompactTextString(m) }
func (*SubmitStudioJoinApplyReq) ProtoMessage()    {}
func (*SubmitStudioJoinApplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{4}
}
func (m *SubmitStudioJoinApplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitStudioJoinApplyReq.Unmarshal(m, b)
}
func (m *SubmitStudioJoinApplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitStudioJoinApplyReq.Marshal(b, m, deterministic)
}
func (dst *SubmitStudioJoinApplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitStudioJoinApplyReq.Merge(dst, src)
}
func (m *SubmitStudioJoinApplyReq) XXX_Size() int {
	return xxx_messageInfo_SubmitStudioJoinApplyReq.Size(m)
}
func (m *SubmitStudioJoinApplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitStudioJoinApplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitStudioJoinApplyReq proto.InternalMessageInfo

func (m *SubmitStudioJoinApplyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SubmitStudioJoinApplyReq) GetStudioApplyInfo() *StudioInfo {
	if m != nil {
		return m.StudioApplyInfo
	}
	return nil
}

type SubmitStudioJoinApplyResp struct {
	ApplyId              string   `protobuf:"bytes,1,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitStudioJoinApplyResp) Reset()         { *m = SubmitStudioJoinApplyResp{} }
func (m *SubmitStudioJoinApplyResp) String() string { return proto.CompactTextString(m) }
func (*SubmitStudioJoinApplyResp) ProtoMessage()    {}
func (*SubmitStudioJoinApplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{5}
}
func (m *SubmitStudioJoinApplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitStudioJoinApplyResp.Unmarshal(m, b)
}
func (m *SubmitStudioJoinApplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitStudioJoinApplyResp.Marshal(b, m, deterministic)
}
func (dst *SubmitStudioJoinApplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitStudioJoinApplyResp.Merge(dst, src)
}
func (m *SubmitStudioJoinApplyResp) XXX_Size() int {
	return xxx_messageInfo_SubmitStudioJoinApplyResp.Size(m)
}
func (m *SubmitStudioJoinApplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitStudioJoinApplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitStudioJoinApplyResp proto.InternalMessageInfo

func (m *SubmitStudioJoinApplyResp) GetApplyId() string {
	if m != nil {
		return m.ApplyId
	}
	return ""
}

// 工作室信息
type StudioInfo struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 公司信息
	CompanyName       string `protobuf:"bytes,2,opt,name=company_name,json=companyName,proto3" json:"company_name,omitempty"`
	CompanyCode       string `protobuf:"bytes,3,opt,name=company_code,json=companyCode,proto3" json:"company_code,omitempty"`
	LegalPerson       string `protobuf:"bytes,4,opt,name=legal_person,json=legalPerson,proto3" json:"legal_person,omitempty"`
	LegalPersonIdCard string `protobuf:"bytes,5,opt,name=legal_person_id_card,json=legalPersonIdCard,proto3" json:"legal_person_id_card,omitempty"`
	LegalPersonPhone  string `protobuf:"bytes,6,opt,name=legal_person_phone,json=legalPersonPhone,proto3" json:"legal_person_phone,omitempty"`
	// 银行信息
	CompanyAccountName      string `protobuf:"bytes,7,opt,name=company_account_name,json=companyAccountName,proto3" json:"company_account_name,omitempty"`
	BankAccount             string `protobuf:"bytes,8,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	BankBranch              string `protobuf:"bytes,9,opt,name=bank_branch,json=bankBranch,proto3" json:"bank_branch,omitempty"`
	BankBasicAccountFileKey string `protobuf:"bytes,10,opt,name=bank_basic_account_file_key,json=bankBasicAccountFileKey,proto3" json:"bank_basic_account_file_key,omitempty"`
	// 纳税信息
	TaxpayerEntityType     TaxpayerEntityT `protobuf:"varint,11,opt,name=taxpayer_entity_type,json=taxpayerEntityType,proto3,enum=settlement_studio.TaxpayerEntityT" json:"taxpayer_entity_type,omitempty"`
	BusinessLicenseFileKey string          `protobuf:"bytes,12,opt,name=business_license_file_key,json=businessLicenseFileKey,proto3" json:"business_license_file_key,omitempty"`
	GeneralTaxpayerFileKey string          `protobuf:"bytes,13,opt,name=general_taxpayer_file_key,json=generalTaxpayerFileKey,proto3" json:"general_taxpayer_file_key,omitempty"`
	GeneralTaxpayerRate    uint32          `protobuf:"varint,14,opt,name=general_taxpayer_rate,json=generalTaxpayerRate,proto3" json:"general_taxpayer_rate,omitempty"`
	SmallTaxpayerRate      uint32          `protobuf:"varint,15,opt,name=small_taxpayer_rate,json=smallTaxpayerRate,proto3" json:"small_taxpayer_rate,omitempty"`
	InvoiceType            InvokeT         `protobuf:"varint,16,opt,name=invoice_type,json=invoiceType,proto3,enum=settlement_studio.InvokeT" json:"invoice_type,omitempty"`
	// 联系信息
	ContactName       string   `protobuf:"bytes,17,opt,name=contact_name,json=contactName,proto3" json:"contact_name,omitempty"`
	ContactPhone      string   `protobuf:"bytes,18,opt,name=contact_phone,json=contactPhone,proto3" json:"contact_phone,omitempty"`
	ContactAddr       []string `protobuf:"bytes,19,rep,name=contact_addr,json=contactAddr,proto3" json:"contact_addr,omitempty"`
	ContactAddrDetail string   `protobuf:"bytes,20,opt,name=contact_addr_detail,json=contactAddrDetail,proto3" json:"contact_addr_detail,omitempty"`
	ContactEmail      string   `protobuf:"bytes,21,opt,name=contact_email,json=contactEmail,proto3" json:"contact_email,omitempty"`
	// 系统附加信息
	JoinType                JoinT    `protobuf:"varint,22,opt,name=join_type,json=joinType,proto3,enum=settlement_studio.JoinT" json:"join_type,omitempty"`
	ContractFlowId          string   `protobuf:"bytes,23,opt,name=contract_flow_id,json=contractFlowId,proto3" json:"contract_flow_id,omitempty"`
	ContractUrl             string   `protobuf:"bytes,24,opt,name=contract_url,json=contractUrl,proto3" json:"contract_url,omitempty"`
	BankBasicAccountFileUrl string   `protobuf:"bytes,25,opt,name=bank_basic_account_file_url,json=bankBasicAccountFileUrl,proto3" json:"bank_basic_account_file_url,omitempty"`
	BusinessLicenseFileUrl  string   `protobuf:"bytes,26,opt,name=business_license_file_url,json=businessLicenseFileUrl,proto3" json:"business_license_file_url,omitempty"`
	GeneralTaxpayerFileUrl  string   `protobuf:"bytes,27,opt,name=general_taxpayer_file_url,json=generalTaxpayerFileUrl,proto3" json:"general_taxpayer_file_url,omitempty"`
	ApplyTime               uint64   `protobuf:"varint,28,opt,name=apply_time,json=applyTime,proto3" json:"apply_time,omitempty"`
	JoinTime                uint64   `protobuf:"varint,29,opt,name=join_time,json=joinTime,proto3" json:"join_time,omitempty"`
	TerminalTime            uint64   `protobuf:"varint,30,opt,name=terminal_time,json=terminalTime,proto3" json:"terminal_time,omitempty"`
	ServiceProviderId       uint64   `protobuf:"varint,31,opt,name=service_provider_id,json=serviceProviderId,proto3" json:"service_provider_id,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *StudioInfo) Reset()         { *m = StudioInfo{} }
func (m *StudioInfo) String() string { return proto.CompactTextString(m) }
func (*StudioInfo) ProtoMessage()    {}
func (*StudioInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{6}
}
func (m *StudioInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StudioInfo.Unmarshal(m, b)
}
func (m *StudioInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StudioInfo.Marshal(b, m, deterministic)
}
func (dst *StudioInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StudioInfo.Merge(dst, src)
}
func (m *StudioInfo) XXX_Size() int {
	return xxx_messageInfo_StudioInfo.Size(m)
}
func (m *StudioInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StudioInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StudioInfo proto.InternalMessageInfo

func (m *StudioInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StudioInfo) GetCompanyName() string {
	if m != nil {
		return m.CompanyName
	}
	return ""
}

func (m *StudioInfo) GetCompanyCode() string {
	if m != nil {
		return m.CompanyCode
	}
	return ""
}

func (m *StudioInfo) GetLegalPerson() string {
	if m != nil {
		return m.LegalPerson
	}
	return ""
}

func (m *StudioInfo) GetLegalPersonIdCard() string {
	if m != nil {
		return m.LegalPersonIdCard
	}
	return ""
}

func (m *StudioInfo) GetLegalPersonPhone() string {
	if m != nil {
		return m.LegalPersonPhone
	}
	return ""
}

func (m *StudioInfo) GetCompanyAccountName() string {
	if m != nil {
		return m.CompanyAccountName
	}
	return ""
}

func (m *StudioInfo) GetBankAccount() string {
	if m != nil {
		return m.BankAccount
	}
	return ""
}

func (m *StudioInfo) GetBankBranch() string {
	if m != nil {
		return m.BankBranch
	}
	return ""
}

func (m *StudioInfo) GetBankBasicAccountFileKey() string {
	if m != nil {
		return m.BankBasicAccountFileKey
	}
	return ""
}

func (m *StudioInfo) GetTaxpayerEntityType() TaxpayerEntityT {
	if m != nil {
		return m.TaxpayerEntityType
	}
	return TaxpayerEntityT_TAXPAYER_ENTITY_T_UNSPECIFIED
}

func (m *StudioInfo) GetBusinessLicenseFileKey() string {
	if m != nil {
		return m.BusinessLicenseFileKey
	}
	return ""
}

func (m *StudioInfo) GetGeneralTaxpayerFileKey() string {
	if m != nil {
		return m.GeneralTaxpayerFileKey
	}
	return ""
}

func (m *StudioInfo) GetGeneralTaxpayerRate() uint32 {
	if m != nil {
		return m.GeneralTaxpayerRate
	}
	return 0
}

func (m *StudioInfo) GetSmallTaxpayerRate() uint32 {
	if m != nil {
		return m.SmallTaxpayerRate
	}
	return 0
}

func (m *StudioInfo) GetInvoiceType() InvokeT {
	if m != nil {
		return m.InvoiceType
	}
	return InvokeT_INVOKE_T_UNSPECIFIED
}

func (m *StudioInfo) GetContactName() string {
	if m != nil {
		return m.ContactName
	}
	return ""
}

func (m *StudioInfo) GetContactPhone() string {
	if m != nil {
		return m.ContactPhone
	}
	return ""
}

func (m *StudioInfo) GetContactAddr() []string {
	if m != nil {
		return m.ContactAddr
	}
	return nil
}

func (m *StudioInfo) GetContactAddrDetail() string {
	if m != nil {
		return m.ContactAddrDetail
	}
	return ""
}

func (m *StudioInfo) GetContactEmail() string {
	if m != nil {
		return m.ContactEmail
	}
	return ""
}

func (m *StudioInfo) GetJoinType() JoinT {
	if m != nil {
		return m.JoinType
	}
	return JoinT_JOIN_T_UNSPECIFIED
}

func (m *StudioInfo) GetContractFlowId() string {
	if m != nil {
		return m.ContractFlowId
	}
	return ""
}

func (m *StudioInfo) GetContractUrl() string {
	if m != nil {
		return m.ContractUrl
	}
	return ""
}

func (m *StudioInfo) GetBankBasicAccountFileUrl() string {
	if m != nil {
		return m.BankBasicAccountFileUrl
	}
	return ""
}

func (m *StudioInfo) GetBusinessLicenseFileUrl() string {
	if m != nil {
		return m.BusinessLicenseFileUrl
	}
	return ""
}

func (m *StudioInfo) GetGeneralTaxpayerFileUrl() string {
	if m != nil {
		return m.GeneralTaxpayerFileUrl
	}
	return ""
}

func (m *StudioInfo) GetApplyTime() uint64 {
	if m != nil {
		return m.ApplyTime
	}
	return 0
}

func (m *StudioInfo) GetJoinTime() uint64 {
	if m != nil {
		return m.JoinTime
	}
	return 0
}

func (m *StudioInfo) GetTerminalTime() uint64 {
	if m != nil {
		return m.TerminalTime
	}
	return 0
}

func (m *StudioInfo) GetServiceProviderId() uint64 {
	if m != nil {
		return m.ServiceProviderId
	}
	return 0
}

// 获取工作室申请信息
type GetStudioApplyInfoReq struct {
	ApplyId              string   `protobuf:"bytes,1,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStudioApplyInfoReq) Reset()         { *m = GetStudioApplyInfoReq{} }
func (m *GetStudioApplyInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetStudioApplyInfoReq) ProtoMessage()    {}
func (*GetStudioApplyInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{7}
}
func (m *GetStudioApplyInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStudioApplyInfoReq.Unmarshal(m, b)
}
func (m *GetStudioApplyInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStudioApplyInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetStudioApplyInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStudioApplyInfoReq.Merge(dst, src)
}
func (m *GetStudioApplyInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetStudioApplyInfoReq.Size(m)
}
func (m *GetStudioApplyInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStudioApplyInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStudioApplyInfoReq proto.InternalMessageInfo

func (m *GetStudioApplyInfoReq) GetApplyId() string {
	if m != nil {
		return m.ApplyId
	}
	return ""
}

type GetStudioApplyInfoResp struct {
	StudioInfo           *StudioInfo `protobuf:"bytes,1,opt,name=studio_info,json=studioInfo,proto3" json:"studio_info,omitempty"`
	ApplyType            ApplyT      `protobuf:"varint,2,opt,name=apply_type,json=applyType,proto3,enum=settlement_studio.ApplyT" json:"apply_type,omitempty"`
	ApplyStatus          uint32      `protobuf:"varint,3,opt,name=apply_status,json=applyStatus,proto3" json:"apply_status,omitempty"`
	Reason               string      `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	CreateTime           uint64      `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           uint64      `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetStudioApplyInfoResp) Reset()         { *m = GetStudioApplyInfoResp{} }
func (m *GetStudioApplyInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetStudioApplyInfoResp) ProtoMessage()    {}
func (*GetStudioApplyInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{8}
}
func (m *GetStudioApplyInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStudioApplyInfoResp.Unmarshal(m, b)
}
func (m *GetStudioApplyInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStudioApplyInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetStudioApplyInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStudioApplyInfoResp.Merge(dst, src)
}
func (m *GetStudioApplyInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetStudioApplyInfoResp.Size(m)
}
func (m *GetStudioApplyInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStudioApplyInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStudioApplyInfoResp proto.InternalMessageInfo

func (m *GetStudioApplyInfoResp) GetStudioInfo() *StudioInfo {
	if m != nil {
		return m.StudioInfo
	}
	return nil
}

func (m *GetStudioApplyInfoResp) GetApplyType() ApplyT {
	if m != nil {
		return m.ApplyType
	}
	return ApplyT_APPLY_T_UNSPECIFIED
}

func (m *GetStudioApplyInfoResp) GetApplyStatus() uint32 {
	if m != nil {
		return m.ApplyStatus
	}
	return 0
}

func (m *GetStudioApplyInfoResp) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *GetStudioApplyInfoResp) GetCreateTime() uint64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GetStudioApplyInfoResp) GetUpdateTime() uint64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// 获取工作室信息
type GetStudioInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStudioInfoReq) Reset()         { *m = GetStudioInfoReq{} }
func (m *GetStudioInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetStudioInfoReq) ProtoMessage()    {}
func (*GetStudioInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{9}
}
func (m *GetStudioInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStudioInfoReq.Unmarshal(m, b)
}
func (m *GetStudioInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStudioInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetStudioInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStudioInfoReq.Merge(dst, src)
}
func (m *GetStudioInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetStudioInfoReq.Size(m)
}
func (m *GetStudioInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStudioInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStudioInfoReq proto.InternalMessageInfo

func (m *GetStudioInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetStudioInfoResp struct {
	StudioInfo           *StudioInfo `protobuf:"bytes,1,opt,name=studio_info,json=studioInfo,proto3" json:"studio_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetStudioInfoResp) Reset()         { *m = GetStudioInfoResp{} }
func (m *GetStudioInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetStudioInfoResp) ProtoMessage()    {}
func (*GetStudioInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{10}
}
func (m *GetStudioInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStudioInfoResp.Unmarshal(m, b)
}
func (m *GetStudioInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStudioInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetStudioInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStudioInfoResp.Merge(dst, src)
}
func (m *GetStudioInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetStudioInfoResp.Size(m)
}
func (m *GetStudioInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStudioInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStudioInfoResp proto.InternalMessageInfo

func (m *GetStudioInfoResp) GetStudioInfo() *StudioInfo {
	if m != nil {
		return m.StudioInfo
	}
	return nil
}

// 获取电子合同
type GetElectronicContractReq struct {
	ApplyId              string   `protobuf:"bytes,1,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetElectronicContractReq) Reset()         { *m = GetElectronicContractReq{} }
func (m *GetElectronicContractReq) String() string { return proto.CompactTextString(m) }
func (*GetElectronicContractReq) ProtoMessage()    {}
func (*GetElectronicContractReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{11}
}
func (m *GetElectronicContractReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetElectronicContractReq.Unmarshal(m, b)
}
func (m *GetElectronicContractReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetElectronicContractReq.Marshal(b, m, deterministic)
}
func (dst *GetElectronicContractReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetElectronicContractReq.Merge(dst, src)
}
func (m *GetElectronicContractReq) XXX_Size() int {
	return xxx_messageInfo_GetElectronicContractReq.Size(m)
}
func (m *GetElectronicContractReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetElectronicContractReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetElectronicContractReq proto.InternalMessageInfo

func (m *GetElectronicContractReq) GetApplyId() string {
	if m != nil {
		return m.ApplyId
	}
	return ""
}

type GetElectronicContractResp struct {
	ContractUrl          string   `protobuf:"bytes,1,opt,name=contract_url,json=contractUrl,proto3" json:"contract_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetElectronicContractResp) Reset()         { *m = GetElectronicContractResp{} }
func (m *GetElectronicContractResp) String() string { return proto.CompactTextString(m) }
func (*GetElectronicContractResp) ProtoMessage()    {}
func (*GetElectronicContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{12}
}
func (m *GetElectronicContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetElectronicContractResp.Unmarshal(m, b)
}
func (m *GetElectronicContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetElectronicContractResp.Marshal(b, m, deterministic)
}
func (dst *GetElectronicContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetElectronicContractResp.Merge(dst, src)
}
func (m *GetElectronicContractResp) XXX_Size() int {
	return xxx_messageInfo_GetElectronicContractResp.Size(m)
}
func (m *GetElectronicContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetElectronicContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetElectronicContractResp proto.InternalMessageInfo

func (m *GetElectronicContractResp) GetContractUrl() string {
	if m != nil {
		return m.ContractUrl
	}
	return ""
}

// 工作室变更历史
type StudioApplyHistoryInfo struct {
	ApplyId              string                         `protobuf:"bytes,1,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	StudioInfo           *StudioInfo                    `protobuf:"bytes,2,opt,name=studio_info,json=studioInfo,proto3" json:"studio_info,omitempty"`
	ChangeType           StudioApplyHistoryInfo_ChangeT `protobuf:"varint,3,opt,name=change_type,json=changeType,proto3,enum=settlement_studio.StudioApplyHistoryInfo_ChangeT" json:"change_type,omitempty"`
	LegalPersonName      string                         `protobuf:"bytes,4,opt,name=legal_person_name,json=legalPersonName,proto3" json:"legal_person_name,omitempty"`
	ChangeTime           uint64                         `protobuf:"varint,6,opt,name=change_time,json=changeTime,proto3" json:"change_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *StudioApplyHistoryInfo) Reset()         { *m = StudioApplyHistoryInfo{} }
func (m *StudioApplyHistoryInfo) String() string { return proto.CompactTextString(m) }
func (*StudioApplyHistoryInfo) ProtoMessage()    {}
func (*StudioApplyHistoryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{13}
}
func (m *StudioApplyHistoryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StudioApplyHistoryInfo.Unmarshal(m, b)
}
func (m *StudioApplyHistoryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StudioApplyHistoryInfo.Marshal(b, m, deterministic)
}
func (dst *StudioApplyHistoryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StudioApplyHistoryInfo.Merge(dst, src)
}
func (m *StudioApplyHistoryInfo) XXX_Size() int {
	return xxx_messageInfo_StudioApplyHistoryInfo.Size(m)
}
func (m *StudioApplyHistoryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StudioApplyHistoryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StudioApplyHistoryInfo proto.InternalMessageInfo

func (m *StudioApplyHistoryInfo) GetApplyId() string {
	if m != nil {
		return m.ApplyId
	}
	return ""
}

func (m *StudioApplyHistoryInfo) GetStudioInfo() *StudioInfo {
	if m != nil {
		return m.StudioInfo
	}
	return nil
}

func (m *StudioApplyHistoryInfo) GetChangeType() StudioApplyHistoryInfo_ChangeT {
	if m != nil {
		return m.ChangeType
	}
	return StudioApplyHistoryInfo_CHANGE_T_UNSPECIFIED
}

func (m *StudioApplyHistoryInfo) GetLegalPersonName() string {
	if m != nil {
		return m.LegalPersonName
	}
	return ""
}

func (m *StudioApplyHistoryInfo) GetChangeTime() uint64 {
	if m != nil {
		return m.ChangeTime
	}
	return 0
}

// 获取工作室变更历史列表
type GetStudioApplyHistoryListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStudioApplyHistoryListReq) Reset()         { *m = GetStudioApplyHistoryListReq{} }
func (m *GetStudioApplyHistoryListReq) String() string { return proto.CompactTextString(m) }
func (*GetStudioApplyHistoryListReq) ProtoMessage()    {}
func (*GetStudioApplyHistoryListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{14}
}
func (m *GetStudioApplyHistoryListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStudioApplyHistoryListReq.Unmarshal(m, b)
}
func (m *GetStudioApplyHistoryListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStudioApplyHistoryListReq.Marshal(b, m, deterministic)
}
func (dst *GetStudioApplyHistoryListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStudioApplyHistoryListReq.Merge(dst, src)
}
func (m *GetStudioApplyHistoryListReq) XXX_Size() int {
	return xxx_messageInfo_GetStudioApplyHistoryListReq.Size(m)
}
func (m *GetStudioApplyHistoryListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStudioApplyHistoryListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStudioApplyHistoryListReq proto.InternalMessageInfo

func (m *GetStudioApplyHistoryListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStudioApplyHistoryListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetStudioApplyHistoryListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetStudioApplyHistoryListResp struct {
	ApplyHistoryList     []*StudioApplyHistoryInfo `protobuf:"bytes,1,rep,name=apply_history_list,json=applyHistoryList,proto3" json:"apply_history_list,omitempty"`
	Total                uint32                    `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetStudioApplyHistoryListResp) Reset()         { *m = GetStudioApplyHistoryListResp{} }
func (m *GetStudioApplyHistoryListResp) String() string { return proto.CompactTextString(m) }
func (*GetStudioApplyHistoryListResp) ProtoMessage()    {}
func (*GetStudioApplyHistoryListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{15}
}
func (m *GetStudioApplyHistoryListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStudioApplyHistoryListResp.Unmarshal(m, b)
}
func (m *GetStudioApplyHistoryListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStudioApplyHistoryListResp.Marshal(b, m, deterministic)
}
func (dst *GetStudioApplyHistoryListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStudioApplyHistoryListResp.Merge(dst, src)
}
func (m *GetStudioApplyHistoryListResp) XXX_Size() int {
	return xxx_messageInfo_GetStudioApplyHistoryListResp.Size(m)
}
func (m *GetStudioApplyHistoryListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStudioApplyHistoryListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStudioApplyHistoryListResp proto.InternalMessageInfo

func (m *GetStudioApplyHistoryListResp) GetApplyHistoryList() []*StudioApplyHistoryInfo {
	if m != nil {
		return m.ApplyHistoryList
	}
	return nil
}

func (m *GetStudioApplyHistoryListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 获取工作室变更历史快照信息
type GetStudioApplySnapshotReq struct {
	ApplyId              string   `protobuf:"bytes,1,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStudioApplySnapshotReq) Reset()         { *m = GetStudioApplySnapshotReq{} }
func (m *GetStudioApplySnapshotReq) String() string { return proto.CompactTextString(m) }
func (*GetStudioApplySnapshotReq) ProtoMessage()    {}
func (*GetStudioApplySnapshotReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{16}
}
func (m *GetStudioApplySnapshotReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStudioApplySnapshotReq.Unmarshal(m, b)
}
func (m *GetStudioApplySnapshotReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStudioApplySnapshotReq.Marshal(b, m, deterministic)
}
func (dst *GetStudioApplySnapshotReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStudioApplySnapshotReq.Merge(dst, src)
}
func (m *GetStudioApplySnapshotReq) XXX_Size() int {
	return xxx_messageInfo_GetStudioApplySnapshotReq.Size(m)
}
func (m *GetStudioApplySnapshotReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStudioApplySnapshotReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStudioApplySnapshotReq proto.InternalMessageInfo

func (m *GetStudioApplySnapshotReq) GetApplyId() string {
	if m != nil {
		return m.ApplyId
	}
	return ""
}

type GetStudioApplySnapshotResp struct {
	StudioInfo           *StudioInfo `protobuf:"bytes,1,opt,name=studio_info,json=studioInfo,proto3" json:"studio_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetStudioApplySnapshotResp) Reset()         { *m = GetStudioApplySnapshotResp{} }
func (m *GetStudioApplySnapshotResp) String() string { return proto.CompactTextString(m) }
func (*GetStudioApplySnapshotResp) ProtoMessage()    {}
func (*GetStudioApplySnapshotResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{17}
}
func (m *GetStudioApplySnapshotResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStudioApplySnapshotResp.Unmarshal(m, b)
}
func (m *GetStudioApplySnapshotResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStudioApplySnapshotResp.Marshal(b, m, deterministic)
}
func (dst *GetStudioApplySnapshotResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStudioApplySnapshotResp.Merge(dst, src)
}
func (m *GetStudioApplySnapshotResp) XXX_Size() int {
	return xxx_messageInfo_GetStudioApplySnapshotResp.Size(m)
}
func (m *GetStudioApplySnapshotResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStudioApplySnapshotResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStudioApplySnapshotResp proto.InternalMessageInfo

func (m *GetStudioApplySnapshotResp) GetStudioInfo() *StudioInfo {
	if m != nil {
		return m.StudioInfo
	}
	return nil
}

// 检查是否符合工作室解除条件
type CheckStudioTerminalValidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckStudioTerminalValidReq) Reset()         { *m = CheckStudioTerminalValidReq{} }
func (m *CheckStudioTerminalValidReq) String() string { return proto.CompactTextString(m) }
func (*CheckStudioTerminalValidReq) ProtoMessage()    {}
func (*CheckStudioTerminalValidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{18}
}
func (m *CheckStudioTerminalValidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckStudioTerminalValidReq.Unmarshal(m, b)
}
func (m *CheckStudioTerminalValidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckStudioTerminalValidReq.Marshal(b, m, deterministic)
}
func (dst *CheckStudioTerminalValidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckStudioTerminalValidReq.Merge(dst, src)
}
func (m *CheckStudioTerminalValidReq) XXX_Size() int {
	return xxx_messageInfo_CheckStudioTerminalValidReq.Size(m)
}
func (m *CheckStudioTerminalValidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckStudioTerminalValidReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckStudioTerminalValidReq proto.InternalMessageInfo

func (m *CheckStudioTerminalValidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckStudioTerminalValidResp struct {
	IsValid              bool     `protobuf:"varint,1,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	Reason               string   `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	RemainTerminalTimes  uint32   `protobuf:"varint,3,opt,name=remain_terminal_times,json=remainTerminalTimes,proto3" json:"remain_terminal_times,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckStudioTerminalValidResp) Reset()         { *m = CheckStudioTerminalValidResp{} }
func (m *CheckStudioTerminalValidResp) String() string { return proto.CompactTextString(m) }
func (*CheckStudioTerminalValidResp) ProtoMessage()    {}
func (*CheckStudioTerminalValidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{19}
}
func (m *CheckStudioTerminalValidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckStudioTerminalValidResp.Unmarshal(m, b)
}
func (m *CheckStudioTerminalValidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckStudioTerminalValidResp.Marshal(b, m, deterministic)
}
func (dst *CheckStudioTerminalValidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckStudioTerminalValidResp.Merge(dst, src)
}
func (m *CheckStudioTerminalValidResp) XXX_Size() int {
	return xxx_messageInfo_CheckStudioTerminalValidResp.Size(m)
}
func (m *CheckStudioTerminalValidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckStudioTerminalValidResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckStudioTerminalValidResp proto.InternalMessageInfo

func (m *CheckStudioTerminalValidResp) GetIsValid() bool {
	if m != nil {
		return m.IsValid
	}
	return false
}

func (m *CheckStudioTerminalValidResp) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *CheckStudioTerminalValidResp) GetRemainTerminalTimes() uint32 {
	if m != nil {
		return m.RemainTerminalTimes
	}
	return 0
}

// 提交解除工作室申请
type SubmitStudioTerminalApplyReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitStudioTerminalApplyReq) Reset()         { *m = SubmitStudioTerminalApplyReq{} }
func (m *SubmitStudioTerminalApplyReq) String() string { return proto.CompactTextString(m) }
func (*SubmitStudioTerminalApplyReq) ProtoMessage()    {}
func (*SubmitStudioTerminalApplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{20}
}
func (m *SubmitStudioTerminalApplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitStudioTerminalApplyReq.Unmarshal(m, b)
}
func (m *SubmitStudioTerminalApplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitStudioTerminalApplyReq.Marshal(b, m, deterministic)
}
func (dst *SubmitStudioTerminalApplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitStudioTerminalApplyReq.Merge(dst, src)
}
func (m *SubmitStudioTerminalApplyReq) XXX_Size() int {
	return xxx_messageInfo_SubmitStudioTerminalApplyReq.Size(m)
}
func (m *SubmitStudioTerminalApplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitStudioTerminalApplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitStudioTerminalApplyReq proto.InternalMessageInfo

func (m *SubmitStudioTerminalApplyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SubmitStudioTerminalApplyResp struct {
	ApplyId              string   `protobuf:"bytes,1,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitStudioTerminalApplyResp) Reset()         { *m = SubmitStudioTerminalApplyResp{} }
func (m *SubmitStudioTerminalApplyResp) String() string { return proto.CompactTextString(m) }
func (*SubmitStudioTerminalApplyResp) ProtoMessage()    {}
func (*SubmitStudioTerminalApplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{21}
}
func (m *SubmitStudioTerminalApplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitStudioTerminalApplyResp.Unmarshal(m, b)
}
func (m *SubmitStudioTerminalApplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitStudioTerminalApplyResp.Marshal(b, m, deterministic)
}
func (dst *SubmitStudioTerminalApplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitStudioTerminalApplyResp.Merge(dst, src)
}
func (m *SubmitStudioTerminalApplyResp) XXX_Size() int {
	return xxx_messageInfo_SubmitStudioTerminalApplyResp.Size(m)
}
func (m *SubmitStudioTerminalApplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitStudioTerminalApplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitStudioTerminalApplyResp proto.InternalMessageInfo

func (m *SubmitStudioTerminalApplyResp) GetApplyId() string {
	if m != nil {
		return m.ApplyId
	}
	return ""
}

// 提现记录简单信息
type WithdrawalOrderSimpleInfo struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string           `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Status               WithdrawalStatus `protobuf:"varint,3,opt,name=status,proto3,enum=settlement_studio.WithdrawalStatus" json:"status,omitempty"`
	CompanyName          string           `protobuf:"bytes,4,opt,name=company_name,json=companyName,proto3" json:"company_name,omitempty"`
	WithdrawalType       uint32           `protobuf:"varint,5,opt,name=withdrawal_type,json=withdrawalType,proto3" json:"withdrawal_type,omitempty"`
	ScoreType            uint32           `protobuf:"varint,6,opt,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
	Amount               uint64           `protobuf:"varint,7,opt,name=amount,proto3" json:"amount,omitempty"`
	WithdrawalTime       uint64           `protobuf:"varint,8,opt,name=withdrawal_time,json=withdrawalTime,proto3" json:"withdrawal_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WithdrawalOrderSimpleInfo) Reset()         { *m = WithdrawalOrderSimpleInfo{} }
func (m *WithdrawalOrderSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*WithdrawalOrderSimpleInfo) ProtoMessage()    {}
func (*WithdrawalOrderSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{22}
}
func (m *WithdrawalOrderSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WithdrawalOrderSimpleInfo.Unmarshal(m, b)
}
func (m *WithdrawalOrderSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WithdrawalOrderSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *WithdrawalOrderSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WithdrawalOrderSimpleInfo.Merge(dst, src)
}
func (m *WithdrawalOrderSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_WithdrawalOrderSimpleInfo.Size(m)
}
func (m *WithdrawalOrderSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WithdrawalOrderSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WithdrawalOrderSimpleInfo proto.InternalMessageInfo

func (m *WithdrawalOrderSimpleInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WithdrawalOrderSimpleInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *WithdrawalOrderSimpleInfo) GetStatus() WithdrawalStatus {
	if m != nil {
		return m.Status
	}
	return WithdrawalStatus_WITHDRAWAL_STATUS_UNSPECIFIED
}

func (m *WithdrawalOrderSimpleInfo) GetCompanyName() string {
	if m != nil {
		return m.CompanyName
	}
	return ""
}

func (m *WithdrawalOrderSimpleInfo) GetWithdrawalType() uint32 {
	if m != nil {
		return m.WithdrawalType
	}
	return 0
}

func (m *WithdrawalOrderSimpleInfo) GetScoreType() uint32 {
	if m != nil {
		return m.ScoreType
	}
	return 0
}

func (m *WithdrawalOrderSimpleInfo) GetAmount() uint64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *WithdrawalOrderSimpleInfo) GetWithdrawalTime() uint64 {
	if m != nil {
		return m.WithdrawalTime
	}
	return 0
}

// 提现记录详细信息
type WithdrawalOrderDetailInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WithdrawalOrderDetailInfo) Reset()         { *m = WithdrawalOrderDetailInfo{} }
func (m *WithdrawalOrderDetailInfo) String() string { return proto.CompactTextString(m) }
func (*WithdrawalOrderDetailInfo) ProtoMessage()    {}
func (*WithdrawalOrderDetailInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{23}
}
func (m *WithdrawalOrderDetailInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WithdrawalOrderDetailInfo.Unmarshal(m, b)
}
func (m *WithdrawalOrderDetailInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WithdrawalOrderDetailInfo.Marshal(b, m, deterministic)
}
func (dst *WithdrawalOrderDetailInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WithdrawalOrderDetailInfo.Merge(dst, src)
}
func (m *WithdrawalOrderDetailInfo) XXX_Size() int {
	return xxx_messageInfo_WithdrawalOrderDetailInfo.Size(m)
}
func (m *WithdrawalOrderDetailInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WithdrawalOrderDetailInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WithdrawalOrderDetailInfo proto.InternalMessageInfo

func (m *WithdrawalOrderDetailInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WithdrawalOrderDetailInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

// 获取工作室提现订单列表
type GetWithdrawalOrderListReq struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint32           `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32           `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	StartTime            uint64           `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint64           `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Status               WithdrawalStatus `protobuf:"varint,6,opt,name=status,proto3,enum=settlement_studio.WithdrawalStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetWithdrawalOrderListReq) Reset()         { *m = GetWithdrawalOrderListReq{} }
func (m *GetWithdrawalOrderListReq) String() string { return proto.CompactTextString(m) }
func (*GetWithdrawalOrderListReq) ProtoMessage()    {}
func (*GetWithdrawalOrderListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{24}
}
func (m *GetWithdrawalOrderListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWithdrawalOrderListReq.Unmarshal(m, b)
}
func (m *GetWithdrawalOrderListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWithdrawalOrderListReq.Marshal(b, m, deterministic)
}
func (dst *GetWithdrawalOrderListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWithdrawalOrderListReq.Merge(dst, src)
}
func (m *GetWithdrawalOrderListReq) XXX_Size() int {
	return xxx_messageInfo_GetWithdrawalOrderListReq.Size(m)
}
func (m *GetWithdrawalOrderListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWithdrawalOrderListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWithdrawalOrderListReq proto.InternalMessageInfo

func (m *GetWithdrawalOrderListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetWithdrawalOrderListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetWithdrawalOrderListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetWithdrawalOrderListReq) GetStartTime() uint64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetWithdrawalOrderListReq) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetWithdrawalOrderListReq) GetStatus() WithdrawalStatus {
	if m != nil {
		return m.Status
	}
	return WithdrawalStatus_WITHDRAWAL_STATUS_UNSPECIFIED
}

type GetWithdrawalOrderListResp struct {
	WithdrawalOrderList  []*WithdrawalOrderSimpleInfo `protobuf:"bytes,1,rep,name=withdrawal_order_list,json=withdrawalOrderList,proto3" json:"withdrawal_order_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetWithdrawalOrderListResp) Reset()         { *m = GetWithdrawalOrderListResp{} }
func (m *GetWithdrawalOrderListResp) String() string { return proto.CompactTextString(m) }
func (*GetWithdrawalOrderListResp) ProtoMessage()    {}
func (*GetWithdrawalOrderListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{25}
}
func (m *GetWithdrawalOrderListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWithdrawalOrderListResp.Unmarshal(m, b)
}
func (m *GetWithdrawalOrderListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWithdrawalOrderListResp.Marshal(b, m, deterministic)
}
func (dst *GetWithdrawalOrderListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWithdrawalOrderListResp.Merge(dst, src)
}
func (m *GetWithdrawalOrderListResp) XXX_Size() int {
	return xxx_messageInfo_GetWithdrawalOrderListResp.Size(m)
}
func (m *GetWithdrawalOrderListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWithdrawalOrderListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWithdrawalOrderListResp proto.InternalMessageInfo

func (m *GetWithdrawalOrderListResp) GetWithdrawalOrderList() []*WithdrawalOrderSimpleInfo {
	if m != nil {
		return m.WithdrawalOrderList
	}
	return nil
}

// 获取工作室提现订单详情
type GetWithdrawalOrderDetailReq struct {
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWithdrawalOrderDetailReq) Reset()         { *m = GetWithdrawalOrderDetailReq{} }
func (m *GetWithdrawalOrderDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetWithdrawalOrderDetailReq) ProtoMessage()    {}
func (*GetWithdrawalOrderDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{26}
}
func (m *GetWithdrawalOrderDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWithdrawalOrderDetailReq.Unmarshal(m, b)
}
func (m *GetWithdrawalOrderDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWithdrawalOrderDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetWithdrawalOrderDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWithdrawalOrderDetailReq.Merge(dst, src)
}
func (m *GetWithdrawalOrderDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetWithdrawalOrderDetailReq.Size(m)
}
func (m *GetWithdrawalOrderDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWithdrawalOrderDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWithdrawalOrderDetailReq proto.InternalMessageInfo

func (m *GetWithdrawalOrderDetailReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetWithdrawalOrderDetailResp struct {
	OrderInfo            *WithdrawalOrderDetailInfo `protobuf:"bytes,1,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetWithdrawalOrderDetailResp) Reset()         { *m = GetWithdrawalOrderDetailResp{} }
func (m *GetWithdrawalOrderDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetWithdrawalOrderDetailResp) ProtoMessage()    {}
func (*GetWithdrawalOrderDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{27}
}
func (m *GetWithdrawalOrderDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWithdrawalOrderDetailResp.Unmarshal(m, b)
}
func (m *GetWithdrawalOrderDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWithdrawalOrderDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetWithdrawalOrderDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWithdrawalOrderDetailResp.Merge(dst, src)
}
func (m *GetWithdrawalOrderDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetWithdrawalOrderDetailResp.Size(m)
}
func (m *GetWithdrawalOrderDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWithdrawalOrderDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWithdrawalOrderDetailResp proto.InternalMessageInfo

func (m *GetWithdrawalOrderDetailResp) GetOrderInfo() *WithdrawalOrderDetailInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

type SaveInvoiceFileReq struct {
	ReceiptId            string   `protobuf:"bytes,1,opt,name=receipt_id,json=receiptId,proto3" json:"receipt_id,omitempty"`
	FileName             string   `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	Size                 uint64   `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	ShowSize             string   `protobuf:"bytes,4,opt,name=show_size,json=showSize,proto3" json:"show_size,omitempty"`
	FileClass            string   `protobuf:"bytes,5,opt,name=file_class,json=fileClass,proto3" json:"file_class,omitempty"`
	Uid                  uint32   `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	ReceiptDate          string   `protobuf:"bytes,7,opt,name=receipt_date,json=receiptDate,proto3" json:"receipt_date,omitempty"`
	ReceiptCode          string   `protobuf:"bytes,8,opt,name=receipt_code,json=receiptCode,proto3" json:"receipt_code,omitempty"`
	ReceiptNo            string   `protobuf:"bytes,9,opt,name=receipt_no,json=receiptNo,proto3" json:"receipt_no,omitempty"`
	TaxRate              string   `protobuf:"bytes,10,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	TaxAmount            uint64   `protobuf:"varint,11,opt,name=tax_amount,json=taxAmount,proto3" json:"tax_amount,omitempty"`
	Amount               uint64   `protobuf:"varint,12,opt,name=amount,proto3" json:"amount,omitempty"`
	ExTaxAmount          uint64   `protobuf:"varint,13,opt,name=ex_tax_amount,json=exTaxAmount,proto3" json:"ex_tax_amount,omitempty"`
	VCode                string   `protobuf:"bytes,14,opt,name=v_code,json=vCode,proto3" json:"v_code,omitempty"`
	Content              string   `protobuf:"bytes,15,opt,name=content,proto3" json:"content,omitempty"`
	SellerName           string   `protobuf:"bytes,16,opt,name=seller_name,json=sellerName,proto3" json:"seller_name,omitempty"`
	SellerTaxNo          string   `protobuf:"bytes,17,opt,name=seller_tax_no,json=sellerTaxNo,proto3" json:"seller_tax_no,omitempty"`
	PurchaserName        string   `protobuf:"bytes,18,opt,name=purchaser_name,json=purchaserName,proto3" json:"purchaser_name,omitempty"`
	PurchaserTaxNo       string   `protobuf:"bytes,19,opt,name=purchaser_tax_no,json=purchaserTaxNo,proto3" json:"purchaser_tax_no,omitempty"`
	VerifyResult         string   `protobuf:"bytes,20,opt,name=verify_result,json=verifyResult,proto3" json:"verify_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveInvoiceFileReq) Reset()         { *m = SaveInvoiceFileReq{} }
func (m *SaveInvoiceFileReq) String() string { return proto.CompactTextString(m) }
func (*SaveInvoiceFileReq) ProtoMessage()    {}
func (*SaveInvoiceFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{28}
}
func (m *SaveInvoiceFileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveInvoiceFileReq.Unmarshal(m, b)
}
func (m *SaveInvoiceFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveInvoiceFileReq.Marshal(b, m, deterministic)
}
func (dst *SaveInvoiceFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveInvoiceFileReq.Merge(dst, src)
}
func (m *SaveInvoiceFileReq) XXX_Size() int {
	return xxx_messageInfo_SaveInvoiceFileReq.Size(m)
}
func (m *SaveInvoiceFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveInvoiceFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_SaveInvoiceFileReq proto.InternalMessageInfo

func (m *SaveInvoiceFileReq) GetReceiptId() string {
	if m != nil {
		return m.ReceiptId
	}
	return ""
}

func (m *SaveInvoiceFileReq) GetFileName() string {
	if m != nil {
		return m.FileName
	}
	return ""
}

func (m *SaveInvoiceFileReq) GetSize() uint64 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *SaveInvoiceFileReq) GetShowSize() string {
	if m != nil {
		return m.ShowSize
	}
	return ""
}

func (m *SaveInvoiceFileReq) GetFileClass() string {
	if m != nil {
		return m.FileClass
	}
	return ""
}

func (m *SaveInvoiceFileReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SaveInvoiceFileReq) GetReceiptDate() string {
	if m != nil {
		return m.ReceiptDate
	}
	return ""
}

func (m *SaveInvoiceFileReq) GetReceiptCode() string {
	if m != nil {
		return m.ReceiptCode
	}
	return ""
}

func (m *SaveInvoiceFileReq) GetReceiptNo() string {
	if m != nil {
		return m.ReceiptNo
	}
	return ""
}

func (m *SaveInvoiceFileReq) GetTaxRate() string {
	if m != nil {
		return m.TaxRate
	}
	return ""
}

func (m *SaveInvoiceFileReq) GetTaxAmount() uint64 {
	if m != nil {
		return m.TaxAmount
	}
	return 0
}

func (m *SaveInvoiceFileReq) GetAmount() uint64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *SaveInvoiceFileReq) GetExTaxAmount() uint64 {
	if m != nil {
		return m.ExTaxAmount
	}
	return 0
}

func (m *SaveInvoiceFileReq) GetVCode() string {
	if m != nil {
		return m.VCode
	}
	return ""
}

func (m *SaveInvoiceFileReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *SaveInvoiceFileReq) GetSellerName() string {
	if m != nil {
		return m.SellerName
	}
	return ""
}

func (m *SaveInvoiceFileReq) GetSellerTaxNo() string {
	if m != nil {
		return m.SellerTaxNo
	}
	return ""
}

func (m *SaveInvoiceFileReq) GetPurchaserName() string {
	if m != nil {
		return m.PurchaserName
	}
	return ""
}

func (m *SaveInvoiceFileReq) GetPurchaserTaxNo() string {
	if m != nil {
		return m.PurchaserTaxNo
	}
	return ""
}

func (m *SaveInvoiceFileReq) GetVerifyResult() string {
	if m != nil {
		return m.VerifyResult
	}
	return ""
}

type SaveInvoiceFileResp struct {
	ReceiptSign          string                           `protobuf:"bytes,1,opt,name=receipt_sign,json=receiptSign,proto3" json:"receipt_sign,omitempty"`
	ErrorList            []*SaveInvoiceFileResp_ErrorInfo `protobuf:"bytes,2,rep,name=error_list,json=errorList,proto3" json:"error_list,omitempty"`
	Result               bool                             `protobuf:"varint,3,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *SaveInvoiceFileResp) Reset()         { *m = SaveInvoiceFileResp{} }
func (m *SaveInvoiceFileResp) String() string { return proto.CompactTextString(m) }
func (*SaveInvoiceFileResp) ProtoMessage()    {}
func (*SaveInvoiceFileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{29}
}
func (m *SaveInvoiceFileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveInvoiceFileResp.Unmarshal(m, b)
}
func (m *SaveInvoiceFileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveInvoiceFileResp.Marshal(b, m, deterministic)
}
func (dst *SaveInvoiceFileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveInvoiceFileResp.Merge(dst, src)
}
func (m *SaveInvoiceFileResp) XXX_Size() int {
	return xxx_messageInfo_SaveInvoiceFileResp.Size(m)
}
func (m *SaveInvoiceFileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveInvoiceFileResp.DiscardUnknown(m)
}

var xxx_messageInfo_SaveInvoiceFileResp proto.InternalMessageInfo

func (m *SaveInvoiceFileResp) GetReceiptSign() string {
	if m != nil {
		return m.ReceiptSign
	}
	return ""
}

func (m *SaveInvoiceFileResp) GetErrorList() []*SaveInvoiceFileResp_ErrorInfo {
	if m != nil {
		return m.ErrorList
	}
	return nil
}

func (m *SaveInvoiceFileResp) GetResult() bool {
	if m != nil {
		return m.Result
	}
	return false
}

type SaveInvoiceFileResp_ErrorInfo struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Result               string   `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	Reason               string   `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveInvoiceFileResp_ErrorInfo) Reset()         { *m = SaveInvoiceFileResp_ErrorInfo{} }
func (m *SaveInvoiceFileResp_ErrorInfo) String() string { return proto.CompactTextString(m) }
func (*SaveInvoiceFileResp_ErrorInfo) ProtoMessage()    {}
func (*SaveInvoiceFileResp_ErrorInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{29, 0}
}
func (m *SaveInvoiceFileResp_ErrorInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveInvoiceFileResp_ErrorInfo.Unmarshal(m, b)
}
func (m *SaveInvoiceFileResp_ErrorInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveInvoiceFileResp_ErrorInfo.Marshal(b, m, deterministic)
}
func (dst *SaveInvoiceFileResp_ErrorInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveInvoiceFileResp_ErrorInfo.Merge(dst, src)
}
func (m *SaveInvoiceFileResp_ErrorInfo) XXX_Size() int {
	return xxx_messageInfo_SaveInvoiceFileResp_ErrorInfo.Size(m)
}
func (m *SaveInvoiceFileResp_ErrorInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveInvoiceFileResp_ErrorInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SaveInvoiceFileResp_ErrorInfo proto.InternalMessageInfo

func (m *SaveInvoiceFileResp_ErrorInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SaveInvoiceFileResp_ErrorInfo) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

func (m *SaveInvoiceFileResp_ErrorInfo) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

// 获取工作室提现订单发票URL
type GetInvoiceUrlReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInvoiceUrlReq) Reset()         { *m = GetInvoiceUrlReq{} }
func (m *GetInvoiceUrlReq) String() string { return proto.CompactTextString(m) }
func (*GetInvoiceUrlReq) ProtoMessage()    {}
func (*GetInvoiceUrlReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{30}
}
func (m *GetInvoiceUrlReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInvoiceUrlReq.Unmarshal(m, b)
}
func (m *GetInvoiceUrlReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInvoiceUrlReq.Marshal(b, m, deterministic)
}
func (dst *GetInvoiceUrlReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInvoiceUrlReq.Merge(dst, src)
}
func (m *GetInvoiceUrlReq) XXX_Size() int {
	return xxx_messageInfo_GetInvoiceUrlReq.Size(m)
}
func (m *GetInvoiceUrlReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInvoiceUrlReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetInvoiceUrlReq proto.InternalMessageInfo

func (m *GetInvoiceUrlReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetInvoiceUrlResp struct {
	InvoiceUrl           string   `protobuf:"bytes,1,opt,name=invoice_url,json=invoiceUrl,proto3" json:"invoice_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInvoiceUrlResp) Reset()         { *m = GetInvoiceUrlResp{} }
func (m *GetInvoiceUrlResp) String() string { return proto.CompactTextString(m) }
func (*GetInvoiceUrlResp) ProtoMessage()    {}
func (*GetInvoiceUrlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{31}
}
func (m *GetInvoiceUrlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInvoiceUrlResp.Unmarshal(m, b)
}
func (m *GetInvoiceUrlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInvoiceUrlResp.Marshal(b, m, deterministic)
}
func (dst *GetInvoiceUrlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInvoiceUrlResp.Merge(dst, src)
}
func (m *GetInvoiceUrlResp) XXX_Size() int {
	return xxx_messageInfo_GetInvoiceUrlResp.Size(m)
}
func (m *GetInvoiceUrlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInvoiceUrlResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetInvoiceUrlResp proto.InternalMessageInfo

func (m *GetInvoiceUrlResp) GetInvoiceUrl() string {
	if m != nil {
		return m.InvoiceUrl
	}
	return ""
}

// 获取工作室结算单URL
type GetSettlementBillFileUrlReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSettlementBillFileUrlReq) Reset()         { *m = GetSettlementBillFileUrlReq{} }
func (m *GetSettlementBillFileUrlReq) String() string { return proto.CompactTextString(m) }
func (*GetSettlementBillFileUrlReq) ProtoMessage()    {}
func (*GetSettlementBillFileUrlReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{32}
}
func (m *GetSettlementBillFileUrlReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSettlementBillFileUrlReq.Unmarshal(m, b)
}
func (m *GetSettlementBillFileUrlReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSettlementBillFileUrlReq.Marshal(b, m, deterministic)
}
func (dst *GetSettlementBillFileUrlReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSettlementBillFileUrlReq.Merge(dst, src)
}
func (m *GetSettlementBillFileUrlReq) XXX_Size() int {
	return xxx_messageInfo_GetSettlementBillFileUrlReq.Size(m)
}
func (m *GetSettlementBillFileUrlReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSettlementBillFileUrlReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSettlementBillFileUrlReq proto.InternalMessageInfo

func (m *GetSettlementBillFileUrlReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetSettlementBillFileUrlResp struct {
	SettlementBillFileUrl string   `protobuf:"bytes,1,opt,name=settlement_bill_file_url,json=settlementBillFileUrl,proto3" json:"settlement_bill_file_url,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *GetSettlementBillFileUrlResp) Reset()         { *m = GetSettlementBillFileUrlResp{} }
func (m *GetSettlementBillFileUrlResp) String() string { return proto.CompactTextString(m) }
func (*GetSettlementBillFileUrlResp) ProtoMessage()    {}
func (*GetSettlementBillFileUrlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_studio_0ae1a508ab54c84a, []int{33}
}
func (m *GetSettlementBillFileUrlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSettlementBillFileUrlResp.Unmarshal(m, b)
}
func (m *GetSettlementBillFileUrlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSettlementBillFileUrlResp.Marshal(b, m, deterministic)
}
func (dst *GetSettlementBillFileUrlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSettlementBillFileUrlResp.Merge(dst, src)
}
func (m *GetSettlementBillFileUrlResp) XXX_Size() int {
	return xxx_messageInfo_GetSettlementBillFileUrlResp.Size(m)
}
func (m *GetSettlementBillFileUrlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSettlementBillFileUrlResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSettlementBillFileUrlResp proto.InternalMessageInfo

func (m *GetSettlementBillFileUrlResp) GetSettlementBillFileUrl() string {
	if m != nil {
		return m.SettlementBillFileUrl
	}
	return ""
}

func init() {
	proto.RegisterType((*GetUserStudioStatusReq)(nil), "settlement_studio.GetUserStudioStatusReq")
	proto.RegisterType((*GetUserStudioStatusResp)(nil), "settlement_studio.GetUserStudioStatusResp")
	proto.RegisterType((*CheckStudioJoinApplyValidReq)(nil), "settlement_studio.CheckStudioJoinApplyValidReq")
	proto.RegisterType((*CheckStudioJoinApplyValidResp)(nil), "settlement_studio.CheckStudioJoinApplyValidResp")
	proto.RegisterType((*SubmitStudioJoinApplyReq)(nil), "settlement_studio.SubmitStudioJoinApplyReq")
	proto.RegisterType((*SubmitStudioJoinApplyResp)(nil), "settlement_studio.SubmitStudioJoinApplyResp")
	proto.RegisterType((*StudioInfo)(nil), "settlement_studio.StudioInfo")
	proto.RegisterType((*GetStudioApplyInfoReq)(nil), "settlement_studio.GetStudioApplyInfoReq")
	proto.RegisterType((*GetStudioApplyInfoResp)(nil), "settlement_studio.GetStudioApplyInfoResp")
	proto.RegisterType((*GetStudioInfoReq)(nil), "settlement_studio.GetStudioInfoReq")
	proto.RegisterType((*GetStudioInfoResp)(nil), "settlement_studio.GetStudioInfoResp")
	proto.RegisterType((*GetElectronicContractReq)(nil), "settlement_studio.GetElectronicContractReq")
	proto.RegisterType((*GetElectronicContractResp)(nil), "settlement_studio.GetElectronicContractResp")
	proto.RegisterType((*StudioApplyHistoryInfo)(nil), "settlement_studio.StudioApplyHistoryInfo")
	proto.RegisterType((*GetStudioApplyHistoryListReq)(nil), "settlement_studio.GetStudioApplyHistoryListReq")
	proto.RegisterType((*GetStudioApplyHistoryListResp)(nil), "settlement_studio.GetStudioApplyHistoryListResp")
	proto.RegisterType((*GetStudioApplySnapshotReq)(nil), "settlement_studio.GetStudioApplySnapshotReq")
	proto.RegisterType((*GetStudioApplySnapshotResp)(nil), "settlement_studio.GetStudioApplySnapshotResp")
	proto.RegisterType((*CheckStudioTerminalValidReq)(nil), "settlement_studio.CheckStudioTerminalValidReq")
	proto.RegisterType((*CheckStudioTerminalValidResp)(nil), "settlement_studio.CheckStudioTerminalValidResp")
	proto.RegisterType((*SubmitStudioTerminalApplyReq)(nil), "settlement_studio.SubmitStudioTerminalApplyReq")
	proto.RegisterType((*SubmitStudioTerminalApplyResp)(nil), "settlement_studio.SubmitStudioTerminalApplyResp")
	proto.RegisterType((*WithdrawalOrderSimpleInfo)(nil), "settlement_studio.WithdrawalOrderSimpleInfo")
	proto.RegisterType((*WithdrawalOrderDetailInfo)(nil), "settlement_studio.WithdrawalOrderDetailInfo")
	proto.RegisterType((*GetWithdrawalOrderListReq)(nil), "settlement_studio.GetWithdrawalOrderListReq")
	proto.RegisterType((*GetWithdrawalOrderListResp)(nil), "settlement_studio.GetWithdrawalOrderListResp")
	proto.RegisterType((*GetWithdrawalOrderDetailReq)(nil), "settlement_studio.GetWithdrawalOrderDetailReq")
	proto.RegisterType((*GetWithdrawalOrderDetailResp)(nil), "settlement_studio.GetWithdrawalOrderDetailResp")
	proto.RegisterType((*SaveInvoiceFileReq)(nil), "settlement_studio.SaveInvoiceFileReq")
	proto.RegisterType((*SaveInvoiceFileResp)(nil), "settlement_studio.SaveInvoiceFileResp")
	proto.RegisterType((*SaveInvoiceFileResp_ErrorInfo)(nil), "settlement_studio.SaveInvoiceFileResp.ErrorInfo")
	proto.RegisterType((*GetInvoiceUrlReq)(nil), "settlement_studio.GetInvoiceUrlReq")
	proto.RegisterType((*GetInvoiceUrlResp)(nil), "settlement_studio.GetInvoiceUrlResp")
	proto.RegisterType((*GetSettlementBillFileUrlReq)(nil), "settlement_studio.GetSettlementBillFileUrlReq")
	proto.RegisterType((*GetSettlementBillFileUrlResp)(nil), "settlement_studio.GetSettlementBillFileUrlResp")
	proto.RegisterEnum("settlement_studio.JoinT", JoinT_name, JoinT_value)
	proto.RegisterEnum("settlement_studio.ApplyT", ApplyT_name, ApplyT_value)
	proto.RegisterEnum("settlement_studio.InvokeT", InvokeT_name, InvokeT_value)
	proto.RegisterEnum("settlement_studio.JoinApplyStatus", JoinApplyStatus_name, JoinApplyStatus_value)
	proto.RegisterEnum("settlement_studio.TerminalApplyStatus", TerminalApplyStatus_name, TerminalApplyStatus_value)
	proto.RegisterEnum("settlement_studio.ServiceProviderJoinStatus", ServiceProviderJoinStatus_name, ServiceProviderJoinStatus_value)
	proto.RegisterEnum("settlement_studio.ServiceProviderTerminalStatus", ServiceProviderTerminalStatus_name, ServiceProviderTerminalStatus_value)
	proto.RegisterEnum("settlement_studio.ApplyStatus", ApplyStatus_name, ApplyStatus_value)
	proto.RegisterEnum("settlement_studio.WithdrawalStatus", WithdrawalStatus_name, WithdrawalStatus_value)
	proto.RegisterEnum("settlement_studio.TaxpayerEntityT", TaxpayerEntityT_name, TaxpayerEntityT_value)
	proto.RegisterEnum("settlement_studio.StudioApplyHistoryInfo_ChangeT", StudioApplyHistoryInfo_ChangeT_name, StudioApplyHistoryInfo_ChangeT_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SettlementStudioClient is the client API for SettlementStudio service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SettlementStudioClient interface {
	// 入驻
	// 获取用户当前工作室状态
	GetUserStudioStatus(ctx context.Context, in *GetUserStudioStatusReq, opts ...grpc.CallOption) (*GetUserStudioStatusResp, error)
	// 检查是否符合工作室申请条件
	CheckStudioJoinApplyValid(ctx context.Context, in *CheckStudioJoinApplyValidReq, opts ...grpc.CallOption) (*CheckStudioJoinApplyValidResp, error)
	// 提交工作室入驻申请
	SubmitStudioJoinApply(ctx context.Context, in *SubmitStudioJoinApplyReq, opts ...grpc.CallOption) (*SubmitStudioJoinApplyResp, error)
	// 获取工作室申请信息（当前）
	GetStudioApplyInfo(ctx context.Context, in *GetStudioApplyInfoReq, opts ...grpc.CallOption) (*GetStudioApplyInfoResp, error)
	// 获取工作室账户信息和资料（当前）
	GetStudioInfo(ctx context.Context, in *GetStudioInfoReq, opts ...grpc.CallOption) (*GetStudioInfoResp, error)
	// 获取电子合同
	GetElectronicContract(ctx context.Context, in *GetElectronicContractReq, opts ...grpc.CallOption) (*GetElectronicContractResp, error)
	// 获取工作室变更历史列表
	GetStudioApplyHistoryList(ctx context.Context, in *GetStudioApplyHistoryListReq, opts ...grpc.CallOption) (*GetStudioApplyHistoryListResp, error)
	// 获取工作室变更历史快照信息
	GetStudioApplySnapshot(ctx context.Context, in *GetStudioApplySnapshotReq, opts ...grpc.CallOption) (*GetStudioApplySnapshotResp, error)
	// 解除
	// 检查是否符合工作室解除条件
	CheckStudioTerminalValid(ctx context.Context, in *CheckStudioTerminalValidReq, opts ...grpc.CallOption) (*CheckStudioTerminalValidResp, error)
	// 提交解除工作室申请
	SubmitStudioTerminalApply(ctx context.Context, in *SubmitStudioTerminalApplyReq, opts ...grpc.CallOption) (*SubmitStudioTerminalApplyResp, error)
	// 提现后续
	// 获取工作室提现记录列表
	GetWithdrawalOrderList(ctx context.Context, in *GetWithdrawalOrderListReq, opts ...grpc.CallOption) (*GetWithdrawalOrderListResp, error)
	// 获取工作室提现订单详情
	GetWithdrawalOrderDetail(ctx context.Context, in *GetWithdrawalOrderDetailReq, opts ...grpc.CallOption) (*GetWithdrawalOrderDetailResp, error)
	// 保存发票文件（http-logic层完成上传+校验后保存信息，并关联订单）
	SaveInvoiceFile(ctx context.Context, in *SaveInvoiceFileReq, opts ...grpc.CallOption) (*SaveInvoiceFileResp, error)
	// 后台
	//  // 运营后台-获取工作室申请列表
	//  rpc GetStudioApplyList(GetStudioApplyListReq) returns (GetStudioApplyListResp) {}
	//  // 运营后台-工作室申请审批
	//  rpc AuditStudioApply(AuditStudioApplyReq) returns (AuditStudioApplyResp) {}
	//  // 运营后台-移除提现订单发票（临时接口，处理发票移除）
	//  rpc RemoveWithdrawalOrderInvoice(RemoveWithdrawalOrderInvoiceReq) returns (RemoveWithdrawalOrderInvoiceResp) {}
	//  // 运营后台-标记付款完成（临时接口，通知用户付款完成）
	//  rpc MarkWithdrawalOrderPaid(MarkWithdrawalOrderPaidReq) returns (MarkWithdrawalOrderPaidResp) {}
	// 获取工作室提现订单发票URL
	GetInvoiceUrl(ctx context.Context, in *GetInvoiceUrlReq, opts ...grpc.CallOption) (*GetInvoiceUrlResp, error)
	// 获取工作室结算单URL
	GetSettlementBillFileUrl(ctx context.Context, in *GetSettlementBillFileUrlReq, opts ...grpc.CallOption) (*GetSettlementBillFileUrlResp, error)
}

type settlementStudioClient struct {
	cc *grpc.ClientConn
}

func NewSettlementStudioClient(cc *grpc.ClientConn) SettlementStudioClient {
	return &settlementStudioClient{cc}
}

func (c *settlementStudioClient) GetUserStudioStatus(ctx context.Context, in *GetUserStudioStatusReq, opts ...grpc.CallOption) (*GetUserStudioStatusResp, error) {
	out := new(GetUserStudioStatusResp)
	err := c.cc.Invoke(ctx, "/settlement_studio.SettlementStudio/GetUserStudioStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementStudioClient) CheckStudioJoinApplyValid(ctx context.Context, in *CheckStudioJoinApplyValidReq, opts ...grpc.CallOption) (*CheckStudioJoinApplyValidResp, error) {
	out := new(CheckStudioJoinApplyValidResp)
	err := c.cc.Invoke(ctx, "/settlement_studio.SettlementStudio/CheckStudioJoinApplyValid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementStudioClient) SubmitStudioJoinApply(ctx context.Context, in *SubmitStudioJoinApplyReq, opts ...grpc.CallOption) (*SubmitStudioJoinApplyResp, error) {
	out := new(SubmitStudioJoinApplyResp)
	err := c.cc.Invoke(ctx, "/settlement_studio.SettlementStudio/SubmitStudioJoinApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementStudioClient) GetStudioApplyInfo(ctx context.Context, in *GetStudioApplyInfoReq, opts ...grpc.CallOption) (*GetStudioApplyInfoResp, error) {
	out := new(GetStudioApplyInfoResp)
	err := c.cc.Invoke(ctx, "/settlement_studio.SettlementStudio/GetStudioApplyInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementStudioClient) GetStudioInfo(ctx context.Context, in *GetStudioInfoReq, opts ...grpc.CallOption) (*GetStudioInfoResp, error) {
	out := new(GetStudioInfoResp)
	err := c.cc.Invoke(ctx, "/settlement_studio.SettlementStudio/GetStudioInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementStudioClient) GetElectronicContract(ctx context.Context, in *GetElectronicContractReq, opts ...grpc.CallOption) (*GetElectronicContractResp, error) {
	out := new(GetElectronicContractResp)
	err := c.cc.Invoke(ctx, "/settlement_studio.SettlementStudio/GetElectronicContract", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementStudioClient) GetStudioApplyHistoryList(ctx context.Context, in *GetStudioApplyHistoryListReq, opts ...grpc.CallOption) (*GetStudioApplyHistoryListResp, error) {
	out := new(GetStudioApplyHistoryListResp)
	err := c.cc.Invoke(ctx, "/settlement_studio.SettlementStudio/GetStudioApplyHistoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementStudioClient) GetStudioApplySnapshot(ctx context.Context, in *GetStudioApplySnapshotReq, opts ...grpc.CallOption) (*GetStudioApplySnapshotResp, error) {
	out := new(GetStudioApplySnapshotResp)
	err := c.cc.Invoke(ctx, "/settlement_studio.SettlementStudio/GetStudioApplySnapshot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementStudioClient) CheckStudioTerminalValid(ctx context.Context, in *CheckStudioTerminalValidReq, opts ...grpc.CallOption) (*CheckStudioTerminalValidResp, error) {
	out := new(CheckStudioTerminalValidResp)
	err := c.cc.Invoke(ctx, "/settlement_studio.SettlementStudio/CheckStudioTerminalValid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementStudioClient) SubmitStudioTerminalApply(ctx context.Context, in *SubmitStudioTerminalApplyReq, opts ...grpc.CallOption) (*SubmitStudioTerminalApplyResp, error) {
	out := new(SubmitStudioTerminalApplyResp)
	err := c.cc.Invoke(ctx, "/settlement_studio.SettlementStudio/SubmitStudioTerminalApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementStudioClient) GetWithdrawalOrderList(ctx context.Context, in *GetWithdrawalOrderListReq, opts ...grpc.CallOption) (*GetWithdrawalOrderListResp, error) {
	out := new(GetWithdrawalOrderListResp)
	err := c.cc.Invoke(ctx, "/settlement_studio.SettlementStudio/GetWithdrawalOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementStudioClient) GetWithdrawalOrderDetail(ctx context.Context, in *GetWithdrawalOrderDetailReq, opts ...grpc.CallOption) (*GetWithdrawalOrderDetailResp, error) {
	out := new(GetWithdrawalOrderDetailResp)
	err := c.cc.Invoke(ctx, "/settlement_studio.SettlementStudio/GetWithdrawalOrderDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementStudioClient) SaveInvoiceFile(ctx context.Context, in *SaveInvoiceFileReq, opts ...grpc.CallOption) (*SaveInvoiceFileResp, error) {
	out := new(SaveInvoiceFileResp)
	err := c.cc.Invoke(ctx, "/settlement_studio.SettlementStudio/SaveInvoiceFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementStudioClient) GetInvoiceUrl(ctx context.Context, in *GetInvoiceUrlReq, opts ...grpc.CallOption) (*GetInvoiceUrlResp, error) {
	out := new(GetInvoiceUrlResp)
	err := c.cc.Invoke(ctx, "/settlement_studio.SettlementStudio/GetInvoiceUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementStudioClient) GetSettlementBillFileUrl(ctx context.Context, in *GetSettlementBillFileUrlReq, opts ...grpc.CallOption) (*GetSettlementBillFileUrlResp, error) {
	out := new(GetSettlementBillFileUrlResp)
	err := c.cc.Invoke(ctx, "/settlement_studio.SettlementStudio/GetSettlementBillFileUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SettlementStudioServer is the server API for SettlementStudio service.
type SettlementStudioServer interface {
	// 入驻
	// 获取用户当前工作室状态
	GetUserStudioStatus(context.Context, *GetUserStudioStatusReq) (*GetUserStudioStatusResp, error)
	// 检查是否符合工作室申请条件
	CheckStudioJoinApplyValid(context.Context, *CheckStudioJoinApplyValidReq) (*CheckStudioJoinApplyValidResp, error)
	// 提交工作室入驻申请
	SubmitStudioJoinApply(context.Context, *SubmitStudioJoinApplyReq) (*SubmitStudioJoinApplyResp, error)
	// 获取工作室申请信息（当前）
	GetStudioApplyInfo(context.Context, *GetStudioApplyInfoReq) (*GetStudioApplyInfoResp, error)
	// 获取工作室账户信息和资料（当前）
	GetStudioInfo(context.Context, *GetStudioInfoReq) (*GetStudioInfoResp, error)
	// 获取电子合同
	GetElectronicContract(context.Context, *GetElectronicContractReq) (*GetElectronicContractResp, error)
	// 获取工作室变更历史列表
	GetStudioApplyHistoryList(context.Context, *GetStudioApplyHistoryListReq) (*GetStudioApplyHistoryListResp, error)
	// 获取工作室变更历史快照信息
	GetStudioApplySnapshot(context.Context, *GetStudioApplySnapshotReq) (*GetStudioApplySnapshotResp, error)
	// 解除
	// 检查是否符合工作室解除条件
	CheckStudioTerminalValid(context.Context, *CheckStudioTerminalValidReq) (*CheckStudioTerminalValidResp, error)
	// 提交解除工作室申请
	SubmitStudioTerminalApply(context.Context, *SubmitStudioTerminalApplyReq) (*SubmitStudioTerminalApplyResp, error)
	// 提现后续
	// 获取工作室提现记录列表
	GetWithdrawalOrderList(context.Context, *GetWithdrawalOrderListReq) (*GetWithdrawalOrderListResp, error)
	// 获取工作室提现订单详情
	GetWithdrawalOrderDetail(context.Context, *GetWithdrawalOrderDetailReq) (*GetWithdrawalOrderDetailResp, error)
	// 保存发票文件（http-logic层完成上传+校验后保存信息，并关联订单）
	SaveInvoiceFile(context.Context, *SaveInvoiceFileReq) (*SaveInvoiceFileResp, error)
	// 后台
	//  // 运营后台-获取工作室申请列表
	//  rpc GetStudioApplyList(GetStudioApplyListReq) returns (GetStudioApplyListResp) {}
	//  // 运营后台-工作室申请审批
	//  rpc AuditStudioApply(AuditStudioApplyReq) returns (AuditStudioApplyResp) {}
	//  // 运营后台-移除提现订单发票（临时接口，处理发票移除）
	//  rpc RemoveWithdrawalOrderInvoice(RemoveWithdrawalOrderInvoiceReq) returns (RemoveWithdrawalOrderInvoiceResp) {}
	//  // 运营后台-标记付款完成（临时接口，通知用户付款完成）
	//  rpc MarkWithdrawalOrderPaid(MarkWithdrawalOrderPaidReq) returns (MarkWithdrawalOrderPaidResp) {}
	// 获取工作室提现订单发票URL
	GetInvoiceUrl(context.Context, *GetInvoiceUrlReq) (*GetInvoiceUrlResp, error)
	// 获取工作室结算单URL
	GetSettlementBillFileUrl(context.Context, *GetSettlementBillFileUrlReq) (*GetSettlementBillFileUrlResp, error)
}

func RegisterSettlementStudioServer(s *grpc.Server, srv SettlementStudioServer) {
	s.RegisterService(&_SettlementStudio_serviceDesc, srv)
}

func _SettlementStudio_GetUserStudioStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserStudioStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementStudioServer).GetUserStudioStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_studio.SettlementStudio/GetUserStudioStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementStudioServer).GetUserStudioStatus(ctx, req.(*GetUserStudioStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementStudio_CheckStudioJoinApplyValid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckStudioJoinApplyValidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementStudioServer).CheckStudioJoinApplyValid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_studio.SettlementStudio/CheckStudioJoinApplyValid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementStudioServer).CheckStudioJoinApplyValid(ctx, req.(*CheckStudioJoinApplyValidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementStudio_SubmitStudioJoinApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitStudioJoinApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementStudioServer).SubmitStudioJoinApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_studio.SettlementStudio/SubmitStudioJoinApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementStudioServer).SubmitStudioJoinApply(ctx, req.(*SubmitStudioJoinApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementStudio_GetStudioApplyInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStudioApplyInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementStudioServer).GetStudioApplyInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_studio.SettlementStudio/GetStudioApplyInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementStudioServer).GetStudioApplyInfo(ctx, req.(*GetStudioApplyInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementStudio_GetStudioInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStudioInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementStudioServer).GetStudioInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_studio.SettlementStudio/GetStudioInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementStudioServer).GetStudioInfo(ctx, req.(*GetStudioInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementStudio_GetElectronicContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetElectronicContractReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementStudioServer).GetElectronicContract(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_studio.SettlementStudio/GetElectronicContract",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementStudioServer).GetElectronicContract(ctx, req.(*GetElectronicContractReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementStudio_GetStudioApplyHistoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStudioApplyHistoryListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementStudioServer).GetStudioApplyHistoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_studio.SettlementStudio/GetStudioApplyHistoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementStudioServer).GetStudioApplyHistoryList(ctx, req.(*GetStudioApplyHistoryListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementStudio_GetStudioApplySnapshot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStudioApplySnapshotReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementStudioServer).GetStudioApplySnapshot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_studio.SettlementStudio/GetStudioApplySnapshot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementStudioServer).GetStudioApplySnapshot(ctx, req.(*GetStudioApplySnapshotReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementStudio_CheckStudioTerminalValid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckStudioTerminalValidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementStudioServer).CheckStudioTerminalValid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_studio.SettlementStudio/CheckStudioTerminalValid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementStudioServer).CheckStudioTerminalValid(ctx, req.(*CheckStudioTerminalValidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementStudio_SubmitStudioTerminalApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitStudioTerminalApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementStudioServer).SubmitStudioTerminalApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_studio.SettlementStudio/SubmitStudioTerminalApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementStudioServer).SubmitStudioTerminalApply(ctx, req.(*SubmitStudioTerminalApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementStudio_GetWithdrawalOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWithdrawalOrderListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementStudioServer).GetWithdrawalOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_studio.SettlementStudio/GetWithdrawalOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementStudioServer).GetWithdrawalOrderList(ctx, req.(*GetWithdrawalOrderListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementStudio_GetWithdrawalOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWithdrawalOrderDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementStudioServer).GetWithdrawalOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_studio.SettlementStudio/GetWithdrawalOrderDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementStudioServer).GetWithdrawalOrderDetail(ctx, req.(*GetWithdrawalOrderDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementStudio_SaveInvoiceFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveInvoiceFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementStudioServer).SaveInvoiceFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_studio.SettlementStudio/SaveInvoiceFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementStudioServer).SaveInvoiceFile(ctx, req.(*SaveInvoiceFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementStudio_GetInvoiceUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvoiceUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementStudioServer).GetInvoiceUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_studio.SettlementStudio/GetInvoiceUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementStudioServer).GetInvoiceUrl(ctx, req.(*GetInvoiceUrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementStudio_GetSettlementBillFileUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSettlementBillFileUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementStudioServer).GetSettlementBillFileUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_studio.SettlementStudio/GetSettlementBillFileUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementStudioServer).GetSettlementBillFileUrl(ctx, req.(*GetSettlementBillFileUrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SettlementStudio_serviceDesc = grpc.ServiceDesc{
	ServiceName: "settlement_studio.SettlementStudio",
	HandlerType: (*SettlementStudioServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserStudioStatus",
			Handler:    _SettlementStudio_GetUserStudioStatus_Handler,
		},
		{
			MethodName: "CheckStudioJoinApplyValid",
			Handler:    _SettlementStudio_CheckStudioJoinApplyValid_Handler,
		},
		{
			MethodName: "SubmitStudioJoinApply",
			Handler:    _SettlementStudio_SubmitStudioJoinApply_Handler,
		},
		{
			MethodName: "GetStudioApplyInfo",
			Handler:    _SettlementStudio_GetStudioApplyInfo_Handler,
		},
		{
			MethodName: "GetStudioInfo",
			Handler:    _SettlementStudio_GetStudioInfo_Handler,
		},
		{
			MethodName: "GetElectronicContract",
			Handler:    _SettlementStudio_GetElectronicContract_Handler,
		},
		{
			MethodName: "GetStudioApplyHistoryList",
			Handler:    _SettlementStudio_GetStudioApplyHistoryList_Handler,
		},
		{
			MethodName: "GetStudioApplySnapshot",
			Handler:    _SettlementStudio_GetStudioApplySnapshot_Handler,
		},
		{
			MethodName: "CheckStudioTerminalValid",
			Handler:    _SettlementStudio_CheckStudioTerminalValid_Handler,
		},
		{
			MethodName: "SubmitStudioTerminalApply",
			Handler:    _SettlementStudio_SubmitStudioTerminalApply_Handler,
		},
		{
			MethodName: "GetWithdrawalOrderList",
			Handler:    _SettlementStudio_GetWithdrawalOrderList_Handler,
		},
		{
			MethodName: "GetWithdrawalOrderDetail",
			Handler:    _SettlementStudio_GetWithdrawalOrderDetail_Handler,
		},
		{
			MethodName: "SaveInvoiceFile",
			Handler:    _SettlementStudio_SaveInvoiceFile_Handler,
		},
		{
			MethodName: "GetInvoiceUrl",
			Handler:    _SettlementStudio_GetInvoiceUrl_Handler,
		},
		{
			MethodName: "GetSettlementBillFileUrl",
			Handler:    _SettlementStudio_GetSettlementBillFileUrl_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/settlement-studio/settlement-studio.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/settlement-studio/settlement-studio.proto", fileDescriptor_settlement_studio_0ae1a508ab54c84a)
}

var fileDescriptor_settlement_studio_0ae1a508ab54c84a = []byte{
	// 2828 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x5a, 0x4b, 0x73, 0xe3, 0xc6,
	0x11, 0x5e, 0x52, 0x12, 0x25, 0x36, 0xf5, 0xa0, 0x46, 0x8f, 0x25, 0xa9, 0xe5, 0x4a, 0x4b, 0x79,
	0x63, 0x59, 0x96, 0xa5, 0x8d, 0x9c, 0x4d, 0xec, 0x3c, 0x5c, 0xc5, 0xa5, 0xb0, 0x5a, 0xd8, 0x12,
	0x45, 0x83, 0xd0, 0x6e, 0x9c, 0x72, 0x05, 0x81, 0xc8, 0x91, 0x84, 0x08, 0x04, 0xb8, 0x00, 0xf4,
	0x4a, 0x55, 0x52, 0xb9, 0x24, 0xc7, 0xa4, 0x2a, 0xf7, 0xfc, 0x80, 0x1c, 0x72, 0xc8, 0x5f, 0x49,
	0xe5, 0x98, 0x5b, 0x7e, 0x42, 0x72, 0xc9, 0x2d, 0x35, 0x3d, 0x03, 0x10, 0x20, 0x00, 0xae, 0x14,
	0xfb, 0x26, 0xf4, 0x7c, 0xdd, 0xd3, 0xf3, 0x4d, 0x4f, 0x4f, 0xf7, 0x50, 0xf0, 0x89, 0xe7, 0xed,
	0xbc, 0xbd, 0x34, 0x3a, 0x17, 0xae, 0x61, 0x5e, 0x51, 0x67, 0xc7, 0xa5, 0x9e, 0x67, 0xd2, 0x1e,
	0xb5, 0xbc, 0x8f, 0x5c, 0xef, 0xb2, 0x6b, 0xd8, 0x71, 0xc9, 0x76, 0xdf, 0xb1, 0x3d, 0x9b, 0xcc,
	0x0f, 0x06, 0x34, 0x3e, 0x50, 0xdb, 0x84, 0xe5, 0x7d, 0xea, 0x1d, 0xbb, 0xd4, 0x69, 0xa3, 0xa0,
	0xed, 0xe9, 0xde, 0xa5, 0xab, 0xd0, 0xb7, 0xa4, 0x08, 0x63, 0x97, 0x46, 0xb7, 0x94, 0x59, 0xcb,
	0x6c, 0xcc, 0x28, 0xec, 0xcf, 0xda, 0x3f, 0x33, 0xf0, 0x30, 0x11, 0xec, 0xf6, 0xc9, 0x06, 0x14,
	0x0d, 0x57, 0x18, 0xd5, 0xf8, 0x34, 0xa8, 0x3a, 0xa5, 0xcc, 0x1a, 0xae, 0x40, 0xa3, 0x94, 0xac,
	0x42, 0xc1, 0x70, 0x35, 0xbd, 0xdf, 0x37, 0x6f, 0x0d, 0xeb, 0xac, 0x94, 0x45, 0x10, 0x18, 0x6e,
	0x5d, 0x48, 0xc8, 0x27, 0x00, 0x38, 0xaa, 0x79, 0xb7, 0x7d, 0x5a, 0x1a, 0x5b, 0xcb, 0x6c, 0xcc,
	0xee, 0x96, 0xb7, 0x63, 0xae, 0x6f, 0xa3, 0x82, 0xaa, 0xe4, 0x11, 0xac, 0xde, 0xf6, 0x29, 0x79,
	0x02, 0xd3, 0x5c, 0xd3, 0x45, 0xc7, 0x4a, 0xe3, 0xe8, 0x7b, 0x01, 0x65, 0xdc, 0x57, 0x52, 0x86,
	0x29, 0x0e, 0x31, 0xba, 0xa5, 0x89, 0xb5, 0xcc, 0x46, 0x5e, 0x99, 0xc4, 0x6f, 0xb9, 0x5b, 0x7b,
	0x06, 0x8f, 0x1a, 0xe7, 0xb4, 0x73, 0xc1, 0xbd, 0xfd, 0xdc, 0x36, 0x2c, 0x9c, 0xe1, 0xb5, 0x6e,
	0x1a, 0xdd, 0x21, 0x42, 0xf2, 0x9c, 0x10, 0x05, 0xaa, 0x23, 0x34, 0xdc, 0x3e, 0x9b, 0xcd, 0x70,
	0xb5, 0x2b, 0xf6, 0x2d, 0xd8, 0x98, 0x34, 0x5c, 0x1c, 0x26, 0xcb, 0x90, 0x73, 0xa8, 0xee, 0xda,
	0x16, 0x32, 0x90, 0x57, 0xc4, 0x57, 0xed, 0x1a, 0x4a, 0xed, 0xcb, 0x93, 0x9e, 0xe1, 0x0d, 0x19,
	0x4d, 0xdc, 0x12, 0x22, 0xc3, 0xbc, 0xe0, 0x5c, 0xac, 0xca, 0x3a, 0xb5, 0xd1, 0x60, 0x61, 0xb7,
	0x9a, 0x40, 0x19, 0xb7, 0x29, 0x5b, 0xa7, 0xb6, 0x32, 0xc7, 0x45, 0x68, 0x9b, 0x09, 0x6a, 0xdf,
	0x87, 0x72, 0xca, 0xc4, 0x7c, 0x21, 0x01, 0x6d, 0x99, 0x28, 0x6d, 0x7f, 0x2a, 0x00, 0x0c, 0xec,
	0x26, 0xf8, 0xf8, 0x04, 0xa6, 0x3b, 0x76, 0xaf, 0xaf, 0x5b, 0xb7, 0x9a, 0xa5, 0xf7, 0xa8, 0x58,
	0x6f, 0x41, 0xc8, 0x9a, 0x7a, 0x8f, 0x86, 0x21, 0x1d, 0xbb, 0xcb, 0x37, 0x7d, 0x00, 0x69, 0xd8,
	0x5d, 0x84, 0x98, 0xf4, 0x4c, 0x37, 0xb5, 0x3e, 0x75, 0x18, 0x6b, 0xe3, 0x1c, 0x82, 0xb2, 0x16,
	0x8a, 0xc8, 0x0e, 0x2c, 0x86, 0x21, 0x9a, 0xd1, 0xd5, 0x3a, 0xba, 0xe3, 0xef, 0xf3, 0x7c, 0x08,
	0x2a, 0x77, 0x1b, 0xba, 0xd3, 0x25, 0x5b, 0x40, 0x22, 0x0a, 0xfd, 0x73, 0xdb, 0xa2, 0xa5, 0x1c,
	0xc2, 0x8b, 0x21, 0x78, 0x8b, 0xc9, 0xc9, 0x33, 0x58, 0xf4, 0x9d, 0xd4, 0x3b, 0x1d, 0xfb, 0xd2,
	0xf2, 0xf8, 0x7a, 0x26, 0x11, 0x4f, 0xc4, 0x58, 0x9d, 0x0f, 0xf9, 0xcb, 0x3a, 0xd1, 0xad, 0x0b,
	0x1f, 0x5e, 0x9a, 0xe2, 0x3e, 0x33, 0x99, 0x80, 0xb1, 0xd3, 0x80, 0x90, 0x13, 0x47, 0xb7, 0x3a,
	0xe7, 0xa5, 0x3c, 0x22, 0x80, 0x89, 0x5e, 0xa0, 0x84, 0xfc, 0x18, 0x56, 0x38, 0x40, 0x77, 0x8d,
	0x4e, 0x30, 0xf1, 0xa9, 0x61, 0x52, 0xed, 0x82, 0xde, 0x96, 0x00, 0x15, 0x1e, 0xa2, 0x02, 0x43,
	0x08, 0xbb, 0x2f, 0x0d, 0x93, 0x7e, 0x41, 0x6f, 0x89, 0x0a, 0x8b, 0x9e, 0x7e, 0xd3, 0xd7, 0x6f,
	0xa9, 0xa3, 0x51, 0xcb, 0x33, 0x3c, 0x71, 0xaa, 0x0a, 0x78, 0xaa, 0x6a, 0x09, 0x21, 0xa2, 0x0a,
	0xb8, 0x84, 0x68, 0x55, 0x21, 0x5e, 0x54, 0xc0, 0xce, 0xd9, 0xa7, 0x50, 0x3e, 0xb9, 0x74, 0x0d,
	0x8b, 0xba, 0xae, 0x66, 0x1a, 0x1d, 0x6a, 0xb9, 0x74, 0xe0, 0xd1, 0x34, 0x7a, 0xb4, 0xec, 0x03,
	0x0e, 0xf8, 0xb8, 0xef, 0xd0, 0xa7, 0x50, 0x3e, 0xa3, 0x16, 0x75, 0x74, 0x53, 0x0b, 0x1c, 0x0b,
	0x54, 0x67, 0xb8, 0xaa, 0x00, 0xf8, 0x9e, 0xf8, 0xaa, 0xbb, 0xb0, 0x14, 0x53, 0x75, 0x74, 0x8f,
	0x96, 0x66, 0x31, 0xd6, 0x16, 0x86, 0xd4, 0x14, 0xdd, 0xa3, 0x64, 0x1b, 0x16, 0xdc, 0x9e, 0x6e,
	0x0e, 0x6b, 0xcc, 0xa1, 0xc6, 0x3c, 0x0e, 0x45, 0xf0, 0x3f, 0x81, 0x69, 0xc3, 0xba, 0xb2, 0x8d,
	0x0e, 0xe5, 0x3c, 0x15, 0x91, 0xa7, 0x4a, 0x02, 0x4f, 0xb2, 0x75, 0x65, 0x5f, 0x50, 0x55, 0x29,
	0x08, 0xbc, 0x9f, 0x80, 0x3a, 0xb6, 0xe5, 0xe9, 0x1d, 0x11, 0x1a, 0xf3, 0x7e, 0x1c, 0xa3, 0x0c,
	0x63, 0x62, 0x1d, 0x66, 0x7c, 0x08, 0x0f, 0x37, 0x82, 0x18, 0x5f, 0x8f, 0x87, 0x5a, 0xc8, 0x8e,
	0xde, 0xed, 0x3a, 0xa5, 0x85, 0xb5, 0xb1, 0x90, 0x9d, 0x7a, 0xb7, 0xeb, 0xb0, 0x95, 0x85, 0x21,
	0x5a, 0x97, 0x7a, 0xba, 0x61, 0x96, 0x16, 0x79, 0xac, 0x87, 0x90, 0x7b, 0x38, 0x10, 0x9e, 0x97,
	0xf6, 0x18, 0x72, 0x29, 0x32, 0xaf, 0xc4, 0x64, 0xe4, 0x39, 0xe4, 0x7f, 0x69, 0x1b, 0x16, 0x5f,
	0xfb, 0x32, 0xae, 0xbd, 0x94, 0xb0, 0x76, 0x96, 0x1b, 0x54, 0x65, 0x8a, 0x41, 0x71, 0xd9, 0x1b,
	0x50, 0x64, 0x66, 0x1c, 0x66, 0xfc, 0xd4, 0xb4, 0xaf, 0x59, 0x96, 0x78, 0x88, 0xe6, 0x67, 0x7d,
	0xf9, 0x4b, 0xd3, 0xbe, 0x96, 0xbb, 0xfe, 0xc2, 0x10, 0x79, 0xe9, 0x98, 0xa5, 0xd2, 0x80, 0x20,
	0x26, 0x3b, 0x76, 0xcc, 0x51, 0x01, 0xcf, 0x34, 0xca, 0xe9, 0x01, 0xcf, 0xb4, 0x53, 0x43, 0x93,
	0xe9, 0x56, 0x52, 0x43, 0x53, 0xa8, 0x26, 0x87, 0x26, 0x53, 0x5d, 0x49, 0x0d, 0x4d, 0xa6, 0x5a,
	0x0d, 0xae, 0x2c, 0xa3, 0x47, 0x4b, 0x8f, 0xd6, 0x32, 0x1b, 0xe3, 0xfe, 0xbd, 0x64, 0xf4, 0x28,
	0x59, 0xf1, 0x69, 0x65, 0xa3, 0x55, 0x1c, 0xe5, 0xe4, 0x19, 0x3c, 0x20, 0x3c, 0xea, 0xf4, 0x0c,
	0x8b, 0xcd, 0xcb, 0x00, 0x8f, 0x11, 0x30, 0xed, 0x0b, 0x11, 0xc4, 0xe2, 0x98, 0x3a, 0x57, 0x2c,
	0x2e, 0xfb, 0x8e, 0x7d, 0x65, 0x74, 0xa9, 0xc3, 0x48, 0x5e, 0x45, 0xe8, 0xbc, 0x18, 0x6a, 0x89,
	0x11, 0xb9, 0x5b, 0xdb, 0x85, 0xa5, 0x7d, 0x2a, 0x32, 0x79, 0x90, 0xe2, 0xd9, 0x15, 0x32, 0x22,
	0x91, 0xff, 0x31, 0x8b, 0xb5, 0x40, 0x4c, 0xc9, 0xed, 0x93, 0xcf, 0xa0, 0x20, 0xae, 0x19, 0xbc,
	0x60, 0x32, 0x77, 0xb9, 0x60, 0xc0, 0x1d, 0x5c, 0x0a, 0xd1, 0x2b, 0x3d, 0xfb, 0x0d, 0xae, 0xf4,
	0xb1, 0xf8, 0x95, 0x3e, 0xb8, 0x49, 0xc7, 0xc3, 0x37, 0x29, 0x4b, 0xad, 0x1d, 0x87, 0xea, 0x1e,
	0xe5, 0xb4, 0x4e, 0x20, 0x57, 0xc0, 0x45, 0x48, 0xea, 0x2a, 0x14, 0x2e, 0xfb, 0xdd, 0x00, 0x90,
	0xe3, 0x00, 0x2e, 0x62, 0x80, 0xda, 0x7b, 0x50, 0x0c, 0x08, 0xf1, 0x09, 0x8c, 0x97, 0x45, 0x6d,
	0x98, 0x1f, 0x42, 0x7d, 0x73, 0xc6, 0x6a, 0xcf, 0xa1, 0xb4, 0x4f, 0x3d, 0xc9, 0xa4, 0x1d, 0xcf,
	0xb1, 0x2d, 0xa3, 0xd3, 0x10, 0x27, 0xe4, 0x1d, 0x7b, 0xf8, 0x19, 0x94, 0x53, 0xd4, 0xdc, 0x7e,
	0xec, 0xf0, 0x65, 0x62, 0x87, 0xaf, 0xf6, 0xaf, 0x2c, 0x2c, 0x87, 0x02, 0xe0, 0x95, 0xe1, 0x7a,
	0xb6, 0x83, 0x71, 0x30, 0x62, 0xd6, 0xe1, 0xc5, 0x66, 0xef, 0x1b, 0x1e, 0x0a, 0x14, 0x3a, 0xe7,
	0xba, 0x75, 0x46, 0xc3, 0x25, 0xdf, 0x77, 0x53, 0xf5, 0x87, 0x5d, 0xdb, 0x6e, 0xa0, 0xb2, 0xaa,
	0x00, 0xb7, 0x82, 0x81, 0xb3, 0x09, 0xf3, 0x91, 0xbb, 0x1d, 0xf3, 0x31, 0x0f, 0x90, 0xb9, 0xd0,
	0xd5, 0x8e, 0x39, 0x79, 0x75, 0x30, 0x7f, 0x28, 0x10, 0x84, 0x31, 0x16, 0x08, 0x87, 0x30, 0x29,
	0xe6, 0x20, 0x25, 0x58, 0x6c, 0xbc, 0xaa, 0x37, 0xf7, 0x25, 0x4d, 0xd5, 0x8e, 0x9b, 0xed, 0x96,
	0xd4, 0x90, 0x5f, 0xca, 0xd2, 0x5e, 0xf1, 0x01, 0x99, 0x87, 0x99, 0x60, 0xe4, 0xf3, 0x23, 0xb9,
	0x59, 0xcc, 0x90, 0x25, 0x98, 0x0f, 0x44, 0xaa, 0xa4, 0x1c, 0xca, 0xcd, 0xfa, 0x41, 0x31, 0x5b,
	0xfb, 0x39, 0x3c, 0x8a, 0x1e, 0x34, 0xb1, 0x98, 0x03, 0xc3, 0xf5, 0x92, 0xeb, 0xbc, 0x65, 0xc8,
	0xd9, 0xa7, 0xa7, 0x2e, 0xf5, 0x90, 0xdc, 0x19, 0x45, 0x7c, 0x91, 0x45, 0x98, 0x30, 0x8d, 0x9e,
	0xe1, 0x89, 0x73, 0xc1, 0x3f, 0x6a, 0x7f, 0xc8, 0x40, 0x75, 0xc4, 0x04, 0x6e, 0x9f, 0xbc, 0x01,
	0xc2, 0x37, 0xf3, 0x9c, 0x0f, 0x68, 0xa6, 0xe1, 0x7a, 0xa5, 0xcc, 0xda, 0xd8, 0x46, 0x61, 0xf7,
	0x83, 0x3b, 0x13, 0xaf, 0x14, 0xf5, 0x21, 0xe3, 0xcc, 0x21, 0xcf, 0xf6, 0x74, 0x53, 0xf8, 0xc9,
	0x3f, 0x58, 0x6d, 0x19, 0xf5, 0xa7, 0x6d, 0xe9, 0x7d, 0xf7, 0xdc, 0x7e, 0x57, 0x38, 0x7f, 0x0d,
	0x95, 0x34, 0xbd, 0x6f, 0xe1, 0x8c, 0xed, 0xc0, 0x4a, 0xa8, 0x7c, 0x57, 0x45, 0xbe, 0x4d, 0xaa,
	0xf7, 0xc5, 0x49, 0xff, 0x5d, 0x26, 0xd2, 0x22, 0x0c, 0x69, 0xfc, 0x5f, 0xf5, 0x3e, 0xab, 0x6a,
	0x1c, 0x76, 0x21, 0x5b, 0x5a, 0xe4, 0x16, 0xf0, 0x33, 0xdd, 0x02, 0x1f, 0x54, 0x43, 0x97, 0x81,
	0xcb, 0x3a, 0x95, 0x70, 0xa9, 0xee, 0x0f, 0xa6, 0xf7, 0x09, 0xb5, 0x1f, 0x42, 0x75, 0x84, 0xc6,
	0xe8, 0x02, 0xff, 0x6f, 0x59, 0x28, 0xbf, 0x31, 0xbc, 0xf3, 0xae, 0xa3, 0x5f, 0xeb, 0xe6, 0x91,
	0xd3, 0xa5, 0x4e, 0xdb, 0xe8, 0xf5, 0x4d, 0x9a, 0x52, 0xef, 0x97, 0x61, 0xca, 0x76, 0xc4, 0x05,
	0xc5, 0xd7, 0x3a, 0x89, 0xdf, 0x72, 0x97, 0xfc, 0x08, 0x72, 0xa1, 0x3c, 0x3e, 0xbb, 0xbb, 0x9e,
	0xb0, 0x59, 0x83, 0xa9, 0x44, 0x7b, 0x29, 0x54, 0x62, 0x7d, 0xc4, 0x78, 0xbc, 0x8f, 0x78, 0x1f,
	0xe6, 0xae, 0x03, 0x75, 0x9e, 0x4c, 0x26, 0xd0, 0xb1, 0xd9, 0x81, 0x18, 0xb3, 0x43, 0x15, 0xc0,
	0xed, 0xd8, 0x8e, 0x48, 0x38, 0x39, 0xc4, 0xe4, 0x51, 0x82, 0xc3, 0xcb, 0x90, 0xd3, 0x7b, 0x58,
	0xb2, 0x4f, 0x62, 0x2e, 0x10, 0x5f, 0xc3, 0xf6, 0x59, 0xb2, 0x98, 0x42, 0x40, 0xd8, 0x3e, 0x4b,
	0x18, 0xaf, 0x62, 0x94, 0xf1, 0x32, 0xec, 0xde, 0x94, 0xd5, 0xfe, 0x9e, 0xc1, 0xb3, 0x33, 0x64,
	0xed, 0x5b, 0xca, 0x14, 0xc8, 0x83, 0xa7, 0x3b, 0x1e, 0x5f, 0xcb, 0x38, 0x2f, 0x5c, 0x50, 0x82,
	0x37, 0x64, 0x19, 0xa6, 0xa8, 0xd5, 0x0d, 0xdf, 0x9f, 0x93, 0xd4, 0xea, 0xe2, 0xd0, 0x60, 0x2b,
	0x73, 0xf7, 0xde, 0xca, 0xda, 0x6f, 0xf0, 0x5c, 0x27, 0xae, 0xc9, 0xed, 0x93, 0x5f, 0xc0, 0x52,
	0x88, 0x65, 0x4e, 0x4c, 0x28, 0x3f, 0x6d, 0x8d, 0x9c, 0x69, 0x28, 0x3e, 0x95, 0x85, 0xeb, 0xf8,
	0x2c, 0xb5, 0x4f, 0x60, 0x25, 0x3e, 0x3f, 0xdf, 0x21, 0x91, 0x91, 0xd2, 0xb6, 0xe3, 0x02, 0x53,
	0x77, 0x8a, 0xa6, 0xdb, 0x27, 0x5f, 0x00, 0x08, 0xd5, 0x41, 0x4a, 0xba, 0x83, 0xc3, 0x83, 0xe8,
	0x50, 0xf2, 0x7c, 0x2a, 0x96, 0xa0, 0xfe, 0x3b, 0x0e, 0xa4, 0xad, 0x5f, 0x51, 0x99, 0xb7, 0x18,
	0xac, 0xda, 0x64, 0xee, 0x55, 0x01, 0x1c, 0xda, 0xa1, 0x46, 0xdf, 0x1b, 0x9c, 0xd6, 0xbc, 0x90,
	0xc8, 0x5d, 0x56, 0x6d, 0x62, 0xd9, 0x1a, 0x6a, 0xb6, 0xa7, 0x98, 0x00, 0x4f, 0x08, 0x81, 0x71,
	0xd7, 0xf8, 0x15, 0xbf, 0x63, 0xc7, 0x15, 0xfc, 0x9b, 0x29, 0xb8, 0xe7, 0xf6, 0xb5, 0x86, 0x03,
	0xfc, 0x54, 0x4d, 0x31, 0x41, 0x9b, 0x0d, 0x56, 0x01, 0xd0, 0x5a, 0xc7, 0xd4, 0x5d, 0x57, 0xb4,
	0xd2, 0x68, 0xbf, 0xc1, 0x04, 0x7e, 0x00, 0xe6, 0x22, 0xed, 0xbe, 0xef, 0x1d, 0x2b, 0xa4, 0x44,
	0x7b, 0x5c, 0x10, 0xb2, 0x3d, 0xd6, 0x65, 0x85, 0x20, 0xd8, 0xee, 0x4f, 0x45, 0x20, 0xd8, 0xee,
	0x87, 0xd6, 0x68, 0xd9, 0xa2, 0x2d, 0xf6, 0xd7, 0xd8, 0xc4, 0x62, 0xc4, 0xd3, 0x6f, 0x78, 0x33,
	0xc7, 0x5b, 0xe0, 0x49, 0x4f, 0xbf, 0xc1, 0x16, 0xae, 0x0a, 0xc0, 0x86, 0xc4, 0xf9, 0x2d, 0xf0,
	0x90, 0xf6, 0xf4, 0x9b, 0x3a, 0x3f, 0xc2, 0x83, 0xa3, 0x3d, 0x1d, 0x39, 0xda, 0x35, 0x98, 0xa1,
	0x37, 0x5a, 0x48, 0x73, 0x06, 0x87, 0x0b, 0xf4, 0x46, 0x0d, 0x74, 0x97, 0x20, 0x77, 0xc5, 0x3d,
	0x9e, 0xc5, 0x39, 0x27, 0xae, 0xd0, 0xd7, 0x12, 0x4c, 0xb2, 0x1a, 0x8a, 0x5a, 0x1e, 0x36, 0x96,
	0x79, 0xc5, 0xff, 0x64, 0x85, 0x85, 0x4b, 0x4d, 0x93, 0x3a, 0x7c, 0x33, 0x8a, 0xbc, 0xbb, 0xe7,
	0x22, 0xdc, 0x8e, 0x1a, 0xcc, 0x08, 0x00, 0x9b, 0xd9, 0xb2, 0xfd, 0x8e, 0x91, 0x0b, 0x55, 0xfd,
	0xa6, 0x69, 0x93, 0xa7, 0x30, 0xdb, 0xbf, 0x74, 0x3a, 0xe7, 0xba, 0xeb, 0xdb, 0xe1, 0x2d, 0xe3,
	0x4c, 0x20, 0x45, 0x53, 0x1b, 0x50, 0x1c, 0xc0, 0x84, 0xb5, 0x05, 0xde, 0x84, 0x05, 0x72, 0x6e,
	0x70, 0x1d, 0x66, 0xae, 0xa8, 0x63, 0x9c, 0xde, 0x6a, 0x0e, 0x75, 0x2f, 0x4d, 0x4f, 0x34, 0x8d,
	0xd3, 0x5c, 0xa8, 0xa0, 0xac, 0xf6, 0x9f, 0x0c, 0x2c, 0xc4, 0x62, 0x8f, 0x17, 0x91, 0xfe, 0xc6,
	0xb8, 0xc6, 0x99, 0xe5, 0x17, 0x91, 0x42, 0xd6, 0x36, 0xce, 0x2c, 0x72, 0x04, 0x40, 0x1d, 0xc7,
	0x16, 0x87, 0x36, 0x8b, 0x87, 0xf6, 0x59, 0xd2, 0xb5, 0x1c, 0x37, 0xbf, 0x2d, 0x31, 0x45, 0x7e,
	0x0e, 0xd0, 0x06, 0x16, 0x15, 0x78, 0x77, 0xa2, 0xa7, 0x63, 0x78, 0xa9, 0x8a, 0xaf, 0xca, 0x97,
	0x90, 0x0f, 0xf0, 0x58, 0x79, 0x18, 0xfe, 0xb3, 0x63, 0x5e, 0xe1, 0x1f, 0x21, 0xd5, 0xe0, 0xda,
	0x65, 0x5f, 0xa1, 0xeb, 0x78, 0x2c, 0xf2, 0xfc, 0xf6, 0x11, 0x96, 0xfc, 0xc2, 0xab, 0x63, 0x27,
	0x96, 0x0e, 0x32, 0xd1, 0x74, 0xf0, 0x3d, 0xac, 0xfd, 0xc3, 0x70, 0xb7, 0x8f, 0x2f, 0x9c, 0xe2,
	0x11, 0x61, 0x50, 0x66, 0x83, 0x11, 0x80, 0x44, 0xfa, 0x69, 0x07, 0x84, 0xbc, 0x30, 0x4c, 0x53,
	0xb4, 0x92, 0xef, 0x98, 0xef, 0x0d, 0xaf, 0x1c, 0x93, 0x35, 0xdd, 0x3e, 0xf9, 0x01, 0x94, 0x42,
	0x3c, 0x9f, 0x18, 0xa6, 0x39, 0x68, 0x61, 0xb9, 0xa9, 0x25, 0x37, 0x49, 0x79, 0xf3, 0x10, 0x26,
	0xb0, 0xab, 0x27, 0xcb, 0x40, 0x58, 0xf1, 0x1a, 0xab, 0x6e, 0xe7, 0xa0, 0x20, 0xe4, 0x6d, 0xe9,
	0xe0, 0x65, 0x31, 0x43, 0x56, 0xe0, 0x61, 0x20, 0x50, 0x5e, 0xcb, 0x0d, 0x49, 0x6b, 0x29, 0x47,
	0xaf, 0xe5, 0x3d, 0x49, 0x29, 0x66, 0x37, 0xff, 0x9c, 0x81, 0x1c, 0x6f, 0xe6, 0xc8, 0x43, 0x58,
	0xa8, 0xb7, 0x5a, 0x07, 0x5f, 0xc5, 0x2c, 0x2e, 0xc1, 0xbc, 0x3f, 0xc0, 0x4c, 0xfa, 0x35, 0xf3,
	0x13, 0xa8, 0x0e, 0xc4, 0x51, 0xc3, 0x1c, 0x92, 0x25, 0x65, 0x58, 0x8a, 0x68, 0x06, 0xa5, 0xf5,
	0x18, 0x79, 0x0a, 0x4f, 0x52, 0xb5, 0x03, 0xd8, 0xf8, 0x66, 0x1b, 0x26, 0xc5, 0x03, 0x0e, 0x2b,
	0xe8, 0xe5, 0xe6, 0xeb, 0xa3, 0x2f, 0xe2, 0x05, 0xfd, 0x22, 0x14, 0x83, 0x11, 0x45, 0xda, 0x3f,
	0x3e, 0xa8, 0x2b, 0xc5, 0x4c, 0x04, 0xff, 0xba, 0xae, 0x6a, 0xa8, 0xc1, 0xca, 0xfa, 0xcd, 0xbf,
	0x66, 0x61, 0x2e, 0x78, 0x36, 0x6d, 0xfb, 0x45, 0x4b, 0x15, 0x59, 0xe2, 0x4e, 0xb5, 0xd5, 0xba,
	0x7a, 0xdc, 0x1e, 0x9a, 0x66, 0x15, 0x56, 0xe2, 0x90, 0xf6, 0xf1, 0x8b, 0x43, 0x59, 0x55, 0xa5,
	0xbd, 0x62, 0x86, 0x3c, 0x86, 0x4a, 0x1c, 0x50, 0x6f, 0xb1, 0x75, 0x49, 0x7b, 0xc5, 0x6c, 0xf2,
	0xb8, 0x22, 0x7d, 0x2e, 0x35, 0x98, 0xfe, 0x58, 0x8a, 0x0f, 0x6d, 0x49, 0xd1, 0xda, 0xf2, 0x7e,
	0x53, 0xda, 0x2b, 0x8e, 0x93, 0x75, 0x58, 0x4d, 0x81, 0x04, 0x76, 0x26, 0x18, 0xb7, 0x71, 0x50,
	0xeb, 0xa0, 0xae, 0xbe, 0x3c, 0x52, 0x0e, 0x7d, 0x5b, 0x39, 0xf2, 0x3e, 0xac, 0x8f, 0x80, 0x05,
	0xf6, 0x26, 0x37, 0xff, 0x92, 0x81, 0x85, 0x48, 0x25, 0x2a, 0x38, 0x7b, 0x0a, 0x4f, 0xfc, 0xad,
	0x1a, 0xc5, 0xdb, 0x3a, 0xac, 0x26, 0xc3, 0xc2, 0xdc, 0xd5, 0xe0, 0x71, 0x32, 0x28, 0xc4, 0x5f,
	0x2a, 0x66, 0xc0, 0xe1, 0xa6, 0x0c, 0xe5, 0x76, 0xf4, 0x95, 0x85, 0xed, 0xb4, 0x70, 0x78, 0x0b,
	0x36, 0x12, 0x43, 0x35, 0xd1, 0xef, 0xcd, 0x2f, 0xa1, 0x3a, 0x64, 0xca, 0x27, 0x41, 0x98, 0x7b,
	0x06, 0x5b, 0xa9, 0xb1, 0x9b, 0x6c, 0xf2, 0xf7, 0x63, 0x50, 0x08, 0x33, 0xf8, 0x08, 0x4a, 0x23,
	0x88, 0xab, 0xc0, 0x72, 0x2a, 0x5f, 0x5b, 0xb0, 0x11, 0x1d, 0x1b, 0x76, 0xa4, 0xa5, 0x1c, 0x35,
	0xa4, 0x76, 0x5b, 0x6e, 0xee, 0x17, 0xb3, 0xe4, 0x43, 0x78, 0x7f, 0x34, 0xba, 0x71, 0x74, 0xd8,
	0x3a, 0x90, 0x78, 0x18, 0xd6, 0xe0, 0x71, 0x04, 0x7c, 0x58, 0x57, 0x25, 0x45, 0x66, 0x9c, 0x1f,
	0xef, 0xc9, 0x2a, 0x33, 0x38, 0x9e, 0x8e, 0x09, 0x85, 0x61, 0x09, 0x16, 0xa3, 0x93, 0xca, 0xfb,
	0x4d, 0xa6, 0x9d, 0x63, 0x07, 0x61, 0x44, 0x00, 0x4f, 0x92, 0x35, 0x78, 0x34, 0x32, 0x76, 0xa7,
	0x62, 0xf3, 0xc7, 0xc3, 0x36, 0x1f, 0xa3, 0x6f, 0xb0, 0x46, 0xd8, 0xfc, 0x47, 0x06, 0x8a, 0xc3,
	0x55, 0x2f, 0x3b, 0x7f, 0x6f, 0x64, 0xf5, 0xd5, 0x9e, 0x52, 0x7f, 0x93, 0xb2, 0x81, 0x6c, 0xde,
	0x38, 0xe4, 0x4d, 0x5d, 0x56, 0x35, 0x96, 0x6b, 0xe4, 0x86, 0xc4, 0xd3, 0x40, 0x1c, 0x13, 0x70,
	0x97, 0x4d, 0x1e, 0x0f, 0xa5, 0x81, 0x2a, 0x94, 0xe3, 0xe3, 0x2d, 0xa9, 0xb9, 0xc7, 0xa9, 0xaf,
	0xc0, 0x72, 0xc2, 0x70, 0x5d, 0xde, 0x2b, 0x4e, 0x6c, 0x5e, 0xc1, 0xdc, 0xd0, 0xef, 0x02, 0x6c,
	0x51, 0x6a, 0xfd, 0xa7, 0xad, 0xfa, 0x57, 0x92, 0xa2, 0x49, 0x4d, 0x55, 0x56, 0xe3, 0x09, 0xbe,
	0x0a, 0xe5, 0x38, 0x64, 0x5f, 0x6a, 0x4a, 0x4a, 0xfd, 0x80, 0x27, 0xfa, 0xf8, 0x70, 0xfb, 0xb0,
	0x7e, 0x70, 0xa0, 0xb5, 0x1b, 0xf5, 0x03, 0xa9, 0x98, 0xdd, 0xfd, 0xf7, 0x0c, 0x14, 0x07, 0x97,
	0x1d, 0xef, 0x5d, 0x89, 0x05, 0x0b, 0x09, 0xbf, 0x42, 0x92, 0xa4, 0x67, 0x8b, 0xe4, 0x9f, 0x36,
	0x2b, 0x9b, 0x77, 0x85, 0xba, 0xfd, 0xda, 0x03, 0xf2, 0xdb, 0x0c, 0x94, 0x53, 0x7f, 0xe6, 0x23,
	0x3b, 0x09, 0xb6, 0x46, 0xfd, 0x8c, 0x58, 0x79, 0x76, 0x3f, 0x05, 0x74, 0xc1, 0x83, 0xa5, 0xc4,
	0xdf, 0xe6, 0xc8, 0x87, 0x49, 0x65, 0x55, 0xca, 0xcf, 0x87, 0x95, 0xad, 0xbb, 0x83, 0x71, 0xd6,
	0x0b, 0x20, 0xf1, 0xf7, 0x60, 0xb2, 0x91, 0x4c, 0x5e, 0xfc, 0xad, 0xb9, 0xf2, 0xc1, 0x1d, 0x91,
	0x38, 0xd9, 0xd7, 0x30, 0x13, 0x79, 0x45, 0x25, 0xeb, 0xa3, 0xb4, 0xfd, 0x29, 0xde, 0x7b, 0x37,
	0xc8, 0x27, 0x30, 0xf1, 0x5d, 0x34, 0x91, 0xc0, 0xb4, 0x87, 0xd7, 0x44, 0x02, 0x53, 0x9f, 0x5b,
	0x45, 0xe4, 0xa4, 0xbe, 0xc3, 0x25, 0x46, 0xce, 0xa8, 0x67, 0xc1, 0xc4, 0xc8, 0x19, 0xf9, 0xcc,
	0x57, 0x7b, 0x40, 0xae, 0x87, 0xdf, 0xf4, 0xfd, 0x17, 0x34, 0xb2, 0xf5, 0x4e, 0x6b, 0xa1, 0x47,
	0xba, 0xca, 0x47, 0xf7, 0x40, 0xe3, 0xc4, 0xbf, 0x86, 0x52, 0xda, 0x53, 0x19, 0xd9, 0x1e, 0x7d,
	0x04, 0x86, 0x5f, 0xe2, 0x2a, 0x3b, 0xf7, 0xc2, 0x07, 0xd4, 0xa7, 0xbe, 0x78, 0x25, 0x52, 0x3f,
	0xea, 0x45, 0x2d, 0x91, 0xfa, 0x91, 0x0f, 0x6a, 0x01, 0xf5, 0x09, 0x8f, 0x1c, 0x69, 0xd4, 0x27,
	0xbf, 0xf1, 0xa4, 0x51, 0x9f, 0xf2, 0x7a, 0xc2, 0xa9, 0x4f, 0x7b, 0xa3, 0x48, 0xa4, 0x7e, 0xc4,
	0x53, 0x48, 0x65, 0xe7, 0x5e, 0x78, 0x9c, 0xfe, 0x04, 0xe6, 0x86, 0x3a, 0x3b, 0xf2, 0xf4, 0x2e,
	0xdd, 0xdf, 0xdb, 0xca, 0x77, 0xee, 0xd6, 0x24, 0x06, 0xd9, 0x62, 0xd0, 0x77, 0xa5, 0x65, 0x8b,
	0x48, 0x23, 0x97, 0x96, 0x2d, 0xa2, 0xed, 0x5b, 0x40, 0x60, 0x62, 0x97, 0x95, 0x46, 0x60, 0x5a,
	0x33, 0x97, 0x46, 0x60, 0x6a, 0x0b, 0x57, 0x7b, 0xf0, 0xe2, 0xf9, 0xcf, 0x3e, 0x3e, 0xb3, 0x4d,
	0xdd, 0x3a, 0xdb, 0x7e, 0xbe, 0xeb, 0x79, 0xdb, 0x1d, 0xbb, 0xb7, 0x83, 0xff, 0xbf, 0xd3, 0xb1,
	0xcd, 0x1d, 0xf1, 0x53, 0x9f, 0x1b, 0xff, 0x1f, 0x9f, 0x93, 0x1c, 0x82, 0x3e, 0xfe, 0x5f, 0x00,
	0x00, 0x00, 0xff, 0xff, 0xce, 0x2c, 0x91, 0x5a, 0x20, 0x24, 0x00, 0x00,
}
