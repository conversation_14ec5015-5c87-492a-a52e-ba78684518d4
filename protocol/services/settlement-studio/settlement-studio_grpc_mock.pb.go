// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/settlement-studio/settlement-studio.proto

package settlement_studio

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockSettlementStudioClient is a mock of SettlementStudioClient interface.
type MockSettlementStudioClient struct {
	ctrl     *gomock.Controller
	recorder *MockSettlementStudioClientMockRecorder
}

// MockSettlementStudioClientMockRecorder is the mock recorder for MockSettlementStudioClient.
type MockSettlementStudioClientMockRecorder struct {
	mock *MockSettlementStudioClient
}

// NewMockSettlementStudioClient creates a new mock instance.
func NewMockSettlementStudioClient(ctrl *gomock.Controller) *MockSettlementStudioClient {
	mock := &MockSettlementStudioClient{ctrl: ctrl}
	mock.recorder = &MockSettlementStudioClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSettlementStudioClient) EXPECT() *MockSettlementStudioClientMockRecorder {
	return m.recorder
}

// CheckStudioJoinApplyValid mocks base method.
func (m *MockSettlementStudioClient) CheckStudioJoinApplyValid(ctx context.Context, in *CheckStudioJoinApplyValidReq, opts ...grpc.CallOption) (*CheckStudioJoinApplyValidResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckStudioJoinApplyValid", varargs...)
	ret0, _ := ret[0].(*CheckStudioJoinApplyValidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStudioJoinApplyValid indicates an expected call of CheckStudioJoinApplyValid.
func (mr *MockSettlementStudioClientMockRecorder) CheckStudioJoinApplyValid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStudioJoinApplyValid", reflect.TypeOf((*MockSettlementStudioClient)(nil).CheckStudioJoinApplyValid), varargs...)
}

// CheckStudioTerminalValid mocks base method.
func (m *MockSettlementStudioClient) CheckStudioTerminalValid(ctx context.Context, in *CheckStudioTerminalValidReq, opts ...grpc.CallOption) (*CheckStudioTerminalValidResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckStudioTerminalValid", varargs...)
	ret0, _ := ret[0].(*CheckStudioTerminalValidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStudioTerminalValid indicates an expected call of CheckStudioTerminalValid.
func (mr *MockSettlementStudioClientMockRecorder) CheckStudioTerminalValid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStudioTerminalValid", reflect.TypeOf((*MockSettlementStudioClient)(nil).CheckStudioTerminalValid), varargs...)
}

// GetElectronicContract mocks base method.
func (m *MockSettlementStudioClient) GetElectronicContract(ctx context.Context, in *GetElectronicContractReq, opts ...grpc.CallOption) (*GetElectronicContractResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetElectronicContract", varargs...)
	ret0, _ := ret[0].(*GetElectronicContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetElectronicContract indicates an expected call of GetElectronicContract.
func (mr *MockSettlementStudioClientMockRecorder) GetElectronicContract(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetElectronicContract", reflect.TypeOf((*MockSettlementStudioClient)(nil).GetElectronicContract), varargs...)
}

// GetInvoiceUrl mocks base method.
func (m *MockSettlementStudioClient) GetInvoiceUrl(ctx context.Context, in *GetInvoiceUrlReq, opts ...grpc.CallOption) (*GetInvoiceUrlResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInvoiceUrl", varargs...)
	ret0, _ := ret[0].(*GetInvoiceUrlResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvoiceUrl indicates an expected call of GetInvoiceUrl.
func (mr *MockSettlementStudioClientMockRecorder) GetInvoiceUrl(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvoiceUrl", reflect.TypeOf((*MockSettlementStudioClient)(nil).GetInvoiceUrl), varargs...)
}

// GetSettlementBillFileUrl mocks base method.
func (m *MockSettlementStudioClient) GetSettlementBillFileUrl(ctx context.Context, in *GetSettlementBillFileUrlReq, opts ...grpc.CallOption) (*GetSettlementBillFileUrlResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSettlementBillFileUrl", varargs...)
	ret0, _ := ret[0].(*GetSettlementBillFileUrlResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSettlementBillFileUrl indicates an expected call of GetSettlementBillFileUrl.
func (mr *MockSettlementStudioClientMockRecorder) GetSettlementBillFileUrl(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettlementBillFileUrl", reflect.TypeOf((*MockSettlementStudioClient)(nil).GetSettlementBillFileUrl), varargs...)
}

// GetStudioApplyHistoryList mocks base method.
func (m *MockSettlementStudioClient) GetStudioApplyHistoryList(ctx context.Context, in *GetStudioApplyHistoryListReq, opts ...grpc.CallOption) (*GetStudioApplyHistoryListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStudioApplyHistoryList", varargs...)
	ret0, _ := ret[0].(*GetStudioApplyHistoryListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStudioApplyHistoryList indicates an expected call of GetStudioApplyHistoryList.
func (mr *MockSettlementStudioClientMockRecorder) GetStudioApplyHistoryList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStudioApplyHistoryList", reflect.TypeOf((*MockSettlementStudioClient)(nil).GetStudioApplyHistoryList), varargs...)
}

// GetStudioApplyInfo mocks base method.
func (m *MockSettlementStudioClient) GetStudioApplyInfo(ctx context.Context, in *GetStudioApplyInfoReq, opts ...grpc.CallOption) (*GetStudioApplyInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStudioApplyInfo", varargs...)
	ret0, _ := ret[0].(*GetStudioApplyInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStudioApplyInfo indicates an expected call of GetStudioApplyInfo.
func (mr *MockSettlementStudioClientMockRecorder) GetStudioApplyInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStudioApplyInfo", reflect.TypeOf((*MockSettlementStudioClient)(nil).GetStudioApplyInfo), varargs...)
}

// GetStudioApplySnapshot mocks base method.
func (m *MockSettlementStudioClient) GetStudioApplySnapshot(ctx context.Context, in *GetStudioApplySnapshotReq, opts ...grpc.CallOption) (*GetStudioApplySnapshotResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStudioApplySnapshot", varargs...)
	ret0, _ := ret[0].(*GetStudioApplySnapshotResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStudioApplySnapshot indicates an expected call of GetStudioApplySnapshot.
func (mr *MockSettlementStudioClientMockRecorder) GetStudioApplySnapshot(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStudioApplySnapshot", reflect.TypeOf((*MockSettlementStudioClient)(nil).GetStudioApplySnapshot), varargs...)
}

// GetStudioInfo mocks base method.
func (m *MockSettlementStudioClient) GetStudioInfo(ctx context.Context, in *GetStudioInfoReq, opts ...grpc.CallOption) (*GetStudioInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStudioInfo", varargs...)
	ret0, _ := ret[0].(*GetStudioInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStudioInfo indicates an expected call of GetStudioInfo.
func (mr *MockSettlementStudioClientMockRecorder) GetStudioInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStudioInfo", reflect.TypeOf((*MockSettlementStudioClient)(nil).GetStudioInfo), varargs...)
}

// GetUserStudioStatus mocks base method.
func (m *MockSettlementStudioClient) GetUserStudioStatus(ctx context.Context, in *GetUserStudioStatusReq, opts ...grpc.CallOption) (*GetUserStudioStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserStudioStatus", varargs...)
	ret0, _ := ret[0].(*GetUserStudioStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserStudioStatus indicates an expected call of GetUserStudioStatus.
func (mr *MockSettlementStudioClientMockRecorder) GetUserStudioStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserStudioStatus", reflect.TypeOf((*MockSettlementStudioClient)(nil).GetUserStudioStatus), varargs...)
}

// GetWithdrawalOrderDetail mocks base method.
func (m *MockSettlementStudioClient) GetWithdrawalOrderDetail(ctx context.Context, in *GetWithdrawalOrderDetailReq, opts ...grpc.CallOption) (*GetWithdrawalOrderDetailResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWithdrawalOrderDetail", varargs...)
	ret0, _ := ret[0].(*GetWithdrawalOrderDetailResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWithdrawalOrderDetail indicates an expected call of GetWithdrawalOrderDetail.
func (mr *MockSettlementStudioClientMockRecorder) GetWithdrawalOrderDetail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWithdrawalOrderDetail", reflect.TypeOf((*MockSettlementStudioClient)(nil).GetWithdrawalOrderDetail), varargs...)
}

// GetWithdrawalOrderList mocks base method.
func (m *MockSettlementStudioClient) GetWithdrawalOrderList(ctx context.Context, in *GetWithdrawalOrderListReq, opts ...grpc.CallOption) (*GetWithdrawalOrderListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWithdrawalOrderList", varargs...)
	ret0, _ := ret[0].(*GetWithdrawalOrderListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWithdrawalOrderList indicates an expected call of GetWithdrawalOrderList.
func (mr *MockSettlementStudioClientMockRecorder) GetWithdrawalOrderList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWithdrawalOrderList", reflect.TypeOf((*MockSettlementStudioClient)(nil).GetWithdrawalOrderList), varargs...)
}

// SaveInvoiceFile mocks base method.
func (m *MockSettlementStudioClient) SaveInvoiceFile(ctx context.Context, in *SaveInvoiceFileReq, opts ...grpc.CallOption) (*SaveInvoiceFileResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveInvoiceFile", varargs...)
	ret0, _ := ret[0].(*SaveInvoiceFileResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveInvoiceFile indicates an expected call of SaveInvoiceFile.
func (mr *MockSettlementStudioClientMockRecorder) SaveInvoiceFile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveInvoiceFile", reflect.TypeOf((*MockSettlementStudioClient)(nil).SaveInvoiceFile), varargs...)
}

// SubmitStudioJoinApply mocks base method.
func (m *MockSettlementStudioClient) SubmitStudioJoinApply(ctx context.Context, in *SubmitStudioJoinApplyReq, opts ...grpc.CallOption) (*SubmitStudioJoinApplyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitStudioJoinApply", varargs...)
	ret0, _ := ret[0].(*SubmitStudioJoinApplyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitStudioJoinApply indicates an expected call of SubmitStudioJoinApply.
func (mr *MockSettlementStudioClientMockRecorder) SubmitStudioJoinApply(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitStudioJoinApply", reflect.TypeOf((*MockSettlementStudioClient)(nil).SubmitStudioJoinApply), varargs...)
}

// SubmitStudioTerminalApply mocks base method.
func (m *MockSettlementStudioClient) SubmitStudioTerminalApply(ctx context.Context, in *SubmitStudioTerminalApplyReq, opts ...grpc.CallOption) (*SubmitStudioTerminalApplyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitStudioTerminalApply", varargs...)
	ret0, _ := ret[0].(*SubmitStudioTerminalApplyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitStudioTerminalApply indicates an expected call of SubmitStudioTerminalApply.
func (mr *MockSettlementStudioClientMockRecorder) SubmitStudioTerminalApply(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitStudioTerminalApply", reflect.TypeOf((*MockSettlementStudioClient)(nil).SubmitStudioTerminalApply), varargs...)
}

// MockSettlementStudioServer is a mock of SettlementStudioServer interface.
type MockSettlementStudioServer struct {
	ctrl     *gomock.Controller
	recorder *MockSettlementStudioServerMockRecorder
}

// MockSettlementStudioServerMockRecorder is the mock recorder for MockSettlementStudioServer.
type MockSettlementStudioServerMockRecorder struct {
	mock *MockSettlementStudioServer
}

// NewMockSettlementStudioServer creates a new mock instance.
func NewMockSettlementStudioServer(ctrl *gomock.Controller) *MockSettlementStudioServer {
	mock := &MockSettlementStudioServer{ctrl: ctrl}
	mock.recorder = &MockSettlementStudioServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSettlementStudioServer) EXPECT() *MockSettlementStudioServerMockRecorder {
	return m.recorder
}

// CheckStudioJoinApplyValid mocks base method.
func (m *MockSettlementStudioServer) CheckStudioJoinApplyValid(ctx context.Context, in *CheckStudioJoinApplyValidReq) (*CheckStudioJoinApplyValidResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckStudioJoinApplyValid", ctx, in)
	ret0, _ := ret[0].(*CheckStudioJoinApplyValidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStudioJoinApplyValid indicates an expected call of CheckStudioJoinApplyValid.
func (mr *MockSettlementStudioServerMockRecorder) CheckStudioJoinApplyValid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStudioJoinApplyValid", reflect.TypeOf((*MockSettlementStudioServer)(nil).CheckStudioJoinApplyValid), ctx, in)
}

// CheckStudioTerminalValid mocks base method.
func (m *MockSettlementStudioServer) CheckStudioTerminalValid(ctx context.Context, in *CheckStudioTerminalValidReq) (*CheckStudioTerminalValidResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckStudioTerminalValid", ctx, in)
	ret0, _ := ret[0].(*CheckStudioTerminalValidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStudioTerminalValid indicates an expected call of CheckStudioTerminalValid.
func (mr *MockSettlementStudioServerMockRecorder) CheckStudioTerminalValid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStudioTerminalValid", reflect.TypeOf((*MockSettlementStudioServer)(nil).CheckStudioTerminalValid), ctx, in)
}

// GetElectronicContract mocks base method.
func (m *MockSettlementStudioServer) GetElectronicContract(ctx context.Context, in *GetElectronicContractReq) (*GetElectronicContractResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetElectronicContract", ctx, in)
	ret0, _ := ret[0].(*GetElectronicContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetElectronicContract indicates an expected call of GetElectronicContract.
func (mr *MockSettlementStudioServerMockRecorder) GetElectronicContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetElectronicContract", reflect.TypeOf((*MockSettlementStudioServer)(nil).GetElectronicContract), ctx, in)
}

// GetInvoiceUrl mocks base method.
func (m *MockSettlementStudioServer) GetInvoiceUrl(ctx context.Context, in *GetInvoiceUrlReq) (*GetInvoiceUrlResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInvoiceUrl", ctx, in)
	ret0, _ := ret[0].(*GetInvoiceUrlResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvoiceUrl indicates an expected call of GetInvoiceUrl.
func (mr *MockSettlementStudioServerMockRecorder) GetInvoiceUrl(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvoiceUrl", reflect.TypeOf((*MockSettlementStudioServer)(nil).GetInvoiceUrl), ctx, in)
}

// GetSettlementBillFileUrl mocks base method.
func (m *MockSettlementStudioServer) GetSettlementBillFileUrl(ctx context.Context, in *GetSettlementBillFileUrlReq) (*GetSettlementBillFileUrlResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSettlementBillFileUrl", ctx, in)
	ret0, _ := ret[0].(*GetSettlementBillFileUrlResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSettlementBillFileUrl indicates an expected call of GetSettlementBillFileUrl.
func (mr *MockSettlementStudioServerMockRecorder) GetSettlementBillFileUrl(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettlementBillFileUrl", reflect.TypeOf((*MockSettlementStudioServer)(nil).GetSettlementBillFileUrl), ctx, in)
}

// GetStudioApplyHistoryList mocks base method.
func (m *MockSettlementStudioServer) GetStudioApplyHistoryList(ctx context.Context, in *GetStudioApplyHistoryListReq) (*GetStudioApplyHistoryListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStudioApplyHistoryList", ctx, in)
	ret0, _ := ret[0].(*GetStudioApplyHistoryListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStudioApplyHistoryList indicates an expected call of GetStudioApplyHistoryList.
func (mr *MockSettlementStudioServerMockRecorder) GetStudioApplyHistoryList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStudioApplyHistoryList", reflect.TypeOf((*MockSettlementStudioServer)(nil).GetStudioApplyHistoryList), ctx, in)
}

// GetStudioApplyInfo mocks base method.
func (m *MockSettlementStudioServer) GetStudioApplyInfo(ctx context.Context, in *GetStudioApplyInfoReq) (*GetStudioApplyInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStudioApplyInfo", ctx, in)
	ret0, _ := ret[0].(*GetStudioApplyInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStudioApplyInfo indicates an expected call of GetStudioApplyInfo.
func (mr *MockSettlementStudioServerMockRecorder) GetStudioApplyInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStudioApplyInfo", reflect.TypeOf((*MockSettlementStudioServer)(nil).GetStudioApplyInfo), ctx, in)
}

// GetStudioApplySnapshot mocks base method.
func (m *MockSettlementStudioServer) GetStudioApplySnapshot(ctx context.Context, in *GetStudioApplySnapshotReq) (*GetStudioApplySnapshotResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStudioApplySnapshot", ctx, in)
	ret0, _ := ret[0].(*GetStudioApplySnapshotResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStudioApplySnapshot indicates an expected call of GetStudioApplySnapshot.
func (mr *MockSettlementStudioServerMockRecorder) GetStudioApplySnapshot(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStudioApplySnapshot", reflect.TypeOf((*MockSettlementStudioServer)(nil).GetStudioApplySnapshot), ctx, in)
}

// GetStudioInfo mocks base method.
func (m *MockSettlementStudioServer) GetStudioInfo(ctx context.Context, in *GetStudioInfoReq) (*GetStudioInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStudioInfo", ctx, in)
	ret0, _ := ret[0].(*GetStudioInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStudioInfo indicates an expected call of GetStudioInfo.
func (mr *MockSettlementStudioServerMockRecorder) GetStudioInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStudioInfo", reflect.TypeOf((*MockSettlementStudioServer)(nil).GetStudioInfo), ctx, in)
}

// GetUserStudioStatus mocks base method.
func (m *MockSettlementStudioServer) GetUserStudioStatus(ctx context.Context, in *GetUserStudioStatusReq) (*GetUserStudioStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserStudioStatus", ctx, in)
	ret0, _ := ret[0].(*GetUserStudioStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserStudioStatus indicates an expected call of GetUserStudioStatus.
func (mr *MockSettlementStudioServerMockRecorder) GetUserStudioStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserStudioStatus", reflect.TypeOf((*MockSettlementStudioServer)(nil).GetUserStudioStatus), ctx, in)
}

// GetWithdrawalOrderDetail mocks base method.
func (m *MockSettlementStudioServer) GetWithdrawalOrderDetail(ctx context.Context, in *GetWithdrawalOrderDetailReq) (*GetWithdrawalOrderDetailResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWithdrawalOrderDetail", ctx, in)
	ret0, _ := ret[0].(*GetWithdrawalOrderDetailResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWithdrawalOrderDetail indicates an expected call of GetWithdrawalOrderDetail.
func (mr *MockSettlementStudioServerMockRecorder) GetWithdrawalOrderDetail(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWithdrawalOrderDetail", reflect.TypeOf((*MockSettlementStudioServer)(nil).GetWithdrawalOrderDetail), ctx, in)
}

// GetWithdrawalOrderList mocks base method.
func (m *MockSettlementStudioServer) GetWithdrawalOrderList(ctx context.Context, in *GetWithdrawalOrderListReq) (*GetWithdrawalOrderListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWithdrawalOrderList", ctx, in)
	ret0, _ := ret[0].(*GetWithdrawalOrderListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWithdrawalOrderList indicates an expected call of GetWithdrawalOrderList.
func (mr *MockSettlementStudioServerMockRecorder) GetWithdrawalOrderList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWithdrawalOrderList", reflect.TypeOf((*MockSettlementStudioServer)(nil).GetWithdrawalOrderList), ctx, in)
}

// SaveInvoiceFile mocks base method.
func (m *MockSettlementStudioServer) SaveInvoiceFile(ctx context.Context, in *SaveInvoiceFileReq) (*SaveInvoiceFileResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveInvoiceFile", ctx, in)
	ret0, _ := ret[0].(*SaveInvoiceFileResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveInvoiceFile indicates an expected call of SaveInvoiceFile.
func (mr *MockSettlementStudioServerMockRecorder) SaveInvoiceFile(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveInvoiceFile", reflect.TypeOf((*MockSettlementStudioServer)(nil).SaveInvoiceFile), ctx, in)
}

// SubmitStudioJoinApply mocks base method.
func (m *MockSettlementStudioServer) SubmitStudioJoinApply(ctx context.Context, in *SubmitStudioJoinApplyReq) (*SubmitStudioJoinApplyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitStudioJoinApply", ctx, in)
	ret0, _ := ret[0].(*SubmitStudioJoinApplyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitStudioJoinApply indicates an expected call of SubmitStudioJoinApply.
func (mr *MockSettlementStudioServerMockRecorder) SubmitStudioJoinApply(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitStudioJoinApply", reflect.TypeOf((*MockSettlementStudioServer)(nil).SubmitStudioJoinApply), ctx, in)
}

// SubmitStudioTerminalApply mocks base method.
func (m *MockSettlementStudioServer) SubmitStudioTerminalApply(ctx context.Context, in *SubmitStudioTerminalApplyReq) (*SubmitStudioTerminalApplyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitStudioTerminalApply", ctx, in)
	ret0, _ := ret[0].(*SubmitStudioTerminalApplyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitStudioTerminalApply indicates an expected call of SubmitStudioTerminalApply.
func (mr *MockSettlementStudioServerMockRecorder) SubmitStudioTerminalApply(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitStudioTerminalApply", reflect.TypeOf((*MockSettlementStudioServer)(nil).SubmitStudioTerminalApply), ctx, in)
}
