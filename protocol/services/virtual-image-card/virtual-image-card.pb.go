// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/virtual-image-card/virtual-image-card.proto

package virtual_image_card // import "golang.52tt.com/protocol/services/virtual-image-card"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PayChannel int32

const (
	PayChannel_PAY_CHANNEL_UNSPECIFIED PayChannel = 0
	PayChannel_PAY_CHANNEL_ALIPAY      PayChannel = 1
	PayChannel_PAY_CHANNEL_WECHAT      PayChannel = 2
	PayChannel_PAY_CHANNEL_APPSTORE    PayChannel = 3
)

var PayChannel_name = map[int32]string{
	0: "PAY_CHANNEL_UNSPECIFIED",
	1: "PAY_CHANNEL_ALIPAY",
	2: "PAY_CHANNEL_WECHAT",
	3: "PAY_CHANNEL_APPSTORE",
}
var PayChannel_value = map[string]int32{
	"PAY_CHANNEL_UNSPECIFIED": 0,
	"PAY_CHANNEL_ALIPAY":      1,
	"PAY_CHANNEL_WECHAT":      2,
	"PAY_CHANNEL_APPSTORE":    3,
}

func (x PayChannel) String() string {
	return proto.EnumName(PayChannel_name, int32(x))
}
func (PayChannel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{0}
}

// 套餐类型
type PackageType int32

const (
	PackageType_PACKAGE_TYPE_UNSPECIFIED PackageType = 0
	PackageType_PACKAGE_TYPE_NORMAL      PackageType = 1
	PackageType_PACKAGE_TYPE_AUTO_RENEW  PackageType = 2
)

var PackageType_name = map[int32]string{
	0: "PACKAGE_TYPE_UNSPECIFIED",
	1: "PACKAGE_TYPE_NORMAL",
	2: "PACKAGE_TYPE_AUTO_RENEW",
}
var PackageType_value = map[string]int32{
	"PACKAGE_TYPE_UNSPECIFIED": 0,
	"PACKAGE_TYPE_NORMAL":      1,
	"PACKAGE_TYPE_AUTO_RENEW":  2,
}

func (x PackageType) String() string {
	return proto.EnumName(PackageType_name, int32(x))
}
func (PackageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{1}
}

// 在售架套餐状态
type SalePackageStatus int32

const (
	SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_UNSPECIFIED  SalePackageStatus = 0
	SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_SHELF        SalePackageStatus = 1
	SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_NOT_ON_SHELF SalePackageStatus = 2
	SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_WAIT_SHELF   SalePackageStatus = 3
)

var SalePackageStatus_name = map[int32]string{
	0: "ENUM_SALE_PACKAGE_STATUS_UNSPECIFIED",
	1: "ENUM_SALE_PACKAGE_STATUS_SHELF",
	2: "ENUM_SALE_PACKAGE_STATUS_NOT_ON_SHELF",
	3: "ENUM_SALE_PACKAGE_STATUS_WAIT_SHELF",
}
var SalePackageStatus_value = map[string]int32{
	"ENUM_SALE_PACKAGE_STATUS_UNSPECIFIED":  0,
	"ENUM_SALE_PACKAGE_STATUS_SHELF":        1,
	"ENUM_SALE_PACKAGE_STATUS_NOT_ON_SHELF": 2,
	"ENUM_SALE_PACKAGE_STATUS_WAIT_SHELF":   3,
}

func (x SalePackageStatus) String() string {
	return proto.EnumName(SalePackageStatus_name, int32(x))
}
func (SalePackageStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{2}
}

type CardStatus int32

const (
	CardStatus_ENUM_STATUS_NO_OPEN     CardStatus = 0
	CardStatus_ENUM_STATUS_OPENING     CardStatus = 1
	CardStatus_ENUM_STATUS_SOON_EXPIRE CardStatus = 2
	CardStatus_ENUM_STATUS_EXPIRED     CardStatus = 3
	CardStatus_ENUM_STATUS_SIGN        CardStatus = 4
	CardStatus_ENUM_STATUS_TRIAL       CardStatus = 5
)

var CardStatus_name = map[int32]string{
	0: "ENUM_STATUS_NO_OPEN",
	1: "ENUM_STATUS_OPENING",
	2: "ENUM_STATUS_SOON_EXPIRE",
	3: "ENUM_STATUS_EXPIRED",
	4: "ENUM_STATUS_SIGN",
	5: "ENUM_STATUS_TRIAL",
}
var CardStatus_value = map[string]int32{
	"ENUM_STATUS_NO_OPEN":     0,
	"ENUM_STATUS_OPENING":     1,
	"ENUM_STATUS_SOON_EXPIRE": 2,
	"ENUM_STATUS_EXPIRED":     3,
	"ENUM_STATUS_SIGN":        4,
	"ENUM_STATUS_TRIAL":       5,
}

func (x CardStatus) String() string {
	return proto.EnumName(CardStatus_name, int32(x))
}
func (CardStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{3}
}

type DisplayCondition_CondType int32

const (
	DisplayCondition_ENUM_USER_ALL       DisplayCondition_CondType = 0
	DisplayCondition_ENUM_USER_SPECIFIED DisplayCondition_CondType = 1
)

var DisplayCondition_CondType_name = map[int32]string{
	0: "ENUM_USER_ALL",
	1: "ENUM_USER_SPECIFIED",
}
var DisplayCondition_CondType_value = map[string]int32{
	"ENUM_USER_ALL":       0,
	"ENUM_USER_SPECIFIED": 1,
}

func (x DisplayCondition_CondType) String() string {
	return proto.EnumName(DisplayCondition_CondType_name, int32(x))
}
func (DisplayCondition_CondType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{38, 0}
}

type PlaceOrderReq struct {
	Uid                    uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PackageId              uint32     `protobuf:"varint,2,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	PayChannel             PayChannel `protobuf:"varint,3,opt,name=pay_channel,json=payChannel,proto3,enum=virtual_image_card.PayChannel" json:"pay_channel,omitempty"`
	OriginalTransactionIds []string   `protobuf:"bytes,4,rep,name=original_transaction_ids,json=originalTransactionIds,proto3" json:"original_transaction_ids,omitempty"`
	PayPriceCent           uint32     `protobuf:"varint,5,opt,name=pay_price_cent,json=payPriceCent,proto3" json:"pay_price_cent,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}   `json:"-"`
	XXX_unrecognized       []byte     `json:"-"`
	XXX_sizecache          int32      `json:"-"`
}

func (m *PlaceOrderReq) Reset()         { *m = PlaceOrderReq{} }
func (m *PlaceOrderReq) String() string { return proto.CompactTextString(m) }
func (*PlaceOrderReq) ProtoMessage()    {}
func (*PlaceOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{0}
}
func (m *PlaceOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaceOrderReq.Unmarshal(m, b)
}
func (m *PlaceOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaceOrderReq.Marshal(b, m, deterministic)
}
func (dst *PlaceOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaceOrderReq.Merge(dst, src)
}
func (m *PlaceOrderReq) XXX_Size() int {
	return xxx_messageInfo_PlaceOrderReq.Size(m)
}
func (m *PlaceOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaceOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_PlaceOrderReq proto.InternalMessageInfo

func (m *PlaceOrderReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PlaceOrderReq) GetPackageId() uint32 {
	if m != nil {
		return m.PackageId
	}
	return 0
}

func (m *PlaceOrderReq) GetPayChannel() PayChannel {
	if m != nil {
		return m.PayChannel
	}
	return PayChannel_PAY_CHANNEL_UNSPECIFIED
}

func (m *PlaceOrderReq) GetOriginalTransactionIds() []string {
	if m != nil {
		return m.OriginalTransactionIds
	}
	return nil
}

func (m *PlaceOrderReq) GetPayPriceCent() uint32 {
	if m != nil {
		return m.PayPriceCent
	}
	return 0
}

type PlaceOrderResp struct {
	OrderNo              string   `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	Token                string   `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	CliOrderNo           string   `protobuf:"bytes,3,opt,name=cli_order_no,json=cliOrderNo,proto3" json:"cli_order_no,omitempty"`
	CliOrderTitle        string   `protobuf:"bytes,4,opt,name=cli_order_title,json=cliOrderTitle,proto3" json:"cli_order_title,omitempty"`
	OrderPrice           string   `protobuf:"bytes,5,opt,name=order_price,json=orderPrice,proto3" json:"order_price,omitempty"`
	Tsk                  string   `protobuf:"bytes,6,opt,name=tsk,proto3" json:"tsk,omitempty"`
	ChannelMap           string   `protobuf:"bytes,7,opt,name=channel_map,json=channelMap,proto3" json:"channel_map,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaceOrderResp) Reset()         { *m = PlaceOrderResp{} }
func (m *PlaceOrderResp) String() string { return proto.CompactTextString(m) }
func (*PlaceOrderResp) ProtoMessage()    {}
func (*PlaceOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{1}
}
func (m *PlaceOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaceOrderResp.Unmarshal(m, b)
}
func (m *PlaceOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaceOrderResp.Marshal(b, m, deterministic)
}
func (dst *PlaceOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaceOrderResp.Merge(dst, src)
}
func (m *PlaceOrderResp) XXX_Size() int {
	return xxx_messageInfo_PlaceOrderResp.Size(m)
}
func (m *PlaceOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaceOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_PlaceOrderResp proto.InternalMessageInfo

func (m *PlaceOrderResp) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

func (m *PlaceOrderResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *PlaceOrderResp) GetCliOrderNo() string {
	if m != nil {
		return m.CliOrderNo
	}
	return ""
}

func (m *PlaceOrderResp) GetCliOrderTitle() string {
	if m != nil {
		return m.CliOrderTitle
	}
	return ""
}

func (m *PlaceOrderResp) GetOrderPrice() string {
	if m != nil {
		return m.OrderPrice
	}
	return ""
}

func (m *PlaceOrderResp) GetTsk() string {
	if m != nil {
		return m.Tsk
	}
	return ""
}

func (m *PlaceOrderResp) GetChannelMap() string {
	if m != nil {
		return m.ChannelMap
	}
	return ""
}

type CancelOrderReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderNo              string   `protobuf:"bytes,2,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelOrderReq) Reset()         { *m = CancelOrderReq{} }
func (m *CancelOrderReq) String() string { return proto.CompactTextString(m) }
func (*CancelOrderReq) ProtoMessage()    {}
func (*CancelOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{2}
}
func (m *CancelOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelOrderReq.Unmarshal(m, b)
}
func (m *CancelOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelOrderReq.Marshal(b, m, deterministic)
}
func (dst *CancelOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelOrderReq.Merge(dst, src)
}
func (m *CancelOrderReq) XXX_Size() int {
	return xxx_messageInfo_CancelOrderReq.Size(m)
}
func (m *CancelOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelOrderReq proto.InternalMessageInfo

func (m *CancelOrderReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CancelOrderReq) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

type CancelOrderResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelOrderResp) Reset()         { *m = CancelOrderResp{} }
func (m *CancelOrderResp) String() string { return proto.CompactTextString(m) }
func (*CancelOrderResp) ProtoMessage()    {}
func (*CancelOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{3}
}
func (m *CancelOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelOrderResp.Unmarshal(m, b)
}
func (m *CancelOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelOrderResp.Marshal(b, m, deterministic)
}
func (dst *CancelOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelOrderResp.Merge(dst, src)
}
func (m *CancelOrderResp) XXX_Size() int {
	return xxx_messageInfo_CancelOrderResp.Size(m)
}
func (m *CancelOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelOrderResp proto.InternalMessageInfo

type PayCallbackReq struct {
	OrderNo              string   `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	CliOrderNo           string   `protobuf:"bytes,2,opt,name=cli_order_no,json=cliOrderNo,proto3" json:"cli_order_no,omitempty"`
	OtherOrderNo         string   `protobuf:"bytes,3,opt,name=other_order_no,json=otherOrderNo,proto3" json:"other_order_no,omitempty"`
	CoinPayChannel       string   `protobuf:"bytes,4,opt,name=coin_pay_channel,json=coinPayChannel,proto3" json:"coin_pay_channel,omitempty"`
	PayPriceCent         uint32   `protobuf:"varint,5,opt,name=pay_price_cent,json=payPriceCent,proto3" json:"pay_price_cent,omitempty"`
	PayTs                int64    `protobuf:"varint,6,opt,name=pay_ts,json=payTs,proto3" json:"pay_ts,omitempty"`
	ActiveUid            uint32   `protobuf:"varint,7,opt,name=active_uid,json=activeUid,proto3" json:"active_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayCallbackReq) Reset()         { *m = PayCallbackReq{} }
func (m *PayCallbackReq) String() string { return proto.CompactTextString(m) }
func (*PayCallbackReq) ProtoMessage()    {}
func (*PayCallbackReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{4}
}
func (m *PayCallbackReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayCallbackReq.Unmarshal(m, b)
}
func (m *PayCallbackReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayCallbackReq.Marshal(b, m, deterministic)
}
func (dst *PayCallbackReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayCallbackReq.Merge(dst, src)
}
func (m *PayCallbackReq) XXX_Size() int {
	return xxx_messageInfo_PayCallbackReq.Size(m)
}
func (m *PayCallbackReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PayCallbackReq.DiscardUnknown(m)
}

var xxx_messageInfo_PayCallbackReq proto.InternalMessageInfo

func (m *PayCallbackReq) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

func (m *PayCallbackReq) GetCliOrderNo() string {
	if m != nil {
		return m.CliOrderNo
	}
	return ""
}

func (m *PayCallbackReq) GetOtherOrderNo() string {
	if m != nil {
		return m.OtherOrderNo
	}
	return ""
}

func (m *PayCallbackReq) GetCoinPayChannel() string {
	if m != nil {
		return m.CoinPayChannel
	}
	return ""
}

func (m *PayCallbackReq) GetPayPriceCent() uint32 {
	if m != nil {
		return m.PayPriceCent
	}
	return 0
}

func (m *PayCallbackReq) GetPayTs() int64 {
	if m != nil {
		return m.PayTs
	}
	return 0
}

func (m *PayCallbackReq) GetActiveUid() uint32 {
	if m != nil {
		return m.ActiveUid
	}
	return 0
}

type PayCallbackResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayCallbackResp) Reset()         { *m = PayCallbackResp{} }
func (m *PayCallbackResp) String() string { return proto.CompactTextString(m) }
func (*PayCallbackResp) ProtoMessage()    {}
func (*PayCallbackResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{5}
}
func (m *PayCallbackResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayCallbackResp.Unmarshal(m, b)
}
func (m *PayCallbackResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayCallbackResp.Marshal(b, m, deterministic)
}
func (dst *PayCallbackResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayCallbackResp.Merge(dst, src)
}
func (m *PayCallbackResp) XXX_Size() int {
	return xxx_messageInfo_PayCallbackResp.Size(m)
}
func (m *PayCallbackResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PayCallbackResp.DiscardUnknown(m)
}

var xxx_messageInfo_PayCallbackResp proto.InternalMessageInfo

type NotifyContractReq struct {
	ContractId           string   `protobuf:"bytes,1,opt,name=contract_id,json=contractId,proto3" json:"contract_id,omitempty"`
	IsSign               bool     `protobuf:"varint,2,opt,name=is_sign,json=isSign,proto3" json:"is_sign,omitempty"`
	ActiveUid            uint32   `protobuf:"varint,3,opt,name=active_uid,json=activeUid,proto3" json:"active_uid,omitempty"`
	RealBuyerUid         uint32   `protobuf:"varint,4,opt,name=real_buyer_uid,json=realBuyerUid,proto3" json:"real_buyer_uid,omitempty"`
	ProductId            string   `protobuf:"bytes,5,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	NextPayTs            int64    `protobuf:"varint,6,opt,name=next_pay_ts,json=nextPayTs,proto3" json:"next_pay_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyContractReq) Reset()         { *m = NotifyContractReq{} }
func (m *NotifyContractReq) String() string { return proto.CompactTextString(m) }
func (*NotifyContractReq) ProtoMessage()    {}
func (*NotifyContractReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{6}
}
func (m *NotifyContractReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyContractReq.Unmarshal(m, b)
}
func (m *NotifyContractReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyContractReq.Marshal(b, m, deterministic)
}
func (dst *NotifyContractReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyContractReq.Merge(dst, src)
}
func (m *NotifyContractReq) XXX_Size() int {
	return xxx_messageInfo_NotifyContractReq.Size(m)
}
func (m *NotifyContractReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyContractReq.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyContractReq proto.InternalMessageInfo

func (m *NotifyContractReq) GetContractId() string {
	if m != nil {
		return m.ContractId
	}
	return ""
}

func (m *NotifyContractReq) GetIsSign() bool {
	if m != nil {
		return m.IsSign
	}
	return false
}

func (m *NotifyContractReq) GetActiveUid() uint32 {
	if m != nil {
		return m.ActiveUid
	}
	return 0
}

func (m *NotifyContractReq) GetRealBuyerUid() uint32 {
	if m != nil {
		return m.RealBuyerUid
	}
	return 0
}

func (m *NotifyContractReq) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *NotifyContractReq) GetNextPayTs() int64 {
	if m != nil {
		return m.NextPayTs
	}
	return 0
}

type NotifyContractResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyContractResp) Reset()         { *m = NotifyContractResp{} }
func (m *NotifyContractResp) String() string { return proto.CompactTextString(m) }
func (*NotifyContractResp) ProtoMessage()    {}
func (*NotifyContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{7}
}
func (m *NotifyContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyContractResp.Unmarshal(m, b)
}
func (m *NotifyContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyContractResp.Marshal(b, m, deterministic)
}
func (dst *NotifyContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyContractResp.Merge(dst, src)
}
func (m *NotifyContractResp) XXX_Size() int {
	return xxx_messageInfo_NotifyContractResp.Size(m)
}
func (m *NotifyContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyContractResp proto.InternalMessageInfo

type PlaceAutoPayOrderReq struct {
	ContractId           string   `protobuf:"bytes,1,opt,name=contract_id,json=contractId,proto3" json:"contract_id,omitempty"`
	ProductId            string   `protobuf:"bytes,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaceAutoPayOrderReq) Reset()         { *m = PlaceAutoPayOrderReq{} }
func (m *PlaceAutoPayOrderReq) String() string { return proto.CompactTextString(m) }
func (*PlaceAutoPayOrderReq) ProtoMessage()    {}
func (*PlaceAutoPayOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{8}
}
func (m *PlaceAutoPayOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaceAutoPayOrderReq.Unmarshal(m, b)
}
func (m *PlaceAutoPayOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaceAutoPayOrderReq.Marshal(b, m, deterministic)
}
func (dst *PlaceAutoPayOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaceAutoPayOrderReq.Merge(dst, src)
}
func (m *PlaceAutoPayOrderReq) XXX_Size() int {
	return xxx_messageInfo_PlaceAutoPayOrderReq.Size(m)
}
func (m *PlaceAutoPayOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaceAutoPayOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_PlaceAutoPayOrderReq proto.InternalMessageInfo

func (m *PlaceAutoPayOrderReq) GetContractId() string {
	if m != nil {
		return m.ContractId
	}
	return ""
}

func (m *PlaceAutoPayOrderReq) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

type PlaceAutoPayOrderResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaceAutoPayOrderResp) Reset()         { *m = PlaceAutoPayOrderResp{} }
func (m *PlaceAutoPayOrderResp) String() string { return proto.CompactTextString(m) }
func (*PlaceAutoPayOrderResp) ProtoMessage()    {}
func (*PlaceAutoPayOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{9}
}
func (m *PlaceAutoPayOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaceAutoPayOrderResp.Unmarshal(m, b)
}
func (m *PlaceAutoPayOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaceAutoPayOrderResp.Marshal(b, m, deterministic)
}
func (dst *PlaceAutoPayOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaceAutoPayOrderResp.Merge(dst, src)
}
func (m *PlaceAutoPayOrderResp) XXX_Size() int {
	return xxx_messageInfo_PlaceAutoPayOrderResp.Size(m)
}
func (m *PlaceAutoPayOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaceAutoPayOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_PlaceAutoPayOrderResp proto.InternalMessageInfo

// 订单退款
type RevokeOrderReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	NotifyTime           string   `protobuf:"bytes,2,opt,name=notify_time,json=notifyTime,proto3" json:"notify_time,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeOrderReq) Reset()         { *m = RevokeOrderReq{} }
func (m *RevokeOrderReq) String() string { return proto.CompactTextString(m) }
func (*RevokeOrderReq) ProtoMessage()    {}
func (*RevokeOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{10}
}
func (m *RevokeOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeOrderReq.Unmarshal(m, b)
}
func (m *RevokeOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeOrderReq.Marshal(b, m, deterministic)
}
func (dst *RevokeOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeOrderReq.Merge(dst, src)
}
func (m *RevokeOrderReq) XXX_Size() int {
	return xxx_messageInfo_RevokeOrderReq.Size(m)
}
func (m *RevokeOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeOrderReq proto.InternalMessageInfo

func (m *RevokeOrderReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *RevokeOrderReq) GetNotifyTime() string {
	if m != nil {
		return m.NotifyTime
	}
	return ""
}

func (m *RevokeOrderReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type RevokeOrderResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeOrderResp) Reset()         { *m = RevokeOrderResp{} }
func (m *RevokeOrderResp) String() string { return proto.CompactTextString(m) }
func (*RevokeOrderResp) ProtoMessage()    {}
func (*RevokeOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{11}
}
func (m *RevokeOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeOrderResp.Unmarshal(m, b)
}
func (m *RevokeOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeOrderResp.Marshal(b, m, deterministic)
}
func (dst *RevokeOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeOrderResp.Merge(dst, src)
}
func (m *RevokeOrderResp) XXX_Size() int {
	return xxx_messageInfo_RevokeOrderResp.Size(m)
}
func (m *RevokeOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeOrderResp proto.InternalMessageInfo

type UseVirtualImageTrialCardReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FuncCardId           uint32   `protobuf:"varint,2,opt,name=func_card_id,json=funcCardId,proto3" json:"func_card_id,omitempty"`
	AddDays              uint32   `protobuf:"varint,3,opt,name=add_days,json=addDays,proto3" json:"add_days,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UseVirtualImageTrialCardReq) Reset()         { *m = UseVirtualImageTrialCardReq{} }
func (m *UseVirtualImageTrialCardReq) String() string { return proto.CompactTextString(m) }
func (*UseVirtualImageTrialCardReq) ProtoMessage()    {}
func (*UseVirtualImageTrialCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{12}
}
func (m *UseVirtualImageTrialCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UseVirtualImageTrialCardReq.Unmarshal(m, b)
}
func (m *UseVirtualImageTrialCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UseVirtualImageTrialCardReq.Marshal(b, m, deterministic)
}
func (dst *UseVirtualImageTrialCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UseVirtualImageTrialCardReq.Merge(dst, src)
}
func (m *UseVirtualImageTrialCardReq) XXX_Size() int {
	return xxx_messageInfo_UseVirtualImageTrialCardReq.Size(m)
}
func (m *UseVirtualImageTrialCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UseVirtualImageTrialCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_UseVirtualImageTrialCardReq proto.InternalMessageInfo

func (m *UseVirtualImageTrialCardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UseVirtualImageTrialCardReq) GetFuncCardId() uint32 {
	if m != nil {
		return m.FuncCardId
	}
	return 0
}

func (m *UseVirtualImageTrialCardReq) GetAddDays() uint32 {
	if m != nil {
		return m.AddDays
	}
	return 0
}

type UseVirtualImageTrialCardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UseVirtualImageTrialCardResp) Reset()         { *m = UseVirtualImageTrialCardResp{} }
func (m *UseVirtualImageTrialCardResp) String() string { return proto.CompactTextString(m) }
func (*UseVirtualImageTrialCardResp) ProtoMessage()    {}
func (*UseVirtualImageTrialCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{13}
}
func (m *UseVirtualImageTrialCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UseVirtualImageTrialCardResp.Unmarshal(m, b)
}
func (m *UseVirtualImageTrialCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UseVirtualImageTrialCardResp.Marshal(b, m, deterministic)
}
func (dst *UseVirtualImageTrialCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UseVirtualImageTrialCardResp.Merge(dst, src)
}
func (m *UseVirtualImageTrialCardResp) XXX_Size() int {
	return xxx_messageInfo_UseVirtualImageTrialCardResp.Size(m)
}
func (m *UseVirtualImageTrialCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UseVirtualImageTrialCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_UseVirtualImageTrialCardResp proto.InternalMessageInfo

type GenerateStatReq struct {
	// 没有严格1号开始/结束，留点tricky测试想象
	StartTime            string   `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              string   `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenerateStatReq) Reset()         { *m = GenerateStatReq{} }
func (m *GenerateStatReq) String() string { return proto.CompactTextString(m) }
func (*GenerateStatReq) ProtoMessage()    {}
func (*GenerateStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{14}
}
func (m *GenerateStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenerateStatReq.Unmarshal(m, b)
}
func (m *GenerateStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenerateStatReq.Marshal(b, m, deterministic)
}
func (dst *GenerateStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenerateStatReq.Merge(dst, src)
}
func (m *GenerateStatReq) XXX_Size() int {
	return xxx_messageInfo_GenerateStatReq.Size(m)
}
func (m *GenerateStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GenerateStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GenerateStatReq proto.InternalMessageInfo

func (m *GenerateStatReq) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GenerateStatReq) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

type GenerateStatResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenerateStatResp) Reset()         { *m = GenerateStatResp{} }
func (m *GenerateStatResp) String() string { return proto.CompactTextString(m) }
func (*GenerateStatResp) ProtoMessage()    {}
func (*GenerateStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{15}
}
func (m *GenerateStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenerateStatResp.Unmarshal(m, b)
}
func (m *GenerateStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenerateStatResp.Marshal(b, m, deterministic)
}
func (dst *GenerateStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenerateStatResp.Merge(dst, src)
}
func (m *GenerateStatResp) XXX_Size() int {
	return xxx_messageInfo_GenerateStatResp.Size(m)
}
func (m *GenerateStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GenerateStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GenerateStatResp proto.InternalMessageInfo

type GetContractByIdReq struct {
	ContractId           string   `protobuf:"bytes,1,opt,name=contract_id,json=contractId,proto3" json:"contract_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetContractByIdReq) Reset()         { *m = GetContractByIdReq{} }
func (m *GetContractByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetContractByIdReq) ProtoMessage()    {}
func (*GetContractByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{16}
}
func (m *GetContractByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetContractByIdReq.Unmarshal(m, b)
}
func (m *GetContractByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetContractByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetContractByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetContractByIdReq.Merge(dst, src)
}
func (m *GetContractByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetContractByIdReq.Size(m)
}
func (m *GetContractByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetContractByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetContractByIdReq proto.InternalMessageInfo

func (m *GetContractByIdReq) GetContractId() string {
	if m != nil {
		return m.ContractId
	}
	return ""
}

type GetContractByIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetContractByIdResp) Reset()         { *m = GetContractByIdResp{} }
func (m *GetContractByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetContractByIdResp) ProtoMessage()    {}
func (*GetContractByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{17}
}
func (m *GetContractByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetContractByIdResp.Unmarshal(m, b)
}
func (m *GetContractByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetContractByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetContractByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetContractByIdResp.Merge(dst, src)
}
func (m *GetContractByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetContractByIdResp.Size(m)
}
func (m *GetContractByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetContractByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetContractByIdResp proto.InternalMessageInfo

type UserCardInfo struct {
	Uid                  uint32              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	EffectTs             int64               `protobuf:"varint,2,opt,name=effect_ts,json=effectTs,proto3" json:"effect_ts,omitempty"`
	ExpireTs             int64               `protobuf:"varint,3,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
	Contracts            []*UserContractInfo `protobuf:"bytes,4,rep,name=contracts,proto3" json:"contracts,omitempty"`
	DiscountOrderId      string              `protobuf:"bytes,5,opt,name=discount_order_id,json=discountOrderId,proto3" json:"discount_order_id,omitempty"`
	FirstBuyOrderId      string              `protobuf:"bytes,6,opt,name=first_buy_order_id,json=firstBuyOrderId,proto3" json:"first_buy_order_id,omitempty"`
	BuyEffectTs          int64               `protobuf:"varint,7,opt,name=buy_effect_ts,json=buyEffectTs,proto3" json:"buy_effect_ts,omitempty"`
	BuyExpireTs          int64               `protobuf:"varint,8,opt,name=buy_expire_ts,json=buyExpireTs,proto3" json:"buy_expire_ts,omitempty"`
	TrialEffectTs        int64               `protobuf:"varint,9,opt,name=trial_effect_ts,json=trialEffectTs,proto3" json:"trial_effect_ts,omitempty"`
	TrialExpireTs        int64               `protobuf:"varint,10,opt,name=trial_expire_ts,json=trialExpireTs,proto3" json:"trial_expire_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UserCardInfo) Reset()         { *m = UserCardInfo{} }
func (m *UserCardInfo) String() string { return proto.CompactTextString(m) }
func (*UserCardInfo) ProtoMessage()    {}
func (*UserCardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{18}
}
func (m *UserCardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserCardInfo.Unmarshal(m, b)
}
func (m *UserCardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserCardInfo.Marshal(b, m, deterministic)
}
func (dst *UserCardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserCardInfo.Merge(dst, src)
}
func (m *UserCardInfo) XXX_Size() int {
	return xxx_messageInfo_UserCardInfo.Size(m)
}
func (m *UserCardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserCardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserCardInfo proto.InternalMessageInfo

func (m *UserCardInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserCardInfo) GetEffectTs() int64 {
	if m != nil {
		return m.EffectTs
	}
	return 0
}

func (m *UserCardInfo) GetExpireTs() int64 {
	if m != nil {
		return m.ExpireTs
	}
	return 0
}

func (m *UserCardInfo) GetContracts() []*UserContractInfo {
	if m != nil {
		return m.Contracts
	}
	return nil
}

func (m *UserCardInfo) GetDiscountOrderId() string {
	if m != nil {
		return m.DiscountOrderId
	}
	return ""
}

func (m *UserCardInfo) GetFirstBuyOrderId() string {
	if m != nil {
		return m.FirstBuyOrderId
	}
	return ""
}

func (m *UserCardInfo) GetBuyEffectTs() int64 {
	if m != nil {
		return m.BuyEffectTs
	}
	return 0
}

func (m *UserCardInfo) GetBuyExpireTs() int64 {
	if m != nil {
		return m.BuyExpireTs
	}
	return 0
}

func (m *UserCardInfo) GetTrialEffectTs() int64 {
	if m != nil {
		return m.TrialEffectTs
	}
	return 0
}

func (m *UserCardInfo) GetTrialExpireTs() int64 {
	if m != nil {
		return m.TrialExpireTs
	}
	return 0
}

type UserContractInfo struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ContractId           string     `protobuf:"bytes,2,opt,name=contract_id,json=contractId,proto3" json:"contract_id,omitempty"`
	PayChannel           PayChannel `protobuf:"varint,3,opt,name=pay_channel,json=payChannel,proto3,enum=virtual_image_card.PayChannel" json:"pay_channel,omitempty"`
	NextPayTs            int64      `protobuf:"varint,4,opt,name=next_pay_ts,json=nextPayTs,proto3" json:"next_pay_ts,omitempty"`
	CreateTs             int64      `protobuf:"varint,5,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	Package              *Package   `protobuf:"bytes,6,opt,name=package,proto3" json:"package,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UserContractInfo) Reset()         { *m = UserContractInfo{} }
func (m *UserContractInfo) String() string { return proto.CompactTextString(m) }
func (*UserContractInfo) ProtoMessage()    {}
func (*UserContractInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{19}
}
func (m *UserContractInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserContractInfo.Unmarshal(m, b)
}
func (m *UserContractInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserContractInfo.Marshal(b, m, deterministic)
}
func (dst *UserContractInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserContractInfo.Merge(dst, src)
}
func (m *UserContractInfo) XXX_Size() int {
	return xxx_messageInfo_UserContractInfo.Size(m)
}
func (m *UserContractInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserContractInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserContractInfo proto.InternalMessageInfo

func (m *UserContractInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserContractInfo) GetContractId() string {
	if m != nil {
		return m.ContractId
	}
	return ""
}

func (m *UserContractInfo) GetPayChannel() PayChannel {
	if m != nil {
		return m.PayChannel
	}
	return PayChannel_PAY_CHANNEL_UNSPECIFIED
}

func (m *UserContractInfo) GetNextPayTs() int64 {
	if m != nil {
		return m.NextPayTs
	}
	return 0
}

func (m *UserContractInfo) GetCreateTs() int64 {
	if m != nil {
		return m.CreateTs
	}
	return 0
}

func (m *UserContractInfo) GetPackage() *Package {
	if m != nil {
		return m.Package
	}
	return nil
}

type GetUserCardInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCardInfoReq) Reset()         { *m = GetUserCardInfoReq{} }
func (m *GetUserCardInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserCardInfoReq) ProtoMessage()    {}
func (*GetUserCardInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{20}
}
func (m *GetUserCardInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCardInfoReq.Unmarshal(m, b)
}
func (m *GetUserCardInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCardInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserCardInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCardInfoReq.Merge(dst, src)
}
func (m *GetUserCardInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserCardInfoReq.Size(m)
}
func (m *GetUserCardInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCardInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCardInfoReq proto.InternalMessageInfo

func (m *GetUserCardInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserCardInfoResp struct {
	Card                      *UserCardInfo `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	ExpireAlertStatusKeepHour uint32        `protobuf:"varint,2,opt,name=expire_alert_status_keep_hour,json=expireAlertStatusKeepHour,proto3" json:"expire_alert_status_keep_hour,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}      `json:"-"`
	XXX_unrecognized          []byte        `json:"-"`
	XXX_sizecache             int32         `json:"-"`
}

func (m *GetUserCardInfoResp) Reset()         { *m = GetUserCardInfoResp{} }
func (m *GetUserCardInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserCardInfoResp) ProtoMessage()    {}
func (*GetUserCardInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{21}
}
func (m *GetUserCardInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCardInfoResp.Unmarshal(m, b)
}
func (m *GetUserCardInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCardInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserCardInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCardInfoResp.Merge(dst, src)
}
func (m *GetUserCardInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserCardInfoResp.Size(m)
}
func (m *GetUserCardInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCardInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCardInfoResp proto.InternalMessageInfo

func (m *GetUserCardInfoResp) GetCard() *UserCardInfo {
	if m != nil {
		return m.Card
	}
	return nil
}

func (m *GetUserCardInfoResp) GetExpireAlertStatusKeepHour() uint32 {
	if m != nil {
		return m.ExpireAlertStatusKeepHour
	}
	return 0
}

type GetUserRedemptionInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRedemptionInfoReq) Reset()         { *m = GetUserRedemptionInfoReq{} }
func (m *GetUserRedemptionInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRedemptionInfoReq) ProtoMessage()    {}
func (*GetUserRedemptionInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{22}
}
func (m *GetUserRedemptionInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRedemptionInfoReq.Unmarshal(m, b)
}
func (m *GetUserRedemptionInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRedemptionInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserRedemptionInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRedemptionInfoReq.Merge(dst, src)
}
func (m *GetUserRedemptionInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserRedemptionInfoReq.Size(m)
}
func (m *GetUserRedemptionInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRedemptionInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRedemptionInfoReq proto.InternalMessageInfo

func (m *GetUserRedemptionInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserRedemptionInfoResp struct {
	HasSingleBuyOrderWaitingRedemption bool     `protobuf:"varint,1,opt,name=has_single_buy_order_waiting_redemption,json=hasSingleBuyOrderWaitingRedemption,proto3" json:"has_single_buy_order_waiting_redemption,omitempty"`
	XXX_NoUnkeyedLiteral               struct{} `json:"-"`
	XXX_unrecognized                   []byte   `json:"-"`
	XXX_sizecache                      int32    `json:"-"`
}

func (m *GetUserRedemptionInfoResp) Reset()         { *m = GetUserRedemptionInfoResp{} }
func (m *GetUserRedemptionInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRedemptionInfoResp) ProtoMessage()    {}
func (*GetUserRedemptionInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{23}
}
func (m *GetUserRedemptionInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRedemptionInfoResp.Unmarshal(m, b)
}
func (m *GetUserRedemptionInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRedemptionInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserRedemptionInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRedemptionInfoResp.Merge(dst, src)
}
func (m *GetUserRedemptionInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserRedemptionInfoResp.Size(m)
}
func (m *GetUserRedemptionInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRedemptionInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRedemptionInfoResp proto.InternalMessageInfo

func (m *GetUserRedemptionInfoResp) GetHasSingleBuyOrderWaitingRedemption() bool {
	if m != nil {
		return m.HasSingleBuyOrderWaitingRedemption
	}
	return false
}

type GetUserPackageListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPackageListReq) Reset()         { *m = GetUserPackageListReq{} }
func (m *GetUserPackageListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPackageListReq) ProtoMessage()    {}
func (*GetUserPackageListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{24}
}
func (m *GetUserPackageListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPackageListReq.Unmarshal(m, b)
}
func (m *GetUserPackageListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPackageListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPackageListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPackageListReq.Merge(dst, src)
}
func (m *GetUserPackageListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPackageListReq.Size(m)
}
func (m *GetUserPackageListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPackageListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPackageListReq proto.InternalMessageInfo

func (m *GetUserPackageListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPackageListReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetUserPackageListReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GetUserPackageListResp struct {
	PackageList          []*UserPackage `protobuf:"bytes,1,rep,name=package_list,json=packageList,proto3" json:"package_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserPackageListResp) Reset()         { *m = GetUserPackageListResp{} }
func (m *GetUserPackageListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPackageListResp) ProtoMessage()    {}
func (*GetUserPackageListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{25}
}
func (m *GetUserPackageListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPackageListResp.Unmarshal(m, b)
}
func (m *GetUserPackageListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPackageListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPackageListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPackageListResp.Merge(dst, src)
}
func (m *GetUserPackageListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPackageListResp.Size(m)
}
func (m *GetUserPackageListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPackageListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPackageListResp proto.InternalMessageInfo

func (m *GetUserPackageListResp) GetPackageList() []*UserPackage {
	if m != nil {
		return m.PackageList
	}
	return nil
}

type UserPackage struct {
	PackageId            uint32            `protobuf:"varint,1,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	Name                 string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string            `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	CurrentPriceCent     uint32            `protobuf:"varint,4,opt,name=current_price_cent,json=currentPriceCent,proto3" json:"current_price_cent,omitempty"`
	OriginalPriceCent    uint32            `protobuf:"varint,5,opt,name=original_price_cent,json=originalPriceCent,proto3" json:"original_price_cent,omitempty"`
	DiscountLabel        string            `protobuf:"bytes,6,opt,name=discount_label,json=discountLabel,proto3" json:"discount_label,omitempty"`
	DailyPriceCent       uint32            `protobuf:"varint,7,opt,name=daily_price_cent,json=dailyPriceCent,proto3" json:"daily_price_cent,omitempty"`
	PayPriceCent         uint32            `protobuf:"varint,8,opt,name=pay_price_cent,json=payPriceCent,proto3" json:"pay_price_cent,omitempty"`
	Explanation          string            `protobuf:"bytes,9,opt,name=explanation,proto3" json:"explanation,omitempty"`
	IsAuto               bool              `protobuf:"varint,10,opt,name=is_auto,json=isAuto,proto3" json:"is_auto,omitempty"`
	PayChannelList       []PayChannel      `protobuf:"varint,11,rep,packed,name=pay_channel_list,json=payChannelList,proto3,enum=virtual_image_card.PayChannel" json:"pay_channel_list,omitempty"`
	ProductId            string            `protobuf:"bytes,12,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	ShowCondition        *DisplayCondition `protobuf:"bytes,13,opt,name=show_condition,json=showCondition,proto3" json:"show_condition,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserPackage) Reset()         { *m = UserPackage{} }
func (m *UserPackage) String() string { return proto.CompactTextString(m) }
func (*UserPackage) ProtoMessage()    {}
func (*UserPackage) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{26}
}
func (m *UserPackage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPackage.Unmarshal(m, b)
}
func (m *UserPackage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPackage.Marshal(b, m, deterministic)
}
func (dst *UserPackage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPackage.Merge(dst, src)
}
func (m *UserPackage) XXX_Size() int {
	return xxx_messageInfo_UserPackage.Size(m)
}
func (m *UserPackage) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPackage.DiscardUnknown(m)
}

var xxx_messageInfo_UserPackage proto.InternalMessageInfo

func (m *UserPackage) GetPackageId() uint32 {
	if m != nil {
		return m.PackageId
	}
	return 0
}

func (m *UserPackage) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UserPackage) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *UserPackage) GetCurrentPriceCent() uint32 {
	if m != nil {
		return m.CurrentPriceCent
	}
	return 0
}

func (m *UserPackage) GetOriginalPriceCent() uint32 {
	if m != nil {
		return m.OriginalPriceCent
	}
	return 0
}

func (m *UserPackage) GetDiscountLabel() string {
	if m != nil {
		return m.DiscountLabel
	}
	return ""
}

func (m *UserPackage) GetDailyPriceCent() uint32 {
	if m != nil {
		return m.DailyPriceCent
	}
	return 0
}

func (m *UserPackage) GetPayPriceCent() uint32 {
	if m != nil {
		return m.PayPriceCent
	}
	return 0
}

func (m *UserPackage) GetExplanation() string {
	if m != nil {
		return m.Explanation
	}
	return ""
}

func (m *UserPackage) GetIsAuto() bool {
	if m != nil {
		return m.IsAuto
	}
	return false
}

func (m *UserPackage) GetPayChannelList() []PayChannel {
	if m != nil {
		return m.PayChannelList
	}
	return nil
}

func (m *UserPackage) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *UserPackage) GetShowCondition() *DisplayCondition {
	if m != nil {
		return m.ShowCondition
	}
	return nil
}

type GetPurchaseHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPurchaseHistoryReq) Reset()         { *m = GetPurchaseHistoryReq{} }
func (m *GetPurchaseHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetPurchaseHistoryReq) ProtoMessage()    {}
func (*GetPurchaseHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{27}
}
func (m *GetPurchaseHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPurchaseHistoryReq.Unmarshal(m, b)
}
func (m *GetPurchaseHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPurchaseHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetPurchaseHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPurchaseHistoryReq.Merge(dst, src)
}
func (m *GetPurchaseHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetPurchaseHistoryReq.Size(m)
}
func (m *GetPurchaseHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPurchaseHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPurchaseHistoryReq proto.InternalMessageInfo

func (m *GetPurchaseHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPurchaseHistoryReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetPurchaseHistoryReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetPurchaseHistoryResp struct {
	PurchaseList         []*PurchaseRecord `protobuf:"bytes,1,rep,name=purchase_list,json=purchaseList,proto3" json:"purchase_list,omitempty"`
	HasMore              bool              `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPurchaseHistoryResp) Reset()         { *m = GetPurchaseHistoryResp{} }
func (m *GetPurchaseHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetPurchaseHistoryResp) ProtoMessage()    {}
func (*GetPurchaseHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{28}
}
func (m *GetPurchaseHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPurchaseHistoryResp.Unmarshal(m, b)
}
func (m *GetPurchaseHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPurchaseHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetPurchaseHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPurchaseHistoryResp.Merge(dst, src)
}
func (m *GetPurchaseHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetPurchaseHistoryResp.Size(m)
}
func (m *GetPurchaseHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPurchaseHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPurchaseHistoryResp proto.InternalMessageInfo

func (m *GetPurchaseHistoryResp) GetPurchaseList() []*PurchaseRecord {
	if m != nil {
		return m.PurchaseList
	}
	return nil
}

func (m *GetPurchaseHistoryResp) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

type PurchaseRecord struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Time                 string   `protobuf:"bytes,2,opt,name=time,proto3" json:"time,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Price                string   `protobuf:"bytes,4,opt,name=price,proto3" json:"price,omitempty"`
	HasRefunded          bool     `protobuf:"varint,5,opt,name=has_refunded,json=hasRefunded,proto3" json:"has_refunded,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PurchaseRecord) Reset()         { *m = PurchaseRecord{} }
func (m *PurchaseRecord) String() string { return proto.CompactTextString(m) }
func (*PurchaseRecord) ProtoMessage()    {}
func (*PurchaseRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{29}
}
func (m *PurchaseRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PurchaseRecord.Unmarshal(m, b)
}
func (m *PurchaseRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PurchaseRecord.Marshal(b, m, deterministic)
}
func (dst *PurchaseRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PurchaseRecord.Merge(dst, src)
}
func (m *PurchaseRecord) XXX_Size() int {
	return xxx_messageInfo_PurchaseRecord.Size(m)
}
func (m *PurchaseRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_PurchaseRecord.DiscardUnknown(m)
}

var xxx_messageInfo_PurchaseRecord proto.InternalMessageInfo

func (m *PurchaseRecord) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PurchaseRecord) GetTime() string {
	if m != nil {
		return m.Time
	}
	return ""
}

func (m *PurchaseRecord) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *PurchaseRecord) GetPrice() string {
	if m != nil {
		return m.Price
	}
	return ""
}

func (m *PurchaseRecord) GetHasRefunded() bool {
	if m != nil {
		return m.HasRefunded
	}
	return false
}

// 套餐
type Package struct {
	Id                   uint32      `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ProductId            string      `protobuf:"bytes,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	Name                 string      `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string      `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	Days                 uint32      `protobuf:"varint,5,opt,name=days,proto3" json:"days,omitempty"`
	OriginalPriceCent    uint32      `protobuf:"varint,6,opt,name=original_price_cent,json=originalPriceCent,proto3" json:"original_price_cent,omitempty"`
	PriceCent            uint32      `protobuf:"varint,7,opt,name=price_cent,json=priceCent,proto3" json:"price_cent,omitempty"`
	IsEnabled            bool        `protobuf:"varint,8,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	Operator             string      `protobuf:"bytes,9,opt,name=operator,proto3" json:"operator,omitempty"`
	MarketId             uint32      `protobuf:"varint,10,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	GroupId              uint32      `protobuf:"varint,11,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	UpdateTs             uint64      `protobuf:"varint,12,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	PackageType          PackageType `protobuf:"varint,13,opt,name=package_type,json=packageType,proto3,enum=virtual_image_card.PackageType" json:"package_type,omitempty"`
	DiscountPriceCent    uint32      `protobuf:"varint,14,opt,name=discount_price_cent,json=discountPriceCent,proto3" json:"discount_price_cent,omitempty"`
	DiscountLabel        string      `protobuf:"bytes,15,opt,name=discount_label,json=discountLabel,proto3" json:"discount_label,omitempty"`
	DiscountDesc         string      `protobuf:"bytes,16,opt,name=discount_desc,json=discountDesc,proto3" json:"discount_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *Package) Reset()         { *m = Package{} }
func (m *Package) String() string { return proto.CompactTextString(m) }
func (*Package) ProtoMessage()    {}
func (*Package) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{30}
}
func (m *Package) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Package.Unmarshal(m, b)
}
func (m *Package) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Package.Marshal(b, m, deterministic)
}
func (dst *Package) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Package.Merge(dst, src)
}
func (m *Package) XXX_Size() int {
	return xxx_messageInfo_Package.Size(m)
}
func (m *Package) XXX_DiscardUnknown() {
	xxx_messageInfo_Package.DiscardUnknown(m)
}

var xxx_messageInfo_Package proto.InternalMessageInfo

func (m *Package) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Package) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *Package) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Package) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *Package) GetDays() uint32 {
	if m != nil {
		return m.Days
	}
	return 0
}

func (m *Package) GetOriginalPriceCent() uint32 {
	if m != nil {
		return m.OriginalPriceCent
	}
	return 0
}

func (m *Package) GetPriceCent() uint32 {
	if m != nil {
		return m.PriceCent
	}
	return 0
}

func (m *Package) GetIsEnabled() bool {
	if m != nil {
		return m.IsEnabled
	}
	return false
}

func (m *Package) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *Package) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *Package) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *Package) GetUpdateTs() uint64 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *Package) GetPackageType() PackageType {
	if m != nil {
		return m.PackageType
	}
	return PackageType_PACKAGE_TYPE_UNSPECIFIED
}

func (m *Package) GetDiscountPriceCent() uint32 {
	if m != nil {
		return m.DiscountPriceCent
	}
	return 0
}

func (m *Package) GetDiscountLabel() string {
	if m != nil {
		return m.DiscountLabel
	}
	return ""
}

func (m *Package) GetDiscountDesc() string {
	if m != nil {
		return m.DiscountDesc
	}
	return ""
}

// 增加套餐配置
type AddPackageReq struct {
	PackageInfo          *Package `protobuf:"bytes,1,opt,name=package_info,json=packageInfo,proto3" json:"package_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPackageReq) Reset()         { *m = AddPackageReq{} }
func (m *AddPackageReq) String() string { return proto.CompactTextString(m) }
func (*AddPackageReq) ProtoMessage()    {}
func (*AddPackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{31}
}
func (m *AddPackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPackageReq.Unmarshal(m, b)
}
func (m *AddPackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPackageReq.Marshal(b, m, deterministic)
}
func (dst *AddPackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPackageReq.Merge(dst, src)
}
func (m *AddPackageReq) XXX_Size() int {
	return xxx_messageInfo_AddPackageReq.Size(m)
}
func (m *AddPackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPackageReq proto.InternalMessageInfo

func (m *AddPackageReq) GetPackageInfo() *Package {
	if m != nil {
		return m.PackageInfo
	}
	return nil
}

type AddPackageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPackageResp) Reset()         { *m = AddPackageResp{} }
func (m *AddPackageResp) String() string { return proto.CompactTextString(m) }
func (*AddPackageResp) ProtoMessage()    {}
func (*AddPackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{32}
}
func (m *AddPackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPackageResp.Unmarshal(m, b)
}
func (m *AddPackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPackageResp.Marshal(b, m, deterministic)
}
func (dst *AddPackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPackageResp.Merge(dst, src)
}
func (m *AddPackageResp) XXX_Size() int {
	return xxx_messageInfo_AddPackageResp.Size(m)
}
func (m *AddPackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPackageResp proto.InternalMessageInfo

// 更新套餐状态
type UpdatePackageStatusReq struct {
	IsEnabled            bool     `protobuf:"varint,1,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	IsGroup              bool     `protobuf:"varint,3,opt,name=is_group,json=isGroup,proto3" json:"is_group,omitempty"`
	Operator             string   `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePackageStatusReq) Reset()         { *m = UpdatePackageStatusReq{} }
func (m *UpdatePackageStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePackageStatusReq) ProtoMessage()    {}
func (*UpdatePackageStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{33}
}
func (m *UpdatePackageStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePackageStatusReq.Unmarshal(m, b)
}
func (m *UpdatePackageStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePackageStatusReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePackageStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePackageStatusReq.Merge(dst, src)
}
func (m *UpdatePackageStatusReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePackageStatusReq.Size(m)
}
func (m *UpdatePackageStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePackageStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePackageStatusReq proto.InternalMessageInfo

func (m *UpdatePackageStatusReq) GetIsEnabled() bool {
	if m != nil {
		return m.IsEnabled
	}
	return false
}

func (m *UpdatePackageStatusReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdatePackageStatusReq) GetIsGroup() bool {
	if m != nil {
		return m.IsGroup
	}
	return false
}

func (m *UpdatePackageStatusReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type UpdatePackageStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePackageStatusResp) Reset()         { *m = UpdatePackageStatusResp{} }
func (m *UpdatePackageStatusResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePackageStatusResp) ProtoMessage()    {}
func (*UpdatePackageStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{34}
}
func (m *UpdatePackageStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePackageStatusResp.Unmarshal(m, b)
}
func (m *UpdatePackageStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePackageStatusResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePackageStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePackageStatusResp.Merge(dst, src)
}
func (m *UpdatePackageStatusResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePackageStatusResp.Size(m)
}
func (m *UpdatePackageStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePackageStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePackageStatusResp proto.InternalMessageInfo

// 获取套餐列表
type GetPackageListByStatusReq struct {
	IsEnabled            bool        `protobuf:"varint,1,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	PackageType          PackageType `protobuf:"varint,2,opt,name=package_type,json=packageType,proto3,enum=virtual_image_card.PackageType" json:"package_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetPackageListByStatusReq) Reset()         { *m = GetPackageListByStatusReq{} }
func (m *GetPackageListByStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetPackageListByStatusReq) ProtoMessage()    {}
func (*GetPackageListByStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{35}
}
func (m *GetPackageListByStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPackageListByStatusReq.Unmarshal(m, b)
}
func (m *GetPackageListByStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPackageListByStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetPackageListByStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPackageListByStatusReq.Merge(dst, src)
}
func (m *GetPackageListByStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetPackageListByStatusReq.Size(m)
}
func (m *GetPackageListByStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPackageListByStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPackageListByStatusReq proto.InternalMessageInfo

func (m *GetPackageListByStatusReq) GetIsEnabled() bool {
	if m != nil {
		return m.IsEnabled
	}
	return false
}

func (m *GetPackageListByStatusReq) GetPackageType() PackageType {
	if m != nil {
		return m.PackageType
	}
	return PackageType_PACKAGE_TYPE_UNSPECIFIED
}

type MixPackage struct {
	IsGroup              bool          `protobuf:"varint,1,opt,name=is_group,json=isGroup,proto3" json:"is_group,omitempty"`
	PackageInfo          *Package      `protobuf:"bytes,2,opt,name=package_info,json=packageInfo,proto3" json:"package_info,omitempty"`
	GroupPackage         *GroupPackage `protobuf:"bytes,3,opt,name=group_package,json=groupPackage,proto3" json:"group_package,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MixPackage) Reset()         { *m = MixPackage{} }
func (m *MixPackage) String() string { return proto.CompactTextString(m) }
func (*MixPackage) ProtoMessage()    {}
func (*MixPackage) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{36}
}
func (m *MixPackage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MixPackage.Unmarshal(m, b)
}
func (m *MixPackage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MixPackage.Marshal(b, m, deterministic)
}
func (dst *MixPackage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MixPackage.Merge(dst, src)
}
func (m *MixPackage) XXX_Size() int {
	return xxx_messageInfo_MixPackage.Size(m)
}
func (m *MixPackage) XXX_DiscardUnknown() {
	xxx_messageInfo_MixPackage.DiscardUnknown(m)
}

var xxx_messageInfo_MixPackage proto.InternalMessageInfo

func (m *MixPackage) GetIsGroup() bool {
	if m != nil {
		return m.IsGroup
	}
	return false
}

func (m *MixPackage) GetPackageInfo() *Package {
	if m != nil {
		return m.PackageInfo
	}
	return nil
}

func (m *MixPackage) GetGroupPackage() *GroupPackage {
	if m != nil {
		return m.GroupPackage
	}
	return nil
}

type GetPackageListByStatusResp struct {
	PackageList          []*MixPackage `protobuf:"bytes,1,rep,name=package_list,json=packageList,proto3" json:"package_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetPackageListByStatusResp) Reset()         { *m = GetPackageListByStatusResp{} }
func (m *GetPackageListByStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetPackageListByStatusResp) ProtoMessage()    {}
func (*GetPackageListByStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{37}
}
func (m *GetPackageListByStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPackageListByStatusResp.Unmarshal(m, b)
}
func (m *GetPackageListByStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPackageListByStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetPackageListByStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPackageListByStatusResp.Merge(dst, src)
}
func (m *GetPackageListByStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetPackageListByStatusResp.Size(m)
}
func (m *GetPackageListByStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPackageListByStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPackageListByStatusResp proto.InternalMessageInfo

func (m *GetPackageListByStatusResp) GetPackageList() []*MixPackage {
	if m != nil {
		return m.PackageList
	}
	return nil
}

// 展示条件
type DisplayCondition struct {
	CondType             uint32       `protobuf:"varint,1,opt,name=cond_type,json=condType,proto3" json:"cond_type,omitempty"`
	CardStatus           []CardStatus `protobuf:"varint,2,rep,packed,name=card_status,json=cardStatus,proto3,enum=virtual_image_card.CardStatus" json:"card_status,omitempty"`
	UserLv               uint32       `protobuf:"varint,3,opt,name=user_lv,json=userLv,proto3" json:"user_lv,omitempty"`
	RichLv               uint32       `protobuf:"varint,4,opt,name=rich_lv,json=richLv,proto3" json:"rich_lv,omitempty"`
	NobilityLv           uint32       `protobuf:"varint,5,opt,name=nobility_lv,json=nobilityLv,proto3" json:"nobility_lv,omitempty"`
	PeopleGroupList      []string     `protobuf:"bytes,6,rep,name=people_group_list,json=peopleGroupList,proto3" json:"people_group_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DisplayCondition) Reset()         { *m = DisplayCondition{} }
func (m *DisplayCondition) String() string { return proto.CompactTextString(m) }
func (*DisplayCondition) ProtoMessage()    {}
func (*DisplayCondition) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{38}
}
func (m *DisplayCondition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisplayCondition.Unmarshal(m, b)
}
func (m *DisplayCondition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisplayCondition.Marshal(b, m, deterministic)
}
func (dst *DisplayCondition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisplayCondition.Merge(dst, src)
}
func (m *DisplayCondition) XXX_Size() int {
	return xxx_messageInfo_DisplayCondition.Size(m)
}
func (m *DisplayCondition) XXX_DiscardUnknown() {
	xxx_messageInfo_DisplayCondition.DiscardUnknown(m)
}

var xxx_messageInfo_DisplayCondition proto.InternalMessageInfo

func (m *DisplayCondition) GetCondType() uint32 {
	if m != nil {
		return m.CondType
	}
	return 0
}

func (m *DisplayCondition) GetCardStatus() []CardStatus {
	if m != nil {
		return m.CardStatus
	}
	return nil
}

func (m *DisplayCondition) GetUserLv() uint32 {
	if m != nil {
		return m.UserLv
	}
	return 0
}

func (m *DisplayCondition) GetRichLv() uint32 {
	if m != nil {
		return m.RichLv
	}
	return 0
}

func (m *DisplayCondition) GetNobilityLv() uint32 {
	if m != nil {
		return m.NobilityLv
	}
	return 0
}

func (m *DisplayCondition) GetPeopleGroupList() []string {
	if m != nil {
		return m.PeopleGroupList
	}
	return nil
}

// 在售架的套餐信息
type SalePackage struct {
	SaleId               uint32            `protobuf:"varint,1,opt,name=sale_id,json=saleId,proto3" json:"sale_id,omitempty"`
	PackageInfo          *Package          `protobuf:"bytes,2,opt,name=package_info,json=packageInfo,proto3" json:"package_info,omitempty"`
	BeginTs              uint64            `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint64            `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Condition            *DisplayCondition `protobuf:"bytes,5,opt,name=condition,proto3" json:"condition,omitempty"`
	Weight               uint32            `protobuf:"varint,6,opt,name=weight,proto3" json:"weight,omitempty"`
	SaleStatus           SalePackageStatus `protobuf:"varint,7,opt,name=sale_status,json=saleStatus,proto3,enum=virtual_image_card.SalePackageStatus" json:"sale_status,omitempty"`
	Label                string            `protobuf:"bytes,8,opt,name=label,proto3" json:"label,omitempty"`
	Operator             string            `protobuf:"bytes,9,opt,name=operator,proto3" json:"operator,omitempty"`
	UpdateTs             uint64            `protobuf:"varint,10,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	Remarks              string            `protobuf:"bytes,11,opt,name=remarks,proto3" json:"remarks,omitempty"`
	GroupId              uint32            `protobuf:"varint,12,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	MarketId             uint32            `protobuf:"varint,13,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SalePackage) Reset()         { *m = SalePackage{} }
func (m *SalePackage) String() string { return proto.CompactTextString(m) }
func (*SalePackage) ProtoMessage()    {}
func (*SalePackage) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{39}
}
func (m *SalePackage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalePackage.Unmarshal(m, b)
}
func (m *SalePackage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalePackage.Marshal(b, m, deterministic)
}
func (dst *SalePackage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalePackage.Merge(dst, src)
}
func (m *SalePackage) XXX_Size() int {
	return xxx_messageInfo_SalePackage.Size(m)
}
func (m *SalePackage) XXX_DiscardUnknown() {
	xxx_messageInfo_SalePackage.DiscardUnknown(m)
}

var xxx_messageInfo_SalePackage proto.InternalMessageInfo

func (m *SalePackage) GetSaleId() uint32 {
	if m != nil {
		return m.SaleId
	}
	return 0
}

func (m *SalePackage) GetPackageInfo() *Package {
	if m != nil {
		return m.PackageInfo
	}
	return nil
}

func (m *SalePackage) GetBeginTs() uint64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *SalePackage) GetEndTs() uint64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *SalePackage) GetCondition() *DisplayCondition {
	if m != nil {
		return m.Condition
	}
	return nil
}

func (m *SalePackage) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *SalePackage) GetSaleStatus() SalePackageStatus {
	if m != nil {
		return m.SaleStatus
	}
	return SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_UNSPECIFIED
}

func (m *SalePackage) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

func (m *SalePackage) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *SalePackage) GetUpdateTs() uint64 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *SalePackage) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

func (m *SalePackage) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *SalePackage) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

// 马甲包、系统信息
type MarketInfo struct {
	MarketId             uint32   `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ProductId            string   `protobuf:"bytes,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	PackageId            uint32   `protobuf:"varint,3,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	UpdateTs             uint64   `protobuf:"varint,4,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarketInfo) Reset()         { *m = MarketInfo{} }
func (m *MarketInfo) String() string { return proto.CompactTextString(m) }
func (*MarketInfo) ProtoMessage()    {}
func (*MarketInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{40}
}
func (m *MarketInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarketInfo.Unmarshal(m, b)
}
func (m *MarketInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarketInfo.Marshal(b, m, deterministic)
}
func (dst *MarketInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarketInfo.Merge(dst, src)
}
func (m *MarketInfo) XXX_Size() int {
	return xxx_messageInfo_MarketInfo.Size(m)
}
func (m *MarketInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MarketInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MarketInfo proto.InternalMessageInfo

func (m *MarketInfo) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *MarketInfo) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *MarketInfo) GetPackageId() uint32 {
	if m != nil {
		return m.PackageId
	}
	return 0
}

func (m *MarketInfo) GetUpdateTs() uint64 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

// 分组套餐配置
type GroupPackage struct {
	GroupId              uint32        `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	PackageType          PackageType   `protobuf:"varint,2,opt,name=package_type,json=packageType,proto3,enum=virtual_image_card.PackageType" json:"package_type,omitempty"`
	Name                 string        `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string        `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	OriginalPriceCent    uint32        `protobuf:"varint,5,opt,name=original_price_cent,json=originalPriceCent,proto3" json:"original_price_cent,omitempty"`
	PriceCent            uint32        `protobuf:"varint,6,opt,name=price_cent,json=priceCent,proto3" json:"price_cent,omitempty"`
	Days                 int32         `protobuf:"varint,7,opt,name=days,proto3" json:"days,omitempty"`
	MarketList           []*MarketInfo `protobuf:"bytes,8,rep,name=market_list,json=marketList,proto3" json:"market_list,omitempty"`
	IsEnabled            bool          `protobuf:"varint,9,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	Operator             string        `protobuf:"bytes,10,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GroupPackage) Reset()         { *m = GroupPackage{} }
func (m *GroupPackage) String() string { return proto.CompactTextString(m) }
func (*GroupPackage) ProtoMessage()    {}
func (*GroupPackage) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{41}
}
func (m *GroupPackage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupPackage.Unmarshal(m, b)
}
func (m *GroupPackage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupPackage.Marshal(b, m, deterministic)
}
func (dst *GroupPackage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupPackage.Merge(dst, src)
}
func (m *GroupPackage) XXX_Size() int {
	return xxx_messageInfo_GroupPackage.Size(m)
}
func (m *GroupPackage) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupPackage.DiscardUnknown(m)
}

var xxx_messageInfo_GroupPackage proto.InternalMessageInfo

func (m *GroupPackage) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupPackage) GetPackageType() PackageType {
	if m != nil {
		return m.PackageType
	}
	return PackageType_PACKAGE_TYPE_UNSPECIFIED
}

func (m *GroupPackage) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GroupPackage) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *GroupPackage) GetOriginalPriceCent() uint32 {
	if m != nil {
		return m.OriginalPriceCent
	}
	return 0
}

func (m *GroupPackage) GetPriceCent() uint32 {
	if m != nil {
		return m.PriceCent
	}
	return 0
}

func (m *GroupPackage) GetDays() int32 {
	if m != nil {
		return m.Days
	}
	return 0
}

func (m *GroupPackage) GetMarketList() []*MarketInfo {
	if m != nil {
		return m.MarketList
	}
	return nil
}

func (m *GroupPackage) GetIsEnabled() bool {
	if m != nil {
		return m.IsEnabled
	}
	return false
}

func (m *GroupPackage) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

// 批量添加分组套餐配置
type BatchAddGroupPackageReq struct {
	GroupList            []*GroupPackage `protobuf:"bytes,1,rep,name=group_list,json=groupList,proto3" json:"group_list,omitempty"`
	IsCheck              bool            `protobuf:"varint,2,opt,name=is_check,json=isCheck,proto3" json:"is_check,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchAddGroupPackageReq) Reset()         { *m = BatchAddGroupPackageReq{} }
func (m *BatchAddGroupPackageReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddGroupPackageReq) ProtoMessage()    {}
func (*BatchAddGroupPackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{42}
}
func (m *BatchAddGroupPackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddGroupPackageReq.Unmarshal(m, b)
}
func (m *BatchAddGroupPackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddGroupPackageReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddGroupPackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddGroupPackageReq.Merge(dst, src)
}
func (m *BatchAddGroupPackageReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddGroupPackageReq.Size(m)
}
func (m *BatchAddGroupPackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddGroupPackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddGroupPackageReq proto.InternalMessageInfo

func (m *BatchAddGroupPackageReq) GetGroupList() []*GroupPackage {
	if m != nil {
		return m.GroupList
	}
	return nil
}

func (m *BatchAddGroupPackageReq) GetIsCheck() bool {
	if m != nil {
		return m.IsCheck
	}
	return false
}

// 批量添加分组套餐配置
type BatchAddGroupPackageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddGroupPackageResp) Reset()         { *m = BatchAddGroupPackageResp{} }
func (m *BatchAddGroupPackageResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddGroupPackageResp) ProtoMessage()    {}
func (*BatchAddGroupPackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{43}
}
func (m *BatchAddGroupPackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddGroupPackageResp.Unmarshal(m, b)
}
func (m *BatchAddGroupPackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddGroupPackageResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddGroupPackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddGroupPackageResp.Merge(dst, src)
}
func (m *BatchAddGroupPackageResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddGroupPackageResp.Size(m)
}
func (m *BatchAddGroupPackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddGroupPackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddGroupPackageResp proto.InternalMessageInfo

// 新增/编辑分组
type EditGroupPackageReq struct {
	GroupId              uint32        `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	MarketList           []*MarketInfo `protobuf:"bytes,2,rep,name=market_list,json=marketList,proto3" json:"market_list,omitempty"`
	Operator             string        `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *EditGroupPackageReq) Reset()         { *m = EditGroupPackageReq{} }
func (m *EditGroupPackageReq) String() string { return proto.CompactTextString(m) }
func (*EditGroupPackageReq) ProtoMessage()    {}
func (*EditGroupPackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{44}
}
func (m *EditGroupPackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditGroupPackageReq.Unmarshal(m, b)
}
func (m *EditGroupPackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditGroupPackageReq.Marshal(b, m, deterministic)
}
func (dst *EditGroupPackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditGroupPackageReq.Merge(dst, src)
}
func (m *EditGroupPackageReq) XXX_Size() int {
	return xxx_messageInfo_EditGroupPackageReq.Size(m)
}
func (m *EditGroupPackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EditGroupPackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_EditGroupPackageReq proto.InternalMessageInfo

func (m *EditGroupPackageReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *EditGroupPackageReq) GetMarketList() []*MarketInfo {
	if m != nil {
		return m.MarketList
	}
	return nil
}

func (m *EditGroupPackageReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

// 新增/编辑分组
type EditGroupPackageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditGroupPackageResp) Reset()         { *m = EditGroupPackageResp{} }
func (m *EditGroupPackageResp) String() string { return proto.CompactTextString(m) }
func (*EditGroupPackageResp) ProtoMessage()    {}
func (*EditGroupPackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{45}
}
func (m *EditGroupPackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditGroupPackageResp.Unmarshal(m, b)
}
func (m *EditGroupPackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditGroupPackageResp.Marshal(b, m, deterministic)
}
func (dst *EditGroupPackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditGroupPackageResp.Merge(dst, src)
}
func (m *EditGroupPackageResp) XXX_Size() int {
	return xxx_messageInfo_EditGroupPackageResp.Size(m)
}
func (m *EditGroupPackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EditGroupPackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_EditGroupPackageResp proto.InternalMessageInfo

// 修改套餐内容
type ModifyPackageReq struct {
	PackageInfo          *Package `protobuf:"bytes,1,opt,name=package_info,json=packageInfo,proto3" json:"package_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyPackageReq) Reset()         { *m = ModifyPackageReq{} }
func (m *ModifyPackageReq) String() string { return proto.CompactTextString(m) }
func (*ModifyPackageReq) ProtoMessage()    {}
func (*ModifyPackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{46}
}
func (m *ModifyPackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyPackageReq.Unmarshal(m, b)
}
func (m *ModifyPackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyPackageReq.Marshal(b, m, deterministic)
}
func (dst *ModifyPackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyPackageReq.Merge(dst, src)
}
func (m *ModifyPackageReq) XXX_Size() int {
	return xxx_messageInfo_ModifyPackageReq.Size(m)
}
func (m *ModifyPackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyPackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyPackageReq proto.InternalMessageInfo

func (m *ModifyPackageReq) GetPackageInfo() *Package {
	if m != nil {
		return m.PackageInfo
	}
	return nil
}

type ModifyPackageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyPackageResp) Reset()         { *m = ModifyPackageResp{} }
func (m *ModifyPackageResp) String() string { return proto.CompactTextString(m) }
func (*ModifyPackageResp) ProtoMessage()    {}
func (*ModifyPackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{47}
}
func (m *ModifyPackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyPackageResp.Unmarshal(m, b)
}
func (m *ModifyPackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyPackageResp.Marshal(b, m, deterministic)
}
func (dst *ModifyPackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyPackageResp.Merge(dst, src)
}
func (m *ModifyPackageResp) XXX_Size() int {
	return xxx_messageInfo_ModifyPackageResp.Size(m)
}
func (m *ModifyPackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyPackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyPackageResp proto.InternalMessageInfo

// 修改分组套餐内容
type ModifyGroupPackageReq struct {
	GroupPackage         *GroupPackage `protobuf:"bytes,1,opt,name=group_package,json=groupPackage,proto3" json:"group_package,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ModifyGroupPackageReq) Reset()         { *m = ModifyGroupPackageReq{} }
func (m *ModifyGroupPackageReq) String() string { return proto.CompactTextString(m) }
func (*ModifyGroupPackageReq) ProtoMessage()    {}
func (*ModifyGroupPackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{48}
}
func (m *ModifyGroupPackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyGroupPackageReq.Unmarshal(m, b)
}
func (m *ModifyGroupPackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyGroupPackageReq.Marshal(b, m, deterministic)
}
func (dst *ModifyGroupPackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyGroupPackageReq.Merge(dst, src)
}
func (m *ModifyGroupPackageReq) XXX_Size() int {
	return xxx_messageInfo_ModifyGroupPackageReq.Size(m)
}
func (m *ModifyGroupPackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyGroupPackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyGroupPackageReq proto.InternalMessageInfo

func (m *ModifyGroupPackageReq) GetGroupPackage() *GroupPackage {
	if m != nil {
		return m.GroupPackage
	}
	return nil
}

// 修改分组套餐内容
type ModifyGroupPackageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyGroupPackageResp) Reset()         { *m = ModifyGroupPackageResp{} }
func (m *ModifyGroupPackageResp) String() string { return proto.CompactTextString(m) }
func (*ModifyGroupPackageResp) ProtoMessage()    {}
func (*ModifyGroupPackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{49}
}
func (m *ModifyGroupPackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyGroupPackageResp.Unmarshal(m, b)
}
func (m *ModifyGroupPackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyGroupPackageResp.Marshal(b, m, deterministic)
}
func (dst *ModifyGroupPackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyGroupPackageResp.Merge(dst, src)
}
func (m *ModifyGroupPackageResp) XXX_Size() int {
	return xxx_messageInfo_ModifyGroupPackageResp.Size(m)
}
func (m *ModifyGroupPackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyGroupPackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyGroupPackageResp proto.InternalMessageInfo

// 增加在售架套餐配置
type AddSalePackageReq struct {
	Info                 *SalePackage `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	IsCheck              bool         `protobuf:"varint,2,opt,name=is_check,json=isCheck,proto3" json:"is_check,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddSalePackageReq) Reset()         { *m = AddSalePackageReq{} }
func (m *AddSalePackageReq) String() string { return proto.CompactTextString(m) }
func (*AddSalePackageReq) ProtoMessage()    {}
func (*AddSalePackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{50}
}
func (m *AddSalePackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSalePackageReq.Unmarshal(m, b)
}
func (m *AddSalePackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSalePackageReq.Marshal(b, m, deterministic)
}
func (dst *AddSalePackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSalePackageReq.Merge(dst, src)
}
func (m *AddSalePackageReq) XXX_Size() int {
	return xxx_messageInfo_AddSalePackageReq.Size(m)
}
func (m *AddSalePackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSalePackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddSalePackageReq proto.InternalMessageInfo

func (m *AddSalePackageReq) GetInfo() *SalePackage {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *AddSalePackageReq) GetIsCheck() bool {
	if m != nil {
		return m.IsCheck
	}
	return false
}

// 增加在售架套餐配置
type AddSalePackageResp struct {
	AlreadyOnShelfList   []uint32 `protobuf:"varint,1,rep,packed,name=already_on_shelf_list,json=alreadyOnShelfList,proto3" json:"already_on_shelf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSalePackageResp) Reset()         { *m = AddSalePackageResp{} }
func (m *AddSalePackageResp) String() string { return proto.CompactTextString(m) }
func (*AddSalePackageResp) ProtoMessage()    {}
func (*AddSalePackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{51}
}
func (m *AddSalePackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSalePackageResp.Unmarshal(m, b)
}
func (m *AddSalePackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSalePackageResp.Marshal(b, m, deterministic)
}
func (dst *AddSalePackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSalePackageResp.Merge(dst, src)
}
func (m *AddSalePackageResp) XXX_Size() int {
	return xxx_messageInfo_AddSalePackageResp.Size(m)
}
func (m *AddSalePackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSalePackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddSalePackageResp proto.InternalMessageInfo

func (m *AddSalePackageResp) GetAlreadyOnShelfList() []uint32 {
	if m != nil {
		return m.AlreadyOnShelfList
	}
	return nil
}

// 修改在售架套餐配置
type UpdateSalePackageReq struct {
	Info                 *SalePackage `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateSalePackageReq) Reset()         { *m = UpdateSalePackageReq{} }
func (m *UpdateSalePackageReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSalePackageReq) ProtoMessage()    {}
func (*UpdateSalePackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{52}
}
func (m *UpdateSalePackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSalePackageReq.Unmarshal(m, b)
}
func (m *UpdateSalePackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSalePackageReq.Marshal(b, m, deterministic)
}
func (dst *UpdateSalePackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSalePackageReq.Merge(dst, src)
}
func (m *UpdateSalePackageReq) XXX_Size() int {
	return xxx_messageInfo_UpdateSalePackageReq.Size(m)
}
func (m *UpdateSalePackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSalePackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSalePackageReq proto.InternalMessageInfo

func (m *UpdateSalePackageReq) GetInfo() *SalePackage {
	if m != nil {
		return m.Info
	}
	return nil
}

// 修改在售架套餐配置
type UpdateSalePackageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSalePackageResp) Reset()         { *m = UpdateSalePackageResp{} }
func (m *UpdateSalePackageResp) String() string { return proto.CompactTextString(m) }
func (*UpdateSalePackageResp) ProtoMessage()    {}
func (*UpdateSalePackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{53}
}
func (m *UpdateSalePackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSalePackageResp.Unmarshal(m, b)
}
func (m *UpdateSalePackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSalePackageResp.Marshal(b, m, deterministic)
}
func (dst *UpdateSalePackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSalePackageResp.Merge(dst, src)
}
func (m *UpdateSalePackageResp) XXX_Size() int {
	return xxx_messageInfo_UpdateSalePackageResp.Size(m)
}
func (m *UpdateSalePackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSalePackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSalePackageResp proto.InternalMessageInfo

// 获取在售架套餐列表
type GetSalePackageListByStatusReq struct {
	Status               SalePackageStatus `protobuf:"varint,1,opt,name=status,proto3,enum=virtual_image_card.SalePackageStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetSalePackageListByStatusReq) Reset()         { *m = GetSalePackageListByStatusReq{} }
func (m *GetSalePackageListByStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetSalePackageListByStatusReq) ProtoMessage()    {}
func (*GetSalePackageListByStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{54}
}
func (m *GetSalePackageListByStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSalePackageListByStatusReq.Unmarshal(m, b)
}
func (m *GetSalePackageListByStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSalePackageListByStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetSalePackageListByStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSalePackageListByStatusReq.Merge(dst, src)
}
func (m *GetSalePackageListByStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetSalePackageListByStatusReq.Size(m)
}
func (m *GetSalePackageListByStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSalePackageListByStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSalePackageListByStatusReq proto.InternalMessageInfo

func (m *GetSalePackageListByStatusReq) GetStatus() SalePackageStatus {
	if m != nil {
		return m.Status
	}
	return SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_UNSPECIFIED
}

// 获取在售架套餐列表
type GetSalePackageListByStatusResp struct {
	// 不做分页
	PackageList          []*SalePackage `protobuf:"bytes,1,rep,name=package_list,json=packageList,proto3" json:"package_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetSalePackageListByStatusResp) Reset()         { *m = GetSalePackageListByStatusResp{} }
func (m *GetSalePackageListByStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetSalePackageListByStatusResp) ProtoMessage()    {}
func (*GetSalePackageListByStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{55}
}
func (m *GetSalePackageListByStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSalePackageListByStatusResp.Unmarshal(m, b)
}
func (m *GetSalePackageListByStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSalePackageListByStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetSalePackageListByStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSalePackageListByStatusResp.Merge(dst, src)
}
func (m *GetSalePackageListByStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetSalePackageListByStatusResp.Size(m)
}
func (m *GetSalePackageListByStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSalePackageListByStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSalePackageListByStatusResp proto.InternalMessageInfo

func (m *GetSalePackageListByStatusResp) GetPackageList() []*SalePackage {
	if m != nil {
		return m.PackageList
	}
	return nil
}

// 套餐排序
type SalePackageSortReq struct {
	SaleIdList           []uint32 `protobuf:"varint,1,rep,packed,name=sale_id_list,json=saleIdList,proto3" json:"sale_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalePackageSortReq) Reset()         { *m = SalePackageSortReq{} }
func (m *SalePackageSortReq) String() string { return proto.CompactTextString(m) }
func (*SalePackageSortReq) ProtoMessage()    {}
func (*SalePackageSortReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{56}
}
func (m *SalePackageSortReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalePackageSortReq.Unmarshal(m, b)
}
func (m *SalePackageSortReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalePackageSortReq.Marshal(b, m, deterministic)
}
func (dst *SalePackageSortReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalePackageSortReq.Merge(dst, src)
}
func (m *SalePackageSortReq) XXX_Size() int {
	return xxx_messageInfo_SalePackageSortReq.Size(m)
}
func (m *SalePackageSortReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SalePackageSortReq.DiscardUnknown(m)
}

var xxx_messageInfo_SalePackageSortReq proto.InternalMessageInfo

func (m *SalePackageSortReq) GetSaleIdList() []uint32 {
	if m != nil {
		return m.SaleIdList
	}
	return nil
}

// 套餐排序
type SalePackageSortResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalePackageSortResp) Reset()         { *m = SalePackageSortResp{} }
func (m *SalePackageSortResp) String() string { return proto.CompactTextString(m) }
func (*SalePackageSortResp) ProtoMessage()    {}
func (*SalePackageSortResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{57}
}
func (m *SalePackageSortResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalePackageSortResp.Unmarshal(m, b)
}
func (m *SalePackageSortResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalePackageSortResp.Marshal(b, m, deterministic)
}
func (dst *SalePackageSortResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalePackageSortResp.Merge(dst, src)
}
func (m *SalePackageSortResp) XXX_Size() int {
	return xxx_messageInfo_SalePackageSortResp.Size(m)
}
func (m *SalePackageSortResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SalePackageSortResp.DiscardUnknown(m)
}

var xxx_messageInfo_SalePackageSortResp proto.InternalMessageInfo

type AboutToExpireCfg struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	ExpireAlertTime      uint32   `protobuf:"varint,15,opt,name=expire_alert_time,json=expireAlertTime,proto3" json:"expire_alert_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AboutToExpireCfg) Reset()         { *m = AboutToExpireCfg{} }
func (m *AboutToExpireCfg) String() string { return proto.CompactTextString(m) }
func (*AboutToExpireCfg) ProtoMessage()    {}
func (*AboutToExpireCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{58}
}
func (m *AboutToExpireCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AboutToExpireCfg.Unmarshal(m, b)
}
func (m *AboutToExpireCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AboutToExpireCfg.Marshal(b, m, deterministic)
}
func (dst *AboutToExpireCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AboutToExpireCfg.Merge(dst, src)
}
func (m *AboutToExpireCfg) XXX_Size() int {
	return xxx_messageInfo_AboutToExpireCfg.Size(m)
}
func (m *AboutToExpireCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_AboutToExpireCfg.DiscardUnknown(m)
}

var xxx_messageInfo_AboutToExpireCfg proto.InternalMessageInfo

func (m *AboutToExpireCfg) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *AboutToExpireCfg) GetExpireAlertTime() uint32 {
	if m != nil {
		return m.ExpireAlertTime
	}
	return 0
}

// 获取虚拟形象卡片通用配置 cfg_version 变大时调用
type GetVirtualImageCardCommonCfgRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVirtualImageCardCommonCfgRequest) Reset()         { *m = GetVirtualImageCardCommonCfgRequest{} }
func (m *GetVirtualImageCardCommonCfgRequest) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageCardCommonCfgRequest) ProtoMessage()    {}
func (*GetVirtualImageCardCommonCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{59}
}
func (m *GetVirtualImageCardCommonCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgRequest.Unmarshal(m, b)
}
func (m *GetVirtualImageCardCommonCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCardCommonCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCardCommonCfgRequest.Merge(dst, src)
}
func (m *GetVirtualImageCardCommonCfgRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgRequest.Size(m)
}
func (m *GetVirtualImageCardCommonCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCardCommonCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCardCommonCfgRequest proto.InternalMessageInfo

type GetVirtualImageCardCommonCfgResponse struct {
	WaitToBuyIcon          string              `protobuf:"bytes,1,opt,name=wait_to_buy_icon,json=waitToBuyIcon,proto3" json:"wait_to_buy_icon,omitempty"`
	AlreadyBuyIcon         string              `protobuf:"bytes,2,opt,name=already_buy_icon,json=alreadyBuyIcon,proto3" json:"already_buy_icon,omitempty"`
	AboutToExpireCfgList   []*AboutToExpireCfg `protobuf:"bytes,3,rep,name=about_to_expire_cfg_list,json=aboutToExpireCfgList,proto3" json:"about_to_expire_cfg_list,omitempty"`
	FirstEnterCardStoreUrl string              `protobuf:"bytes,4,opt,name=first_enter_card_store_url,json=firstEnterCardStoreUrl,proto3" json:"first_enter_card_store_url,omitempty"`
	AdText                 string              `protobuf:"bytes,5,opt,name=ad_text,json=adText,proto3" json:"ad_text,omitempty"`
	NDayShowOnce           uint32              `protobuf:"varint,6,opt,name=n_day_show_once,json=nDayShowOnce,proto3" json:"n_day_show_once,omitempty"`
	CfgVersion             uint32              `protobuf:"varint,7,opt,name=cfg_version,json=cfgVersion,proto3" json:"cfg_version,omitempty"`
	WaitToBuyBg            string              `protobuf:"bytes,8,opt,name=wait_to_buy_bg,json=waitToBuyBg,proto3" json:"wait_to_buy_bg,omitempty"`
	AlreadyBuyBg           string              `protobuf:"bytes,9,opt,name=already_buy_bg,json=alreadyBuyBg,proto3" json:"already_buy_bg,omitempty"`
	AboutToExpireBg        string              `protobuf:"bytes,10,opt,name=about_to_expire_bg,json=aboutToExpireBg,proto3" json:"about_to_expire_bg,omitempty"`
	StoreResidentEntryIcon string              `protobuf:"bytes,11,opt,name=store_resident_entry_icon,json=storeResidentEntryIcon,proto3" json:"store_resident_entry_icon,omitempty"`
	StoreTabIconSelected   string              `protobuf:"bytes,12,opt,name=store_tab_icon_selected,json=storeTabIconSelected,proto3" json:"store_tab_icon_selected,omitempty"`
	StoreTabIconUnselected string              `protobuf:"bytes,13,opt,name=store_tab_icon_unselected,json=storeTabIconUnselected,proto3" json:"store_tab_icon_unselected,omitempty"`
	ExpireAlertTime        uint32              `protobuf:"varint,14,opt,name=expire_alert_time,json=expireAlertTime,proto3" json:"expire_alert_time,omitempty"`
	FirstEnterCardStoreMd5 string              `protobuf:"bytes,15,opt,name=first_enter_card_store_md5,json=firstEnterCardStoreMd5,proto3" json:"first_enter_card_store_md5,omitempty"`
	PcWaitToBuyBg          string              `protobuf:"bytes,16,opt,name=pc_wait_to_buy_bg,json=pcWaitToBuyBg,proto3" json:"pc_wait_to_buy_bg,omitempty"`
	PcAlreadyBuyBg         string              `protobuf:"bytes,17,opt,name=pc_already_buy_bg,json=pcAlreadyBuyBg,proto3" json:"pc_already_buy_bg,omitempty"`
	PcAboutToExpireBg      string              `protobuf:"bytes,18,opt,name=pc_about_to_expire_bg,json=pcAboutToExpireBg,proto3" json:"pc_about_to_expire_bg,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}            `json:"-"`
	XXX_unrecognized       []byte              `json:"-"`
	XXX_sizecache          int32               `json:"-"`
}

func (m *GetVirtualImageCardCommonCfgResponse) Reset()         { *m = GetVirtualImageCardCommonCfgResponse{} }
func (m *GetVirtualImageCardCommonCfgResponse) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageCardCommonCfgResponse) ProtoMessage()    {}
func (*GetVirtualImageCardCommonCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{60}
}
func (m *GetVirtualImageCardCommonCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgResponse.Unmarshal(m, b)
}
func (m *GetVirtualImageCardCommonCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCardCommonCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCardCommonCfgResponse.Merge(dst, src)
}
func (m *GetVirtualImageCardCommonCfgResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgResponse.Size(m)
}
func (m *GetVirtualImageCardCommonCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCardCommonCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCardCommonCfgResponse proto.InternalMessageInfo

func (m *GetVirtualImageCardCommonCfgResponse) GetWaitToBuyIcon() string {
	if m != nil {
		return m.WaitToBuyIcon
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetAlreadyBuyIcon() string {
	if m != nil {
		return m.AlreadyBuyIcon
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetAboutToExpireCfgList() []*AboutToExpireCfg {
	if m != nil {
		return m.AboutToExpireCfgList
	}
	return nil
}

func (m *GetVirtualImageCardCommonCfgResponse) GetFirstEnterCardStoreUrl() string {
	if m != nil {
		return m.FirstEnterCardStoreUrl
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetAdText() string {
	if m != nil {
		return m.AdText
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetNDayShowOnce() uint32 {
	if m != nil {
		return m.NDayShowOnce
	}
	return 0
}

func (m *GetVirtualImageCardCommonCfgResponse) GetCfgVersion() uint32 {
	if m != nil {
		return m.CfgVersion
	}
	return 0
}

func (m *GetVirtualImageCardCommonCfgResponse) GetWaitToBuyBg() string {
	if m != nil {
		return m.WaitToBuyBg
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetAlreadyBuyBg() string {
	if m != nil {
		return m.AlreadyBuyBg
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetAboutToExpireBg() string {
	if m != nil {
		return m.AboutToExpireBg
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetStoreResidentEntryIcon() string {
	if m != nil {
		return m.StoreResidentEntryIcon
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetStoreTabIconSelected() string {
	if m != nil {
		return m.StoreTabIconSelected
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetStoreTabIconUnselected() string {
	if m != nil {
		return m.StoreTabIconUnselected
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetExpireAlertTime() uint32 {
	if m != nil {
		return m.ExpireAlertTime
	}
	return 0
}

func (m *GetVirtualImageCardCommonCfgResponse) GetFirstEnterCardStoreMd5() string {
	if m != nil {
		return m.FirstEnterCardStoreMd5
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetPcWaitToBuyBg() string {
	if m != nil {
		return m.PcWaitToBuyBg
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetPcAlreadyBuyBg() string {
	if m != nil {
		return m.PcAlreadyBuyBg
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetPcAboutToExpireBg() string {
	if m != nil {
		return m.PcAboutToExpireBg
	}
	return ""
}

// 获取虚拟形象卡片入口状态
type GetVirtualImageCardEntryStatusRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVirtualImageCardEntryStatusRequest) Reset()         { *m = GetVirtualImageCardEntryStatusRequest{} }
func (m *GetVirtualImageCardEntryStatusRequest) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageCardEntryStatusRequest) ProtoMessage()    {}
func (*GetVirtualImageCardEntryStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{61}
}
func (m *GetVirtualImageCardEntryStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusRequest.Unmarshal(m, b)
}
func (m *GetVirtualImageCardEntryStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCardEntryStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCardEntryStatusRequest.Merge(dst, src)
}
func (m *GetVirtualImageCardEntryStatusRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusRequest.Size(m)
}
func (m *GetVirtualImageCardEntryStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCardEntryStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCardEntryStatusRequest proto.InternalMessageInfo

func (m *GetVirtualImageCardEntryStatusRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 虚拟形象卡片入口状态
type GetVirtualImageCardEntryStatusResponse struct {
	ExpireTime           uint32   `protobuf:"varint,1,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	LowPriceText         string   `protobuf:"bytes,2,opt,name=low_price_text,json=lowPriceText,proto3" json:"low_price_text,omitempty"`
	AdIdx                uint32   `protobuf:"varint,3,opt,name=ad_idx,json=adIdx,proto3" json:"ad_idx,omitempty"`
	CfgVersion           uint32   `protobuf:"varint,4,opt,name=cfg_version,json=cfgVersion,proto3" json:"cfg_version,omitempty"`
	Switch               bool     `protobuf:"varint,5,opt,name=switch,proto3" json:"switch,omitempty"`
	TrialEffectTs        int64    `protobuf:"varint,6,opt,name=trial_effect_ts,json=trialEffectTs,proto3" json:"trial_effect_ts,omitempty"`
	TrialExpireTs        int64    `protobuf:"varint,7,opt,name=trial_expire_ts,json=trialExpireTs,proto3" json:"trial_expire_ts,omitempty"`
	BuyEffectTs          int64    `protobuf:"varint,8,opt,name=buy_effect_ts,json=buyEffectTs,proto3" json:"buy_effect_ts,omitempty"`
	BuyExpireTs          int64    `protobuf:"varint,9,opt,name=buy_expire_ts,json=buyExpireTs,proto3" json:"buy_expire_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVirtualImageCardEntryStatusResponse) Reset() {
	*m = GetVirtualImageCardEntryStatusResponse{}
}
func (m *GetVirtualImageCardEntryStatusResponse) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageCardEntryStatusResponse) ProtoMessage()    {}
func (*GetVirtualImageCardEntryStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{62}
}
func (m *GetVirtualImageCardEntryStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusResponse.Unmarshal(m, b)
}
func (m *GetVirtualImageCardEntryStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCardEntryStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCardEntryStatusResponse.Merge(dst, src)
}
func (m *GetVirtualImageCardEntryStatusResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusResponse.Size(m)
}
func (m *GetVirtualImageCardEntryStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCardEntryStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCardEntryStatusResponse proto.InternalMessageInfo

func (m *GetVirtualImageCardEntryStatusResponse) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *GetVirtualImageCardEntryStatusResponse) GetLowPriceText() string {
	if m != nil {
		return m.LowPriceText
	}
	return ""
}

func (m *GetVirtualImageCardEntryStatusResponse) GetAdIdx() uint32 {
	if m != nil {
		return m.AdIdx
	}
	return 0
}

func (m *GetVirtualImageCardEntryStatusResponse) GetCfgVersion() uint32 {
	if m != nil {
		return m.CfgVersion
	}
	return 0
}

func (m *GetVirtualImageCardEntryStatusResponse) GetSwitch() bool {
	if m != nil {
		return m.Switch
	}
	return false
}

func (m *GetVirtualImageCardEntryStatusResponse) GetTrialEffectTs() int64 {
	if m != nil {
		return m.TrialEffectTs
	}
	return 0
}

func (m *GetVirtualImageCardEntryStatusResponse) GetTrialExpireTs() int64 {
	if m != nil {
		return m.TrialExpireTs
	}
	return 0
}

func (m *GetVirtualImageCardEntryStatusResponse) GetBuyEffectTs() int64 {
	if m != nil {
		return m.BuyEffectTs
	}
	return 0
}

func (m *GetVirtualImageCardEntryStatusResponse) GetBuyExpireTs() int64 {
	if m != nil {
		return m.BuyExpireTs
	}
	return 0
}

type ActivityPlaceOrderReq struct {
	Uid                    uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PackageId              uint32     `protobuf:"varint,2,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	PayChannel             PayChannel `protobuf:"varint,3,opt,name=pay_channel,json=payChannel,proto3,enum=virtual_image_card.PayChannel" json:"pay_channel,omitempty"`
	OriginalTransactionIds []string   `protobuf:"bytes,4,rep,name=original_transaction_ids,json=originalTransactionIds,proto3" json:"original_transaction_ids,omitempty"`
	PayPriceCent           uint32     `protobuf:"varint,5,opt,name=pay_price_cent,json=payPriceCent,proto3" json:"pay_price_cent,omitempty"`
	FaceAuthResultToken    string     `protobuf:"bytes,6,opt,name=face_auth_result_token,json=faceAuthResultToken,proto3" json:"face_auth_result_token,omitempty"`
	ClientType             uint32     `protobuf:"varint,7,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	MarketId               uint32     `protobuf:"varint,8,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientVersion          uint32     `protobuf:"varint,9,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}   `json:"-"`
	XXX_unrecognized       []byte     `json:"-"`
	XXX_sizecache          int32      `json:"-"`
}

func (m *ActivityPlaceOrderReq) Reset()         { *m = ActivityPlaceOrderReq{} }
func (m *ActivityPlaceOrderReq) String() string { return proto.CompactTextString(m) }
func (*ActivityPlaceOrderReq) ProtoMessage()    {}
func (*ActivityPlaceOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{63}
}
func (m *ActivityPlaceOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityPlaceOrderReq.Unmarshal(m, b)
}
func (m *ActivityPlaceOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityPlaceOrderReq.Marshal(b, m, deterministic)
}
func (dst *ActivityPlaceOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityPlaceOrderReq.Merge(dst, src)
}
func (m *ActivityPlaceOrderReq) XXX_Size() int {
	return xxx_messageInfo_ActivityPlaceOrderReq.Size(m)
}
func (m *ActivityPlaceOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityPlaceOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityPlaceOrderReq proto.InternalMessageInfo

func (m *ActivityPlaceOrderReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ActivityPlaceOrderReq) GetPackageId() uint32 {
	if m != nil {
		return m.PackageId
	}
	return 0
}

func (m *ActivityPlaceOrderReq) GetPayChannel() PayChannel {
	if m != nil {
		return m.PayChannel
	}
	return PayChannel_PAY_CHANNEL_UNSPECIFIED
}

func (m *ActivityPlaceOrderReq) GetOriginalTransactionIds() []string {
	if m != nil {
		return m.OriginalTransactionIds
	}
	return nil
}

func (m *ActivityPlaceOrderReq) GetPayPriceCent() uint32 {
	if m != nil {
		return m.PayPriceCent
	}
	return 0
}

func (m *ActivityPlaceOrderReq) GetFaceAuthResultToken() string {
	if m != nil {
		return m.FaceAuthResultToken
	}
	return ""
}

func (m *ActivityPlaceOrderReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *ActivityPlaceOrderReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *ActivityPlaceOrderReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

type ActivityPlaceOrderResp struct {
	OrderNo              string   `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	Token                string   `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	CliOrderNo           string   `protobuf:"bytes,3,opt,name=cli_order_no,json=cliOrderNo,proto3" json:"cli_order_no,omitempty"`
	CliOrderTitle        string   `protobuf:"bytes,4,opt,name=cli_order_title,json=cliOrderTitle,proto3" json:"cli_order_title,omitempty"`
	OrderPrice           string   `protobuf:"bytes,5,opt,name=order_price,json=orderPrice,proto3" json:"order_price,omitempty"`
	Tsk                  string   `protobuf:"bytes,6,opt,name=tsk,proto3" json:"tsk,omitempty"`
	ChannelMap           string   `protobuf:"bytes,7,opt,name=channel_map,json=channelMap,proto3" json:"channel_map,omitempty"`
	FaceAuthContextJson  string   `protobuf:"bytes,8,opt,name=face_auth_context_json,json=faceAuthContextJson,proto3" json:"face_auth_context_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActivityPlaceOrderResp) Reset()         { *m = ActivityPlaceOrderResp{} }
func (m *ActivityPlaceOrderResp) String() string { return proto.CompactTextString(m) }
func (*ActivityPlaceOrderResp) ProtoMessage()    {}
func (*ActivityPlaceOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{64}
}
func (m *ActivityPlaceOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityPlaceOrderResp.Unmarshal(m, b)
}
func (m *ActivityPlaceOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityPlaceOrderResp.Marshal(b, m, deterministic)
}
func (dst *ActivityPlaceOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityPlaceOrderResp.Merge(dst, src)
}
func (m *ActivityPlaceOrderResp) XXX_Size() int {
	return xxx_messageInfo_ActivityPlaceOrderResp.Size(m)
}
func (m *ActivityPlaceOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityPlaceOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityPlaceOrderResp proto.InternalMessageInfo

func (m *ActivityPlaceOrderResp) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

func (m *ActivityPlaceOrderResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *ActivityPlaceOrderResp) GetCliOrderNo() string {
	if m != nil {
		return m.CliOrderNo
	}
	return ""
}

func (m *ActivityPlaceOrderResp) GetCliOrderTitle() string {
	if m != nil {
		return m.CliOrderTitle
	}
	return ""
}

func (m *ActivityPlaceOrderResp) GetOrderPrice() string {
	if m != nil {
		return m.OrderPrice
	}
	return ""
}

func (m *ActivityPlaceOrderResp) GetTsk() string {
	if m != nil {
		return m.Tsk
	}
	return ""
}

func (m *ActivityPlaceOrderResp) GetChannelMap() string {
	if m != nil {
		return m.ChannelMap
	}
	return ""
}

func (m *ActivityPlaceOrderResp) GetFaceAuthContextJson() string {
	if m != nil {
		return m.FaceAuthContextJson
	}
	return ""
}

type GetPackageListByIdsReq struct {
	PackageIds           []uint32 `protobuf:"varint,1,rep,packed,name=package_ids,json=packageIds,proto3" json:"package_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPackageListByIdsReq) Reset()         { *m = GetPackageListByIdsReq{} }
func (m *GetPackageListByIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetPackageListByIdsReq) ProtoMessage()    {}
func (*GetPackageListByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{65}
}
func (m *GetPackageListByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPackageListByIdsReq.Unmarshal(m, b)
}
func (m *GetPackageListByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPackageListByIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetPackageListByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPackageListByIdsReq.Merge(dst, src)
}
func (m *GetPackageListByIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetPackageListByIdsReq.Size(m)
}
func (m *GetPackageListByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPackageListByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPackageListByIdsReq proto.InternalMessageInfo

func (m *GetPackageListByIdsReq) GetPackageIds() []uint32 {
	if m != nil {
		return m.PackageIds
	}
	return nil
}

type GetPackageListByIdsResp struct {
	PackageList          []*Package `protobuf:"bytes,1,rep,name=package_list,json=packageList,proto3" json:"package_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetPackageListByIdsResp) Reset()         { *m = GetPackageListByIdsResp{} }
func (m *GetPackageListByIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetPackageListByIdsResp) ProtoMessage()    {}
func (*GetPackageListByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_7023d0e484d57446, []int{66}
}
func (m *GetPackageListByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPackageListByIdsResp.Unmarshal(m, b)
}
func (m *GetPackageListByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPackageListByIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetPackageListByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPackageListByIdsResp.Merge(dst, src)
}
func (m *GetPackageListByIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetPackageListByIdsResp.Size(m)
}
func (m *GetPackageListByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPackageListByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPackageListByIdsResp proto.InternalMessageInfo

func (m *GetPackageListByIdsResp) GetPackageList() []*Package {
	if m != nil {
		return m.PackageList
	}
	return nil
}

func init() {
	proto.RegisterType((*PlaceOrderReq)(nil), "virtual_image_card.PlaceOrderReq")
	proto.RegisterType((*PlaceOrderResp)(nil), "virtual_image_card.PlaceOrderResp")
	proto.RegisterType((*CancelOrderReq)(nil), "virtual_image_card.CancelOrderReq")
	proto.RegisterType((*CancelOrderResp)(nil), "virtual_image_card.CancelOrderResp")
	proto.RegisterType((*PayCallbackReq)(nil), "virtual_image_card.PayCallbackReq")
	proto.RegisterType((*PayCallbackResp)(nil), "virtual_image_card.PayCallbackResp")
	proto.RegisterType((*NotifyContractReq)(nil), "virtual_image_card.NotifyContractReq")
	proto.RegisterType((*NotifyContractResp)(nil), "virtual_image_card.NotifyContractResp")
	proto.RegisterType((*PlaceAutoPayOrderReq)(nil), "virtual_image_card.PlaceAutoPayOrderReq")
	proto.RegisterType((*PlaceAutoPayOrderResp)(nil), "virtual_image_card.PlaceAutoPayOrderResp")
	proto.RegisterType((*RevokeOrderReq)(nil), "virtual_image_card.RevokeOrderReq")
	proto.RegisterType((*RevokeOrderResp)(nil), "virtual_image_card.RevokeOrderResp")
	proto.RegisterType((*UseVirtualImageTrialCardReq)(nil), "virtual_image_card.UseVirtualImageTrialCardReq")
	proto.RegisterType((*UseVirtualImageTrialCardResp)(nil), "virtual_image_card.UseVirtualImageTrialCardResp")
	proto.RegisterType((*GenerateStatReq)(nil), "virtual_image_card.GenerateStatReq")
	proto.RegisterType((*GenerateStatResp)(nil), "virtual_image_card.GenerateStatResp")
	proto.RegisterType((*GetContractByIdReq)(nil), "virtual_image_card.GetContractByIdReq")
	proto.RegisterType((*GetContractByIdResp)(nil), "virtual_image_card.GetContractByIdResp")
	proto.RegisterType((*UserCardInfo)(nil), "virtual_image_card.UserCardInfo")
	proto.RegisterType((*UserContractInfo)(nil), "virtual_image_card.UserContractInfo")
	proto.RegisterType((*GetUserCardInfoReq)(nil), "virtual_image_card.GetUserCardInfoReq")
	proto.RegisterType((*GetUserCardInfoResp)(nil), "virtual_image_card.GetUserCardInfoResp")
	proto.RegisterType((*GetUserRedemptionInfoReq)(nil), "virtual_image_card.GetUserRedemptionInfoReq")
	proto.RegisterType((*GetUserRedemptionInfoResp)(nil), "virtual_image_card.GetUserRedemptionInfoResp")
	proto.RegisterType((*GetUserPackageListReq)(nil), "virtual_image_card.GetUserPackageListReq")
	proto.RegisterType((*GetUserPackageListResp)(nil), "virtual_image_card.GetUserPackageListResp")
	proto.RegisterType((*UserPackage)(nil), "virtual_image_card.UserPackage")
	proto.RegisterType((*GetPurchaseHistoryReq)(nil), "virtual_image_card.GetPurchaseHistoryReq")
	proto.RegisterType((*GetPurchaseHistoryResp)(nil), "virtual_image_card.GetPurchaseHistoryResp")
	proto.RegisterType((*PurchaseRecord)(nil), "virtual_image_card.PurchaseRecord")
	proto.RegisterType((*Package)(nil), "virtual_image_card.Package")
	proto.RegisterType((*AddPackageReq)(nil), "virtual_image_card.AddPackageReq")
	proto.RegisterType((*AddPackageResp)(nil), "virtual_image_card.AddPackageResp")
	proto.RegisterType((*UpdatePackageStatusReq)(nil), "virtual_image_card.UpdatePackageStatusReq")
	proto.RegisterType((*UpdatePackageStatusResp)(nil), "virtual_image_card.UpdatePackageStatusResp")
	proto.RegisterType((*GetPackageListByStatusReq)(nil), "virtual_image_card.GetPackageListByStatusReq")
	proto.RegisterType((*MixPackage)(nil), "virtual_image_card.MixPackage")
	proto.RegisterType((*GetPackageListByStatusResp)(nil), "virtual_image_card.GetPackageListByStatusResp")
	proto.RegisterType((*DisplayCondition)(nil), "virtual_image_card.DisplayCondition")
	proto.RegisterType((*SalePackage)(nil), "virtual_image_card.SalePackage")
	proto.RegisterType((*MarketInfo)(nil), "virtual_image_card.MarketInfo")
	proto.RegisterType((*GroupPackage)(nil), "virtual_image_card.GroupPackage")
	proto.RegisterType((*BatchAddGroupPackageReq)(nil), "virtual_image_card.BatchAddGroupPackageReq")
	proto.RegisterType((*BatchAddGroupPackageResp)(nil), "virtual_image_card.BatchAddGroupPackageResp")
	proto.RegisterType((*EditGroupPackageReq)(nil), "virtual_image_card.EditGroupPackageReq")
	proto.RegisterType((*EditGroupPackageResp)(nil), "virtual_image_card.EditGroupPackageResp")
	proto.RegisterType((*ModifyPackageReq)(nil), "virtual_image_card.ModifyPackageReq")
	proto.RegisterType((*ModifyPackageResp)(nil), "virtual_image_card.ModifyPackageResp")
	proto.RegisterType((*ModifyGroupPackageReq)(nil), "virtual_image_card.ModifyGroupPackageReq")
	proto.RegisterType((*ModifyGroupPackageResp)(nil), "virtual_image_card.ModifyGroupPackageResp")
	proto.RegisterType((*AddSalePackageReq)(nil), "virtual_image_card.AddSalePackageReq")
	proto.RegisterType((*AddSalePackageResp)(nil), "virtual_image_card.AddSalePackageResp")
	proto.RegisterType((*UpdateSalePackageReq)(nil), "virtual_image_card.UpdateSalePackageReq")
	proto.RegisterType((*UpdateSalePackageResp)(nil), "virtual_image_card.UpdateSalePackageResp")
	proto.RegisterType((*GetSalePackageListByStatusReq)(nil), "virtual_image_card.GetSalePackageListByStatusReq")
	proto.RegisterType((*GetSalePackageListByStatusResp)(nil), "virtual_image_card.GetSalePackageListByStatusResp")
	proto.RegisterType((*SalePackageSortReq)(nil), "virtual_image_card.SalePackageSortReq")
	proto.RegisterType((*SalePackageSortResp)(nil), "virtual_image_card.SalePackageSortResp")
	proto.RegisterType((*AboutToExpireCfg)(nil), "virtual_image_card.AboutToExpireCfg")
	proto.RegisterType((*GetVirtualImageCardCommonCfgRequest)(nil), "virtual_image_card.GetVirtualImageCardCommonCfgRequest")
	proto.RegisterType((*GetVirtualImageCardCommonCfgResponse)(nil), "virtual_image_card.GetVirtualImageCardCommonCfgResponse")
	proto.RegisterType((*GetVirtualImageCardEntryStatusRequest)(nil), "virtual_image_card.GetVirtualImageCardEntryStatusRequest")
	proto.RegisterType((*GetVirtualImageCardEntryStatusResponse)(nil), "virtual_image_card.GetVirtualImageCardEntryStatusResponse")
	proto.RegisterType((*ActivityPlaceOrderReq)(nil), "virtual_image_card.ActivityPlaceOrderReq")
	proto.RegisterType((*ActivityPlaceOrderResp)(nil), "virtual_image_card.ActivityPlaceOrderResp")
	proto.RegisterType((*GetPackageListByIdsReq)(nil), "virtual_image_card.GetPackageListByIdsReq")
	proto.RegisterType((*GetPackageListByIdsResp)(nil), "virtual_image_card.GetPackageListByIdsResp")
	proto.RegisterEnum("virtual_image_card.PayChannel", PayChannel_name, PayChannel_value)
	proto.RegisterEnum("virtual_image_card.PackageType", PackageType_name, PackageType_value)
	proto.RegisterEnum("virtual_image_card.SalePackageStatus", SalePackageStatus_name, SalePackageStatus_value)
	proto.RegisterEnum("virtual_image_card.CardStatus", CardStatus_name, CardStatus_value)
	proto.RegisterEnum("virtual_image_card.DisplayCondition_CondType", DisplayCondition_CondType_name, DisplayCondition_CondType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// VirtualImageCardClient is the client API for VirtualImageCard service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type VirtualImageCardClient interface {
	// 下单
	PlaceOrder(ctx context.Context, in *PlaceOrderReq, opts ...grpc.CallOption) (*PlaceOrderResp, error)
	// 取消订单
	CancelOrder(ctx context.Context, in *CancelOrderReq, opts ...grpc.CallOption) (*CancelOrderResp, error)
	// 支付回调
	PayCallback(ctx context.Context, in *PayCallbackReq, opts ...grpc.CallOption) (*PayCallbackResp, error)
	// 签约回调
	NotifyContract(ctx context.Context, in *NotifyContractReq, opts ...grpc.CallOption) (*NotifyContractResp, error)
	// 苹果的自动下单回调
	PlaceAutoPayOrder(ctx context.Context, in *PlaceAutoPayOrderReq, opts ...grpc.CallOption) (*PlaceAutoPayOrderResp, error)
	// 订单退款
	RevokeOrder(ctx context.Context, in *RevokeOrderReq, opts ...grpc.CallOption) (*RevokeOrderResp, error)
	// 生成统计报表
	GenerateStat(ctx context.Context, in *GenerateStatReq, opts ...grpc.CallOption) (*GenerateStatResp, error)
	// 根据签约id查询最新信息
	GetContractById(ctx context.Context, in *GetContractByIdReq, opts ...grpc.CallOption) (*GetContractByIdResp, error)
	// 使用体验卡
	UseVirtualImageTrialCard(ctx context.Context, in *UseVirtualImageTrialCardReq, opts ...grpc.CallOption) (*UseVirtualImageTrialCardResp, error)
	// 体验卡对账与补单
	GetTrialCardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetTrialCardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	ReplaceTrialCardOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// 获取用户卡信息
	GetUserCardInfo(ctx context.Context, in *GetUserCardInfoReq, opts ...grpc.CallOption) (*GetUserCardInfoResp, error)
	// 获取用户核销信息
	GetUserRedemptionInfo(ctx context.Context, in *GetUserRedemptionInfoReq, opts ...grpc.CallOption) (*GetUserRedemptionInfoResp, error)
	// 获取用户可用套餐列表
	GetUserPackageList(ctx context.Context, in *GetUserPackageListReq, opts ...grpc.CallOption) (*GetUserPackageListResp, error)
	// 获取购买历史
	GetPurchaseHistory(ctx context.Context, in *GetPurchaseHistoryReq, opts ...grpc.CallOption) (*GetPurchaseHistoryResp, error)
	// 活动下单
	ActivityPlaceOrder(ctx context.Context, in *ActivityPlaceOrderReq, opts ...grpc.CallOption) (*ActivityPlaceOrderResp, error)
	// 根据id列表获取套餐信息
	GetPackageListByIds(ctx context.Context, in *GetPackageListByIdsReq, opts ...grpc.CallOption) (*GetPackageListByIdsResp, error)
	// ========================== 套餐运营后台 ==================================
	// 新增套餐
	AddPackage(ctx context.Context, in *AddPackageReq, opts ...grpc.CallOption) (*AddPackageResp, error)
	// 批量新增分组套餐
	BatchAddGroupPackage(ctx context.Context, in *BatchAddGroupPackageReq, opts ...grpc.CallOption) (*BatchAddGroupPackageResp, error)
	// 修改套餐内容
	ModifyPackage(ctx context.Context, in *ModifyPackageReq, opts ...grpc.CallOption) (*ModifyPackageResp, error)
	// 更新套餐/分组状态（停用/启用）
	UpdatePackageStatus(ctx context.Context, in *UpdatePackageStatusReq, opts ...grpc.CallOption) (*UpdatePackageStatusResp, error)
	// 获取套餐/分组列表(按更新时间排序)
	GetPackageListByStatus(ctx context.Context, in *GetPackageListByStatusReq, opts ...grpc.CallOption) (*GetPackageListByStatusResp, error)
	// 新增/编辑分组套餐
	EditGroupPackage(ctx context.Context, in *EditGroupPackageReq, opts ...grpc.CallOption) (*EditGroupPackageResp, error)
	// 修改分组套餐内容
	ModifyGroupPackage(ctx context.Context, in *ModifyGroupPackageReq, opts ...grpc.CallOption) (*ModifyGroupPackageResp, error)
	// 增加在售架套餐
	AddSalePackage(ctx context.Context, in *AddSalePackageReq, opts ...grpc.CallOption) (*AddSalePackageResp, error)
	// 修改在售架套餐
	UpdateSalePackage(ctx context.Context, in *UpdateSalePackageReq, opts ...grpc.CallOption) (*UpdateSalePackageResp, error)
	// 获取在售架套餐列表
	GetSalePackageListByStatus(ctx context.Context, in *GetSalePackageListByStatusReq, opts ...grpc.CallOption) (*GetSalePackageListByStatusResp, error)
	// 修改在售架套餐排序
	SalePackageSort(ctx context.Context, in *SalePackageSortReq, opts ...grpc.CallOption) (*SalePackageSortResp, error)
	// 获取无限卡配置
	GetVirtualImageCardCommonCfg(ctx context.Context, in *GetVirtualImageCardCommonCfgRequest, opts ...grpc.CallOption) (*GetVirtualImageCardCommonCfgResponse, error)
	// 获取无限卡入口状态
	GetVirtualImageCardEntryStatus(ctx context.Context, in *GetVirtualImageCardEntryStatusRequest, opts ...grpc.CallOption) (*GetVirtualImageCardEntryStatusResponse, error)
}

type virtualImageCardClient struct {
	cc *grpc.ClientConn
}

func NewVirtualImageCardClient(cc *grpc.ClientConn) VirtualImageCardClient {
	return &virtualImageCardClient{cc}
}

func (c *virtualImageCardClient) PlaceOrder(ctx context.Context, in *PlaceOrderReq, opts ...grpc.CallOption) (*PlaceOrderResp, error) {
	out := new(PlaceOrderResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/PlaceOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) CancelOrder(ctx context.Context, in *CancelOrderReq, opts ...grpc.CallOption) (*CancelOrderResp, error) {
	out := new(CancelOrderResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/CancelOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) PayCallback(ctx context.Context, in *PayCallbackReq, opts ...grpc.CallOption) (*PayCallbackResp, error) {
	out := new(PayCallbackResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/PayCallback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) NotifyContract(ctx context.Context, in *NotifyContractReq, opts ...grpc.CallOption) (*NotifyContractResp, error) {
	out := new(NotifyContractResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/NotifyContract", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) PlaceAutoPayOrder(ctx context.Context, in *PlaceAutoPayOrderReq, opts ...grpc.CallOption) (*PlaceAutoPayOrderResp, error) {
	out := new(PlaceAutoPayOrderResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/PlaceAutoPayOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) RevokeOrder(ctx context.Context, in *RevokeOrderReq, opts ...grpc.CallOption) (*RevokeOrderResp, error) {
	out := new(RevokeOrderResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/RevokeOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GenerateStat(ctx context.Context, in *GenerateStatReq, opts ...grpc.CallOption) (*GenerateStatResp, error) {
	out := new(GenerateStatResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GenerateStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetContractById(ctx context.Context, in *GetContractByIdReq, opts ...grpc.CallOption) (*GetContractByIdResp, error) {
	out := new(GetContractByIdResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetContractById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) UseVirtualImageTrialCard(ctx context.Context, in *UseVirtualImageTrialCardReq, opts ...grpc.CallOption) (*UseVirtualImageTrialCardResp, error) {
	out := new(UseVirtualImageTrialCardResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/UseVirtualImageTrialCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetTrialCardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetTrialCardTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetTrialCardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetTrialCardOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) ReplaceTrialCardOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/ReplaceTrialCardOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetUserCardInfo(ctx context.Context, in *GetUserCardInfoReq, opts ...grpc.CallOption) (*GetUserCardInfoResp, error) {
	out := new(GetUserCardInfoResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetUserCardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetUserRedemptionInfo(ctx context.Context, in *GetUserRedemptionInfoReq, opts ...grpc.CallOption) (*GetUserRedemptionInfoResp, error) {
	out := new(GetUserRedemptionInfoResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetUserRedemptionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetUserPackageList(ctx context.Context, in *GetUserPackageListReq, opts ...grpc.CallOption) (*GetUserPackageListResp, error) {
	out := new(GetUserPackageListResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetUserPackageList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetPurchaseHistory(ctx context.Context, in *GetPurchaseHistoryReq, opts ...grpc.CallOption) (*GetPurchaseHistoryResp, error) {
	out := new(GetPurchaseHistoryResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetPurchaseHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) ActivityPlaceOrder(ctx context.Context, in *ActivityPlaceOrderReq, opts ...grpc.CallOption) (*ActivityPlaceOrderResp, error) {
	out := new(ActivityPlaceOrderResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/ActivityPlaceOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetPackageListByIds(ctx context.Context, in *GetPackageListByIdsReq, opts ...grpc.CallOption) (*GetPackageListByIdsResp, error) {
	out := new(GetPackageListByIdsResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetPackageListByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) AddPackage(ctx context.Context, in *AddPackageReq, opts ...grpc.CallOption) (*AddPackageResp, error) {
	out := new(AddPackageResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/AddPackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) BatchAddGroupPackage(ctx context.Context, in *BatchAddGroupPackageReq, opts ...grpc.CallOption) (*BatchAddGroupPackageResp, error) {
	out := new(BatchAddGroupPackageResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/BatchAddGroupPackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) ModifyPackage(ctx context.Context, in *ModifyPackageReq, opts ...grpc.CallOption) (*ModifyPackageResp, error) {
	out := new(ModifyPackageResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/ModifyPackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) UpdatePackageStatus(ctx context.Context, in *UpdatePackageStatusReq, opts ...grpc.CallOption) (*UpdatePackageStatusResp, error) {
	out := new(UpdatePackageStatusResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/UpdatePackageStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetPackageListByStatus(ctx context.Context, in *GetPackageListByStatusReq, opts ...grpc.CallOption) (*GetPackageListByStatusResp, error) {
	out := new(GetPackageListByStatusResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetPackageListByStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) EditGroupPackage(ctx context.Context, in *EditGroupPackageReq, opts ...grpc.CallOption) (*EditGroupPackageResp, error) {
	out := new(EditGroupPackageResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/EditGroupPackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) ModifyGroupPackage(ctx context.Context, in *ModifyGroupPackageReq, opts ...grpc.CallOption) (*ModifyGroupPackageResp, error) {
	out := new(ModifyGroupPackageResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/ModifyGroupPackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) AddSalePackage(ctx context.Context, in *AddSalePackageReq, opts ...grpc.CallOption) (*AddSalePackageResp, error) {
	out := new(AddSalePackageResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/AddSalePackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) UpdateSalePackage(ctx context.Context, in *UpdateSalePackageReq, opts ...grpc.CallOption) (*UpdateSalePackageResp, error) {
	out := new(UpdateSalePackageResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/UpdateSalePackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetSalePackageListByStatus(ctx context.Context, in *GetSalePackageListByStatusReq, opts ...grpc.CallOption) (*GetSalePackageListByStatusResp, error) {
	out := new(GetSalePackageListByStatusResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetSalePackageListByStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) SalePackageSort(ctx context.Context, in *SalePackageSortReq, opts ...grpc.CallOption) (*SalePackageSortResp, error) {
	out := new(SalePackageSortResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/SalePackageSort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetVirtualImageCardCommonCfg(ctx context.Context, in *GetVirtualImageCardCommonCfgRequest, opts ...grpc.CallOption) (*GetVirtualImageCardCommonCfgResponse, error) {
	out := new(GetVirtualImageCardCommonCfgResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetVirtualImageCardCommonCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetVirtualImageCardEntryStatus(ctx context.Context, in *GetVirtualImageCardEntryStatusRequest, opts ...grpc.CallOption) (*GetVirtualImageCardEntryStatusResponse, error) {
	out := new(GetVirtualImageCardEntryStatusResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetVirtualImageCardEntryStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VirtualImageCardServer is the server API for VirtualImageCard service.
type VirtualImageCardServer interface {
	// 下单
	PlaceOrder(context.Context, *PlaceOrderReq) (*PlaceOrderResp, error)
	// 取消订单
	CancelOrder(context.Context, *CancelOrderReq) (*CancelOrderResp, error)
	// 支付回调
	PayCallback(context.Context, *PayCallbackReq) (*PayCallbackResp, error)
	// 签约回调
	NotifyContract(context.Context, *NotifyContractReq) (*NotifyContractResp, error)
	// 苹果的自动下单回调
	PlaceAutoPayOrder(context.Context, *PlaceAutoPayOrderReq) (*PlaceAutoPayOrderResp, error)
	// 订单退款
	RevokeOrder(context.Context, *RevokeOrderReq) (*RevokeOrderResp, error)
	// 生成统计报表
	GenerateStat(context.Context, *GenerateStatReq) (*GenerateStatResp, error)
	// 根据签约id查询最新信息
	GetContractById(context.Context, *GetContractByIdReq) (*GetContractByIdResp, error)
	// 使用体验卡
	UseVirtualImageTrialCard(context.Context, *UseVirtualImageTrialCardReq) (*UseVirtualImageTrialCardResp, error)
	// 体验卡对账与补单
	GetTrialCardTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetTrialCardOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	ReplaceTrialCardOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// 获取用户卡信息
	GetUserCardInfo(context.Context, *GetUserCardInfoReq) (*GetUserCardInfoResp, error)
	// 获取用户核销信息
	GetUserRedemptionInfo(context.Context, *GetUserRedemptionInfoReq) (*GetUserRedemptionInfoResp, error)
	// 获取用户可用套餐列表
	GetUserPackageList(context.Context, *GetUserPackageListReq) (*GetUserPackageListResp, error)
	// 获取购买历史
	GetPurchaseHistory(context.Context, *GetPurchaseHistoryReq) (*GetPurchaseHistoryResp, error)
	// 活动下单
	ActivityPlaceOrder(context.Context, *ActivityPlaceOrderReq) (*ActivityPlaceOrderResp, error)
	// 根据id列表获取套餐信息
	GetPackageListByIds(context.Context, *GetPackageListByIdsReq) (*GetPackageListByIdsResp, error)
	// ========================== 套餐运营后台 ==================================
	// 新增套餐
	AddPackage(context.Context, *AddPackageReq) (*AddPackageResp, error)
	// 批量新增分组套餐
	BatchAddGroupPackage(context.Context, *BatchAddGroupPackageReq) (*BatchAddGroupPackageResp, error)
	// 修改套餐内容
	ModifyPackage(context.Context, *ModifyPackageReq) (*ModifyPackageResp, error)
	// 更新套餐/分组状态（停用/启用）
	UpdatePackageStatus(context.Context, *UpdatePackageStatusReq) (*UpdatePackageStatusResp, error)
	// 获取套餐/分组列表(按更新时间排序)
	GetPackageListByStatus(context.Context, *GetPackageListByStatusReq) (*GetPackageListByStatusResp, error)
	// 新增/编辑分组套餐
	EditGroupPackage(context.Context, *EditGroupPackageReq) (*EditGroupPackageResp, error)
	// 修改分组套餐内容
	ModifyGroupPackage(context.Context, *ModifyGroupPackageReq) (*ModifyGroupPackageResp, error)
	// 增加在售架套餐
	AddSalePackage(context.Context, *AddSalePackageReq) (*AddSalePackageResp, error)
	// 修改在售架套餐
	UpdateSalePackage(context.Context, *UpdateSalePackageReq) (*UpdateSalePackageResp, error)
	// 获取在售架套餐列表
	GetSalePackageListByStatus(context.Context, *GetSalePackageListByStatusReq) (*GetSalePackageListByStatusResp, error)
	// 修改在售架套餐排序
	SalePackageSort(context.Context, *SalePackageSortReq) (*SalePackageSortResp, error)
	// 获取无限卡配置
	GetVirtualImageCardCommonCfg(context.Context, *GetVirtualImageCardCommonCfgRequest) (*GetVirtualImageCardCommonCfgResponse, error)
	// 获取无限卡入口状态
	GetVirtualImageCardEntryStatus(context.Context, *GetVirtualImageCardEntryStatusRequest) (*GetVirtualImageCardEntryStatusResponse, error)
}

func RegisterVirtualImageCardServer(s *grpc.Server, srv VirtualImageCardServer) {
	s.RegisterService(&_VirtualImageCard_serviceDesc, srv)
}

func _VirtualImageCard_PlaceOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).PlaceOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/PlaceOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).PlaceOrder(ctx, req.(*PlaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_CancelOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).CancelOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/CancelOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).CancelOrder(ctx, req.(*CancelOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_PayCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayCallbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).PayCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/PayCallback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).PayCallback(ctx, req.(*PayCallbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_NotifyContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyContractReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).NotifyContract(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/NotifyContract",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).NotifyContract(ctx, req.(*NotifyContractReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_PlaceAutoPayOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlaceAutoPayOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).PlaceAutoPayOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/PlaceAutoPayOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).PlaceAutoPayOrder(ctx, req.(*PlaceAutoPayOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_RevokeOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).RevokeOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/RevokeOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).RevokeOrder(ctx, req.(*RevokeOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GenerateStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GenerateStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GenerateStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GenerateStat(ctx, req.(*GenerateStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetContractById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetContractByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetContractById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetContractById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetContractById(ctx, req.(*GetContractByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_UseVirtualImageTrialCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UseVirtualImageTrialCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).UseVirtualImageTrialCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/UseVirtualImageTrialCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).UseVirtualImageTrialCard(ctx, req.(*UseVirtualImageTrialCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetTrialCardTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetTrialCardTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetTrialCardTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetTrialCardTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetTrialCardOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetTrialCardOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetTrialCardOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetTrialCardOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_ReplaceTrialCardOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).ReplaceTrialCardOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/ReplaceTrialCardOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).ReplaceTrialCardOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetUserCardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCardInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetUserCardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetUserCardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetUserCardInfo(ctx, req.(*GetUserCardInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetUserRedemptionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRedemptionInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetUserRedemptionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetUserRedemptionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetUserRedemptionInfo(ctx, req.(*GetUserRedemptionInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetUserPackageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPackageListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetUserPackageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetUserPackageList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetUserPackageList(ctx, req.(*GetUserPackageListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetPurchaseHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPurchaseHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetPurchaseHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetPurchaseHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetPurchaseHistory(ctx, req.(*GetPurchaseHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_ActivityPlaceOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivityPlaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).ActivityPlaceOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/ActivityPlaceOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).ActivityPlaceOrder(ctx, req.(*ActivityPlaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetPackageListByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPackageListByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetPackageListByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetPackageListByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetPackageListByIds(ctx, req.(*GetPackageListByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_AddPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).AddPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/AddPackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).AddPackage(ctx, req.(*AddPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_BatchAddGroupPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddGroupPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).BatchAddGroupPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/BatchAddGroupPackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).BatchAddGroupPackage(ctx, req.(*BatchAddGroupPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_ModifyPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).ModifyPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/ModifyPackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).ModifyPackage(ctx, req.(*ModifyPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_UpdatePackageStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePackageStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).UpdatePackageStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/UpdatePackageStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).UpdatePackageStatus(ctx, req.(*UpdatePackageStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetPackageListByStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPackageListByStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetPackageListByStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetPackageListByStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetPackageListByStatus(ctx, req.(*GetPackageListByStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_EditGroupPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditGroupPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).EditGroupPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/EditGroupPackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).EditGroupPackage(ctx, req.(*EditGroupPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_ModifyGroupPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyGroupPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).ModifyGroupPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/ModifyGroupPackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).ModifyGroupPackage(ctx, req.(*ModifyGroupPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_AddSalePackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSalePackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).AddSalePackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/AddSalePackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).AddSalePackage(ctx, req.(*AddSalePackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_UpdateSalePackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSalePackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).UpdateSalePackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/UpdateSalePackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).UpdateSalePackage(ctx, req.(*UpdateSalePackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetSalePackageListByStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSalePackageListByStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetSalePackageListByStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetSalePackageListByStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetSalePackageListByStatus(ctx, req.(*GetSalePackageListByStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_SalePackageSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SalePackageSortReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).SalePackageSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/SalePackageSort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).SalePackageSort(ctx, req.(*SalePackageSortReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetVirtualImageCardCommonCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVirtualImageCardCommonCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetVirtualImageCardCommonCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetVirtualImageCardCommonCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetVirtualImageCardCommonCfg(ctx, req.(*GetVirtualImageCardCommonCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetVirtualImageCardEntryStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVirtualImageCardEntryStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetVirtualImageCardEntryStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetVirtualImageCardEntryStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetVirtualImageCardEntryStatus(ctx, req.(*GetVirtualImageCardEntryStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _VirtualImageCard_serviceDesc = grpc.ServiceDesc{
	ServiceName: "virtual_image_card.VirtualImageCard",
	HandlerType: (*VirtualImageCardServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PlaceOrder",
			Handler:    _VirtualImageCard_PlaceOrder_Handler,
		},
		{
			MethodName: "CancelOrder",
			Handler:    _VirtualImageCard_CancelOrder_Handler,
		},
		{
			MethodName: "PayCallback",
			Handler:    _VirtualImageCard_PayCallback_Handler,
		},
		{
			MethodName: "NotifyContract",
			Handler:    _VirtualImageCard_NotifyContract_Handler,
		},
		{
			MethodName: "PlaceAutoPayOrder",
			Handler:    _VirtualImageCard_PlaceAutoPayOrder_Handler,
		},
		{
			MethodName: "RevokeOrder",
			Handler:    _VirtualImageCard_RevokeOrder_Handler,
		},
		{
			MethodName: "GenerateStat",
			Handler:    _VirtualImageCard_GenerateStat_Handler,
		},
		{
			MethodName: "GetContractById",
			Handler:    _VirtualImageCard_GetContractById_Handler,
		},
		{
			MethodName: "UseVirtualImageTrialCard",
			Handler:    _VirtualImageCard_UseVirtualImageTrialCard_Handler,
		},
		{
			MethodName: "GetTrialCardTotalCount",
			Handler:    _VirtualImageCard_GetTrialCardTotalCount_Handler,
		},
		{
			MethodName: "GetTrialCardOrderIds",
			Handler:    _VirtualImageCard_GetTrialCardOrderIds_Handler,
		},
		{
			MethodName: "ReplaceTrialCardOrder",
			Handler:    _VirtualImageCard_ReplaceTrialCardOrder_Handler,
		},
		{
			MethodName: "GetUserCardInfo",
			Handler:    _VirtualImageCard_GetUserCardInfo_Handler,
		},
		{
			MethodName: "GetUserRedemptionInfo",
			Handler:    _VirtualImageCard_GetUserRedemptionInfo_Handler,
		},
		{
			MethodName: "GetUserPackageList",
			Handler:    _VirtualImageCard_GetUserPackageList_Handler,
		},
		{
			MethodName: "GetPurchaseHistory",
			Handler:    _VirtualImageCard_GetPurchaseHistory_Handler,
		},
		{
			MethodName: "ActivityPlaceOrder",
			Handler:    _VirtualImageCard_ActivityPlaceOrder_Handler,
		},
		{
			MethodName: "GetPackageListByIds",
			Handler:    _VirtualImageCard_GetPackageListByIds_Handler,
		},
		{
			MethodName: "AddPackage",
			Handler:    _VirtualImageCard_AddPackage_Handler,
		},
		{
			MethodName: "BatchAddGroupPackage",
			Handler:    _VirtualImageCard_BatchAddGroupPackage_Handler,
		},
		{
			MethodName: "ModifyPackage",
			Handler:    _VirtualImageCard_ModifyPackage_Handler,
		},
		{
			MethodName: "UpdatePackageStatus",
			Handler:    _VirtualImageCard_UpdatePackageStatus_Handler,
		},
		{
			MethodName: "GetPackageListByStatus",
			Handler:    _VirtualImageCard_GetPackageListByStatus_Handler,
		},
		{
			MethodName: "EditGroupPackage",
			Handler:    _VirtualImageCard_EditGroupPackage_Handler,
		},
		{
			MethodName: "ModifyGroupPackage",
			Handler:    _VirtualImageCard_ModifyGroupPackage_Handler,
		},
		{
			MethodName: "AddSalePackage",
			Handler:    _VirtualImageCard_AddSalePackage_Handler,
		},
		{
			MethodName: "UpdateSalePackage",
			Handler:    _VirtualImageCard_UpdateSalePackage_Handler,
		},
		{
			MethodName: "GetSalePackageListByStatus",
			Handler:    _VirtualImageCard_GetSalePackageListByStatus_Handler,
		},
		{
			MethodName: "SalePackageSort",
			Handler:    _VirtualImageCard_SalePackageSort_Handler,
		},
		{
			MethodName: "GetVirtualImageCardCommonCfg",
			Handler:    _VirtualImageCard_GetVirtualImageCardCommonCfg_Handler,
		},
		{
			MethodName: "GetVirtualImageCardEntryStatus",
			Handler:    _VirtualImageCard_GetVirtualImageCardEntryStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/virtual-image-card/virtual-image-card.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/virtual-image-card/virtual-image-card.proto", fileDescriptor_virtual_image_card_7023d0e484d57446)
}

var fileDescriptor_virtual_image_card_7023d0e484d57446 = []byte{
	// 4037 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x5b, 0xcd, 0x6f, 0x1b, 0x49,
	0x76, 0x5f, 0x7e, 0x48, 0x22, 0x1f, 0x45, 0x8a, 0x2a, 0xeb, 0x83, 0xa2, 0x67, 0x6c, 0x6f, 0xfb,
	0x63, 0x3c, 0x9e, 0xb1, 0xbc, 0xeb, 0x59, 0x4f, 0x76, 0x06, 0xd8, 0x4c, 0x28, 0x9a, 0x23, 0x33,
	0x96, 0x25, 0xa1, 0x49, 0xd9, 0xf1, 0x62, 0x90, 0x46, 0xa9, 0xbb, 0x44, 0x76, 0xd4, 0xea, 0x6e,
	0x77, 0x35, 0x65, 0x73, 0x91, 0xe4, 0xb2, 0x39, 0xe4, 0x90, 0x20, 0x08, 0x72, 0x08, 0x10, 0xe4,
	0x92, 0x53, 0x6e, 0xb9, 0x05, 0xf9, 0x27, 0x72, 0x09, 0x10, 0x20, 0xa7, 0x20, 0xc8, 0xff, 0x90,
	0x4b, 0xf6, 0xb4, 0xa8, 0x8f, 0x6e, 0x76, 0x37, 0x8b, 0x14, 0xb5, 0x3b, 0xa7, 0x3d, 0x89, 0xf5,
	0xde, 0xab, 0xaf, 0xf7, 0x5e, 0xbd, 0xf7, 0xeb, 0x57, 0x25, 0xf8, 0x3a, 0x0c, 0x9f, 0xbc, 0x1b,
	0xd9, 0xe6, 0x39, 0xb5, 0x9d, 0x4b, 0x12, 0x3c, 0xb9, 0xb4, 0x83, 0x70, 0x84, 0x9d, 0xc7, 0xf6,
	0x05, 0x1e, 0x90, 0xc7, 0x26, 0x0e, 0x2c, 0x05, 0x69, 0xd7, 0x0f, 0xbc, 0xd0, 0x43, 0x48, 0x72,
	0x0c, 0xce, 0x31, 0x18, 0xa7, 0xb9, 0x9b, 0x19, 0x8f, 0x7c, 0x08, 0x89, 0x4b, 0x6d, 0xcf, 0x7d,
	0xe2, 0xf9, 0xa1, 0xed, 0xb9, 0x34, 0xfa, 0x2b, 0xc6, 0x98, 0x92, 0x0f, 0x88, 0xe9, 0xb9, 0xa6,
	0xed, 0x90, 0xc7, 0x97, 0x4f, 0x53, 0x0d, 0x21, 0xaf, 0xfd, 0x6f, 0x0e, 0xaa, 0xc7, 0x0e, 0x36,
	0xc9, 0x51, 0x60, 0x91, 0x40, 0x27, 0xef, 0x50, 0x1d, 0x0a, 0x23, 0xdb, 0x6a, 0xe4, 0xee, 0xe4,
	0x1e, 0x56, 0x75, 0xf6, 0x13, 0x7d, 0x0c, 0xe0, 0x63, 0xf3, 0x9c, 0xad, 0xc9, 0xb6, 0x1a, 0x79,
	0xce, 0x28, 0x4b, 0x4a, 0xd7, 0x42, 0xdf, 0x40, 0xc5, 0xc7, 0x63, 0xc3, 0x1c, 0x62, 0xd7, 0x25,
	0x4e, 0xa3, 0x70, 0x27, 0xf7, 0xb0, 0xf6, 0xf4, 0xd6, 0xee, 0xf4, 0x66, 0x76, 0x8f, 0xf1, 0xb8,
	0x2d, 0xa4, 0x74, 0xf0, 0xe3, 0xdf, 0xe8, 0xa7, 0xd0, 0xf0, 0x02, 0x7b, 0x60, 0xbb, 0xd8, 0x31,
	0xc2, 0x00, 0xbb, 0x14, 0x9b, 0x6c, 0x4b, 0x86, 0x6d, 0xd1, 0x46, 0xf1, 0x4e, 0xe1, 0x61, 0x59,
	0xdf, 0x8a, 0xf8, 0xfd, 0x09, 0xbb, 0x6b, 0x51, 0x74, 0x0f, 0x6a, 0x6c, 0x6a, 0x3f, 0xb0, 0x4d,
	0x62, 0x98, 0xc4, 0x0d, 0x1b, 0x4b, 0x7c, 0x75, 0xab, 0x3e, 0x1e, 0x1f, 0x33, 0x62, 0x9b, 0xb8,
	0xa1, 0xf6, 0x3f, 0x39, 0xa8, 0x25, 0xf7, 0x48, 0x7d, 0xb4, 0x03, 0x25, 0x8f, 0x35, 0x0c, 0xd7,
	0xe3, 0x3b, 0x2d, 0xeb, 0x2b, 0xbc, 0x7d, 0xe8, 0xa1, 0x0d, 0x58, 0x0a, 0xbd, 0x73, 0xe2, 0xf2,
	0x8d, 0x96, 0x75, 0xd1, 0x40, 0x77, 0x60, 0xd5, 0x74, 0x6c, 0x23, 0xee, 0x54, 0xe0, 0x4c, 0x30,
	0x1d, 0xfb, 0x48, 0xf6, 0x7b, 0x00, 0x6b, 0x13, 0x89, 0xd0, 0x0e, 0x1d, 0xd2, 0x28, 0x72, 0xa1,
	0x6a, 0x24, 0xd4, 0x67, 0x44, 0x74, 0x1b, 0x2a, 0x42, 0x86, 0xaf, 0x9a, 0x2f, 0xb8, 0xac, 0x03,
	0x27, 0xf1, 0x25, 0x33, 0x03, 0x84, 0xf4, 0xbc, 0xb1, 0xcc, 0x19, 0xec, 0x27, 0xeb, 0x22, 0xb5,
	0x6b, 0x5c, 0x60, 0xbf, 0xb1, 0x22, 0xe7, 0x16, 0xa4, 0x57, 0xd8, 0xd7, 0x7e, 0x06, 0xb5, 0x36,
	0x76, 0x4d, 0xe2, 0xcc, 0xb1, 0x62, 0x72, 0xcb, 0xf9, 0xd4, 0x96, 0xb5, 0x75, 0x58, 0x4b, 0x75,
	0xa7, 0xbe, 0xf6, 0xff, 0x4c, 0x67, 0x78, 0xdc, 0xc6, 0x8e, 0x73, 0x8a, 0xcd, 0x73, 0x36, 0xe4,
	0x1c, 0x9d, 0x65, 0xb5, 0x93, 0x9f, 0xd2, 0xce, 0x3d, 0xa8, 0x79, 0xe1, 0x90, 0x04, 0x59, 0x0d,
	0xae, 0x72, 0x6a, 0x24, 0xf5, 0x10, 0xea, 0xa6, 0x67, 0xbb, 0x46, 0xd2, 0x9f, 0x84, 0x12, 0x6b,
	0x8c, 0x3e, 0xf1, 0x9f, 0xc5, 0x2c, 0x8f, 0x36, 0x61, 0x99, 0x49, 0x85, 0x94, 0x6b, 0xb3, 0xa0,
	0x2f, 0xf9, 0x78, 0xdc, 0xa7, 0xcc, 0xa1, 0x99, 0x0f, 0x5d, 0x12, 0x83, 0xe9, 0x68, 0x45, 0x38,
	0xb4, 0xa0, 0x9c, 0xd8, 0x16, 0x53, 0x47, 0x6a, 0xeb, 0xd4, 0xd7, 0xfe, 0x23, 0x07, 0xeb, 0x87,
	0x5e, 0x68, 0x9f, 0x8d, 0xdb, 0x9e, 0x1b, 0x06, 0xd8, 0x0c, 0x99, 0x46, 0x98, 0x5d, 0x64, 0xd3,
	0x90, 0xca, 0x66, 0xbb, 0x96, 0xa4, 0xae, 0x85, 0xb6, 0x61, 0xc5, 0xa6, 0x06, 0xb5, 0x07, 0xc2,
	0x9b, 0x4a, 0xfa, 0xb2, 0x4d, 0x7b, 0xf6, 0xc0, 0xcd, 0xac, 0xa0, 0x90, 0x59, 0x01, 0xdb, 0x5d,
	0x40, 0xb0, 0x63, 0x9c, 0x8e, 0xc6, 0x24, 0xe0, 0x22, 0x45, 0xb1, 0x3b, 0x46, 0xdd, 0x63, 0xc4,
	0x13, 0x79, 0x2e, 0x03, 0xcf, 0x1a, 0x89, 0xd9, 0x85, 0x23, 0x95, 0x25, 0xa5, 0x6b, 0xa1, 0x5b,
	0x50, 0x71, 0xc9, 0x87, 0xd0, 0x48, 0x69, 0xa0, 0xcc, 0x48, 0xc7, 0x4c, 0x0b, 0xda, 0x06, 0xa0,
	0xec, 0x96, 0xa8, 0xaf, 0xbd, 0x86, 0x0d, 0x7e, 0x56, 0x5a, 0xa3, 0xd0, 0x3b, 0xc6, 0xe3, 0xd8,
	0xa1, 0xae, 0xdc, 0x6b, 0x7a, 0x35, 0xf9, 0xcc, 0x6a, 0xb4, 0x6d, 0xd8, 0x54, 0x8c, 0x4b, 0x7d,
	0xed, 0x8f, 0xa1, 0xa6, 0x93, 0x4b, 0xef, 0x7c, 0x12, 0x81, 0x62, 0x47, 0x8b, 0xe7, 0x11, 0x8e,
	0xd6, 0xb5, 0xd8, 0x2a, 0x5c, 0xbe, 0x66, 0x23, 0xb4, 0x2f, 0x48, 0xe4, 0x67, 0x82, 0xd4, 0xb7,
	0x2f, 0x48, 0xe4, 0xf7, 0x85, 0xd8, 0xef, 0x99, 0x35, 0x53, 0xe3, 0x53, 0x5f, 0x73, 0xe0, 0xe6,
	0x09, 0x25, 0xaf, 0x45, 0x80, 0xea, 0xb2, 0xf8, 0xd4, 0x0f, 0x6c, 0xec, 0xb4, 0x71, 0x60, 0xa9,
	0xcf, 0xce, 0x1d, 0x58, 0x3d, 0x1b, 0xb9, 0x26, 0x8f, 0x62, 0x93, 0x18, 0x08, 0x8c, 0xc6, 0x3a,
	0x75, 0xf9, 0xe9, 0xc2, 0x96, 0x65, 0x58, 0x78, 0x4c, 0xe5, 0xe4, 0x2b, 0xd8, 0xb2, 0x9e, 0xe3,
	0x31, 0xd5, 0x6e, 0xc1, 0x47, 0xb3, 0x67, 0xa3, 0xbe, 0xf6, 0x12, 0xd6, 0xf6, 0x89, 0x4b, 0x02,
	0x1c, 0x92, 0x5e, 0x88, 0xb9, 0x63, 0x7d, 0x0c, 0x40, 0x43, 0x1c, 0x84, 0x62, 0x97, 0x42, 0x07,
	0x65, 0x4e, 0xe1, 0x9b, 0xdc, 0x81, 0x12, 0x71, 0xad, 0xa4, 0x0a, 0x56, 0x88, 0x6b, 0x31, 0x96,
	0x86, 0xa0, 0x9e, 0x1e, 0x8c, 0xfa, 0xda, 0x33, 0x40, 0xfb, 0x24, 0x8c, 0xac, 0xbc, 0x37, 0xee,
	0x5a, 0x8b, 0x18, 0x54, 0xdb, 0x84, 0x1b, 0x53, 0xdd, 0xa8, 0xaf, 0xfd, 0x4d, 0x01, 0x56, 0x4f,
	0x28, 0x09, 0xf8, 0xc6, 0xdd, 0x33, 0x4f, 0xa1, 0xae, 0x9b, 0x50, 0x26, 0x67, 0x67, 0xc4, 0x0c,
	0x99, 0xdf, 0xe5, 0xb9, 0xdf, 0x95, 0x04, 0xa1, 0x4f, 0x39, 0xf3, 0x83, 0x6f, 0x07, 0x84, 0x31,
	0x0b, 0x92, 0xc9, 0x09, 0x7d, 0x8a, 0xf6, 0xa0, 0x1c, 0xad, 0x40, 0xc4, 0xfe, 0xca, 0xd3, 0x7b,
	0xaa, 0x4c, 0xc2, 0x17, 0x10, 0x2d, 0xd5, 0x3d, 0xf3, 0xf4, 0x49, 0x37, 0xf4, 0x08, 0xd6, 0x2d,
	0x9b, 0x9a, 0xde, 0xc8, 0x0d, 0x8d, 0xd8, 0x8f, 0xc4, 0xe9, 0x58, 0x8b, 0x18, 0x47, 0xd2, 0x9f,
	0x3e, 0x03, 0x74, 0x66, 0x07, 0x34, 0x64, 0x27, 0x6d, 0x22, 0x2c, 0x42, 0xef, 0x1a, 0xe7, 0xec,
	0x8d, 0xc6, 0x91, 0xb0, 0x06, 0x55, 0x26, 0x36, 0xd9, 0xda, 0x0a, 0x5f, 0x7d, 0xe5, 0x74, 0x34,
	0xee, 0x44, 0xbb, 0x8b, 0x64, 0xe2, 0x1d, 0x96, 0x26, 0x32, 0xd1, 0x26, 0x1f, 0xc0, 0x5a, 0xc8,
	0x3c, 0x20, 0x31, 0x52, 0x99, 0x4b, 0x55, 0x39, 0x39, 0x1e, 0x6b, 0x22, 0x17, 0x8f, 0x06, 0x49,
	0x39, 0x39, 0x9e, 0xf6, 0xab, 0x1c, 0xd4, 0xb3, 0x0a, 0x51, 0x58, 0x25, 0x63, 0xf0, 0xfc, 0xd4,
	0x09, 0xfe, 0xad, 0x13, 0x79, 0x26, 0xe2, 0x14, 0x33, 0x11, 0x87, 0x99, 0xde, 0x0c, 0x08, 0x0e,
	0xf9, 0x56, 0x96, 0x84, 0xe9, 0x05, 0xa1, 0x4f, 0xd1, 0x33, 0x58, 0x91, 0x98, 0x82, 0xeb, 0xbf,
	0xf2, 0xf4, 0xa6, 0x7a, 0x66, 0x2e, 0xa2, 0x47, 0xb2, 0xda, 0x03, 0xee, 0xdc, 0x49, 0x87, 0x54,
	0x1e, 0x61, 0xed, 0xaf, 0x73, 0xdc, 0x9d, 0xd3, 0x82, 0xd4, 0x47, 0x3f, 0x81, 0x22, 0x1b, 0x98,
	0x8b, 0x56, 0x9e, 0xde, 0x99, 0xe9, 0x6c, 0x51, 0x1f, 0x2e, 0x8d, 0xfe, 0x00, 0x3e, 0x96, 0x46,
	0xc1, 0x0e, 0x09, 0x42, 0x83, 0x86, 0x38, 0x1c, 0x51, 0xe3, 0x9c, 0x10, 0xdf, 0x18, 0x7a, 0xa3,
	0x40, 0x46, 0x88, 0x1d, 0x21, 0xd4, 0x62, 0x32, 0x3d, 0x2e, 0xf2, 0x92, 0x10, 0xff, 0x85, 0x37,
	0x0a, 0xb4, 0xcf, 0xa1, 0x21, 0x97, 0xa3, 0x13, 0x8b, 0x5c, 0x70, 0x10, 0x37, 0x7b, 0xf5, 0x3e,
	0xec, 0xcc, 0x90, 0xa6, 0x3e, 0xea, 0xc1, 0x27, 0x43, 0xcc, 0xd2, 0x8c, 0x3b, 0x70, 0x48, 0xc2,
	0x93, 0xdf, 0x63, 0x3b, 0xb4, 0xdd, 0x81, 0x11, 0xc4, 0x5d, 0xf8, 0x90, 0x25, 0x5d, 0x1b, 0x62,
	0xda, 0xe3, 0xd2, 0x91, 0x77, 0xbf, 0x11, 0xa2, 0x93, 0xc1, 0xb5, 0x01, 0x6c, 0xca, 0x19, 0xa5,
	0xca, 0x0f, 0x6c, 0x1a, 0xaa, 0xa3, 0xe3, 0x4d, 0x28, 0x5f, 0xe0, 0xe0, 0x9c, 0x84, 0x93, 0xd0,
	0x58, 0x12, 0x04, 0x11, 0xb1, 0x4d, 0xc7, 0x26, 0x6e, 0x68, 0x84, 0x63, 0x9f, 0xc8, 0xd8, 0x08,
	0x82, 0xd4, 0x1f, 0xfb, 0x44, 0xfb, 0x0e, 0xb6, 0x54, 0x13, 0x51, 0x1f, 0xed, 0xc1, 0x6a, 0x84,
	0x3b, 0x1d, 0x9b, 0x86, 0x8d, 0x1c, 0x8f, 0x07, 0xb7, 0x67, 0x99, 0x28, 0x72, 0x8d, 0x8a, 0x3f,
	0x19, 0x47, 0xfb, 0x87, 0x22, 0x54, 0x12, 0xcc, 0x0c, 0x96, 0xcd, 0x65, 0xb1, 0x2c, 0x82, 0xa2,
	0x8b, 0xe3, 0xa8, 0xca, 0x7f, 0x33, 0x9a, 0x45, 0xa8, 0x29, 0x01, 0x0b, 0xff, 0x8d, 0x3e, 0x07,
	0x64, 0x8e, 0x82, 0x80, 0x6d, 0x2b, 0x01, 0x41, 0x44, 0x92, 0xae, 0x4b, 0xce, 0x04, 0x86, 0xec,
	0xc2, 0x8d, 0x18, 0xe0, 0x4e, 0x21, 0x96, 0xf5, 0x88, 0x35, 0x91, 0xbf, 0x0f, 0xb5, 0x38, 0x82,
	0x39, 0xf8, 0x94, 0x38, 0x32, 0x22, 0x55, 0x23, 0xea, 0x01, 0x23, 0x32, 0xb4, 0x64, 0x61, 0xdb,
	0x49, 0xa1, 0x20, 0x01, 0x66, 0x6a, 0x9c, 0x3e, 0x19, 0x70, 0x1a, 0x2d, 0x95, 0x14, 0x68, 0xe9,
	0x0e, 0x54, 0xc8, 0x07, 0xdf, 0xc1, 0x2e, 0xe6, 0xbe, 0x52, 0xe6, 0x73, 0x26, 0x49, 0x12, 0xcf,
	0xe0, 0x51, 0xe8, 0xf1, 0x48, 0xc4, 0xf1, 0x0c, 0x4b, 0xe8, 0xe8, 0x05, 0xd4, 0x13, 0xa1, 0x43,
	0x98, 0xab, 0x72, 0xa7, 0xb0, 0x40, 0xfc, 0xa8, 0x4d, 0xe2, 0x07, 0x33, 0x58, 0x06, 0x46, 0xac,
	0x66, 0x41, 0xcd, 0x4b, 0xa8, 0xd1, 0xa1, 0xf7, 0xde, 0x30, 0x3d, 0xd7, 0xb2, 0xf9, 0x32, 0xab,
	0xfc, 0xe0, 0x2a, 0xb3, 0xc4, 0x73, 0x9b, 0xfa, 0x0e, 0x66, 0xf8, 0x46, 0xc8, 0xea, 0x55, 0xd6,
	0x37, 0x6e, 0x6a, 0x3f, 0xe7, 0x3e, 0x7e, 0x3c, 0x0a, 0xcc, 0x21, 0xa6, 0xe4, 0x85, 0x4d, 0x43,
	0x2f, 0x18, 0xab, 0x7d, 0x1c, 0x41, 0xd1, 0x67, 0xa1, 0x49, 0xb8, 0x37, 0xff, 0xcd, 0xfc, 0x9e,
	0xfd, 0x35, 0xa8, 0xfd, 0x8b, 0xc8, 0xb1, 0x4b, 0x8c, 0xd0, 0xb3, 0x7f, 0x41, 0xb4, 0x3f, 0xe5,
	0x6e, 0x3d, 0x35, 0x36, 0xf5, 0xd1, 0x3e, 0x54, 0x7d, 0x49, 0x4e, 0xfa, 0xb5, 0xa6, 0x54, 0x94,
	0x14, 0xd4, 0x89, 0xe9, 0x05, 0x96, 0xbe, 0x1a, 0x75, 0xe4, 0xaa, 0xda, 0x81, 0x12, 0x3b, 0xf7,
	0x17, 0x5e, 0x40, 0x24, 0xbc, 0x5c, 0x19, 0x62, 0xfa, 0xca, 0x0b, 0x88, 0xf6, 0x57, 0x0c, 0xbe,
	0xa7, 0xfa, 0xce, 0x43, 0x55, 0x08, 0x8a, 0x09, 0x2c, 0xc1, 0x7f, 0x2b, 0xbd, 0x7e, 0x03, 0x96,
	0xc4, 0x47, 0x8b, 0xc0, 0xe4, 0xa2, 0x81, 0x7e, 0x08, 0xab, 0x6c, 0x19, 0x01, 0x39, 0x1b, 0xb9,
	0x16, 0x11, 0xa9, 0xb6, 0xa4, 0x57, 0x86, 0x98, 0xea, 0x92, 0xa4, 0xfd, 0x5d, 0x11, 0x56, 0xa2,
	0x13, 0x58, 0x83, 0x7c, 0xac, 0xda, 0xbc, 0x7d, 0x15, 0x6e, 0x8c, 0x4f, 0x64, 0x41, 0x71, 0x22,
	0x8b, 0x89, 0xb5, 0x31, 0x1a, 0x03, 0x5f, 0xe2, 0x50, 0xf1, 0xdf, 0xb3, 0xce, 0xdd, 0xf2, 0xac,
	0x73, 0xc7, 0x97, 0x92, 0x39, 0x4a, 0x65, 0x3f, 0xc9, 0xb6, 0xa9, 0x41, 0x5c, 0x7c, 0xea, 0x10,
	0x8b, 0x9f, 0xa0, 0x92, 0x5e, 0xb6, 0x69, 0x47, 0x10, 0x50, 0x13, 0x4a, 0x9e, 0xcf, 0x90, 0x97,
	0x17, 0xc8, 0xb3, 0x13, 0xb7, 0xd3, 0x21, 0x12, 0x32, 0x21, 0x72, 0x07, 0x4a, 0x83, 0xc0, 0x1b,
	0xf9, 0x8c, 0x57, 0x11, 0xd8, 0x91, 0xb7, 0xbb, 0x3c, 0xb4, 0x8e, 0x7c, 0x4b, 0x66, 0x4c, 0x76,
	0x18, 0x8a, 0x7a, 0x49, 0x10, 0x38, 0x58, 0x8a, 0xe3, 0x23, 0x8f, 0xad, 0x55, 0x9e, 0xb0, 0x6f,
	0xcf, 0x49, 0x9b, 0x2c, 0xe0, 0xc6, 0xf1, 0x91, 0x35, 0x98, 0x8a, 0xe2, 0x50, 0x93, 0xd8, 0x7b,
	0x4d, 0xa8, 0x28, 0x62, 0xcd, 0x0b, 0x4d, 0x6b, 0xaa, 0xd0, 0x74, 0x17, 0x62, 0x82, 0xc1, 0x4d,
	0x55, 0x17, 0x5f, 0x7b, 0x11, 0xf1, 0x39, 0xa1, 0xa6, 0x76, 0x04, 0xd5, 0x96, 0x65, 0x45, 0x61,
	0x9b, 0xbc, 0x43, 0xbf, 0x3f, 0xd9, 0x90, 0xed, 0x9e, 0x79, 0x32, 0x27, 0xcf, 0xc5, 0x01, 0xd1,
	0x66, 0x58, 0x32, 0xd4, 0xea, 0x50, 0x4b, 0x0e, 0x48, 0x7d, 0xed, 0xcf, 0x61, 0xeb, 0x84, 0xab,
	0x4b, 0x12, 0x45, 0x12, 0x96, 0x10, 0x3b, 0x61, 0xcc, 0x5c, 0xd6, 0x98, 0xc2, 0x4b, 0xf3, 0xb1,
	0x97, 0xee, 0x40, 0xc9, 0xa6, 0x06, 0x37, 0x0b, 0x77, 0xc5, 0x92, 0xbe, 0x62, 0xd3, 0x7d, 0xd6,
	0x4c, 0xd9, 0xbd, 0x98, 0xb6, 0xbb, 0xb6, 0x03, 0xdb, 0xca, 0xf9, 0xf9, 0xd2, 0x58, 0x4a, 0x4f,
	0xe4, 0xbc, 0xbd, 0xf1, 0xc2, 0xab, 0xcb, 0x5a, 0x3e, 0x7f, 0x7d, 0xcb, 0x6b, 0xff, 0x92, 0x03,
	0x78, 0x65, 0x7f, 0x88, 0x8e, 0x65, 0x72, 0x83, 0xb9, 0xf4, 0x06, 0xb3, 0x66, 0xc9, 0x5f, 0xcf,
	0x2c, 0xa8, 0x03, 0x55, 0xe1, 0xdf, 0x11, 0xbe, 0x2b, 0xcc, 0xc6, 0x5a, 0x7c, 0xc6, 0x68, 0x94,
	0xd5, 0x41, 0xa2, 0xa5, 0x19, 0xd0, 0x9c, 0xa5, 0x30, 0xea, 0xa3, 0x96, 0x12, 0x2c, 0x28, 0xb3,
	0xcf, 0x64, 0xd7, 0x69, 0xac, 0xf0, 0xcf, 0x79, 0xa8, 0x67, 0x53, 0x06, 0xc7, 0xac, 0x1e, 0xfb,
	0xd8, 0x62, 0x7a, 0x16, 0x51, 0xab, 0xc4, 0x08, 0xfc, 0xf4, 0x7c, 0x03, 0x15, 0xfe, 0x49, 0x28,
	0xe0, 0x5f, 0x23, 0x3f, 0x3b, 0xe3, 0x31, 0xfc, 0x28, 0x57, 0x0b, 0x66, 0xfc, 0x9b, 0x25, 0xd4,
	0x11, 0x25, 0x81, 0xe1, 0x5c, 0xca, 0x04, 0xb2, 0xcc, 0x9a, 0x07, 0x97, 0x8c, 0x11, 0xd8, 0xe6,
	0x90, 0x31, 0x04, 0xaa, 0x58, 0x66, 0xcd, 0x83, 0x4b, 0xf1, 0x05, 0x7c, 0x6a, 0x3b, 0x76, 0x38,
	0x66, 0x4c, 0x11, 0xee, 0x20, 0x22, 0x1d, 0x5c, 0xb2, 0xcf, 0x1f, 0x9f, 0x78, 0xbe, 0x43, 0x84,
	0x31, 0x85, 0x36, 0x96, 0x79, 0x19, 0x6d, 0x4d, 0x30, 0xb8, 0x8e, 0xf9, 0x8e, 0xbf, 0x84, 0x52,
	0x3b, 0xda, 0xcb, 0x3a, 0x54, 0x3b, 0x87, 0x27, 0xaf, 0x8c, 0x93, 0x5e, 0x47, 0x37, 0x5a, 0x07,
	0x07, 0xf5, 0x1f, 0xa0, 0x6d, 0xb8, 0x31, 0x21, 0xf5, 0x8e, 0x3b, 0xed, 0xee, 0xb7, 0xdd, 0xce,
	0xf3, 0x7a, 0x4e, 0xfb, 0xef, 0x02, 0x54, 0x7a, 0xd8, 0x89, 0xbc, 0x9a, 0xad, 0x96, 0x62, 0x27,
	0x01, 0xa9, 0x96, 0x59, 0xb3, 0x6b, 0xfd, 0xd6, 0xae, 0xb3, 0x03, 0xa5, 0x53, 0x32, 0xb0, 0xdd,
	0xe8, 0x5b, 0xb1, 0xa8, 0xaf, 0xf0, 0x76, 0x9f, 0xa2, 0x4d, 0x58, 0xe6, 0x1f, 0xc1, 0xe2, 0x3b,
	0xa3, 0xa8, 0x2f, 0xb1, 0x4f, 0xe0, 0xe8, 0x0b, 0x52, 0x62, 0x83, 0xa5, 0x6b, 0x60, 0x83, 0x49,
	0x37, 0xb4, 0x05, 0xcb, 0xef, 0x89, 0x3d, 0x18, 0x46, 0xa9, 0x42, 0xb6, 0xd0, 0xb7, 0x50, 0xe1,
	0xdb, 0x94, 0xe6, 0x5e, 0xe1, 0xa7, 0xee, 0xbe, 0x6a, 0xf4, 0x84, 0x72, 0x22, 0xab, 0xb3, 0x9e,
	0xd2, 0xea, 0x1b, 0xb0, 0x24, 0x62, 0x67, 0x49, 0xe4, 0x51, 0xde, 0xb8, 0x2a, 0x7f, 0x4c, 0xf2,
	0x00, 0x64, 0xf2, 0x40, 0x03, 0x56, 0x02, 0xc2, 0xb2, 0x09, 0xe5, 0xe9, 0xa3, 0xac, 0x47, 0xcd,
	0x54, 0x66, 0x59, 0x9d, 0xca, 0x2c, 0x93, 0x8c, 0x54, 0x4d, 0x67, 0x24, 0xed, 0x2f, 0x58, 0x6c,
	0x10, 0x0d, 0x66, 0x85, 0x94, 0x6c, 0x2e, 0x93, 0xbd, 0xae, 0xc8, 0xdf, 0x69, 0xc0, 0x5d, 0xc8,
	0x02, 0xee, 0xd4, 0xc6, 0x8a, 0xe9, 0x8d, 0x69, 0xff, 0x97, 0x87, 0xd5, 0x64, 0x40, 0x48, 0xed,
	0x27, 0x97, 0xde, 0xcf, 0xf7, 0x10, 0x12, 0x17, 0xc6, 0x1a, 0xd7, 0xc5, 0xf3, 0x69, 0x5c, 0xb1,
	0x9c, 0xc5, 0x15, 0x11, 0x74, 0x61, 0xfe, 0xb4, 0x24, 0xa1, 0xcb, 0x37, 0x50, 0x91, 0x2a, 0xe7,
	0xe7, 0xb7, 0x34, 0x27, 0x9a, 0xc5, 0x76, 0xd2, 0x41, 0x74, 0x89, 0x70, 0x74, 0x22, 0x83, 0x94,
	0xe7, 0x81, 0x15, 0xc8, 0x24, 0xad, 0x11, 0x6c, 0xef, 0xe1, 0xd0, 0x1c, 0xb6, 0x2c, 0x2b, 0x15,
	0x8e, 0xc9, 0x3b, 0xf4, 0x0d, 0x40, 0x22, 0xaa, 0x88, 0x18, 0x7b, 0x75, 0x1c, 0x2f, 0x0f, 0xa2,
	0x88, 0x23, 0xd3, 0x8c, 0x39, 0x24, 0xe6, 0x79, 0x84, 0x59, 0x6d, 0xda, 0x66, 0x4d, 0xad, 0x09,
	0x0d, 0xf5, 0xb4, 0xd4, 0xe7, 0x5f, 0xef, 0x1d, 0xcb, 0x0e, 0xb3, 0xeb, 0x99, 0xe3, 0x10, 0x19,
	0x0d, 0xe6, 0xaf, 0xad, 0xc1, 0xa4, 0x8a, 0x0a, 0x19, 0x15, 0x6d, 0xc1, 0xc6, 0xf4, 0x72, 0xa8,
	0xaf, 0xe9, 0x50, 0x7f, 0xe5, 0x59, 0xf6, 0xd9, 0xf8, 0x7b, 0x44, 0x35, 0x37, 0x60, 0x3d, 0x33,
	0x26, 0xaf, 0x9a, 0x6e, 0x0a, 0x62, 0x56, 0x23, 0x53, 0xc9, 0x36, 0xf7, 0x1b, 0x25, 0xdb, 0x06,
	0x6c, 0xa9, 0xc6, 0xa7, 0xbe, 0x66, 0xc2, 0x7a, 0xcb, 0xb2, 0x12, 0x01, 0x8e, 0xcd, 0xfa, 0x05,
	0x14, 0x13, 0x7b, 0xbb, 0x7d, 0x45, 0x48, 0xd4, 0xb9, 0xf0, 0x3c, 0x5f, 0xd8, 0x07, 0x94, 0x9d,
	0x84, 0xfa, 0xe8, 0xc7, 0xb0, 0x89, 0x9d, 0x80, 0x60, 0x6b, 0x6c, 0x78, 0xae, 0x41, 0x87, 0xc4,
	0x39, 0x9b, 0x38, 0x62, 0x55, 0x47, 0x92, 0x79, 0xe4, 0xf6, 0x18, 0x8b, 0x67, 0xb8, 0x97, 0xb0,
	0x21, 0x00, 0xd8, 0xf7, 0xb0, 0x60, 0x6d, 0x1b, 0x36, 0x15, 0x83, 0x71, 0x6b, 0x7c, 0xbc, 0x4f,
	0xc2, 0x04, 0x35, 0x8b, 0xe7, 0x7e, 0x06, 0xcb, 0x32, 0x69, 0xe4, 0xae, 0x93, 0x34, 0x64, 0x27,
	0xcd, 0x82, 0x5b, 0xf3, 0xc6, 0xbf, 0x5e, 0xad, 0x24, 0xb9, 0xf8, 0x14, 0xfe, 0xf9, 0x12, 0x50,
	0x72, 0x09, 0x5e, 0xc0, 0xeb, 0x3d, 0x77, 0x60, 0x55, 0xe6, 0xf6, 0xa4, 0xae, 0x41, 0x24, 0x78,
	0xde, 0x6f, 0x13, 0x6e, 0x4c, 0xf5, 0x13, 0x67, 0xa1, 0x75, 0xea, 0x8d, 0xc2, 0xbe, 0x27, 0x2a,
	0x95, 0xed, 0xb3, 0x01, 0x0b, 0x75, 0xb6, 0x29, 0xeb, 0x50, 0x65, 0x9d, 0xff, 0x66, 0x80, 0x25,
	0x55, 0x4b, 0xe3, 0x9f, 0xa2, 0x6b, 0xfc, 0x30, 0xaf, 0x25, 0xea, 0x67, 0xbc, 0xbc, 0x7d, 0x1f,
	0xee, 0xee, 0x93, 0x30, 0x59, 0x4b, 0x67, 0xc0, 0xaa, 0xed, 0x5d, 0x5c, 0x78, 0x6e, 0xfb, 0x6c,
	0xa0, 0x93, 0x77, 0x23, 0x42, 0x43, 0xed, 0x2f, 0x57, 0xe0, 0xde, 0x7c, 0x39, 0xea, 0x7b, 0x2e,
	0x25, 0xe8, 0x13, 0xa8, 0xbf, 0xc7, 0x76, 0x68, 0x84, 0x1e, 0xaf, 0x9b, 0x25, 0xd6, 0x56, 0x65,
	0xf4, 0xbe, 0xb7, 0x37, 0x1a, 0x77, 0xd9, 0x22, 0x1f, 0x42, 0x3d, 0x72, 0xbd, 0x58, 0x50, 0xe4,
	0xba, 0x9a, 0xa4, 0x47, 0x92, 0xdf, 0x41, 0x03, 0xb3, 0x6d, 0xb3, 0x31, 0xe5, 0xbe, 0xcc, 0xb3,
	0x81, 0xd0, 0x5d, 0x61, 0x76, 0x45, 0x3b, 0xab, 0x2a, 0x7d, 0x03, 0x67, 0x28, 0x3c, 0x28, 0x7d,
	0x0d, 0x4d, 0x51, 0xb0, 0x26, 0x6e, 0x48, 0x02, 0x43, 0xa2, 0x4f, 0x2f, 0x20, 0xc6, 0x28, 0x88,
	0xee, 0xca, 0xb6, 0xb8, 0x44, 0x87, 0x09, 0x08, 0xd8, 0xe9, 0x05, 0xe4, 0x24, 0x70, 0x18, 0x4a,
	0xc3, 0x96, 0x11, 0x92, 0x0f, 0xa1, 0x2c, 0x87, 0x2f, 0x63, 0xab, 0x4f, 0x3e, 0xb0, 0x8f, 0xba,
	0x35, 0xd7, 0xb0, 0xf0, 0xd8, 0xe0, 0xa5, 0x15, 0xcf, 0x35, 0x89, 0x4c, 0x52, 0xab, 0xee, 0x73,
	0x3c, 0xee, 0x0d, 0xbd, 0xf7, 0x47, 0xae, 0xc9, 0x6f, 0x2e, 0xd9, 0x4e, 0x2e, 0x49, 0x40, 0x19,
	0xb8, 0x5a, 0x91, 0xa5, 0xbc, 0xb3, 0xc1, 0x6b, 0x41, 0x41, 0x77, 0xa1, 0x96, 0xd4, 0xe6, 0xe9,
	0x40, 0x02, 0x9c, 0x4a, 0xac, 0xcb, 0xbd, 0x01, 0xba, 0x07, 0xb5, 0xa4, 0x26, 0x4f, 0x07, 0x12,
	0xec, 0xac, 0x4e, 0xf4, 0xb8, 0x37, 0x40, 0x9f, 0x01, 0xca, 0x6a, 0xf1, 0x74, 0x20, 0x33, 0xd5,
	0x5a, 0x4a, 0x33, 0x7b, 0x03, 0xf4, 0x15, 0xec, 0x08, 0x1d, 0x04, 0x84, 0xda, 0x16, 0x71, 0xb9,
	0x76, 0x02, 0x69, 0x25, 0x01, 0x89, 0xb6, 0xb8, 0x80, 0x2e, 0xf9, 0x1d, 0xc6, 0xe6, 0xd6, 0x7a,
	0x06, 0xdb, 0xa2, 0x6b, 0x88, 0x4f, 0xb9, 0xbc, 0x41, 0x89, 0x43, 0xcc, 0x90, 0x44, 0xb5, 0xa7,
	0x0d, 0xce, 0xee, 0xe3, 0x53, 0x26, 0xde, 0x93, 0xbc, 0xc9, 0x8c, 0x71, 0xb7, 0x91, 0x1b, 0x77,
	0xac, 0x26, 0x66, 0x94, 0x1d, 0x4f, 0x62, 0xae, 0xda, 0xdd, 0x6b, 0x4a, 0x77, 0x9f, 0x63, 0xed,
	0x0b, 0xeb, 0x99, 0xfc, 0xf2, 0x56, 0x59, 0xfb, 0x95, 0xf5, 0x0c, 0x3d, 0x84, 0x75, 0xdf, 0x34,
	0x32, 0xf6, 0x10, 0x9f, 0xe1, 0x55, 0xdf, 0x7c, 0x93, 0xb0, 0xc8, 0xa7, 0x5c, 0x32, 0x63, 0x94,
	0x75, 0xe1, 0xdc, 0xbe, 0xd9, 0x4a, 0x9a, 0xe5, 0x47, 0xb0, 0xc9, 0x44, 0xa7, 0x2d, 0x83, 0xb8,
	0xf8, 0xba, 0x6f, 0xb6, 0xd2, 0xb6, 0xd1, 0xbe, 0x82, 0xfb, 0x8a, 0x93, 0xc8, 0x0d, 0x10, 0x87,
	0x47, 0x76, 0x66, 0x15, 0x45, 0xef, 0xff, 0xca, 0xc3, 0x83, 0xab, 0xfa, 0xca, 0x73, 0x7c, 0x9b,
	0x97, 0x2e, 0xf9, 0x25, 0x49, 0x74, 0x63, 0x56, 0xd5, 0x41, 0x5e, 0x2b, 0x31, 0x4d, 0xde, 0x83,
	0x9a, 0xe3, 0xbd, 0x97, 0x68, 0x8d, 0x1f, 0x01, 0x71, 0x7a, 0x57, 0x1d, 0xef, 0x3d, 0x07, 0x6a,
	0xfc, 0x20, 0x6c, 0xc2, 0x32, 0xb6, 0x0c, 0xdb, 0xfa, 0x20, 0x81, 0xea, 0x12, 0xb6, 0xba, 0xd6,
	0x87, 0xac, 0xe3, 0x17, 0xa7, 0x1c, 0x7f, 0x0b, 0x96, 0xe9, 0x7b, 0x3b, 0x34, 0x87, 0xb2, 0xf8,
	0x25, 0x5b, 0xaa, 0x9b, 0x9e, 0xe5, 0x05, 0x6f, 0x7a, 0x56, 0x14, 0x37, 0x3d, 0xd3, 0x37, 0x50,
	0xa5, 0x05, 0x6e, 0xa0, 0xca, 0x53, 0x37, 0x50, 0xda, 0x2f, 0x0b, 0xb0, 0xd9, 0x32, 0x43, 0xfb,
	0xd2, 0x0e, 0xc7, 0xbf, 0xe3, 0xaf, 0x3f, 0xd0, 0x17, 0xb0, 0x75, 0x86, 0x4d, 0x62, 0xe0, 0x51,
	0x38, 0x64, 0x01, 0x62, 0xe4, 0x30, 0xd7, 0x3d, 0x27, 0xae, 0x2c, 0xaa, 0xdf, 0x38, 0x13, 0xb7,
	0xd2, 0x43, 0x9d, 0xf3, 0xfa, 0xfc, 0xb9, 0x47, 0xe6, 0xd6, 0x62, 0x25, 0x7b, 0x6b, 0x91, 0xfe,
	0x24, 0x2a, 0x65, 0x3e, 0x89, 0xee, 0x43, 0x4d, 0xf6, 0x8e, 0x5c, 0xa6, 0xcc, 0x25, 0xaa, 0x82,
	0x2a, 0xbd, 0x46, 0xfb, 0xc7, 0x3c, 0x6c, 0xa9, 0xac, 0xf0, 0x3b, 0xf7, 0x3e, 0x25, 0x6d, 0x03,
	0xd3, 0x73, 0xd9, 0x01, 0x34, 0xfe, 0x84, 0x7a, 0xae, 0x4c, 0x10, 0xb1, 0x0d, 0xda, 0x82, 0xf7,
	0x87, 0xd4, 0x73, 0xb5, 0xaf, 0x44, 0x05, 0x3d, 0x09, 0x78, 0xba, 0x16, 0x95, 0x57, 0xd7, 0x13,
	0x97, 0xa4, 0x11, 0x22, 0x89, 0x7d, 0x92, 0x6a, 0x6f, 0x61, 0x5b, 0xd9, 0x95, 0xa6, 0x8a, 0x59,
	0x09, 0xa0, 0xb4, 0x10, 0x1a, 0x67, 0x03, 0x3d, 0xa2, 0x00, 0x89, 0x67, 0x28, 0x37, 0x61, 0xfb,
	0xb8, 0xf5, 0xd6, 0x68, 0xbf, 0x68, 0x1d, 0x1e, 0x76, 0x0e, 0x8c, 0x93, 0xc3, 0x49, 0x95, 0xe4,
	0x07, 0x68, 0x0b, 0x50, 0x92, 0xd9, 0x3a, 0xe8, 0x1e, 0xb7, 0xde, 0xd6, 0x73, 0x59, 0xfa, 0x9b,
	0x4e, 0xfb, 0x45, 0xab, 0x5f, 0xcf, 0xa3, 0x06, 0x6c, 0xa4, 0xe4, 0x8f, 0x8f, 0x7b, 0xfd, 0x23,
	0xbd, 0x53, 0x2f, 0x3c, 0xc2, 0x50, 0x49, 0x7c, 0xb4, 0xa2, 0x8f, 0xa0, 0x71, 0xdc, 0x6a, 0xbf,
	0x6c, 0xed, 0x77, 0x8c, 0xfe, 0xdb, 0xe3, 0x4e, 0x66, 0xda, 0x6d, 0xb8, 0x91, 0xe2, 0x1e, 0x1e,
	0xe9, 0xaf, 0x5a, 0x07, 0xf5, 0x9c, 0x58, 0x6c, 0x82, 0xd1, 0x3a, 0xe9, 0x1f, 0x19, 0x7a, 0xe7,
	0xb0, 0xf3, 0xa6, 0x9e, 0x7f, 0xf4, 0xaf, 0x39, 0x58, 0x9f, 0x02, 0xa0, 0xe8, 0x21, 0xdc, 0xe3,
	0x15, 0xa0, 0x5e, 0xeb, 0xa0, 0x63, 0x44, 0x9d, 0x7b, 0xfd, 0x56, 0xff, 0xa4, 0x97, 0x99, 0x55,
	0x83, 0x5b, 0x33, 0x25, 0x7b, 0x2f, 0x3a, 0x07, 0xdf, 0xd6, 0x73, 0xe8, 0x53, 0xb8, 0x3f, 0x53,
	0xe6, 0xf0, 0xa8, 0x6f, 0x1c, 0x1d, 0x4a, 0xd1, 0x3c, 0xfa, 0x04, 0xee, 0xce, 0x14, 0x7d, 0xd3,
	0xea, 0xf6, 0xa5, 0x60, 0xe1, 0xd1, 0x3f, 0xe5, 0x00, 0xda, 0xc9, 0x82, 0x9a, 0x28, 0x59, 0xc5,
	0xa3, 0x1a, 0x47, 0xc7, 0x9d, 0xc3, 0x44, 0x2d, 0x4b, 0x32, 0x18, 0xb5, 0x7b, 0xb8, 0x2f, 0xb4,
	0x92, 0x64, 0xf4, 0x8e, 0x8e, 0x0e, 0x8d, 0xce, 0x1f, 0x1d, 0x77, 0xf5, 0x4e, 0x3d, 0x9f, 0xed,
	0x25, 0xe8, 0xcf, 0xeb, 0x05, 0xb4, 0x01, 0xf5, 0x54, 0xaf, 0xee, 0xfe, 0x61, 0xbd, 0x88, 0x36,
	0x61, 0x3d, 0x49, 0xed, 0xeb, 0xdd, 0xd6, 0x41, 0x7d, 0xe9, 0xe9, 0x7f, 0xee, 0x40, 0x3d, 0x9b,
	0xc5, 0x50, 0x0f, 0x60, 0x72, 0xe8, 0xd1, 0x0f, 0x95, 0x0e, 0x98, 0x0c, 0xcd, 0x4d, 0xed, 0x2a,
	0x11, 0xea, 0xa3, 0xd7, 0x50, 0x49, 0xbc, 0xe4, 0x42, 0x9a, 0xba, 0x14, 0x99, 0x7c, 0x29, 0xd6,
	0xbc, 0x7b, 0xa5, 0x8c, 0x18, 0x37, 0xf1, 0x24, 0x4a, 0x3d, 0x6e, 0xfa, 0xb9, 0x98, 0x7a, 0xdc,
	0xcc, 0xbb, 0x2a, 0x64, 0x40, 0x2d, 0xfd, 0x06, 0x09, 0x29, 0xbf, 0x8c, 0xa6, 0x9e, 0x5e, 0x35,
	0x1f, 0x2c, 0x22, 0x46, 0x7d, 0x34, 0x84, 0xf5, 0xa9, 0x67, 0x47, 0xe8, 0xe1, 0x4c, 0x4d, 0x66,
	0x5e, 0x3d, 0x35, 0x3f, 0x5d, 0x50, 0x52, 0xa8, 0x28, 0xf1, 0xce, 0x48, 0xad, 0xa2, 0xf4, 0x43,
	0x27, 0xb5, 0x8a, 0x32, 0x8f, 0x95, 0xd0, 0x5b, 0x58, 0x4d, 0xbe, 0xe8, 0x41, 0xca, 0x4e, 0x99,
	0x07, 0x44, 0xcd, 0x7b, 0x57, 0x0b, 0x51, 0x1f, 0x9d, 0xc2, 0x5a, 0xe6, 0x85, 0x0f, 0x7a, 0xa0,
	0xee, 0x98, 0x7d, 0x3d, 0xd4, 0xfc, 0x64, 0x21, 0x39, 0xea, 0xa3, 0x3f, 0x83, 0xc6, 0xac, 0xd7,
	0x4f, 0xe8, 0xc9, 0x8c, 0xab, 0xfc, 0x59, 0x2f, 0xb3, 0x9a, 0x3f, 0xba, 0x5e, 0x07, 0xea, 0xa3,
	0x97, 0x3c, 0x89, 0xc4, 0xb4, 0xbe, 0x17, 0x62, 0xa7, 0xed, 0x8d, 0xdc, 0x10, 0xed, 0xec, 0xea,
	0xd1, 0x73, 0xd8, 0xd7, 0x4f, 0x77, 0x19, 0x48, 0xd4, 0xb1, 0xcb, 0x8b, 0x03, 0xcd, 0xad, 0x14,
	0x8b, 0x8b, 0xf3, 0xc1, 0x0e, 0x60, 0x23, 0x39, 0x98, 0x7c, 0x17, 0x44, 0xe7, 0x0d, 0x95, 0x66,
	0x45, 0x3d, 0xf8, 0x68, 0xaf, 0x60, 0x53, 0x27, 0x3e, 0xf3, 0xa5, 0xf4, 0x88, 0xe8, 0xa3, 0x54,
	0x1f, 0x29, 0x13, 0x3b, 0x4d, 0x7a, 0x71, 0x9d, 0x0b, 0x3f, 0x1c, 0x27, 0x8c, 0x99, 0x7a, 0x99,
	0x35, 0xcb, 0x98, 0x99, 0xd7, 0x32, 0x33, 0x8d, 0x39, 0xf5, 0x58, 0x26, 0x8c, 0x1f, 0x85, 0xa4,
	0x9f, 0xa1, 0xa0, 0xcf, 0xe7, 0x8c, 0x30, 0xf5, 0xbe, 0xa5, 0xf9, 0xf8, 0x1a, 0xd2, 0xd4, 0x47,
	0xe7, 0xf1, 0x13, 0x9f, 0x44, 0x46, 0x47, 0x9f, 0xce, 0x19, 0x24, 0xfd, 0x64, 0xa5, 0xf9, 0x68,
	0x51, 0xd1, 0x78, 0xb2, 0xcc, 0xbd, 0xfd, 0xcc, 0xc9, 0xa6, 0xdf, 0x0e, 0xcc, 0x9c, 0x4c, 0xf5,
	0x14, 0xe0, 0x1c, 0xd0, 0x34, 0x00, 0x54, 0x4f, 0xa6, 0x84, 0xeb, 0xea, 0xc9, 0x66, 0x60, 0x4a,
	0x97, 0x3f, 0x80, 0xca, 0x82, 0x22, 0x34, 0x73, 0xbd, 0xd3, 0xc0, 0xab, 0xf9, 0xd9, 0xc2, 0xb2,
	0xfc, 0x59, 0x12, 0x4c, 0x6e, 0x63, 0xd5, 0x09, 0x2e, 0x75, 0xfd, 0xab, 0x4e, 0x70, 0xe9, 0x0b,
	0x5d, 0xf4, 0x0e, 0x36, 0x54, 0x45, 0x62, 0xa4, 0x5c, 0xd9, 0x8c, 0x2a, 0x76, 0xf3, 0xf3, 0xc5,
	0x85, 0xa9, 0x8f, 0xbe, 0x83, 0x6a, 0xaa, 0xfe, 0x8a, 0x94, 0xc1, 0x35, 0x5b, 0xf6, 0x6d, 0xde,
	0x5f, 0x40, 0x4a, 0x58, 0x45, 0x71, 0x43, 0xac, 0xb6, 0x8a, 0xfa, 0x2a, 0x5b, 0x6d, 0x95, 0x19,
	0xd7, 0xce, 0xe8, 0xfd, 0x34, 0xaa, 0x96, 0x53, 0x3e, 0x5e, 0xc4, 0xb8, 0x93, 0x59, 0x77, 0xaf,
	0x23, 0x4e, 0x7d, 0x44, 0xa0, 0x9e, 0x2d, 0x99, 0x23, 0x65, 0xe0, 0x51, 0xd4, 0xf9, 0x9b, 0x0f,
	0x17, 0x13, 0x14, 0x47, 0x6a, 0xba, 0x70, 0xad, 0x3e, 0x52, 0xca, 0x02, 0xba, 0xfa, 0x48, 0xa9,
	0x6b, 0xe1, 0x0c, 0xbe, 0xa4, 0xcb, 0xd4, 0x6a, 0xf8, 0x32, 0x55, 0x2f, 0x57, 0xc3, 0x17, 0x45,
	0xc5, 0x7b, 0x08, 0xeb, 0x53, 0x15, 0x67, 0x35, 0x7c, 0x51, 0x55, 0xb9, 0xd5, 0xf0, 0x45, 0x59,
	0xc2, 0x46, 0xbf, 0xcc, 0xf1, 0xeb, 0xf5, 0x19, 0x35, 0x66, 0xf4, 0xe3, 0x19, 0xd6, 0x9e, 0x5d,
	0xf3, 0x6e, 0x3e, 0xbd, 0x6e, 0x17, 0x91, 0xc4, 0x32, 0xa5, 0x64, 0x75, 0x12, 0x9b, 0xae, 0x53,
	0xab, 0x93, 0x98, 0xa2, 0x2e, 0x8d, 0xfe, 0x36, 0x07, 0x1f, 0xcd, 0x2b, 0x0e, 0xa3, 0xdf, 0x9b,
	0xb1, 0xf0, 0xab, 0xca, 0xce, 0xcd, 0x9f, 0x5e, 0xbf, 0xa3, 0xac, 0x5f, 0xfd, 0x7d, 0x8e, 0x57,
	0xf8, 0xe7, 0x94, 0xba, 0xd0, 0x57, 0x0b, 0x0e, 0x3e, 0x5d, 0x5a, 0x6b, 0x7e, 0xfd, 0x9b, 0x74,
	0x15, 0x2b, 0x6b, 0xee, 0xfc, 0xea, 0xdf, 0xfe, 0xbd, 0xbf, 0x01, 0x68, 0xfa, 0xbf, 0x96, 0xf6,
	0xbe, 0xfc, 0xf9, 0x4f, 0x06, 0x9e, 0x83, 0xdd, 0xc1, 0xee, 0xb3, 0xa7, 0x61, 0xb8, 0x6b, 0x7a,
	0x17, 0x4f, 0xf8, 0x3f, 0x15, 0x99, 0x9e, 0xf3, 0x84, 0x92, 0xe0, 0xd2, 0x36, 0x09, 0x55, 0xfc,
	0xb7, 0xd3, 0xe9, 0x32, 0x97, 0xfa, 0xe2, 0xd7, 0x01, 0x00, 0x00, 0xff, 0xff, 0xfd, 0x3c, 0xea,
	0xad, 0x2c, 0x35, 0x00, 0x00,
}
