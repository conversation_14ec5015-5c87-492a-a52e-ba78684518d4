// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/virtual-image-card/virtual-image-card.proto

package virtual_image_card

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	grpc "google.golang.org/grpc"
)

// MockVirtualImageCardClient is a mock of VirtualImageCardClient interface.
type MockVirtualImageCardClient struct {
	ctrl     *gomock.Controller
	recorder *MockVirtualImageCardClientMockRecorder
}

// MockVirtualImageCardClientMockRecorder is the mock recorder for MockVirtualImageCardClient.
type MockVirtualImageCardClientMockRecorder struct {
	mock *MockVirtualImageCardClient
}

// NewMockVirtualImageCardClient creates a new mock instance.
func NewMockVirtualImageCardClient(ctrl *gomock.Controller) *MockVirtualImageCardClient {
	mock := &MockVirtualImageCardClient{ctrl: ctrl}
	mock.recorder = &MockVirtualImageCardClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVirtualImageCardClient) EXPECT() *MockVirtualImageCardClientMockRecorder {
	return m.recorder
}

// ActivityPlaceOrder mocks base method.
func (m *MockVirtualImageCardClient) ActivityPlaceOrder(ctx context.Context, in *ActivityPlaceOrderReq, opts ...grpc.CallOption) (*ActivityPlaceOrderResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ActivityPlaceOrder", varargs...)
	ret0, _ := ret[0].(*ActivityPlaceOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ActivityPlaceOrder indicates an expected call of ActivityPlaceOrder.
func (mr *MockVirtualImageCardClientMockRecorder) ActivityPlaceOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ActivityPlaceOrder", reflect.TypeOf((*MockVirtualImageCardClient)(nil).ActivityPlaceOrder), varargs...)
}

// AddPackage mocks base method.
func (m *MockVirtualImageCardClient) AddPackage(ctx context.Context, in *AddPackageReq, opts ...grpc.CallOption) (*AddPackageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPackage", varargs...)
	ret0, _ := ret[0].(*AddPackageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPackage indicates an expected call of AddPackage.
func (mr *MockVirtualImageCardClientMockRecorder) AddPackage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPackage", reflect.TypeOf((*MockVirtualImageCardClient)(nil).AddPackage), varargs...)
}

// AddSalePackage mocks base method.
func (m *MockVirtualImageCardClient) AddSalePackage(ctx context.Context, in *AddSalePackageReq, opts ...grpc.CallOption) (*AddSalePackageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddSalePackage", varargs...)
	ret0, _ := ret[0].(*AddSalePackageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddSalePackage indicates an expected call of AddSalePackage.
func (mr *MockVirtualImageCardClientMockRecorder) AddSalePackage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSalePackage", reflect.TypeOf((*MockVirtualImageCardClient)(nil).AddSalePackage), varargs...)
}

// BatchAddGroupPackage mocks base method.
func (m *MockVirtualImageCardClient) BatchAddGroupPackage(ctx context.Context, in *BatchAddGroupPackageReq, opts ...grpc.CallOption) (*BatchAddGroupPackageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchAddGroupPackage", varargs...)
	ret0, _ := ret[0].(*BatchAddGroupPackageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAddGroupPackage indicates an expected call of BatchAddGroupPackage.
func (mr *MockVirtualImageCardClientMockRecorder) BatchAddGroupPackage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddGroupPackage", reflect.TypeOf((*MockVirtualImageCardClient)(nil).BatchAddGroupPackage), varargs...)
}

// CancelOrder mocks base method.
func (m *MockVirtualImageCardClient) CancelOrder(ctx context.Context, in *CancelOrderReq, opts ...grpc.CallOption) (*CancelOrderResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelOrder", varargs...)
	ret0, _ := ret[0].(*CancelOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelOrder indicates an expected call of CancelOrder.
func (mr *MockVirtualImageCardClientMockRecorder) CancelOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelOrder", reflect.TypeOf((*MockVirtualImageCardClient)(nil).CancelOrder), varargs...)
}

// EditGroupPackage mocks base method.
func (m *MockVirtualImageCardClient) EditGroupPackage(ctx context.Context, in *EditGroupPackageReq, opts ...grpc.CallOption) (*EditGroupPackageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EditGroupPackage", varargs...)
	ret0, _ := ret[0].(*EditGroupPackageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EditGroupPackage indicates an expected call of EditGroupPackage.
func (mr *MockVirtualImageCardClientMockRecorder) EditGroupPackage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditGroupPackage", reflect.TypeOf((*MockVirtualImageCardClient)(nil).EditGroupPackage), varargs...)
}

// GenerateStat mocks base method.
func (m *MockVirtualImageCardClient) GenerateStat(ctx context.Context, in *GenerateStatReq, opts ...grpc.CallOption) (*GenerateStatResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateStat", varargs...)
	ret0, _ := ret[0].(*GenerateStatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateStat indicates an expected call of GenerateStat.
func (mr *MockVirtualImageCardClientMockRecorder) GenerateStat(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateStat", reflect.TypeOf((*MockVirtualImageCardClient)(nil).GenerateStat), varargs...)
}

// GetContractById mocks base method.
func (m *MockVirtualImageCardClient) GetContractById(ctx context.Context, in *GetContractByIdReq, opts ...grpc.CallOption) (*GetContractByIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetContractById", varargs...)
	ret0, _ := ret[0].(*GetContractByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContractById indicates an expected call of GetContractById.
func (mr *MockVirtualImageCardClientMockRecorder) GetContractById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContractById", reflect.TypeOf((*MockVirtualImageCardClient)(nil).GetContractById), varargs...)
}

// GetPackageListByIds mocks base method.
func (m *MockVirtualImageCardClient) GetPackageListByIds(ctx context.Context, in *GetPackageListByIdsReq, opts ...grpc.CallOption) (*GetPackageListByIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPackageListByIds", varargs...)
	ret0, _ := ret[0].(*GetPackageListByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPackageListByIds indicates an expected call of GetPackageListByIds.
func (mr *MockVirtualImageCardClientMockRecorder) GetPackageListByIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPackageListByIds", reflect.TypeOf((*MockVirtualImageCardClient)(nil).GetPackageListByIds), varargs...)
}

// GetPackageListByStatus mocks base method.
func (m *MockVirtualImageCardClient) GetPackageListByStatus(ctx context.Context, in *GetPackageListByStatusReq, opts ...grpc.CallOption) (*GetPackageListByStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPackageListByStatus", varargs...)
	ret0, _ := ret[0].(*GetPackageListByStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPackageListByStatus indicates an expected call of GetPackageListByStatus.
func (mr *MockVirtualImageCardClientMockRecorder) GetPackageListByStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPackageListByStatus", reflect.TypeOf((*MockVirtualImageCardClient)(nil).GetPackageListByStatus), varargs...)
}

// GetPurchaseHistory mocks base method.
func (m *MockVirtualImageCardClient) GetPurchaseHistory(ctx context.Context, in *GetPurchaseHistoryReq, opts ...grpc.CallOption) (*GetPurchaseHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPurchaseHistory", varargs...)
	ret0, _ := ret[0].(*GetPurchaseHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPurchaseHistory indicates an expected call of GetPurchaseHistory.
func (mr *MockVirtualImageCardClientMockRecorder) GetPurchaseHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPurchaseHistory", reflect.TypeOf((*MockVirtualImageCardClient)(nil).GetPurchaseHistory), varargs...)
}

// GetSalePackageListByStatus mocks base method.
func (m *MockVirtualImageCardClient) GetSalePackageListByStatus(ctx context.Context, in *GetSalePackageListByStatusReq, opts ...grpc.CallOption) (*GetSalePackageListByStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSalePackageListByStatus", varargs...)
	ret0, _ := ret[0].(*GetSalePackageListByStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSalePackageListByStatus indicates an expected call of GetSalePackageListByStatus.
func (mr *MockVirtualImageCardClientMockRecorder) GetSalePackageListByStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSalePackageListByStatus", reflect.TypeOf((*MockVirtualImageCardClient)(nil).GetSalePackageListByStatus), varargs...)
}

// GetTrialCardOrderIds mocks base method.
func (m *MockVirtualImageCardClient) GetTrialCardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTrialCardOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTrialCardOrderIds indicates an expected call of GetTrialCardOrderIds.
func (mr *MockVirtualImageCardClientMockRecorder) GetTrialCardOrderIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTrialCardOrderIds", reflect.TypeOf((*MockVirtualImageCardClient)(nil).GetTrialCardOrderIds), varargs...)
}

// GetTrialCardTotalCount mocks base method.
func (m *MockVirtualImageCardClient) GetTrialCardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTrialCardTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTrialCardTotalCount indicates an expected call of GetTrialCardTotalCount.
func (mr *MockVirtualImageCardClientMockRecorder) GetTrialCardTotalCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTrialCardTotalCount", reflect.TypeOf((*MockVirtualImageCardClient)(nil).GetTrialCardTotalCount), varargs...)
}

// GetUserCardInfo mocks base method.
func (m *MockVirtualImageCardClient) GetUserCardInfo(ctx context.Context, in *GetUserCardInfoReq, opts ...grpc.CallOption) (*GetUserCardInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserCardInfo", varargs...)
	ret0, _ := ret[0].(*GetUserCardInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserCardInfo indicates an expected call of GetUserCardInfo.
func (mr *MockVirtualImageCardClientMockRecorder) GetUserCardInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCardInfo", reflect.TypeOf((*MockVirtualImageCardClient)(nil).GetUserCardInfo), varargs...)
}

// GetUserPackageList mocks base method.
func (m *MockVirtualImageCardClient) GetUserPackageList(ctx context.Context, in *GetUserPackageListReq, opts ...grpc.CallOption) (*GetUserPackageListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPackageList", varargs...)
	ret0, _ := ret[0].(*GetUserPackageListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPackageList indicates an expected call of GetUserPackageList.
func (mr *MockVirtualImageCardClientMockRecorder) GetUserPackageList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPackageList", reflect.TypeOf((*MockVirtualImageCardClient)(nil).GetUserPackageList), varargs...)
}

// GetUserRedemptionInfo mocks base method.
func (m *MockVirtualImageCardClient) GetUserRedemptionInfo(ctx context.Context, in *GetUserRedemptionInfoReq, opts ...grpc.CallOption) (*GetUserRedemptionInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserRedemptionInfo", varargs...)
	ret0, _ := ret[0].(*GetUserRedemptionInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserRedemptionInfo indicates an expected call of GetUserRedemptionInfo.
func (mr *MockVirtualImageCardClientMockRecorder) GetUserRedemptionInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRedemptionInfo", reflect.TypeOf((*MockVirtualImageCardClient)(nil).GetUserRedemptionInfo), varargs...)
}

// GetVirtualImageCardCommonCfg mocks base method.
func (m *MockVirtualImageCardClient) GetVirtualImageCardCommonCfg(ctx context.Context, in *GetVirtualImageCardCommonCfgRequest, opts ...grpc.CallOption) (*GetVirtualImageCardCommonCfgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVirtualImageCardCommonCfg", varargs...)
	ret0, _ := ret[0].(*GetVirtualImageCardCommonCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageCardCommonCfg indicates an expected call of GetVirtualImageCardCommonCfg.
func (mr *MockVirtualImageCardClientMockRecorder) GetVirtualImageCardCommonCfg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageCardCommonCfg", reflect.TypeOf((*MockVirtualImageCardClient)(nil).GetVirtualImageCardCommonCfg), varargs...)
}

// GetVirtualImageCardEntryStatus mocks base method.
func (m *MockVirtualImageCardClient) GetVirtualImageCardEntryStatus(ctx context.Context, in *GetVirtualImageCardEntryStatusRequest, opts ...grpc.CallOption) (*GetVirtualImageCardEntryStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVirtualImageCardEntryStatus", varargs...)
	ret0, _ := ret[0].(*GetVirtualImageCardEntryStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageCardEntryStatus indicates an expected call of GetVirtualImageCardEntryStatus.
func (mr *MockVirtualImageCardClientMockRecorder) GetVirtualImageCardEntryStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageCardEntryStatus", reflect.TypeOf((*MockVirtualImageCardClient)(nil).GetVirtualImageCardEntryStatus), varargs...)
}

// ModifyGroupPackage mocks base method.
func (m *MockVirtualImageCardClient) ModifyGroupPackage(ctx context.Context, in *ModifyGroupPackageReq, opts ...grpc.CallOption) (*ModifyGroupPackageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ModifyGroupPackage", varargs...)
	ret0, _ := ret[0].(*ModifyGroupPackageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyGroupPackage indicates an expected call of ModifyGroupPackage.
func (mr *MockVirtualImageCardClientMockRecorder) ModifyGroupPackage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyGroupPackage", reflect.TypeOf((*MockVirtualImageCardClient)(nil).ModifyGroupPackage), varargs...)
}

// ModifyPackage mocks base method.
func (m *MockVirtualImageCardClient) ModifyPackage(ctx context.Context, in *ModifyPackageReq, opts ...grpc.CallOption) (*ModifyPackageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ModifyPackage", varargs...)
	ret0, _ := ret[0].(*ModifyPackageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyPackage indicates an expected call of ModifyPackage.
func (mr *MockVirtualImageCardClientMockRecorder) ModifyPackage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyPackage", reflect.TypeOf((*MockVirtualImageCardClient)(nil).ModifyPackage), varargs...)
}

// NotifyContract mocks base method.
func (m *MockVirtualImageCardClient) NotifyContract(ctx context.Context, in *NotifyContractReq, opts ...grpc.CallOption) (*NotifyContractResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "NotifyContract", varargs...)
	ret0, _ := ret[0].(*NotifyContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NotifyContract indicates an expected call of NotifyContract.
func (mr *MockVirtualImageCardClientMockRecorder) NotifyContract(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyContract", reflect.TypeOf((*MockVirtualImageCardClient)(nil).NotifyContract), varargs...)
}

// PayCallback mocks base method.
func (m *MockVirtualImageCardClient) PayCallback(ctx context.Context, in *PayCallbackReq, opts ...grpc.CallOption) (*PayCallbackResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayCallback", varargs...)
	ret0, _ := ret[0].(*PayCallbackResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayCallback indicates an expected call of PayCallback.
func (mr *MockVirtualImageCardClientMockRecorder) PayCallback(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayCallback", reflect.TypeOf((*MockVirtualImageCardClient)(nil).PayCallback), varargs...)
}

// PlaceAutoPayOrder mocks base method.
func (m *MockVirtualImageCardClient) PlaceAutoPayOrder(ctx context.Context, in *PlaceAutoPayOrderReq, opts ...grpc.CallOption) (*PlaceAutoPayOrderResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PlaceAutoPayOrder", varargs...)
	ret0, _ := ret[0].(*PlaceAutoPayOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PlaceAutoPayOrder indicates an expected call of PlaceAutoPayOrder.
func (mr *MockVirtualImageCardClientMockRecorder) PlaceAutoPayOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PlaceAutoPayOrder", reflect.TypeOf((*MockVirtualImageCardClient)(nil).PlaceAutoPayOrder), varargs...)
}

// PlaceOrder mocks base method.
func (m *MockVirtualImageCardClient) PlaceOrder(ctx context.Context, in *PlaceOrderReq, opts ...grpc.CallOption) (*PlaceOrderResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PlaceOrder", varargs...)
	ret0, _ := ret[0].(*PlaceOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PlaceOrder indicates an expected call of PlaceOrder.
func (mr *MockVirtualImageCardClientMockRecorder) PlaceOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PlaceOrder", reflect.TypeOf((*MockVirtualImageCardClient)(nil).PlaceOrder), varargs...)
}

// ReplaceTrialCardOrder mocks base method.
func (m *MockVirtualImageCardClient) ReplaceTrialCardOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReplaceTrialCardOrder", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReplaceTrialCardOrder indicates an expected call of ReplaceTrialCardOrder.
func (mr *MockVirtualImageCardClientMockRecorder) ReplaceTrialCardOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReplaceTrialCardOrder", reflect.TypeOf((*MockVirtualImageCardClient)(nil).ReplaceTrialCardOrder), varargs...)
}

// RevokeOrder mocks base method.
func (m *MockVirtualImageCardClient) RevokeOrder(ctx context.Context, in *RevokeOrderReq, opts ...grpc.CallOption) (*RevokeOrderResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RevokeOrder", varargs...)
	ret0, _ := ret[0].(*RevokeOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RevokeOrder indicates an expected call of RevokeOrder.
func (mr *MockVirtualImageCardClientMockRecorder) RevokeOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokeOrder", reflect.TypeOf((*MockVirtualImageCardClient)(nil).RevokeOrder), varargs...)
}

// SalePackageSort mocks base method.
func (m *MockVirtualImageCardClient) SalePackageSort(ctx context.Context, in *SalePackageSortReq, opts ...grpc.CallOption) (*SalePackageSortResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SalePackageSort", varargs...)
	ret0, _ := ret[0].(*SalePackageSortResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SalePackageSort indicates an expected call of SalePackageSort.
func (mr *MockVirtualImageCardClientMockRecorder) SalePackageSort(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SalePackageSort", reflect.TypeOf((*MockVirtualImageCardClient)(nil).SalePackageSort), varargs...)
}

// UpdatePackageStatus mocks base method.
func (m *MockVirtualImageCardClient) UpdatePackageStatus(ctx context.Context, in *UpdatePackageStatusReq, opts ...grpc.CallOption) (*UpdatePackageStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePackageStatus", varargs...)
	ret0, _ := ret[0].(*UpdatePackageStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePackageStatus indicates an expected call of UpdatePackageStatus.
func (mr *MockVirtualImageCardClientMockRecorder) UpdatePackageStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePackageStatus", reflect.TypeOf((*MockVirtualImageCardClient)(nil).UpdatePackageStatus), varargs...)
}

// UpdateSalePackage mocks base method.
func (m *MockVirtualImageCardClient) UpdateSalePackage(ctx context.Context, in *UpdateSalePackageReq, opts ...grpc.CallOption) (*UpdateSalePackageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateSalePackage", varargs...)
	ret0, _ := ret[0].(*UpdateSalePackageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSalePackage indicates an expected call of UpdateSalePackage.
func (mr *MockVirtualImageCardClientMockRecorder) UpdateSalePackage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSalePackage", reflect.TypeOf((*MockVirtualImageCardClient)(nil).UpdateSalePackage), varargs...)
}

// UseVirtualImageTrialCard mocks base method.
func (m *MockVirtualImageCardClient) UseVirtualImageTrialCard(ctx context.Context, in *UseVirtualImageTrialCardReq, opts ...grpc.CallOption) (*UseVirtualImageTrialCardResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UseVirtualImageTrialCard", varargs...)
	ret0, _ := ret[0].(*UseVirtualImageTrialCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UseVirtualImageTrialCard indicates an expected call of UseVirtualImageTrialCard.
func (mr *MockVirtualImageCardClientMockRecorder) UseVirtualImageTrialCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UseVirtualImageTrialCard", reflect.TypeOf((*MockVirtualImageCardClient)(nil).UseVirtualImageTrialCard), varargs...)
}

// MockVirtualImageCardServer is a mock of VirtualImageCardServer interface.
type MockVirtualImageCardServer struct {
	ctrl     *gomock.Controller
	recorder *MockVirtualImageCardServerMockRecorder
}

// MockVirtualImageCardServerMockRecorder is the mock recorder for MockVirtualImageCardServer.
type MockVirtualImageCardServerMockRecorder struct {
	mock *MockVirtualImageCardServer
}

// NewMockVirtualImageCardServer creates a new mock instance.
func NewMockVirtualImageCardServer(ctrl *gomock.Controller) *MockVirtualImageCardServer {
	mock := &MockVirtualImageCardServer{ctrl: ctrl}
	mock.recorder = &MockVirtualImageCardServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVirtualImageCardServer) EXPECT() *MockVirtualImageCardServerMockRecorder {
	return m.recorder
}

// ActivityPlaceOrder mocks base method.
func (m *MockVirtualImageCardServer) ActivityPlaceOrder(ctx context.Context, in *ActivityPlaceOrderReq) (*ActivityPlaceOrderResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ActivityPlaceOrder", ctx, in)
	ret0, _ := ret[0].(*ActivityPlaceOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ActivityPlaceOrder indicates an expected call of ActivityPlaceOrder.
func (mr *MockVirtualImageCardServerMockRecorder) ActivityPlaceOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ActivityPlaceOrder", reflect.TypeOf((*MockVirtualImageCardServer)(nil).ActivityPlaceOrder), ctx, in)
}

// AddPackage mocks base method.
func (m *MockVirtualImageCardServer) AddPackage(ctx context.Context, in *AddPackageReq) (*AddPackageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPackage", ctx, in)
	ret0, _ := ret[0].(*AddPackageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPackage indicates an expected call of AddPackage.
func (mr *MockVirtualImageCardServerMockRecorder) AddPackage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPackage", reflect.TypeOf((*MockVirtualImageCardServer)(nil).AddPackage), ctx, in)
}

// AddSalePackage mocks base method.
func (m *MockVirtualImageCardServer) AddSalePackage(ctx context.Context, in *AddSalePackageReq) (*AddSalePackageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddSalePackage", ctx, in)
	ret0, _ := ret[0].(*AddSalePackageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddSalePackage indicates an expected call of AddSalePackage.
func (mr *MockVirtualImageCardServerMockRecorder) AddSalePackage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSalePackage", reflect.TypeOf((*MockVirtualImageCardServer)(nil).AddSalePackage), ctx, in)
}

// BatchAddGroupPackage mocks base method.
func (m *MockVirtualImageCardServer) BatchAddGroupPackage(ctx context.Context, in *BatchAddGroupPackageReq) (*BatchAddGroupPackageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddGroupPackage", ctx, in)
	ret0, _ := ret[0].(*BatchAddGroupPackageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAddGroupPackage indicates an expected call of BatchAddGroupPackage.
func (mr *MockVirtualImageCardServerMockRecorder) BatchAddGroupPackage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddGroupPackage", reflect.TypeOf((*MockVirtualImageCardServer)(nil).BatchAddGroupPackage), ctx, in)
}

// CancelOrder mocks base method.
func (m *MockVirtualImageCardServer) CancelOrder(ctx context.Context, in *CancelOrderReq) (*CancelOrderResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelOrder", ctx, in)
	ret0, _ := ret[0].(*CancelOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelOrder indicates an expected call of CancelOrder.
func (mr *MockVirtualImageCardServerMockRecorder) CancelOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelOrder", reflect.TypeOf((*MockVirtualImageCardServer)(nil).CancelOrder), ctx, in)
}

// EditGroupPackage mocks base method.
func (m *MockVirtualImageCardServer) EditGroupPackage(ctx context.Context, in *EditGroupPackageReq) (*EditGroupPackageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EditGroupPackage", ctx, in)
	ret0, _ := ret[0].(*EditGroupPackageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EditGroupPackage indicates an expected call of EditGroupPackage.
func (mr *MockVirtualImageCardServerMockRecorder) EditGroupPackage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditGroupPackage", reflect.TypeOf((*MockVirtualImageCardServer)(nil).EditGroupPackage), ctx, in)
}

// GenerateStat mocks base method.
func (m *MockVirtualImageCardServer) GenerateStat(ctx context.Context, in *GenerateStatReq) (*GenerateStatResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateStat", ctx, in)
	ret0, _ := ret[0].(*GenerateStatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateStat indicates an expected call of GenerateStat.
func (mr *MockVirtualImageCardServerMockRecorder) GenerateStat(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateStat", reflect.TypeOf((*MockVirtualImageCardServer)(nil).GenerateStat), ctx, in)
}

// GetContractById mocks base method.
func (m *MockVirtualImageCardServer) GetContractById(ctx context.Context, in *GetContractByIdReq) (*GetContractByIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContractById", ctx, in)
	ret0, _ := ret[0].(*GetContractByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContractById indicates an expected call of GetContractById.
func (mr *MockVirtualImageCardServerMockRecorder) GetContractById(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContractById", reflect.TypeOf((*MockVirtualImageCardServer)(nil).GetContractById), ctx, in)
}

// GetPackageListByIds mocks base method.
func (m *MockVirtualImageCardServer) GetPackageListByIds(ctx context.Context, in *GetPackageListByIdsReq) (*GetPackageListByIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPackageListByIds", ctx, in)
	ret0, _ := ret[0].(*GetPackageListByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPackageListByIds indicates an expected call of GetPackageListByIds.
func (mr *MockVirtualImageCardServerMockRecorder) GetPackageListByIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPackageListByIds", reflect.TypeOf((*MockVirtualImageCardServer)(nil).GetPackageListByIds), ctx, in)
}

// GetPackageListByStatus mocks base method.
func (m *MockVirtualImageCardServer) GetPackageListByStatus(ctx context.Context, in *GetPackageListByStatusReq) (*GetPackageListByStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPackageListByStatus", ctx, in)
	ret0, _ := ret[0].(*GetPackageListByStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPackageListByStatus indicates an expected call of GetPackageListByStatus.
func (mr *MockVirtualImageCardServerMockRecorder) GetPackageListByStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPackageListByStatus", reflect.TypeOf((*MockVirtualImageCardServer)(nil).GetPackageListByStatus), ctx, in)
}

// GetPurchaseHistory mocks base method.
func (m *MockVirtualImageCardServer) GetPurchaseHistory(ctx context.Context, in *GetPurchaseHistoryReq) (*GetPurchaseHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPurchaseHistory", ctx, in)
	ret0, _ := ret[0].(*GetPurchaseHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPurchaseHistory indicates an expected call of GetPurchaseHistory.
func (mr *MockVirtualImageCardServerMockRecorder) GetPurchaseHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPurchaseHistory", reflect.TypeOf((*MockVirtualImageCardServer)(nil).GetPurchaseHistory), ctx, in)
}

// GetSalePackageListByStatus mocks base method.
func (m *MockVirtualImageCardServer) GetSalePackageListByStatus(ctx context.Context, in *GetSalePackageListByStatusReq) (*GetSalePackageListByStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSalePackageListByStatus", ctx, in)
	ret0, _ := ret[0].(*GetSalePackageListByStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSalePackageListByStatus indicates an expected call of GetSalePackageListByStatus.
func (mr *MockVirtualImageCardServerMockRecorder) GetSalePackageListByStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSalePackageListByStatus", reflect.TypeOf((*MockVirtualImageCardServer)(nil).GetSalePackageListByStatus), ctx, in)
}

// GetTrialCardOrderIds mocks base method.
func (m *MockVirtualImageCardServer) GetTrialCardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTrialCardOrderIds", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTrialCardOrderIds indicates an expected call of GetTrialCardOrderIds.
func (mr *MockVirtualImageCardServerMockRecorder) GetTrialCardOrderIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTrialCardOrderIds", reflect.TypeOf((*MockVirtualImageCardServer)(nil).GetTrialCardOrderIds), ctx, in)
}

// GetTrialCardTotalCount mocks base method.
func (m *MockVirtualImageCardServer) GetTrialCardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTrialCardTotalCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTrialCardTotalCount indicates an expected call of GetTrialCardTotalCount.
func (mr *MockVirtualImageCardServerMockRecorder) GetTrialCardTotalCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTrialCardTotalCount", reflect.TypeOf((*MockVirtualImageCardServer)(nil).GetTrialCardTotalCount), ctx, in)
}

// GetUserCardInfo mocks base method.
func (m *MockVirtualImageCardServer) GetUserCardInfo(ctx context.Context, in *GetUserCardInfoReq) (*GetUserCardInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserCardInfo", ctx, in)
	ret0, _ := ret[0].(*GetUserCardInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserCardInfo indicates an expected call of GetUserCardInfo.
func (mr *MockVirtualImageCardServerMockRecorder) GetUserCardInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCardInfo", reflect.TypeOf((*MockVirtualImageCardServer)(nil).GetUserCardInfo), ctx, in)
}

// GetUserPackageList mocks base method.
func (m *MockVirtualImageCardServer) GetUserPackageList(ctx context.Context, in *GetUserPackageListReq) (*GetUserPackageListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPackageList", ctx, in)
	ret0, _ := ret[0].(*GetUserPackageListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPackageList indicates an expected call of GetUserPackageList.
func (mr *MockVirtualImageCardServerMockRecorder) GetUserPackageList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPackageList", reflect.TypeOf((*MockVirtualImageCardServer)(nil).GetUserPackageList), ctx, in)
}

// GetUserRedemptionInfo mocks base method.
func (m *MockVirtualImageCardServer) GetUserRedemptionInfo(ctx context.Context, in *GetUserRedemptionInfoReq) (*GetUserRedemptionInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRedemptionInfo", ctx, in)
	ret0, _ := ret[0].(*GetUserRedemptionInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserRedemptionInfo indicates an expected call of GetUserRedemptionInfo.
func (mr *MockVirtualImageCardServerMockRecorder) GetUserRedemptionInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRedemptionInfo", reflect.TypeOf((*MockVirtualImageCardServer)(nil).GetUserRedemptionInfo), ctx, in)
}

// GetVirtualImageCardCommonCfg mocks base method.
func (m *MockVirtualImageCardServer) GetVirtualImageCardCommonCfg(ctx context.Context, in *GetVirtualImageCardCommonCfgRequest) (*GetVirtualImageCardCommonCfgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageCardCommonCfg", ctx, in)
	ret0, _ := ret[0].(*GetVirtualImageCardCommonCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageCardCommonCfg indicates an expected call of GetVirtualImageCardCommonCfg.
func (mr *MockVirtualImageCardServerMockRecorder) GetVirtualImageCardCommonCfg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageCardCommonCfg", reflect.TypeOf((*MockVirtualImageCardServer)(nil).GetVirtualImageCardCommonCfg), ctx, in)
}

// GetVirtualImageCardEntryStatus mocks base method.
func (m *MockVirtualImageCardServer) GetVirtualImageCardEntryStatus(ctx context.Context, in *GetVirtualImageCardEntryStatusRequest) (*GetVirtualImageCardEntryStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageCardEntryStatus", ctx, in)
	ret0, _ := ret[0].(*GetVirtualImageCardEntryStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageCardEntryStatus indicates an expected call of GetVirtualImageCardEntryStatus.
func (mr *MockVirtualImageCardServerMockRecorder) GetVirtualImageCardEntryStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageCardEntryStatus", reflect.TypeOf((*MockVirtualImageCardServer)(nil).GetVirtualImageCardEntryStatus), ctx, in)
}

// ModifyGroupPackage mocks base method.
func (m *MockVirtualImageCardServer) ModifyGroupPackage(ctx context.Context, in *ModifyGroupPackageReq) (*ModifyGroupPackageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyGroupPackage", ctx, in)
	ret0, _ := ret[0].(*ModifyGroupPackageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyGroupPackage indicates an expected call of ModifyGroupPackage.
func (mr *MockVirtualImageCardServerMockRecorder) ModifyGroupPackage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyGroupPackage", reflect.TypeOf((*MockVirtualImageCardServer)(nil).ModifyGroupPackage), ctx, in)
}

// ModifyPackage mocks base method.
func (m *MockVirtualImageCardServer) ModifyPackage(ctx context.Context, in *ModifyPackageReq) (*ModifyPackageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyPackage", ctx, in)
	ret0, _ := ret[0].(*ModifyPackageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyPackage indicates an expected call of ModifyPackage.
func (mr *MockVirtualImageCardServerMockRecorder) ModifyPackage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyPackage", reflect.TypeOf((*MockVirtualImageCardServer)(nil).ModifyPackage), ctx, in)
}

// NotifyContract mocks base method.
func (m *MockVirtualImageCardServer) NotifyContract(ctx context.Context, in *NotifyContractReq) (*NotifyContractResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NotifyContract", ctx, in)
	ret0, _ := ret[0].(*NotifyContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NotifyContract indicates an expected call of NotifyContract.
func (mr *MockVirtualImageCardServerMockRecorder) NotifyContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyContract", reflect.TypeOf((*MockVirtualImageCardServer)(nil).NotifyContract), ctx, in)
}

// PayCallback mocks base method.
func (m *MockVirtualImageCardServer) PayCallback(ctx context.Context, in *PayCallbackReq) (*PayCallbackResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayCallback", ctx, in)
	ret0, _ := ret[0].(*PayCallbackResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayCallback indicates an expected call of PayCallback.
func (mr *MockVirtualImageCardServerMockRecorder) PayCallback(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayCallback", reflect.TypeOf((*MockVirtualImageCardServer)(nil).PayCallback), ctx, in)
}

// PlaceAutoPayOrder mocks base method.
func (m *MockVirtualImageCardServer) PlaceAutoPayOrder(ctx context.Context, in *PlaceAutoPayOrderReq) (*PlaceAutoPayOrderResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PlaceAutoPayOrder", ctx, in)
	ret0, _ := ret[0].(*PlaceAutoPayOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PlaceAutoPayOrder indicates an expected call of PlaceAutoPayOrder.
func (mr *MockVirtualImageCardServerMockRecorder) PlaceAutoPayOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PlaceAutoPayOrder", reflect.TypeOf((*MockVirtualImageCardServer)(nil).PlaceAutoPayOrder), ctx, in)
}

// PlaceOrder mocks base method.
func (m *MockVirtualImageCardServer) PlaceOrder(ctx context.Context, in *PlaceOrderReq) (*PlaceOrderResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PlaceOrder", ctx, in)
	ret0, _ := ret[0].(*PlaceOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PlaceOrder indicates an expected call of PlaceOrder.
func (mr *MockVirtualImageCardServerMockRecorder) PlaceOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PlaceOrder", reflect.TypeOf((*MockVirtualImageCardServer)(nil).PlaceOrder), ctx, in)
}

// ReplaceTrialCardOrder mocks base method.
func (m *MockVirtualImageCardServer) ReplaceTrialCardOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReplaceTrialCardOrder", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReplaceTrialCardOrder indicates an expected call of ReplaceTrialCardOrder.
func (mr *MockVirtualImageCardServerMockRecorder) ReplaceTrialCardOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReplaceTrialCardOrder", reflect.TypeOf((*MockVirtualImageCardServer)(nil).ReplaceTrialCardOrder), ctx, in)
}

// RevokeOrder mocks base method.
func (m *MockVirtualImageCardServer) RevokeOrder(ctx context.Context, in *RevokeOrderReq) (*RevokeOrderResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RevokeOrder", ctx, in)
	ret0, _ := ret[0].(*RevokeOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RevokeOrder indicates an expected call of RevokeOrder.
func (mr *MockVirtualImageCardServerMockRecorder) RevokeOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokeOrder", reflect.TypeOf((*MockVirtualImageCardServer)(nil).RevokeOrder), ctx, in)
}

// SalePackageSort mocks base method.
func (m *MockVirtualImageCardServer) SalePackageSort(ctx context.Context, in *SalePackageSortReq) (*SalePackageSortResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SalePackageSort", ctx, in)
	ret0, _ := ret[0].(*SalePackageSortResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SalePackageSort indicates an expected call of SalePackageSort.
func (mr *MockVirtualImageCardServerMockRecorder) SalePackageSort(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SalePackageSort", reflect.TypeOf((*MockVirtualImageCardServer)(nil).SalePackageSort), ctx, in)
}

// UpdatePackageStatus mocks base method.
func (m *MockVirtualImageCardServer) UpdatePackageStatus(ctx context.Context, in *UpdatePackageStatusReq) (*UpdatePackageStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePackageStatus", ctx, in)
	ret0, _ := ret[0].(*UpdatePackageStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePackageStatus indicates an expected call of UpdatePackageStatus.
func (mr *MockVirtualImageCardServerMockRecorder) UpdatePackageStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePackageStatus", reflect.TypeOf((*MockVirtualImageCardServer)(nil).UpdatePackageStatus), ctx, in)
}

// UpdateSalePackage mocks base method.
func (m *MockVirtualImageCardServer) UpdateSalePackage(ctx context.Context, in *UpdateSalePackageReq) (*UpdateSalePackageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSalePackage", ctx, in)
	ret0, _ := ret[0].(*UpdateSalePackageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSalePackage indicates an expected call of UpdateSalePackage.
func (mr *MockVirtualImageCardServerMockRecorder) UpdateSalePackage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSalePackage", reflect.TypeOf((*MockVirtualImageCardServer)(nil).UpdateSalePackage), ctx, in)
}

// UseVirtualImageTrialCard mocks base method.
func (m *MockVirtualImageCardServer) UseVirtualImageTrialCard(ctx context.Context, in *UseVirtualImageTrialCardReq) (*UseVirtualImageTrialCardResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UseVirtualImageTrialCard", ctx, in)
	ret0, _ := ret[0].(*UseVirtualImageTrialCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UseVirtualImageTrialCard indicates an expected call of UseVirtualImageTrialCard.
func (mr *MockVirtualImageCardServerMockRecorder) UseVirtualImageTrialCard(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UseVirtualImageTrialCard", reflect.TypeOf((*MockVirtualImageCardServer)(nil).UseVirtualImageTrialCard), ctx, in)
}
