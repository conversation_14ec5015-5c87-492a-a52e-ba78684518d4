// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/anchor-newbie-guide/anchor-newbie-guide.proto

package anchor_newbie_guide // import "golang.52tt.com/protocol/services/anchor-newbie-guide"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type KnowledgeTaskStatus int32

const (
	KnowledgeTaskStatus_KnowledgeTaskStatus_Invalid   KnowledgeTaskStatus = 0
	KnowledgeTaskStatus_KnowledgeTaskStatus_NotFinish KnowledgeTaskStatus = 1
	KnowledgeTaskStatus_KnowledgeTaskStatus_Finished  KnowledgeTaskStatus = 2
)

var KnowledgeTaskStatus_name = map[int32]string{
	0: "KnowledgeTaskStatus_Invalid",
	1: "KnowledgeTaskStatus_NotFinish",
	2: "KnowledgeTaskStatus_Finished",
}
var KnowledgeTaskStatus_value = map[string]int32{
	"KnowledgeTaskStatus_Invalid":   0,
	"KnowledgeTaskStatus_NotFinish": 1,
	"KnowledgeTaskStatus_Finished":  2,
}

func (x KnowledgeTaskStatus) String() string {
	return proto.EnumName(KnowledgeTaskStatus_name, int32(x))
}
func (KnowledgeTaskStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{0}
}

type WidgetType int32

const (
	WidgetType_WIDGET_TYPE_INVALID_UNSPECIFIED      WidgetType = 0
	WidgetType_WIDGET_TYPE_NEWBIE_ANCHOR_LIVE_READY WidgetType = 1
	WidgetType_WIDGET_TYPE_NEWBIE_ANCHOR_LIVE_ROOM  WidgetType = 2
)

var WidgetType_name = map[int32]string{
	0: "WIDGET_TYPE_INVALID_UNSPECIFIED",
	1: "WIDGET_TYPE_NEWBIE_ANCHOR_LIVE_READY",
	2: "WIDGET_TYPE_NEWBIE_ANCHOR_LIVE_ROOM",
}
var WidgetType_value = map[string]int32{
	"WIDGET_TYPE_INVALID_UNSPECIFIED":      0,
	"WIDGET_TYPE_NEWBIE_ANCHOR_LIVE_READY": 1,
	"WIDGET_TYPE_NEWBIE_ANCHOR_LIVE_ROOM":  2,
}

func (x WidgetType) String() string {
	return proto.EnumName(WidgetType_name, int32(x))
}
func (WidgetType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{1}
}

type WidgetStatus int32

const (
	WidgetStatus_WIDGET_STATUS_INVALID_UNSPECIFIED WidgetStatus = 0
	WidgetStatus_WIDGET_STATUS_NORMAL              WidgetStatus = 1
	WidgetStatus_WIDGET_STATUS_HAS_TASK            WidgetStatus = 2
	WidgetStatus_WIDGET_STATUS_HAS_REWARD          WidgetStatus = 3
)

var WidgetStatus_name = map[int32]string{
	0: "WIDGET_STATUS_INVALID_UNSPECIFIED",
	1: "WIDGET_STATUS_NORMAL",
	2: "WIDGET_STATUS_HAS_TASK",
	3: "WIDGET_STATUS_HAS_REWARD",
}
var WidgetStatus_value = map[string]int32{
	"WIDGET_STATUS_INVALID_UNSPECIFIED": 0,
	"WIDGET_STATUS_NORMAL":              1,
	"WIDGET_STATUS_HAS_TASK":            2,
	"WIDGET_STATUS_HAS_REWARD":          3,
}

func (x WidgetStatus) String() string {
	return proto.EnumName(WidgetStatus_name, int32(x))
}
func (WidgetStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{2}
}

// 新手任务类型
type NewbieAnchorTaskType int32

const (
	NewbieAnchorTaskType_NEWBIE_ANCHOR_TASK_TYPE_UNSPECIFIED NewbieAnchorTaskType = 0
	NewbieAnchorTaskType_NEWBIE_ANCHOR_TASK_TYPE_BEGINNER    NewbieAnchorTaskType = 1
	NewbieAnchorTaskType_NEWBIE_ANCHOR_TASK_TYPE_ADVANCED    NewbieAnchorTaskType = 2
)

var NewbieAnchorTaskType_name = map[int32]string{
	0: "NEWBIE_ANCHOR_TASK_TYPE_UNSPECIFIED",
	1: "NEWBIE_ANCHOR_TASK_TYPE_BEGINNER",
	2: "NEWBIE_ANCHOR_TASK_TYPE_ADVANCED",
}
var NewbieAnchorTaskType_value = map[string]int32{
	"NEWBIE_ANCHOR_TASK_TYPE_UNSPECIFIED": 0,
	"NEWBIE_ANCHOR_TASK_TYPE_BEGINNER":    1,
	"NEWBIE_ANCHOR_TASK_TYPE_ADVANCED":    2,
}

func (x NewbieAnchorTaskType) String() string {
	return proto.EnumName(NewbieAnchorTaskType_name, int32(x))
}
func (NewbieAnchorTaskType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{3}
}

// 新手任务奖励状态
type NewbieRewardStatus int32

const (
	NewbieRewardStatus_NEWBIE_REWARD_STATUS_UNSPECIFIED NewbieRewardStatus = 0
	NewbieRewardStatus_NEWBIE_REWARD_STATUS_NOT_CLAIMED NewbieRewardStatus = 1
	NewbieRewardStatus_NEWBIE_REWARD_STATUS_CLAIMED     NewbieRewardStatus = 2
	NewbieRewardStatus_NEWBIE_REWARD_STATUS_EXPIRED     NewbieRewardStatus = 3
)

var NewbieRewardStatus_name = map[int32]string{
	0: "NEWBIE_REWARD_STATUS_UNSPECIFIED",
	1: "NEWBIE_REWARD_STATUS_NOT_CLAIMED",
	2: "NEWBIE_REWARD_STATUS_CLAIMED",
	3: "NEWBIE_REWARD_STATUS_EXPIRED",
}
var NewbieRewardStatus_value = map[string]int32{
	"NEWBIE_REWARD_STATUS_UNSPECIFIED": 0,
	"NEWBIE_REWARD_STATUS_NOT_CLAIMED": 1,
	"NEWBIE_REWARD_STATUS_CLAIMED":     2,
	"NEWBIE_REWARD_STATUS_EXPIRED":     3,
}

func (x NewbieRewardStatus) String() string {
	return proto.EnumName(NewbieRewardStatus_name, int32(x))
}
func (NewbieRewardStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{4}
}

// 新手任务标识
type NewbieAnchorTaskSign int32

const (
	NewbieAnchorTaskSign_NEWBIE_ANCHOR_TASK_SIGN_UNSPECIFIED NewbieAnchorTaskSign = 0
	NewbieAnchorTaskSign_NEWBIE_ANCHOR_TASK_SIGN_ONLINE      NewbieAnchorTaskSign = 1
	NewbieAnchorTaskSign_NEWBIE_ANCHOR_TASK_SIGN_HOLD_MIC    NewbieAnchorTaskSign = 2
	NewbieAnchorTaskSign_NEWBIE_ANCHOR_TASK_SIGN_VALID_DAYS  NewbieAnchorTaskSign = 3
	NewbieAnchorTaskSign_NEWBIE_ANCHOR_TASK_SIGN_LIVE_TIME   NewbieAnchorTaskSign = 4
	NewbieAnchorTaskSign_NEWBIE_ANCHOR_TASK_SIGN_NEW_FANS    NewbieAnchorTaskSign = 5
	NewbieAnchorTaskSign_NEWBIE_ANCHOR_TASK_SIGN_PK          NewbieAnchorTaskSign = 6
	NewbieAnchorTaskSign_NEWBIE_ANCHOR_TASK_SIGN_HELLO       NewbieAnchorTaskSign = 7
	NewbieAnchorTaskSign_NEWBIE_ANCHOR_TASK_SIGN_PRESENT     NewbieAnchorTaskSign = 8
	NewbieAnchorTaskSign_NEWBIE_ANCHOR_TASK_SIGN_ACTIVE_DAYS NewbieAnchorTaskSign = 9
)

var NewbieAnchorTaskSign_name = map[int32]string{
	0: "NEWBIE_ANCHOR_TASK_SIGN_UNSPECIFIED",
	1: "NEWBIE_ANCHOR_TASK_SIGN_ONLINE",
	2: "NEWBIE_ANCHOR_TASK_SIGN_HOLD_MIC",
	3: "NEWBIE_ANCHOR_TASK_SIGN_VALID_DAYS",
	4: "NEWBIE_ANCHOR_TASK_SIGN_LIVE_TIME",
	5: "NEWBIE_ANCHOR_TASK_SIGN_NEW_FANS",
	6: "NEWBIE_ANCHOR_TASK_SIGN_PK",
	7: "NEWBIE_ANCHOR_TASK_SIGN_HELLO",
	8: "NEWBIE_ANCHOR_TASK_SIGN_PRESENT",
	9: "NEWBIE_ANCHOR_TASK_SIGN_ACTIVE_DAYS",
}
var NewbieAnchorTaskSign_value = map[string]int32{
	"NEWBIE_ANCHOR_TASK_SIGN_UNSPECIFIED": 0,
	"NEWBIE_ANCHOR_TASK_SIGN_ONLINE":      1,
	"NEWBIE_ANCHOR_TASK_SIGN_HOLD_MIC":    2,
	"NEWBIE_ANCHOR_TASK_SIGN_VALID_DAYS":  3,
	"NEWBIE_ANCHOR_TASK_SIGN_LIVE_TIME":   4,
	"NEWBIE_ANCHOR_TASK_SIGN_NEW_FANS":    5,
	"NEWBIE_ANCHOR_TASK_SIGN_PK":          6,
	"NEWBIE_ANCHOR_TASK_SIGN_HELLO":       7,
	"NEWBIE_ANCHOR_TASK_SIGN_PRESENT":     8,
	"NEWBIE_ANCHOR_TASK_SIGN_ACTIVE_DAYS": 9,
}

func (x NewbieAnchorTaskSign) String() string {
	return proto.EnumName(NewbieAnchorTaskSign_name, int32(x))
}
func (NewbieAnchorTaskSign) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{5}
}

// 新手任务初始化方式
type NewbieAnchorTaskInitType int32

const (
	NewbieAnchorTaskInitType_NEWBIE_ANCHOR_TASK_INIT_TYPE_UNSPECIFIED NewbieAnchorTaskInitType = 0
	NewbieAnchorTaskInitType_NEWBIE_ANCHOR_TASK_INIT_TYPE_AUTO        NewbieAnchorTaskInitType = 1
	NewbieAnchorTaskInitType_NEWBIE_ANCHOR_TASK_INIT_TYPE_LIVE        NewbieAnchorTaskInitType = 2
	NewbieAnchorTaskInitType_NEWBIE_ANCHOR_TASK_INIT_TYPE_MIC         NewbieAnchorTaskInitType = 3
)

var NewbieAnchorTaskInitType_name = map[int32]string{
	0: "NEWBIE_ANCHOR_TASK_INIT_TYPE_UNSPECIFIED",
	1: "NEWBIE_ANCHOR_TASK_INIT_TYPE_AUTO",
	2: "NEWBIE_ANCHOR_TASK_INIT_TYPE_LIVE",
	3: "NEWBIE_ANCHOR_TASK_INIT_TYPE_MIC",
}
var NewbieAnchorTaskInitType_value = map[string]int32{
	"NEWBIE_ANCHOR_TASK_INIT_TYPE_UNSPECIFIED": 0,
	"NEWBIE_ANCHOR_TASK_INIT_TYPE_AUTO":        1,
	"NEWBIE_ANCHOR_TASK_INIT_TYPE_LIVE":        2,
	"NEWBIE_ANCHOR_TASK_INIT_TYPE_MIC":         3,
}

func (x NewbieAnchorTaskInitType) String() string {
	return proto.EnumName(NewbieAnchorTaskInitType_name, int32(x))
}
func (NewbieAnchorTaskInitType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{6}
}

type ChannelLiveEndGuidePopupType int32

const (
	ChannelLiveEndGuidePopupType_CHANNEL_LIVE_END_GUIDE_POPUP_TYPE_INVALID_UNSPECIFIED ChannelLiveEndGuidePopupType = 0
	ChannelLiveEndGuidePopupType_CHANNEL_LIVE_END_GUIDE_POPUP_TYPE_USER_INFO           ChannelLiveEndGuidePopupType = 1
	ChannelLiveEndGuidePopupType_CHANNEL_LIVE_END_GUIDE_POPUP_TYPE_ANCHOR_RECOMMEND    ChannelLiveEndGuidePopupType = 2
)

var ChannelLiveEndGuidePopupType_name = map[int32]string{
	0: "CHANNEL_LIVE_END_GUIDE_POPUP_TYPE_INVALID_UNSPECIFIED",
	1: "CHANNEL_LIVE_END_GUIDE_POPUP_TYPE_USER_INFO",
	2: "CHANNEL_LIVE_END_GUIDE_POPUP_TYPE_ANCHOR_RECOMMEND",
}
var ChannelLiveEndGuidePopupType_value = map[string]int32{
	"CHANNEL_LIVE_END_GUIDE_POPUP_TYPE_INVALID_UNSPECIFIED": 0,
	"CHANNEL_LIVE_END_GUIDE_POPUP_TYPE_USER_INFO":           1,
	"CHANNEL_LIVE_END_GUIDE_POPUP_TYPE_ANCHOR_RECOMMEND":    2,
}

func (x ChannelLiveEndGuidePopupType) String() string {
	return proto.EnumName(ChannelLiveEndGuidePopupType_name, int32(x))
}
func (ChannelLiveEndGuidePopupType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{7}
}

type NewbieAnchorTaskDay_Status int32

const (
	NewbieAnchorTaskDay_STATUS_UNSPECIFIED   NewbieAnchorTaskDay_Status = 0
	NewbieAnchorTaskDay_STATUS_NOT_COMPLETED NewbieAnchorTaskDay_Status = 1
	NewbieAnchorTaskDay_STATUS_COMPLETED     NewbieAnchorTaskDay_Status = 2
	NewbieAnchorTaskDay_STATUS_EXPIRED       NewbieAnchorTaskDay_Status = 3
)

var NewbieAnchorTaskDay_Status_name = map[int32]string{
	0: "STATUS_UNSPECIFIED",
	1: "STATUS_NOT_COMPLETED",
	2: "STATUS_COMPLETED",
	3: "STATUS_EXPIRED",
}
var NewbieAnchorTaskDay_Status_value = map[string]int32{
	"STATUS_UNSPECIFIED":   0,
	"STATUS_NOT_COMPLETED": 1,
	"STATUS_COMPLETED":     2,
	"STATUS_EXPIRED":       3,
}

func (x NewbieAnchorTaskDay_Status) String() string {
	return proto.EnumName(NewbieAnchorTaskDay_Status_name, int32(x))
}
func (NewbieAnchorTaskDay_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{12, 0}
}

// 获取新手指南信息
type GetNewbieAnchorGuideInfoReq struct {
	IdentityType         uint32   `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	IsAdvance            bool     `protobuf:"varint,3,opt,name=is_advance,json=isAdvance,proto3" json:"is_advance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewbieAnchorGuideInfoReq) Reset()         { *m = GetNewbieAnchorGuideInfoReq{} }
func (m *GetNewbieAnchorGuideInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetNewbieAnchorGuideInfoReq) ProtoMessage()    {}
func (*GetNewbieAnchorGuideInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{0}
}
func (m *GetNewbieAnchorGuideInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieAnchorGuideInfoReq.Unmarshal(m, b)
}
func (m *GetNewbieAnchorGuideInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieAnchorGuideInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetNewbieAnchorGuideInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieAnchorGuideInfoReq.Merge(dst, src)
}
func (m *GetNewbieAnchorGuideInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetNewbieAnchorGuideInfoReq.Size(m)
}
func (m *GetNewbieAnchorGuideInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieAnchorGuideInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieAnchorGuideInfoReq proto.InternalMessageInfo

func (m *GetNewbieAnchorGuideInfoReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

func (m *GetNewbieAnchorGuideInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetNewbieAnchorGuideInfoReq) GetIsAdvance() bool {
	if m != nil {
		return m.IsAdvance
	}
	return false
}

type GetNewbieAnchorGuideInfoResp struct {
	ThemeList            []*NewbieGuideTheme `protobuf:"bytes,1,rep,name=theme_list,json=themeList,proto3" json:"theme_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetNewbieAnchorGuideInfoResp) Reset()         { *m = GetNewbieAnchorGuideInfoResp{} }
func (m *GetNewbieAnchorGuideInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetNewbieAnchorGuideInfoResp) ProtoMessage()    {}
func (*GetNewbieAnchorGuideInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{1}
}
func (m *GetNewbieAnchorGuideInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieAnchorGuideInfoResp.Unmarshal(m, b)
}
func (m *GetNewbieAnchorGuideInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieAnchorGuideInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetNewbieAnchorGuideInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieAnchorGuideInfoResp.Merge(dst, src)
}
func (m *GetNewbieAnchorGuideInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetNewbieAnchorGuideInfoResp.Size(m)
}
func (m *GetNewbieAnchorGuideInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieAnchorGuideInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieAnchorGuideInfoResp proto.InternalMessageInfo

func (m *GetNewbieAnchorGuideInfoResp) GetThemeList() []*NewbieGuideTheme {
	if m != nil {
		return m.ThemeList
	}
	return nil
}

// 知识点任务
type KnowledgeTask struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	TaskName             string   `protobuf:"bytes,2,opt,name=task_name,json=taskName,proto3" json:"task_name,omitempty"`
	JumpUrl              string   `protobuf:"bytes,3,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	Status               uint32   `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KnowledgeTask) Reset()         { *m = KnowledgeTask{} }
func (m *KnowledgeTask) String() string { return proto.CompactTextString(m) }
func (*KnowledgeTask) ProtoMessage()    {}
func (*KnowledgeTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{2}
}
func (m *KnowledgeTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KnowledgeTask.Unmarshal(m, b)
}
func (m *KnowledgeTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KnowledgeTask.Marshal(b, m, deterministic)
}
func (dst *KnowledgeTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KnowledgeTask.Merge(dst, src)
}
func (m *KnowledgeTask) XXX_Size() int {
	return xxx_messageInfo_KnowledgeTask.Size(m)
}
func (m *KnowledgeTask) XXX_DiscardUnknown() {
	xxx_messageInfo_KnowledgeTask.DiscardUnknown(m)
}

var xxx_messageInfo_KnowledgeTask proto.InternalMessageInfo

func (m *KnowledgeTask) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *KnowledgeTask) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *KnowledgeTask) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *KnowledgeTask) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type NewbieGuideContent struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Urls                 []string `protobuf:"bytes,3,rep,name=urls,proto3" json:"urls,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewbieGuideContent) Reset()         { *m = NewbieGuideContent{} }
func (m *NewbieGuideContent) String() string { return proto.CompactTextString(m) }
func (*NewbieGuideContent) ProtoMessage()    {}
func (*NewbieGuideContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{3}
}
func (m *NewbieGuideContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieGuideContent.Unmarshal(m, b)
}
func (m *NewbieGuideContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieGuideContent.Marshal(b, m, deterministic)
}
func (dst *NewbieGuideContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieGuideContent.Merge(dst, src)
}
func (m *NewbieGuideContent) XXX_Size() int {
	return xxx_messageInfo_NewbieGuideContent.Size(m)
}
func (m *NewbieGuideContent) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieGuideContent.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieGuideContent proto.InternalMessageInfo

func (m *NewbieGuideContent) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *NewbieGuideContent) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *NewbieGuideContent) GetUrls() []string {
	if m != nil {
		return m.Urls
	}
	return nil
}

// 子主题信息
type NewbieGuideSubTheme struct {
	ThemeName            string                `protobuf:"bytes,1,opt,name=theme_name,json=themeName,proto3" json:"theme_name,omitempty"`
	ContentList          []*NewbieGuideContent `protobuf:"bytes,2,rep,name=content_list,json=contentList,proto3" json:"content_list,omitempty"`
	TaskList             []*KnowledgeTask      `protobuf:"bytes,3,rep,name=task_list,json=taskList,proto3" json:"task_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *NewbieGuideSubTheme) Reset()         { *m = NewbieGuideSubTheme{} }
func (m *NewbieGuideSubTheme) String() string { return proto.CompactTextString(m) }
func (*NewbieGuideSubTheme) ProtoMessage()    {}
func (*NewbieGuideSubTheme) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{4}
}
func (m *NewbieGuideSubTheme) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieGuideSubTheme.Unmarshal(m, b)
}
func (m *NewbieGuideSubTheme) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieGuideSubTheme.Marshal(b, m, deterministic)
}
func (dst *NewbieGuideSubTheme) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieGuideSubTheme.Merge(dst, src)
}
func (m *NewbieGuideSubTheme) XXX_Size() int {
	return xxx_messageInfo_NewbieGuideSubTheme.Size(m)
}
func (m *NewbieGuideSubTheme) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieGuideSubTheme.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieGuideSubTheme proto.InternalMessageInfo

func (m *NewbieGuideSubTheme) GetThemeName() string {
	if m != nil {
		return m.ThemeName
	}
	return ""
}

func (m *NewbieGuideSubTheme) GetContentList() []*NewbieGuideContent {
	if m != nil {
		return m.ContentList
	}
	return nil
}

func (m *NewbieGuideSubTheme) GetTaskList() []*KnowledgeTask {
	if m != nil {
		return m.TaskList
	}
	return nil
}

// 主题信息
type NewbieGuideTheme struct {
	ThemeNameUrl         string                 `protobuf:"bytes,1,opt,name=theme_name_url,json=themeNameUrl,proto3" json:"theme_name_url,omitempty"`
	SubThemeList         []*NewbieGuideSubTheme `protobuf:"bytes,2,rep,name=sub_theme_list,json=subThemeList,proto3" json:"sub_theme_list,omitempty"`
	IdentityType         uint32                 `protobuf:"varint,3,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *NewbieGuideTheme) Reset()         { *m = NewbieGuideTheme{} }
func (m *NewbieGuideTheme) String() string { return proto.CompactTextString(m) }
func (*NewbieGuideTheme) ProtoMessage()    {}
func (*NewbieGuideTheme) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{5}
}
func (m *NewbieGuideTheme) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieGuideTheme.Unmarshal(m, b)
}
func (m *NewbieGuideTheme) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieGuideTheme.Marshal(b, m, deterministic)
}
func (dst *NewbieGuideTheme) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieGuideTheme.Merge(dst, src)
}
func (m *NewbieGuideTheme) XXX_Size() int {
	return xxx_messageInfo_NewbieGuideTheme.Size(m)
}
func (m *NewbieGuideTheme) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieGuideTheme.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieGuideTheme proto.InternalMessageInfo

func (m *NewbieGuideTheme) GetThemeNameUrl() string {
	if m != nil {
		return m.ThemeNameUrl
	}
	return ""
}

func (m *NewbieGuideTheme) GetSubThemeList() []*NewbieGuideSubTheme {
	if m != nil {
		return m.SubThemeList
	}
	return nil
}

func (m *NewbieGuideTheme) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

type GetNewbieGuidePopInfoReq struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SignDay              uint32            `protobuf:"varint,2,opt,name=sign_day,json=signDay,proto3" json:"sign_day,omitempty"`
	IdentityObtainDays   map[uint32]uint32 `protobuf:"bytes,4,rep,name=identity_obtain_days,json=identityObtainDays,proto3" json:"identity_obtain_days,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetNewbieGuidePopInfoReq) Reset()         { *m = GetNewbieGuidePopInfoReq{} }
func (m *GetNewbieGuidePopInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetNewbieGuidePopInfoReq) ProtoMessage()    {}
func (*GetNewbieGuidePopInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{6}
}
func (m *GetNewbieGuidePopInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieGuidePopInfoReq.Unmarshal(m, b)
}
func (m *GetNewbieGuidePopInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieGuidePopInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetNewbieGuidePopInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieGuidePopInfoReq.Merge(dst, src)
}
func (m *GetNewbieGuidePopInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetNewbieGuidePopInfoReq.Size(m)
}
func (m *GetNewbieGuidePopInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieGuidePopInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieGuidePopInfoReq proto.InternalMessageInfo

func (m *GetNewbieGuidePopInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetNewbieGuidePopInfoReq) GetSignDay() uint32 {
	if m != nil {
		return m.SignDay
	}
	return 0
}

func (m *GetNewbieGuidePopInfoReq) GetIdentityObtainDays() map[uint32]uint32 {
	if m != nil {
		return m.IdentityObtainDays
	}
	return nil
}

type GetNewbieGuidePopInfoResp struct {
	PopTxt               string   `protobuf:"bytes,1,opt,name=pop_txt,json=popTxt,proto3" json:"pop_txt,omitempty"`
	PopType              uint32   `protobuf:"varint,2,opt,name=pop_type,json=popType,proto3" json:"pop_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewbieGuidePopInfoResp) Reset()         { *m = GetNewbieGuidePopInfoResp{} }
func (m *GetNewbieGuidePopInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetNewbieGuidePopInfoResp) ProtoMessage()    {}
func (*GetNewbieGuidePopInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{7}
}
func (m *GetNewbieGuidePopInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieGuidePopInfoResp.Unmarshal(m, b)
}
func (m *GetNewbieGuidePopInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieGuidePopInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetNewbieGuidePopInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieGuidePopInfoResp.Merge(dst, src)
}
func (m *GetNewbieGuidePopInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetNewbieGuidePopInfoResp.Size(m)
}
func (m *GetNewbieGuidePopInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieGuidePopInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieGuidePopInfoResp proto.InternalMessageInfo

func (m *GetNewbieGuidePopInfoResp) GetPopTxt() string {
	if m != nil {
		return m.PopTxt
	}
	return ""
}

func (m *GetNewbieGuidePopInfoResp) GetPopType() uint32 {
	if m != nil {
		return m.PopType
	}
	return 0
}

type GetNewbieWidgetInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IdentityType         uint32   `protobuf:"varint,2,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	WidgetType           uint32   `protobuf:"varint,3,opt,name=widget_type,json=widgetType,proto3" json:"widget_type,omitempty"`
	ObtainDay            uint32   `protobuf:"varint,4,opt,name=obtain_day,json=obtainDay,proto3" json:"obtain_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewbieWidgetInfoReq) Reset()         { *m = GetNewbieWidgetInfoReq{} }
func (m *GetNewbieWidgetInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetNewbieWidgetInfoReq) ProtoMessage()    {}
func (*GetNewbieWidgetInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{8}
}
func (m *GetNewbieWidgetInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieWidgetInfoReq.Unmarshal(m, b)
}
func (m *GetNewbieWidgetInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieWidgetInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetNewbieWidgetInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieWidgetInfoReq.Merge(dst, src)
}
func (m *GetNewbieWidgetInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetNewbieWidgetInfoReq.Size(m)
}
func (m *GetNewbieWidgetInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieWidgetInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieWidgetInfoReq proto.InternalMessageInfo

func (m *GetNewbieWidgetInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetNewbieWidgetInfoReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

func (m *GetNewbieWidgetInfoReq) GetWidgetType() uint32 {
	if m != nil {
		return m.WidgetType
	}
	return 0
}

func (m *GetNewbieWidgetInfoReq) GetObtainDay() uint32 {
	if m != nil {
		return m.ObtainDay
	}
	return 0
}

type GetNewbieWidgetInfoResp struct {
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	TaskProgress         string   `protobuf:"bytes,4,opt,name=task_progress,json=taskProgress,proto3" json:"task_progress,omitempty"`
	Status               uint32   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	PopTxt               string   `protobuf:"bytes,6,opt,name=pop_txt,json=popTxt,proto3" json:"pop_txt,omitempty"`
	PopType              uint32   `protobuf:"varint,7,opt,name=pop_type,json=popType,proto3" json:"pop_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewbieWidgetInfoResp) Reset()         { *m = GetNewbieWidgetInfoResp{} }
func (m *GetNewbieWidgetInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetNewbieWidgetInfoResp) ProtoMessage()    {}
func (*GetNewbieWidgetInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{9}
}
func (m *GetNewbieWidgetInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieWidgetInfoResp.Unmarshal(m, b)
}
func (m *GetNewbieWidgetInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieWidgetInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetNewbieWidgetInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieWidgetInfoResp.Merge(dst, src)
}
func (m *GetNewbieWidgetInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetNewbieWidgetInfoResp.Size(m)
}
func (m *GetNewbieWidgetInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieWidgetInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieWidgetInfoResp proto.InternalMessageInfo

func (m *GetNewbieWidgetInfoResp) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetNewbieWidgetInfoResp) GetTaskProgress() string {
	if m != nil {
		return m.TaskProgress
	}
	return ""
}

func (m *GetNewbieWidgetInfoResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetNewbieWidgetInfoResp) GetPopTxt() string {
	if m != nil {
		return m.PopTxt
	}
	return ""
}

func (m *GetNewbieWidgetInfoResp) GetPopType() uint32 {
	if m != nil {
		return m.PopType
	}
	return 0
}

// 任务项
// 新手任务项
type NewbieAnchorTaskItem struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	TaskString           string   `protobuf:"bytes,2,opt,name=task_string,json=taskString,proto3" json:"task_string,omitempty"`
	TaskIcon             string   `protobuf:"bytes,3,opt,name=task_icon,json=taskIcon,proto3" json:"task_icon,omitempty"`
	IsCompleted          bool     `protobuf:"varint,4,opt,name=is_completed,json=isCompleted,proto3" json:"is_completed,omitempty"`
	Progress             uint32   `protobuf:"varint,5,opt,name=progress,proto3" json:"progress,omitempty"`
	Total                uint32   `protobuf:"varint,6,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewbieAnchorTaskItem) Reset()         { *m = NewbieAnchorTaskItem{} }
func (m *NewbieAnchorTaskItem) String() string { return proto.CompactTextString(m) }
func (*NewbieAnchorTaskItem) ProtoMessage()    {}
func (*NewbieAnchorTaskItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{10}
}
func (m *NewbieAnchorTaskItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieAnchorTaskItem.Unmarshal(m, b)
}
func (m *NewbieAnchorTaskItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieAnchorTaskItem.Marshal(b, m, deterministic)
}
func (dst *NewbieAnchorTaskItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieAnchorTaskItem.Merge(dst, src)
}
func (m *NewbieAnchorTaskItem) XXX_Size() int {
	return xxx_messageInfo_NewbieAnchorTaskItem.Size(m)
}
func (m *NewbieAnchorTaskItem) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieAnchorTaskItem.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieAnchorTaskItem proto.InternalMessageInfo

func (m *NewbieAnchorTaskItem) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *NewbieAnchorTaskItem) GetTaskString() string {
	if m != nil {
		return m.TaskString
	}
	return ""
}

func (m *NewbieAnchorTaskItem) GetTaskIcon() string {
	if m != nil {
		return m.TaskIcon
	}
	return ""
}

func (m *NewbieAnchorTaskItem) GetIsCompleted() bool {
	if m != nil {
		return m.IsCompleted
	}
	return false
}

func (m *NewbieAnchorTaskItem) GetProgress() uint32 {
	if m != nil {
		return m.Progress
	}
	return 0
}

func (m *NewbieAnchorTaskItem) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 获取新手任务
type GetNewbieAnchorTaskReq struct {
	IdentityType         uint32   `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewbieAnchorTaskReq) Reset()         { *m = GetNewbieAnchorTaskReq{} }
func (m *GetNewbieAnchorTaskReq) String() string { return proto.CompactTextString(m) }
func (*GetNewbieAnchorTaskReq) ProtoMessage()    {}
func (*GetNewbieAnchorTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{11}
}
func (m *GetNewbieAnchorTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieAnchorTaskReq.Unmarshal(m, b)
}
func (m *GetNewbieAnchorTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieAnchorTaskReq.Marshal(b, m, deterministic)
}
func (dst *GetNewbieAnchorTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieAnchorTaskReq.Merge(dst, src)
}
func (m *GetNewbieAnchorTaskReq) XXX_Size() int {
	return xxx_messageInfo_GetNewbieAnchorTaskReq.Size(m)
}
func (m *GetNewbieAnchorTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieAnchorTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieAnchorTaskReq proto.InternalMessageInfo

func (m *GetNewbieAnchorTaskReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

func (m *GetNewbieAnchorTaskReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 新手任务日期
type NewbieAnchorTaskDay struct {
	Day                  uint64                     `protobuf:"varint,1,opt,name=day,proto3" json:"day,omitempty"`
	CompleteStatus       NewbieAnchorTaskDay_Status `protobuf:"varint,2,opt,name=complete_status,json=completeStatus,proto3,enum=anchor_newbie_guide.NewbieAnchorTaskDay_Status" json:"complete_status,omitempty"`
	TaskList             []*NewbieAnchorTaskItem    `protobuf:"bytes,3,rep,name=task_list,json=taskList,proto3" json:"task_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *NewbieAnchorTaskDay) Reset()         { *m = NewbieAnchorTaskDay{} }
func (m *NewbieAnchorTaskDay) String() string { return proto.CompactTextString(m) }
func (*NewbieAnchorTaskDay) ProtoMessage()    {}
func (*NewbieAnchorTaskDay) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{12}
}
func (m *NewbieAnchorTaskDay) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieAnchorTaskDay.Unmarshal(m, b)
}
func (m *NewbieAnchorTaskDay) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieAnchorTaskDay.Marshal(b, m, deterministic)
}
func (dst *NewbieAnchorTaskDay) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieAnchorTaskDay.Merge(dst, src)
}
func (m *NewbieAnchorTaskDay) XXX_Size() int {
	return xxx_messageInfo_NewbieAnchorTaskDay.Size(m)
}
func (m *NewbieAnchorTaskDay) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieAnchorTaskDay.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieAnchorTaskDay proto.InternalMessageInfo

func (m *NewbieAnchorTaskDay) GetDay() uint64 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *NewbieAnchorTaskDay) GetCompleteStatus() NewbieAnchorTaskDay_Status {
	if m != nil {
		return m.CompleteStatus
	}
	return NewbieAnchorTaskDay_STATUS_UNSPECIFIED
}

func (m *NewbieAnchorTaskDay) GetTaskList() []*NewbieAnchorTaskItem {
	if m != nil {
		return m.TaskList
	}
	return nil
}

// 获取新手入门任务
type NewbieAnchorTaskBeginner struct {
	Days                 []*NewbieAnchorTaskDay             `protobuf:"bytes,1,rep,name=days,proto3" json:"days,omitempty"`
	CompletedDays        uint32                             `protobuf:"varint,2,opt,name=completed_days,json=completedDays,proto3" json:"completed_days,omitempty"`
	Rewards              []*NewbieAnchorTaskBeginner_Reward `protobuf:"bytes,6,rep,name=rewards,proto3" json:"rewards,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *NewbieAnchorTaskBeginner) Reset()         { *m = NewbieAnchorTaskBeginner{} }
func (m *NewbieAnchorTaskBeginner) String() string { return proto.CompactTextString(m) }
func (*NewbieAnchorTaskBeginner) ProtoMessage()    {}
func (*NewbieAnchorTaskBeginner) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{13}
}
func (m *NewbieAnchorTaskBeginner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieAnchorTaskBeginner.Unmarshal(m, b)
}
func (m *NewbieAnchorTaskBeginner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieAnchorTaskBeginner.Marshal(b, m, deterministic)
}
func (dst *NewbieAnchorTaskBeginner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieAnchorTaskBeginner.Merge(dst, src)
}
func (m *NewbieAnchorTaskBeginner) XXX_Size() int {
	return xxx_messageInfo_NewbieAnchorTaskBeginner.Size(m)
}
func (m *NewbieAnchorTaskBeginner) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieAnchorTaskBeginner.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieAnchorTaskBeginner proto.InternalMessageInfo

func (m *NewbieAnchorTaskBeginner) GetDays() []*NewbieAnchorTaskDay {
	if m != nil {
		return m.Days
	}
	return nil
}

func (m *NewbieAnchorTaskBeginner) GetCompletedDays() uint32 {
	if m != nil {
		return m.CompletedDays
	}
	return 0
}

func (m *NewbieAnchorTaskBeginner) GetRewards() []*NewbieAnchorTaskBeginner_Reward {
	if m != nil {
		return m.Rewards
	}
	return nil
}

type NewbieAnchorTaskBeginner_Reward struct {
	ConditionDays        uint32             `protobuf:"varint,1,opt,name=condition_days,json=conditionDays,proto3" json:"condition_days,omitempty"`
	ChestIcon            string             `protobuf:"bytes,3,opt,name=chest_icon,json=chestIcon,proto3" json:"chest_icon,omitempty"`
	ClaimStatus          NewbieRewardStatus `protobuf:"varint,4,opt,name=claim_status,json=claimStatus,proto3,enum=anchor_newbie_guide.NewbieRewardStatus" json:"claim_status,omitempty"`
	MainText             string             `protobuf:"bytes,7,opt,name=main_text,json=mainText,proto3" json:"main_text,omitempty"`
	SubText              string             `protobuf:"bytes,8,opt,name=sub_text,json=subText,proto3" json:"sub_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *NewbieAnchorTaskBeginner_Reward) Reset()         { *m = NewbieAnchorTaskBeginner_Reward{} }
func (m *NewbieAnchorTaskBeginner_Reward) String() string { return proto.CompactTextString(m) }
func (*NewbieAnchorTaskBeginner_Reward) ProtoMessage()    {}
func (*NewbieAnchorTaskBeginner_Reward) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{13, 0}
}
func (m *NewbieAnchorTaskBeginner_Reward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieAnchorTaskBeginner_Reward.Unmarshal(m, b)
}
func (m *NewbieAnchorTaskBeginner_Reward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieAnchorTaskBeginner_Reward.Marshal(b, m, deterministic)
}
func (dst *NewbieAnchorTaskBeginner_Reward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieAnchorTaskBeginner_Reward.Merge(dst, src)
}
func (m *NewbieAnchorTaskBeginner_Reward) XXX_Size() int {
	return xxx_messageInfo_NewbieAnchorTaskBeginner_Reward.Size(m)
}
func (m *NewbieAnchorTaskBeginner_Reward) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieAnchorTaskBeginner_Reward.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieAnchorTaskBeginner_Reward proto.InternalMessageInfo

func (m *NewbieAnchorTaskBeginner_Reward) GetConditionDays() uint32 {
	if m != nil {
		return m.ConditionDays
	}
	return 0
}

func (m *NewbieAnchorTaskBeginner_Reward) GetChestIcon() string {
	if m != nil {
		return m.ChestIcon
	}
	return ""
}

func (m *NewbieAnchorTaskBeginner_Reward) GetClaimStatus() NewbieRewardStatus {
	if m != nil {
		return m.ClaimStatus
	}
	return NewbieRewardStatus_NEWBIE_REWARD_STATUS_UNSPECIFIED
}

func (m *NewbieAnchorTaskBeginner_Reward) GetMainText() string {
	if m != nil {
		return m.MainText
	}
	return ""
}

func (m *NewbieAnchorTaskBeginner_Reward) GetSubText() string {
	if m != nil {
		return m.SubText
	}
	return ""
}

// 获取新手进阶任务
type NewbieAnchorTaskAdvanced struct {
	Period               uint32                                 `protobuf:"varint,1,opt,name=period,proto3" json:"period,omitempty"`
	PeriodStartTime      uint64                                 `protobuf:"varint,2,opt,name=period_start_time,json=periodStartTime,proto3" json:"period_start_time,omitempty"`
	PeriodEndTime        uint64                                 `protobuf:"varint,3,opt,name=period_end_time,json=periodEndTime,proto3" json:"period_end_time,omitempty"`
	TaskList             []*NewbieAnchorTaskItem                `protobuf:"bytes,4,rep,name=task_list,json=taskList,proto3" json:"task_list,omitempty"`
	ClaimStatus          NewbieRewardStatus                     `protobuf:"varint,5,opt,name=claim_status,json=claimStatus,proto3,enum=anchor_newbie_guide.NewbieRewardStatus" json:"claim_status,omitempty"`
	RewardItems          []*NewbieAnchorTaskAdvanced_RewardItem `protobuf:"bytes,6,rep,name=reward_items,json=rewardItems,proto3" json:"reward_items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *NewbieAnchorTaskAdvanced) Reset()         { *m = NewbieAnchorTaskAdvanced{} }
func (m *NewbieAnchorTaskAdvanced) String() string { return proto.CompactTextString(m) }
func (*NewbieAnchorTaskAdvanced) ProtoMessage()    {}
func (*NewbieAnchorTaskAdvanced) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{14}
}
func (m *NewbieAnchorTaskAdvanced) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieAnchorTaskAdvanced.Unmarshal(m, b)
}
func (m *NewbieAnchorTaskAdvanced) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieAnchorTaskAdvanced.Marshal(b, m, deterministic)
}
func (dst *NewbieAnchorTaskAdvanced) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieAnchorTaskAdvanced.Merge(dst, src)
}
func (m *NewbieAnchorTaskAdvanced) XXX_Size() int {
	return xxx_messageInfo_NewbieAnchorTaskAdvanced.Size(m)
}
func (m *NewbieAnchorTaskAdvanced) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieAnchorTaskAdvanced.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieAnchorTaskAdvanced proto.InternalMessageInfo

func (m *NewbieAnchorTaskAdvanced) GetPeriod() uint32 {
	if m != nil {
		return m.Period
	}
	return 0
}

func (m *NewbieAnchorTaskAdvanced) GetPeriodStartTime() uint64 {
	if m != nil {
		return m.PeriodStartTime
	}
	return 0
}

func (m *NewbieAnchorTaskAdvanced) GetPeriodEndTime() uint64 {
	if m != nil {
		return m.PeriodEndTime
	}
	return 0
}

func (m *NewbieAnchorTaskAdvanced) GetTaskList() []*NewbieAnchorTaskItem {
	if m != nil {
		return m.TaskList
	}
	return nil
}

func (m *NewbieAnchorTaskAdvanced) GetClaimStatus() NewbieRewardStatus {
	if m != nil {
		return m.ClaimStatus
	}
	return NewbieRewardStatus_NEWBIE_REWARD_STATUS_UNSPECIFIED
}

func (m *NewbieAnchorTaskAdvanced) GetRewardItems() []*NewbieAnchorTaskAdvanced_RewardItem {
	if m != nil {
		return m.RewardItems
	}
	return nil
}

type NewbieAnchorTaskAdvanced_RewardItem struct {
	RewardName           string   `protobuf:"bytes,1,opt,name=reward_name,json=rewardName,proto3" json:"reward_name,omitempty"`
	RewardIcon           string   `protobuf:"bytes,2,opt,name=reward_icon,json=rewardIcon,proto3" json:"reward_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewbieAnchorTaskAdvanced_RewardItem) Reset()         { *m = NewbieAnchorTaskAdvanced_RewardItem{} }
func (m *NewbieAnchorTaskAdvanced_RewardItem) String() string { return proto.CompactTextString(m) }
func (*NewbieAnchorTaskAdvanced_RewardItem) ProtoMessage()    {}
func (*NewbieAnchorTaskAdvanced_RewardItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{14, 0}
}
func (m *NewbieAnchorTaskAdvanced_RewardItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieAnchorTaskAdvanced_RewardItem.Unmarshal(m, b)
}
func (m *NewbieAnchorTaskAdvanced_RewardItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieAnchorTaskAdvanced_RewardItem.Marshal(b, m, deterministic)
}
func (dst *NewbieAnchorTaskAdvanced_RewardItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieAnchorTaskAdvanced_RewardItem.Merge(dst, src)
}
func (m *NewbieAnchorTaskAdvanced_RewardItem) XXX_Size() int {
	return xxx_messageInfo_NewbieAnchorTaskAdvanced_RewardItem.Size(m)
}
func (m *NewbieAnchorTaskAdvanced_RewardItem) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieAnchorTaskAdvanced_RewardItem.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieAnchorTaskAdvanced_RewardItem proto.InternalMessageInfo

func (m *NewbieAnchorTaskAdvanced_RewardItem) GetRewardName() string {
	if m != nil {
		return m.RewardName
	}
	return ""
}

func (m *NewbieAnchorTaskAdvanced_RewardItem) GetRewardIcon() string {
	if m != nil {
		return m.RewardIcon
	}
	return ""
}

type GetNewbieAnchorTaskResp struct {
	BeginnerTask         *NewbieAnchorTaskBeginner   `protobuf:"bytes,1,opt,name=beginner_task,json=beginnerTask,proto3" json:"beginner_task,omitempty"`
	AdvancedTaskList     []*NewbieAnchorTaskAdvanced `protobuf:"bytes,2,rep,name=advanced_task_list,json=advancedTaskList,proto3" json:"advanced_task_list,omitempty"`
	AutoClaimRewardItems []*TaskRewardItem           `protobuf:"bytes,3,rep,name=auto_claim_reward_items,json=autoClaimRewardItems,proto3" json:"auto_claim_reward_items,omitempty"`
	IsWaitingBegin       bool                        `protobuf:"varint,5,opt,name=is_waiting_begin,json=isWaitingBegin,proto3" json:"is_waiting_begin,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetNewbieAnchorTaskResp) Reset()         { *m = GetNewbieAnchorTaskResp{} }
func (m *GetNewbieAnchorTaskResp) String() string { return proto.CompactTextString(m) }
func (*GetNewbieAnchorTaskResp) ProtoMessage()    {}
func (*GetNewbieAnchorTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{15}
}
func (m *GetNewbieAnchorTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieAnchorTaskResp.Unmarshal(m, b)
}
func (m *GetNewbieAnchorTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieAnchorTaskResp.Marshal(b, m, deterministic)
}
func (dst *GetNewbieAnchorTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieAnchorTaskResp.Merge(dst, src)
}
func (m *GetNewbieAnchorTaskResp) XXX_Size() int {
	return xxx_messageInfo_GetNewbieAnchorTaskResp.Size(m)
}
func (m *GetNewbieAnchorTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieAnchorTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieAnchorTaskResp proto.InternalMessageInfo

func (m *GetNewbieAnchorTaskResp) GetBeginnerTask() *NewbieAnchorTaskBeginner {
	if m != nil {
		return m.BeginnerTask
	}
	return nil
}

func (m *GetNewbieAnchorTaskResp) GetAdvancedTaskList() []*NewbieAnchorTaskAdvanced {
	if m != nil {
		return m.AdvancedTaskList
	}
	return nil
}

func (m *GetNewbieAnchorTaskResp) GetAutoClaimRewardItems() []*TaskRewardItem {
	if m != nil {
		return m.AutoClaimRewardItems
	}
	return nil
}

func (m *GetNewbieAnchorTaskResp) GetIsWaitingBegin() bool {
	if m != nil {
		return m.IsWaitingBegin
	}
	return false
}

// 领取新手入门任务奖励
type ClaimNewbieAnchorTaskRewardReq struct {
	IdentityType         uint32   `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClaimNewbieAnchorTaskRewardReq) Reset()         { *m = ClaimNewbieAnchorTaskRewardReq{} }
func (m *ClaimNewbieAnchorTaskRewardReq) String() string { return proto.CompactTextString(m) }
func (*ClaimNewbieAnchorTaskRewardReq) ProtoMessage()    {}
func (*ClaimNewbieAnchorTaskRewardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{16}
}
func (m *ClaimNewbieAnchorTaskRewardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClaimNewbieAnchorTaskRewardReq.Unmarshal(m, b)
}
func (m *ClaimNewbieAnchorTaskRewardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClaimNewbieAnchorTaskRewardReq.Marshal(b, m, deterministic)
}
func (dst *ClaimNewbieAnchorTaskRewardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClaimNewbieAnchorTaskRewardReq.Merge(dst, src)
}
func (m *ClaimNewbieAnchorTaskRewardReq) XXX_Size() int {
	return xxx_messageInfo_ClaimNewbieAnchorTaskRewardReq.Size(m)
}
func (m *ClaimNewbieAnchorTaskRewardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClaimNewbieAnchorTaskRewardReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClaimNewbieAnchorTaskRewardReq proto.InternalMessageInfo

func (m *ClaimNewbieAnchorTaskRewardReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

func (m *ClaimNewbieAnchorTaskRewardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 领取新手进阶任务奖励
type ClaimNewbieAnchorTaskAdvancedRewardReq struct {
	IdentityType         uint32   `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	Period               uint32   `protobuf:"varint,2,opt,name=period,proto3" json:"period,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) Reset() {
	*m = ClaimNewbieAnchorTaskAdvancedRewardReq{}
}
func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) String() string { return proto.CompactTextString(m) }
func (*ClaimNewbieAnchorTaskAdvancedRewardReq) ProtoMessage()    {}
func (*ClaimNewbieAnchorTaskAdvancedRewardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{17}
}
func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClaimNewbieAnchorTaskAdvancedRewardReq.Unmarshal(m, b)
}
func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClaimNewbieAnchorTaskAdvancedRewardReq.Marshal(b, m, deterministic)
}
func (dst *ClaimNewbieAnchorTaskAdvancedRewardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClaimNewbieAnchorTaskAdvancedRewardReq.Merge(dst, src)
}
func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) XXX_Size() int {
	return xxx_messageInfo_ClaimNewbieAnchorTaskAdvancedRewardReq.Size(m)
}
func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClaimNewbieAnchorTaskAdvancedRewardReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClaimNewbieAnchorTaskAdvancedRewardReq proto.InternalMessageInfo

func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) GetPeriod() uint32 {
	if m != nil {
		return m.Period
	}
	return 0
}

func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 领取新手任务奖励
type ClaimNewbieAnchorTaskRewardResp struct {
	Rewards              []*TaskRewardItem `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ClaimNewbieAnchorTaskRewardResp) Reset()         { *m = ClaimNewbieAnchorTaskRewardResp{} }
func (m *ClaimNewbieAnchorTaskRewardResp) String() string { return proto.CompactTextString(m) }
func (*ClaimNewbieAnchorTaskRewardResp) ProtoMessage()    {}
func (*ClaimNewbieAnchorTaskRewardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{18}
}
func (m *ClaimNewbieAnchorTaskRewardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClaimNewbieAnchorTaskRewardResp.Unmarshal(m, b)
}
func (m *ClaimNewbieAnchorTaskRewardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClaimNewbieAnchorTaskRewardResp.Marshal(b, m, deterministic)
}
func (dst *ClaimNewbieAnchorTaskRewardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClaimNewbieAnchorTaskRewardResp.Merge(dst, src)
}
func (m *ClaimNewbieAnchorTaskRewardResp) XXX_Size() int {
	return xxx_messageInfo_ClaimNewbieAnchorTaskRewardResp.Size(m)
}
func (m *ClaimNewbieAnchorTaskRewardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClaimNewbieAnchorTaskRewardResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClaimNewbieAnchorTaskRewardResp proto.InternalMessageInfo

func (m *ClaimNewbieAnchorTaskRewardResp) GetRewards() []*TaskRewardItem {
	if m != nil {
		return m.Rewards
	}
	return nil
}

// 单个奖励项
type TaskRewardItem struct {
	RewardName           string   `protobuf:"bytes,1,opt,name=reward_name,json=rewardName,proto3" json:"reward_name,omitempty"`
	RewardIcon           string   `protobuf:"bytes,2,opt,name=reward_icon,json=rewardIcon,proto3" json:"reward_icon,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskRewardItem) Reset()         { *m = TaskRewardItem{} }
func (m *TaskRewardItem) String() string { return proto.CompactTextString(m) }
func (*TaskRewardItem) ProtoMessage()    {}
func (*TaskRewardItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{19}
}
func (m *TaskRewardItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskRewardItem.Unmarshal(m, b)
}
func (m *TaskRewardItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskRewardItem.Marshal(b, m, deterministic)
}
func (dst *TaskRewardItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskRewardItem.Merge(dst, src)
}
func (m *TaskRewardItem) XXX_Size() int {
	return xxx_messageInfo_TaskRewardItem.Size(m)
}
func (m *TaskRewardItem) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskRewardItem.DiscardUnknown(m)
}

var xxx_messageInfo_TaskRewardItem proto.InternalMessageInfo

func (m *TaskRewardItem) GetRewardName() string {
	if m != nil {
		return m.RewardName
	}
	return ""
}

func (m *TaskRewardItem) GetRewardIcon() string {
	if m != nil {
		return m.RewardIcon
	}
	return ""
}

func (m *TaskRewardItem) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// 奖励条件
type TaskRewardCondition struct {
	ConditionDesc        string            `protobuf:"bytes,1,opt,name=condition_desc,json=conditionDesc,proto3" json:"condition_desc,omitempty"`
	Rewards              []*TaskRewardItem `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"`
	IsClaimed            bool              `protobuf:"varint,3,opt,name=is_claimed,json=isClaimed,proto3" json:"is_claimed,omitempty"`
	ChestIcon            string            `protobuf:"bytes,4,opt,name=chest_icon,json=chestIcon,proto3" json:"chest_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *TaskRewardCondition) Reset()         { *m = TaskRewardCondition{} }
func (m *TaskRewardCondition) String() string { return proto.CompactTextString(m) }
func (*TaskRewardCondition) ProtoMessage()    {}
func (*TaskRewardCondition) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{20}
}
func (m *TaskRewardCondition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskRewardCondition.Unmarshal(m, b)
}
func (m *TaskRewardCondition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskRewardCondition.Marshal(b, m, deterministic)
}
func (dst *TaskRewardCondition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskRewardCondition.Merge(dst, src)
}
func (m *TaskRewardCondition) XXX_Size() int {
	return xxx_messageInfo_TaskRewardCondition.Size(m)
}
func (m *TaskRewardCondition) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskRewardCondition.DiscardUnknown(m)
}

var xxx_messageInfo_TaskRewardCondition proto.InternalMessageInfo

func (m *TaskRewardCondition) GetConditionDesc() string {
	if m != nil {
		return m.ConditionDesc
	}
	return ""
}

func (m *TaskRewardCondition) GetRewards() []*TaskRewardItem {
	if m != nil {
		return m.Rewards
	}
	return nil
}

func (m *TaskRewardCondition) GetIsClaimed() bool {
	if m != nil {
		return m.IsClaimed
	}
	return false
}

func (m *TaskRewardCondition) GetChestIcon() string {
	if m != nil {
		return m.ChestIcon
	}
	return ""
}

// 获取新手任务奖励列表
type GetNewbieAnchorTaskRewardListReq struct {
	IdentityType         uint32               `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	TaskType             NewbieAnchorTaskType `protobuf:"varint,2,opt,name=task_type,json=taskType,proto3,enum=anchor_newbie_guide.NewbieAnchorTaskType" json:"task_type,omitempty"`
	Uid                  uint32               `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetNewbieAnchorTaskRewardListReq) Reset()         { *m = GetNewbieAnchorTaskRewardListReq{} }
func (m *GetNewbieAnchorTaskRewardListReq) String() string { return proto.CompactTextString(m) }
func (*GetNewbieAnchorTaskRewardListReq) ProtoMessage()    {}
func (*GetNewbieAnchorTaskRewardListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{21}
}
func (m *GetNewbieAnchorTaskRewardListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieAnchorTaskRewardListReq.Unmarshal(m, b)
}
func (m *GetNewbieAnchorTaskRewardListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieAnchorTaskRewardListReq.Marshal(b, m, deterministic)
}
func (dst *GetNewbieAnchorTaskRewardListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieAnchorTaskRewardListReq.Merge(dst, src)
}
func (m *GetNewbieAnchorTaskRewardListReq) XXX_Size() int {
	return xxx_messageInfo_GetNewbieAnchorTaskRewardListReq.Size(m)
}
func (m *GetNewbieAnchorTaskRewardListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieAnchorTaskRewardListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieAnchorTaskRewardListReq proto.InternalMessageInfo

func (m *GetNewbieAnchorTaskRewardListReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

func (m *GetNewbieAnchorTaskRewardListReq) GetTaskType() NewbieAnchorTaskType {
	if m != nil {
		return m.TaskType
	}
	return NewbieAnchorTaskType_NEWBIE_ANCHOR_TASK_TYPE_UNSPECIFIED
}

func (m *GetNewbieAnchorTaskRewardListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetNewbieAnchorTaskRewardListResp struct {
	RewardConditionList  []*TaskRewardCondition `protobuf:"bytes,1,rep,name=reward_condition_list,json=rewardConditionList,proto3" json:"reward_condition_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetNewbieAnchorTaskRewardListResp) Reset()         { *m = GetNewbieAnchorTaskRewardListResp{} }
func (m *GetNewbieAnchorTaskRewardListResp) String() string { return proto.CompactTextString(m) }
func (*GetNewbieAnchorTaskRewardListResp) ProtoMessage()    {}
func (*GetNewbieAnchorTaskRewardListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{22}
}
func (m *GetNewbieAnchorTaskRewardListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieAnchorTaskRewardListResp.Unmarshal(m, b)
}
func (m *GetNewbieAnchorTaskRewardListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieAnchorTaskRewardListResp.Marshal(b, m, deterministic)
}
func (dst *GetNewbieAnchorTaskRewardListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieAnchorTaskRewardListResp.Merge(dst, src)
}
func (m *GetNewbieAnchorTaskRewardListResp) XXX_Size() int {
	return xxx_messageInfo_GetNewbieAnchorTaskRewardListResp.Size(m)
}
func (m *GetNewbieAnchorTaskRewardListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieAnchorTaskRewardListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieAnchorTaskRewardListResp proto.InternalMessageInfo

func (m *GetNewbieAnchorTaskRewardListResp) GetRewardConditionList() []*TaskRewardCondition {
	if m != nil {
		return m.RewardConditionList
	}
	return nil
}

// 测试重置新手任务
type TestResetNewbieAnchorTaskReq struct {
	Certain              bool     `protobuf:"varint,1,opt,name=certain,proto3" json:"certain,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestResetNewbieAnchorTaskReq) Reset()         { *m = TestResetNewbieAnchorTaskReq{} }
func (m *TestResetNewbieAnchorTaskReq) String() string { return proto.CompactTextString(m) }
func (*TestResetNewbieAnchorTaskReq) ProtoMessage()    {}
func (*TestResetNewbieAnchorTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{23}
}
func (m *TestResetNewbieAnchorTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestResetNewbieAnchorTaskReq.Unmarshal(m, b)
}
func (m *TestResetNewbieAnchorTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestResetNewbieAnchorTaskReq.Marshal(b, m, deterministic)
}
func (dst *TestResetNewbieAnchorTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestResetNewbieAnchorTaskReq.Merge(dst, src)
}
func (m *TestResetNewbieAnchorTaskReq) XXX_Size() int {
	return xxx_messageInfo_TestResetNewbieAnchorTaskReq.Size(m)
}
func (m *TestResetNewbieAnchorTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestResetNewbieAnchorTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestResetNewbieAnchorTaskReq proto.InternalMessageInfo

func (m *TestResetNewbieAnchorTaskReq) GetCertain() bool {
	if m != nil {
		return m.Certain
	}
	return false
}

func (m *TestResetNewbieAnchorTaskReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type TestResetNewbieAnchorTaskResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestResetNewbieAnchorTaskResp) Reset()         { *m = TestResetNewbieAnchorTaskResp{} }
func (m *TestResetNewbieAnchorTaskResp) String() string { return proto.CompactTextString(m) }
func (*TestResetNewbieAnchorTaskResp) ProtoMessage()    {}
func (*TestResetNewbieAnchorTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{24}
}
func (m *TestResetNewbieAnchorTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestResetNewbieAnchorTaskResp.Unmarshal(m, b)
}
func (m *TestResetNewbieAnchorTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestResetNewbieAnchorTaskResp.Marshal(b, m, deterministic)
}
func (dst *TestResetNewbieAnchorTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestResetNewbieAnchorTaskResp.Merge(dst, src)
}
func (m *TestResetNewbieAnchorTaskResp) XXX_Size() int {
	return xxx_messageInfo_TestResetNewbieAnchorTaskResp.Size(m)
}
func (m *TestResetNewbieAnchorTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestResetNewbieAnchorTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestResetNewbieAnchorTaskResp proto.InternalMessageInfo

// 测试完成新手任务
type TestCompleteNewbieAnchorTaskReq struct {
	Certain              bool                   `protobuf:"varint,1,opt,name=certain,proto3" json:"certain,omitempty"`
	Uid                  uint32                 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Day                  uint32                 `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty"`
	TaskSign             []NewbieAnchorTaskSign `protobuf:"varint,4,rep,packed,name=task_sign,json=taskSign,proto3,enum=anchor_newbie_guide.NewbieAnchorTaskSign" json:"task_sign,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *TestCompleteNewbieAnchorTaskReq) Reset()         { *m = TestCompleteNewbieAnchorTaskReq{} }
func (m *TestCompleteNewbieAnchorTaskReq) String() string { return proto.CompactTextString(m) }
func (*TestCompleteNewbieAnchorTaskReq) ProtoMessage()    {}
func (*TestCompleteNewbieAnchorTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{25}
}
func (m *TestCompleteNewbieAnchorTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestCompleteNewbieAnchorTaskReq.Unmarshal(m, b)
}
func (m *TestCompleteNewbieAnchorTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestCompleteNewbieAnchorTaskReq.Marshal(b, m, deterministic)
}
func (dst *TestCompleteNewbieAnchorTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestCompleteNewbieAnchorTaskReq.Merge(dst, src)
}
func (m *TestCompleteNewbieAnchorTaskReq) XXX_Size() int {
	return xxx_messageInfo_TestCompleteNewbieAnchorTaskReq.Size(m)
}
func (m *TestCompleteNewbieAnchorTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestCompleteNewbieAnchorTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestCompleteNewbieAnchorTaskReq proto.InternalMessageInfo

func (m *TestCompleteNewbieAnchorTaskReq) GetCertain() bool {
	if m != nil {
		return m.Certain
	}
	return false
}

func (m *TestCompleteNewbieAnchorTaskReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TestCompleteNewbieAnchorTaskReq) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *TestCompleteNewbieAnchorTaskReq) GetTaskSign() []NewbieAnchorTaskSign {
	if m != nil {
		return m.TaskSign
	}
	return nil
}

type TestCompleteNewbieAnchorTaskResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestCompleteNewbieAnchorTaskResp) Reset()         { *m = TestCompleteNewbieAnchorTaskResp{} }
func (m *TestCompleteNewbieAnchorTaskResp) String() string { return proto.CompactTextString(m) }
func (*TestCompleteNewbieAnchorTaskResp) ProtoMessage()    {}
func (*TestCompleteNewbieAnchorTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{26}
}
func (m *TestCompleteNewbieAnchorTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestCompleteNewbieAnchorTaskResp.Unmarshal(m, b)
}
func (m *TestCompleteNewbieAnchorTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestCompleteNewbieAnchorTaskResp.Marshal(b, m, deterministic)
}
func (dst *TestCompleteNewbieAnchorTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestCompleteNewbieAnchorTaskResp.Merge(dst, src)
}
func (m *TestCompleteNewbieAnchorTaskResp) XXX_Size() int {
	return xxx_messageInfo_TestCompleteNewbieAnchorTaskResp.Size(m)
}
func (m *TestCompleteNewbieAnchorTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestCompleteNewbieAnchorTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestCompleteNewbieAnchorTaskResp proto.InternalMessageInfo

// 测试手动触发计划任务
type TestTriggerTimerReq struct {
	Certain              bool     `protobuf:"varint,1,opt,name=certain,proto3" json:"certain,omitempty"`
	TaskName             string   `protobuf:"bytes,2,opt,name=task_name,json=taskName,proto3" json:"task_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestTriggerTimerReq) Reset()         { *m = TestTriggerTimerReq{} }
func (m *TestTriggerTimerReq) String() string { return proto.CompactTextString(m) }
func (*TestTriggerTimerReq) ProtoMessage()    {}
func (*TestTriggerTimerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{27}
}
func (m *TestTriggerTimerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestTriggerTimerReq.Unmarshal(m, b)
}
func (m *TestTriggerTimerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestTriggerTimerReq.Marshal(b, m, deterministic)
}
func (dst *TestTriggerTimerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestTriggerTimerReq.Merge(dst, src)
}
func (m *TestTriggerTimerReq) XXX_Size() int {
	return xxx_messageInfo_TestTriggerTimerReq.Size(m)
}
func (m *TestTriggerTimerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestTriggerTimerReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestTriggerTimerReq proto.InternalMessageInfo

func (m *TestTriggerTimerReq) GetCertain() bool {
	if m != nil {
		return m.Certain
	}
	return false
}

func (m *TestTriggerTimerReq) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

type TestTriggerTimerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestTriggerTimerResp) Reset()         { *m = TestTriggerTimerResp{} }
func (m *TestTriggerTimerResp) String() string { return proto.CompactTextString(m) }
func (*TestTriggerTimerResp) ProtoMessage()    {}
func (*TestTriggerTimerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{28}
}
func (m *TestTriggerTimerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestTriggerTimerResp.Unmarshal(m, b)
}
func (m *TestTriggerTimerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestTriggerTimerResp.Marshal(b, m, deterministic)
}
func (dst *TestTriggerTimerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestTriggerTimerResp.Merge(dst, src)
}
func (m *TestTriggerTimerResp) XXX_Size() int {
	return xxx_messageInfo_TestTriggerTimerResp.Size(m)
}
func (m *TestTriggerTimerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestTriggerTimerResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestTriggerTimerResp proto.InternalMessageInfo

type GetChannelLiveEndGuidePopupRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelLiveEndGuidePopupRequest) Reset()         { *m = GetChannelLiveEndGuidePopupRequest{} }
func (m *GetChannelLiveEndGuidePopupRequest) String() string { return proto.CompactTextString(m) }
func (*GetChannelLiveEndGuidePopupRequest) ProtoMessage()    {}
func (*GetChannelLiveEndGuidePopupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{29}
}
func (m *GetChannelLiveEndGuidePopupRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLiveEndGuidePopupRequest.Unmarshal(m, b)
}
func (m *GetChannelLiveEndGuidePopupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLiveEndGuidePopupRequest.Marshal(b, m, deterministic)
}
func (dst *GetChannelLiveEndGuidePopupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLiveEndGuidePopupRequest.Merge(dst, src)
}
func (m *GetChannelLiveEndGuidePopupRequest) XXX_Size() int {
	return xxx_messageInfo_GetChannelLiveEndGuidePopupRequest.Size(m)
}
func (m *GetChannelLiveEndGuidePopupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLiveEndGuidePopupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLiveEndGuidePopupRequest proto.InternalMessageInfo

func (m *GetChannelLiveEndGuidePopupRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetChannelLiveEndGuidePopupResponse struct {
	PopupType            uint32                 `protobuf:"varint,1,opt,name=popup_type,json=popupType,proto3" json:"popup_type,omitempty"`
	UserInfoList         []*InteractionUserInfo `protobuf:"bytes,2,rep,name=user_info_list,json=userInfoList,proto3" json:"user_info_list,omitempty"`
	AnchorInfoList       []*RecommendAnchorInfo `protobuf:"bytes,3,rep,name=anchor_info_list,json=anchorInfoList,proto3" json:"anchor_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetChannelLiveEndGuidePopupResponse) Reset()         { *m = GetChannelLiveEndGuidePopupResponse{} }
func (m *GetChannelLiveEndGuidePopupResponse) String() string { return proto.CompactTextString(m) }
func (*GetChannelLiveEndGuidePopupResponse) ProtoMessage()    {}
func (*GetChannelLiveEndGuidePopupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{30}
}
func (m *GetChannelLiveEndGuidePopupResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLiveEndGuidePopupResponse.Unmarshal(m, b)
}
func (m *GetChannelLiveEndGuidePopupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLiveEndGuidePopupResponse.Marshal(b, m, deterministic)
}
func (dst *GetChannelLiveEndGuidePopupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLiveEndGuidePopupResponse.Merge(dst, src)
}
func (m *GetChannelLiveEndGuidePopupResponse) XXX_Size() int {
	return xxx_messageInfo_GetChannelLiveEndGuidePopupResponse.Size(m)
}
func (m *GetChannelLiveEndGuidePopupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLiveEndGuidePopupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLiveEndGuidePopupResponse proto.InternalMessageInfo

func (m *GetChannelLiveEndGuidePopupResponse) GetPopupType() uint32 {
	if m != nil {
		return m.PopupType
	}
	return 0
}

func (m *GetChannelLiveEndGuidePopupResponse) GetUserInfoList() []*InteractionUserInfo {
	if m != nil {
		return m.UserInfoList
	}
	return nil
}

func (m *GetChannelLiveEndGuidePopupResponse) GetAnchorInfoList() []*RecommendAnchorInfo {
	if m != nil {
		return m.AnchorInfoList
	}
	return nil
}

type InteractionUserInfo struct {
	Uid                   uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account               string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname              string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	NobilityLevel         float32  `protobuf:"fixed32,4,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	TagList               []string `protobuf:"bytes,5,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	InteractionButtonText string   `protobuf:"bytes,6,opt,name=interaction_button_text,json=interactionButtonText,proto3" json:"interaction_button_text,omitempty"`
	IsUserOl              bool     `protobuf:"varint,7,opt,name=is_user_ol,json=isUserOl,proto3" json:"is_user_ol,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *InteractionUserInfo) Reset()         { *m = InteractionUserInfo{} }
func (m *InteractionUserInfo) String() string { return proto.CompactTextString(m) }
func (*InteractionUserInfo) ProtoMessage()    {}
func (*InteractionUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{31}
}
func (m *InteractionUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractionUserInfo.Unmarshal(m, b)
}
func (m *InteractionUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractionUserInfo.Marshal(b, m, deterministic)
}
func (dst *InteractionUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractionUserInfo.Merge(dst, src)
}
func (m *InteractionUserInfo) XXX_Size() int {
	return xxx_messageInfo_InteractionUserInfo.Size(m)
}
func (m *InteractionUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractionUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_InteractionUserInfo proto.InternalMessageInfo

func (m *InteractionUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *InteractionUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *InteractionUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *InteractionUserInfo) GetNobilityLevel() float32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *InteractionUserInfo) GetTagList() []string {
	if m != nil {
		return m.TagList
	}
	return nil
}

func (m *InteractionUserInfo) GetInteractionButtonText() string {
	if m != nil {
		return m.InteractionButtonText
	}
	return ""
}

func (m *InteractionUserInfo) GetIsUserOl() bool {
	if m != nil {
		return m.IsUserOl
	}
	return false
}

// 主播认证标识
type NewbieAnchorCertInfo struct {
	ItemName             string   `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	BaseImgurl           string   `protobuf:"bytes,2,opt,name=base_imgurl,json=baseImgurl,proto3" json:"base_imgurl,omitempty"`
	ShadowColor          string   `protobuf:"bytes,3,opt,name=shadow_color,json=shadowColor,proto3" json:"shadow_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewbieAnchorCertInfo) Reset()         { *m = NewbieAnchorCertInfo{} }
func (m *NewbieAnchorCertInfo) String() string { return proto.CompactTextString(m) }
func (*NewbieAnchorCertInfo) ProtoMessage()    {}
func (*NewbieAnchorCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{32}
}
func (m *NewbieAnchorCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieAnchorCertInfo.Unmarshal(m, b)
}
func (m *NewbieAnchorCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieAnchorCertInfo.Marshal(b, m, deterministic)
}
func (dst *NewbieAnchorCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieAnchorCertInfo.Merge(dst, src)
}
func (m *NewbieAnchorCertInfo) XXX_Size() int {
	return xxx_messageInfo_NewbieAnchorCertInfo.Size(m)
}
func (m *NewbieAnchorCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieAnchorCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieAnchorCertInfo proto.InternalMessageInfo

func (m *NewbieAnchorCertInfo) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *NewbieAnchorCertInfo) GetBaseImgurl() string {
	if m != nil {
		return m.BaseImgurl
	}
	return ""
}

func (m *NewbieAnchorCertInfo) GetShadowColor() string {
	if m != nil {
		return m.ShadowColor
	}
	return ""
}

type RecommendAnchorInfo struct {
	Uid                  uint32                  `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32                  `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Account              string                  `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string                  `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	CertUrlList          []*NewbieAnchorCertInfo `protobuf:"bytes,5,rep,name=cert_url_list,json=certUrlList,proto3" json:"cert_url_list,omitempty"`
	IsUserOl             bool                    `protobuf:"varint,6,opt,name=is_user_ol,json=isUserOl,proto3" json:"is_user_ol,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *RecommendAnchorInfo) Reset()         { *m = RecommendAnchorInfo{} }
func (m *RecommendAnchorInfo) String() string { return proto.CompactTextString(m) }
func (*RecommendAnchorInfo) ProtoMessage()    {}
func (*RecommendAnchorInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{33}
}
func (m *RecommendAnchorInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendAnchorInfo.Unmarshal(m, b)
}
func (m *RecommendAnchorInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendAnchorInfo.Marshal(b, m, deterministic)
}
func (dst *RecommendAnchorInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendAnchorInfo.Merge(dst, src)
}
func (m *RecommendAnchorInfo) XXX_Size() int {
	return xxx_messageInfo_RecommendAnchorInfo.Size(m)
}
func (m *RecommendAnchorInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendAnchorInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendAnchorInfo proto.InternalMessageInfo

func (m *RecommendAnchorInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecommendAnchorInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecommendAnchorInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *RecommendAnchorInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *RecommendAnchorInfo) GetCertUrlList() []*NewbieAnchorCertInfo {
	if m != nil {
		return m.CertUrlList
	}
	return nil
}

func (m *RecommendAnchorInfo) GetIsUserOl() bool {
	if m != nil {
		return m.IsUserOl
	}
	return false
}

type SendChannelLiveThanksRequest struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendChannelLiveThanksRequest) Reset()         { *m = SendChannelLiveThanksRequest{} }
func (m *SendChannelLiveThanksRequest) String() string { return proto.CompactTextString(m) }
func (*SendChannelLiveThanksRequest) ProtoMessage()    {}
func (*SendChannelLiveThanksRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{34}
}
func (m *SendChannelLiveThanksRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendChannelLiveThanksRequest.Unmarshal(m, b)
}
func (m *SendChannelLiveThanksRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendChannelLiveThanksRequest.Marshal(b, m, deterministic)
}
func (dst *SendChannelLiveThanksRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendChannelLiveThanksRequest.Merge(dst, src)
}
func (m *SendChannelLiveThanksRequest) XXX_Size() int {
	return xxx_messageInfo_SendChannelLiveThanksRequest.Size(m)
}
func (m *SendChannelLiveThanksRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendChannelLiveThanksRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendChannelLiveThanksRequest proto.InternalMessageInfo

func (m *SendChannelLiveThanksRequest) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *SendChannelLiveThanksRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type SendChannelLiveThanksResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendChannelLiveThanksResponse) Reset()         { *m = SendChannelLiveThanksResponse{} }
func (m *SendChannelLiveThanksResponse) String() string { return proto.CompactTextString(m) }
func (*SendChannelLiveThanksResponse) ProtoMessage()    {}
func (*SendChannelLiveThanksResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba, []int{35}
}
func (m *SendChannelLiveThanksResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendChannelLiveThanksResponse.Unmarshal(m, b)
}
func (m *SendChannelLiveThanksResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendChannelLiveThanksResponse.Marshal(b, m, deterministic)
}
func (dst *SendChannelLiveThanksResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendChannelLiveThanksResponse.Merge(dst, src)
}
func (m *SendChannelLiveThanksResponse) XXX_Size() int {
	return xxx_messageInfo_SendChannelLiveThanksResponse.Size(m)
}
func (m *SendChannelLiveThanksResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SendChannelLiveThanksResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SendChannelLiveThanksResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetNewbieAnchorGuideInfoReq)(nil), "anchor_newbie_guide.GetNewbieAnchorGuideInfoReq")
	proto.RegisterType((*GetNewbieAnchorGuideInfoResp)(nil), "anchor_newbie_guide.GetNewbieAnchorGuideInfoResp")
	proto.RegisterType((*KnowledgeTask)(nil), "anchor_newbie_guide.KnowledgeTask")
	proto.RegisterType((*NewbieGuideContent)(nil), "anchor_newbie_guide.NewbieGuideContent")
	proto.RegisterType((*NewbieGuideSubTheme)(nil), "anchor_newbie_guide.NewbieGuideSubTheme")
	proto.RegisterType((*NewbieGuideTheme)(nil), "anchor_newbie_guide.NewbieGuideTheme")
	proto.RegisterType((*GetNewbieGuidePopInfoReq)(nil), "anchor_newbie_guide.GetNewbieGuidePopInfoReq")
	proto.RegisterMapType((map[uint32]uint32)(nil), "anchor_newbie_guide.GetNewbieGuidePopInfoReq.IdentityObtainDaysEntry")
	proto.RegisterType((*GetNewbieGuidePopInfoResp)(nil), "anchor_newbie_guide.GetNewbieGuidePopInfoResp")
	proto.RegisterType((*GetNewbieWidgetInfoReq)(nil), "anchor_newbie_guide.GetNewbieWidgetInfoReq")
	proto.RegisterType((*GetNewbieWidgetInfoResp)(nil), "anchor_newbie_guide.GetNewbieWidgetInfoResp")
	proto.RegisterType((*NewbieAnchorTaskItem)(nil), "anchor_newbie_guide.NewbieAnchorTaskItem")
	proto.RegisterType((*GetNewbieAnchorTaskReq)(nil), "anchor_newbie_guide.GetNewbieAnchorTaskReq")
	proto.RegisterType((*NewbieAnchorTaskDay)(nil), "anchor_newbie_guide.NewbieAnchorTaskDay")
	proto.RegisterType((*NewbieAnchorTaskBeginner)(nil), "anchor_newbie_guide.NewbieAnchorTaskBeginner")
	proto.RegisterType((*NewbieAnchorTaskBeginner_Reward)(nil), "anchor_newbie_guide.NewbieAnchorTaskBeginner.Reward")
	proto.RegisterType((*NewbieAnchorTaskAdvanced)(nil), "anchor_newbie_guide.NewbieAnchorTaskAdvanced")
	proto.RegisterType((*NewbieAnchorTaskAdvanced_RewardItem)(nil), "anchor_newbie_guide.NewbieAnchorTaskAdvanced.RewardItem")
	proto.RegisterType((*GetNewbieAnchorTaskResp)(nil), "anchor_newbie_guide.GetNewbieAnchorTaskResp")
	proto.RegisterType((*ClaimNewbieAnchorTaskRewardReq)(nil), "anchor_newbie_guide.ClaimNewbieAnchorTaskRewardReq")
	proto.RegisterType((*ClaimNewbieAnchorTaskAdvancedRewardReq)(nil), "anchor_newbie_guide.ClaimNewbieAnchorTaskAdvancedRewardReq")
	proto.RegisterType((*ClaimNewbieAnchorTaskRewardResp)(nil), "anchor_newbie_guide.ClaimNewbieAnchorTaskRewardResp")
	proto.RegisterType((*TaskRewardItem)(nil), "anchor_newbie_guide.TaskRewardItem")
	proto.RegisterType((*TaskRewardCondition)(nil), "anchor_newbie_guide.TaskRewardCondition")
	proto.RegisterType((*GetNewbieAnchorTaskRewardListReq)(nil), "anchor_newbie_guide.GetNewbieAnchorTaskRewardListReq")
	proto.RegisterType((*GetNewbieAnchorTaskRewardListResp)(nil), "anchor_newbie_guide.GetNewbieAnchorTaskRewardListResp")
	proto.RegisterType((*TestResetNewbieAnchorTaskReq)(nil), "anchor_newbie_guide.TestResetNewbieAnchorTaskReq")
	proto.RegisterType((*TestResetNewbieAnchorTaskResp)(nil), "anchor_newbie_guide.TestResetNewbieAnchorTaskResp")
	proto.RegisterType((*TestCompleteNewbieAnchorTaskReq)(nil), "anchor_newbie_guide.TestCompleteNewbieAnchorTaskReq")
	proto.RegisterType((*TestCompleteNewbieAnchorTaskResp)(nil), "anchor_newbie_guide.TestCompleteNewbieAnchorTaskResp")
	proto.RegisterType((*TestTriggerTimerReq)(nil), "anchor_newbie_guide.TestTriggerTimerReq")
	proto.RegisterType((*TestTriggerTimerResp)(nil), "anchor_newbie_guide.TestTriggerTimerResp")
	proto.RegisterType((*GetChannelLiveEndGuidePopupRequest)(nil), "anchor_newbie_guide.GetChannelLiveEndGuidePopupRequest")
	proto.RegisterType((*GetChannelLiveEndGuidePopupResponse)(nil), "anchor_newbie_guide.GetChannelLiveEndGuidePopupResponse")
	proto.RegisterType((*InteractionUserInfo)(nil), "anchor_newbie_guide.InteractionUserInfo")
	proto.RegisterType((*NewbieAnchorCertInfo)(nil), "anchor_newbie_guide.NewbieAnchorCertInfo")
	proto.RegisterType((*RecommendAnchorInfo)(nil), "anchor_newbie_guide.RecommendAnchorInfo")
	proto.RegisterType((*SendChannelLiveThanksRequest)(nil), "anchor_newbie_guide.SendChannelLiveThanksRequest")
	proto.RegisterType((*SendChannelLiveThanksResponse)(nil), "anchor_newbie_guide.SendChannelLiveThanksResponse")
	proto.RegisterEnum("anchor_newbie_guide.KnowledgeTaskStatus", KnowledgeTaskStatus_name, KnowledgeTaskStatus_value)
	proto.RegisterEnum("anchor_newbie_guide.WidgetType", WidgetType_name, WidgetType_value)
	proto.RegisterEnum("anchor_newbie_guide.WidgetStatus", WidgetStatus_name, WidgetStatus_value)
	proto.RegisterEnum("anchor_newbie_guide.NewbieAnchorTaskType", NewbieAnchorTaskType_name, NewbieAnchorTaskType_value)
	proto.RegisterEnum("anchor_newbie_guide.NewbieRewardStatus", NewbieRewardStatus_name, NewbieRewardStatus_value)
	proto.RegisterEnum("anchor_newbie_guide.NewbieAnchorTaskSign", NewbieAnchorTaskSign_name, NewbieAnchorTaskSign_value)
	proto.RegisterEnum("anchor_newbie_guide.NewbieAnchorTaskInitType", NewbieAnchorTaskInitType_name, NewbieAnchorTaskInitType_value)
	proto.RegisterEnum("anchor_newbie_guide.ChannelLiveEndGuidePopupType", ChannelLiveEndGuidePopupType_name, ChannelLiveEndGuidePopupType_value)
	proto.RegisterEnum("anchor_newbie_guide.NewbieAnchorTaskDay_Status", NewbieAnchorTaskDay_Status_name, NewbieAnchorTaskDay_Status_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AnchorNewbieGuideServiceClient is the client API for AnchorNewbieGuideService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AnchorNewbieGuideServiceClient interface {
	// 获取新手指南信息
	GetNewbieAnchorGuideInfo(ctx context.Context, in *GetNewbieAnchorGuideInfoReq, opts ...grpc.CallOption) (*GetNewbieAnchorGuideInfoResp, error)
	// 获取新手指南泡泡信息
	GetNewbieGuidePopInfo(ctx context.Context, in *GetNewbieGuidePopInfoReq, opts ...grpc.CallOption) (*GetNewbieGuidePopInfoResp, error)
	// 获取新手引导挂件信息
	GetNewbieWidgetInfo(ctx context.Context, in *GetNewbieWidgetInfoReq, opts ...grpc.CallOption) (*GetNewbieWidgetInfoResp, error)
	// 获取新手任务
	GetNewbieAnchorTask(ctx context.Context, in *GetNewbieAnchorTaskReq, opts ...grpc.CallOption) (*GetNewbieAnchorTaskResp, error)
	// 领取新手任务奖励
	ClaimNewbieAnchorTaskReward(ctx context.Context, in *ClaimNewbieAnchorTaskRewardReq, opts ...grpc.CallOption) (*ClaimNewbieAnchorTaskRewardResp, error)
	// 领取新手进阶任务奖励
	ClaimNewbieAnchorTaskAdvancedReward(ctx context.Context, in *ClaimNewbieAnchorTaskAdvancedRewardReq, opts ...grpc.CallOption) (*ClaimNewbieAnchorTaskRewardResp, error)
	// 获取新手任务奖励列表
	GetNewbieAnchorTaskRewardList(ctx context.Context, in *GetNewbieAnchorTaskRewardListReq, opts ...grpc.CallOption) (*GetNewbieAnchorTaskRewardListResp, error)
	// 获取直播间结束引导弹窗信息
	GetChannelLiveEndGuidePopup(ctx context.Context, in *GetChannelLiveEndGuidePopupRequest, opts ...grpc.CallOption) (*GetChannelLiveEndGuidePopupResponse, error)
	// 发送直播间感谢互动
	SendChannelLiveThanks(ctx context.Context, in *SendChannelLiveThanksRequest, opts ...grpc.CallOption) (*SendChannelLiveThanksResponse, error)
	// 测试接口 重置新手任务
	TestResetNewbieAnchorTask(ctx context.Context, in *TestResetNewbieAnchorTaskReq, opts ...grpc.CallOption) (*TestResetNewbieAnchorTaskResp, error)
	// 测试接口 完成任务
	TestCompleteNewbieAnchorTask(ctx context.Context, in *TestCompleteNewbieAnchorTaskReq, opts ...grpc.CallOption) (*TestCompleteNewbieAnchorTaskResp, error)
	// 测试接口 手动触发计划任务
	TestTriggerTimer(ctx context.Context, in *TestTriggerTimerReq, opts ...grpc.CallOption) (*TestTriggerTimerResp, error)
}

type anchorNewbieGuideServiceClient struct {
	cc *grpc.ClientConn
}

func NewAnchorNewbieGuideServiceClient(cc *grpc.ClientConn) AnchorNewbieGuideServiceClient {
	return &anchorNewbieGuideServiceClient{cc}
}

func (c *anchorNewbieGuideServiceClient) GetNewbieAnchorGuideInfo(ctx context.Context, in *GetNewbieAnchorGuideInfoReq, opts ...grpc.CallOption) (*GetNewbieAnchorGuideInfoResp, error) {
	out := new(GetNewbieAnchorGuideInfoResp)
	err := c.cc.Invoke(ctx, "/anchor_newbie_guide.AnchorNewbieGuideService/GetNewbieAnchorGuideInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorNewbieGuideServiceClient) GetNewbieGuidePopInfo(ctx context.Context, in *GetNewbieGuidePopInfoReq, opts ...grpc.CallOption) (*GetNewbieGuidePopInfoResp, error) {
	out := new(GetNewbieGuidePopInfoResp)
	err := c.cc.Invoke(ctx, "/anchor_newbie_guide.AnchorNewbieGuideService/GetNewbieGuidePopInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorNewbieGuideServiceClient) GetNewbieWidgetInfo(ctx context.Context, in *GetNewbieWidgetInfoReq, opts ...grpc.CallOption) (*GetNewbieWidgetInfoResp, error) {
	out := new(GetNewbieWidgetInfoResp)
	err := c.cc.Invoke(ctx, "/anchor_newbie_guide.AnchorNewbieGuideService/GetNewbieWidgetInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorNewbieGuideServiceClient) GetNewbieAnchorTask(ctx context.Context, in *GetNewbieAnchorTaskReq, opts ...grpc.CallOption) (*GetNewbieAnchorTaskResp, error) {
	out := new(GetNewbieAnchorTaskResp)
	err := c.cc.Invoke(ctx, "/anchor_newbie_guide.AnchorNewbieGuideService/GetNewbieAnchorTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorNewbieGuideServiceClient) ClaimNewbieAnchorTaskReward(ctx context.Context, in *ClaimNewbieAnchorTaskRewardReq, opts ...grpc.CallOption) (*ClaimNewbieAnchorTaskRewardResp, error) {
	out := new(ClaimNewbieAnchorTaskRewardResp)
	err := c.cc.Invoke(ctx, "/anchor_newbie_guide.AnchorNewbieGuideService/ClaimNewbieAnchorTaskReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorNewbieGuideServiceClient) ClaimNewbieAnchorTaskAdvancedReward(ctx context.Context, in *ClaimNewbieAnchorTaskAdvancedRewardReq, opts ...grpc.CallOption) (*ClaimNewbieAnchorTaskRewardResp, error) {
	out := new(ClaimNewbieAnchorTaskRewardResp)
	err := c.cc.Invoke(ctx, "/anchor_newbie_guide.AnchorNewbieGuideService/ClaimNewbieAnchorTaskAdvancedReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorNewbieGuideServiceClient) GetNewbieAnchorTaskRewardList(ctx context.Context, in *GetNewbieAnchorTaskRewardListReq, opts ...grpc.CallOption) (*GetNewbieAnchorTaskRewardListResp, error) {
	out := new(GetNewbieAnchorTaskRewardListResp)
	err := c.cc.Invoke(ctx, "/anchor_newbie_guide.AnchorNewbieGuideService/GetNewbieAnchorTaskRewardList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorNewbieGuideServiceClient) GetChannelLiveEndGuidePopup(ctx context.Context, in *GetChannelLiveEndGuidePopupRequest, opts ...grpc.CallOption) (*GetChannelLiveEndGuidePopupResponse, error) {
	out := new(GetChannelLiveEndGuidePopupResponse)
	err := c.cc.Invoke(ctx, "/anchor_newbie_guide.AnchorNewbieGuideService/GetChannelLiveEndGuidePopup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorNewbieGuideServiceClient) SendChannelLiveThanks(ctx context.Context, in *SendChannelLiveThanksRequest, opts ...grpc.CallOption) (*SendChannelLiveThanksResponse, error) {
	out := new(SendChannelLiveThanksResponse)
	err := c.cc.Invoke(ctx, "/anchor_newbie_guide.AnchorNewbieGuideService/SendChannelLiveThanks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorNewbieGuideServiceClient) TestResetNewbieAnchorTask(ctx context.Context, in *TestResetNewbieAnchorTaskReq, opts ...grpc.CallOption) (*TestResetNewbieAnchorTaskResp, error) {
	out := new(TestResetNewbieAnchorTaskResp)
	err := c.cc.Invoke(ctx, "/anchor_newbie_guide.AnchorNewbieGuideService/TestResetNewbieAnchorTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorNewbieGuideServiceClient) TestCompleteNewbieAnchorTask(ctx context.Context, in *TestCompleteNewbieAnchorTaskReq, opts ...grpc.CallOption) (*TestCompleteNewbieAnchorTaskResp, error) {
	out := new(TestCompleteNewbieAnchorTaskResp)
	err := c.cc.Invoke(ctx, "/anchor_newbie_guide.AnchorNewbieGuideService/TestCompleteNewbieAnchorTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorNewbieGuideServiceClient) TestTriggerTimer(ctx context.Context, in *TestTriggerTimerReq, opts ...grpc.CallOption) (*TestTriggerTimerResp, error) {
	out := new(TestTriggerTimerResp)
	err := c.cc.Invoke(ctx, "/anchor_newbie_guide.AnchorNewbieGuideService/TestTriggerTimer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AnchorNewbieGuideServiceServer is the server API for AnchorNewbieGuideService service.
type AnchorNewbieGuideServiceServer interface {
	// 获取新手指南信息
	GetNewbieAnchorGuideInfo(context.Context, *GetNewbieAnchorGuideInfoReq) (*GetNewbieAnchorGuideInfoResp, error)
	// 获取新手指南泡泡信息
	GetNewbieGuidePopInfo(context.Context, *GetNewbieGuidePopInfoReq) (*GetNewbieGuidePopInfoResp, error)
	// 获取新手引导挂件信息
	GetNewbieWidgetInfo(context.Context, *GetNewbieWidgetInfoReq) (*GetNewbieWidgetInfoResp, error)
	// 获取新手任务
	GetNewbieAnchorTask(context.Context, *GetNewbieAnchorTaskReq) (*GetNewbieAnchorTaskResp, error)
	// 领取新手任务奖励
	ClaimNewbieAnchorTaskReward(context.Context, *ClaimNewbieAnchorTaskRewardReq) (*ClaimNewbieAnchorTaskRewardResp, error)
	// 领取新手进阶任务奖励
	ClaimNewbieAnchorTaskAdvancedReward(context.Context, *ClaimNewbieAnchorTaskAdvancedRewardReq) (*ClaimNewbieAnchorTaskRewardResp, error)
	// 获取新手任务奖励列表
	GetNewbieAnchorTaskRewardList(context.Context, *GetNewbieAnchorTaskRewardListReq) (*GetNewbieAnchorTaskRewardListResp, error)
	// 获取直播间结束引导弹窗信息
	GetChannelLiveEndGuidePopup(context.Context, *GetChannelLiveEndGuidePopupRequest) (*GetChannelLiveEndGuidePopupResponse, error)
	// 发送直播间感谢互动
	SendChannelLiveThanks(context.Context, *SendChannelLiveThanksRequest) (*SendChannelLiveThanksResponse, error)
	// 测试接口 重置新手任务
	TestResetNewbieAnchorTask(context.Context, *TestResetNewbieAnchorTaskReq) (*TestResetNewbieAnchorTaskResp, error)
	// 测试接口 完成任务
	TestCompleteNewbieAnchorTask(context.Context, *TestCompleteNewbieAnchorTaskReq) (*TestCompleteNewbieAnchorTaskResp, error)
	// 测试接口 手动触发计划任务
	TestTriggerTimer(context.Context, *TestTriggerTimerReq) (*TestTriggerTimerResp, error)
}

func RegisterAnchorNewbieGuideServiceServer(s *grpc.Server, srv AnchorNewbieGuideServiceServer) {
	s.RegisterService(&_AnchorNewbieGuideService_serviceDesc, srv)
}

func _AnchorNewbieGuideService_GetNewbieAnchorGuideInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewbieAnchorGuideInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorNewbieGuideServiceServer).GetNewbieAnchorGuideInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_newbie_guide.AnchorNewbieGuideService/GetNewbieAnchorGuideInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorNewbieGuideServiceServer).GetNewbieAnchorGuideInfo(ctx, req.(*GetNewbieAnchorGuideInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorNewbieGuideService_GetNewbieGuidePopInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewbieGuidePopInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorNewbieGuideServiceServer).GetNewbieGuidePopInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_newbie_guide.AnchorNewbieGuideService/GetNewbieGuidePopInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorNewbieGuideServiceServer).GetNewbieGuidePopInfo(ctx, req.(*GetNewbieGuidePopInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorNewbieGuideService_GetNewbieWidgetInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewbieWidgetInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorNewbieGuideServiceServer).GetNewbieWidgetInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_newbie_guide.AnchorNewbieGuideService/GetNewbieWidgetInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorNewbieGuideServiceServer).GetNewbieWidgetInfo(ctx, req.(*GetNewbieWidgetInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorNewbieGuideService_GetNewbieAnchorTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewbieAnchorTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorNewbieGuideServiceServer).GetNewbieAnchorTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_newbie_guide.AnchorNewbieGuideService/GetNewbieAnchorTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorNewbieGuideServiceServer).GetNewbieAnchorTask(ctx, req.(*GetNewbieAnchorTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorNewbieGuideService_ClaimNewbieAnchorTaskReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClaimNewbieAnchorTaskRewardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorNewbieGuideServiceServer).ClaimNewbieAnchorTaskReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_newbie_guide.AnchorNewbieGuideService/ClaimNewbieAnchorTaskReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorNewbieGuideServiceServer).ClaimNewbieAnchorTaskReward(ctx, req.(*ClaimNewbieAnchorTaskRewardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorNewbieGuideService_ClaimNewbieAnchorTaskAdvancedReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClaimNewbieAnchorTaskAdvancedRewardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorNewbieGuideServiceServer).ClaimNewbieAnchorTaskAdvancedReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_newbie_guide.AnchorNewbieGuideService/ClaimNewbieAnchorTaskAdvancedReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorNewbieGuideServiceServer).ClaimNewbieAnchorTaskAdvancedReward(ctx, req.(*ClaimNewbieAnchorTaskAdvancedRewardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorNewbieGuideService_GetNewbieAnchorTaskRewardList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewbieAnchorTaskRewardListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorNewbieGuideServiceServer).GetNewbieAnchorTaskRewardList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_newbie_guide.AnchorNewbieGuideService/GetNewbieAnchorTaskRewardList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorNewbieGuideServiceServer).GetNewbieAnchorTaskRewardList(ctx, req.(*GetNewbieAnchorTaskRewardListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorNewbieGuideService_GetChannelLiveEndGuidePopup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelLiveEndGuidePopupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorNewbieGuideServiceServer).GetChannelLiveEndGuidePopup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_newbie_guide.AnchorNewbieGuideService/GetChannelLiveEndGuidePopup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorNewbieGuideServiceServer).GetChannelLiveEndGuidePopup(ctx, req.(*GetChannelLiveEndGuidePopupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorNewbieGuideService_SendChannelLiveThanks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendChannelLiveThanksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorNewbieGuideServiceServer).SendChannelLiveThanks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_newbie_guide.AnchorNewbieGuideService/SendChannelLiveThanks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorNewbieGuideServiceServer).SendChannelLiveThanks(ctx, req.(*SendChannelLiveThanksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorNewbieGuideService_TestResetNewbieAnchorTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestResetNewbieAnchorTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorNewbieGuideServiceServer).TestResetNewbieAnchorTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_newbie_guide.AnchorNewbieGuideService/TestResetNewbieAnchorTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorNewbieGuideServiceServer).TestResetNewbieAnchorTask(ctx, req.(*TestResetNewbieAnchorTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorNewbieGuideService_TestCompleteNewbieAnchorTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestCompleteNewbieAnchorTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorNewbieGuideServiceServer).TestCompleteNewbieAnchorTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_newbie_guide.AnchorNewbieGuideService/TestCompleteNewbieAnchorTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorNewbieGuideServiceServer).TestCompleteNewbieAnchorTask(ctx, req.(*TestCompleteNewbieAnchorTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorNewbieGuideService_TestTriggerTimer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestTriggerTimerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorNewbieGuideServiceServer).TestTriggerTimer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_newbie_guide.AnchorNewbieGuideService/TestTriggerTimer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorNewbieGuideServiceServer).TestTriggerTimer(ctx, req.(*TestTriggerTimerReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AnchorNewbieGuideService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "anchor_newbie_guide.AnchorNewbieGuideService",
	HandlerType: (*AnchorNewbieGuideServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNewbieAnchorGuideInfo",
			Handler:    _AnchorNewbieGuideService_GetNewbieAnchorGuideInfo_Handler,
		},
		{
			MethodName: "GetNewbieGuidePopInfo",
			Handler:    _AnchorNewbieGuideService_GetNewbieGuidePopInfo_Handler,
		},
		{
			MethodName: "GetNewbieWidgetInfo",
			Handler:    _AnchorNewbieGuideService_GetNewbieWidgetInfo_Handler,
		},
		{
			MethodName: "GetNewbieAnchorTask",
			Handler:    _AnchorNewbieGuideService_GetNewbieAnchorTask_Handler,
		},
		{
			MethodName: "ClaimNewbieAnchorTaskReward",
			Handler:    _AnchorNewbieGuideService_ClaimNewbieAnchorTaskReward_Handler,
		},
		{
			MethodName: "ClaimNewbieAnchorTaskAdvancedReward",
			Handler:    _AnchorNewbieGuideService_ClaimNewbieAnchorTaskAdvancedReward_Handler,
		},
		{
			MethodName: "GetNewbieAnchorTaskRewardList",
			Handler:    _AnchorNewbieGuideService_GetNewbieAnchorTaskRewardList_Handler,
		},
		{
			MethodName: "GetChannelLiveEndGuidePopup",
			Handler:    _AnchorNewbieGuideService_GetChannelLiveEndGuidePopup_Handler,
		},
		{
			MethodName: "SendChannelLiveThanks",
			Handler:    _AnchorNewbieGuideService_SendChannelLiveThanks_Handler,
		},
		{
			MethodName: "TestResetNewbieAnchorTask",
			Handler:    _AnchorNewbieGuideService_TestResetNewbieAnchorTask_Handler,
		},
		{
			MethodName: "TestCompleteNewbieAnchorTask",
			Handler:    _AnchorNewbieGuideService_TestCompleteNewbieAnchorTask_Handler,
		},
		{
			MethodName: "TestTriggerTimer",
			Handler:    _AnchorNewbieGuideService_TestTriggerTimer_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/anchor-newbie-guide/anchor-newbie-guide.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/anchor-newbie-guide/anchor-newbie-guide.proto", fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba)
}

var fileDescriptor_anchor_newbie_guide_2d3d56beaf62c3ba = []byte{
	// 2722 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x5a, 0x4b, 0x6f, 0xe3, 0xd6,
	0xf5, 0x1f, 0x4a, 0x1e, 0x5b, 0x3a, 0x7e, 0x84, 0xff, 0x6b, 0xcf, 0x8c, 0x46, 0xe3, 0x89, 0x3d,
	0x74, 0x1e, 0x8e, 0x93, 0xf1, 0xfc, 0xeb, 0x64, 0x26, 0x69, 0x9b, 0xa2, 0xd0, 0x48, 0x1c, 0x0f,
	0x13, 0x99, 0x32, 0x28, 0x39, 0xce, 0xa3, 0x00, 0x4b, 0x8b, 0x37, 0x32, 0x6b, 0x89, 0x64, 0x78,
	0x29, 0x7b, 0x8c, 0x6e, 0x1a, 0x20, 0x40, 0xd1, 0x16, 0x45, 0x8a, 0x2e, 0xbb, 0x6a, 0x81, 0x00,
	0x5d, 0x15, 0x5d, 0x17, 0xe8, 0xa6, 0xd9, 0x74, 0x5b, 0x74, 0xd3, 0xaf, 0xd0, 0x7e, 0x80, 0xee,
	0x8b, 0xfb, 0xa0, 0x48, 0x49, 0xa4, 0x24, 0xcf, 0xec, 0x74, 0xcf, 0x3d, 0xf7, 0xdc, 0x73, 0x7e,
	0xe7, 0x71, 0xef, 0xb9, 0x14, 0xbc, 0x1f, 0x86, 0x0f, 0xbe, 0xe8, 0x3b, 0xed, 0x33, 0xe2, 0x74,
	0xcf, 0x71, 0xf0, 0xc0, 0x72, 0xdb, 0xa7, 0x5e, 0x70, 0xdf, 0xc5, 0x17, 0x27, 0x0e, 0xbe, 0xdf,
	0xe9, 0x3b, 0x36, 0x4e, 0xa3, 0xed, 0xfa, 0x81, 0x17, 0x7a, 0x68, 0x95, 0x4f, 0x99, 0x7c, 0xca,
	0x64, 0x53, 0x0a, 0x81, 0x3b, 0xfb, 0x38, 0xd4, 0x19, 0xa9, 0xc2, 0xe6, 0xf7, 0x29, 0x5d, 0x73,
	0x3f, 0xf7, 0x0c, 0xfc, 0x05, 0xda, 0x82, 0x65, 0xc7, 0xc6, 0x6e, 0xe8, 0x84, 0x97, 0x66, 0x78,
	0xe9, 0xe3, 0x92, 0xb4, 0x29, 0x6d, 0x2f, 0x1b, 0x4b, 0x11, 0xb1, 0x75, 0xe9, 0x63, 0x24, 0x43,
	0xbe, 0xef, 0xd8, 0xa5, 0x1c, 0x9b, 0xa2, 0x3f, 0xd1, 0x5d, 0x00, 0x87, 0x98, 0x96, 0x7d, 0x6e,
	0xb9, 0x6d, 0x5c, 0xca, 0x6f, 0x4a, 0xdb, 0x05, 0xa3, 0xe8, 0x90, 0x0a, 0x27, 0x28, 0x36, 0xac,
	0x67, 0x6f, 0x4a, 0x7c, 0x54, 0x03, 0x08, 0x4f, 0x71, 0x0f, 0x9b, 0x5d, 0x87, 0x84, 0x25, 0x69,
	0x33, 0xbf, 0xbd, 0xb8, 0xf7, 0xea, 0x6e, 0x8a, 0xfa, 0xbb, 0x5c, 0x06, 0x5b, 0xdd, 0xa2, 0x2b,
	0x8c, 0x22, 0x5b, 0x58, 0x77, 0x48, 0xa8, 0x3c, 0x83, 0xe5, 0x0f, 0x5d, 0xef, 0xa2, 0x8b, 0xed,
	0x0e, 0x6e, 0x59, 0xe4, 0x0c, 0xdd, 0x82, 0x85, 0xd0, 0x22, 0x67, 0xa6, 0x63, 0x33, 0x33, 0x8a,
	0xc6, 0x3c, 0x1d, 0x6a, 0x36, 0xba, 0x03, 0x45, 0x36, 0xe1, 0x5a, 0x3d, 0xcc, 0xcc, 0x28, 0x1a,
	0x05, 0x4a, 0xd0, 0xad, 0x1e, 0x46, 0xb7, 0xa1, 0xf0, 0x93, 0x7e, 0xcf, 0x37, 0xfb, 0x41, 0x97,
	0x59, 0x52, 0x34, 0x16, 0xe8, 0xf8, 0x28, 0xe8, 0xa2, 0x9b, 0x30, 0x4f, 0x42, 0x2b, 0xec, 0x93,
	0xd2, 0x1c, 0xb3, 0x5d, 0x8c, 0x94, 0x8f, 0x01, 0x25, 0x14, 0xab, 0x7a, 0x6e, 0x88, 0xdd, 0x10,
	0xad, 0xc1, 0xf5, 0xd0, 0x09, 0xbb, 0x58, 0x6c, 0xce, 0x07, 0xa8, 0x04, 0x0b, 0x6d, 0xce, 0x20,
	0x76, 0x8e, 0x86, 0x08, 0xc1, 0x5c, 0x3f, 0xe8, 0x92, 0x52, 0x7e, 0x33, 0xbf, 0x5d, 0x34, 0xd8,
	0x6f, 0xe5, 0x6f, 0x12, 0xac, 0x26, 0x44, 0x37, 0xfb, 0x27, 0xcc, 0x6c, 0x0a, 0x38, 0x47, 0x8c,
	0x99, 0xc0, 0x37, 0xe0, 0x50, 0x30, 0x1b, 0x3e, 0x80, 0x25, 0x21, 0x95, 0x43, 0x9a, 0x63, 0x90,
	0xbe, 0x3e, 0x0d, 0x52, 0xa1, 0xb9, 0xb1, 0x28, 0x16, 0x53, 0x58, 0xd1, 0x0f, 0x05, 0x58, 0x4c,
	0x50, 0x9e, 0x09, 0x52, 0x52, 0x05, 0x0d, 0x81, 0xcf, 0x01, 0x65, 0x7e, 0xf9, 0x93, 0x04, 0xf2,
	0xa8, 0xdf, 0xd0, 0x2b, 0xb0, 0x12, 0x1b, 0xc0, 0xb0, 0xe6, 0x46, 0x2c, 0x0d, 0x8c, 0xa0, 0x80,
	0xeb, 0xb0, 0x42, 0xfa, 0x27, 0x66, 0x22, 0x38, 0xb8, 0x25, 0xdb, 0xd3, 0x2c, 0x89, 0x80, 0x32,
	0x96, 0x88, 0xf8, 0xc5, 0x6c, 0x19, 0x0b, 0xef, 0xfc, 0x78, 0x78, 0x2b, 0x3f, 0xcf, 0x41, 0x69,
	0x10, 0xae, 0x4c, 0xda, 0xa1, 0xe7, 0x47, 0x09, 0x22, 0x62, 0x5f, 0x8a, 0x63, 0xff, 0x36, 0x14,
	0x88, 0xd3, 0x71, 0x4d, 0xdb, 0xba, 0x14, 0x29, 0xb1, 0x40, 0xc7, 0x35, 0xeb, 0x12, 0x5d, 0xc0,
	0xda, 0x60, 0x3b, 0xef, 0x24, 0xb4, 0x1c, 0xc6, 0x45, 0xa3, 0x87, 0x1a, 0xa1, 0xa6, 0x1a, 0x91,
	0xb5, 0xf3, 0xae, 0x26, 0x24, 0x35, 0x98, 0xa0, 0x9a, 0x75, 0x49, 0x54, 0x37, 0x0c, 0x2e, 0x0d,
	0xe4, 0x8c, 0x4d, 0x94, 0x55, 0xb8, 0x95, 0xc1, 0x4e, 0x0d, 0x38, 0xc3, 0x97, 0x91, 0x01, 0x67,
	0xf8, 0x92, 0xc6, 0xe9, 0xb9, 0xd5, 0xed, 0x63, 0xa1, 0x3d, 0x1f, 0x7c, 0x2f, 0xf7, 0x9e, 0xa4,
	0x34, 0xe0, 0x76, 0x86, 0x3a, 0xc4, 0xa7, 0xd9, 0xe5, 0x7b, 0xbe, 0x19, 0x3e, 0x0b, 0xa3, 0xec,
	0xf2, 0x3d, 0xbf, 0xf5, 0x2c, 0xa4, 0x80, 0xb0, 0x09, 0x8a, 0xaf, 0x00, 0x84, 0xce, 0x50, 0x68,
	0xbf, 0x96, 0xe0, 0xe6, 0x40, 0xe2, 0xb1, 0x63, 0x77, 0x70, 0x98, 0x0d, 0xec, 0x98, 0xb3, 0x72,
	0x29, 0xb5, 0x68, 0x03, 0x16, 0x2f, 0x98, 0x9c, 0xa4, 0x3f, 0x81, 0x93, 0x18, 0xc3, 0x5d, 0x80,
	0x18, 0x7a, 0x91, 0xb7, 0x45, 0x2f, 0x02, 0x45, 0xf9, 0xbd, 0x04, 0xb7, 0x52, 0x35, 0x22, 0x7e,
	0x9c, 0xc0, 0xf9, 0x64, 0x02, 0x6f, 0xc1, 0x32, 0xcb, 0x07, 0x3f, 0xf0, 0x3a, 0x01, 0x26, 0xbc,
	0x16, 0xd0, 0xc0, 0xb5, 0xc8, 0xd9, 0xa1, 0xa0, 0x25, 0x2a, 0xc5, 0xf5, 0x64, 0xa5, 0x48, 0x82,
	0x36, 0x9f, 0x09, 0xda, 0xc2, 0x30, 0x68, 0xdf, 0x4a, 0xb0, 0x96, 0xac, 0x9d, 0x34, 0xbd, 0xb4,
	0x10, 0xf7, 0xb2, 0xeb, 0xdb, 0x06, 0x2c, 0xb2, 0x09, 0x12, 0x06, 0x8e, 0xdb, 0x11, 0x75, 0x06,
	0x28, 0xa9, 0xc9, 0x28, 0x83, 0x02, 0xe8, 0xb4, 0x3d, 0x57, 0x58, 0xc7, 0xf2, 0x55, 0x6b, 0x7b,
	0x2e, 0xba, 0x07, 0x4b, 0x0e, 0x31, 0xdb, 0x5e, 0xcf, 0xef, 0xe2, 0x10, 0xdb, 0xcc, 0xbe, 0x82,
	0xb1, 0xe8, 0x90, 0x6a, 0x44, 0x42, 0x65, 0x28, 0x0c, 0xcc, 0xe7, 0x06, 0x0e, 0xc6, 0x0c, 0x35,
	0x2f, 0xb4, 0xba, 0xcc, 0xc0, 0x65, 0x83, 0x0f, 0x94, 0x46, 0xc2, 0xf1, 0xb1, 0x19, 0xcf, 0x7f,
	0xe4, 0x28, 0x7f, 0xce, 0x45, 0x95, 0x31, 0x16, 0x47, 0x73, 0x4e, 0x86, 0x3c, 0x75, 0x34, 0x15,
	0x32, 0x67, 0xd0, 0x9f, 0xe8, 0x63, 0x78, 0x29, 0x32, 0xc6, 0x14, 0x4e, 0xa1, 0x72, 0x56, 0xf6,
	0x1e, 0x4c, 0xa8, 0x22, 0x43, 0x42, 0x77, 0x9b, 0x6c, 0x99, 0xb1, 0x12, 0xc9, 0xe1, 0x63, 0xf4,
	0x64, 0xbc, 0x34, 0xbe, 0x31, 0x93, 0x4c, 0xea, 0xbe, 0x44, 0x85, 0xb4, 0x61, 0x5e, 0x48, 0xbc,
	0x09, 0xa8, 0xd9, 0xaa, 0xb4, 0x8e, 0x9a, 0xe6, 0x91, 0xde, 0x3c, 0x54, 0xab, 0xda, 0x13, 0x4d,
	0xad, 0xc9, 0xd7, 0x50, 0x09, 0xd6, 0x04, 0x5d, 0x6f, 0xb4, 0xcc, 0x6a, 0xe3, 0xe0, 0xb0, 0xae,
	0xb6, 0xd4, 0x9a, 0x2c, 0xa1, 0x35, 0x90, 0xc5, 0x4c, 0x4c, 0xcd, 0x21, 0x04, 0x2b, 0x82, 0xaa,
	0x7e, 0x7c, 0xa8, 0x19, 0x6a, 0x4d, 0xce, 0x2b, 0xdf, 0xe4, 0xa1, 0x34, 0xaa, 0xc8, 0x63, 0xdc,
	0x71, 0x5c, 0x17, 0x07, 0xe8, 0x7d, 0x98, 0x63, 0xa5, 0x49, 0x9a, 0x5a, 0x5f, 0x87, 0x90, 0x31,
	0xd8, 0x2a, 0xf4, 0x2a, 0x0c, 0xa0, 0xb1, 0x79, 0x89, 0xe3, 0x9e, 0x5a, 0x1e, 0x50, 0x69, 0x01,
	0x42, 0x3a, 0x2c, 0x04, 0xf8, 0xc2, 0x0a, 0x6c, 0x52, 0x9a, 0x67, 0xfb, 0xbc, 0x33, 0xd3, 0x3e,
	0x91, 0x92, 0xbb, 0x06, 0x5b, 0x6c, 0x44, 0x42, 0xca, 0xff, 0x94, 0x60, 0x9e, 0xd3, 0xb8, 0x06,
	0xae, 0xed, 0x84, 0x8e, 0x27, 0x8a, 0xac, 0x14, 0x69, 0x20, 0xa8, 0x4c, 0x83, 0xbb, 0x00, 0xed,
	0x53, 0x4c, 0xc2, 0x64, 0xe4, 0x17, 0x19, 0x85, 0x85, 0x3e, 0x3d, 0x37, 0xbb, 0x96, 0xd3, 0x33,
	0x13, 0xc7, 0xfc, 0xca, 0xc4, 0x73, 0x93, 0x6f, 0x2f, 0xe2, 0x63, 0x91, 0x2d, 0x16, 0xae, 0xbc,
	0x03, 0xc5, 0x1e, 0x2d, 0x3b, 0x21, 0x7e, 0x16, 0xb2, 0x94, 0x2e, 0x1a, 0x05, 0x4a, 0x68, 0x61,
	0x9e, 0xee, 0xec, 0x60, 0xa3, 0x73, 0x05, 0x7e, 0x0d, 0xa0, 0x07, 0x15, 0x7e, 0x16, 0x2a, 0x7f,
	0x4f, 0x71, 0x93, 0xb8, 0x48, 0xd9, 0xb4, 0xae, 0xf8, 0x38, 0x70, 0xbc, 0xa8, 0x50, 0x8a, 0x11,
	0xda, 0x81, 0xff, 0xe3, 0xbf, 0xa8, 0xe6, 0x41, 0x68, 0x86, 0x8e, 0xb8, 0xd9, 0xcc, 0x19, 0x2f,
	0xf1, 0x89, 0x26, 0xa5, 0xb7, 0x9c, 0x1e, 0x46, 0xaf, 0x81, 0x20, 0x99, 0xd8, 0xb5, 0x39, 0x67,
	0x9e, 0x71, 0x2e, 0x73, 0xb2, 0xea, 0xda, 0x8c, 0x6f, 0x28, 0xba, 0xe7, 0x9e, 0x3b, 0xba, 0xc7,
	0x40, 0xbd, 0xfe, 0x02, 0xa0, 0x7e, 0x06, 0x4b, 0xdc, 0xf9, 0xa6, 0x13, 0xe2, 0x5e, 0x14, 0x46,
	0xef, 0xcd, 0xa4, 0x56, 0x04, 0xa2, 0x08, 0x23, 0xa6, 0xe5, 0x62, 0x30, 0xf8, 0x4d, 0xca, 0x3a,
	0x40, 0x3c, 0x45, 0x8b, 0xa8, 0xd8, 0x2a, 0x71, 0xc7, 0x02, 0x4e, 0x62, 0x97, 0xac, 0x98, 0x81,
	0x05, 0x53, 0x2e, 0xc9, 0x40, 0xa3, 0x49, 0xf9, 0x47, 0x2e, 0x71, 0xb6, 0x24, 0x8b, 0x1e, 0xf1,
	0x91, 0x01, 0xcb, 0x27, 0x22, 0xac, 0x4d, 0x8a, 0x14, 0x93, 0xbf, 0xb8, 0x77, 0xff, 0x4a, 0x09,
	0x61, 0x2c, 0x45, 0x32, 0xd8, 0x7d, 0xf7, 0x33, 0x40, 0xe2, 0x0a, 0x6e, 0x9b, 0xb1, 0xe7, 0xf8,
	0x8d, 0xe9, 0xfe, 0x95, 0x20, 0x32, 0xe4, 0x48, 0x50, 0x2b, 0xf2, 0xe2, 0xa7, 0x70, 0xcb, 0xea,
	0x87, 0x9e, 0xc9, 0x5d, 0x39, 0xe4, 0x04, 0x5e, 0xf9, 0xb6, 0x52, 0x77, 0xe0, 0x06, 0x0f, 0xf0,
	0x5e, 0xa3, 0x32, 0xaa, 0x54, 0x44, 0x4c, 0x24, 0x68, 0x1b, 0x64, 0x87, 0x98, 0x17, 0x96, 0x13,
	0x3a, 0x6e, 0xc7, 0x64, 0x36, 0xb1, 0x28, 0x29, 0x18, 0x2b, 0x0e, 0x39, 0xe6, 0x64, 0x66, 0xb3,
	0x72, 0x0c, 0x2f, 0xb3, 0xd5, 0xe3, 0x98, 0xb2, 0xca, 0xf0, 0xfc, 0xc7, 0xc9, 0x05, 0xbc, 0x96,
	0x2a, 0x78, 0x80, 0xc8, 0xd5, 0x36, 0x88, 0xf3, 0x34, 0x37, 0x94, 0xa7, 0x62, 0xe3, 0x7c, 0xbc,
	0xf1, 0x8f, 0x61, 0x63, 0xa2, 0x45, 0xc4, 0x47, 0x3f, 0x88, 0xcb, 0x66, 0x6e, 0x76, 0xa8, 0xa3,
	0x35, 0xca, 0x29, 0xac, 0x0c, 0x4f, 0xbd, 0x78, 0x68, 0xd3, 0x43, 0xbe, 0xed, 0xf5, 0xdd, 0x50,
	0x58, 0xc2, 0x07, 0xca, 0x5f, 0x24, 0x58, 0x8d, 0xb7, 0xaa, 0x46, 0x95, 0x77, 0xa4, 0x38, 0x63,
	0xd2, 0x16, 0x5b, 0x26, 0x8a, 0x33, 0x26, 0xed, 0x17, 0xb4, 0x53, 0x34, 0xa1, 0x2c, 0x3e, 0xb1,
	0x1d, 0x37, 0xa1, 0x55, 0x4e, 0x18, 0x29, 0xfd, 0x73, 0x23, 0xa5, 0x5f, 0xf9, 0x83, 0x04, 0x9b,
	0xa9, 0xc9, 0x4a, 0x85, 0xd3, 0x0c, 0x98, 0xd9, 0xf7, 0x51, 0xdd, 0x1c, 0xdc, 0x59, 0x57, 0x66,
	0xac, 0x9b, 0x74, 0x35, 0xaf, 0x9b, 0xc9, 0x20, 0x9d, 0x8b, 0x63, 0xe5, 0x4b, 0x09, 0xee, 0x4d,
	0xd1, 0x91, 0xf8, 0xe8, 0x47, 0x70, 0x43, 0x38, 0x2f, 0x06, 0x3d, 0xd1, 0x58, 0x6f, 0x4f, 0x01,
	0x75, 0xe0, 0x36, 0x63, 0x35, 0x18, 0x26, 0xb0, 0xbb, 0xca, 0x07, 0xb0, 0xde, 0xc2, 0x6c, 0xa7,
	0xf4, 0xeb, 0x1c, 0xed, 0x6f, 0x71, 0x40, 0xaf, 0xd7, 0x0c, 0x9c, 0x82, 0x11, 0x0d, 0x53, 0x92,
	0x6e, 0x03, 0xee, 0x4e, 0x90, 0x45, 0x7c, 0xe5, 0x8f, 0x12, 0x6c, 0x50, 0x8e, 0xe8, 0xe6, 0xf9,
	0x82, 0x1b, 0x46, 0x97, 0x43, 0x91, 0x7e, 0xf4, 0x72, 0x18, 0x39, 0x8b, 0xb6, 0x6c, 0xec, 0x90,
	0x9b, 0xd5, 0x59, 0x4d, 0xa7, 0xe3, 0x72, 0x67, 0xd1, 0x5f, 0x8a, 0x02, 0x9b, 0x93, 0x15, 0x25,
	0xbe, 0x52, 0x87, 0x55, 0xca, 0xd3, 0x0a, 0x9c, 0x4e, 0x07, 0x07, 0xf4, 0x8c, 0x0d, 0x26, 0x1b,
	0x30, 0xe9, 0x9d, 0x42, 0xb9, 0x09, 0x6b, 0xe3, 0xd2, 0x88, 0xaf, 0x3c, 0x02, 0x65, 0x1f, 0x87,
	0xd5, 0x53, 0xcb, 0x75, 0x71, 0xb7, 0xee, 0x9c, 0x63, 0xd5, 0xb5, 0xa3, 0xe6, 0xad, 0xef, 0x1b,
	0xf8, 0x8b, 0x3e, 0x26, 0xe1, 0x78, 0xbb, 0xa5, 0xfc, 0x5b, 0x82, 0xad, 0x89, 0x0b, 0x89, 0xef,
	0xb9, 0x84, 0x35, 0x54, 0x3e, 0x25, 0x24, 0x13, 0xa0, 0xc8, 0x28, 0x2c, 0x6a, 0x75, 0x58, 0xe9,
	0x13, 0x1c, 0x98, 0x8e, 0xfb, 0xb9, 0x37, 0xbd, 0x65, 0xd7, 0xdc, 0x10, 0x07, 0x56, 0x9b, 0x46,
	0xd7, 0x11, 0xc1, 0x01, 0x6b, 0xbd, 0x96, 0xfa, 0xe2, 0x17, 0x3b, 0x77, 0x0c, 0x90, 0xc5, 0xc2,
	0x58, 0x62, 0x7e, 0x82, 0x44, 0x03, 0xb7, 0xbd, 0x5e, 0x0f, 0xbb, 0x36, 0x07, 0x9f, 0x49, 0x5c,
	0xb1, 0x06, 0xbf, 0x59, 0x0c, 0xff, 0x57, 0x82, 0xd5, 0x94, 0x9d, 0x53, 0x7a, 0xd0, 0x12, 0x2c,
	0x58, 0x6d, 0x5e, 0xe9, 0xc4, 0x6b, 0x8d, 0x18, 0xd2, 0x16, 0xc8, 0x75, 0xda, 0x67, 0xcc, 0x35,
	0xa2, 0x83, 0x8a, 0xc6, 0xb4, 0xde, 0xb9, 0xde, 0x89, 0xd3, 0xa5, 0x65, 0xa2, 0x8b, 0xcf, 0x71,
	0x97, 0x25, 0x71, 0xce, 0x58, 0x8e, 0xa8, 0x75, 0x4a, 0xa4, 0x97, 0xc0, 0xd0, 0xea, 0x70, 0x93,
	0xae, 0xb3, 0x47, 0x9f, 0x85, 0xd0, 0xea, 0x30, 0xab, 0x1f, 0xc1, 0x2d, 0x27, 0x56, 0xd0, 0x3c,
	0xe9, 0x87, 0xa1, 0x27, 0xae, 0x92, 0xbc, 0x6f, 0xbc, 0x91, 0x98, 0x7e, 0xcc, 0x66, 0xd9, 0xbd,
	0x72, 0x9d, 0xd5, 0x40, 0xe6, 0x00, 0xaf, 0xcb, 0x6e, 0x9d, 0x05, 0xa3, 0xe0, 0x10, 0x6a, 0x61,
	0xa3, 0xab, 0x5c, 0x0c, 0x37, 0x92, 0x55, 0x1c, 0xb0, 0x66, 0x97, 0xc6, 0x19, 0x3d, 0xc9, 0x93,
	0xa7, 0x41, 0x81, 0x12, 0xa2, 0xb3, 0xe0, 0xc4, 0x22, 0xd8, 0x74, 0x7a, 0x9d, 0x7e, 0xd0, 0x8d,
	0xce, 0x02, 0x4a, 0xd2, 0x18, 0x85, 0xf6, 0x8b, 0xe4, 0xd4, 0xb2, 0xbd, 0x0b, 0xb3, 0xed, 0x75,
	0xbd, 0x40, 0xa0, 0xb1, 0xc8, 0x69, 0x55, 0x4a, 0x52, 0xfe, 0x23, 0xc1, 0x6a, 0x8a, 0x63, 0x52,
	0x00, 0x67, 0x55, 0x9a, 0x45, 0xa0, 0x39, 0x48, 0xdd, 0xa2, 0xa0, 0x68, 0x43, 0xfe, 0xc8, 0x67,
	0xfb, 0x63, 0x6e, 0xc4, 0x1f, 0x07, 0xb0, 0x4c, 0x53, 0xca, 0xec, 0x07, 0xdd, 0x18, 0xed, 0x59,
	0x6e, 0xb3, 0x11, 0x42, 0xc6, 0x22, 0x5d, 0x7f, 0x14, 0x74, 0x99, 0x73, 0x86, 0x41, 0x9e, 0x1f,
	0x01, 0xb9, 0x05, 0xeb, 0x4d, 0xec, 0xda, 0x89, 0x3c, 0x6a, 0x9d, 0x5a, 0xee, 0x19, 0x89, 0x32,
	0xef, 0x36, 0x14, 0x3e, 0x0f, 0xbc, 0x9e, 0x19, 0x1b, 0xbe, 0x40, 0xc7, 0x47, 0xfc, 0x29, 0xa9,
	0xef, 0xd8, 0x71, 0xd6, 0x2c, 0x1b, 0x0b, 0x7d, 0x87, 0x15, 0x76, 0x5a, 0x2a, 0x33, 0xa4, 0xf2,
	0xb4, 0xdc, 0xf9, 0x29, 0xac, 0x0e, 0x3d, 0xc0, 0x89, 0x0b, 0xf3, 0x06, 0xdc, 0x49, 0x21, 0x9b,
	0x9a, 0x7b, 0x6e, 0x75, 0x1d, 0x5b, 0xbe, 0x86, 0xee, 0xc1, 0xdd, 0x34, 0x06, 0xdd, 0x0b, 0x9f,
	0x38, 0xae, 0x43, 0x4e, 0x65, 0x09, 0x6d, 0xc2, 0x7a, 0x1a, 0x0b, 0x9f, 0xc7, 0xb6, 0x9c, 0xdb,
	0xf9, 0x4a, 0x02, 0x38, 0x8e, 0xdf, 0x5c, 0xb6, 0x60, 0xe3, 0x58, 0xab, 0xed, 0xab, 0x2d, 0xb3,
	0xf5, 0xc9, 0xa1, 0x6a, 0x6a, 0xfa, 0x47, 0x95, 0xba, 0x56, 0x1b, 0x69, 0x69, 0xb7, 0xe1, 0x95,
	0x24, 0x93, 0xae, 0x1e, 0x3f, 0xd6, 0x54, 0xb3, 0xa2, 0x57, 0x9f, 0x36, 0x0c, 0xb3, 0xae, 0x7d,
	0xa4, 0x9a, 0x86, 0x5a, 0xa9, 0x7d, 0x22, 0x4b, 0xe8, 0x75, 0xd8, 0x9a, 0xc6, 0xd9, 0x68, 0x1c,
	0xc8, 0xb9, 0x9d, 0x5f, 0x48, 0xb0, 0xc4, 0xd5, 0x10, 0xd6, 0xbf, 0x0a, 0xf7, 0xc4, 0x4a, 0xd1,
	0x0d, 0xa7, 0xab, 0x52, 0x82, 0xb5, 0x61, 0x36, 0xbd, 0x61, 0x1c, 0x54, 0xea, 0xb2, 0x84, 0xca,
	0x70, 0x73, 0x78, 0xe6, 0x69, 0xa5, 0x69, 0xb6, 0x2a, 0xcd, 0x0f, 0xe5, 0x1c, 0x5a, 0x87, 0xd2,
	0xf8, 0x9c, 0xa1, 0x1e, 0x57, 0x8c, 0x9a, 0x9c, 0xdf, 0xf9, 0x55, 0xca, 0xab, 0x0d, 0x03, 0xe7,
	0x75, 0xd8, 0x1a, 0xb6, 0x80, 0x8a, 0xe3, 0x96, 0x0d, 0x6b, 0xf5, 0x0a, 0x6c, 0x66, 0x31, 0x3e,
	0x56, 0xf7, 0x35, 0x5d, 0x57, 0x0d, 0x59, 0x9a, 0xc4, 0x55, 0xa9, 0x7d, 0x54, 0xd1, 0xab, 0x6a,
	0x4d, 0xce, 0xed, 0x7c, 0x23, 0x45, 0x4f, 0xd4, 0xc9, 0xde, 0x2a, 0xb1, 0x98, 0xeb, 0x6d, 0xa6,
	0x3e, 0x3e, 0x64, 0x71, 0xb1, 0xa7, 0x88, 0x7a, 0x45, 0x3b, 0x60, 0x0f, 0x11, 0x9b, 0xb0, 0x9e,
	0xca, 0x15, 0x71, 0xe4, 0x32, 0x39, 0x06, 0x4f, 0x14, 0x3b, 0x5f, 0xe7, 0xc7, 0x41, 0xa3, 0xc7,
	0x6b, 0x06, 0x68, 0x4d, 0x6d, 0x5f, 0x1f, 0xd1, 0x55, 0x81, 0x97, 0xb3, 0x18, 0x1b, 0x7a, 0x5d,
	0xd3, 0xd5, 0x4c, 0xc8, 0x18, 0xcf, 0xd3, 0x46, 0xbd, 0x66, 0x1e, 0x68, 0x55, 0x39, 0x87, 0x5e,
	0x03, 0x25, 0x8b, 0x8b, 0xc7, 0x50, 0xad, 0xf2, 0x49, 0x53, 0xce, 0xd3, 0x18, 0xcb, 0xe2, 0x63,
	0xb1, 0xd9, 0xd2, 0x0e, 0x54, 0x79, 0x6e, 0xd2, 0xa6, 0xba, 0x7a, 0x6c, 0x3e, 0xa9, 0xe8, 0x4d,
	0xf9, 0x3a, 0x7a, 0x19, 0xca, 0x59, 0x5c, 0x87, 0x1f, 0xca, 0xf3, 0x34, 0x5b, 0x33, 0x55, 0x57,
	0xeb, 0xf5, 0x86, 0xbc, 0x40, 0x93, 0x2f, 0x53, 0x84, 0xa1, 0x36, 0x55, 0xbd, 0x25, 0x17, 0x26,
	0xe1, 0x59, 0xa9, 0xb6, 0xa8, 0xda, 0xcc, 0xba, 0xe2, 0xce, 0x5f, 0xa5, 0xf1, 0xd7, 0x08, 0xcd,
	0x75, 0x78, 0x9e, 0xbf, 0x05, 0xdb, 0x29, 0x52, 0x34, 0x5d, 0x6b, 0xa5, 0xc5, 0x73, 0x3a, 0x50,
	0x31, 0x77, 0xe5, 0xa8, 0xd5, 0x90, 0xa5, 0xa9, 0x6c, 0x14, 0x54, 0x39, 0x97, 0x81, 0x67, 0xcc,
	0x46, 0x9d, 0x98, 0xdf, 0xf9, 0x56, 0x82, 0xf5, 0xac, 0x1b, 0x0d, 0x33, 0xe1, 0xbb, 0xf0, 0xb0,
	0xfa, 0xb4, 0xa2, 0xeb, 0x6a, 0x9d, 0x7b, 0x4b, 0xd5, 0x6b, 0xe6, 0xfe, 0x91, 0x56, 0x53, 0xcd,
	0xc3, 0xc6, 0xe1, 0xd1, 0xe1, 0xa4, 0x02, 0xf6, 0x00, 0xde, 0x9c, 0xbe, 0xf4, 0xa8, 0xa9, 0x1a,
	0xa6, 0xa6, 0x3f, 0xa1, 0x96, 0x3d, 0x82, 0xbd, 0xe9, 0x0b, 0x84, 0x35, 0x86, 0x5a, 0x6d, 0x1c,
	0x1c, 0xa8, 0x7a, 0x4d, 0xce, 0xed, 0xfd, 0x6b, 0x09, 0x4a, 0x1c, 0xfd, 0xe4, 0x17, 0x0e, 0x1c,
	0x9c, 0x3b, 0x6d, 0x8c, 0xbe, 0x94, 0x12, 0x5f, 0x2b, 0x46, 0x3e, 0xae, 0xa1, 0xff, 0x9f, 0xfc,
	0x89, 0x61, 0xfc, 0x03, 0x60, 0xf9, 0x3b, 0x57, 0x5c, 0x41, 0x7c, 0xe5, 0x1a, 0x7a, 0x06, 0x37,
	0x52, 0xbf, 0x13, 0xa0, 0xfb, 0x57, 0xfa, 0xc4, 0x51, 0xde, 0xbd, 0x0a, 0x3b, 0xdb, 0x39, 0x80,
	0xd5, 0x94, 0xd7, 0x7b, 0xf4, 0xe6, 0x64, 0x41, 0x43, 0x5f, 0x1e, 0xca, 0x6f, 0xcd, 0xce, 0x3c,
	0xb6, 0x67, 0x9c, 0x14, 0xd3, 0xf6, 0x1c, 0x6a, 0x5a, 0xa6, 0xed, 0x39, 0xd2, 0x38, 0x5c, 0x43,
	0xbf, 0x94, 0xe0, 0xce, 0x84, 0x67, 0x02, 0xf4, 0x76, 0xaa, 0xbc, 0xc9, 0x4f, 0x25, 0xe5, 0x77,
	0xae, 0xbe, 0x88, 0x29, 0xf3, 0x3b, 0x09, 0xb6, 0x66, 0x78, 0x2c, 0x41, 0xdf, 0x9f, 0x5d, 0xfe,
	0xd8, 0x33, 0xcb, 0x73, 0x2b, 0xf7, 0x1b, 0x09, 0xee, 0x4e, 0xec, 0x91, 0xd1, 0xc3, 0xd9, 0xb1,
	0x4f, 0xf4, 0xfe, 0xe5, 0x47, 0xcf, 0xb3, 0x8c, 0xa9, 0xf4, 0x5b, 0x89, 0x7d, 0x74, 0xcf, 0xaa,
	0x43, 0xe8, 0xdd, 0x2c, 0xc9, 0x53, 0x9a, 0xb8, 0xf2, 0x7b, 0x57, 0x5f, 0xc8, 0x6f, 0x8b, 0xca,
	0x35, 0xf4, 0x33, 0x09, 0x6e, 0xa4, 0xde, 0x28, 0x51, 0x7a, 0x09, 0x98, 0x74, 0xa7, 0x2d, 0xef,
	0x5d, 0x65, 0xc9, 0x40, 0x85, 0xaf, 0x24, 0xb8, 0x9d, 0xd9, 0xff, 0x67, 0xa8, 0x31, 0xe9, 0xed,
	0x21, 0x43, 0x8d, 0xc9, 0x4f, 0x0c, 0xd7, 0xd0, 0xaf, 0x25, 0xfe, 0xa4, 0x91, 0xd5, 0xbb, 0xa3,
	0x77, 0x32, 0xc5, 0x4e, 0x78, 0x97, 0x28, 0x3f, 0x7c, 0x8e, 0x55, 0x4c, 0x1f, 0x07, 0xe4, 0xd1,
	0xc6, 0x1e, 0x6d, 0x67, 0x0a, 0x1b, 0x79, 0x4d, 0x28, 0xbf, 0x31, 0x23, 0x27, 0xdd, 0xea, 0xf1,
	0xbb, 0x9f, 0x3e, 0xec, 0x78, 0x5d, 0xcb, 0xed, 0xec, 0x3e, 0xdc, 0x0b, 0xc3, 0xdd, 0xb6, 0xd7,
	0x7b, 0xc0, 0xfe, 0x3b, 0xd2, 0xf6, 0xba, 0x0f, 0x08, 0x3f, 0x60, 0x48, 0xda, 0x3f, 0x4c, 0x4e,
	0xe6, 0x19, 0xdb, 0xdb, 0xff, 0x0b, 0x00, 0x00, 0xff, 0xff, 0x1e, 0x48, 0x2d, 0x11, 0xa2, 0x22,
	0x00, 0x00,
}
