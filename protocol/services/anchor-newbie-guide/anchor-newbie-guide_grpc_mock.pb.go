// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/anchor-newbie-guide/anchor-newbie-guide.proto

package anchor_newbie_guide

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAnchorNewbieGuideServiceClient is a mock of AnchorNewbieGuideServiceClient interface.
type MockAnchorNewbieGuideServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockAnchorNewbieGuideServiceClientMockRecorder
}

// MockAnchorNewbieGuideServiceClientMockRecorder is the mock recorder for MockAnchorNewbieGuideServiceClient.
type MockAnchorNewbieGuideServiceClientMockRecorder struct {
	mock *MockAnchorNewbieGuideServiceClient
}

// NewMockAnchorNewbieGuideServiceClient creates a new mock instance.
func NewMockAnchorNewbieGuideServiceClient(ctrl *gomock.Controller) *MockAnchorNewbieGuideServiceClient {
	mock := &MockAnchorNewbieGuideServiceClient{ctrl: ctrl}
	mock.recorder = &MockAnchorNewbieGuideServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAnchorNewbieGuideServiceClient) EXPECT() *MockAnchorNewbieGuideServiceClientMockRecorder {
	return m.recorder
}

// ClaimNewbieAnchorTaskAdvancedReward mocks base method.
func (m *MockAnchorNewbieGuideServiceClient) ClaimNewbieAnchorTaskAdvancedReward(ctx context.Context, in *ClaimNewbieAnchorTaskAdvancedRewardReq, opts ...grpc.CallOption) (*ClaimNewbieAnchorTaskRewardResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClaimNewbieAnchorTaskAdvancedReward", varargs...)
	ret0, _ := ret[0].(*ClaimNewbieAnchorTaskRewardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClaimNewbieAnchorTaskAdvancedReward indicates an expected call of ClaimNewbieAnchorTaskAdvancedReward.
func (mr *MockAnchorNewbieGuideServiceClientMockRecorder) ClaimNewbieAnchorTaskAdvancedReward(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClaimNewbieAnchorTaskAdvancedReward", reflect.TypeOf((*MockAnchorNewbieGuideServiceClient)(nil).ClaimNewbieAnchorTaskAdvancedReward), varargs...)
}

// ClaimNewbieAnchorTaskReward mocks base method.
func (m *MockAnchorNewbieGuideServiceClient) ClaimNewbieAnchorTaskReward(ctx context.Context, in *ClaimNewbieAnchorTaskRewardReq, opts ...grpc.CallOption) (*ClaimNewbieAnchorTaskRewardResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClaimNewbieAnchorTaskReward", varargs...)
	ret0, _ := ret[0].(*ClaimNewbieAnchorTaskRewardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClaimNewbieAnchorTaskReward indicates an expected call of ClaimNewbieAnchorTaskReward.
func (mr *MockAnchorNewbieGuideServiceClientMockRecorder) ClaimNewbieAnchorTaskReward(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClaimNewbieAnchorTaskReward", reflect.TypeOf((*MockAnchorNewbieGuideServiceClient)(nil).ClaimNewbieAnchorTaskReward), varargs...)
}

// GetChannelLiveEndGuidePopup mocks base method.
func (m *MockAnchorNewbieGuideServiceClient) GetChannelLiveEndGuidePopup(ctx context.Context, in *GetChannelLiveEndGuidePopupRequest, opts ...grpc.CallOption) (*GetChannelLiveEndGuidePopupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelLiveEndGuidePopup", varargs...)
	ret0, _ := ret[0].(*GetChannelLiveEndGuidePopupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveEndGuidePopup indicates an expected call of GetChannelLiveEndGuidePopup.
func (mr *MockAnchorNewbieGuideServiceClientMockRecorder) GetChannelLiveEndGuidePopup(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveEndGuidePopup", reflect.TypeOf((*MockAnchorNewbieGuideServiceClient)(nil).GetChannelLiveEndGuidePopup), varargs...)
}

// GetNewbieAnchorGuideInfo mocks base method.
func (m *MockAnchorNewbieGuideServiceClient) GetNewbieAnchorGuideInfo(ctx context.Context, in *GetNewbieAnchorGuideInfoReq, opts ...grpc.CallOption) (*GetNewbieAnchorGuideInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewbieAnchorGuideInfo", varargs...)
	ret0, _ := ret[0].(*GetNewbieAnchorGuideInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewbieAnchorGuideInfo indicates an expected call of GetNewbieAnchorGuideInfo.
func (mr *MockAnchorNewbieGuideServiceClientMockRecorder) GetNewbieAnchorGuideInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewbieAnchorGuideInfo", reflect.TypeOf((*MockAnchorNewbieGuideServiceClient)(nil).GetNewbieAnchorGuideInfo), varargs...)
}

// GetNewbieAnchorTask mocks base method.
func (m *MockAnchorNewbieGuideServiceClient) GetNewbieAnchorTask(ctx context.Context, in *GetNewbieAnchorTaskReq, opts ...grpc.CallOption) (*GetNewbieAnchorTaskResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewbieAnchorTask", varargs...)
	ret0, _ := ret[0].(*GetNewbieAnchorTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewbieAnchorTask indicates an expected call of GetNewbieAnchorTask.
func (mr *MockAnchorNewbieGuideServiceClientMockRecorder) GetNewbieAnchorTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewbieAnchorTask", reflect.TypeOf((*MockAnchorNewbieGuideServiceClient)(nil).GetNewbieAnchorTask), varargs...)
}

// GetNewbieAnchorTaskRewardList mocks base method.
func (m *MockAnchorNewbieGuideServiceClient) GetNewbieAnchorTaskRewardList(ctx context.Context, in *GetNewbieAnchorTaskRewardListReq, opts ...grpc.CallOption) (*GetNewbieAnchorTaskRewardListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewbieAnchorTaskRewardList", varargs...)
	ret0, _ := ret[0].(*GetNewbieAnchorTaskRewardListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewbieAnchorTaskRewardList indicates an expected call of GetNewbieAnchorTaskRewardList.
func (mr *MockAnchorNewbieGuideServiceClientMockRecorder) GetNewbieAnchorTaskRewardList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewbieAnchorTaskRewardList", reflect.TypeOf((*MockAnchorNewbieGuideServiceClient)(nil).GetNewbieAnchorTaskRewardList), varargs...)
}

// GetNewbieGuidePopInfo mocks base method.
func (m *MockAnchorNewbieGuideServiceClient) GetNewbieGuidePopInfo(ctx context.Context, in *GetNewbieGuidePopInfoReq, opts ...grpc.CallOption) (*GetNewbieGuidePopInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewbieGuidePopInfo", varargs...)
	ret0, _ := ret[0].(*GetNewbieGuidePopInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewbieGuidePopInfo indicates an expected call of GetNewbieGuidePopInfo.
func (mr *MockAnchorNewbieGuideServiceClientMockRecorder) GetNewbieGuidePopInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewbieGuidePopInfo", reflect.TypeOf((*MockAnchorNewbieGuideServiceClient)(nil).GetNewbieGuidePopInfo), varargs...)
}

// GetNewbieWidgetInfo mocks base method.
func (m *MockAnchorNewbieGuideServiceClient) GetNewbieWidgetInfo(ctx context.Context, in *GetNewbieWidgetInfoReq, opts ...grpc.CallOption) (*GetNewbieWidgetInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewbieWidgetInfo", varargs...)
	ret0, _ := ret[0].(*GetNewbieWidgetInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewbieWidgetInfo indicates an expected call of GetNewbieWidgetInfo.
func (mr *MockAnchorNewbieGuideServiceClientMockRecorder) GetNewbieWidgetInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewbieWidgetInfo", reflect.TypeOf((*MockAnchorNewbieGuideServiceClient)(nil).GetNewbieWidgetInfo), varargs...)
}

// SendChannelLiveThanks mocks base method.
func (m *MockAnchorNewbieGuideServiceClient) SendChannelLiveThanks(ctx context.Context, in *SendChannelLiveThanksRequest, opts ...grpc.CallOption) (*SendChannelLiveThanksResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendChannelLiveThanks", varargs...)
	ret0, _ := ret[0].(*SendChannelLiveThanksResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendChannelLiveThanks indicates an expected call of SendChannelLiveThanks.
func (mr *MockAnchorNewbieGuideServiceClientMockRecorder) SendChannelLiveThanks(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendChannelLiveThanks", reflect.TypeOf((*MockAnchorNewbieGuideServiceClient)(nil).SendChannelLiveThanks), varargs...)
}

// TestCompleteNewbieAnchorTask mocks base method.
func (m *MockAnchorNewbieGuideServiceClient) TestCompleteNewbieAnchorTask(ctx context.Context, in *TestCompleteNewbieAnchorTaskReq, opts ...grpc.CallOption) (*TestCompleteNewbieAnchorTaskResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestCompleteNewbieAnchorTask", varargs...)
	ret0, _ := ret[0].(*TestCompleteNewbieAnchorTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestCompleteNewbieAnchorTask indicates an expected call of TestCompleteNewbieAnchorTask.
func (mr *MockAnchorNewbieGuideServiceClientMockRecorder) TestCompleteNewbieAnchorTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestCompleteNewbieAnchorTask", reflect.TypeOf((*MockAnchorNewbieGuideServiceClient)(nil).TestCompleteNewbieAnchorTask), varargs...)
}

// TestResetNewbieAnchorTask mocks base method.
func (m *MockAnchorNewbieGuideServiceClient) TestResetNewbieAnchorTask(ctx context.Context, in *TestResetNewbieAnchorTaskReq, opts ...grpc.CallOption) (*TestResetNewbieAnchorTaskResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestResetNewbieAnchorTask", varargs...)
	ret0, _ := ret[0].(*TestResetNewbieAnchorTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestResetNewbieAnchorTask indicates an expected call of TestResetNewbieAnchorTask.
func (mr *MockAnchorNewbieGuideServiceClientMockRecorder) TestResetNewbieAnchorTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestResetNewbieAnchorTask", reflect.TypeOf((*MockAnchorNewbieGuideServiceClient)(nil).TestResetNewbieAnchorTask), varargs...)
}

// TestTriggerTimer mocks base method.
func (m *MockAnchorNewbieGuideServiceClient) TestTriggerTimer(ctx context.Context, in *TestTriggerTimerReq, opts ...grpc.CallOption) (*TestTriggerTimerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestTriggerTimer", varargs...)
	ret0, _ := ret[0].(*TestTriggerTimerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestTriggerTimer indicates an expected call of TestTriggerTimer.
func (mr *MockAnchorNewbieGuideServiceClientMockRecorder) TestTriggerTimer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestTriggerTimer", reflect.TypeOf((*MockAnchorNewbieGuideServiceClient)(nil).TestTriggerTimer), varargs...)
}

// MockAnchorNewbieGuideServiceServer is a mock of AnchorNewbieGuideServiceServer interface.
type MockAnchorNewbieGuideServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockAnchorNewbieGuideServiceServerMockRecorder
}

// MockAnchorNewbieGuideServiceServerMockRecorder is the mock recorder for MockAnchorNewbieGuideServiceServer.
type MockAnchorNewbieGuideServiceServerMockRecorder struct {
	mock *MockAnchorNewbieGuideServiceServer
}

// NewMockAnchorNewbieGuideServiceServer creates a new mock instance.
func NewMockAnchorNewbieGuideServiceServer(ctrl *gomock.Controller) *MockAnchorNewbieGuideServiceServer {
	mock := &MockAnchorNewbieGuideServiceServer{ctrl: ctrl}
	mock.recorder = &MockAnchorNewbieGuideServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAnchorNewbieGuideServiceServer) EXPECT() *MockAnchorNewbieGuideServiceServerMockRecorder {
	return m.recorder
}

// ClaimNewbieAnchorTaskAdvancedReward mocks base method.
func (m *MockAnchorNewbieGuideServiceServer) ClaimNewbieAnchorTaskAdvancedReward(ctx context.Context, in *ClaimNewbieAnchorTaskAdvancedRewardReq) (*ClaimNewbieAnchorTaskRewardResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClaimNewbieAnchorTaskAdvancedReward", ctx, in)
	ret0, _ := ret[0].(*ClaimNewbieAnchorTaskRewardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClaimNewbieAnchorTaskAdvancedReward indicates an expected call of ClaimNewbieAnchorTaskAdvancedReward.
func (mr *MockAnchorNewbieGuideServiceServerMockRecorder) ClaimNewbieAnchorTaskAdvancedReward(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClaimNewbieAnchorTaskAdvancedReward", reflect.TypeOf((*MockAnchorNewbieGuideServiceServer)(nil).ClaimNewbieAnchorTaskAdvancedReward), ctx, in)
}

// ClaimNewbieAnchorTaskReward mocks base method.
func (m *MockAnchorNewbieGuideServiceServer) ClaimNewbieAnchorTaskReward(ctx context.Context, in *ClaimNewbieAnchorTaskRewardReq) (*ClaimNewbieAnchorTaskRewardResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClaimNewbieAnchorTaskReward", ctx, in)
	ret0, _ := ret[0].(*ClaimNewbieAnchorTaskRewardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClaimNewbieAnchorTaskReward indicates an expected call of ClaimNewbieAnchorTaskReward.
func (mr *MockAnchorNewbieGuideServiceServerMockRecorder) ClaimNewbieAnchorTaskReward(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClaimNewbieAnchorTaskReward", reflect.TypeOf((*MockAnchorNewbieGuideServiceServer)(nil).ClaimNewbieAnchorTaskReward), ctx, in)
}

// GetChannelLiveEndGuidePopup mocks base method.
func (m *MockAnchorNewbieGuideServiceServer) GetChannelLiveEndGuidePopup(ctx context.Context, in *GetChannelLiveEndGuidePopupRequest) (*GetChannelLiveEndGuidePopupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveEndGuidePopup", ctx, in)
	ret0, _ := ret[0].(*GetChannelLiveEndGuidePopupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveEndGuidePopup indicates an expected call of GetChannelLiveEndGuidePopup.
func (mr *MockAnchorNewbieGuideServiceServerMockRecorder) GetChannelLiveEndGuidePopup(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveEndGuidePopup", reflect.TypeOf((*MockAnchorNewbieGuideServiceServer)(nil).GetChannelLiveEndGuidePopup), ctx, in)
}

// GetNewbieAnchorGuideInfo mocks base method.
func (m *MockAnchorNewbieGuideServiceServer) GetNewbieAnchorGuideInfo(ctx context.Context, in *GetNewbieAnchorGuideInfoReq) (*GetNewbieAnchorGuideInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewbieAnchorGuideInfo", ctx, in)
	ret0, _ := ret[0].(*GetNewbieAnchorGuideInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewbieAnchorGuideInfo indicates an expected call of GetNewbieAnchorGuideInfo.
func (mr *MockAnchorNewbieGuideServiceServerMockRecorder) GetNewbieAnchorGuideInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewbieAnchorGuideInfo", reflect.TypeOf((*MockAnchorNewbieGuideServiceServer)(nil).GetNewbieAnchorGuideInfo), ctx, in)
}

// GetNewbieAnchorTask mocks base method.
func (m *MockAnchorNewbieGuideServiceServer) GetNewbieAnchorTask(ctx context.Context, in *GetNewbieAnchorTaskReq) (*GetNewbieAnchorTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewbieAnchorTask", ctx, in)
	ret0, _ := ret[0].(*GetNewbieAnchorTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewbieAnchorTask indicates an expected call of GetNewbieAnchorTask.
func (mr *MockAnchorNewbieGuideServiceServerMockRecorder) GetNewbieAnchorTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewbieAnchorTask", reflect.TypeOf((*MockAnchorNewbieGuideServiceServer)(nil).GetNewbieAnchorTask), ctx, in)
}

// GetNewbieAnchorTaskRewardList mocks base method.
func (m *MockAnchorNewbieGuideServiceServer) GetNewbieAnchorTaskRewardList(ctx context.Context, in *GetNewbieAnchorTaskRewardListReq) (*GetNewbieAnchorTaskRewardListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewbieAnchorTaskRewardList", ctx, in)
	ret0, _ := ret[0].(*GetNewbieAnchorTaskRewardListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewbieAnchorTaskRewardList indicates an expected call of GetNewbieAnchorTaskRewardList.
func (mr *MockAnchorNewbieGuideServiceServerMockRecorder) GetNewbieAnchorTaskRewardList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewbieAnchorTaskRewardList", reflect.TypeOf((*MockAnchorNewbieGuideServiceServer)(nil).GetNewbieAnchorTaskRewardList), ctx, in)
}

// GetNewbieGuidePopInfo mocks base method.
func (m *MockAnchorNewbieGuideServiceServer) GetNewbieGuidePopInfo(ctx context.Context, in *GetNewbieGuidePopInfoReq) (*GetNewbieGuidePopInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewbieGuidePopInfo", ctx, in)
	ret0, _ := ret[0].(*GetNewbieGuidePopInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewbieGuidePopInfo indicates an expected call of GetNewbieGuidePopInfo.
func (mr *MockAnchorNewbieGuideServiceServerMockRecorder) GetNewbieGuidePopInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewbieGuidePopInfo", reflect.TypeOf((*MockAnchorNewbieGuideServiceServer)(nil).GetNewbieGuidePopInfo), ctx, in)
}

// GetNewbieWidgetInfo mocks base method.
func (m *MockAnchorNewbieGuideServiceServer) GetNewbieWidgetInfo(ctx context.Context, in *GetNewbieWidgetInfoReq) (*GetNewbieWidgetInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewbieWidgetInfo", ctx, in)
	ret0, _ := ret[0].(*GetNewbieWidgetInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewbieWidgetInfo indicates an expected call of GetNewbieWidgetInfo.
func (mr *MockAnchorNewbieGuideServiceServerMockRecorder) GetNewbieWidgetInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewbieWidgetInfo", reflect.TypeOf((*MockAnchorNewbieGuideServiceServer)(nil).GetNewbieWidgetInfo), ctx, in)
}

// SendChannelLiveThanks mocks base method.
func (m *MockAnchorNewbieGuideServiceServer) SendChannelLiveThanks(ctx context.Context, in *SendChannelLiveThanksRequest) (*SendChannelLiveThanksResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendChannelLiveThanks", ctx, in)
	ret0, _ := ret[0].(*SendChannelLiveThanksResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendChannelLiveThanks indicates an expected call of SendChannelLiveThanks.
func (mr *MockAnchorNewbieGuideServiceServerMockRecorder) SendChannelLiveThanks(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendChannelLiveThanks", reflect.TypeOf((*MockAnchorNewbieGuideServiceServer)(nil).SendChannelLiveThanks), ctx, in)
}

// TestCompleteNewbieAnchorTask mocks base method.
func (m *MockAnchorNewbieGuideServiceServer) TestCompleteNewbieAnchorTask(ctx context.Context, in *TestCompleteNewbieAnchorTaskReq) (*TestCompleteNewbieAnchorTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestCompleteNewbieAnchorTask", ctx, in)
	ret0, _ := ret[0].(*TestCompleteNewbieAnchorTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestCompleteNewbieAnchorTask indicates an expected call of TestCompleteNewbieAnchorTask.
func (mr *MockAnchorNewbieGuideServiceServerMockRecorder) TestCompleteNewbieAnchorTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestCompleteNewbieAnchorTask", reflect.TypeOf((*MockAnchorNewbieGuideServiceServer)(nil).TestCompleteNewbieAnchorTask), ctx, in)
}

// TestResetNewbieAnchorTask mocks base method.
func (m *MockAnchorNewbieGuideServiceServer) TestResetNewbieAnchorTask(ctx context.Context, in *TestResetNewbieAnchorTaskReq) (*TestResetNewbieAnchorTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestResetNewbieAnchorTask", ctx, in)
	ret0, _ := ret[0].(*TestResetNewbieAnchorTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestResetNewbieAnchorTask indicates an expected call of TestResetNewbieAnchorTask.
func (mr *MockAnchorNewbieGuideServiceServerMockRecorder) TestResetNewbieAnchorTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestResetNewbieAnchorTask", reflect.TypeOf((*MockAnchorNewbieGuideServiceServer)(nil).TestResetNewbieAnchorTask), ctx, in)
}

// TestTriggerTimer mocks base method.
func (m *MockAnchorNewbieGuideServiceServer) TestTriggerTimer(ctx context.Context, in *TestTriggerTimerReq) (*TestTriggerTimerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestTriggerTimer", ctx, in)
	ret0, _ := ret[0].(*TestTriggerTimerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestTriggerTimer indicates an expected call of TestTriggerTimer.
func (mr *MockAnchorNewbieGuideServiceServerMockRecorder) TestTriggerTimer(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestTriggerTimer", reflect.TypeOf((*MockAnchorNewbieGuideServiceServer)(nil).TestTriggerTimer), ctx, in)
}
