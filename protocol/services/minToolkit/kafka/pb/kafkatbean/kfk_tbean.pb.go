// Code generated by protoc-gen-go. DO NOT EDIT.
// source: src/minToolkit/kafka/pb/kafkatbean/kfk_tbean.proto

package kafkatbean

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TBeanConsumeSourceType int32

const (
	TBeanConsumeSourceType_ENUM_UNKOWN_TYPE           TBeanConsumeSourceType = 0
	TBeanConsumeSourceType_EVENT_EVENT_PRESENT        TBeanConsumeSourceType = 1
	TBeanConsumeSourceType_ENUM_BUY_YKW_TYPE          TBeanConsumeSourceType = 2
	TBeanConsumeSourceType_ENUM_WEREWOLF_BUY_IDENTITY TBeanConsumeSourceType = 3
	TBeanConsumeSourceType_ENUM_WEREWOLF_BUY_TIME     TBeanConsumeSourceType = 4
	TBeanConsumeSourceType_ENUM_TREASURE_HOUSE_BUY    TBeanConsumeSourceType = 5
	TBeanConsumeSourceType_ENUM_BUY_FELLOW_HOUSE      TBeanConsumeSourceType = 6
	TBeanConsumeSourceType_ENUM_VIRTUAL_IMAGE_BUY     TBeanConsumeSourceType = 7
	TBeanConsumeSourceType_ENUM_AI_PRESENT            TBeanConsumeSourceType = 8
)

var TBeanConsumeSourceType_name = map[int32]string{
	0: "ENUM_UNKOWN_TYPE",
	1: "EVENT_EVENT_PRESENT",
	2: "ENUM_BUY_YKW_TYPE",
	3: "ENUM_WEREWOLF_BUY_IDENTITY",
	4: "ENUM_WEREWOLF_BUY_TIME",
	5: "ENUM_TREASURE_HOUSE_BUY",
	6: "ENUM_BUY_FELLOW_HOUSE",
	7: "ENUM_VIRTUAL_IMAGE_BUY",
	8: "ENUM_AI_PRESENT",
}
var TBeanConsumeSourceType_value = map[string]int32{
	"ENUM_UNKOWN_TYPE":           0,
	"EVENT_EVENT_PRESENT":        1,
	"ENUM_BUY_YKW_TYPE":          2,
	"ENUM_WEREWOLF_BUY_IDENTITY": 3,
	"ENUM_WEREWOLF_BUY_TIME":     4,
	"ENUM_TREASURE_HOUSE_BUY":    5,
	"ENUM_BUY_FELLOW_HOUSE":      6,
	"ENUM_VIRTUAL_IMAGE_BUY":     7,
	"ENUM_AI_PRESENT":            8,
}

func (x TBeanConsumeSourceType) String() string {
	return proto.EnumName(TBeanConsumeSourceType_name, int32(x))
}
func (TBeanConsumeSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_kfk_tbean_de1f6ce875c5ee07, []int{0}
}

type TBeanConsumeEvent struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Value                uint32   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	Timestamp            uint32   `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Source               string   `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
	OrderId              string   `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	SourceType           uint32   `protobuf:"varint,6,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	ChannelId            uint32   `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DealToken            string   `protobuf:"bytes,8,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	TargetUid            uint32   `protobuf:"varint,9,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TBeanConsumeEvent) Reset()         { *m = TBeanConsumeEvent{} }
func (m *TBeanConsumeEvent) String() string { return proto.CompactTextString(m) }
func (*TBeanConsumeEvent) ProtoMessage()    {}
func (*TBeanConsumeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_kfk_tbean_de1f6ce875c5ee07, []int{0}
}
func (m *TBeanConsumeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TBeanConsumeEvent.Unmarshal(m, b)
}
func (m *TBeanConsumeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TBeanConsumeEvent.Marshal(b, m, deterministic)
}
func (dst *TBeanConsumeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TBeanConsumeEvent.Merge(dst, src)
}
func (m *TBeanConsumeEvent) XXX_Size() int {
	return xxx_messageInfo_TBeanConsumeEvent.Size(m)
}
func (m *TBeanConsumeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_TBeanConsumeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_TBeanConsumeEvent proto.InternalMessageInfo

func (m *TBeanConsumeEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TBeanConsumeEvent) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *TBeanConsumeEvent) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *TBeanConsumeEvent) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *TBeanConsumeEvent) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *TBeanConsumeEvent) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *TBeanConsumeEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TBeanConsumeEvent) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

func (m *TBeanConsumeEvent) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func init() {
	proto.RegisterType((*TBeanConsumeEvent)(nil), "kafkatbean.TBeanConsumeEvent")
	proto.RegisterEnum("kafkatbean.TBeanConsumeSourceType", TBeanConsumeSourceType_name, TBeanConsumeSourceType_value)
}

func init() {
	proto.RegisterFile("src/minToolkit/kafka/pb/kafkatbean/kfk_tbean.proto", fileDescriptor_kfk_tbean_de1f6ce875c5ee07)
}

var fileDescriptor_kfk_tbean_de1f6ce875c5ee07 = []byte{
	// 414 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x64, 0x92, 0x5d, 0x6f, 0xd3, 0x30,
	0x14, 0x86, 0x69, 0xbb, 0x7e, 0x1d, 0x84, 0xf0, 0xbc, 0xad, 0xcb, 0xc6, 0xd7, 0xc4, 0xd5, 0xc4,
	0x05, 0x95, 0xe0, 0x17, 0x74, 0xe0, 0x81, 0xb5, 0x36, 0x9d, 0x52, 0x67, 0x51, 0xaf, 0x2c, 0xb7,
	0xf1, 0x20, 0x4a, 0x63, 0x47, 0x89, 0x33, 0xa9, 0xd7, 0xdc, 0xf1, 0xab, 0x91, 0xed, 0xaa, 0x45,
	0xda, 0x4d, 0x74, 0xce, 0xf3, 0xbc, 0x3e, 0xc7, 0x89, 0x02, 0x5f, 0xea, 0x6a, 0x3d, 0x2e, 0x32,
	0xc5, 0xb4, 0xde, 0xe4, 0x99, 0x19, 0xe7, 0xe2, 0x31, 0x17, 0xe3, 0x72, 0xe5, 0x0b, 0xb3, 0x92,
	0x42, 0x8d, 0xf3, 0xc7, 0x9c, 0xbb, 0xea, 0x73, 0x59, 0x69, 0xa3, 0x31, 0x1c, 0xdc, 0xc7, 0xbf,
	0x6d, 0x38, 0x66, 0x37, 0x52, 0xa8, 0x6f, 0x5a, 0xd5, 0x4d, 0x21, 0xc9, 0x93, 0x54, 0x06, 0x23,
	0xe8, 0x34, 0x59, 0x1a, 0xb4, 0xae, 0x5a, 0xd7, 0xaf, 0x22, 0x5b, 0xe2, 0x53, 0xe8, 0x3e, 0x89,
	0x4d, 0x23, 0x83, 0xb6, 0x63, 0xbe, 0xc1, 0x6f, 0x61, 0x68, 0xb2, 0x42, 0xd6, 0x46, 0x14, 0x65,
	0xd0, 0x71, 0xe6, 0x00, 0xf0, 0x08, 0x7a, 0xb5, 0x6e, 0xaa, 0xb5, 0x0c, 0x8e, 0xae, 0x5a, 0xd7,
	0xc3, 0x68, 0xd7, 0xe1, 0x0b, 0x18, 0xe8, 0x2a, 0x95, 0x15, 0xcf, 0xd2, 0xa0, 0xeb, 0x4c, 0xdf,
	0xf5, 0x34, 0xc5, 0x1f, 0xe0, 0xa5, 0x0f, 0x71, 0xb3, 0x2d, 0x65, 0xd0, 0x73, 0x23, 0xc1, 0x23,
	0xb6, 0x2d, 0x25, 0x7e, 0x07, 0xb0, 0xfe, 0x2d, 0x94, 0x92, 0x1b, 0x7b, 0xba, 0xef, 0x57, 0xee,
	0x08, 0x4d, 0xad, 0x4e, 0xa5, 0xd8, 0x70, 0xa3, 0x73, 0xa9, 0x82, 0x81, 0x1b, 0x3e, 0xb4, 0x84,
	0x59, 0x60, 0xb5, 0x11, 0xd5, 0x2f, 0x69, 0xb8, 0x7d, 0xbd, 0xe1, 0xee, 0xc2, 0x8e, 0xc4, 0x59,
	0xfa, 0xe9, 0x4f, 0x1b, 0x46, 0xff, 0x7f, 0x8c, 0xc5, 0x61, 0xef, 0x29, 0x20, 0x12, 0xc6, 0x33,
	0x1e, 0x87, 0x77, 0xf3, 0x24, 0xe4, 0x6c, 0x79, 0x4f, 0xd0, 0x0b, 0x7c, 0x0e, 0x27, 0xe4, 0x81,
	0x84, 0x8c, 0xfb, 0xe7, 0x7d, 0x44, 0x16, 0x24, 0x64, 0xa8, 0x85, 0xcf, 0xe0, 0xd8, 0xc5, 0x6f,
	0xe2, 0x25, 0x5f, 0xde, 0x25, 0x3e, 0xdf, 0xc6, 0xef, 0xe1, 0xd2, 0xe1, 0x84, 0x44, 0x24, 0x99,
	0x4f, 0x6f, 0x9d, 0xa7, 0xdf, 0x49, 0xc8, 0x28, 0x5b, 0xa2, 0x0e, 0xbe, 0x84, 0xd1, 0x73, 0xcf,
	0xe8, 0x8c, 0xa0, 0x23, 0xfc, 0x06, 0xce, 0x9d, 0x63, 0x11, 0x99, 0x2c, 0xe2, 0x88, 0xf0, 0x9f,
	0xf3, 0x78, 0x41, 0x6c, 0x02, 0x75, 0xf1, 0x05, 0x9c, 0xed, 0xf7, 0xdd, 0x92, 0xe9, 0x74, 0x9e,
	0x78, 0x8d, 0x7a, 0xfb, 0x99, 0x0f, 0x34, 0x62, 0xf1, 0x64, 0xca, 0xe9, 0x6c, 0xf2, 0xc3, 0x1f,
	0xeb, 0xe3, 0x13, 0x78, 0xed, 0xdc, 0x84, 0xee, 0xef, 0x3e, 0x58, 0xf5, 0xdc, 0x5f, 0xf2, 0xf5,
	0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x55, 0xb7, 0xfe, 0x5b, 0x02, 0x00, 0x00,
}
