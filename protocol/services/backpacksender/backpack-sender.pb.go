// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/backpack-sender/backpack-sender.proto

package backpacksender // import "golang.52tt.com/protocol/services/backpacksender"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type LimitTimeType int32

const (
	LimitTimeType_UNKOWN   LimitTimeType = 0
	LimitTimeType_HOUR_CNT LimitTimeType = 1
	LimitTimeType_DAY_CNT  LimitTimeType = 2
	LimitTimeType_HOUR_VAL LimitTimeType = 3
	LimitTimeType_DAY_VAL  LimitTimeType = 4
)

var LimitTimeType_name = map[int32]string{
	0: "UNKOWN",
	1: "HOUR_CNT",
	2: "DAY_CNT",
	3: "HOUR_VAL",
	4: "DAY_VAL",
}
var LimitTimeType_value = map[string]int32{
	"UNKOWN":   0,
	"HOUR_CNT": 1,
	"DAY_CNT":  2,
	"HOUR_VAL": 3,
	"DAY_VAL":  4,
}

func (x LimitTimeType) String() string {
	return proto.EnumName(LimitTimeType_name, int32(x))
}
func (LimitTimeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{0}
}

type OrderStatus int32

const (
	OrderStatus_INIT OrderStatus = 0
	OrderStatus_SEND OrderStatus = 1
)

var OrderStatus_name = map[int32]string{
	0: "INIT",
	1: "SEND",
}
var OrderStatus_value = map[string]int32{
	"INIT": 0,
	"SEND": 1,
}

func (x OrderStatus) String() string {
	return proto.EnumName(OrderStatus_name, int32(x))
}
func (OrderStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{1}
}

// T豆包裹类型
type PresentBusinessType int32

const (
	PresentBusinessType_E_PRESENT_BUSINESS_TYPE_DEFAULT          PresentBusinessType = 0
	PresentBusinessType_E_PRESENT_BUSINESS_TYPE_INTIMATE_PRESENT PresentBusinessType = 1
)

var PresentBusinessType_name = map[int32]string{
	0: "E_PRESENT_BUSINESS_TYPE_DEFAULT",
	1: "E_PRESENT_BUSINESS_TYPE_INTIMATE_PRESENT",
}
var PresentBusinessType_value = map[string]int32{
	"E_PRESENT_BUSINESS_TYPE_DEFAULT":          0,
	"E_PRESENT_BUSINESS_TYPE_INTIMATE_PRESENT": 1,
}

func (x PresentBusinessType) String() string {
	return proto.EnumName(PresentBusinessType_name, int32(x))
}
func (PresentBusinessType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{2}
}

type DeductType int32

const (
	DeductType_UNKNOW_DEDUCT_TYPE DeductType = 0
	DeductType_OPRATE_DEDUCT      DeductType = 1
	DeductType_BUSINESS_DEDUCT    DeductType = 2
)

var DeductType_name = map[int32]string{
	0: "UNKNOW_DEDUCT_TYPE",
	1: "OPRATE_DEDUCT",
	2: "BUSINESS_DEDUCT",
}
var DeductType_value = map[string]int32{
	"UNKNOW_DEDUCT_TYPE": 0,
	"OPRATE_DEDUCT":      1,
	"BUSINESS_DEDUCT":    2,
}

func (x DeductType) String() string {
	return proto.EnumName(DeductType_name, int32(x))
}
func (DeductType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{3}
}

type DeductFailType int32

const (
	DeductFailType_DEDUCTFAILTYPE_UNVALID          DeductFailType = 0
	DeductFailType_DEDUCTFAILTYPE_UID_NOT_EXIST    DeductFailType = 1
	DeductFailType_DEDUCTFAILTYPE_ITEM_NOT_ENOUGH  DeductFailType = 2
	DeductFailType_DEDUCTFAILTYPE_DEDUCT_ITEM_FAIL DeductFailType = 3
)

var DeductFailType_name = map[int32]string{
	0: "DEDUCTFAILTYPE_UNVALID",
	1: "DEDUCTFAILTYPE_UID_NOT_EXIST",
	2: "DEDUCTFAILTYPE_ITEM_NOT_ENOUGH",
	3: "DEDUCTFAILTYPE_DEDUCT_ITEM_FAIL",
}
var DeductFailType_value = map[string]int32{
	"DEDUCTFAILTYPE_UNVALID":          0,
	"DEDUCTFAILTYPE_UID_NOT_EXIST":    1,
	"DEDUCTFAILTYPE_ITEM_NOT_ENOUGH":  2,
	"DEDUCTFAILTYPE_DEDUCT_ITEM_FAIL": 3,
}

func (x DeductFailType) String() string {
	return proto.EnumName(DeductFailType_name, int32(x))
}
func (DeductFailType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{4}
}

type BusinessConf struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	CallbackUrl          string   `protobuf:"bytes,3,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url,omitempty"`
	SourceId             uint32   `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	OperaUser            string   `protobuf:"bytes,5,opt,name=opera_user,json=operaUser,proto3" json:"opera_user,omitempty"`
	BeginTime            string   `protobuf:"bytes,6,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              string   `protobuf:"bytes,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	BusinessId           uint32   `protobuf:"varint,8,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	SecretKey            string   `protobuf:"bytes,9,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	WarningPrecent       uint32   `protobuf:"varint,10,opt,name=warning_precent,json=warningPrecent,proto3" json:"warning_precent,omitempty"`
	OperEmail            string   `protobuf:"bytes,11,opt,name=oper_email,json=operEmail,proto3" json:"oper_email,omitempty"`
	BusinessType         uint32   `protobuf:"varint,12,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	ActivityId           uint32   `protobuf:"varint,13,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	OperateUser          string   `protobuf:"bytes,14,opt,name=operate_user,json=operateUser,proto3" json:"operate_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BusinessConf) Reset()         { *m = BusinessConf{} }
func (m *BusinessConf) String() string { return proto.CompactTextString(m) }
func (*BusinessConf) ProtoMessage()    {}
func (*BusinessConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{0}
}
func (m *BusinessConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BusinessConf.Unmarshal(m, b)
}
func (m *BusinessConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BusinessConf.Marshal(b, m, deterministic)
}
func (dst *BusinessConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BusinessConf.Merge(dst, src)
}
func (m *BusinessConf) XXX_Size() int {
	return xxx_messageInfo_BusinessConf.Size(m)
}
func (m *BusinessConf) XXX_DiscardUnknown() {
	xxx_messageInfo_BusinessConf.DiscardUnknown(m)
}

var xxx_messageInfo_BusinessConf proto.InternalMessageInfo

func (m *BusinessConf) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *BusinessConf) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *BusinessConf) GetCallbackUrl() string {
	if m != nil {
		return m.CallbackUrl
	}
	return ""
}

func (m *BusinessConf) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *BusinessConf) GetOperaUser() string {
	if m != nil {
		return m.OperaUser
	}
	return ""
}

func (m *BusinessConf) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *BusinessConf) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *BusinessConf) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *BusinessConf) GetSecretKey() string {
	if m != nil {
		return m.SecretKey
	}
	return ""
}

func (m *BusinessConf) GetWarningPrecent() uint32 {
	if m != nil {
		return m.WarningPrecent
	}
	return 0
}

func (m *BusinessConf) GetOperEmail() string {
	if m != nil {
		return m.OperEmail
	}
	return ""
}

func (m *BusinessConf) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *BusinessConf) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *BusinessConf) GetOperateUser() string {
	if m != nil {
		return m.OperateUser
	}
	return ""
}

// 添加业务
type AddBusinessReq struct {
	BusinessConf         *BusinessConf `protobuf:"bytes,1,opt,name=business_conf,json=businessConf,proto3" json:"business_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AddBusinessReq) Reset()         { *m = AddBusinessReq{} }
func (m *AddBusinessReq) String() string { return proto.CompactTextString(m) }
func (*AddBusinessReq) ProtoMessage()    {}
func (*AddBusinessReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{1}
}
func (m *AddBusinessReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBusinessReq.Unmarshal(m, b)
}
func (m *AddBusinessReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBusinessReq.Marshal(b, m, deterministic)
}
func (dst *AddBusinessReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBusinessReq.Merge(dst, src)
}
func (m *AddBusinessReq) XXX_Size() int {
	return xxx_messageInfo_AddBusinessReq.Size(m)
}
func (m *AddBusinessReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBusinessReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddBusinessReq proto.InternalMessageInfo

func (m *AddBusinessReq) GetBusinessConf() *BusinessConf {
	if m != nil {
		return m.BusinessConf
	}
	return nil
}

type AddBusinessResp struct {
	BusinessConf         *BusinessConf `protobuf:"bytes,1,opt,name=business_conf,json=businessConf,proto3" json:"business_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AddBusinessResp) Reset()         { *m = AddBusinessResp{} }
func (m *AddBusinessResp) String() string { return proto.CompactTextString(m) }
func (*AddBusinessResp) ProtoMessage()    {}
func (*AddBusinessResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{2}
}
func (m *AddBusinessResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBusinessResp.Unmarshal(m, b)
}
func (m *AddBusinessResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBusinessResp.Marshal(b, m, deterministic)
}
func (dst *AddBusinessResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBusinessResp.Merge(dst, src)
}
func (m *AddBusinessResp) XXX_Size() int {
	return xxx_messageInfo_AddBusinessResp.Size(m)
}
func (m *AddBusinessResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBusinessResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddBusinessResp proto.InternalMessageInfo

func (m *AddBusinessResp) GetBusinessConf() *BusinessConf {
	if m != nil {
		return m.BusinessConf
	}
	return nil
}

// 获取所有业务
type GetAllBusinessReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllBusinessReq) Reset()         { *m = GetAllBusinessReq{} }
func (m *GetAllBusinessReq) String() string { return proto.CompactTextString(m) }
func (*GetAllBusinessReq) ProtoMessage()    {}
func (*GetAllBusinessReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{3}
}
func (m *GetAllBusinessReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllBusinessReq.Unmarshal(m, b)
}
func (m *GetAllBusinessReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllBusinessReq.Marshal(b, m, deterministic)
}
func (dst *GetAllBusinessReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllBusinessReq.Merge(dst, src)
}
func (m *GetAllBusinessReq) XXX_Size() int {
	return xxx_messageInfo_GetAllBusinessReq.Size(m)
}
func (m *GetAllBusinessReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllBusinessReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllBusinessReq proto.InternalMessageInfo

type GetAllBusinessResp struct {
	BusinessList         []*BusinessConf `protobuf:"bytes,1,rep,name=business_list,json=businessList,proto3" json:"business_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAllBusinessResp) Reset()         { *m = GetAllBusinessResp{} }
func (m *GetAllBusinessResp) String() string { return proto.CompactTextString(m) }
func (*GetAllBusinessResp) ProtoMessage()    {}
func (*GetAllBusinessResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{4}
}
func (m *GetAllBusinessResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllBusinessResp.Unmarshal(m, b)
}
func (m *GetAllBusinessResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllBusinessResp.Marshal(b, m, deterministic)
}
func (dst *GetAllBusinessResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllBusinessResp.Merge(dst, src)
}
func (m *GetAllBusinessResp) XXX_Size() int {
	return xxx_messageInfo_GetAllBusinessResp.Size(m)
}
func (m *GetAllBusinessResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllBusinessResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllBusinessResp proto.InternalMessageInfo

func (m *GetAllBusinessResp) GetBusinessList() []*BusinessConf {
	if m != nil {
		return m.BusinessList
	}
	return nil
}

// 根据id列表获取业务配置
type GetBusinessByIdsReq struct {
	IdList               []uint32 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBusinessByIdsReq) Reset()         { *m = GetBusinessByIdsReq{} }
func (m *GetBusinessByIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetBusinessByIdsReq) ProtoMessage()    {}
func (*GetBusinessByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{5}
}
func (m *GetBusinessByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessByIdsReq.Unmarshal(m, b)
}
func (m *GetBusinessByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessByIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetBusinessByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessByIdsReq.Merge(dst, src)
}
func (m *GetBusinessByIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetBusinessByIdsReq.Size(m)
}
func (m *GetBusinessByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessByIdsReq proto.InternalMessageInfo

func (m *GetBusinessByIdsReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type GetBusinessByIdsResp struct {
	BusinessList         []*BusinessConf `protobuf:"bytes,1,rep,name=business_list,json=businessList,proto3" json:"business_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetBusinessByIdsResp) Reset()         { *m = GetBusinessByIdsResp{} }
func (m *GetBusinessByIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetBusinessByIdsResp) ProtoMessage()    {}
func (*GetBusinessByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{6}
}
func (m *GetBusinessByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessByIdsResp.Unmarshal(m, b)
}
func (m *GetBusinessByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessByIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetBusinessByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessByIdsResp.Merge(dst, src)
}
func (m *GetBusinessByIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetBusinessByIdsResp.Size(m)
}
func (m *GetBusinessByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessByIdsResp proto.InternalMessageInfo

func (m *GetBusinessByIdsResp) GetBusinessList() []*BusinessConf {
	if m != nil {
		return m.BusinessList
	}
	return nil
}

type BusinessRiskControlConf struct {
	BusinessId              uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	BgId                    uint32   `protobuf:"varint,2,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	HourCntLimit            uint64   `protobuf:"varint,3,opt,name=hour_cnt_limit,json=hourCntLimit,proto3" json:"hour_cnt_limit,omitempty"`
	DayCntLimit             uint64   `protobuf:"varint,4,opt,name=day_cnt_limit,json=dayCntLimit,proto3" json:"day_cnt_limit,omitempty"`
	SingleCntLimit          uint32   `protobuf:"varint,5,opt,name=single_cnt_limit,json=singleCntLimit,proto3" json:"single_cnt_limit,omitempty"`
	SingleValueLimit        uint32   `protobuf:"varint,6,opt,name=single_value_limit,json=singleValueLimit,proto3" json:"single_value_limit,omitempty"`
	HourTbeanValueLimit     uint64   `protobuf:"varint,7,opt,name=hour_tbean_value_limit,json=hourTbeanValueLimit,proto3" json:"hour_tbean_value_limit,omitempty"`
	DayTbeanValueLimit      uint64   `protobuf:"varint,8,opt,name=day_tbean_value_limit,json=dayTbeanValueLimit,proto3" json:"day_tbean_value_limit,omitempty"`
	OperaUser               string   `protobuf:"bytes,9,opt,name=opera_user,json=operaUser,proto3" json:"opera_user,omitempty"`
	Id                      uint32   `protobuf:"varint,10,opt,name=id,proto3" json:"id,omitempty"`
	UsedHourTbeanValue      uint64   `protobuf:"varint,11,opt,name=used_hour_tbean_value,json=usedHourTbeanValue,proto3" json:"used_hour_tbean_value,omitempty"`
	UsedDayTbeanValue       uint64   `protobuf:"varint,12,opt,name=used_day_tbean_value,json=usedDayTbeanValue,proto3" json:"used_day_tbean_value,omitempty"`
	UsedHourCnt             uint64   `protobuf:"varint,13,opt,name=used_hour_cnt,json=usedHourCnt,proto3" json:"used_hour_cnt,omitempty"`
	UsedDayCnt              uint64   `protobuf:"varint,14,opt,name=used_day_cnt,json=usedDayCnt,proto3" json:"used_day_cnt,omitempty"`
	RestHourTbeanValue      int64    `protobuf:"varint,15,opt,name=rest_hour_tbean_value,json=restHourTbeanValue,proto3" json:"rest_hour_tbean_value,omitempty"`
	RestDayTbeanValue       int64    `protobuf:"varint,16,opt,name=rest_day_tbean_value,json=restDayTbeanValue,proto3" json:"rest_day_tbean_value,omitempty"`
	RestHourCnt             int64    `protobuf:"varint,17,opt,name=rest_hour_cnt,json=restHourCnt,proto3" json:"rest_hour_cnt,omitempty"`
	RestDayCnt              int64    `protobuf:"varint,18,opt,name=rest_day_cnt,json=restDayCnt,proto3" json:"rest_day_cnt,omitempty"`
	OperEmail               string   `protobuf:"bytes,19,opt,name=oper_email,json=operEmail,proto3" json:"oper_email,omitempty"`
	UserHourCntLimit        uint64   `protobuf:"varint,20,opt,name=user_hour_cnt_limit,json=userHourCntLimit,proto3" json:"user_hour_cnt_limit,omitempty"`
	UserDayCntLimit         uint64   `protobuf:"varint,21,opt,name=user_day_cnt_limit,json=userDayCntLimit,proto3" json:"user_day_cnt_limit,omitempty"`
	UserHourTbeanValueLimit uint64   `protobuf:"varint,22,opt,name=user_hour_tbean_value_limit,json=userHourTbeanValueLimit,proto3" json:"user_hour_tbean_value_limit,omitempty"`
	UserDayTbeanValueLimit  uint64   `protobuf:"varint,23,opt,name=user_day_tbean_value_limit,json=userDayTbeanValueLimit,proto3" json:"user_day_tbean_value_limit,omitempty"`
	IsCurAllBg              bool     `protobuf:"varint,24,opt,name=is_cur_all_bg,json=isCurAllBg,proto3" json:"is_cur_all_bg,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *BusinessRiskControlConf) Reset()         { *m = BusinessRiskControlConf{} }
func (m *BusinessRiskControlConf) String() string { return proto.CompactTextString(m) }
func (*BusinessRiskControlConf) ProtoMessage()    {}
func (*BusinessRiskControlConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{7}
}
func (m *BusinessRiskControlConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BusinessRiskControlConf.Unmarshal(m, b)
}
func (m *BusinessRiskControlConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BusinessRiskControlConf.Marshal(b, m, deterministic)
}
func (dst *BusinessRiskControlConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BusinessRiskControlConf.Merge(dst, src)
}
func (m *BusinessRiskControlConf) XXX_Size() int {
	return xxx_messageInfo_BusinessRiskControlConf.Size(m)
}
func (m *BusinessRiskControlConf) XXX_DiscardUnknown() {
	xxx_messageInfo_BusinessRiskControlConf.DiscardUnknown(m)
}

var xxx_messageInfo_BusinessRiskControlConf proto.InternalMessageInfo

func (m *BusinessRiskControlConf) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *BusinessRiskControlConf) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *BusinessRiskControlConf) GetHourCntLimit() uint64 {
	if m != nil {
		return m.HourCntLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetDayCntLimit() uint64 {
	if m != nil {
		return m.DayCntLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetSingleCntLimit() uint32 {
	if m != nil {
		return m.SingleCntLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetSingleValueLimit() uint32 {
	if m != nil {
		return m.SingleValueLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetHourTbeanValueLimit() uint64 {
	if m != nil {
		return m.HourTbeanValueLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetDayTbeanValueLimit() uint64 {
	if m != nil {
		return m.DayTbeanValueLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetOperaUser() string {
	if m != nil {
		return m.OperaUser
	}
	return ""
}

func (m *BusinessRiskControlConf) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BusinessRiskControlConf) GetUsedHourTbeanValue() uint64 {
	if m != nil {
		return m.UsedHourTbeanValue
	}
	return 0
}

func (m *BusinessRiskControlConf) GetUsedDayTbeanValue() uint64 {
	if m != nil {
		return m.UsedDayTbeanValue
	}
	return 0
}

func (m *BusinessRiskControlConf) GetUsedHourCnt() uint64 {
	if m != nil {
		return m.UsedHourCnt
	}
	return 0
}

func (m *BusinessRiskControlConf) GetUsedDayCnt() uint64 {
	if m != nil {
		return m.UsedDayCnt
	}
	return 0
}

func (m *BusinessRiskControlConf) GetRestHourTbeanValue() int64 {
	if m != nil {
		return m.RestHourTbeanValue
	}
	return 0
}

func (m *BusinessRiskControlConf) GetRestDayTbeanValue() int64 {
	if m != nil {
		return m.RestDayTbeanValue
	}
	return 0
}

func (m *BusinessRiskControlConf) GetRestHourCnt() int64 {
	if m != nil {
		return m.RestHourCnt
	}
	return 0
}

func (m *BusinessRiskControlConf) GetRestDayCnt() int64 {
	if m != nil {
		return m.RestDayCnt
	}
	return 0
}

func (m *BusinessRiskControlConf) GetOperEmail() string {
	if m != nil {
		return m.OperEmail
	}
	return ""
}

func (m *BusinessRiskControlConf) GetUserHourCntLimit() uint64 {
	if m != nil {
		return m.UserHourCntLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetUserDayCntLimit() uint64 {
	if m != nil {
		return m.UserDayCntLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetUserHourTbeanValueLimit() uint64 {
	if m != nil {
		return m.UserHourTbeanValueLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetUserDayTbeanValueLimit() uint64 {
	if m != nil {
		return m.UserDayTbeanValueLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetIsCurAllBg() bool {
	if m != nil {
		return m.IsCurAllBg
	}
	return false
}

// 添加业务风控配置
type AddBusinessRiskControlConfReq struct {
	BusinessRiskConf     *BusinessRiskControlConf `protobuf:"bytes,1,opt,name=business_risk_conf,json=businessRiskConf,proto3" json:"business_risk_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AddBusinessRiskControlConfReq) Reset()         { *m = AddBusinessRiskControlConfReq{} }
func (m *AddBusinessRiskControlConfReq) String() string { return proto.CompactTextString(m) }
func (*AddBusinessRiskControlConfReq) ProtoMessage()    {}
func (*AddBusinessRiskControlConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{8}
}
func (m *AddBusinessRiskControlConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBusinessRiskControlConfReq.Unmarshal(m, b)
}
func (m *AddBusinessRiskControlConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBusinessRiskControlConfReq.Marshal(b, m, deterministic)
}
func (dst *AddBusinessRiskControlConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBusinessRiskControlConfReq.Merge(dst, src)
}
func (m *AddBusinessRiskControlConfReq) XXX_Size() int {
	return xxx_messageInfo_AddBusinessRiskControlConfReq.Size(m)
}
func (m *AddBusinessRiskControlConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBusinessRiskControlConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddBusinessRiskControlConfReq proto.InternalMessageInfo

func (m *AddBusinessRiskControlConfReq) GetBusinessRiskConf() *BusinessRiskControlConf {
	if m != nil {
		return m.BusinessRiskConf
	}
	return nil
}

type AddBusinessRiskControlConfResp struct {
	BusinessRiskConf     *BusinessRiskControlConf `protobuf:"bytes,1,opt,name=business_risk_conf,json=businessRiskConf,proto3" json:"business_risk_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AddBusinessRiskControlConfResp) Reset()         { *m = AddBusinessRiskControlConfResp{} }
func (m *AddBusinessRiskControlConfResp) String() string { return proto.CompactTextString(m) }
func (*AddBusinessRiskControlConfResp) ProtoMessage()    {}
func (*AddBusinessRiskControlConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{9}
}
func (m *AddBusinessRiskControlConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBusinessRiskControlConfResp.Unmarshal(m, b)
}
func (m *AddBusinessRiskControlConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBusinessRiskControlConfResp.Marshal(b, m, deterministic)
}
func (dst *AddBusinessRiskControlConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBusinessRiskControlConfResp.Merge(dst, src)
}
func (m *AddBusinessRiskControlConfResp) XXX_Size() int {
	return xxx_messageInfo_AddBusinessRiskControlConfResp.Size(m)
}
func (m *AddBusinessRiskControlConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBusinessRiskControlConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddBusinessRiskControlConfResp proto.InternalMessageInfo

func (m *AddBusinessRiskControlConfResp) GetBusinessRiskConf() *BusinessRiskControlConf {
	if m != nil {
		return m.BusinessRiskConf
	}
	return nil
}

// 批量修改或增加风控配置
type BatchAddOrModBusinessRiskControlConfReq struct {
	AddRiskList          []*BusinessRiskControlConf `protobuf:"bytes,1,rep,name=add_risk_list,json=addRiskList,proto3" json:"add_risk_list,omitempty"`
	ModRiskList          []*BusinessRiskControlConf `protobuf:"bytes,2,rep,name=mod_risk_list,json=modRiskList,proto3" json:"mod_risk_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *BatchAddOrModBusinessRiskControlConfReq) Reset() {
	*m = BatchAddOrModBusinessRiskControlConfReq{}
}
func (m *BatchAddOrModBusinessRiskControlConfReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddOrModBusinessRiskControlConfReq) ProtoMessage()    {}
func (*BatchAddOrModBusinessRiskControlConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{10}
}
func (m *BatchAddOrModBusinessRiskControlConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddOrModBusinessRiskControlConfReq.Unmarshal(m, b)
}
func (m *BatchAddOrModBusinessRiskControlConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddOrModBusinessRiskControlConfReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddOrModBusinessRiskControlConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddOrModBusinessRiskControlConfReq.Merge(dst, src)
}
func (m *BatchAddOrModBusinessRiskControlConfReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddOrModBusinessRiskControlConfReq.Size(m)
}
func (m *BatchAddOrModBusinessRiskControlConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddOrModBusinessRiskControlConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddOrModBusinessRiskControlConfReq proto.InternalMessageInfo

func (m *BatchAddOrModBusinessRiskControlConfReq) GetAddRiskList() []*BusinessRiskControlConf {
	if m != nil {
		return m.AddRiskList
	}
	return nil
}

func (m *BatchAddOrModBusinessRiskControlConfReq) GetModRiskList() []*BusinessRiskControlConf {
	if m != nil {
		return m.ModRiskList
	}
	return nil
}

type BatchAddOrModBusinessRiskControlConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddOrModBusinessRiskControlConfResp) Reset() {
	*m = BatchAddOrModBusinessRiskControlConfResp{}
}
func (m *BatchAddOrModBusinessRiskControlConfResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddOrModBusinessRiskControlConfResp) ProtoMessage()    {}
func (*BatchAddOrModBusinessRiskControlConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{11}
}
func (m *BatchAddOrModBusinessRiskControlConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddOrModBusinessRiskControlConfResp.Unmarshal(m, b)
}
func (m *BatchAddOrModBusinessRiskControlConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddOrModBusinessRiskControlConfResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddOrModBusinessRiskControlConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddOrModBusinessRiskControlConfResp.Merge(dst, src)
}
func (m *BatchAddOrModBusinessRiskControlConfResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddOrModBusinessRiskControlConfResp.Size(m)
}
func (m *BatchAddOrModBusinessRiskControlConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddOrModBusinessRiskControlConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddOrModBusinessRiskControlConfResp proto.InternalMessageInfo

type ModBusinessRiskControlConfReq struct {
	BusinessRiskConf     *BusinessRiskControlConf `protobuf:"bytes,1,opt,name=business_risk_conf,json=businessRiskConf,proto3" json:"business_risk_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ModBusinessRiskControlConfReq) Reset()         { *m = ModBusinessRiskControlConfReq{} }
func (m *ModBusinessRiskControlConfReq) String() string { return proto.CompactTextString(m) }
func (*ModBusinessRiskControlConfReq) ProtoMessage()    {}
func (*ModBusinessRiskControlConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{12}
}
func (m *ModBusinessRiskControlConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModBusinessRiskControlConfReq.Unmarshal(m, b)
}
func (m *ModBusinessRiskControlConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModBusinessRiskControlConfReq.Marshal(b, m, deterministic)
}
func (dst *ModBusinessRiskControlConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModBusinessRiskControlConfReq.Merge(dst, src)
}
func (m *ModBusinessRiskControlConfReq) XXX_Size() int {
	return xxx_messageInfo_ModBusinessRiskControlConfReq.Size(m)
}
func (m *ModBusinessRiskControlConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModBusinessRiskControlConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModBusinessRiskControlConfReq proto.InternalMessageInfo

func (m *ModBusinessRiskControlConfReq) GetBusinessRiskConf() *BusinessRiskControlConf {
	if m != nil {
		return m.BusinessRiskConf
	}
	return nil
}

type ModBusinessRiskControlConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModBusinessRiskControlConfResp) Reset()         { *m = ModBusinessRiskControlConfResp{} }
func (m *ModBusinessRiskControlConfResp) String() string { return proto.CompactTextString(m) }
func (*ModBusinessRiskControlConfResp) ProtoMessage()    {}
func (*ModBusinessRiskControlConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{13}
}
func (m *ModBusinessRiskControlConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModBusinessRiskControlConfResp.Unmarshal(m, b)
}
func (m *ModBusinessRiskControlConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModBusinessRiskControlConfResp.Marshal(b, m, deterministic)
}
func (dst *ModBusinessRiskControlConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModBusinessRiskControlConfResp.Merge(dst, src)
}
func (m *ModBusinessRiskControlConfResp) XXX_Size() int {
	return xxx_messageInfo_ModBusinessRiskControlConfResp.Size(m)
}
func (m *ModBusinessRiskControlConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModBusinessRiskControlConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModBusinessRiskControlConfResp proto.InternalMessageInfo

type GetBusinessRiskControlConfReq struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	BusinessIdList       []uint32 `protobuf:"varint,2,rep,packed,name=business_id_list,json=businessIdList,proto3" json:"business_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBusinessRiskControlConfReq) Reset()         { *m = GetBusinessRiskControlConfReq{} }
func (m *GetBusinessRiskControlConfReq) String() string { return proto.CompactTextString(m) }
func (*GetBusinessRiskControlConfReq) ProtoMessage()    {}
func (*GetBusinessRiskControlConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{14}
}
func (m *GetBusinessRiskControlConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessRiskControlConfReq.Unmarshal(m, b)
}
func (m *GetBusinessRiskControlConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessRiskControlConfReq.Marshal(b, m, deterministic)
}
func (dst *GetBusinessRiskControlConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessRiskControlConfReq.Merge(dst, src)
}
func (m *GetBusinessRiskControlConfReq) XXX_Size() int {
	return xxx_messageInfo_GetBusinessRiskControlConfReq.Size(m)
}
func (m *GetBusinessRiskControlConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessRiskControlConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessRiskControlConfReq proto.InternalMessageInfo

func (m *GetBusinessRiskControlConfReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *GetBusinessRiskControlConfReq) GetBusinessIdList() []uint32 {
	if m != nil {
		return m.BusinessIdList
	}
	return nil
}

type BusinessRiskControlConfList struct {
	BusinessRiskConfList []*BusinessRiskControlConf `protobuf:"bytes,1,rep,name=business_risk_conf_list,json=businessRiskConfList,proto3" json:"business_risk_conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *BusinessRiskControlConfList) Reset()         { *m = BusinessRiskControlConfList{} }
func (m *BusinessRiskControlConfList) String() string { return proto.CompactTextString(m) }
func (*BusinessRiskControlConfList) ProtoMessage()    {}
func (*BusinessRiskControlConfList) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{15}
}
func (m *BusinessRiskControlConfList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BusinessRiskControlConfList.Unmarshal(m, b)
}
func (m *BusinessRiskControlConfList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BusinessRiskControlConfList.Marshal(b, m, deterministic)
}
func (dst *BusinessRiskControlConfList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BusinessRiskControlConfList.Merge(dst, src)
}
func (m *BusinessRiskControlConfList) XXX_Size() int {
	return xxx_messageInfo_BusinessRiskControlConfList.Size(m)
}
func (m *BusinessRiskControlConfList) XXX_DiscardUnknown() {
	xxx_messageInfo_BusinessRiskControlConfList.DiscardUnknown(m)
}

var xxx_messageInfo_BusinessRiskControlConfList proto.InternalMessageInfo

func (m *BusinessRiskControlConfList) GetBusinessRiskConfList() []*BusinessRiskControlConf {
	if m != nil {
		return m.BusinessRiskConfList
	}
	return nil
}

type GetBusinessRiskControlConfResp struct {
	BusinessRiskConfList   []*BusinessRiskControlConf              `protobuf:"bytes,1,rep,name=business_risk_conf_list,json=businessRiskConfList,proto3" json:"business_risk_conf_list,omitempty"`
	Business_2RiskConfList map[uint32]*BusinessRiskControlConfList `protobuf:"bytes,2,rep,name=business_2_risk_conf_list,json=business2RiskConfList,proto3" json:"business_2_risk_conf_list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Business_2Risk         uint32                                  `protobuf:"varint,3,opt,name=business_2_risk,json=business2Risk,proto3" json:"business_2_risk,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                                `json:"-"`
	XXX_unrecognized       []byte                                  `json:"-"`
	XXX_sizecache          int32                                   `json:"-"`
}

func (m *GetBusinessRiskControlConfResp) Reset()         { *m = GetBusinessRiskControlConfResp{} }
func (m *GetBusinessRiskControlConfResp) String() string { return proto.CompactTextString(m) }
func (*GetBusinessRiskControlConfResp) ProtoMessage()    {}
func (*GetBusinessRiskControlConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{16}
}
func (m *GetBusinessRiskControlConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessRiskControlConfResp.Unmarshal(m, b)
}
func (m *GetBusinessRiskControlConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessRiskControlConfResp.Marshal(b, m, deterministic)
}
func (dst *GetBusinessRiskControlConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessRiskControlConfResp.Merge(dst, src)
}
func (m *GetBusinessRiskControlConfResp) XXX_Size() int {
	return xxx_messageInfo_GetBusinessRiskControlConfResp.Size(m)
}
func (m *GetBusinessRiskControlConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessRiskControlConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessRiskControlConfResp proto.InternalMessageInfo

func (m *GetBusinessRiskControlConfResp) GetBusinessRiskConfList() []*BusinessRiskControlConf {
	if m != nil {
		return m.BusinessRiskConfList
	}
	return nil
}

func (m *GetBusinessRiskControlConfResp) GetBusiness_2RiskConfList() map[uint32]*BusinessRiskControlConfList {
	if m != nil {
		return m.Business_2RiskConfList
	}
	return nil
}

func (m *GetBusinessRiskControlConfResp) GetBusiness_2Risk() uint32 {
	if m != nil {
		return m.Business_2Risk
	}
	return 0
}

// 根据包裹ID获取业务风控配置
type GetBusinessRiskControlConfByBgIdReq struct {
	BgId                 uint32   `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBusinessRiskControlConfByBgIdReq) Reset()         { *m = GetBusinessRiskControlConfByBgIdReq{} }
func (m *GetBusinessRiskControlConfByBgIdReq) String() string { return proto.CompactTextString(m) }
func (*GetBusinessRiskControlConfByBgIdReq) ProtoMessage()    {}
func (*GetBusinessRiskControlConfByBgIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{17}
}
func (m *GetBusinessRiskControlConfByBgIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessRiskControlConfByBgIdReq.Unmarshal(m, b)
}
func (m *GetBusinessRiskControlConfByBgIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessRiskControlConfByBgIdReq.Marshal(b, m, deterministic)
}
func (dst *GetBusinessRiskControlConfByBgIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessRiskControlConfByBgIdReq.Merge(dst, src)
}
func (m *GetBusinessRiskControlConfByBgIdReq) XXX_Size() int {
	return xxx_messageInfo_GetBusinessRiskControlConfByBgIdReq.Size(m)
}
func (m *GetBusinessRiskControlConfByBgIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessRiskControlConfByBgIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessRiskControlConfByBgIdReq proto.InternalMessageInfo

func (m *GetBusinessRiskControlConfByBgIdReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

type GetBusinessRiskControlConfByBgIdResp struct {
	BusinessRiskConfList []*BusinessRiskControlConf `protobuf:"bytes,1,rep,name=business_risk_conf_list,json=businessRiskConfList,proto3" json:"business_risk_conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetBusinessRiskControlConfByBgIdResp) Reset()         { *m = GetBusinessRiskControlConfByBgIdResp{} }
func (m *GetBusinessRiskControlConfByBgIdResp) String() string { return proto.CompactTextString(m) }
func (*GetBusinessRiskControlConfByBgIdResp) ProtoMessage()    {}
func (*GetBusinessRiskControlConfByBgIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{18}
}
func (m *GetBusinessRiskControlConfByBgIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessRiskControlConfByBgIdResp.Unmarshal(m, b)
}
func (m *GetBusinessRiskControlConfByBgIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessRiskControlConfByBgIdResp.Marshal(b, m, deterministic)
}
func (dst *GetBusinessRiskControlConfByBgIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessRiskControlConfByBgIdResp.Merge(dst, src)
}
func (m *GetBusinessRiskControlConfByBgIdResp) XXX_Size() int {
	return xxx_messageInfo_GetBusinessRiskControlConfByBgIdResp.Size(m)
}
func (m *GetBusinessRiskControlConfByBgIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessRiskControlConfByBgIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessRiskControlConfByBgIdResp proto.InternalMessageInfo

func (m *GetBusinessRiskControlConfByBgIdResp) GetBusinessRiskConfList() []*BusinessRiskControlConf {
	if m != nil {
		return m.BusinessRiskConfList
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type SendBackpackOrderInfo struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	BackpackId           uint32   `protobuf:"varint,2,opt,name=backpack_id,json=backpackId,proto3" json:"backpack_id,omitempty"`
	ReceiveUid           uint32   `protobuf:"varint,3,opt,name=receive_uid,json=receiveUid,proto3" json:"receive_uid,omitempty"`
	BackpackCnt          uint32   `protobuf:"varint,4,opt,name=backpack_cnt,json=backpackCnt,proto3" json:"backpack_cnt,omitempty"`
	SourceId             uint32   `protobuf:"varint,5,opt,name=sourceId,proto3" json:"sourceId,omitempty"`
	OutsideTime          int64    `protobuf:"varint,6,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	ServerTime           int64    `protobuf:"varint,7,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	OrderId              string   `protobuf:"bytes,8,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Ciphertext           []byte   `protobuf:"bytes,9,opt,name=ciphertext,proto3" json:"ciphertext,omitempty"`
	ExpireDuration       uint32   `protobuf:"varint,10,opt,name=expire_duration,json=expireDuration,proto3" json:"expire_duration,omitempty"`
	SourceAppId          string   `protobuf:"bytes,11,opt,name=source_app_id,json=sourceAppId,proto3" json:"source_app_id,omitempty"`
	TrafficMark          string   `protobuf:"bytes,12,opt,name=traffic_mark,json=trafficMark,proto3" json:"traffic_mark,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendBackpackOrderInfo) Reset()         { *m = SendBackpackOrderInfo{} }
func (m *SendBackpackOrderInfo) String() string { return proto.CompactTextString(m) }
func (*SendBackpackOrderInfo) ProtoMessage()    {}
func (*SendBackpackOrderInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{19}
}
func (m *SendBackpackOrderInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendBackpackOrderInfo.Unmarshal(m, b)
}
func (m *SendBackpackOrderInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendBackpackOrderInfo.Marshal(b, m, deterministic)
}
func (dst *SendBackpackOrderInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendBackpackOrderInfo.Merge(dst, src)
}
func (m *SendBackpackOrderInfo) XXX_Size() int {
	return xxx_messageInfo_SendBackpackOrderInfo.Size(m)
}
func (m *SendBackpackOrderInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SendBackpackOrderInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SendBackpackOrderInfo proto.InternalMessageInfo

func (m *SendBackpackOrderInfo) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *SendBackpackOrderInfo) GetBackpackId() uint32 {
	if m != nil {
		return m.BackpackId
	}
	return 0
}

func (m *SendBackpackOrderInfo) GetReceiveUid() uint32 {
	if m != nil {
		return m.ReceiveUid
	}
	return 0
}

func (m *SendBackpackOrderInfo) GetBackpackCnt() uint32 {
	if m != nil {
		return m.BackpackCnt
	}
	return 0
}

func (m *SendBackpackOrderInfo) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *SendBackpackOrderInfo) GetOutsideTime() int64 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

func (m *SendBackpackOrderInfo) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *SendBackpackOrderInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *SendBackpackOrderInfo) GetCiphertext() []byte {
	if m != nil {
		return m.Ciphertext
	}
	return nil
}

func (m *SendBackpackOrderInfo) GetExpireDuration() uint32 {
	if m != nil {
		return m.ExpireDuration
	}
	return 0
}

func (m *SendBackpackOrderInfo) GetSourceAppId() string {
	if m != nil {
		return m.SourceAppId
	}
	return ""
}

func (m *SendBackpackOrderInfo) GetTrafficMark() string {
	if m != nil {
		return m.TrafficMark
	}
	return ""
}

type SendBackpackWithRiskControlReq struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	BackpackId           uint32   `protobuf:"varint,2,opt,name=backpack_id,json=backpackId,proto3" json:"backpack_id,omitempty"`
	ReceiveUid           uint32   `protobuf:"varint,3,opt,name=receive_uid,json=receiveUid,proto3" json:"receive_uid,omitempty"`
	BackpackCnt          uint32   `protobuf:"varint,4,opt,name=backpack_cnt,json=backpackCnt,proto3" json:"backpack_cnt,omitempty"`
	ServerTime           int64    `protobuf:"varint,5,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	OrderId              string   `protobuf:"bytes,6,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Ciphertext           []byte   `protobuf:"bytes,7,opt,name=ciphertext,proto3" json:"ciphertext,omitempty"`
	ExpireDuration       uint32   `protobuf:"varint,8,opt,name=expire_duration,json=expireDuration,proto3" json:"expire_duration,omitempty"`
	SourceAppId          string   `protobuf:"bytes,9,opt,name=source_app_id,json=sourceAppId,proto3" json:"source_app_id,omitempty"`
	DealToken            string   `protobuf:"bytes,10,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendBackpackWithRiskControlReq) Reset()         { *m = SendBackpackWithRiskControlReq{} }
func (m *SendBackpackWithRiskControlReq) String() string { return proto.CompactTextString(m) }
func (*SendBackpackWithRiskControlReq) ProtoMessage()    {}
func (*SendBackpackWithRiskControlReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{20}
}
func (m *SendBackpackWithRiskControlReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendBackpackWithRiskControlReq.Unmarshal(m, b)
}
func (m *SendBackpackWithRiskControlReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendBackpackWithRiskControlReq.Marshal(b, m, deterministic)
}
func (dst *SendBackpackWithRiskControlReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendBackpackWithRiskControlReq.Merge(dst, src)
}
func (m *SendBackpackWithRiskControlReq) XXX_Size() int {
	return xxx_messageInfo_SendBackpackWithRiskControlReq.Size(m)
}
func (m *SendBackpackWithRiskControlReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendBackpackWithRiskControlReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendBackpackWithRiskControlReq proto.InternalMessageInfo

func (m *SendBackpackWithRiskControlReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *SendBackpackWithRiskControlReq) GetBackpackId() uint32 {
	if m != nil {
		return m.BackpackId
	}
	return 0
}

func (m *SendBackpackWithRiskControlReq) GetReceiveUid() uint32 {
	if m != nil {
		return m.ReceiveUid
	}
	return 0
}

func (m *SendBackpackWithRiskControlReq) GetBackpackCnt() uint32 {
	if m != nil {
		return m.BackpackCnt
	}
	return 0
}

func (m *SendBackpackWithRiskControlReq) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *SendBackpackWithRiskControlReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *SendBackpackWithRiskControlReq) GetCiphertext() []byte {
	if m != nil {
		return m.Ciphertext
	}
	return nil
}

func (m *SendBackpackWithRiskControlReq) GetExpireDuration() uint32 {
	if m != nil {
		return m.ExpireDuration
	}
	return 0
}

func (m *SendBackpackWithRiskControlReq) GetSourceAppId() string {
	if m != nil {
		return m.SourceAppId
	}
	return ""
}

func (m *SendBackpackWithRiskControlReq) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

type SendBackpackWithRiskControlResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendBackpackWithRiskControlResp) Reset()         { *m = SendBackpackWithRiskControlResp{} }
func (m *SendBackpackWithRiskControlResp) String() string { return proto.CompactTextString(m) }
func (*SendBackpackWithRiskControlResp) ProtoMessage()    {}
func (*SendBackpackWithRiskControlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{21}
}
func (m *SendBackpackWithRiskControlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendBackpackWithRiskControlResp.Unmarshal(m, b)
}
func (m *SendBackpackWithRiskControlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendBackpackWithRiskControlResp.Marshal(b, m, deterministic)
}
func (dst *SendBackpackWithRiskControlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendBackpackWithRiskControlResp.Merge(dst, src)
}
func (m *SendBackpackWithRiskControlResp) XXX_Size() int {
	return xxx_messageInfo_SendBackpackWithRiskControlResp.Size(m)
}
func (m *SendBackpackWithRiskControlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendBackpackWithRiskControlResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendBackpackWithRiskControlResp proto.InternalMessageInfo

type SendOneBackpackWithRiskControlReq struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	BackpackId           uint32   `protobuf:"varint,2,opt,name=backpack_id,json=backpackId,proto3" json:"backpack_id,omitempty"`
	ReceiveUid           uint32   `protobuf:"varint,3,opt,name=receive_uid,json=receiveUid,proto3" json:"receive_uid,omitempty"`
	ServerTime           int64    `protobuf:"varint,4,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	OrderId              string   `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Ciphertext           []byte   `protobuf:"bytes,6,opt,name=ciphertext,proto3" json:"ciphertext,omitempty"`
	ExpireDuration       uint32   `protobuf:"varint,7,opt,name=expire_duration,json=expireDuration,proto3" json:"expire_duration,omitempty"`
	SourceAppId          string   `protobuf:"bytes,8,opt,name=source_app_id,json=sourceAppId,proto3" json:"source_app_id,omitempty"`
	DealToken            string   `protobuf:"bytes,9,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendOneBackpackWithRiskControlReq) Reset()         { *m = SendOneBackpackWithRiskControlReq{} }
func (m *SendOneBackpackWithRiskControlReq) String() string { return proto.CompactTextString(m) }
func (*SendOneBackpackWithRiskControlReq) ProtoMessage()    {}
func (*SendOneBackpackWithRiskControlReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{22}
}
func (m *SendOneBackpackWithRiskControlReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendOneBackpackWithRiskControlReq.Unmarshal(m, b)
}
func (m *SendOneBackpackWithRiskControlReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendOneBackpackWithRiskControlReq.Marshal(b, m, deterministic)
}
func (dst *SendOneBackpackWithRiskControlReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendOneBackpackWithRiskControlReq.Merge(dst, src)
}
func (m *SendOneBackpackWithRiskControlReq) XXX_Size() int {
	return xxx_messageInfo_SendOneBackpackWithRiskControlReq.Size(m)
}
func (m *SendOneBackpackWithRiskControlReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendOneBackpackWithRiskControlReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendOneBackpackWithRiskControlReq proto.InternalMessageInfo

func (m *SendOneBackpackWithRiskControlReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *SendOneBackpackWithRiskControlReq) GetBackpackId() uint32 {
	if m != nil {
		return m.BackpackId
	}
	return 0
}

func (m *SendOneBackpackWithRiskControlReq) GetReceiveUid() uint32 {
	if m != nil {
		return m.ReceiveUid
	}
	return 0
}

func (m *SendOneBackpackWithRiskControlReq) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *SendOneBackpackWithRiskControlReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *SendOneBackpackWithRiskControlReq) GetCiphertext() []byte {
	if m != nil {
		return m.Ciphertext
	}
	return nil
}

func (m *SendOneBackpackWithRiskControlReq) GetExpireDuration() uint32 {
	if m != nil {
		return m.ExpireDuration
	}
	return 0
}

func (m *SendOneBackpackWithRiskControlReq) GetSourceAppId() string {
	if m != nil {
		return m.SourceAppId
	}
	return ""
}

func (m *SendOneBackpackWithRiskControlReq) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

type SendOneBackpackWithRiskControlResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendOneBackpackWithRiskControlResp) Reset()         { *m = SendOneBackpackWithRiskControlResp{} }
func (m *SendOneBackpackWithRiskControlResp) String() string { return proto.CompactTextString(m) }
func (*SendOneBackpackWithRiskControlResp) ProtoMessage()    {}
func (*SendOneBackpackWithRiskControlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{23}
}
func (m *SendOneBackpackWithRiskControlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendOneBackpackWithRiskControlResp.Unmarshal(m, b)
}
func (m *SendOneBackpackWithRiskControlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendOneBackpackWithRiskControlResp.Marshal(b, m, deterministic)
}
func (dst *SendOneBackpackWithRiskControlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendOneBackpackWithRiskControlResp.Merge(dst, src)
}
func (m *SendOneBackpackWithRiskControlResp) XXX_Size() int {
	return xxx_messageInfo_SendOneBackpackWithRiskControlResp.Size(m)
}
func (m *SendOneBackpackWithRiskControlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendOneBackpackWithRiskControlResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendOneBackpackWithRiskControlResp proto.InternalMessageInfo

type DeductUserBackpackWithRiskControlReq struct {
	BusinessId           uint32          `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	ServerTime           int64           `protobuf:"varint,2,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	OrderId              string          `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Ciphertext           []byte          `protobuf:"bytes,4,opt,name=ciphertext,proto3" json:"ciphertext,omitempty"`
	Oper                 string          `protobuf:"bytes,5,opt,name=oper,proto3" json:"oper,omitempty"`
	DeductType           uint32          `protobuf:"varint,6,opt,name=deduct_type,json=deductType,proto3" json:"deduct_type,omitempty"`
	DeductList           []*DeductDetail `protobuf:"bytes,7,rep,name=deduct_list,json=deductList,proto3" json:"deduct_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *DeductUserBackpackWithRiskControlReq) Reset()         { *m = DeductUserBackpackWithRiskControlReq{} }
func (m *DeductUserBackpackWithRiskControlReq) String() string { return proto.CompactTextString(m) }
func (*DeductUserBackpackWithRiskControlReq) ProtoMessage()    {}
func (*DeductUserBackpackWithRiskControlReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{24}
}
func (m *DeductUserBackpackWithRiskControlReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductUserBackpackWithRiskControlReq.Unmarshal(m, b)
}
func (m *DeductUserBackpackWithRiskControlReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductUserBackpackWithRiskControlReq.Marshal(b, m, deterministic)
}
func (dst *DeductUserBackpackWithRiskControlReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductUserBackpackWithRiskControlReq.Merge(dst, src)
}
func (m *DeductUserBackpackWithRiskControlReq) XXX_Size() int {
	return xxx_messageInfo_DeductUserBackpackWithRiskControlReq.Size(m)
}
func (m *DeductUserBackpackWithRiskControlReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductUserBackpackWithRiskControlReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeductUserBackpackWithRiskControlReq proto.InternalMessageInfo

func (m *DeductUserBackpackWithRiskControlReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *DeductUserBackpackWithRiskControlReq) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *DeductUserBackpackWithRiskControlReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *DeductUserBackpackWithRiskControlReq) GetCiphertext() []byte {
	if m != nil {
		return m.Ciphertext
	}
	return nil
}

func (m *DeductUserBackpackWithRiskControlReq) GetOper() string {
	if m != nil {
		return m.Oper
	}
	return ""
}

func (m *DeductUserBackpackWithRiskControlReq) GetDeductType() uint32 {
	if m != nil {
		return m.DeductType
	}
	return 0
}

func (m *DeductUserBackpackWithRiskControlReq) GetDeductList() []*DeductDetail {
	if m != nil {
		return m.DeductList
	}
	return nil
}

type DeductItem struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId             uint32   `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeductItem) Reset()         { *m = DeductItem{} }
func (m *DeductItem) String() string { return proto.CompactTextString(m) }
func (*DeductItem) ProtoMessage()    {}
func (*DeductItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{25}
}
func (m *DeductItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductItem.Unmarshal(m, b)
}
func (m *DeductItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductItem.Marshal(b, m, deterministic)
}
func (dst *DeductItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductItem.Merge(dst, src)
}
func (m *DeductItem) XXX_Size() int {
	return xxx_messageInfo_DeductItem.Size(m)
}
func (m *DeductItem) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductItem.DiscardUnknown(m)
}

var xxx_messageInfo_DeductItem proto.InternalMessageInfo

func (m *DeductItem) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *DeductItem) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *DeductItem) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type DeductDetail struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemList             []*DeductItem `protobuf:"bytes,2,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	Count                uint32        `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	SourceType           uint32        `protobuf:"varint,4,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	IsAllSource          bool          `protobuf:"varint,5,opt,name=is_all_source,json=isAllSource,proto3" json:"is_all_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DeductDetail) Reset()         { *m = DeductDetail{} }
func (m *DeductDetail) String() string { return proto.CompactTextString(m) }
func (*DeductDetail) ProtoMessage()    {}
func (*DeductDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{26}
}
func (m *DeductDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductDetail.Unmarshal(m, b)
}
func (m *DeductDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductDetail.Marshal(b, m, deterministic)
}
func (dst *DeductDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductDetail.Merge(dst, src)
}
func (m *DeductDetail) XXX_Size() int {
	return xxx_messageInfo_DeductDetail.Size(m)
}
func (m *DeductDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DeductDetail proto.InternalMessageInfo

func (m *DeductDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DeductDetail) GetItemList() []*DeductItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *DeductDetail) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *DeductDetail) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *DeductDetail) GetIsAllSource() bool {
	if m != nil {
		return m.IsAllSource
	}
	return false
}

type DeductResult struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SuccessItemList      []*DeductItem `protobuf:"bytes,2,rep,name=success_item_list,json=successItemList,proto3" json:"success_item_list,omitempty"`
	FailItemList         []*DeductItem `protobuf:"bytes,3,rep,name=fail_item_list,json=failItemList,proto3" json:"fail_item_list,omitempty"`
	FailType             uint32        `protobuf:"varint,4,opt,name=fail_type,json=failType,proto3" json:"fail_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DeductResult) Reset()         { *m = DeductResult{} }
func (m *DeductResult) String() string { return proto.CompactTextString(m) }
func (*DeductResult) ProtoMessage()    {}
func (*DeductResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{27}
}
func (m *DeductResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductResult.Unmarshal(m, b)
}
func (m *DeductResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductResult.Marshal(b, m, deterministic)
}
func (dst *DeductResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductResult.Merge(dst, src)
}
func (m *DeductResult) XXX_Size() int {
	return xxx_messageInfo_DeductResult.Size(m)
}
func (m *DeductResult) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductResult.DiscardUnknown(m)
}

var xxx_messageInfo_DeductResult proto.InternalMessageInfo

func (m *DeductResult) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DeductResult) GetSuccessItemList() []*DeductItem {
	if m != nil {
		return m.SuccessItemList
	}
	return nil
}

func (m *DeductResult) GetFailItemList() []*DeductItem {
	if m != nil {
		return m.FailItemList
	}
	return nil
}

func (m *DeductResult) GetFailType() uint32 {
	if m != nil {
		return m.FailType
	}
	return 0
}

type DeductUserBackpackWithRiskControlResp struct {
	DeductList           []*DeductResult `protobuf:"bytes,1,rep,name=deduct_list,json=deductList,proto3" json:"deduct_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *DeductUserBackpackWithRiskControlResp) Reset()         { *m = DeductUserBackpackWithRiskControlResp{} }
func (m *DeductUserBackpackWithRiskControlResp) String() string { return proto.CompactTextString(m) }
func (*DeductUserBackpackWithRiskControlResp) ProtoMessage()    {}
func (*DeductUserBackpackWithRiskControlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{28}
}
func (m *DeductUserBackpackWithRiskControlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductUserBackpackWithRiskControlResp.Unmarshal(m, b)
}
func (m *DeductUserBackpackWithRiskControlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductUserBackpackWithRiskControlResp.Marshal(b, m, deterministic)
}
func (dst *DeductUserBackpackWithRiskControlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductUserBackpackWithRiskControlResp.Merge(dst, src)
}
func (m *DeductUserBackpackWithRiskControlResp) XXX_Size() int {
	return xxx_messageInfo_DeductUserBackpackWithRiskControlResp.Size(m)
}
func (m *DeductUserBackpackWithRiskControlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductUserBackpackWithRiskControlResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeductUserBackpackWithRiskControlResp proto.InternalMessageInfo

func (m *DeductUserBackpackWithRiskControlResp) GetDeductList() []*DeductResult {
	if m != nil {
		return m.DeductList
	}
	return nil
}

type SendPackDetail struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BgId                 uint32   `protobuf:"varint,2,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	BgCnt                uint32   `protobuf:"varint,3,opt,name=bg_cnt,json=bgCnt,proto3" json:"bg_cnt,omitempty"`
	ExpireDuration       uint32   `protobuf:"varint,4,opt,name=expire_duration,json=expireDuration,proto3" json:"expire_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendPackDetail) Reset()         { *m = SendPackDetail{} }
func (m *SendPackDetail) String() string { return proto.CompactTextString(m) }
func (*SendPackDetail) ProtoMessage()    {}
func (*SendPackDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{29}
}
func (m *SendPackDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPackDetail.Unmarshal(m, b)
}
func (m *SendPackDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPackDetail.Marshal(b, m, deterministic)
}
func (dst *SendPackDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPackDetail.Merge(dst, src)
}
func (m *SendPackDetail) XXX_Size() int {
	return xxx_messageInfo_SendPackDetail.Size(m)
}
func (m *SendPackDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPackDetail.DiscardUnknown(m)
}

var xxx_messageInfo_SendPackDetail proto.InternalMessageInfo

func (m *SendPackDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendPackDetail) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *SendPackDetail) GetBgCnt() uint32 {
	if m != nil {
		return m.BgCnt
	}
	return 0
}

func (m *SendPackDetail) GetExpireDuration() uint32 {
	if m != nil {
		return m.ExpireDuration
	}
	return 0
}

type BatchSendBackpackWithRiskControlReq struct {
	BusinessId           uint32            `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	OperationName        string            `protobuf:"bytes,2,opt,name=operation_name,json=operationName,proto3" json:"operation_name,omitempty"`
	ServerTime           int64             `protobuf:"varint,3,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	OrderId              string            `protobuf:"bytes,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Ciphertext           []byte            `protobuf:"bytes,5,opt,name=ciphertext,proto3" json:"ciphertext,omitempty"`
	SendPackList         []*SendPackDetail `protobuf:"bytes,6,rep,name=send_pack_list,json=sendPackList,proto3" json:"send_pack_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchSendBackpackWithRiskControlReq) Reset()         { *m = BatchSendBackpackWithRiskControlReq{} }
func (m *BatchSendBackpackWithRiskControlReq) String() string { return proto.CompactTextString(m) }
func (*BatchSendBackpackWithRiskControlReq) ProtoMessage()    {}
func (*BatchSendBackpackWithRiskControlReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{30}
}
func (m *BatchSendBackpackWithRiskControlReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSendBackpackWithRiskControlReq.Unmarshal(m, b)
}
func (m *BatchSendBackpackWithRiskControlReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSendBackpackWithRiskControlReq.Marshal(b, m, deterministic)
}
func (dst *BatchSendBackpackWithRiskControlReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSendBackpackWithRiskControlReq.Merge(dst, src)
}
func (m *BatchSendBackpackWithRiskControlReq) XXX_Size() int {
	return xxx_messageInfo_BatchSendBackpackWithRiskControlReq.Size(m)
}
func (m *BatchSendBackpackWithRiskControlReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSendBackpackWithRiskControlReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSendBackpackWithRiskControlReq proto.InternalMessageInfo

func (m *BatchSendBackpackWithRiskControlReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *BatchSendBackpackWithRiskControlReq) GetOperationName() string {
	if m != nil {
		return m.OperationName
	}
	return ""
}

func (m *BatchSendBackpackWithRiskControlReq) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *BatchSendBackpackWithRiskControlReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *BatchSendBackpackWithRiskControlReq) GetCiphertext() []byte {
	if m != nil {
		return m.Ciphertext
	}
	return nil
}

func (m *BatchSendBackpackWithRiskControlReq) GetSendPackList() []*SendPackDetail {
	if m != nil {
		return m.SendPackList
	}
	return nil
}

type BatchSendBackpackWithRiskControlResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSendBackpackWithRiskControlResp) Reset()         { *m = BatchSendBackpackWithRiskControlResp{} }
func (m *BatchSendBackpackWithRiskControlResp) String() string { return proto.CompactTextString(m) }
func (*BatchSendBackpackWithRiskControlResp) ProtoMessage()    {}
func (*BatchSendBackpackWithRiskControlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{31}
}
func (m *BatchSendBackpackWithRiskControlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSendBackpackWithRiskControlResp.Unmarshal(m, b)
}
func (m *BatchSendBackpackWithRiskControlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSendBackpackWithRiskControlResp.Marshal(b, m, deterministic)
}
func (dst *BatchSendBackpackWithRiskControlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSendBackpackWithRiskControlResp.Merge(dst, src)
}
func (m *BatchSendBackpackWithRiskControlResp) XXX_Size() int {
	return xxx_messageInfo_BatchSendBackpackWithRiskControlResp.Size(m)
}
func (m *BatchSendBackpackWithRiskControlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSendBackpackWithRiskControlResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSendBackpackWithRiskControlResp proto.InternalMessageInfo

type PackageItemCfg struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemId               uint32   `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemCnt              uint32   `protobuf:"varint,3,opt,name=item_cnt,json=itemCnt,proto3" json:"item_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PackageItemCfg) Reset()         { *m = PackageItemCfg{} }
func (m *PackageItemCfg) String() string { return proto.CompactTextString(m) }
func (*PackageItemCfg) ProtoMessage()    {}
func (*PackageItemCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{32}
}
func (m *PackageItemCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PackageItemCfg.Unmarshal(m, b)
}
func (m *PackageItemCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PackageItemCfg.Marshal(b, m, deterministic)
}
func (dst *PackageItemCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PackageItemCfg.Merge(dst, src)
}
func (m *PackageItemCfg) XXX_Size() int {
	return xxx_messageInfo_PackageItemCfg.Size(m)
}
func (m *PackageItemCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_PackageItemCfg.DiscardUnknown(m)
}

var xxx_messageInfo_PackageItemCfg proto.InternalMessageInfo

func (m *PackageItemCfg) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *PackageItemCfg) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PackageItemCfg) GetItemCnt() uint32 {
	if m != nil {
		return m.ItemCnt
	}
	return 0
}

type BackpackReceiveEvent struct {
	OrderId              string            `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Uid                  uint32            `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	BgId                 uint32            `protobuf:"varint,3,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	Cnt                  uint32            `protobuf:"varint,4,opt,name=cnt,proto3" json:"cnt,omitempty"`
	BusinessId           uint32            `protobuf:"varint,5,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	CreateTime           int64             `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	ItemList             []*PackageItemCfg `protobuf:"bytes,7,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BackpackReceiveEvent) Reset()         { *m = BackpackReceiveEvent{} }
func (m *BackpackReceiveEvent) String() string { return proto.CompactTextString(m) }
func (*BackpackReceiveEvent) ProtoMessage()    {}
func (*BackpackReceiveEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{33}
}
func (m *BackpackReceiveEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BackpackReceiveEvent.Unmarshal(m, b)
}
func (m *BackpackReceiveEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BackpackReceiveEvent.Marshal(b, m, deterministic)
}
func (dst *BackpackReceiveEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BackpackReceiveEvent.Merge(dst, src)
}
func (m *BackpackReceiveEvent) XXX_Size() int {
	return xxx_messageInfo_BackpackReceiveEvent.Size(m)
}
func (m *BackpackReceiveEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_BackpackReceiveEvent.DiscardUnknown(m)
}

var xxx_messageInfo_BackpackReceiveEvent proto.InternalMessageInfo

func (m *BackpackReceiveEvent) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *BackpackReceiveEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BackpackReceiveEvent) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *BackpackReceiveEvent) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

func (m *BackpackReceiveEvent) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *BackpackReceiveEvent) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *BackpackReceiveEvent) GetItemList() []*PackageItemCfg {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type ReSendSecretKeyEmailReq struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	IsRecreate           bool     `protobuf:"varint,2,opt,name=is_recreate,json=isRecreate,proto3" json:"is_recreate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReSendSecretKeyEmailReq) Reset()         { *m = ReSendSecretKeyEmailReq{} }
func (m *ReSendSecretKeyEmailReq) String() string { return proto.CompactTextString(m) }
func (*ReSendSecretKeyEmailReq) ProtoMessage()    {}
func (*ReSendSecretKeyEmailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{34}
}
func (m *ReSendSecretKeyEmailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReSendSecretKeyEmailReq.Unmarshal(m, b)
}
func (m *ReSendSecretKeyEmailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReSendSecretKeyEmailReq.Marshal(b, m, deterministic)
}
func (dst *ReSendSecretKeyEmailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReSendSecretKeyEmailReq.Merge(dst, src)
}
func (m *ReSendSecretKeyEmailReq) XXX_Size() int {
	return xxx_messageInfo_ReSendSecretKeyEmailReq.Size(m)
}
func (m *ReSendSecretKeyEmailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReSendSecretKeyEmailReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReSendSecretKeyEmailReq proto.InternalMessageInfo

func (m *ReSendSecretKeyEmailReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *ReSendSecretKeyEmailReq) GetIsRecreate() bool {
	if m != nil {
		return m.IsRecreate
	}
	return false
}

type ReSendSecretKeyEmailResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReSendSecretKeyEmailResp) Reset()         { *m = ReSendSecretKeyEmailResp{} }
func (m *ReSendSecretKeyEmailResp) String() string { return proto.CompactTextString(m) }
func (*ReSendSecretKeyEmailResp) ProtoMessage()    {}
func (*ReSendSecretKeyEmailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{35}
}
func (m *ReSendSecretKeyEmailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReSendSecretKeyEmailResp.Unmarshal(m, b)
}
func (m *ReSendSecretKeyEmailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReSendSecretKeyEmailResp.Marshal(b, m, deterministic)
}
func (dst *ReSendSecretKeyEmailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReSendSecretKeyEmailResp.Merge(dst, src)
}
func (m *ReSendSecretKeyEmailResp) XXX_Size() int {
	return xxx_messageInfo_ReSendSecretKeyEmailResp.Size(m)
}
func (m *ReSendSecretKeyEmailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReSendSecretKeyEmailResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReSendSecretKeyEmailResp proto.InternalMessageInfo

// 前置风控系统检查业务风控
type PreCheckBussinessRiskControlReq struct {
	BusinessId           uint32            `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	BgidCnts             map[uint32]uint32 `protobuf:"bytes,2,rep,name=bgid_cnts,json=bgidCnts,proto3" json:"bgid_cnts,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PreCheckBussinessRiskControlReq) Reset()         { *m = PreCheckBussinessRiskControlReq{} }
func (m *PreCheckBussinessRiskControlReq) String() string { return proto.CompactTextString(m) }
func (*PreCheckBussinessRiskControlReq) ProtoMessage()    {}
func (*PreCheckBussinessRiskControlReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{36}
}
func (m *PreCheckBussinessRiskControlReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreCheckBussinessRiskControlReq.Unmarshal(m, b)
}
func (m *PreCheckBussinessRiskControlReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreCheckBussinessRiskControlReq.Marshal(b, m, deterministic)
}
func (dst *PreCheckBussinessRiskControlReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreCheckBussinessRiskControlReq.Merge(dst, src)
}
func (m *PreCheckBussinessRiskControlReq) XXX_Size() int {
	return xxx_messageInfo_PreCheckBussinessRiskControlReq.Size(m)
}
func (m *PreCheckBussinessRiskControlReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PreCheckBussinessRiskControlReq.DiscardUnknown(m)
}

var xxx_messageInfo_PreCheckBussinessRiskControlReq proto.InternalMessageInfo

func (m *PreCheckBussinessRiskControlReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *PreCheckBussinessRiskControlReq) GetBgidCnts() map[uint32]uint32 {
	if m != nil {
		return m.BgidCnts
	}
	return nil
}

type PreCheckBussinessRiskControlResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreCheckBussinessRiskControlResp) Reset()         { *m = PreCheckBussinessRiskControlResp{} }
func (m *PreCheckBussinessRiskControlResp) String() string { return proto.CompactTextString(m) }
func (*PreCheckBussinessRiskControlResp) ProtoMessage()    {}
func (*PreCheckBussinessRiskControlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{37}
}
func (m *PreCheckBussinessRiskControlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreCheckBussinessRiskControlResp.Unmarshal(m, b)
}
func (m *PreCheckBussinessRiskControlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreCheckBussinessRiskControlResp.Marshal(b, m, deterministic)
}
func (dst *PreCheckBussinessRiskControlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreCheckBussinessRiskControlResp.Merge(dst, src)
}
func (m *PreCheckBussinessRiskControlResp) XXX_Size() int {
	return xxx_messageInfo_PreCheckBussinessRiskControlResp.Size(m)
}
func (m *PreCheckBussinessRiskControlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PreCheckBussinessRiskControlResp.DiscardUnknown(m)
}

var xxx_messageInfo_PreCheckBussinessRiskControlResp proto.InternalMessageInfo

type ModifyBusinessRiskWarningPercentReq struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	WarningPercent       uint32   `protobuf:"varint,2,opt,name=warning_percent,json=warningPercent,proto3" json:"warning_percent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyBusinessRiskWarningPercentReq) Reset()         { *m = ModifyBusinessRiskWarningPercentReq{} }
func (m *ModifyBusinessRiskWarningPercentReq) String() string { return proto.CompactTextString(m) }
func (*ModifyBusinessRiskWarningPercentReq) ProtoMessage()    {}
func (*ModifyBusinessRiskWarningPercentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{38}
}
func (m *ModifyBusinessRiskWarningPercentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyBusinessRiskWarningPercentReq.Unmarshal(m, b)
}
func (m *ModifyBusinessRiskWarningPercentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyBusinessRiskWarningPercentReq.Marshal(b, m, deterministic)
}
func (dst *ModifyBusinessRiskWarningPercentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyBusinessRiskWarningPercentReq.Merge(dst, src)
}
func (m *ModifyBusinessRiskWarningPercentReq) XXX_Size() int {
	return xxx_messageInfo_ModifyBusinessRiskWarningPercentReq.Size(m)
}
func (m *ModifyBusinessRiskWarningPercentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyBusinessRiskWarningPercentReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyBusinessRiskWarningPercentReq proto.InternalMessageInfo

func (m *ModifyBusinessRiskWarningPercentReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *ModifyBusinessRiskWarningPercentReq) GetWarningPercent() uint32 {
	if m != nil {
		return m.WarningPercent
	}
	return 0
}

type ModifyBusinessRiskWarningPercentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyBusinessRiskWarningPercentResp) Reset()         { *m = ModifyBusinessRiskWarningPercentResp{} }
func (m *ModifyBusinessRiskWarningPercentResp) String() string { return proto.CompactTextString(m) }
func (*ModifyBusinessRiskWarningPercentResp) ProtoMessage()    {}
func (*ModifyBusinessRiskWarningPercentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_sender_180eb0484bb64cc1, []int{39}
}
func (m *ModifyBusinessRiskWarningPercentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyBusinessRiskWarningPercentResp.Unmarshal(m, b)
}
func (m *ModifyBusinessRiskWarningPercentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyBusinessRiskWarningPercentResp.Marshal(b, m, deterministic)
}
func (dst *ModifyBusinessRiskWarningPercentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyBusinessRiskWarningPercentResp.Merge(dst, src)
}
func (m *ModifyBusinessRiskWarningPercentResp) XXX_Size() int {
	return xxx_messageInfo_ModifyBusinessRiskWarningPercentResp.Size(m)
}
func (m *ModifyBusinessRiskWarningPercentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyBusinessRiskWarningPercentResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyBusinessRiskWarningPercentResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*BusinessConf)(nil), "backpacksender.BusinessConf")
	proto.RegisterType((*AddBusinessReq)(nil), "backpacksender.AddBusinessReq")
	proto.RegisterType((*AddBusinessResp)(nil), "backpacksender.AddBusinessResp")
	proto.RegisterType((*GetAllBusinessReq)(nil), "backpacksender.GetAllBusinessReq")
	proto.RegisterType((*GetAllBusinessResp)(nil), "backpacksender.GetAllBusinessResp")
	proto.RegisterType((*GetBusinessByIdsReq)(nil), "backpacksender.GetBusinessByIdsReq")
	proto.RegisterType((*GetBusinessByIdsResp)(nil), "backpacksender.GetBusinessByIdsResp")
	proto.RegisterType((*BusinessRiskControlConf)(nil), "backpacksender.BusinessRiskControlConf")
	proto.RegisterType((*AddBusinessRiskControlConfReq)(nil), "backpacksender.AddBusinessRiskControlConfReq")
	proto.RegisterType((*AddBusinessRiskControlConfResp)(nil), "backpacksender.AddBusinessRiskControlConfResp")
	proto.RegisterType((*BatchAddOrModBusinessRiskControlConfReq)(nil), "backpacksender.BatchAddOrModBusinessRiskControlConfReq")
	proto.RegisterType((*BatchAddOrModBusinessRiskControlConfResp)(nil), "backpacksender.BatchAddOrModBusinessRiskControlConfResp")
	proto.RegisterType((*ModBusinessRiskControlConfReq)(nil), "backpacksender.ModBusinessRiskControlConfReq")
	proto.RegisterType((*ModBusinessRiskControlConfResp)(nil), "backpacksender.ModBusinessRiskControlConfResp")
	proto.RegisterType((*GetBusinessRiskControlConfReq)(nil), "backpacksender.GetBusinessRiskControlConfReq")
	proto.RegisterType((*BusinessRiskControlConfList)(nil), "backpacksender.BusinessRiskControlConfList")
	proto.RegisterType((*GetBusinessRiskControlConfResp)(nil), "backpacksender.GetBusinessRiskControlConfResp")
	proto.RegisterMapType((map[uint32]*BusinessRiskControlConfList)(nil), "backpacksender.GetBusinessRiskControlConfResp.Business2RiskConfListEntry")
	proto.RegisterType((*GetBusinessRiskControlConfByBgIdReq)(nil), "backpacksender.GetBusinessRiskControlConfByBgIdReq")
	proto.RegisterType((*GetBusinessRiskControlConfByBgIdResp)(nil), "backpacksender.GetBusinessRiskControlConfByBgIdResp")
	proto.RegisterType((*SendBackpackOrderInfo)(nil), "backpacksender.SendBackpackOrderInfo")
	proto.RegisterType((*SendBackpackWithRiskControlReq)(nil), "backpacksender.SendBackpackWithRiskControlReq")
	proto.RegisterType((*SendBackpackWithRiskControlResp)(nil), "backpacksender.SendBackpackWithRiskControlResp")
	proto.RegisterType((*SendOneBackpackWithRiskControlReq)(nil), "backpacksender.SendOneBackpackWithRiskControlReq")
	proto.RegisterType((*SendOneBackpackWithRiskControlResp)(nil), "backpacksender.SendOneBackpackWithRiskControlResp")
	proto.RegisterType((*DeductUserBackpackWithRiskControlReq)(nil), "backpacksender.DeductUserBackpackWithRiskControlReq")
	proto.RegisterType((*DeductItem)(nil), "backpacksender.DeductItem")
	proto.RegisterType((*DeductDetail)(nil), "backpacksender.DeductDetail")
	proto.RegisterType((*DeductResult)(nil), "backpacksender.DeductResult")
	proto.RegisterType((*DeductUserBackpackWithRiskControlResp)(nil), "backpacksender.DeductUserBackpackWithRiskControlResp")
	proto.RegisterType((*SendPackDetail)(nil), "backpacksender.SendPackDetail")
	proto.RegisterType((*BatchSendBackpackWithRiskControlReq)(nil), "backpacksender.BatchSendBackpackWithRiskControlReq")
	proto.RegisterType((*BatchSendBackpackWithRiskControlResp)(nil), "backpacksender.BatchSendBackpackWithRiskControlResp")
	proto.RegisterType((*PackageItemCfg)(nil), "backpacksender.PackageItemCfg")
	proto.RegisterType((*BackpackReceiveEvent)(nil), "backpacksender.BackpackReceiveEvent")
	proto.RegisterType((*ReSendSecretKeyEmailReq)(nil), "backpacksender.ReSendSecretKeyEmailReq")
	proto.RegisterType((*ReSendSecretKeyEmailResp)(nil), "backpacksender.ReSendSecretKeyEmailResp")
	proto.RegisterType((*PreCheckBussinessRiskControlReq)(nil), "backpacksender.PreCheckBussinessRiskControlReq")
	proto.RegisterMapType((map[uint32]uint32)(nil), "backpacksender.PreCheckBussinessRiskControlReq.BgidCntsEntry")
	proto.RegisterType((*PreCheckBussinessRiskControlResp)(nil), "backpacksender.PreCheckBussinessRiskControlResp")
	proto.RegisterType((*ModifyBusinessRiskWarningPercentReq)(nil), "backpacksender.ModifyBusinessRiskWarningPercentReq")
	proto.RegisterType((*ModifyBusinessRiskWarningPercentResp)(nil), "backpacksender.ModifyBusinessRiskWarningPercentResp")
	proto.RegisterEnum("backpacksender.LimitTimeType", LimitTimeType_name, LimitTimeType_value)
	proto.RegisterEnum("backpacksender.OrderStatus", OrderStatus_name, OrderStatus_value)
	proto.RegisterEnum("backpacksender.PresentBusinessType", PresentBusinessType_name, PresentBusinessType_value)
	proto.RegisterEnum("backpacksender.DeductType", DeductType_name, DeductType_value)
	proto.RegisterEnum("backpacksender.DeductFailType", DeductFailType_name, DeductFailType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// BackpackSenderClient is the client API for BackpackSender service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BackpackSenderClient interface {
	AddBusiness(ctx context.Context, in *AddBusinessReq, opts ...grpc.CallOption) (*AddBusinessResp, error)
	GetAllBusiness(ctx context.Context, in *GetAllBusinessReq, opts ...grpc.CallOption) (*GetAllBusinessResp, error)
	GetBusinessByIds(ctx context.Context, in *GetBusinessByIdsReq, opts ...grpc.CallOption) (*GetBusinessByIdsResp, error)
	AddBusinessRiskControlConf(ctx context.Context, in *AddBusinessRiskControlConfReq, opts ...grpc.CallOption) (*AddBusinessRiskControlConfResp, error)
	// 批量修改或增加风控配置
	BatchAddOrModBusinessRiskControlConf(ctx context.Context, in *BatchAddOrModBusinessRiskControlConfReq, opts ...grpc.CallOption) (*BatchAddOrModBusinessRiskControlConfResp, error)
	ModBusinessRiskControlConf(ctx context.Context, in *ModBusinessRiskControlConfReq, opts ...grpc.CallOption) (*ModBusinessRiskControlConfResp, error)
	GetBusinessRiskControlConf(ctx context.Context, in *GetBusinessRiskControlConfReq, opts ...grpc.CallOption) (*GetBusinessRiskControlConfResp, error)
	GetBusinessRiskControlConfByBgId(ctx context.Context, in *GetBusinessRiskControlConfByBgIdReq, opts ...grpc.CallOption) (*GetBusinessRiskControlConfByBgIdResp, error)
	SendBackpackWithRiskControl(ctx context.Context, in *SendBackpackWithRiskControlReq, opts ...grpc.CallOption) (*SendBackpackWithRiskControlResp, error)
	SendOneBackpackWithRiskControl(ctx context.Context, in *SendOneBackpackWithRiskControlReq, opts ...grpc.CallOption) (*SendOneBackpackWithRiskControlResp, error)
	// 后台
	DeductUserBackpackWithRiskControl(ctx context.Context, in *DeductUserBackpackWithRiskControlReq, opts ...grpc.CallOption) (*DeductUserBackpackWithRiskControlResp, error)
	BatchSendBackpackWithRiskControl(ctx context.Context, in *BatchSendBackpackWithRiskControlReq, opts ...grpc.CallOption) (*BatchSendBackpackWithRiskControlResp, error)
	// 重新下发秘钥邮件
	ReSendSecretKeyEmail(ctx context.Context, in *ReSendSecretKeyEmailReq, opts ...grpc.CallOption) (*ReSendSecretKeyEmailResp, error)
	// 前置检查业务风控
	PreCheckBussinessRiskControl(ctx context.Context, in *PreCheckBussinessRiskControlReq, opts ...grpc.CallOption) (*PreCheckBussinessRiskControlResp, error)
	ModifyBusinessRiskWarningPercent(ctx context.Context, in *ModifyBusinessRiskWarningPercentReq, opts ...grpc.CallOption) (*ModifyBusinessRiskWarningPercentResp, error)
}

type backpackSenderClient struct {
	cc *grpc.ClientConn
}

func NewBackpackSenderClient(cc *grpc.ClientConn) BackpackSenderClient {
	return &backpackSenderClient{cc}
}

func (c *backpackSenderClient) AddBusiness(ctx context.Context, in *AddBusinessReq, opts ...grpc.CallOption) (*AddBusinessResp, error) {
	out := new(AddBusinessResp)
	err := c.cc.Invoke(ctx, "/backpacksender.BackpackSender/AddBusiness", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackSenderClient) GetAllBusiness(ctx context.Context, in *GetAllBusinessReq, opts ...grpc.CallOption) (*GetAllBusinessResp, error) {
	out := new(GetAllBusinessResp)
	err := c.cc.Invoke(ctx, "/backpacksender.BackpackSender/GetAllBusiness", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackSenderClient) GetBusinessByIds(ctx context.Context, in *GetBusinessByIdsReq, opts ...grpc.CallOption) (*GetBusinessByIdsResp, error) {
	out := new(GetBusinessByIdsResp)
	err := c.cc.Invoke(ctx, "/backpacksender.BackpackSender/GetBusinessByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackSenderClient) AddBusinessRiskControlConf(ctx context.Context, in *AddBusinessRiskControlConfReq, opts ...grpc.CallOption) (*AddBusinessRiskControlConfResp, error) {
	out := new(AddBusinessRiskControlConfResp)
	err := c.cc.Invoke(ctx, "/backpacksender.BackpackSender/AddBusinessRiskControlConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackSenderClient) BatchAddOrModBusinessRiskControlConf(ctx context.Context, in *BatchAddOrModBusinessRiskControlConfReq, opts ...grpc.CallOption) (*BatchAddOrModBusinessRiskControlConfResp, error) {
	out := new(BatchAddOrModBusinessRiskControlConfResp)
	err := c.cc.Invoke(ctx, "/backpacksender.BackpackSender/BatchAddOrModBusinessRiskControlConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackSenderClient) ModBusinessRiskControlConf(ctx context.Context, in *ModBusinessRiskControlConfReq, opts ...grpc.CallOption) (*ModBusinessRiskControlConfResp, error) {
	out := new(ModBusinessRiskControlConfResp)
	err := c.cc.Invoke(ctx, "/backpacksender.BackpackSender/ModBusinessRiskControlConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackSenderClient) GetBusinessRiskControlConf(ctx context.Context, in *GetBusinessRiskControlConfReq, opts ...grpc.CallOption) (*GetBusinessRiskControlConfResp, error) {
	out := new(GetBusinessRiskControlConfResp)
	err := c.cc.Invoke(ctx, "/backpacksender.BackpackSender/GetBusinessRiskControlConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackSenderClient) GetBusinessRiskControlConfByBgId(ctx context.Context, in *GetBusinessRiskControlConfByBgIdReq, opts ...grpc.CallOption) (*GetBusinessRiskControlConfByBgIdResp, error) {
	out := new(GetBusinessRiskControlConfByBgIdResp)
	err := c.cc.Invoke(ctx, "/backpacksender.BackpackSender/GetBusinessRiskControlConfByBgId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackSenderClient) SendBackpackWithRiskControl(ctx context.Context, in *SendBackpackWithRiskControlReq, opts ...grpc.CallOption) (*SendBackpackWithRiskControlResp, error) {
	out := new(SendBackpackWithRiskControlResp)
	err := c.cc.Invoke(ctx, "/backpacksender.BackpackSender/SendBackpackWithRiskControl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackSenderClient) SendOneBackpackWithRiskControl(ctx context.Context, in *SendOneBackpackWithRiskControlReq, opts ...grpc.CallOption) (*SendOneBackpackWithRiskControlResp, error) {
	out := new(SendOneBackpackWithRiskControlResp)
	err := c.cc.Invoke(ctx, "/backpacksender.BackpackSender/SendOneBackpackWithRiskControl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackSenderClient) DeductUserBackpackWithRiskControl(ctx context.Context, in *DeductUserBackpackWithRiskControlReq, opts ...grpc.CallOption) (*DeductUserBackpackWithRiskControlResp, error) {
	out := new(DeductUserBackpackWithRiskControlResp)
	err := c.cc.Invoke(ctx, "/backpacksender.BackpackSender/DeductUserBackpackWithRiskControl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackSenderClient) BatchSendBackpackWithRiskControl(ctx context.Context, in *BatchSendBackpackWithRiskControlReq, opts ...grpc.CallOption) (*BatchSendBackpackWithRiskControlResp, error) {
	out := new(BatchSendBackpackWithRiskControlResp)
	err := c.cc.Invoke(ctx, "/backpacksender.BackpackSender/BatchSendBackpackWithRiskControl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackSenderClient) ReSendSecretKeyEmail(ctx context.Context, in *ReSendSecretKeyEmailReq, opts ...grpc.CallOption) (*ReSendSecretKeyEmailResp, error) {
	out := new(ReSendSecretKeyEmailResp)
	err := c.cc.Invoke(ctx, "/backpacksender.BackpackSender/ReSendSecretKeyEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackSenderClient) PreCheckBussinessRiskControl(ctx context.Context, in *PreCheckBussinessRiskControlReq, opts ...grpc.CallOption) (*PreCheckBussinessRiskControlResp, error) {
	out := new(PreCheckBussinessRiskControlResp)
	err := c.cc.Invoke(ctx, "/backpacksender.BackpackSender/PreCheckBussinessRiskControl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackSenderClient) ModifyBusinessRiskWarningPercent(ctx context.Context, in *ModifyBusinessRiskWarningPercentReq, opts ...grpc.CallOption) (*ModifyBusinessRiskWarningPercentResp, error) {
	out := new(ModifyBusinessRiskWarningPercentResp)
	err := c.cc.Invoke(ctx, "/backpacksender.BackpackSender/ModifyBusinessRiskWarningPercent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BackpackSenderServer is the server API for BackpackSender service.
type BackpackSenderServer interface {
	AddBusiness(context.Context, *AddBusinessReq) (*AddBusinessResp, error)
	GetAllBusiness(context.Context, *GetAllBusinessReq) (*GetAllBusinessResp, error)
	GetBusinessByIds(context.Context, *GetBusinessByIdsReq) (*GetBusinessByIdsResp, error)
	AddBusinessRiskControlConf(context.Context, *AddBusinessRiskControlConfReq) (*AddBusinessRiskControlConfResp, error)
	// 批量修改或增加风控配置
	BatchAddOrModBusinessRiskControlConf(context.Context, *BatchAddOrModBusinessRiskControlConfReq) (*BatchAddOrModBusinessRiskControlConfResp, error)
	ModBusinessRiskControlConf(context.Context, *ModBusinessRiskControlConfReq) (*ModBusinessRiskControlConfResp, error)
	GetBusinessRiskControlConf(context.Context, *GetBusinessRiskControlConfReq) (*GetBusinessRiskControlConfResp, error)
	GetBusinessRiskControlConfByBgId(context.Context, *GetBusinessRiskControlConfByBgIdReq) (*GetBusinessRiskControlConfByBgIdResp, error)
	SendBackpackWithRiskControl(context.Context, *SendBackpackWithRiskControlReq) (*SendBackpackWithRiskControlResp, error)
	SendOneBackpackWithRiskControl(context.Context, *SendOneBackpackWithRiskControlReq) (*SendOneBackpackWithRiskControlResp, error)
	// 后台
	DeductUserBackpackWithRiskControl(context.Context, *DeductUserBackpackWithRiskControlReq) (*DeductUserBackpackWithRiskControlResp, error)
	BatchSendBackpackWithRiskControl(context.Context, *BatchSendBackpackWithRiskControlReq) (*BatchSendBackpackWithRiskControlResp, error)
	// 重新下发秘钥邮件
	ReSendSecretKeyEmail(context.Context, *ReSendSecretKeyEmailReq) (*ReSendSecretKeyEmailResp, error)
	// 前置检查业务风控
	PreCheckBussinessRiskControl(context.Context, *PreCheckBussinessRiskControlReq) (*PreCheckBussinessRiskControlResp, error)
	ModifyBusinessRiskWarningPercent(context.Context, *ModifyBusinessRiskWarningPercentReq) (*ModifyBusinessRiskWarningPercentResp, error)
}

func RegisterBackpackSenderServer(s *grpc.Server, srv BackpackSenderServer) {
	s.RegisterService(&_BackpackSender_serviceDesc, srv)
}

func _BackpackSender_AddBusiness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBusinessReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackSenderServer).AddBusiness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpacksender.BackpackSender/AddBusiness",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackSenderServer).AddBusiness(ctx, req.(*AddBusinessReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackSender_GetAllBusiness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllBusinessReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackSenderServer).GetAllBusiness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpacksender.BackpackSender/GetAllBusiness",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackSenderServer).GetAllBusiness(ctx, req.(*GetAllBusinessReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackSender_GetBusinessByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackSenderServer).GetBusinessByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpacksender.BackpackSender/GetBusinessByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackSenderServer).GetBusinessByIds(ctx, req.(*GetBusinessByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackSender_AddBusinessRiskControlConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBusinessRiskControlConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackSenderServer).AddBusinessRiskControlConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpacksender.BackpackSender/AddBusinessRiskControlConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackSenderServer).AddBusinessRiskControlConf(ctx, req.(*AddBusinessRiskControlConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackSender_BatchAddOrModBusinessRiskControlConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddOrModBusinessRiskControlConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackSenderServer).BatchAddOrModBusinessRiskControlConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpacksender.BackpackSender/BatchAddOrModBusinessRiskControlConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackSenderServer).BatchAddOrModBusinessRiskControlConf(ctx, req.(*BatchAddOrModBusinessRiskControlConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackSender_ModBusinessRiskControlConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModBusinessRiskControlConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackSenderServer).ModBusinessRiskControlConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpacksender.BackpackSender/ModBusinessRiskControlConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackSenderServer).ModBusinessRiskControlConf(ctx, req.(*ModBusinessRiskControlConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackSender_GetBusinessRiskControlConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessRiskControlConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackSenderServer).GetBusinessRiskControlConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpacksender.BackpackSender/GetBusinessRiskControlConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackSenderServer).GetBusinessRiskControlConf(ctx, req.(*GetBusinessRiskControlConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackSender_GetBusinessRiskControlConfByBgId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessRiskControlConfByBgIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackSenderServer).GetBusinessRiskControlConfByBgId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpacksender.BackpackSender/GetBusinessRiskControlConfByBgId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackSenderServer).GetBusinessRiskControlConfByBgId(ctx, req.(*GetBusinessRiskControlConfByBgIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackSender_SendBackpackWithRiskControl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendBackpackWithRiskControlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackSenderServer).SendBackpackWithRiskControl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpacksender.BackpackSender/SendBackpackWithRiskControl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackSenderServer).SendBackpackWithRiskControl(ctx, req.(*SendBackpackWithRiskControlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackSender_SendOneBackpackWithRiskControl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendOneBackpackWithRiskControlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackSenderServer).SendOneBackpackWithRiskControl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpacksender.BackpackSender/SendOneBackpackWithRiskControl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackSenderServer).SendOneBackpackWithRiskControl(ctx, req.(*SendOneBackpackWithRiskControlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackSender_DeductUserBackpackWithRiskControl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeductUserBackpackWithRiskControlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackSenderServer).DeductUserBackpackWithRiskControl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpacksender.BackpackSender/DeductUserBackpackWithRiskControl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackSenderServer).DeductUserBackpackWithRiskControl(ctx, req.(*DeductUserBackpackWithRiskControlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackSender_BatchSendBackpackWithRiskControl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSendBackpackWithRiskControlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackSenderServer).BatchSendBackpackWithRiskControl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpacksender.BackpackSender/BatchSendBackpackWithRiskControl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackSenderServer).BatchSendBackpackWithRiskControl(ctx, req.(*BatchSendBackpackWithRiskControlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackSender_ReSendSecretKeyEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReSendSecretKeyEmailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackSenderServer).ReSendSecretKeyEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpacksender.BackpackSender/ReSendSecretKeyEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackSenderServer).ReSendSecretKeyEmail(ctx, req.(*ReSendSecretKeyEmailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackSender_PreCheckBussinessRiskControl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreCheckBussinessRiskControlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackSenderServer).PreCheckBussinessRiskControl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpacksender.BackpackSender/PreCheckBussinessRiskControl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackSenderServer).PreCheckBussinessRiskControl(ctx, req.(*PreCheckBussinessRiskControlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackSender_ModifyBusinessRiskWarningPercent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyBusinessRiskWarningPercentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackSenderServer).ModifyBusinessRiskWarningPercent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpacksender.BackpackSender/ModifyBusinessRiskWarningPercent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackSenderServer).ModifyBusinessRiskWarningPercent(ctx, req.(*ModifyBusinessRiskWarningPercentReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _BackpackSender_serviceDesc = grpc.ServiceDesc{
	ServiceName: "backpacksender.BackpackSender",
	HandlerType: (*BackpackSenderServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddBusiness",
			Handler:    _BackpackSender_AddBusiness_Handler,
		},
		{
			MethodName: "GetAllBusiness",
			Handler:    _BackpackSender_GetAllBusiness_Handler,
		},
		{
			MethodName: "GetBusinessByIds",
			Handler:    _BackpackSender_GetBusinessByIds_Handler,
		},
		{
			MethodName: "AddBusinessRiskControlConf",
			Handler:    _BackpackSender_AddBusinessRiskControlConf_Handler,
		},
		{
			MethodName: "BatchAddOrModBusinessRiskControlConf",
			Handler:    _BackpackSender_BatchAddOrModBusinessRiskControlConf_Handler,
		},
		{
			MethodName: "ModBusinessRiskControlConf",
			Handler:    _BackpackSender_ModBusinessRiskControlConf_Handler,
		},
		{
			MethodName: "GetBusinessRiskControlConf",
			Handler:    _BackpackSender_GetBusinessRiskControlConf_Handler,
		},
		{
			MethodName: "GetBusinessRiskControlConfByBgId",
			Handler:    _BackpackSender_GetBusinessRiskControlConfByBgId_Handler,
		},
		{
			MethodName: "SendBackpackWithRiskControl",
			Handler:    _BackpackSender_SendBackpackWithRiskControl_Handler,
		},
		{
			MethodName: "SendOneBackpackWithRiskControl",
			Handler:    _BackpackSender_SendOneBackpackWithRiskControl_Handler,
		},
		{
			MethodName: "DeductUserBackpackWithRiskControl",
			Handler:    _BackpackSender_DeductUserBackpackWithRiskControl_Handler,
		},
		{
			MethodName: "BatchSendBackpackWithRiskControl",
			Handler:    _BackpackSender_BatchSendBackpackWithRiskControl_Handler,
		},
		{
			MethodName: "ReSendSecretKeyEmail",
			Handler:    _BackpackSender_ReSendSecretKeyEmail_Handler,
		},
		{
			MethodName: "PreCheckBussinessRiskControl",
			Handler:    _BackpackSender_PreCheckBussinessRiskControl_Handler,
		},
		{
			MethodName: "ModifyBusinessRiskWarningPercent",
			Handler:    _BackpackSender_ModifyBusinessRiskWarningPercent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/backpack-sender/backpack-sender.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/backpack-sender/backpack-sender.proto", fileDescriptor_backpack_sender_180eb0484bb64cc1)
}

var fileDescriptor_backpack_sender_180eb0484bb64cc1 = []byte{
	// 2567 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x1a, 0x4d, 0x6f, 0xe3, 0xd6,
	0xd1, 0x94, 0x64, 0x4b, 0x1a, 0x59, 0xb2, 0xf6, 0xd9, 0xbb, 0x56, 0xb4, 0x59, 0xaf, 0x4d, 0x3b,
	0x89, 0xe1, 0x24, 0x76, 0xe2, 0x24, 0x48, 0x90, 0x34, 0x40, 0x65, 0x49, 0x9b, 0x15, 0xd6, 0x2b,
	0xbb, 0x94, 0x94, 0xed, 0xa6, 0x45, 0x09, 0x8a, 0x7c, 0x96, 0x59, 0xc9, 0x14, 0x43, 0x52, 0x4e,
	0x74, 0x08, 0x50, 0x20, 0x40, 0x80, 0x9e, 0x02, 0xf4, 0x92, 0x43, 0xcf, 0x05, 0xfa, 0x03, 0xfa,
	0x03, 0x0a, 0xf4, 0xd0, 0x5b, 0xff, 0x40, 0x7f, 0x41, 0x2f, 0x3d, 0xf5, 0x07, 0x14, 0x6f, 0x1e,
	0x49, 0x51, 0xa4, 0x4c, 0xc9, 0x9b, 0x6c, 0x7b, 0x13, 0xe7, 0xfb, 0xcd, 0xcc, 0x1b, 0xce, 0x0c,
	0x05, 0xef, 0x3b, 0xce, 0xd1, 0x97, 0x23, 0x5d, 0xed, 0xdb, 0xfa, 0xe0, 0x9a, 0x5a, 0x47, 0x5d,
	0x45, 0xed, 0x9b, 0x8a, 0xda, 0x7f, 0xdb, 0xa6, 0x86, 0x16, 0x7d, 0x3e, 0x34, 0xad, 0xa1, 0x33,
	0x24, 0x05, 0x0f, 0xcc, 0xa1, 0xe2, 0x5f, 0x93, 0xb0, 0x7a, 0x32, 0xb2, 0x75, 0x83, 0xda, 0x76,
	0x75, 0x68, 0x5c, 0x10, 0x02, 0x29, 0x43, 0xb9, 0xa2, 0x25, 0x61, 0x5b, 0xd8, 0xcf, 0x4a, 0xf8,
	0x9b, 0xc1, 0x34, 0x6a, 0xab, 0xa5, 0x04, 0x87, 0xb1, 0xdf, 0x64, 0x07, 0x56, 0x55, 0x65, 0x30,
	0x60, 0xe2, 0xe4, 0x91, 0x35, 0x28, 0x25, 0x11, 0x97, 0xf3, 0x60, 0x1d, 0x6b, 0x40, 0xee, 0x43,
	0xd6, 0x1e, 0x8e, 0x2c, 0x95, 0xca, 0xba, 0x56, 0x4a, 0x6d, 0x0b, 0xfb, 0x79, 0x29, 0xc3, 0x01,
	0x0d, 0x8d, 0x3c, 0x00, 0x18, 0x9a, 0xd4, 0x52, 0xe4, 0x91, 0x4d, 0xad, 0xd2, 0x32, 0x72, 0x67,
	0x11, 0xd2, 0xb1, 0xa9, 0xc5, 0xd0, 0x5d, 0xda, 0xd3, 0x0d, 0xd9, 0xd1, 0xaf, 0x68, 0x69, 0x85,
	0xa3, 0x11, 0xd2, 0xd6, 0xaf, 0x28, 0x79, 0x05, 0x32, 0xd4, 0xd0, 0x38, 0x32, 0x8d, 0xc8, 0x34,
	0x35, 0x34, 0x44, 0x3d, 0x84, 0x5c, 0xd7, 0x3d, 0x10, 0xd3, 0x9b, 0x41, 0xbd, 0xe0, 0x81, 0xb8,
	0x66, 0x9b, 0xaa, 0x16, 0x75, 0xe4, 0x3e, 0x1d, 0x97, 0xb2, 0x5c, 0x34, 0x87, 0x3c, 0xa1, 0x63,
	0xf2, 0x06, 0xac, 0x7d, 0xa5, 0x58, 0x86, 0x6e, 0xf4, 0x64, 0xd3, 0xa2, 0x2a, 0x35, 0x9c, 0x12,
	0xa0, 0x8c, 0x82, 0x0b, 0x3e, 0xe7, 0x50, 0xef, 0x04, 0x32, 0xbd, 0x52, 0xf4, 0x41, 0x29, 0x37,
	0x39, 0x41, 0x9d, 0x01, 0xc8, 0x2e, 0xe4, 0x7d, 0x3b, 0x9c, 0xb1, 0x49, 0x4b, 0xab, 0x28, 0x65,
	0xd5, 0x03, 0xb6, 0xc7, 0x26, 0x1a, 0xab, 0xa8, 0x8e, 0x7e, 0xad, 0x3b, 0x63, 0x66, 0x6c, 0x9e,
	0x1b, 0xeb, 0x81, 0x1a, 0x1a, 0x73, 0x33, 0x3a, 0xc5, 0xa1, 0xdc, 0x51, 0x05, 0xee, 0x66, 0x17,
	0xc6, 0x5c, 0x25, 0xb6, 0xa0, 0x50, 0xd1, 0x34, 0x2f, 0x88, 0x12, 0xfd, 0x92, 0x54, 0x02, 0xaa,
	0xd5, 0xa1, 0x71, 0x81, 0xc1, 0xcc, 0x1d, 0xbf, 0x7a, 0x38, 0x1d, 0xfc, 0xc3, 0x60, 0xe0, 0x27,
	0x86, 0xb1, 0x27, 0xb1, 0x0d, 0x6b, 0x53, 0x42, 0x6d, 0xf3, 0xa7, 0x90, 0xba, 0x0e, 0x77, 0x3e,
	0xa3, 0x4e, 0x65, 0x30, 0x08, 0x58, 0x2b, 0x3e, 0x03, 0x12, 0x06, 0x86, 0xb4, 0x0d, 0x74, 0xdb,
	0x29, 0x09, 0xdb, 0xc9, 0xc5, 0xb5, 0x9d, 0xea, 0xb6, 0x23, 0x1e, 0xc2, 0xfa, 0x67, 0xd4, 0xf1,
	0x08, 0x4e, 0xc6, 0x0d, 0x0d, 0xbd, 0xb3, 0x09, 0x69, 0x5d, 0x9b, 0xc8, 0xcc, 0x4b, 0x2b, 0xba,
	0x86, 0xf4, 0xcf, 0x61, 0x23, 0x4a, 0xff, 0xd3, 0x98, 0xf2, 0x9f, 0x34, 0x6c, 0xfa, 0xc7, 0xd3,
	0xed, 0x7e, 0x75, 0x68, 0x38, 0xd6, 0x70, 0x80, 0x37, 0x2e, 0x94, 0xb0, 0x42, 0x24, 0x61, 0xd7,
	0x61, 0xb9, 0xdb, 0x63, 0xa8, 0x04, 0xa2, 0x52, 0xdd, 0x5e, 0x43, 0x23, 0x7b, 0x50, 0xb8, 0x1c,
	0x8e, 0x2c, 0x59, 0x35, 0x1c, 0x79, 0xa0, 0x5f, 0xe9, 0x0e, 0xde, 0xc0, 0x94, 0xb4, 0xca, 0xa0,
	0x55, 0xc3, 0x39, 0x65, 0x30, 0x22, 0x42, 0x5e, 0x53, 0xc6, 0x01, 0xa2, 0x14, 0x12, 0xe5, 0x34,
	0x65, 0xec, 0xd3, 0xec, 0x43, 0xd1, 0xd6, 0x8d, 0xde, 0x80, 0x06, 0xc8, 0x96, 0x79, 0xc6, 0x73,
	0xb8, 0x4f, 0xf9, 0x16, 0x10, 0x97, 0xf2, 0x5a, 0x19, 0x8c, 0xa8, 0x4b, 0xbb, 0x82, 0xb4, 0xae,
	0x8c, 0xcf, 0x19, 0x82, 0x53, 0xbf, 0x07, 0xf7, 0xd0, 0x42, 0xa7, 0x4b, 0x15, 0x63, 0x8a, 0x23,
	0x8d, 0x46, 0xac, 0x33, 0x6c, 0x9b, 0x21, 0x03, 0x4c, 0xef, 0xc2, 0x5d, 0x66, 0x70, 0x94, 0x27,
	0x83, 0x3c, 0x44, 0x53, 0xc6, 0x61, 0x96, 0xe9, 0x4a, 0x92, 0x0d, 0x57, 0x92, 0x02, 0x24, 0x74,
	0xcd, 0xbd, 0xc2, 0x09, 0x5d, 0x63, 0x1a, 0x46, 0x36, 0xd5, 0xe4, 0xb0, 0x6d, 0x78, 0x83, 0x53,
	0x12, 0x61, 0xc8, 0xc7, 0x53, 0x96, 0x91, 0x23, 0xd8, 0x40, 0x96, 0x90, 0x65, 0x78, 0xa3, 0x53,
	0xd2, 0x1d, 0x86, 0xab, 0x05, 0xed, 0x62, 0x6e, 0x9f, 0xe8, 0x50, 0x0d, 0x07, 0x2f, 0x76, 0x4a,
	0xca, 0x79, 0xb2, 0xab, 0x86, 0x43, 0xb6, 0x61, 0xd5, 0x17, 0xca, 0x48, 0x0a, 0x48, 0x02, 0xae,
	0x30, 0x46, 0xf1, 0x2e, 0xdc, 0xb5, 0xa8, 0xed, 0x44, 0x2d, 0x5d, 0xdb, 0x16, 0xf6, 0x93, 0x12,
	0x61, 0xc8, 0xa8, 0xa5, 0xc8, 0x12, 0xb6, 0xb4, 0x88, 0x1c, 0x77, 0x18, 0x2e, 0x62, 0xe9, 0x44,
	0x07, 0x33, 0xe3, 0x0e, 0x52, 0xe6, 0x3c, 0xd9, 0xae, 0xa5, 0xbe, 0x50, 0x46, 0x42, 0x90, 0x04,
	0x5c, 0x61, 0xd5, 0x48, 0x29, 0x5c, 0x0f, 0x97, 0xc2, 0xb7, 0x61, 0x9d, 0xc5, 0x46, 0x0e, 0x25,
	0xec, 0x06, 0x9e, 0xb8, 0xc8, 0x50, 0x8f, 0x83, 0x49, 0xfb, 0x26, 0x10, 0x24, 0x9f, 0xce, 0xdc,
	0xbb, 0x48, 0xbd, 0xc6, 0x30, 0xb5, 0x40, 0xf6, 0xfe, 0x0c, 0xee, 0x4f, 0x64, 0x47, 0xd3, 0xe6,
	0x1e, 0x72, 0x6d, 0x7a, 0x3a, 0xc2, 0xb9, 0xf3, 0x31, 0x94, 0x7d, 0x55, 0x51, 0xe6, 0x4d, 0x64,
	0xbe, 0xe7, 0xaa, 0x0c, 0xf3, 0xee, 0x40, 0x5e, 0xb7, 0x65, 0x75, 0x64, 0xc9, 0xca, 0x60, 0x20,
	0x77, 0x7b, 0xa5, 0xd2, 0xb6, 0xb0, 0x9f, 0x91, 0x40, 0xb7, 0xab, 0x23, 0x8b, 0x95, 0xb3, 0x9e,
	0x78, 0x0d, 0x0f, 0x82, 0x55, 0x74, 0xfa, 0xe2, 0xb3, 0x5a, 0xd4, 0x01, 0xe2, 0xdf, 0x7d, 0x4b,
	0xb7, 0xfb, 0xc1, 0xc2, 0xfa, 0xc6, 0x4d, 0xf5, 0x25, 0x2c, 0xa7, 0xd8, 0x9d, 0x46, 0x5c, 0x88,
	0x5f, 0xc1, 0x56, 0x9c, 0x5e, 0xdb, 0x7c, 0x59, 0x8a, 0xff, 0x26, 0xc0, 0x1b, 0x27, 0x8a, 0xa3,
	0x5e, 0x56, 0x34, 0xed, 0xcc, 0x7a, 0x3a, 0x8c, 0x3b, 0xfb, 0x13, 0xc8, 0x2b, 0x9a, 0xc6, 0xb5,
	0x07, 0xca, 0xea, 0xc2, 0xda, 0x73, 0x8a, 0xa6, 0x31, 0x18, 0x2b, 0xb0, 0x4c, 0xd8, 0xd5, 0x30,
	0x28, 0x2c, 0x71, 0x4b, 0x61, 0x57, 0x43, 0x5f, 0x98, 0x78, 0x00, 0xfb, 0x8b, 0x1d, 0xc2, 0x36,
	0x59, 0x88, 0xe3, 0x8f, 0xf9, 0x92, 0x3c, 0xbd, 0x0d, 0x5b, 0x73, 0x2c, 0xfb, 0x2d, 0x3c, 0x08,
	0xbc, 0xce, 0x66, 0x58, 0x36, 0xf7, 0xc5, 0xb3, 0x0f, 0xc5, 0x00, 0xc1, 0xc4, 0xaf, 0x79, 0xa9,
	0x30, 0xa1, 0x42, 0x8f, 0x7d, 0x03, 0xf7, 0x6f, 0x50, 0x84, 0xd1, 0xf9, 0x0d, 0x6c, 0x46, 0x7d,
	0xf0, 0x42, 0x41, 0xdf, 0x08, 0x3b, 0x02, 0xd5, 0xff, 0x29, 0x09, 0x5b, 0x71, 0x67, 0xb5, 0xcd,
	0x97, 0x6d, 0x02, 0xf9, 0x56, 0x80, 0x57, 0x7c, 0x05, 0xc7, 0x61, 0x15, 0x3c, 0x1b, 0x1b, 0x61,
	0x15, 0xf1, 0x36, 0xfb, 0x16, 0x1c, 0x07, 0x35, 0xd5, 0x0d, 0xc7, 0x1a, 0x4b, 0x77, 0xbb, 0xb3,
	0x70, 0xe4, 0x75, 0x58, 0x0b, 0x19, 0x81, 0x6d, 0x41, 0x5e, 0xca, 0x4f, 0xd1, 0x97, 0x47, 0x50,
	0xbe, 0x59, 0x38, 0x29, 0x42, 0x92, 0xb5, 0xc6, 0x3c, 0x21, 0xd8, 0x4f, 0x52, 0x81, 0x65, 0xfe,
	0x22, 0x49, 0x60, 0xde, 0xbe, 0xb9, 0xa0, 0xaf, 0x98, 0x48, 0x89, 0x73, 0x7e, 0x9c, 0xf8, 0x48,
	0x10, 0x3f, 0x86, 0xdd, 0x9b, 0x8f, 0x7c, 0x32, 0x3e, 0xe9, 0x35, 0x34, 0x96, 0x98, 0x7e, 0xc3,
	0x23, 0x4c, 0x1a, 0x1e, 0xf1, 0x3b, 0x01, 0xf6, 0xe6, 0x33, 0xbf, 0xfc, 0x48, 0x8b, 0x7f, 0x4e,
	0xc2, 0xdd, 0x16, 0x35, 0xb4, 0x13, 0x57, 0xc8, 0x99, 0xa5, 0x51, 0xab, 0x61, 0x5c, 0x0c, 0xe7,
	0x5f, 0x28, 0x46, 0xe0, 0x72, 0x4d, 0xfa, 0x39, 0xf0, 0x40, 0x9c, 0x80, 0x4d, 0x17, 0xfa, 0x35,
	0x95, 0x47, 0xba, 0xe6, 0xc6, 0x0e, 0x5c, 0x50, 0x47, 0xc7, 0x79, 0xc0, 0x97, 0xc0, 0xde, 0xc5,
	0x7c, 0xac, 0xf2, 0xa5, 0xb2, 0x97, 0x71, 0x19, 0xfc, 0x29, 0xcb, 0xed, 0xe3, 0x26, 0x53, 0x17,
	0x1b, 0x27, 0x46, 0x8e, 0xad, 0x6b, 0x74, 0x32, 0x58, 0x25, 0xa5, 0x9c, 0x0b, 0xf3, 0xe6, 0x27,
	0x9b, 0x5a, 0xd7, 0xd4, 0x9a, 0x4c, 0x57, 0x49, 0x09, 0x38, 0xc8, 0x9b, 0xbd, 0x86, 0xec, 0xc8,
	0xde, 0x74, 0x95, 0x95, 0xd2, 0xf8, 0xdc, 0xd0, 0xc8, 0x16, 0x80, 0xaa, 0x9b, 0x97, 0xd4, 0x72,
	0xe8, 0xd7, 0x0e, 0xb6, 0x62, 0xab, 0x52, 0x00, 0xc2, 0x66, 0x2b, 0xfa, 0xb5, 0xa9, 0x5b, 0x54,
	0xd6, 0x46, 0x96, 0xe2, 0xe8, 0x43, 0xc3, 0x9b, 0xad, 0x38, 0xb8, 0xe6, 0x42, 0x59, 0x5b, 0xe2,
	0x8e, 0x8e, 0x8a, 0x69, 0x32, 0x45, 0x7c, 0xbc, 0xca, 0x71, 0x60, 0xc5, 0x34, 0xf9, 0x59, 0x1c,
	0x4b, 0xb9, 0xb8, 0xd0, 0x55, 0xf9, 0x4a, 0xb1, 0xfa, 0xd8, 0x8d, 0x65, 0xa5, 0x9c, 0x0b, 0x7b,
	0xaa, 0x58, 0x7d, 0xf1, 0xdf, 0x09, 0xd8, 0x0a, 0x86, 0xea, 0x99, 0xee, 0x5c, 0x06, 0x82, 0xbc,
	0x50, 0x11, 0xfc, 0x9f, 0xc4, 0x2c, 0xe4, 0xf4, 0xe5, 0x58, 0xa7, 0xaf, 0xc4, 0x39, 0x3d, 0xbd,
	0x88, 0xd3, 0x33, 0x8b, 0x39, 0x3d, 0x1b, 0x75, 0xfa, 0x03, 0x00, 0x8d, 0x2a, 0x03, 0xd9, 0x19,
	0xf6, 0x29, 0x0f, 0x5e, 0x56, 0xca, 0x32, 0x48, 0x9b, 0x01, 0xc4, 0x1d, 0x78, 0x18, 0xeb, 0x6f,
	0xdb, 0x14, 0xff, 0x91, 0x80, 0x1d, 0x46, 0x73, 0x66, 0xd0, 0xff, 0x6f, 0x58, 0x42, 0x3e, 0x4f,
	0xc5, 0xfa, 0x7c, 0x39, 0xce, 0xe7, 0x2b, 0x8b, 0xf8, 0x3c, 0xbd, 0x98, 0xcf, 0x33, 0xf3, 0x7c,
	0x9e, 0x0d, 0xfb, 0x7c, 0x0f, 0xc4, 0x79, 0xfe, 0xb4, 0x4d, 0xf1, 0x87, 0x04, 0xec, 0xd5, 0xa8,
	0x36, 0x52, 0x1d, 0x36, 0x15, 0xfd, 0x48, 0xcf, 0x07, 0xfd, 0x96, 0x88, 0xf5, 0x5b, 0x32, 0xce,
	0x6f, 0xa9, 0x88, 0xdf, 0x08, 0xa4, 0xd8, 0xd8, 0xe0, 0xba, 0x1b, 0x7f, 0x33, 0x7d, 0x1a, 0x1a,
	0xce, 0xd7, 0x28, 0x7c, 0xdc, 0x04, 0x0e, 0xc2, 0x25, 0xca, 0xa7, 0x3e, 0x01, 0x16, 0xf9, 0xf4,
	0xec, 0xe9, 0x9c, 0x1f, 0xbe, 0x46, 0x1d, 0x45, 0x1f, 0x78, 0xec, 0x58, 0xcf, 0x7f, 0x0d, 0xc0,
	0x71, 0x0d, 0x87, 0x5e, 0x91, 0xfb, 0x90, 0xd5, 0x1d, 0x7a, 0xc5, 0x75, 0xf1, 0xc3, 0x67, 0x18,
	0x00, 0x35, 0x4d, 0x6d, 0xb4, 0x12, 0xa1, 0xda, 0xba, 0x01, 0xcb, 0xea, 0x70, 0x64, 0x38, 0x6e,
	0xaa, 0xf1, 0x07, 0xf1, 0x2f, 0x02, 0xac, 0x06, 0x55, 0xb3, 0x97, 0xeb, 0xc8, 0xf7, 0x2b, 0xfb,
	0x49, 0x3e, 0x74, 0x55, 0x06, 0x3a, 0x85, 0xf2, 0x6c, 0xeb, 0x99, 0x85, 0xdc, 0x1c, 0x7c, 0xdb,
	0xcf, 0xd4, 0x88, 0xf1, 0xe1, 0x46, 0xe2, 0x19, 0x78, 0xb5, 0x01, 0x0e, 0xc2, 0x53, 0x88, 0x38,
	0xb8, 0xb0, 0xa1, 0x85, 0x03, 0xd1, 0xdb, 0x19, 0x29, 0xa7, 0xdb, 0x95, 0xc1, 0xa0, 0x85, 0x20,
	0xf1, 0xef, 0xbe, 0xd9, 0x12, 0xb5, 0x47, 0x03, 0x67, 0x86, 0xd9, 0x8f, 0xe0, 0x8e, 0x3d, 0x52,
	0x55, 0xcc, 0x93, 0x5b, 0x98, 0xbf, 0xe6, 0x32, 0x35, 0xbc, 0x53, 0xfc, 0x1c, 0x0a, 0x17, 0x8a,
	0x3e, 0x08, 0x08, 0x49, 0xce, 0x15, 0xb2, 0xca, 0x38, 0x7c, 0x09, 0xf7, 0x21, 0x8b, 0x12, 0x02,
	0xe7, 0xcd, 0x30, 0x00, 0x3b, 0xad, 0x78, 0x01, 0xaf, 0x2d, 0x90, 0xf7, 0xb6, 0x19, 0x4e, 0x23,
	0x21, 0x2e, 0x8d, 0xb8, 0x53, 0xa6, 0xd2, 0xe8, 0x2b, 0x28, 0xb0, 0x6b, 0x78, 0xae, 0xa8, 0xfd,
	0x1b, 0x23, 0x3d, 0x73, 0x93, 0x73, 0x17, 0x56, 0xba, 0x3d, 0x7c, 0x31, 0xb8, 0x61, 0xec, 0xf6,
	0xd8, 0x2b, 0x61, 0x46, 0x09, 0x49, 0xcd, 0x2a, 0x21, 0xe2, 0xf7, 0x09, 0xd8, 0xc5, 0x71, 0xe5,
	0xc7, 0xbe, 0xe9, 0x5e, 0x83, 0x02, 0xdf, 0x2b, 0xea, 0x43, 0x43, 0xc6, 0x25, 0x30, 0x5f, 0xf8,
	0xe6, 0x7d, 0x68, 0x53, 0x89, 0x36, 0x08, 0xc9, 0xd8, 0xfb, 0x9f, 0x8a, 0xbb, 0xff, 0xcb, 0x91,
	0xfb, 0x5f, 0x83, 0x02, 0xf3, 0xb3, 0x8c, 0x65, 0x1d, 0xc3, 0xb0, 0x82, 0x61, 0xd8, 0x0a, 0x87,
	0x61, 0xda, 0xd5, 0xd2, 0xaa, 0xed, 0x3e, 0x63, 0x28, 0x5e, 0x87, 0xbd, 0xf9, 0x0e, 0xb1, 0x4d,
	0x51, 0x81, 0x02, 0xe3, 0x51, 0x7a, 0x94, 0xa5, 0x52, 0xf5, 0xa2, 0x17, 0x7f, 0xfb, 0x37, 0x21,
	0x8d, 0x48, 0x3f, 0x7e, 0x2b, 0xec, 0xb1, 0xa1, 0xb1, 0x03, 0x23, 0x62, 0x12, 0x43, 0x24, 0xac,
	0x1a, 0x8e, 0xf8, 0x2f, 0x01, 0x36, 0x3c, 0x13, 0x24, 0xfe, 0xee, 0xa9, 0x5f, 0x53, 0xc3, 0x99,
	0x72, 0x92, 0x30, 0xed, 0x24, 0x37, 0x6f, 0x12, 0x33, 0xf2, 0x26, 0x19, 0xc8, 0x9b, 0x22, 0x24,
	0x27, 0xdd, 0x04, 0xfb, 0x19, 0x8e, 0xf0, 0xf2, 0xac, 0xd2, 0xad, 0x5a, 0x54, 0x71, 0xa6, 0xba,
	0x3f, 0xe0, 0x20, 0x0c, 0xdd, 0x27, 0xc1, 0x52, 0x94, 0x9e, 0xed, 0xfa, 0x69, 0x97, 0x4d, 0xca,
	0x91, 0xf8, 0x2b, 0xd8, 0x94, 0x28, 0xf3, 0x79, 0xcb, 0x5b, 0xa6, 0xe3, 0xfa, 0x67, 0xd1, 0x97,
	0x8a, 0x6e, 0xcb, 0x16, 0xe5, 0xb6, 0xe0, 0xd9, 0x71, 0x95, 0x22, 0xb9, 0x10, 0xb1, 0x0c, 0xa5,
	0xd9, 0xc2, 0x6d, 0x53, 0xfc, 0xa7, 0x00, 0x0f, 0xcf, 0x2d, 0x5a, 0xbd, 0xa4, 0x6a, 0xff, 0x64,
	0x64, 0x47, 0x9a, 0xf9, 0x85, 0x2c, 0xf8, 0x02, 0xb2, 0xdd, 0x9e, 0xae, 0xb1, 0x20, 0xda, 0x6e,
	0x19, 0xfb, 0x34, 0x72, 0xf4, 0x78, 0x25, 0x87, 0x27, 0x3d, 0x5d, 0xab, 0x1a, 0x8e, 0xcd, 0x67,
	0xb4, 0x4c, 0xd7, 0x7d, 0x2c, 0x7f, 0x02, 0xf9, 0x29, 0xd4, 0x8c, 0x09, 0x6b, 0x23, 0x38, 0x61,
	0xe5, 0x83, 0x43, 0x93, 0x08, 0xdb, 0xf1, 0x7a, 0x6d, 0x53, 0x1c, 0xc2, 0xee, 0xd3, 0xa1, 0xa6,
	0x5f, 0x8c, 0x83, 0xa3, 0xcc, 0x33, 0xf7, 0x7b, 0x05, 0xb5, 0x54, 0x6a, 0x38, 0x0b, 0x39, 0x21,
	0xf8, 0xf1, 0x83, 0xb3, 0xb9, 0xf6, 0xf8, 0x1f, 0x3f, 0x38, 0x94, 0x5d, 0xb1, 0xf9, 0x0a, 0x6d,
	0xf3, 0xe0, 0x17, 0x90, 0xc7, 0x6d, 0x19, 0xcb, 0x2e, 0xbc, 0x44, 0x00, 0x2b, 0x9d, 0xe6, 0x93,
	0xb3, 0x67, 0xcd, 0xe2, 0x12, 0x59, 0x85, 0xcc, 0xe3, 0xb3, 0x8e, 0x24, 0x57, 0x9b, 0xed, 0xa2,
	0x40, 0x72, 0x90, 0xae, 0x55, 0x9e, 0xe3, 0x43, 0xc2, 0x47, 0x7d, 0x5e, 0x39, 0x2d, 0x26, 0x3d,
	0x14, 0x7b, 0x48, 0x1d, 0xec, 0x40, 0x0e, 0x47, 0xae, 0x96, 0xa3, 0x38, 0x23, 0x9b, 0x64, 0x20,
	0xd5, 0x68, 0x36, 0xda, 0xc5, 0x25, 0xf6, 0xab, 0x55, 0x6f, 0xd6, 0x8a, 0xc2, 0xc1, 0x25, 0xac,
	0x9f, 0x5b, 0xd4, 0xa6, 0x86, 0x3f, 0x2e, 0xa2, 0xee, 0x5d, 0x78, 0x58, 0x97, 0xcf, 0xa5, 0x7a,
	0xab, 0xde, 0x6c, 0xcb, 0x27, 0x9d, 0x56, 0xa3, 0x59, 0x6f, 0xb5, 0xe4, 0xf6, 0xf3, 0xf3, 0xba,
	0x5c, 0xab, 0x3f, 0xaa, 0x74, 0x4e, 0x99, 0x94, 0xb7, 0x60, 0xff, 0x26, 0xa2, 0x46, 0xb3, 0xdd,
	0x78, 0x5a, 0x69, 0xfb, 0xe8, 0xa2, 0x70, 0x70, 0xea, 0x35, 0x0f, 0xa8, 0xe0, 0x1e, 0x90, 0x4e,
	0xf3, 0x49, 0xf3, 0xec, 0x99, 0x5c, 0xab, 0xd7, 0x3a, 0xd5, 0x36, 0xb2, 0x15, 0x97, 0xc8, 0x1d,
	0xc8, 0x9f, 0x9d, 0x4b, 0x8c, 0x93, 0xc3, 0x8b, 0x02, 0x59, 0x87, 0x35, 0x5f, 0xb8, 0x0b, 0x4c,
	0x1c, 0xfc, 0x20, 0x40, 0x81, 0x8b, 0x7b, 0xe4, 0xbe, 0xbe, 0x48, 0x19, 0xee, 0x71, 0xf4, 0xa3,
	0x4a, 0xe3, 0x14, 0xad, 0xe8, 0x34, 0x3f, 0xaf, 0x9c, 0x36, 0x6a, 0xc5, 0x25, 0xb2, 0x0d, 0xaf,
	0x86, 0x71, 0x8d, 0x9a, 0xdc, 0x3c, 0x6b, 0xcb, 0xf5, 0x5f, 0x36, 0x5a, 0x4c, 0x8b, 0x08, 0x5b,
	0x21, 0x8a, 0x46, 0xbb, 0xfe, 0x94, 0x93, 0x34, 0xcf, 0x3a, 0x9f, 0x3d, 0x2e, 0x26, 0x98, 0x57,
	0x42, 0x34, 0xae, 0xf1, 0x48, 0xca, 0x80, 0xc5, 0xe4, 0xf1, 0x77, 0x6b, 0x50, 0xf0, 0xea, 0x58,
	0x0b, 0x2f, 0x03, 0x39, 0x87, 0x5c, 0x60, 0xc9, 0x48, 0x22, 0x75, 0x62, 0xfa, 0xa3, 0x54, 0xf9,
	0x61, 0x2c, 0xde, 0x36, 0xc5, 0x25, 0xf2, 0x1c, 0x0a, 0xd3, 0x5f, 0x82, 0xc8, 0xce, 0x8c, 0x8d,
	0xc9, 0xf4, 0xe7, 0xa3, 0xb2, 0x38, 0x8f, 0x04, 0x45, 0xcb, 0x50, 0x0c, 0x7f, 0xdb, 0x21, 0xbb,
	0x31, 0xeb, 0x18, 0xef, 0x6b, 0x51, 0x79, 0x6f, 0x3e, 0x11, 0x2a, 0xf8, 0x06, 0xca, 0x37, 0xaf,
	0x5c, 0xc9, 0xdb, 0x71, 0x87, 0x8f, 0x6c, 0xe6, 0xca, 0x87, 0xb7, 0x21, 0x47, 0xf5, 0x7f, 0x14,
	0xdc, 0x77, 0xde, 0x9c, 0x9d, 0x25, 0xf9, 0x30, 0xb2, 0xfc, 0x58, 0x6c, 0x5d, 0x5b, 0xfe, 0xe8,
	0xc5, 0x18, 0x3d, 0xe7, 0xc4, 0x98, 0x14, 0x71, 0x4e, 0xbc, 0x21, 0x87, 0xb7, 0x21, 0xf7, 0xd4,
	0xdf, 0xbc, 0x39, 0x8a, 0xaa, 0x8f, 0xdd, 0x9a, 0x46, 0xd5, 0xc7, 0x2f, 0xf1, 0xc4, 0x25, 0xf2,
	0xbd, 0x00, 0xdb, 0xf3, 0x36, 0x57, 0xe4, 0xbd, 0xc5, 0xc5, 0xfa, 0x8b, 0xb2, 0xf2, 0xfb, 0xb7,
	0x67, 0x42, 0x8b, 0x7e, 0x27, 0xc0, 0xfd, 0x98, 0xe6, 0x88, 0x1c, 0xce, 0x6a, 0xb7, 0x6e, 0x6e,
	0x2d, 0xcb, 0x47, 0xb7, 0xa2, 0x47, 0x13, 0x7e, 0x2f, 0xf0, 0xd5, 0xcc, 0xcd, 0x63, 0x2b, 0x79,
	0x77, 0x96, 0xd4, 0xd8, 0xb5, 0x41, 0xf9, 0xf8, 0xb6, 0x2c, 0x68, 0xcb, 0x1f, 0x04, 0xd8, 0x99,
	0x3b, 0x23, 0x90, 0xf7, 0x67, 0x8f, 0x02, 0xf1, 0xe3, 0x74, 0xf9, 0x83, 0x17, 0xe0, 0xf2, 0xb3,
	0x66, 0x5e, 0x17, 0x1b, 0xcd, 0x9a, 0x05, 0x06, 0x81, 0x68, 0xd6, 0x2c, 0xd4, 0x2c, 0x2f, 0x91,
	0x3e, 0x6c, 0xcc, 0x6a, 0xc1, 0x48, 0x64, 0x9f, 0x7a, 0x43, 0x17, 0x58, 0xde, 0x5f, 0x8c, 0x10,
	0x95, 0x7d, 0x2b, 0xc0, 0xab, 0x71, 0x6d, 0x0f, 0x39, 0xba, 0x65, 0x73, 0x56, 0x7e, 0xe7, 0x76,
	0x0c, 0x7e, 0x10, 0xe6, 0xf5, 0x39, 0xd1, 0x20, 0x2c, 0xd0, 0x8a, 0x45, 0x83, 0xb0, 0x48, 0x3b,
	0x25, 0x2e, 0x9d, 0x1c, 0x7f, 0xf1, 0x4e, 0x6f, 0x38, 0x50, 0x8c, 0xde, 0xe1, 0x07, 0xc7, 0x8e,
	0x73, 0xa8, 0x0e, 0xaf, 0x8e, 0xf0, 0x9f, 0x3d, 0xea, 0x70, 0x70, 0xc4, 0x66, 0x30, 0x5d, 0xa5,
	0xf6, 0xd1, 0xb4, 0xe8, 0xee, 0x0a, 0x52, 0xbc, 0xf7, 0xdf, 0x00, 0x00, 0x00, 0xff, 0xff, 0x21,
	0xf1, 0x02, 0x2e, 0x33, 0x24, 0x00, 0x00,
}
