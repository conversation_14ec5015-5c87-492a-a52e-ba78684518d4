// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-live-show-list/channel-live-show-list.proto

package channel_live_show_list // import "golang.52tt.com/protocol/services/channel-live-show-list"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChannelLiveShowApprovalAuditType int32

const (
	ChannelLiveShowApprovalAuditType_ChannelLiveShowApprovalAuditTypeInvalid ChannelLiveShowApprovalAuditType = 0
	ChannelLiveShowApprovalAuditType_ChannelLiveShowApprovalAuditTypePending ChannelLiveShowApprovalAuditType = 1
	ChannelLiveShowApprovalAuditType_ChannelLiveShowApprovalAuditTypePass    ChannelLiveShowApprovalAuditType = 2
	ChannelLiveShowApprovalAuditType_ChannelLiveShowApprovalAuditTypeReject  ChannelLiveShowApprovalAuditType = 3
)

var ChannelLiveShowApprovalAuditType_name = map[int32]string{
	0: "ChannelLiveShowApprovalAuditTypeInvalid",
	1: "ChannelLiveShowApprovalAuditTypePending",
	2: "ChannelLiveShowApprovalAuditTypePass",
	3: "ChannelLiveShowApprovalAuditTypeReject",
}
var ChannelLiveShowApprovalAuditType_value = map[string]int32{
	"ChannelLiveShowApprovalAuditTypeInvalid": 0,
	"ChannelLiveShowApprovalAuditTypePending": 1,
	"ChannelLiveShowApprovalAuditTypePass":    2,
	"ChannelLiveShowApprovalAuditTypeReject":  3,
}

func (x ChannelLiveShowApprovalAuditType) String() string {
	return proto.EnumName(ChannelLiveShowApprovalAuditType_name, int32(x))
}
func (ChannelLiveShowApprovalAuditType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{0}
}

type ChannelLiveShowApprovalAuditScene int32

const (
	ChannelLiveShowApprovalAuditScene_ChannelLiveShowApprovalAuditSceneInvalid ChannelLiveShowApprovalAuditScene = 0
	ChannelLiveShowApprovalAuditScene_ChannelLiveShowApprovalAuditSceneName    ChannelLiveShowApprovalAuditScene = 1
	ChannelLiveShowApprovalAuditScene_ChannelLiveShowApprovalAuditSceneCover   ChannelLiveShowApprovalAuditScene = 2
	ChannelLiveShowApprovalAuditScene_ChannelLiveShowApprovalAuditSceneAudio   ChannelLiveShowApprovalAuditScene = 3
)

var ChannelLiveShowApprovalAuditScene_name = map[int32]string{
	0: "ChannelLiveShowApprovalAuditSceneInvalid",
	1: "ChannelLiveShowApprovalAuditSceneName",
	2: "ChannelLiveShowApprovalAuditSceneCover",
	3: "ChannelLiveShowApprovalAuditSceneAudio",
}
var ChannelLiveShowApprovalAuditScene_value = map[string]int32{
	"ChannelLiveShowApprovalAuditSceneInvalid": 0,
	"ChannelLiveShowApprovalAuditSceneName":    1,
	"ChannelLiveShowApprovalAuditSceneCover":   2,
	"ChannelLiveShowApprovalAuditSceneAudio":   3,
}

func (x ChannelLiveShowApprovalAuditScene) String() string {
	return proto.EnumName(ChannelLiveShowApprovalAuditScene_name, int32(x))
}
func (ChannelLiveShowApprovalAuditScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{1}
}

type GetChannelLiveShowApprovalListRequest_ApprovalFilterStatus int32

const (
	GetChannelLiveShowApprovalListRequest_Unknown  GetChannelLiveShowApprovalListRequest_ApprovalFilterStatus = 0
	GetChannelLiveShowApprovalListRequest_Pending  GetChannelLiveShowApprovalListRequest_ApprovalFilterStatus = 1
	GetChannelLiveShowApprovalListRequest_Approved GetChannelLiveShowApprovalListRequest_ApprovalFilterStatus = 2
)

var GetChannelLiveShowApprovalListRequest_ApprovalFilterStatus_name = map[int32]string{
	0: "Unknown",
	1: "Pending",
	2: "Approved",
}
var GetChannelLiveShowApprovalListRequest_ApprovalFilterStatus_value = map[string]int32{
	"Unknown":  0,
	"Pending":  1,
	"Approved": 2,
}

func (x GetChannelLiveShowApprovalListRequest_ApprovalFilterStatus) String() string {
	return proto.EnumName(GetChannelLiveShowApprovalListRequest_ApprovalFilterStatus_name, int32(x))
}
func (GetChannelLiveShowApprovalListRequest_ApprovalFilterStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{0, 0}
}

type ChannelLiveShowApprovalItem_ApprovalStatus int32

const (
	ChannelLiveShowApprovalItem_Unknown ChannelLiveShowApprovalItem_ApprovalStatus = 0
	ChannelLiveShowApprovalItem_Pending ChannelLiveShowApprovalItem_ApprovalStatus = 1
	ChannelLiveShowApprovalItem_Pass    ChannelLiveShowApprovalItem_ApprovalStatus = 2
	ChannelLiveShowApprovalItem_Reject  ChannelLiveShowApprovalItem_ApprovalStatus = 3
)

var ChannelLiveShowApprovalItem_ApprovalStatus_name = map[int32]string{
	0: "Unknown",
	1: "Pending",
	2: "Pass",
	3: "Reject",
}
var ChannelLiveShowApprovalItem_ApprovalStatus_value = map[string]int32{
	"Unknown": 0,
	"Pending": 1,
	"Pass":    2,
	"Reject":  3,
}

func (x ChannelLiveShowApprovalItem_ApprovalStatus) String() string {
	return proto.EnumName(ChannelLiveShowApprovalItem_ApprovalStatus_name, int32(x))
}
func (ChannelLiveShowApprovalItem_ApprovalStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{1, 0}
}

// 获取审批记录
type GetChannelLiveShowApprovalListRequest struct {
	Datetime             uint32   `protobuf:"varint,1,opt,name=datetime,proto3" json:"datetime,omitempty"`
	Ttid                 uint32   `protobuf:"varint,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	ApprovalFilterStatus uint32   `protobuf:"varint,3,opt,name=approval_filter_status,json=approvalFilterStatus,proto3" json:"approval_filter_status,omitempty"`
	PageNum              uint32   `protobuf:"varint,4,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelLiveShowApprovalListRequest) Reset()         { *m = GetChannelLiveShowApprovalListRequest{} }
func (m *GetChannelLiveShowApprovalListRequest) String() string { return proto.CompactTextString(m) }
func (*GetChannelLiveShowApprovalListRequest) ProtoMessage()    {}
func (*GetChannelLiveShowApprovalListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{0}
}
func (m *GetChannelLiveShowApprovalListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLiveShowApprovalListRequest.Unmarshal(m, b)
}
func (m *GetChannelLiveShowApprovalListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLiveShowApprovalListRequest.Marshal(b, m, deterministic)
}
func (dst *GetChannelLiveShowApprovalListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLiveShowApprovalListRequest.Merge(dst, src)
}
func (m *GetChannelLiveShowApprovalListRequest) XXX_Size() int {
	return xxx_messageInfo_GetChannelLiveShowApprovalListRequest.Size(m)
}
func (m *GetChannelLiveShowApprovalListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLiveShowApprovalListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLiveShowApprovalListRequest proto.InternalMessageInfo

func (m *GetChannelLiveShowApprovalListRequest) GetDatetime() uint32 {
	if m != nil {
		return m.Datetime
	}
	return 0
}

func (m *GetChannelLiveShowApprovalListRequest) GetTtid() uint32 {
	if m != nil {
		return m.Ttid
	}
	return 0
}

func (m *GetChannelLiveShowApprovalListRequest) GetApprovalFilterStatus() uint32 {
	if m != nil {
		return m.ApprovalFilterStatus
	}
	return 0
}

func (m *GetChannelLiveShowApprovalListRequest) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetChannelLiveShowApprovalListRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type ChannelLiveShowApprovalItem struct {
	ShowApprovalId       uint32   `protobuf:"varint,1,opt,name=show_approval_id,json=showApprovalId,proto3" json:"show_approval_id,omitempty"`
	AnchorNickname       string   `protobuf:"bytes,2,opt,name=anchor_nickname,json=anchorNickname,proto3" json:"anchor_nickname,omitempty"`
	AnchorTtid           string   `protobuf:"bytes,3,opt,name=anchor_ttid,json=anchorTtid,proto3" json:"anchor_ttid,omitempty"`
	ShowStartTime        uint32   `protobuf:"varint,4,opt,name=show_start_time,json=showStartTime,proto3" json:"show_start_time,omitempty"`
	ShowEndTime          uint32   `protobuf:"varint,5,opt,name=show_end_time,json=showEndTime,proto3" json:"show_end_time,omitempty"`
	AnchorScore          float64  `protobuf:"fixed64,6,opt,name=anchor_score,json=anchorScore,proto3" json:"anchor_score,omitempty"`
	ThisShowScore        float64  `protobuf:"fixed64,16,opt,name=this_show_score,json=thisShowScore,proto3" json:"this_show_score,omitempty"`
	ShowName             string   `protobuf:"bytes,7,opt,name=show_name,json=showName,proto3" json:"show_name,omitempty"`
	ShowCoverImg         string   `protobuf:"bytes,8,opt,name=show_cover_img,json=showCoverImg,proto3" json:"show_cover_img,omitempty"`
	ShowDescAudio        string   `protobuf:"bytes,9,opt,name=show_desc_audio,json=showDescAudio,proto3" json:"show_desc_audio,omitempty"`
	VoiceTagId           uint32   `protobuf:"varint,10,opt,name=voice_tag_id,json=voiceTagId,proto3" json:"voice_tag_id,omitempty"`
	ContentTagId         uint32   `protobuf:"varint,11,opt,name=content_tag_id,json=contentTagId,proto3" json:"content_tag_id,omitempty"`
	ApprovalStatus       uint32   `protobuf:"varint,12,opt,name=approval_status,json=approvalStatus,proto3" json:"approval_status,omitempty"`
	ApprovalOperator     string   `protobuf:"bytes,13,opt,name=approval_operator,json=approvalOperator,proto3" json:"approval_operator,omitempty"`
	ApprovalTime         uint32   `protobuf:"varint,14,opt,name=approval_time,json=approvalTime,proto3" json:"approval_time,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,15,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelLiveShowApprovalItem) Reset()         { *m = ChannelLiveShowApprovalItem{} }
func (m *ChannelLiveShowApprovalItem) String() string { return proto.CompactTextString(m) }
func (*ChannelLiveShowApprovalItem) ProtoMessage()    {}
func (*ChannelLiveShowApprovalItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{1}
}
func (m *ChannelLiveShowApprovalItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLiveShowApprovalItem.Unmarshal(m, b)
}
func (m *ChannelLiveShowApprovalItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLiveShowApprovalItem.Marshal(b, m, deterministic)
}
func (dst *ChannelLiveShowApprovalItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLiveShowApprovalItem.Merge(dst, src)
}
func (m *ChannelLiveShowApprovalItem) XXX_Size() int {
	return xxx_messageInfo_ChannelLiveShowApprovalItem.Size(m)
}
func (m *ChannelLiveShowApprovalItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLiveShowApprovalItem.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLiveShowApprovalItem proto.InternalMessageInfo

func (m *ChannelLiveShowApprovalItem) GetShowApprovalId() uint32 {
	if m != nil {
		return m.ShowApprovalId
	}
	return 0
}

func (m *ChannelLiveShowApprovalItem) GetAnchorNickname() string {
	if m != nil {
		return m.AnchorNickname
	}
	return ""
}

func (m *ChannelLiveShowApprovalItem) GetAnchorTtid() string {
	if m != nil {
		return m.AnchorTtid
	}
	return ""
}

func (m *ChannelLiveShowApprovalItem) GetShowStartTime() uint32 {
	if m != nil {
		return m.ShowStartTime
	}
	return 0
}

func (m *ChannelLiveShowApprovalItem) GetShowEndTime() uint32 {
	if m != nil {
		return m.ShowEndTime
	}
	return 0
}

func (m *ChannelLiveShowApprovalItem) GetAnchorScore() float64 {
	if m != nil {
		return m.AnchorScore
	}
	return 0
}

func (m *ChannelLiveShowApprovalItem) GetThisShowScore() float64 {
	if m != nil {
		return m.ThisShowScore
	}
	return 0
}

func (m *ChannelLiveShowApprovalItem) GetShowName() string {
	if m != nil {
		return m.ShowName
	}
	return ""
}

func (m *ChannelLiveShowApprovalItem) GetShowCoverImg() string {
	if m != nil {
		return m.ShowCoverImg
	}
	return ""
}

func (m *ChannelLiveShowApprovalItem) GetShowDescAudio() string {
	if m != nil {
		return m.ShowDescAudio
	}
	return ""
}

func (m *ChannelLiveShowApprovalItem) GetVoiceTagId() uint32 {
	if m != nil {
		return m.VoiceTagId
	}
	return 0
}

func (m *ChannelLiveShowApprovalItem) GetContentTagId() uint32 {
	if m != nil {
		return m.ContentTagId
	}
	return 0
}

func (m *ChannelLiveShowApprovalItem) GetApprovalStatus() uint32 {
	if m != nil {
		return m.ApprovalStatus
	}
	return 0
}

func (m *ChannelLiveShowApprovalItem) GetApprovalOperator() string {
	if m != nil {
		return m.ApprovalOperator
	}
	return ""
}

func (m *ChannelLiveShowApprovalItem) GetApprovalTime() uint32 {
	if m != nil {
		return m.ApprovalTime
	}
	return 0
}

func (m *ChannelLiveShowApprovalItem) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

type GetChannelLiveShowApprovalListResp struct {
	ApprovalList         []*ChannelLiveShowApprovalItem `protobuf:"bytes,1,rep,name=approval_list,json=approvalList,proto3" json:"approval_list,omitempty"`
	Total                uint32                         `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *GetChannelLiveShowApprovalListResp) Reset()         { *m = GetChannelLiveShowApprovalListResp{} }
func (m *GetChannelLiveShowApprovalListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelLiveShowApprovalListResp) ProtoMessage()    {}
func (*GetChannelLiveShowApprovalListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{2}
}
func (m *GetChannelLiveShowApprovalListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLiveShowApprovalListResp.Unmarshal(m, b)
}
func (m *GetChannelLiveShowApprovalListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLiveShowApprovalListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelLiveShowApprovalListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLiveShowApprovalListResp.Merge(dst, src)
}
func (m *GetChannelLiveShowApprovalListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelLiveShowApprovalListResp.Size(m)
}
func (m *GetChannelLiveShowApprovalListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLiveShowApprovalListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLiveShowApprovalListResp proto.InternalMessageInfo

func (m *GetChannelLiveShowApprovalListResp) GetApprovalList() []*ChannelLiveShowApprovalItem {
	if m != nil {
		return m.ApprovalList
	}
	return nil
}

func (m *GetChannelLiveShowApprovalListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 获取标签配置
type GetShowTagRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShowTagRequest) Reset()         { *m = GetShowTagRequest{} }
func (m *GetShowTagRequest) String() string { return proto.CompactTextString(m) }
func (*GetShowTagRequest) ProtoMessage()    {}
func (*GetShowTagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{3}
}
func (m *GetShowTagRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowTagRequest.Unmarshal(m, b)
}
func (m *GetShowTagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowTagRequest.Marshal(b, m, deterministic)
}
func (dst *GetShowTagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowTagRequest.Merge(dst, src)
}
func (m *GetShowTagRequest) XXX_Size() int {
	return xxx_messageInfo_GetShowTagRequest.Size(m)
}
func (m *GetShowTagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowTagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowTagRequest proto.InternalMessageInfo

type TagNode struct {
	TagId                uint32     `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagName              string     `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	ChildList            []*TagNode `protobuf:"bytes,3,rep,name=child_list,json=childList,proto3" json:"child_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *TagNode) Reset()         { *m = TagNode{} }
func (m *TagNode) String() string { return proto.CompactTextString(m) }
func (*TagNode) ProtoMessage()    {}
func (*TagNode) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{4}
}
func (m *TagNode) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagNode.Unmarshal(m, b)
}
func (m *TagNode) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagNode.Marshal(b, m, deterministic)
}
func (dst *TagNode) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagNode.Merge(dst, src)
}
func (m *TagNode) XXX_Size() int {
	return xxx_messageInfo_TagNode.Size(m)
}
func (m *TagNode) XXX_DiscardUnknown() {
	xxx_messageInfo_TagNode.DiscardUnknown(m)
}

var xxx_messageInfo_TagNode proto.InternalMessageInfo

func (m *TagNode) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *TagNode) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *TagNode) GetChildList() []*TagNode {
	if m != nil {
		return m.ChildList
	}
	return nil
}

type GetShowTagResponse struct {
	VoiceTagList         []*TagNode `protobuf:"bytes,1,rep,name=voice_tag_list,json=voiceTagList,proto3" json:"voice_tag_list,omitempty"`
	ContentTagList       []*TagNode `protobuf:"bytes,2,rep,name=content_tag_list,json=contentTagList,proto3" json:"content_tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetShowTagResponse) Reset()         { *m = GetShowTagResponse{} }
func (m *GetShowTagResponse) String() string { return proto.CompactTextString(m) }
func (*GetShowTagResponse) ProtoMessage()    {}
func (*GetShowTagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{5}
}
func (m *GetShowTagResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowTagResponse.Unmarshal(m, b)
}
func (m *GetShowTagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowTagResponse.Marshal(b, m, deterministic)
}
func (dst *GetShowTagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowTagResponse.Merge(dst, src)
}
func (m *GetShowTagResponse) XXX_Size() int {
	return xxx_messageInfo_GetShowTagResponse.Size(m)
}
func (m *GetShowTagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowTagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowTagResponse proto.InternalMessageInfo

func (m *GetShowTagResponse) GetVoiceTagList() []*TagNode {
	if m != nil {
		return m.VoiceTagList
	}
	return nil
}

func (m *GetShowTagResponse) GetContentTagList() []*TagNode {
	if m != nil {
		return m.ContentTagList
	}
	return nil
}

// 修改标签
type ModifyShowApprovalTagRequest struct {
	ShowApprovalId       uint32   `protobuf:"varint,1,opt,name=show_approval_id,json=showApprovalId,proto3" json:"show_approval_id,omitempty"`
	VoiceTagId           uint32   `protobuf:"varint,2,opt,name=voice_tag_id,json=voiceTagId,proto3" json:"voice_tag_id,omitempty"`
	ContentTagId         uint32   `protobuf:"varint,3,opt,name=content_tag_id,json=contentTagId,proto3" json:"content_tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyShowApprovalTagRequest) Reset()         { *m = ModifyShowApprovalTagRequest{} }
func (m *ModifyShowApprovalTagRequest) String() string { return proto.CompactTextString(m) }
func (*ModifyShowApprovalTagRequest) ProtoMessage()    {}
func (*ModifyShowApprovalTagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{6}
}
func (m *ModifyShowApprovalTagRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyShowApprovalTagRequest.Unmarshal(m, b)
}
func (m *ModifyShowApprovalTagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyShowApprovalTagRequest.Marshal(b, m, deterministic)
}
func (dst *ModifyShowApprovalTagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyShowApprovalTagRequest.Merge(dst, src)
}
func (m *ModifyShowApprovalTagRequest) XXX_Size() int {
	return xxx_messageInfo_ModifyShowApprovalTagRequest.Size(m)
}
func (m *ModifyShowApprovalTagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyShowApprovalTagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyShowApprovalTagRequest proto.InternalMessageInfo

func (m *ModifyShowApprovalTagRequest) GetShowApprovalId() uint32 {
	if m != nil {
		return m.ShowApprovalId
	}
	return 0
}

func (m *ModifyShowApprovalTagRequest) GetVoiceTagId() uint32 {
	if m != nil {
		return m.VoiceTagId
	}
	return 0
}

func (m *ModifyShowApprovalTagRequest) GetContentTagId() uint32 {
	if m != nil {
		return m.ContentTagId
	}
	return 0
}

type ModifyShowApprovalTagResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyShowApprovalTagResponse) Reset()         { *m = ModifyShowApprovalTagResponse{} }
func (m *ModifyShowApprovalTagResponse) String() string { return proto.CompactTextString(m) }
func (*ModifyShowApprovalTagResponse) ProtoMessage()    {}
func (*ModifyShowApprovalTagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{7}
}
func (m *ModifyShowApprovalTagResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyShowApprovalTagResponse.Unmarshal(m, b)
}
func (m *ModifyShowApprovalTagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyShowApprovalTagResponse.Marshal(b, m, deterministic)
}
func (dst *ModifyShowApprovalTagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyShowApprovalTagResponse.Merge(dst, src)
}
func (m *ModifyShowApprovalTagResponse) XXX_Size() int {
	return xxx_messageInfo_ModifyShowApprovalTagResponse.Size(m)
}
func (m *ModifyShowApprovalTagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyShowApprovalTagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyShowApprovalTagResponse proto.InternalMessageInfo

// 处理审批
type HandleShowApprovalRequest struct {
	ShowApprovalId       uint32   `protobuf:"varint,1,opt,name=show_approval_id,json=showApprovalId,proto3" json:"show_approval_id,omitempty"`
	IsPass               bool     `protobuf:"varint,2,opt,name=is_pass,json=isPass,proto3" json:"is_pass,omitempty"`
	Operator             string   `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleShowApprovalRequest) Reset()         { *m = HandleShowApprovalRequest{} }
func (m *HandleShowApprovalRequest) String() string { return proto.CompactTextString(m) }
func (*HandleShowApprovalRequest) ProtoMessage()    {}
func (*HandleShowApprovalRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{8}
}
func (m *HandleShowApprovalRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleShowApprovalRequest.Unmarshal(m, b)
}
func (m *HandleShowApprovalRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleShowApprovalRequest.Marshal(b, m, deterministic)
}
func (dst *HandleShowApprovalRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleShowApprovalRequest.Merge(dst, src)
}
func (m *HandleShowApprovalRequest) XXX_Size() int {
	return xxx_messageInfo_HandleShowApprovalRequest.Size(m)
}
func (m *HandleShowApprovalRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleShowApprovalRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HandleShowApprovalRequest proto.InternalMessageInfo

func (m *HandleShowApprovalRequest) GetShowApprovalId() uint32 {
	if m != nil {
		return m.ShowApprovalId
	}
	return 0
}

func (m *HandleShowApprovalRequest) GetIsPass() bool {
	if m != nil {
		return m.IsPass
	}
	return false
}

func (m *HandleShowApprovalRequest) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type HandelShowApprovalResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandelShowApprovalResponse) Reset()         { *m = HandelShowApprovalResponse{} }
func (m *HandelShowApprovalResponse) String() string { return proto.CompactTextString(m) }
func (*HandelShowApprovalResponse) ProtoMessage()    {}
func (*HandelShowApprovalResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{9}
}
func (m *HandelShowApprovalResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandelShowApprovalResponse.Unmarshal(m, b)
}
func (m *HandelShowApprovalResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandelShowApprovalResponse.Marshal(b, m, deterministic)
}
func (dst *HandelShowApprovalResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandelShowApprovalResponse.Merge(dst, src)
}
func (m *HandelShowApprovalResponse) XXX_Size() int {
	return xxx_messageInfo_HandelShowApprovalResponse.Size(m)
}
func (m *HandelShowApprovalResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HandelShowApprovalResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HandelShowApprovalResponse proto.InternalMessageInfo

type GetShowTimeRequest struct {
	Datetime             uint32   `protobuf:"varint,1,opt,name=datetime,proto3" json:"datetime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShowTimeRequest) Reset()         { *m = GetShowTimeRequest{} }
func (m *GetShowTimeRequest) String() string { return proto.CompactTextString(m) }
func (*GetShowTimeRequest) ProtoMessage()    {}
func (*GetShowTimeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{10}
}
func (m *GetShowTimeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowTimeRequest.Unmarshal(m, b)
}
func (m *GetShowTimeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowTimeRequest.Marshal(b, m, deterministic)
}
func (dst *GetShowTimeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowTimeRequest.Merge(dst, src)
}
func (m *GetShowTimeRequest) XXX_Size() int {
	return xxx_messageInfo_GetShowTimeRequest.Size(m)
}
func (m *GetShowTimeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowTimeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowTimeRequest proto.InternalMessageInfo

func (m *GetShowTimeRequest) GetDatetime() uint32 {
	if m != nil {
		return m.Datetime
	}
	return 0
}

type GetShowTimeResponse struct {
	ShowTimeList           []*GetShowTimeResponse_ShowTime `protobuf:"bytes,1,rep,name=show_time_list,json=showTimeList,proto3" json:"show_time_list,omitempty"`
	EarliestSelectableTime uint32                          `protobuf:"varint,2,opt,name=earliest_selectable_time,json=earliestSelectableTime,proto3" json:"earliest_selectable_time,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                        `json:"-"`
	XXX_unrecognized       []byte                          `json:"-"`
	XXX_sizecache          int32                           `json:"-"`
}

func (m *GetShowTimeResponse) Reset()         { *m = GetShowTimeResponse{} }
func (m *GetShowTimeResponse) String() string { return proto.CompactTextString(m) }
func (*GetShowTimeResponse) ProtoMessage()    {}
func (*GetShowTimeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{11}
}
func (m *GetShowTimeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowTimeResponse.Unmarshal(m, b)
}
func (m *GetShowTimeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowTimeResponse.Marshal(b, m, deterministic)
}
func (dst *GetShowTimeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowTimeResponse.Merge(dst, src)
}
func (m *GetShowTimeResponse) XXX_Size() int {
	return xxx_messageInfo_GetShowTimeResponse.Size(m)
}
func (m *GetShowTimeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowTimeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowTimeResponse proto.InternalMessageInfo

func (m *GetShowTimeResponse) GetShowTimeList() []*GetShowTimeResponse_ShowTime {
	if m != nil {
		return m.ShowTimeList
	}
	return nil
}

func (m *GetShowTimeResponse) GetEarliestSelectableTime() uint32 {
	if m != nil {
		return m.EarliestSelectableTime
	}
	return 0
}

type GetShowTimeResponse_ShowTime struct {
	ShowStartTime        uint32   `protobuf:"varint,1,opt,name=show_start_time,json=showStartTime,proto3" json:"show_start_time,omitempty"`
	ShowEndTime          uint32   `protobuf:"varint,2,opt,name=show_end_time,json=showEndTime,proto3" json:"show_end_time,omitempty"`
	RemainCnt            uint32   `protobuf:"varint,3,opt,name=remain_cnt,json=remainCnt,proto3" json:"remain_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShowTimeResponse_ShowTime) Reset()         { *m = GetShowTimeResponse_ShowTime{} }
func (m *GetShowTimeResponse_ShowTime) String() string { return proto.CompactTextString(m) }
func (*GetShowTimeResponse_ShowTime) ProtoMessage()    {}
func (*GetShowTimeResponse_ShowTime) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{11, 0}
}
func (m *GetShowTimeResponse_ShowTime) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowTimeResponse_ShowTime.Unmarshal(m, b)
}
func (m *GetShowTimeResponse_ShowTime) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowTimeResponse_ShowTime.Marshal(b, m, deterministic)
}
func (dst *GetShowTimeResponse_ShowTime) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowTimeResponse_ShowTime.Merge(dst, src)
}
func (m *GetShowTimeResponse_ShowTime) XXX_Size() int {
	return xxx_messageInfo_GetShowTimeResponse_ShowTime.Size(m)
}
func (m *GetShowTimeResponse_ShowTime) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowTimeResponse_ShowTime.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowTimeResponse_ShowTime proto.InternalMessageInfo

func (m *GetShowTimeResponse_ShowTime) GetShowStartTime() uint32 {
	if m != nil {
		return m.ShowStartTime
	}
	return 0
}

func (m *GetShowTimeResponse_ShowTime) GetShowEndTime() uint32 {
	if m != nil {
		return m.ShowEndTime
	}
	return 0
}

func (m *GetShowTimeResponse_ShowTime) GetRemainCnt() uint32 {
	if m != nil {
		return m.RemainCnt
	}
	return 0
}

type GetLiveShowEntryInfoRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveShowEntryInfoRequest) Reset()         { *m = GetLiveShowEntryInfoRequest{} }
func (m *GetLiveShowEntryInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetLiveShowEntryInfoRequest) ProtoMessage()    {}
func (*GetLiveShowEntryInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{12}
}
func (m *GetLiveShowEntryInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveShowEntryInfoRequest.Unmarshal(m, b)
}
func (m *GetLiveShowEntryInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveShowEntryInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetLiveShowEntryInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveShowEntryInfoRequest.Merge(dst, src)
}
func (m *GetLiveShowEntryInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetLiveShowEntryInfoRequest.Size(m)
}
func (m *GetLiveShowEntryInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveShowEntryInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveShowEntryInfoRequest proto.InternalMessageInfo

func (m *GetLiveShowEntryInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetLiveShowEntryInfoResponse struct {
	ShowEntryTextList    []string `protobuf:"bytes,1,rep,name=show_entry_text_list,json=showEntryTextList,proto3" json:"show_entry_text_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveShowEntryInfoResponse) Reset()         { *m = GetLiveShowEntryInfoResponse{} }
func (m *GetLiveShowEntryInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetLiveShowEntryInfoResponse) ProtoMessage()    {}
func (*GetLiveShowEntryInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{13}
}
func (m *GetLiveShowEntryInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveShowEntryInfoResponse.Unmarshal(m, b)
}
func (m *GetLiveShowEntryInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveShowEntryInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetLiveShowEntryInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveShowEntryInfoResponse.Merge(dst, src)
}
func (m *GetLiveShowEntryInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetLiveShowEntryInfoResponse.Size(m)
}
func (m *GetLiveShowEntryInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveShowEntryInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveShowEntryInfoResponse proto.InternalMessageInfo

func (m *GetLiveShowEntryInfoResponse) GetShowEntryTextList() []string {
	if m != nil {
		return m.ShowEntryTextList
	}
	return nil
}

type GetShowListRequest struct {
	Datetime             uint32   `protobuf:"varint,1,opt,name=datetime,proto3" json:"datetime,omitempty"`
	VoiceTagIdList       []uint32 `protobuf:"varint,2,rep,packed,name=voice_tag_id_list,json=voiceTagIdList,proto3" json:"voice_tag_id_list,omitempty"`
	ContentTagIdList     []uint32 `protobuf:"varint,3,rep,packed,name=content_tag_id_list,json=contentTagIdList,proto3" json:"content_tag_id_list,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShowListRequest) Reset()         { *m = GetShowListRequest{} }
func (m *GetShowListRequest) String() string { return proto.CompactTextString(m) }
func (*GetShowListRequest) ProtoMessage()    {}
func (*GetShowListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{14}
}
func (m *GetShowListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowListRequest.Unmarshal(m, b)
}
func (m *GetShowListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowListRequest.Marshal(b, m, deterministic)
}
func (dst *GetShowListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowListRequest.Merge(dst, src)
}
func (m *GetShowListRequest) XXX_Size() int {
	return xxx_messageInfo_GetShowListRequest.Size(m)
}
func (m *GetShowListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowListRequest proto.InternalMessageInfo

func (m *GetShowListRequest) GetDatetime() uint32 {
	if m != nil {
		return m.Datetime
	}
	return 0
}

func (m *GetShowListRequest) GetVoiceTagIdList() []uint32 {
	if m != nil {
		return m.VoiceTagIdList
	}
	return nil
}

func (m *GetShowListRequest) GetContentTagIdList() []uint32 {
	if m != nil {
		return m.ContentTagIdList
	}
	return nil
}

func (m *GetShowListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ShowItem struct {
	ShowId               uint32   `protobuf:"varint,1,opt,name=show_id,json=showId,proto3" json:"show_id,omitempty"`
	ShowName             string   `protobuf:"bytes,2,opt,name=show_name,json=showName,proto3" json:"show_name,omitempty"`
	ShowStartTime        uint32   `protobuf:"varint,3,opt,name=show_start_time,json=showStartTime,proto3" json:"show_start_time,omitempty"`
	ShowEndTime          uint32   `protobuf:"varint,4,opt,name=show_end_time,json=showEndTime,proto3" json:"show_end_time,omitempty"`
	ShowDescAudio        string   `protobuf:"bytes,5,opt,name=show_desc_audio,json=showDescAudio,proto3" json:"show_desc_audio,omitempty"`
	ShowCoverImg         string   `protobuf:"bytes,6,opt,name=show_cover_img,json=showCoverImg,proto3" json:"show_cover_img,omitempty"`
	Uid                  uint32   `protobuf:"varint,7,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,8,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AudioDuration        uint32   `protobuf:"varint,9,opt,name=audio_duration,json=audioDuration,proto3" json:"audio_duration,omitempty"`
	ContentId            uint32   `protobuf:"varint,10,opt,name=content_id,json=contentId,proto3" json:"content_id,omitempty"`
	VoiceId              uint32   `protobuf:"varint,11,opt,name=voice_id,json=voiceId,proto3" json:"voice_id,omitempty"`
	HotValue             int64    `protobuf:"varint,12,opt,name=hot_value,json=hotValue,proto3" json:"hot_value,omitempty"`
	LiveStatus           uint32   `protobuf:"varint,13,opt,name=live_status,json=liveStatus,proto3" json:"live_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowItem) Reset()         { *m = ShowItem{} }
func (m *ShowItem) String() string { return proto.CompactTextString(m) }
func (*ShowItem) ProtoMessage()    {}
func (*ShowItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{15}
}
func (m *ShowItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowItem.Unmarshal(m, b)
}
func (m *ShowItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowItem.Marshal(b, m, deterministic)
}
func (dst *ShowItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowItem.Merge(dst, src)
}
func (m *ShowItem) XXX_Size() int {
	return xxx_messageInfo_ShowItem.Size(m)
}
func (m *ShowItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowItem.DiscardUnknown(m)
}

var xxx_messageInfo_ShowItem proto.InternalMessageInfo

func (m *ShowItem) GetShowId() uint32 {
	if m != nil {
		return m.ShowId
	}
	return 0
}

func (m *ShowItem) GetShowName() string {
	if m != nil {
		return m.ShowName
	}
	return ""
}

func (m *ShowItem) GetShowStartTime() uint32 {
	if m != nil {
		return m.ShowStartTime
	}
	return 0
}

func (m *ShowItem) GetShowEndTime() uint32 {
	if m != nil {
		return m.ShowEndTime
	}
	return 0
}

func (m *ShowItem) GetShowDescAudio() string {
	if m != nil {
		return m.ShowDescAudio
	}
	return ""
}

func (m *ShowItem) GetShowCoverImg() string {
	if m != nil {
		return m.ShowCoverImg
	}
	return ""
}

func (m *ShowItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ShowItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ShowItem) GetAudioDuration() uint32 {
	if m != nil {
		return m.AudioDuration
	}
	return 0
}

func (m *ShowItem) GetContentId() uint32 {
	if m != nil {
		return m.ContentId
	}
	return 0
}

func (m *ShowItem) GetVoiceId() uint32 {
	if m != nil {
		return m.VoiceId
	}
	return 0
}

func (m *ShowItem) GetHotValue() int64 {
	if m != nil {
		return m.HotValue
	}
	return 0
}

func (m *ShowItem) GetLiveStatus() uint32 {
	if m != nil {
		return m.LiveStatus
	}
	return 0
}

type GetShowListResponse struct {
	ShowList             []*GetShowListResponse_ShowGroupItem `protobuf:"bytes,1,rep,name=show_list,json=showList,proto3" json:"show_list,omitempty"`
	ShowApplyEntry       bool                                 `protobuf:"varint,2,opt,name=show_apply_entry,json=showApplyEntry,proto3" json:"show_apply_entry,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *GetShowListResponse) Reset()         { *m = GetShowListResponse{} }
func (m *GetShowListResponse) String() string { return proto.CompactTextString(m) }
func (*GetShowListResponse) ProtoMessage()    {}
func (*GetShowListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{16}
}
func (m *GetShowListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowListResponse.Unmarshal(m, b)
}
func (m *GetShowListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowListResponse.Marshal(b, m, deterministic)
}
func (dst *GetShowListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowListResponse.Merge(dst, src)
}
func (m *GetShowListResponse) XXX_Size() int {
	return xxx_messageInfo_GetShowListResponse.Size(m)
}
func (m *GetShowListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowListResponse proto.InternalMessageInfo

func (m *GetShowListResponse) GetShowList() []*GetShowListResponse_ShowGroupItem {
	if m != nil {
		return m.ShowList
	}
	return nil
}

func (m *GetShowListResponse) GetShowApplyEntry() bool {
	if m != nil {
		return m.ShowApplyEntry
	}
	return false
}

type GetShowListResponse_ShowGroupItem struct {
	TimeRange            string      `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
	ShowItem             []*ShowItem `protobuf:"bytes,2,rep,name=show_item,json=showItem,proto3" json:"show_item,omitempty"`
	IsEnd                bool        `protobuf:"varint,3,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	HasMore              bool        `protobuf:"varint,4,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	BeginTime            uint32      `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32      `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetShowListResponse_ShowGroupItem) Reset()         { *m = GetShowListResponse_ShowGroupItem{} }
func (m *GetShowListResponse_ShowGroupItem) String() string { return proto.CompactTextString(m) }
func (*GetShowListResponse_ShowGroupItem) ProtoMessage()    {}
func (*GetShowListResponse_ShowGroupItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{16, 0}
}
func (m *GetShowListResponse_ShowGroupItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowListResponse_ShowGroupItem.Unmarshal(m, b)
}
func (m *GetShowListResponse_ShowGroupItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowListResponse_ShowGroupItem.Marshal(b, m, deterministic)
}
func (dst *GetShowListResponse_ShowGroupItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowListResponse_ShowGroupItem.Merge(dst, src)
}
func (m *GetShowListResponse_ShowGroupItem) XXX_Size() int {
	return xxx_messageInfo_GetShowListResponse_ShowGroupItem.Size(m)
}
func (m *GetShowListResponse_ShowGroupItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowListResponse_ShowGroupItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowListResponse_ShowGroupItem proto.InternalMessageInfo

func (m *GetShowListResponse_ShowGroupItem) GetTimeRange() string {
	if m != nil {
		return m.TimeRange
	}
	return ""
}

func (m *GetShowListResponse_ShowGroupItem) GetShowItem() []*ShowItem {
	if m != nil {
		return m.ShowItem
	}
	return nil
}

func (m *GetShowListResponse_ShowGroupItem) GetIsEnd() bool {
	if m != nil {
		return m.IsEnd
	}
	return false
}

func (m *GetShowListResponse_ShowGroupItem) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

func (m *GetShowListResponse_ShowGroupItem) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetShowListResponse_ShowGroupItem) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetChannelLiveShowInfoRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelLiveShowInfoRequest) Reset()         { *m = GetChannelLiveShowInfoRequest{} }
func (m *GetChannelLiveShowInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetChannelLiveShowInfoRequest) ProtoMessage()    {}
func (*GetChannelLiveShowInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{17}
}
func (m *GetChannelLiveShowInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLiveShowInfoRequest.Unmarshal(m, b)
}
func (m *GetChannelLiveShowInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLiveShowInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetChannelLiveShowInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLiveShowInfoRequest.Merge(dst, src)
}
func (m *GetChannelLiveShowInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetChannelLiveShowInfoRequest.Size(m)
}
func (m *GetChannelLiveShowInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLiveShowInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLiveShowInfoRequest proto.InternalMessageInfo

func (m *GetChannelLiveShowInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelLiveShowInfoRequest) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

type ShowRatingFloat struct {
	IsShow               bool     `protobuf:"varint,1,opt,name=is_show,json=isShow,proto3" json:"is_show,omitempty"`
	ListenSec            int64    `protobuf:"varint,2,opt,name=listen_sec,json=listenSec,proto3" json:"listen_sec,omitempty"`
	FloatStaySec         int64    `protobuf:"varint,3,opt,name=float_stay_sec,json=floatStaySec,proto3" json:"float_stay_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowRatingFloat) Reset()         { *m = ShowRatingFloat{} }
func (m *ShowRatingFloat) String() string { return proto.CompactTextString(m) }
func (*ShowRatingFloat) ProtoMessage()    {}
func (*ShowRatingFloat) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{18}
}
func (m *ShowRatingFloat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowRatingFloat.Unmarshal(m, b)
}
func (m *ShowRatingFloat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowRatingFloat.Marshal(b, m, deterministic)
}
func (dst *ShowRatingFloat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowRatingFloat.Merge(dst, src)
}
func (m *ShowRatingFloat) XXX_Size() int {
	return xxx_messageInfo_ShowRatingFloat.Size(m)
}
func (m *ShowRatingFloat) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowRatingFloat.DiscardUnknown(m)
}

var xxx_messageInfo_ShowRatingFloat proto.InternalMessageInfo

func (m *ShowRatingFloat) GetIsShow() bool {
	if m != nil {
		return m.IsShow
	}
	return false
}

func (m *ShowRatingFloat) GetListenSec() int64 {
	if m != nil {
		return m.ListenSec
	}
	return 0
}

func (m *ShowRatingFloat) GetFloatStaySec() int64 {
	if m != nil {
		return m.FloatStaySec
	}
	return 0
}

type LiveShowInfo struct {
	ShowId               uint32           `protobuf:"varint,1,opt,name=show_id,json=showId,proto3" json:"show_id,omitempty"`
	ShowName             string           `protobuf:"bytes,2,opt,name=show_name,json=showName,proto3" json:"show_name,omitempty"`
	BeginTs              int64            `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                int64            `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	AnchorUid            uint32           `protobuf:"varint,5,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	RatingFloatConf      *ShowRatingFloat `protobuf:"bytes,6,opt,name=rating_float_conf,json=ratingFloatConf,proto3" json:"rating_float_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *LiveShowInfo) Reset()         { *m = LiveShowInfo{} }
func (m *LiveShowInfo) String() string { return proto.CompactTextString(m) }
func (*LiveShowInfo) ProtoMessage()    {}
func (*LiveShowInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{19}
}
func (m *LiveShowInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LiveShowInfo.Unmarshal(m, b)
}
func (m *LiveShowInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LiveShowInfo.Marshal(b, m, deterministic)
}
func (dst *LiveShowInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LiveShowInfo.Merge(dst, src)
}
func (m *LiveShowInfo) XXX_Size() int {
	return xxx_messageInfo_LiveShowInfo.Size(m)
}
func (m *LiveShowInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LiveShowInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LiveShowInfo proto.InternalMessageInfo

func (m *LiveShowInfo) GetShowId() uint32 {
	if m != nil {
		return m.ShowId
	}
	return 0
}

func (m *LiveShowInfo) GetShowName() string {
	if m != nil {
		return m.ShowName
	}
	return ""
}

func (m *LiveShowInfo) GetBeginTs() int64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *LiveShowInfo) GetEndTs() int64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *LiveShowInfo) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *LiveShowInfo) GetRatingFloatConf() *ShowRatingFloat {
	if m != nil {
		return m.RatingFloatConf
	}
	return nil
}

type GetChannelLiveShowInfoResponse struct {
	LiveShowInfo         *LiveShowInfo `protobuf:"bytes,1,opt,name=live_show_info,json=liveShowInfo,proto3" json:"live_show_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelLiveShowInfoResponse) Reset()         { *m = GetChannelLiveShowInfoResponse{} }
func (m *GetChannelLiveShowInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetChannelLiveShowInfoResponse) ProtoMessage()    {}
func (*GetChannelLiveShowInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{20}
}
func (m *GetChannelLiveShowInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLiveShowInfoResponse.Unmarshal(m, b)
}
func (m *GetChannelLiveShowInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLiveShowInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetChannelLiveShowInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLiveShowInfoResponse.Merge(dst, src)
}
func (m *GetChannelLiveShowInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetChannelLiveShowInfoResponse.Size(m)
}
func (m *GetChannelLiveShowInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLiveShowInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLiveShowInfoResponse proto.InternalMessageInfo

func (m *GetChannelLiveShowInfoResponse) GetLiveShowInfo() *LiveShowInfo {
	if m != nil {
		return m.LiveShowInfo
	}
	return nil
}

type RateChannelLiveShowRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ShowId               uint32   `protobuf:"varint,2,opt,name=show_id,json=showId,proto3" json:"show_id,omitempty"`
	Score                uint32   `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RateChannelLiveShowRequest) Reset()         { *m = RateChannelLiveShowRequest{} }
func (m *RateChannelLiveShowRequest) String() string { return proto.CompactTextString(m) }
func (*RateChannelLiveShowRequest) ProtoMessage()    {}
func (*RateChannelLiveShowRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{21}
}
func (m *RateChannelLiveShowRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RateChannelLiveShowRequest.Unmarshal(m, b)
}
func (m *RateChannelLiveShowRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RateChannelLiveShowRequest.Marshal(b, m, deterministic)
}
func (dst *RateChannelLiveShowRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RateChannelLiveShowRequest.Merge(dst, src)
}
func (m *RateChannelLiveShowRequest) XXX_Size() int {
	return xxx_messageInfo_RateChannelLiveShowRequest.Size(m)
}
func (m *RateChannelLiveShowRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RateChannelLiveShowRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RateChannelLiveShowRequest proto.InternalMessageInfo

func (m *RateChannelLiveShowRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RateChannelLiveShowRequest) GetShowId() uint32 {
	if m != nil {
		return m.ShowId
	}
	return 0
}

func (m *RateChannelLiveShowRequest) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type RateChannelLiveShowResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RateChannelLiveShowResponse) Reset()         { *m = RateChannelLiveShowResponse{} }
func (m *RateChannelLiveShowResponse) String() string { return proto.CompactTextString(m) }
func (*RateChannelLiveShowResponse) ProtoMessage()    {}
func (*RateChannelLiveShowResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{22}
}
func (m *RateChannelLiveShowResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RateChannelLiveShowResponse.Unmarshal(m, b)
}
func (m *RateChannelLiveShowResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RateChannelLiveShowResponse.Marshal(b, m, deterministic)
}
func (dst *RateChannelLiveShowResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RateChannelLiveShowResponse.Merge(dst, src)
}
func (m *RateChannelLiveShowResponse) XXX_Size() int {
	return xxx_messageInfo_RateChannelLiveShowResponse.Size(m)
}
func (m *RateChannelLiveShowResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RateChannelLiveShowResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RateChannelLiveShowResponse proto.InternalMessageInfo

type TestToolRequest struct {
	ShowInfo             *TestToolRequest_TestShowBeginPush `protobuf:"bytes,1,opt,name=show_info,json=showInfo,proto3" json:"show_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *TestToolRequest) Reset()         { *m = TestToolRequest{} }
func (m *TestToolRequest) String() string { return proto.CompactTextString(m) }
func (*TestToolRequest) ProtoMessage()    {}
func (*TestToolRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{23}
}
func (m *TestToolRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestToolRequest.Unmarshal(m, b)
}
func (m *TestToolRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestToolRequest.Marshal(b, m, deterministic)
}
func (dst *TestToolRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestToolRequest.Merge(dst, src)
}
func (m *TestToolRequest) XXX_Size() int {
	return xxx_messageInfo_TestToolRequest.Size(m)
}
func (m *TestToolRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TestToolRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TestToolRequest proto.InternalMessageInfo

func (m *TestToolRequest) GetShowInfo() *TestToolRequest_TestShowBeginPush {
	if m != nil {
		return m.ShowInfo
	}
	return nil
}

type TestToolRequest_TestShowBeginPush struct {
	AnchorUid            uint32   `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ShowName             string   `protobuf:"bytes,3,opt,name=show_name,json=showName,proto3" json:"show_name,omitempty"`
	BeginTs              int64    `protobuf:"varint,4,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                int64    `protobuf:"varint,5,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestToolRequest_TestShowBeginPush) Reset()         { *m = TestToolRequest_TestShowBeginPush{} }
func (m *TestToolRequest_TestShowBeginPush) String() string { return proto.CompactTextString(m) }
func (*TestToolRequest_TestShowBeginPush) ProtoMessage()    {}
func (*TestToolRequest_TestShowBeginPush) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{23, 0}
}
func (m *TestToolRequest_TestShowBeginPush) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestToolRequest_TestShowBeginPush.Unmarshal(m, b)
}
func (m *TestToolRequest_TestShowBeginPush) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestToolRequest_TestShowBeginPush.Marshal(b, m, deterministic)
}
func (dst *TestToolRequest_TestShowBeginPush) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestToolRequest_TestShowBeginPush.Merge(dst, src)
}
func (m *TestToolRequest_TestShowBeginPush) XXX_Size() int {
	return xxx_messageInfo_TestToolRequest_TestShowBeginPush.Size(m)
}
func (m *TestToolRequest_TestShowBeginPush) XXX_DiscardUnknown() {
	xxx_messageInfo_TestToolRequest_TestShowBeginPush.DiscardUnknown(m)
}

var xxx_messageInfo_TestToolRequest_TestShowBeginPush proto.InternalMessageInfo

func (m *TestToolRequest_TestShowBeginPush) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *TestToolRequest_TestShowBeginPush) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TestToolRequest_TestShowBeginPush) GetShowName() string {
	if m != nil {
		return m.ShowName
	}
	return ""
}

func (m *TestToolRequest_TestShowBeginPush) GetBeginTs() int64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *TestToolRequest_TestShowBeginPush) GetEndTs() int64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type TestToolResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestToolResponse) Reset()         { *m = TestToolResponse{} }
func (m *TestToolResponse) String() string { return proto.CompactTextString(m) }
func (*TestToolResponse) ProtoMessage()    {}
func (*TestToolResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{24}
}
func (m *TestToolResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestToolResponse.Unmarshal(m, b)
}
func (m *TestToolResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestToolResponse.Marshal(b, m, deterministic)
}
func (dst *TestToolResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestToolResponse.Merge(dst, src)
}
func (m *TestToolResponse) XXX_Size() int {
	return xxx_messageInfo_TestToolResponse.Size(m)
}
func (m *TestToolResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TestToolResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TestToolResponse proto.InternalMessageInfo

type DeclareShowRequest struct {
	ShowName              string   `protobuf:"bytes,1,opt,name=show_name,json=showName,proto3" json:"show_name,omitempty"`
	ShowStartTime         uint32   `protobuf:"varint,2,opt,name=show_start_time,json=showStartTime,proto3" json:"show_start_time,omitempty"`
	ShowEndTime           uint32   `protobuf:"varint,3,opt,name=show_end_time,json=showEndTime,proto3" json:"show_end_time,omitempty"`
	VoiceTagId            uint32   `protobuf:"varint,4,opt,name=voice_tag_id,json=voiceTagId,proto3" json:"voice_tag_id,omitempty"`
	ContentTagId          uint32   `protobuf:"varint,5,opt,name=content_tag_id,json=contentTagId,proto3" json:"content_tag_id,omitempty"`
	ShowDescAudio         string   `protobuf:"bytes,6,opt,name=show_desc_audio,json=showDescAudio,proto3" json:"show_desc_audio,omitempty"`
	ShowDescAudioDuration uint32   `protobuf:"varint,7,opt,name=show_desc_audio_duration,json=showDescAudioDuration,proto3" json:"show_desc_audio_duration,omitempty"`
	ShowCoverImg          string   `protobuf:"bytes,8,opt,name=show_cover_img,json=showCoverImg,proto3" json:"show_cover_img,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *DeclareShowRequest) Reset()         { *m = DeclareShowRequest{} }
func (m *DeclareShowRequest) String() string { return proto.CompactTextString(m) }
func (*DeclareShowRequest) ProtoMessage()    {}
func (*DeclareShowRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{25}
}
func (m *DeclareShowRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeclareShowRequest.Unmarshal(m, b)
}
func (m *DeclareShowRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeclareShowRequest.Marshal(b, m, deterministic)
}
func (dst *DeclareShowRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeclareShowRequest.Merge(dst, src)
}
func (m *DeclareShowRequest) XXX_Size() int {
	return xxx_messageInfo_DeclareShowRequest.Size(m)
}
func (m *DeclareShowRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeclareShowRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeclareShowRequest proto.InternalMessageInfo

func (m *DeclareShowRequest) GetShowName() string {
	if m != nil {
		return m.ShowName
	}
	return ""
}

func (m *DeclareShowRequest) GetShowStartTime() uint32 {
	if m != nil {
		return m.ShowStartTime
	}
	return 0
}

func (m *DeclareShowRequest) GetShowEndTime() uint32 {
	if m != nil {
		return m.ShowEndTime
	}
	return 0
}

func (m *DeclareShowRequest) GetVoiceTagId() uint32 {
	if m != nil {
		return m.VoiceTagId
	}
	return 0
}

func (m *DeclareShowRequest) GetContentTagId() uint32 {
	if m != nil {
		return m.ContentTagId
	}
	return 0
}

func (m *DeclareShowRequest) GetShowDescAudio() string {
	if m != nil {
		return m.ShowDescAudio
	}
	return ""
}

func (m *DeclareShowRequest) GetShowDescAudioDuration() uint32 {
	if m != nil {
		return m.ShowDescAudioDuration
	}
	return 0
}

func (m *DeclareShowRequest) GetShowCoverImg() string {
	if m != nil {
		return m.ShowCoverImg
	}
	return ""
}

type DeclareShowResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeclareShowResponse) Reset()         { *m = DeclareShowResponse{} }
func (m *DeclareShowResponse) String() string { return proto.CompactTextString(m) }
func (*DeclareShowResponse) ProtoMessage()    {}
func (*DeclareShowResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{26}
}
func (m *DeclareShowResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeclareShowResponse.Unmarshal(m, b)
}
func (m *DeclareShowResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeclareShowResponse.Marshal(b, m, deterministic)
}
func (dst *DeclareShowResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeclareShowResponse.Merge(dst, src)
}
func (m *DeclareShowResponse) XXX_Size() int {
	return xxx_messageInfo_DeclareShowResponse.Size(m)
}
func (m *DeclareShowResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeclareShowResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeclareShowResponse proto.InternalMessageInfo

type AddShowTagItem struct {
	TagId                uint32   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	ParentId             uint32   `protobuf:"varint,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddShowTagItem) Reset()         { *m = AddShowTagItem{} }
func (m *AddShowTagItem) String() string { return proto.CompactTextString(m) }
func (*AddShowTagItem) ProtoMessage()    {}
func (*AddShowTagItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{27}
}
func (m *AddShowTagItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddShowTagItem.Unmarshal(m, b)
}
func (m *AddShowTagItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddShowTagItem.Marshal(b, m, deterministic)
}
func (dst *AddShowTagItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddShowTagItem.Merge(dst, src)
}
func (m *AddShowTagItem) XXX_Size() int {
	return xxx_messageInfo_AddShowTagItem.Size(m)
}
func (m *AddShowTagItem) XXX_DiscardUnknown() {
	xxx_messageInfo_AddShowTagItem.DiscardUnknown(m)
}

var xxx_messageInfo_AddShowTagItem proto.InternalMessageInfo

func (m *AddShowTagItem) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *AddShowTagItem) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *AddShowTagItem) GetParentId() uint32 {
	if m != nil {
		return m.ParentId
	}
	return 0
}

type AddShowTagRequest struct {
	TagList              []*AddShowTagItem `protobuf:"bytes,1,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AddShowTagRequest) Reset()         { *m = AddShowTagRequest{} }
func (m *AddShowTagRequest) String() string { return proto.CompactTextString(m) }
func (*AddShowTagRequest) ProtoMessage()    {}
func (*AddShowTagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{28}
}
func (m *AddShowTagRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddShowTagRequest.Unmarshal(m, b)
}
func (m *AddShowTagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddShowTagRequest.Marshal(b, m, deterministic)
}
func (dst *AddShowTagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddShowTagRequest.Merge(dst, src)
}
func (m *AddShowTagRequest) XXX_Size() int {
	return xxx_messageInfo_AddShowTagRequest.Size(m)
}
func (m *AddShowTagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddShowTagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddShowTagRequest proto.InternalMessageInfo

func (m *AddShowTagRequest) GetTagList() []*AddShowTagItem {
	if m != nil {
		return m.TagList
	}
	return nil
}

type AddShowTagResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddShowTagResponse) Reset()         { *m = AddShowTagResponse{} }
func (m *AddShowTagResponse) String() string { return proto.CompactTextString(m) }
func (*AddShowTagResponse) ProtoMessage()    {}
func (*AddShowTagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{29}
}
func (m *AddShowTagResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddShowTagResponse.Unmarshal(m, b)
}
func (m *AddShowTagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddShowTagResponse.Marshal(b, m, deterministic)
}
func (dst *AddShowTagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddShowTagResponse.Merge(dst, src)
}
func (m *AddShowTagResponse) XXX_Size() int {
	return xxx_messageInfo_AddShowTagResponse.Size(m)
}
func (m *AddShowTagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddShowTagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddShowTagResponse proto.InternalMessageInfo

type DeleteShowTagRequest struct {
	TagIdList            []uint32 `protobuf:"varint,1,rep,packed,name=tag_id_list,json=tagIdList,proto3" json:"tag_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteShowTagRequest) Reset()         { *m = DeleteShowTagRequest{} }
func (m *DeleteShowTagRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteShowTagRequest) ProtoMessage()    {}
func (*DeleteShowTagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{30}
}
func (m *DeleteShowTagRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteShowTagRequest.Unmarshal(m, b)
}
func (m *DeleteShowTagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteShowTagRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteShowTagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteShowTagRequest.Merge(dst, src)
}
func (m *DeleteShowTagRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteShowTagRequest.Size(m)
}
func (m *DeleteShowTagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteShowTagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteShowTagRequest proto.InternalMessageInfo

func (m *DeleteShowTagRequest) GetTagIdList() []uint32 {
	if m != nil {
		return m.TagIdList
	}
	return nil
}

type DeleteShowTagResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteShowTagResponse) Reset()         { *m = DeleteShowTagResponse{} }
func (m *DeleteShowTagResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteShowTagResponse) ProtoMessage()    {}
func (*DeleteShowTagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{31}
}
func (m *DeleteShowTagResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteShowTagResponse.Unmarshal(m, b)
}
func (m *DeleteShowTagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteShowTagResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteShowTagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteShowTagResponse.Merge(dst, src)
}
func (m *DeleteShowTagResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteShowTagResponse.Size(m)
}
func (m *DeleteShowTagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteShowTagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteShowTagResponse proto.InternalMessageInfo

type GetShowListUserBaseInfoRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShowListUserBaseInfoRequest) Reset()         { *m = GetShowListUserBaseInfoRequest{} }
func (m *GetShowListUserBaseInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetShowListUserBaseInfoRequest) ProtoMessage()    {}
func (*GetShowListUserBaseInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{32}
}
func (m *GetShowListUserBaseInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowListUserBaseInfoRequest.Unmarshal(m, b)
}
func (m *GetShowListUserBaseInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowListUserBaseInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetShowListUserBaseInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowListUserBaseInfoRequest.Merge(dst, src)
}
func (m *GetShowListUserBaseInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetShowListUserBaseInfoRequest.Size(m)
}
func (m *GetShowListUserBaseInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowListUserBaseInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowListUserBaseInfoRequest proto.InternalMessageInfo

func (m *GetShowListUserBaseInfoRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetShowListUserBaseInfoResponse struct {
	RemainDeclareCnt     uint32   `protobuf:"varint,1,opt,name=remain_declare_cnt,json=remainDeclareCnt,proto3" json:"remain_declare_cnt,omitempty"`
	ShowApplyEntry       bool     `protobuf:"varint,2,opt,name=show_apply_entry,json=showApplyEntry,proto3" json:"show_apply_entry,omitempty"`
	UnableReason         string   `protobuf:"bytes,3,opt,name=unable_reason,json=unableReason,proto3" json:"unable_reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShowListUserBaseInfoResponse) Reset()         { *m = GetShowListUserBaseInfoResponse{} }
func (m *GetShowListUserBaseInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetShowListUserBaseInfoResponse) ProtoMessage()    {}
func (*GetShowListUserBaseInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{33}
}
func (m *GetShowListUserBaseInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowListUserBaseInfoResponse.Unmarshal(m, b)
}
func (m *GetShowListUserBaseInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowListUserBaseInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetShowListUserBaseInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowListUserBaseInfoResponse.Merge(dst, src)
}
func (m *GetShowListUserBaseInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetShowListUserBaseInfoResponse.Size(m)
}
func (m *GetShowListUserBaseInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowListUserBaseInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowListUserBaseInfoResponse proto.InternalMessageInfo

func (m *GetShowListUserBaseInfoResponse) GetRemainDeclareCnt() uint32 {
	if m != nil {
		return m.RemainDeclareCnt
	}
	return 0
}

func (m *GetShowListUserBaseInfoResponse) GetShowApplyEntry() bool {
	if m != nil {
		return m.ShowApplyEntry
	}
	return false
}

func (m *GetShowListUserBaseInfoResponse) GetUnableReason() string {
	if m != nil {
		return m.UnableReason
	}
	return ""
}

type SetApprovalAuditTypeRequest struct {
	ApprovalId                        uint32   `protobuf:"varint,1,opt,name=approval_id,json=approvalId,proto3" json:"approval_id,omitempty"`
	ChannelLiveShowApprovalAuditScene uint32   `protobuf:"varint,2,opt,name=channel_live_show_approval_audit_scene,json=channelLiveShowApprovalAuditScene,proto3" json:"channel_live_show_approval_audit_scene,omitempty"`
	ChannelLiveShowApprovalAuditType  uint32   `protobuf:"varint,3,opt,name=channel_live_show_approval_audit_type,json=channelLiveShowApprovalAuditType,proto3" json:"channel_live_show_approval_audit_type,omitempty"`
	XXX_NoUnkeyedLiteral              struct{} `json:"-"`
	XXX_unrecognized                  []byte   `json:"-"`
	XXX_sizecache                     int32    `json:"-"`
}

func (m *SetApprovalAuditTypeRequest) Reset()         { *m = SetApprovalAuditTypeRequest{} }
func (m *SetApprovalAuditTypeRequest) String() string { return proto.CompactTextString(m) }
func (*SetApprovalAuditTypeRequest) ProtoMessage()    {}
func (*SetApprovalAuditTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{34}
}
func (m *SetApprovalAuditTypeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetApprovalAuditTypeRequest.Unmarshal(m, b)
}
func (m *SetApprovalAuditTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetApprovalAuditTypeRequest.Marshal(b, m, deterministic)
}
func (dst *SetApprovalAuditTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetApprovalAuditTypeRequest.Merge(dst, src)
}
func (m *SetApprovalAuditTypeRequest) XXX_Size() int {
	return xxx_messageInfo_SetApprovalAuditTypeRequest.Size(m)
}
func (m *SetApprovalAuditTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetApprovalAuditTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetApprovalAuditTypeRequest proto.InternalMessageInfo

func (m *SetApprovalAuditTypeRequest) GetApprovalId() uint32 {
	if m != nil {
		return m.ApprovalId
	}
	return 0
}

func (m *SetApprovalAuditTypeRequest) GetChannelLiveShowApprovalAuditScene() uint32 {
	if m != nil {
		return m.ChannelLiveShowApprovalAuditScene
	}
	return 0
}

func (m *SetApprovalAuditTypeRequest) GetChannelLiveShowApprovalAuditType() uint32 {
	if m != nil {
		return m.ChannelLiveShowApprovalAuditType
	}
	return 0
}

type SetApprovalAuditTypeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetApprovalAuditTypeResponse) Reset()         { *m = SetApprovalAuditTypeResponse{} }
func (m *SetApprovalAuditTypeResponse) String() string { return proto.CompactTextString(m) }
func (*SetApprovalAuditTypeResponse) ProtoMessage()    {}
func (*SetApprovalAuditTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{35}
}
func (m *SetApprovalAuditTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetApprovalAuditTypeResponse.Unmarshal(m, b)
}
func (m *SetApprovalAuditTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetApprovalAuditTypeResponse.Marshal(b, m, deterministic)
}
func (dst *SetApprovalAuditTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetApprovalAuditTypeResponse.Merge(dst, src)
}
func (m *SetApprovalAuditTypeResponse) XXX_Size() int {
	return xxx_messageInfo_SetApprovalAuditTypeResponse.Size(m)
}
func (m *SetApprovalAuditTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetApprovalAuditTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetApprovalAuditTypeResponse proto.InternalMessageInfo

type UnfoldShowListRequest struct {
	BeginTime            uint32   `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	VoiceTagIdList       []uint32 `protobuf:"varint,3,rep,packed,name=voice_tag_id_list,json=voiceTagIdList,proto3" json:"voice_tag_id_list,omitempty"`
	ContentTagIdList     []uint32 `protobuf:"varint,4,rep,packed,name=content_tag_id_list,json=contentTagIdList,proto3" json:"content_tag_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnfoldShowListRequest) Reset()         { *m = UnfoldShowListRequest{} }
func (m *UnfoldShowListRequest) String() string { return proto.CompactTextString(m) }
func (*UnfoldShowListRequest) ProtoMessage()    {}
func (*UnfoldShowListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{36}
}
func (m *UnfoldShowListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfoldShowListRequest.Unmarshal(m, b)
}
func (m *UnfoldShowListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfoldShowListRequest.Marshal(b, m, deterministic)
}
func (dst *UnfoldShowListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfoldShowListRequest.Merge(dst, src)
}
func (m *UnfoldShowListRequest) XXX_Size() int {
	return xxx_messageInfo_UnfoldShowListRequest.Size(m)
}
func (m *UnfoldShowListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfoldShowListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UnfoldShowListRequest proto.InternalMessageInfo

func (m *UnfoldShowListRequest) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *UnfoldShowListRequest) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *UnfoldShowListRequest) GetVoiceTagIdList() []uint32 {
	if m != nil {
		return m.VoiceTagIdList
	}
	return nil
}

func (m *UnfoldShowListRequest) GetContentTagIdList() []uint32 {
	if m != nil {
		return m.ContentTagIdList
	}
	return nil
}

type UnfoldShowListResponse struct {
	UnfoldShowList       []*ShowItem `protobuf:"bytes,1,rep,name=unfold_show_list,json=unfoldShowList,proto3" json:"unfold_show_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UnfoldShowListResponse) Reset()         { *m = UnfoldShowListResponse{} }
func (m *UnfoldShowListResponse) String() string { return proto.CompactTextString(m) }
func (*UnfoldShowListResponse) ProtoMessage()    {}
func (*UnfoldShowListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_show_list_e8f7ce354736083e, []int{37}
}
func (m *UnfoldShowListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfoldShowListResponse.Unmarshal(m, b)
}
func (m *UnfoldShowListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfoldShowListResponse.Marshal(b, m, deterministic)
}
func (dst *UnfoldShowListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfoldShowListResponse.Merge(dst, src)
}
func (m *UnfoldShowListResponse) XXX_Size() int {
	return xxx_messageInfo_UnfoldShowListResponse.Size(m)
}
func (m *UnfoldShowListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfoldShowListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UnfoldShowListResponse proto.InternalMessageInfo

func (m *UnfoldShowListResponse) GetUnfoldShowList() []*ShowItem {
	if m != nil {
		return m.UnfoldShowList
	}
	return nil
}

func init() {
	proto.RegisterType((*GetChannelLiveShowApprovalListRequest)(nil), "channel_live_show_list.GetChannelLiveShowApprovalListRequest")
	proto.RegisterType((*ChannelLiveShowApprovalItem)(nil), "channel_live_show_list.ChannelLiveShowApprovalItem")
	proto.RegisterType((*GetChannelLiveShowApprovalListResp)(nil), "channel_live_show_list.GetChannelLiveShowApprovalListResp")
	proto.RegisterType((*GetShowTagRequest)(nil), "channel_live_show_list.GetShowTagRequest")
	proto.RegisterType((*TagNode)(nil), "channel_live_show_list.TagNode")
	proto.RegisterType((*GetShowTagResponse)(nil), "channel_live_show_list.GetShowTagResponse")
	proto.RegisterType((*ModifyShowApprovalTagRequest)(nil), "channel_live_show_list.ModifyShowApprovalTagRequest")
	proto.RegisterType((*ModifyShowApprovalTagResponse)(nil), "channel_live_show_list.ModifyShowApprovalTagResponse")
	proto.RegisterType((*HandleShowApprovalRequest)(nil), "channel_live_show_list.HandleShowApprovalRequest")
	proto.RegisterType((*HandelShowApprovalResponse)(nil), "channel_live_show_list.HandelShowApprovalResponse")
	proto.RegisterType((*GetShowTimeRequest)(nil), "channel_live_show_list.GetShowTimeRequest")
	proto.RegisterType((*GetShowTimeResponse)(nil), "channel_live_show_list.GetShowTimeResponse")
	proto.RegisterType((*GetShowTimeResponse_ShowTime)(nil), "channel_live_show_list.GetShowTimeResponse.ShowTime")
	proto.RegisterType((*GetLiveShowEntryInfoRequest)(nil), "channel_live_show_list.GetLiveShowEntryInfoRequest")
	proto.RegisterType((*GetLiveShowEntryInfoResponse)(nil), "channel_live_show_list.GetLiveShowEntryInfoResponse")
	proto.RegisterType((*GetShowListRequest)(nil), "channel_live_show_list.GetShowListRequest")
	proto.RegisterType((*ShowItem)(nil), "channel_live_show_list.ShowItem")
	proto.RegisterType((*GetShowListResponse)(nil), "channel_live_show_list.GetShowListResponse")
	proto.RegisterType((*GetShowListResponse_ShowGroupItem)(nil), "channel_live_show_list.GetShowListResponse.ShowGroupItem")
	proto.RegisterType((*GetChannelLiveShowInfoRequest)(nil), "channel_live_show_list.GetChannelLiveShowInfoRequest")
	proto.RegisterType((*ShowRatingFloat)(nil), "channel_live_show_list.ShowRatingFloat")
	proto.RegisterType((*LiveShowInfo)(nil), "channel_live_show_list.LiveShowInfo")
	proto.RegisterType((*GetChannelLiveShowInfoResponse)(nil), "channel_live_show_list.GetChannelLiveShowInfoResponse")
	proto.RegisterType((*RateChannelLiveShowRequest)(nil), "channel_live_show_list.RateChannelLiveShowRequest")
	proto.RegisterType((*RateChannelLiveShowResponse)(nil), "channel_live_show_list.RateChannelLiveShowResponse")
	proto.RegisterType((*TestToolRequest)(nil), "channel_live_show_list.TestToolRequest")
	proto.RegisterType((*TestToolRequest_TestShowBeginPush)(nil), "channel_live_show_list.TestToolRequest.TestShowBeginPush")
	proto.RegisterType((*TestToolResponse)(nil), "channel_live_show_list.TestToolResponse")
	proto.RegisterType((*DeclareShowRequest)(nil), "channel_live_show_list.DeclareShowRequest")
	proto.RegisterType((*DeclareShowResponse)(nil), "channel_live_show_list.DeclareShowResponse")
	proto.RegisterType((*AddShowTagItem)(nil), "channel_live_show_list.AddShowTagItem")
	proto.RegisterType((*AddShowTagRequest)(nil), "channel_live_show_list.AddShowTagRequest")
	proto.RegisterType((*AddShowTagResponse)(nil), "channel_live_show_list.AddShowTagResponse")
	proto.RegisterType((*DeleteShowTagRequest)(nil), "channel_live_show_list.DeleteShowTagRequest")
	proto.RegisterType((*DeleteShowTagResponse)(nil), "channel_live_show_list.DeleteShowTagResponse")
	proto.RegisterType((*GetShowListUserBaseInfoRequest)(nil), "channel_live_show_list.GetShowListUserBaseInfoRequest")
	proto.RegisterType((*GetShowListUserBaseInfoResponse)(nil), "channel_live_show_list.GetShowListUserBaseInfoResponse")
	proto.RegisterType((*SetApprovalAuditTypeRequest)(nil), "channel_live_show_list.SetApprovalAuditTypeRequest")
	proto.RegisterType((*SetApprovalAuditTypeResponse)(nil), "channel_live_show_list.SetApprovalAuditTypeResponse")
	proto.RegisterType((*UnfoldShowListRequest)(nil), "channel_live_show_list.UnfoldShowListRequest")
	proto.RegisterType((*UnfoldShowListResponse)(nil), "channel_live_show_list.UnfoldShowListResponse")
	proto.RegisterEnum("channel_live_show_list.ChannelLiveShowApprovalAuditType", ChannelLiveShowApprovalAuditType_name, ChannelLiveShowApprovalAuditType_value)
	proto.RegisterEnum("channel_live_show_list.ChannelLiveShowApprovalAuditScene", ChannelLiveShowApprovalAuditScene_name, ChannelLiveShowApprovalAuditScene_value)
	proto.RegisterEnum("channel_live_show_list.GetChannelLiveShowApprovalListRequest_ApprovalFilterStatus", GetChannelLiveShowApprovalListRequest_ApprovalFilterStatus_name, GetChannelLiveShowApprovalListRequest_ApprovalFilterStatus_value)
	proto.RegisterEnum("channel_live_show_list.ChannelLiveShowApprovalItem_ApprovalStatus", ChannelLiveShowApprovalItem_ApprovalStatus_name, ChannelLiveShowApprovalItem_ApprovalStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelLiveShowListClient is the client API for ChannelLiveShowList service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelLiveShowListClient interface {
	// 获取审批记录
	GetChannelLiveShowApprovalList(ctx context.Context, in *GetChannelLiveShowApprovalListRequest, opts ...grpc.CallOption) (*GetChannelLiveShowApprovalListResp, error)
	// 获取标签配置
	GetShowTag(ctx context.Context, in *GetShowTagRequest, opts ...grpc.CallOption) (*GetShowTagResponse, error)
	// 修改标签
	ModifyShowApprovalTag(ctx context.Context, in *ModifyShowApprovalTagRequest, opts ...grpc.CallOption) (*ModifyShowApprovalTagResponse, error)
	// 处理审批
	HandleShowApproval(ctx context.Context, in *HandleShowApprovalRequest, opts ...grpc.CallOption) (*HandelShowApprovalResponse, error)
	// 获取可申报时段
	GetShowTime(ctx context.Context, in *GetShowTimeRequest, opts ...grpc.CallOption) (*GetShowTimeResponse, error)
	// 根据主播uid、cid获取直播间节目信息
	GetChannelLiveShowInfo(ctx context.Context, in *GetChannelLiveShowInfoRequest, opts ...grpc.CallOption) (*GetChannelLiveShowInfoResponse, error)
	// 节目评分
	RateChannelLiveShow(ctx context.Context, in *RateChannelLiveShowRequest, opts ...grpc.CallOption) (*RateChannelLiveShowResponse, error)
	TestTool(ctx context.Context, in *TestToolRequest, opts ...grpc.CallOption) (*TestToolResponse, error)
	// 获取节目入口信息
	GetLiveShowEntryInfo(ctx context.Context, in *GetLiveShowEntryInfoRequest, opts ...grpc.CallOption) (*GetLiveShowEntryInfoResponse, error)
	// 获取节目列表
	GetShowList(ctx context.Context, in *GetShowListRequest, opts ...grpc.CallOption) (*GetShowListResponse, error)
	// 展开节目
	UnfoldShowList(ctx context.Context, in *UnfoldShowListRequest, opts ...grpc.CallOption) (*UnfoldShowListResponse, error)
	// 申报节目
	DeclareShow(ctx context.Context, in *DeclareShowRequest, opts ...grpc.CallOption) (*DeclareShowResponse, error)
	// 添加标签
	AddShowTag(ctx context.Context, in *AddShowTagRequest, opts ...grpc.CallOption) (*AddShowTagResponse, error)
	// 删除标签
	DeleteShowTag(ctx context.Context, in *DeleteShowTagRequest, opts ...grpc.CallOption) (*DeleteShowTagResponse, error)
	// 获取用户可申报次数
	GetShowListUserBaseInfo(ctx context.Context, in *GetShowListUserBaseInfoRequest, opts ...grpc.CallOption) (*GetShowListUserBaseInfoResponse, error)
	// 设置审核状态
	SetApprovalAuditType(ctx context.Context, in *SetApprovalAuditTypeRequest, opts ...grpc.CallOption) (*SetApprovalAuditTypeResponse, error)
}

type channelLiveShowListClient struct {
	cc *grpc.ClientConn
}

func NewChannelLiveShowListClient(cc *grpc.ClientConn) ChannelLiveShowListClient {
	return &channelLiveShowListClient{cc}
}

func (c *channelLiveShowListClient) GetChannelLiveShowApprovalList(ctx context.Context, in *GetChannelLiveShowApprovalListRequest, opts ...grpc.CallOption) (*GetChannelLiveShowApprovalListResp, error) {
	out := new(GetChannelLiveShowApprovalListResp)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/GetChannelLiveShowApprovalList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveShowListClient) GetShowTag(ctx context.Context, in *GetShowTagRequest, opts ...grpc.CallOption) (*GetShowTagResponse, error) {
	out := new(GetShowTagResponse)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/GetShowTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveShowListClient) ModifyShowApprovalTag(ctx context.Context, in *ModifyShowApprovalTagRequest, opts ...grpc.CallOption) (*ModifyShowApprovalTagResponse, error) {
	out := new(ModifyShowApprovalTagResponse)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/ModifyShowApprovalTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveShowListClient) HandleShowApproval(ctx context.Context, in *HandleShowApprovalRequest, opts ...grpc.CallOption) (*HandelShowApprovalResponse, error) {
	out := new(HandelShowApprovalResponse)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/HandleShowApproval", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveShowListClient) GetShowTime(ctx context.Context, in *GetShowTimeRequest, opts ...grpc.CallOption) (*GetShowTimeResponse, error) {
	out := new(GetShowTimeResponse)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/GetShowTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveShowListClient) GetChannelLiveShowInfo(ctx context.Context, in *GetChannelLiveShowInfoRequest, opts ...grpc.CallOption) (*GetChannelLiveShowInfoResponse, error) {
	out := new(GetChannelLiveShowInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/GetChannelLiveShowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveShowListClient) RateChannelLiveShow(ctx context.Context, in *RateChannelLiveShowRequest, opts ...grpc.CallOption) (*RateChannelLiveShowResponse, error) {
	out := new(RateChannelLiveShowResponse)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/RateChannelLiveShow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveShowListClient) TestTool(ctx context.Context, in *TestToolRequest, opts ...grpc.CallOption) (*TestToolResponse, error) {
	out := new(TestToolResponse)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/TestTool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveShowListClient) GetLiveShowEntryInfo(ctx context.Context, in *GetLiveShowEntryInfoRequest, opts ...grpc.CallOption) (*GetLiveShowEntryInfoResponse, error) {
	out := new(GetLiveShowEntryInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/GetLiveShowEntryInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveShowListClient) GetShowList(ctx context.Context, in *GetShowListRequest, opts ...grpc.CallOption) (*GetShowListResponse, error) {
	out := new(GetShowListResponse)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/GetShowList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveShowListClient) UnfoldShowList(ctx context.Context, in *UnfoldShowListRequest, opts ...grpc.CallOption) (*UnfoldShowListResponse, error) {
	out := new(UnfoldShowListResponse)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/UnfoldShowList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveShowListClient) DeclareShow(ctx context.Context, in *DeclareShowRequest, opts ...grpc.CallOption) (*DeclareShowResponse, error) {
	out := new(DeclareShowResponse)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/DeclareShow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveShowListClient) AddShowTag(ctx context.Context, in *AddShowTagRequest, opts ...grpc.CallOption) (*AddShowTagResponse, error) {
	out := new(AddShowTagResponse)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/AddShowTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveShowListClient) DeleteShowTag(ctx context.Context, in *DeleteShowTagRequest, opts ...grpc.CallOption) (*DeleteShowTagResponse, error) {
	out := new(DeleteShowTagResponse)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/DeleteShowTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveShowListClient) GetShowListUserBaseInfo(ctx context.Context, in *GetShowListUserBaseInfoRequest, opts ...grpc.CallOption) (*GetShowListUserBaseInfoResponse, error) {
	out := new(GetShowListUserBaseInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/GetShowListUserBaseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveShowListClient) SetApprovalAuditType(ctx context.Context, in *SetApprovalAuditTypeRequest, opts ...grpc.CallOption) (*SetApprovalAuditTypeResponse, error) {
	out := new(SetApprovalAuditTypeResponse)
	err := c.cc.Invoke(ctx, "/channel_live_show_list.ChannelLiveShowList/SetApprovalAuditType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelLiveShowListServer is the server API for ChannelLiveShowList service.
type ChannelLiveShowListServer interface {
	// 获取审批记录
	GetChannelLiveShowApprovalList(context.Context, *GetChannelLiveShowApprovalListRequest) (*GetChannelLiveShowApprovalListResp, error)
	// 获取标签配置
	GetShowTag(context.Context, *GetShowTagRequest) (*GetShowTagResponse, error)
	// 修改标签
	ModifyShowApprovalTag(context.Context, *ModifyShowApprovalTagRequest) (*ModifyShowApprovalTagResponse, error)
	// 处理审批
	HandleShowApproval(context.Context, *HandleShowApprovalRequest) (*HandelShowApprovalResponse, error)
	// 获取可申报时段
	GetShowTime(context.Context, *GetShowTimeRequest) (*GetShowTimeResponse, error)
	// 根据主播uid、cid获取直播间节目信息
	GetChannelLiveShowInfo(context.Context, *GetChannelLiveShowInfoRequest) (*GetChannelLiveShowInfoResponse, error)
	// 节目评分
	RateChannelLiveShow(context.Context, *RateChannelLiveShowRequest) (*RateChannelLiveShowResponse, error)
	TestTool(context.Context, *TestToolRequest) (*TestToolResponse, error)
	// 获取节目入口信息
	GetLiveShowEntryInfo(context.Context, *GetLiveShowEntryInfoRequest) (*GetLiveShowEntryInfoResponse, error)
	// 获取节目列表
	GetShowList(context.Context, *GetShowListRequest) (*GetShowListResponse, error)
	// 展开节目
	UnfoldShowList(context.Context, *UnfoldShowListRequest) (*UnfoldShowListResponse, error)
	// 申报节目
	DeclareShow(context.Context, *DeclareShowRequest) (*DeclareShowResponse, error)
	// 添加标签
	AddShowTag(context.Context, *AddShowTagRequest) (*AddShowTagResponse, error)
	// 删除标签
	DeleteShowTag(context.Context, *DeleteShowTagRequest) (*DeleteShowTagResponse, error)
	// 获取用户可申报次数
	GetShowListUserBaseInfo(context.Context, *GetShowListUserBaseInfoRequest) (*GetShowListUserBaseInfoResponse, error)
	// 设置审核状态
	SetApprovalAuditType(context.Context, *SetApprovalAuditTypeRequest) (*SetApprovalAuditTypeResponse, error)
}

func RegisterChannelLiveShowListServer(s *grpc.Server, srv ChannelLiveShowListServer) {
	s.RegisterService(&_ChannelLiveShowList_serviceDesc, srv)
}

func _ChannelLiveShowList_GetChannelLiveShowApprovalList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelLiveShowApprovalListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).GetChannelLiveShowApprovalList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/GetChannelLiveShowApprovalList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).GetChannelLiveShowApprovalList(ctx, req.(*GetChannelLiveShowApprovalListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveShowList_GetShowTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetShowTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).GetShowTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/GetShowTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).GetShowTag(ctx, req.(*GetShowTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveShowList_ModifyShowApprovalTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyShowApprovalTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).ModifyShowApprovalTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/ModifyShowApprovalTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).ModifyShowApprovalTag(ctx, req.(*ModifyShowApprovalTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveShowList_HandleShowApproval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleShowApprovalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).HandleShowApproval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/HandleShowApproval",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).HandleShowApproval(ctx, req.(*HandleShowApprovalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveShowList_GetShowTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetShowTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).GetShowTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/GetShowTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).GetShowTime(ctx, req.(*GetShowTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveShowList_GetChannelLiveShowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelLiveShowInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).GetChannelLiveShowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/GetChannelLiveShowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).GetChannelLiveShowInfo(ctx, req.(*GetChannelLiveShowInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveShowList_RateChannelLiveShow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RateChannelLiveShowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).RateChannelLiveShow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/RateChannelLiveShow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).RateChannelLiveShow(ctx, req.(*RateChannelLiveShowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveShowList_TestTool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestToolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).TestTool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/TestTool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).TestTool(ctx, req.(*TestToolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveShowList_GetLiveShowEntryInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveShowEntryInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).GetLiveShowEntryInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/GetLiveShowEntryInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).GetLiveShowEntryInfo(ctx, req.(*GetLiveShowEntryInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveShowList_GetShowList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetShowListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).GetShowList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/GetShowList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).GetShowList(ctx, req.(*GetShowListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveShowList_UnfoldShowList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnfoldShowListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).UnfoldShowList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/UnfoldShowList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).UnfoldShowList(ctx, req.(*UnfoldShowListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveShowList_DeclareShow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeclareShowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).DeclareShow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/DeclareShow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).DeclareShow(ctx, req.(*DeclareShowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveShowList_AddShowTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddShowTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).AddShowTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/AddShowTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).AddShowTag(ctx, req.(*AddShowTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveShowList_DeleteShowTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteShowTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).DeleteShowTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/DeleteShowTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).DeleteShowTag(ctx, req.(*DeleteShowTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveShowList_GetShowListUserBaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetShowListUserBaseInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).GetShowListUserBaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/GetShowListUserBaseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).GetShowListUserBaseInfo(ctx, req.(*GetShowListUserBaseInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveShowList_SetApprovalAuditType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetApprovalAuditTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveShowListServer).SetApprovalAuditType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_show_list.ChannelLiveShowList/SetApprovalAuditType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveShowListServer).SetApprovalAuditType(ctx, req.(*SetApprovalAuditTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelLiveShowList_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_live_show_list.ChannelLiveShowList",
	HandlerType: (*ChannelLiveShowListServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelLiveShowApprovalList",
			Handler:    _ChannelLiveShowList_GetChannelLiveShowApprovalList_Handler,
		},
		{
			MethodName: "GetShowTag",
			Handler:    _ChannelLiveShowList_GetShowTag_Handler,
		},
		{
			MethodName: "ModifyShowApprovalTag",
			Handler:    _ChannelLiveShowList_ModifyShowApprovalTag_Handler,
		},
		{
			MethodName: "HandleShowApproval",
			Handler:    _ChannelLiveShowList_HandleShowApproval_Handler,
		},
		{
			MethodName: "GetShowTime",
			Handler:    _ChannelLiveShowList_GetShowTime_Handler,
		},
		{
			MethodName: "GetChannelLiveShowInfo",
			Handler:    _ChannelLiveShowList_GetChannelLiveShowInfo_Handler,
		},
		{
			MethodName: "RateChannelLiveShow",
			Handler:    _ChannelLiveShowList_RateChannelLiveShow_Handler,
		},
		{
			MethodName: "TestTool",
			Handler:    _ChannelLiveShowList_TestTool_Handler,
		},
		{
			MethodName: "GetLiveShowEntryInfo",
			Handler:    _ChannelLiveShowList_GetLiveShowEntryInfo_Handler,
		},
		{
			MethodName: "GetShowList",
			Handler:    _ChannelLiveShowList_GetShowList_Handler,
		},
		{
			MethodName: "UnfoldShowList",
			Handler:    _ChannelLiveShowList_UnfoldShowList_Handler,
		},
		{
			MethodName: "DeclareShow",
			Handler:    _ChannelLiveShowList_DeclareShow_Handler,
		},
		{
			MethodName: "AddShowTag",
			Handler:    _ChannelLiveShowList_AddShowTag_Handler,
		},
		{
			MethodName: "DeleteShowTag",
			Handler:    _ChannelLiveShowList_DeleteShowTag_Handler,
		},
		{
			MethodName: "GetShowListUserBaseInfo",
			Handler:    _ChannelLiveShowList_GetShowListUserBaseInfo_Handler,
		},
		{
			MethodName: "SetApprovalAuditType",
			Handler:    _ChannelLiveShowList_SetApprovalAuditType_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-live-show-list/channel-live-show-list.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-live-show-list/channel-live-show-list.proto", fileDescriptor_channel_live_show_list_e8f7ce354736083e)
}

var fileDescriptor_channel_live_show_list_e8f7ce354736083e = []byte{
	// 2372 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x59, 0x4b, 0x6f, 0x1b, 0xc9,
	0xf1, 0xd7, 0x90, 0x7a, 0x90, 0x25, 0x92, 0xa2, 0x5a, 0x92, 0x4d, 0x53, 0xd6, 0x5a, 0x3b, 0xeb,
	0x87, 0x2c, 0x5b, 0xf2, 0xff, 0x2f, 0x3f, 0x76, 0xb3, 0xc8, 0x2e, 0x22, 0xcb, 0x5e, 0x47, 0xc1,
	0xae, 0xed, 0x90, 0xb2, 0x11, 0x2c, 0xb0, 0x18, 0x8c, 0x67, 0x5a, 0xe4, 0xc4, 0xc3, 0x19, 0x7a,
	0xba, 0x29, 0xaf, 0x1c, 0x04, 0x08, 0x10, 0x24, 0x40, 0x80, 0x3d, 0xe4, 0x90, 0x63, 0x12, 0x04,
	0x39, 0xe4, 0x90, 0x7c, 0x80, 0xfd, 0x12, 0x39, 0x05, 0xb9, 0xe4, 0x9a, 0x63, 0xae, 0xf9, 0x02,
	0x41, 0x57, 0xf7, 0xbc, 0xc8, 0x19, 0x3e, 0xf6, 0xc6, 0xa9, 0xae, 0xae, 0xae, 0xfa, 0xd5, 0xb3,
	0x9b, 0xf0, 0x90, 0xf3, 0x3b, 0x6f, 0x06, 0x8e, 0xf5, 0x9a, 0x39, 0xee, 0x19, 0x0d, 0xee, 0x58,
	0x5d, 0xd3, 0xf3, 0xa8, 0xbb, 0xe7, 0x3a, 0x67, 0x74, 0x8f, 0x75, 0xfd, 0xb7, 0x7b, 0xae, 0xc3,
	0x78, 0x0e, 0x79, 0xbf, 0x1f, 0xf8, 0xdc, 0x27, 0x17, 0xd4, 0xaa, 0x21, 0x56, 0x0d, 0xb1, 0x6a,
	0x88, 0x55, 0xfd, 0x57, 0x05, 0xb8, 0xf6, 0x84, 0xf2, 0x23, 0xb9, 0xfa, 0xb9, 0x73, 0x46, 0xdb,
	0x5d, 0xff, 0xed, 0x61, 0xbf, 0x1f, 0xf8, 0x67, 0xa6, 0xfb, 0xb9, 0xc3, 0x78, 0x8b, 0xbe, 0x19,
	0x50, 0xc6, 0x49, 0x13, 0x4a, 0xb6, 0xc9, 0x29, 0x77, 0x7a, 0xb4, 0xa1, 0x6d, 0x6b, 0x3b, 0xd5,
	0x56, 0xf4, 0x4d, 0x08, 0xcc, 0x73, 0xee, 0xd8, 0x8d, 0x02, 0xd2, 0xf1, 0x37, 0xb9, 0x07, 0x17,
	0x4c, 0x25, 0xc6, 0x38, 0x75, 0x5c, 0x4e, 0x03, 0x83, 0x71, 0x93, 0x0f, 0x58, 0xa3, 0x88, 0x5c,
	0xeb, 0xe1, 0xea, 0x67, 0xb8, 0xd8, 0xc6, 0x35, 0x72, 0x09, 0x4a, 0x7d, 0xb3, 0x43, 0x0d, 0x6f,
	0xd0, 0x6b, 0xcc, 0x23, 0xdf, 0x92, 0xf8, 0x7e, 0x3a, 0xe8, 0x91, 0x4d, 0x28, 0xe3, 0x12, 0x73,
	0xde, 0xd1, 0xc6, 0x82, 0xd4, 0x40, 0x10, 0xda, 0xce, 0x3b, 0xaa, 0x7f, 0x0a, 0xeb, 0x87, 0x59,
	0xf2, 0x96, 0x61, 0xe9, 0x85, 0xf7, 0xda, 0xf3, 0xdf, 0x7a, 0xf5, 0x39, 0xf1, 0xf1, 0x9c, 0x7a,
	0xb6, 0xe3, 0x75, 0xea, 0x1a, 0xa9, 0x40, 0x49, 0xee, 0xa0, 0x76, 0xbd, 0xa0, 0x7f, 0xbb, 0x00,
	0x9b, 0x39, 0x20, 0x1c, 0x73, 0xda, 0x23, 0x3b, 0x50, 0x47, 0xd0, 0x22, 0x93, 0x1c, 0x5b, 0xa1,
	0x50, 0x63, 0x49, 0x5e, 0x9b, 0xdc, 0x80, 0x15, 0xd3, 0xb3, 0xba, 0x7e, 0x60, 0x78, 0x8e, 0xf5,
	0xda, 0x33, 0x7b, 0x14, 0x61, 0x29, 0xb7, 0x6a, 0x92, 0xfc, 0x54, 0x51, 0xc9, 0x15, 0x58, 0x56,
	0x8c, 0x88, 0x5d, 0x11, 0x99, 0x40, 0x92, 0x4e, 0x04, 0x82, 0xd7, 0x61, 0x05, 0xcf, 0x64, 0xdc,
	0x0c, 0xb8, 0x81, 0xc0, 0x4b, 0x48, 0xaa, 0x82, 0xdc, 0x16, 0xd4, 0x13, 0x81, 0xbe, 0x0e, 0x48,
	0x30, 0xa8, 0x67, 0x4b, 0x2e, 0x09, 0xce, 0xb2, 0x20, 0x3e, 0xf6, 0x6c, 0xe4, 0x79, 0x1f, 0x2a,
	0xea, 0x30, 0x66, 0xf9, 0x01, 0x6d, 0x2c, 0x6e, 0x6b, 0x3b, 0x5a, 0x4b, 0x29, 0xd0, 0x16, 0x24,
	0x71, 0x1c, 0xef, 0x3a, 0x4c, 0x06, 0x87, 0xe4, 0xaa, 0x23, 0x57, 0x55, 0x90, 0x05, 0x22, 0x92,
	0x6f, 0x13, 0xca, 0xc8, 0x82, 0xa6, 0x2d, 0xa1, 0xd6, 0x25, 0x41, 0x78, 0x2a, 0x8c, 0xba, 0x0a,
	0x88, 0x87, 0x61, 0xf9, 0x67, 0x34, 0x30, 0x9c, 0x5e, 0xa7, 0x51, 0x42, 0x8e, 0x8a, 0xa0, 0x1e,
	0x09, 0xe2, 0x71, 0xaf, 0x13, 0x59, 0x66, 0x53, 0x66, 0x19, 0xe6, 0xc0, 0x76, 0xfc, 0x46, 0x19,
	0xd9, 0xd0, 0x90, 0x47, 0x94, 0x59, 0x87, 0x82, 0x48, 0xb6, 0xa1, 0x72, 0xe6, 0x3b, 0x16, 0x35,
	0xb8, 0xd9, 0x11, 0x88, 0x03, 0x1a, 0x06, 0x48, 0x3b, 0x31, 0x3b, 0xc7, 0xb6, 0x38, 0xcf, 0xf2,
	0x3d, 0x4e, 0x3d, 0x1e, 0xf2, 0x2c, 0x23, 0x4f, 0x45, 0x51, 0x25, 0x97, 0xf0, 0x49, 0xe8, 0x38,
	0x15, 0x84, 0x15, 0xe9, 0xbc, 0x90, 0xac, 0xc2, 0xe5, 0x16, 0xac, 0x46, 0x8c, 0x7e, 0x9f, 0x06,
	0x26, 0xf7, 0x83, 0x46, 0x15, 0x55, 0xab, 0x87, 0x0b, 0xcf, 0x14, 0x9d, 0x7c, 0x00, 0xd5, 0x88,
	0x19, 0x71, 0xaf, 0xc9, 0xa3, 0x43, 0x22, 0x02, 0xbf, 0x05, 0xca, 0xa5, 0xc6, 0xc0, 0xb1, 0x1b,
	0x2b, 0xc8, 0x51, 0x96, 0x94, 0x17, 0x8e, 0xad, 0xff, 0x00, 0x6a, 0x87, 0x69, 0x15, 0xf2, 0x23,
	0xb6, 0x04, 0xf3, 0xcf, 0x4d, 0xc6, 0xea, 0x05, 0x02, 0xb0, 0xd8, 0xa2, 0x3f, 0xa5, 0x16, 0xaf,
	0x17, 0xf5, 0xdf, 0x69, 0xa0, 0x4f, 0xca, 0x60, 0xd6, 0x27, 0x3f, 0x49, 0x28, 0x2b, 0x32, 0xbf,
	0xa1, 0x6d, 0x17, 0x77, 0x96, 0x0f, 0xee, 0xee, 0x67, 0x17, 0x86, 0xfd, 0x31, 0xc9, 0x10, 0x5b,
	0x28, 0xa4, 0x93, 0x75, 0x58, 0xe0, 0x3e, 0x37, 0x5d, 0x95, 0xfd, 0xf2, 0x43, 0x5f, 0x83, 0xd5,
	0x27, 0x94, 0x8b, 0xad, 0x27, 0x66, 0x47, 0xd5, 0x10, 0xfd, 0x67, 0xb0, 0x74, 0x62, 0x76, 0x9e,
	0xfa, 0x36, 0x25, 0x1b, 0xb0, 0xa8, 0x1c, 0xa6, 0xa9, 0x6d, 0xe8, 0xa9, 0x4b, 0x50, 0x12, 0xe4,
	0x44, 0xda, 0x2c, 0x71, 0xb3, 0x83, 0xa1, 0xf5, 0x29, 0x80, 0xd5, 0x75, 0x5c, 0x5b, 0xaa, 0x5f,
	0x44, 0xf5, 0xaf, 0xe4, 0xa9, 0xaf, 0x8e, 0x69, 0x95, 0x71, 0x8b, 0xd0, 0x53, 0xff, 0x8b, 0x06,
	0x24, 0xa9, 0x12, 0xeb, 0xfb, 0x1e, 0xa3, 0xe4, 0x31, 0xd4, 0xe2, 0x18, 0x4b, 0x20, 0x33, 0x51,
	0x74, 0x25, 0x0c, 0x43, 0x44, 0xe1, 0x18, 0xea, 0xc9, 0x40, 0x44, 0x41, 0x85, 0xe9, 0x04, 0xd5,
	0xe2, 0x58, 0x45, 0x45, 0x7f, 0xab, 0xc1, 0xe5, 0x2f, 0x7c, 0xdb, 0x39, 0x3d, 0x4f, 0x22, 0x1f,
	0xc3, 0x38, 0x43, 0x31, 0x1a, 0x4e, 0xa0, 0xc2, 0x14, 0x09, 0x54, 0x1c, 0x4d, 0x20, 0xfd, 0x0a,
	0x6c, 0xe5, 0x68, 0x24, 0x51, 0xd4, 0xdf, 0xc1, 0xa5, 0x1f, 0x9a, 0x9e, 0xed, 0xa6, 0x82, 0x65,
	0x76, 0x7d, 0x2f, 0xc2, 0x92, 0xc3, 0x8c, 0xbe, 0xc9, 0x18, 0xaa, 0x5a, 0x6a, 0x2d, 0x3a, 0x4c,
	0xc4, 0xbc, 0xe8, 0x3e, 0x51, 0x3e, 0xca, 0x4a, 0x19, 0x7d, 0xeb, 0x97, 0xa1, 0x29, 0xce, 0xa6,
	0x6e, 0xfa, 0x6c, 0xa5, 0xd9, 0xff, 0xc5, 0x5e, 0x77, 0x7a, 0x74, 0x8a, 0x6e, 0xa6, 0xff, 0xb9,
	0x00, 0x6b, 0xa9, 0x2d, 0x2a, 0x52, 0xbe, 0x54, 0xb5, 0x4d, 0x30, 0x25, 0x23, 0xe5, 0x5e, 0x9e,
	0x83, 0x33, 0x84, 0xec, 0x47, 0x04, 0xac, 0x88, 0xe2, 0x17, 0x86, 0xcf, 0x47, 0xd0, 0xa0, 0x66,
	0xe0, 0x3a, 0x94, 0x71, 0x83, 0x51, 0x97, 0x5a, 0xdc, 0x7c, 0xe5, 0x52, 0x59, 0x56, 0xa4, 0xd3,
	0x2e, 0x84, 0xeb, 0xed, 0x68, 0x59, 0xec, 0x6e, 0x0e, 0xa0, 0x14, 0xca, 0xcc, 0xea, 0x18, 0xda,
	0x54, 0x1d, 0xa3, 0x30, 0xda, 0x31, 0xb6, 0x00, 0x02, 0xda, 0x33, 0x1d, 0xcf, 0xb0, 0x3c, 0xae,
	0x82, 0xa2, 0x2c, 0x29, 0x47, 0x1e, 0xd7, 0xbf, 0x0f, 0x9b, 0x4f, 0x28, 0x0f, 0xcb, 0xc3, 0x63,
	0x8f, 0x07, 0xe7, 0xc7, 0xde, 0xa9, 0x1f, 0xe2, 0xbb, 0x25, 0x92, 0x55, 0x82, 0x12, 0x39, 0xbb,
	0xac, 0x28, 0xc7, 0xb6, 0xfe, 0x0c, 0x2e, 0x67, 0xef, 0x56, 0x50, 0xdf, 0x81, 0x75, 0xa5, 0x20,
	0x0f, 0xce, 0x0d, 0x4e, 0xbf, 0xe6, 0x31, 0xe0, 0xe5, 0xd6, 0x2a, 0x0b, 0x37, 0x9d, 0xd0, 0xaf,
	0x39, 0xe6, 0xcc, 0xef, 0xe3, 0xe4, 0x9e, 0x76, 0x68, 0xb9, 0x09, 0xab, 0xc9, 0xdc, 0x88, 0x53,
	0xb6, 0xda, 0xaa, 0xc5, 0x09, 0x82, 0xde, 0xd9, 0x83, 0xb5, 0x74, 0x92, 0xc4, 0x35, 0xa8, 0xda,
	0xaa, 0x27, 0x33, 0x05, 0xd9, 0xeb, 0x50, 0x14, 0xc5, 0x5e, 0x36, 0x6b, 0xf1, 0x53, 0xff, 0x6b,
	0x51, 0x7a, 0x09, 0x67, 0x89, 0x8b, 0xb0, 0x84, 0xc6, 0x45, 0xc0, 0x2c, 0x8a, 0xcf, 0x63, 0x3b,
	0xdd, 0x59, 0x0b, 0x43, 0x9d, 0x35, 0xc3, 0xb7, 0xc5, 0xa9, 0x7c, 0x3b, 0x3f, 0xea, 0xdb, 0x8c,
	0xfe, 0xbb, 0x90, 0xd5, 0x7f, 0x47, 0xbb, 0xf9, 0x62, 0x46, 0x37, 0x57, 0xe6, 0x2e, 0x45, 0xe6,
	0x0e, 0x79, 0xbf, 0x34, 0xe4, 0x7d, 0x72, 0x0d, 0x6a, 0x78, 0xa8, 0x61, 0x0f, 0x02, 0x93, 0x3b,
	0xbe, 0x87, 0xdd, 0xbf, 0xda, 0xaa, 0x22, 0xf5, 0x91, 0x22, 0xa2, 0x14, 0x85, 0x7a, 0xd4, 0xfb,
	0xcb, 0x8a, 0x22, 0x5b, 0x85, 0xf4, 0x5f, 0xd4, 0xf4, 0x97, 0xf0, 0x5b, 0x02, 0xd9, 0xf5, 0xb9,
	0x71, 0x66, 0xba, 0x03, 0x8a, 0x9d, 0xbe, 0xd8, 0x2a, 0x75, 0x7d, 0xfe, 0x52, 0x7c, 0x8b, 0xb9,
	0x4b, 0xe6, 0xa9, 0x1c, 0x04, 0xaa, 0xb2, 0x24, 0x0a, 0x92, 0xec, 0xc0, 0xfa, 0x7f, 0xe3, 0xfc,
	0x0f, 0xdb, 0x27, 0x06, 0xe5, 0x4b, 0xe5, 0x9e, 0x44, 0xea, 0x7f, 0x6f, 0x42, 0xea, 0x27, 0xf7,
	0x63, 0xea, 0x3f, 0x09, 0xfc, 0x41, 0x1f, 0x9b, 0x28, 0x7a, 0x16, 0xc3, 0x25, 0x51, 0x1e, 0xdd,
	0x73, 0x19, 0xf2, 0xaa, 0xfa, 0x85, 0xe5, 0xd1, 0x3d, 0xc7, 0x68, 0x6f, 0xfe, 0x4b, 0x83, 0x6a,
	0x4a, 0x8a, 0xc0, 0x08, 0xcb, 0x51, 0x60, 0x7a, 0x1d, 0x19, 0xe2, 0xe5, 0x56, 0x59, 0x50, 0x5a,
	0x82, 0x40, 0x3e, 0x51, 0x2a, 0x3b, 0x9c, 0xf6, 0x54, 0x3b, 0xda, 0xce, 0x53, 0x39, 0x8c, 0x4f,
	0xa9, 0x19, 0x4a, 0xdf, 0x80, 0x45, 0x87, 0x89, 0x48, 0xc2, 0x50, 0x2b, 0xb5, 0x16, 0x1c, 0xf6,
	0xd8, 0x43, 0xe4, 0xbb, 0x26, 0x33, 0x7a, 0x62, 0x44, 0x9c, 0xc7, 0x85, 0xa5, 0xae, 0xc9, 0xbe,
	0x10, 0xc3, 0xe1, 0x16, 0xc0, 0x2b, 0xda, 0x71, 0xbc, 0xe4, 0x20, 0x5a, 0x46, 0x0a, 0x06, 0xde,
	0x25, 0x28, 0x45, 0x71, 0xb9, 0x28, 0x7d, 0x46, 0x65, 0x4c, 0xea, 0x5f, 0xc1, 0xd6, 0xe8, 0x18,
	0x33, 0x7d, 0x49, 0x19, 0x1a, 0xb4, 0x0a, 0xc3, 0x83, 0x96, 0x0f, 0x2b, 0x42, 0x60, 0xcb, 0xe4,
	0x8e, 0xd7, 0xf9, 0xcc, 0xf5, 0x4d, 0xae, 0x9a, 0x8d, 0x30, 0x16, 0xa5, 0x61, 0xb3, 0x11, 0x3c,
	0x42, 0x94, 0x40, 0x84, 0x7a, 0x06, 0xa3, 0x16, 0x8a, 0x2a, 0xb6, 0xca, 0x92, 0xd2, 0xa6, 0x96,
	0xc8, 0x8a, 0x53, 0x21, 0x40, 0x44, 0xd0, 0x39, 0xb2, 0x14, 0x91, 0xa5, 0x82, 0xd4, 0x36, 0x37,
	0xcf, 0xdb, 0xd4, 0xd2, 0xff, 0xa3, 0x41, 0x25, 0x69, 0xc6, 0x77, 0x4c, 0xfb, 0x4b, 0x50, 0x52,
	0x80, 0x32, 0x75, 0xcc, 0x92, 0x84, 0x93, 0x09, 0xef, 0x20, 0x98, 0x0c, 0x9d, 0x50, 0x6c, 0x2d,
	0x08, 0x28, 0xd9, 0x10, 0x10, 0x0b, 0x43, 0x40, 0x90, 0x36, 0xac, 0x06, 0x08, 0x82, 0x21, 0x8d,
	0xb0, 0x7c, 0xef, 0x14, 0x7d, 0xb1, 0x7c, 0x70, 0x63, 0x5c, 0x68, 0x24, 0x90, 0x6b, 0xad, 0x04,
	0xf1, 0xc7, 0x91, 0xef, 0x9d, 0xea, 0x2e, 0xbc, 0x97, 0xe7, 0x3c, 0x95, 0x3c, 0x3f, 0x82, 0x5a,
	0x2c, 0xd4, 0xf1, 0x4e, 0x7d, 0x04, 0x61, 0xf9, 0xe0, 0x6a, 0xde, 0x99, 0x29, 0x29, 0x15, 0x37,
	0xf1, 0xa5, 0x7f, 0x05, 0xcd, 0x96, 0xc9, 0xe9, 0xd0, 0x71, 0x61, 0x9c, 0xa8, 0x72, 0xa4, 0xc5,
	0xe5, 0x28, 0x81, 0x7c, 0x21, 0x85, 0xfc, 0x3a, 0x2c, 0xc8, 0x8b, 0x8e, 0xac, 0xa4, 0xf2, 0x43,
	0xdf, 0x82, 0xcd, 0x4c, 0xf1, 0x6a, 0xa0, 0xf8, 0xa6, 0x00, 0x2b, 0x27, 0x94, 0xf1, 0x13, 0xdf,
	0x8f, 0x26, 0x9c, 0xb0, 0x34, 0x24, 0x0c, 0xcb, 0x2d, 0x0d, 0x43, 0x7b, 0xf1, 0x5b, 0x1c, 0xf0,
	0x50, 0xb8, 0xf5, 0xf9, 0x80, 0x75, 0x55, 0x02, 0x7a, 0xa7, 0x7e, 0xf3, 0x4f, 0x1a, 0xac, 0x8e,
	0xac, 0x0f, 0x79, 0x58, 0x1b, 0xf6, 0x70, 0x3a, 0x51, 0x0a, 0xc3, 0x89, 0x92, 0x0a, 0xb7, 0xe2,
	0x98, 0x70, 0x9b, 0xcf, 0x0b, 0xb7, 0x85, 0x44, 0xb8, 0xe9, 0x04, 0xea, 0xb1, 0x45, 0x0a, 0xa2,
	0xbf, 0x17, 0x80, 0x3c, 0xa2, 0x96, 0x6b, 0x06, 0x29, 0xcf, 0xa4, 0x4e, 0xd6, 0x26, 0xf7, 0xb7,
	0xc2, 0x54, 0xfd, 0xad, 0x38, 0xda, 0xdf, 0x86, 0xc7, 0xde, 0xf9, 0x29, 0xc6, 0xde, 0x85, 0x8c,
	0x7b, 0x63, 0x46, 0x9f, 0x5c, 0xcc, 0xea, 0x93, 0x1f, 0x42, 0x63, 0x88, 0x2f, 0x6e, 0x6d, 0xb2,
	0x2d, 0x6e, 0xa4, 0x36, 0x44, 0x2d, 0x6e, 0xaa, 0xeb, 0xb2, 0xbe, 0x01, 0x6b, 0x29, 0x34, 0x15,
	0xca, 0x06, 0xd4, 0x0e, 0x6d, 0x5b, 0xdd, 0x67, 0xc2, 0x7a, 0x3d, 0xe3, 0xa5, 0x0a, 0x1f, 0x55,
	0x02, 0xd5, 0x62, 0x8b, 0xe1, 0xa3, 0x4a, 0x80, 0x1d, 0x56, 0x7f, 0x09, 0xab, 0xf1, 0x01, 0xa1,
	0x13, 0x0f, 0xa5, 0xb0, 0x44, 0x13, 0xbc, 0x9e, 0x17, 0xe9, 0x69, 0xed, 0xf0, 0x50, 0x1c, 0xd6,
	0xd6, 0x81, 0x24, 0xe5, 0x2a, 0x73, 0x1e, 0xc0, 0xfa, 0x23, 0xea, 0x52, 0x4e, 0x87, 0x0e, 0x7c,
	0x0f, 0x96, 0x93, 0x43, 0x97, 0x86, 0x43, 0x57, 0x99, 0x87, 0xd3, 0x96, 0x7e, 0x11, 0x36, 0x86,
	0xf6, 0x29, 0x81, 0x07, 0x58, 0x94, 0xc2, 0x36, 0xfc, 0x82, 0xd1, 0xe0, 0xa1, 0xc9, 0x68, 0xb2,
	0xa5, 0x8c, 0x94, 0x0a, 0xfd, 0x8f, 0x1a, 0x5c, 0xc9, 0xdd, 0xa4, 0x4a, 0xd9, 0x6d, 0x20, 0x6a,
	0x32, 0xb6, 0xa5, 0x57, 0x70, 0x42, 0x96, 0x42, 0xea, 0x72, 0x45, 0xb9, 0xeb, 0xc8, 0x9b, 0xa1,
	0xbb, 0x93, 0x0f, 0xa0, 0x3a, 0xf0, 0x70, 0xec, 0x0f, 0xa8, 0xc9, 0x7c, 0x4f, 0x25, 0x67, 0x45,
	0x12, 0x5b, 0x48, 0xd3, 0xff, 0xad, 0xc1, 0x66, 0x9b, 0xf2, 0xf0, 0x9a, 0x23, 0xc2, 0x89, 0x9f,
	0x9c, 0xf7, 0xa3, 0x8b, 0xcd, 0x15, 0x58, 0x1e, 0xbd, 0x66, 0x81, 0x19, 0x5f, 0xb1, 0x7e, 0x0c,
	0xd7, 0x47, 0xdd, 0x15, 0x6d, 0x11, 0xc1, 0xcb, 0x0d, 0x66, 0x51, 0x2f, 0x4c, 0xbf, 0xf7, 0xad,
	0xec, 0x97, 0x00, 0x3c, 0xb9, 0x2d, 0x18, 0xc9, 0x33, 0xb8, 0x36, 0x51, 0x24, 0x3f, 0xef, 0x87,
	0xa9, 0xba, 0x3d, 0x4e, 0xa2, 0xb0, 0x45, 0x7f, 0x0f, 0x2e, 0x67, 0xdb, 0xa8, 0x3c, 0xfb, 0x37,
	0x0d, 0x36, 0x5e, 0x78, 0xa7, 0xbe, 0x6b, 0x0f, 0x0f, 0xfc, 0xe9, 0xf9, 0x43, 0x1b, 0x37, 0x7f,
	0x14, 0x52, 0xf3, 0x47, 0xf6, 0x75, 0xa0, 0x38, 0xcb, 0x75, 0x60, 0x3e, 0xfb, 0x3a, 0xa0, 0xdb,
	0x70, 0x61, 0x58, 0xd9, 0xa8, 0x29, 0xd6, 0x07, 0xb8, 0x62, 0x0c, 0x0f, 0x96, 0x93, 0xa7, 0xb4,
	0xda, 0x20, 0x25, 0x73, 0xf7, 0x1f, 0x1a, 0x6c, 0x1f, 0x4d, 0x00, 0x96, 0xdc, 0x82, 0x1b, 0x93,
	0x78, 0x8e, 0xbd, 0x33, 0xd3, 0x75, 0xec, 0xfa, 0xdc, 0x34, 0xcc, 0xf1, 0xe3, 0xd4, 0x0e, 0x5c,
	0x9d, 0xc8, 0x2c, 0x1f, 0xaf, 0x76, 0xe1, 0xfa, 0x24, 0xce, 0xf0, 0x71, 0x6b, 0xf7, 0x9f, 0x1a,
	0xbc, 0x7f, 0x34, 0x31, 0xfe, 0x6e, 0xc3, 0xce, 0x44, 0xa6, 0xd8, 0xac, 0x9b, 0x70, 0x6d, 0x22,
	0xb7, 0xa8, 0x8d, 0x75, 0x6d, 0x92, 0xaa, 0xc8, 0x8a, 0x55, 0x7a, 0xb2, 0x59, 0xc8, 0x8b, 0xc5,
	0xbf, 0x5e, 0x3c, 0xf8, 0x76, 0x05, 0xd6, 0x86, 0x98, 0x31, 0xb0, 0xfe, 0xa0, 0x65, 0xcd, 0x51,
	0xc9, 0xb7, 0x3c, 0xf2, 0xc9, 0x98, 0x1b, 0xc7, 0xe4, 0x57, 0xfc, 0xe6, 0xc7, 0xdf, 0x75, 0x3b,
	0xeb, 0xeb, 0x73, 0x84, 0x02, 0xc4, 0x2f, 0x68, 0xe4, 0xe6, 0xa4, 0x77, 0x8f, 0xa8, 0x86, 0x37,
	0x77, 0xa7, 0x61, 0x55, 0xc9, 0x3d, 0x47, 0x7e, 0xad, 0xc1, 0x46, 0xe6, 0x73, 0x13, 0xc9, 0x7d,
	0x6a, 0x19, 0xf7, 0x5e, 0xd6, 0xbc, 0x3f, 0xe3, 0xae, 0x48, 0x91, 0x9f, 0x03, 0x19, 0x7d, 0xd5,
	0x22, 0xff, 0x9f, 0x27, 0x2e, 0xf7, 0x05, 0xac, 0x79, 0x30, 0x6e, 0x4b, 0xce, 0xc3, 0xd5, 0x1c,
	0xe9, 0xc2, 0x72, 0xe2, 0x09, 0x89, 0xec, 0x4e, 0xf5, 0xce, 0x24, 0x0f, 0xbc, 0x35, 0xc3, 0x9b,
	0x94, 0x3e, 0x47, 0x7e, 0xa3, 0xc1, 0x85, 0xec, 0x01, 0x9e, 0xdc, 0x9f, 0x3e, 0x62, 0x12, 0xad,
	0xb5, 0xf9, 0x60, 0xd6, 0x6d, 0x91, 0x2e, 0xbf, 0xd0, 0x60, 0x2d, 0x63, 0xfe, 0x26, 0xb9, 0x18,
	0xe6, 0xdf, 0x05, 0x9a, 0x77, 0x67, 0xda, 0x13, 0xa9, 0x60, 0x40, 0x29, 0x9c, 0x69, 0xc9, 0x8d,
	0x29, 0xe7, 0xf8, 0xe6, 0xce, 0x64, 0xc6, 0xe8, 0x80, 0x5f, 0x6a, 0xb0, 0x9e, 0xf5, 0x00, 0x46,
	0xee, 0x8e, 0x81, 0x2d, 0xef, 0xb1, 0xad, 0x79, 0x6f, 0xb6, 0x4d, 0x19, 0xf1, 0x85, 0xa5, 0x65,
	0x77, 0xaa, 0xc7, 0x8c, 0xe9, 0xe2, 0x2b, 0xd9, 0xe6, 0xf4, 0x39, 0xf2, 0x06, 0x6a, 0xe9, 0x16,
	0x48, 0xf6, 0xf2, 0x04, 0x64, 0xf6, 0xf5, 0xe6, 0xfe, 0xb4, 0xec, 0x49, 0xe3, 0x12, 0x43, 0x73,
	0xbe, 0x71, 0xa3, 0xf7, 0x94, 0x7c, 0xe3, 0xb2, 0xa6, 0x70, 0xac, 0x8a, 0xf1, 0x38, 0x9b, 0x5f,
	0x15, 0x47, 0x46, 0xe9, 0xfc, 0xaa, 0x98, 0x31, 0x1d, 0xcf, 0x11, 0x0f, 0xaa, 0xa9, 0x39, 0x97,
	0xdc, 0xce, 0x57, 0x73, 0x74, 0x8c, 0x6e, 0xee, 0x4d, 0xc9, 0x1d, 0x9d, 0xf7, 0x8d, 0x06, 0x17,
	0x73, 0x46, 0x61, 0xf2, 0x60, 0x0a, 0xf7, 0x67, 0x0c, 0xdc, 0xcd, 0x0f, 0x67, 0xde, 0x97, 0x4a,
	0x99, 0xac, 0xa1, 0x30, 0x3f, 0x65, 0xc6, 0x8c, 0xc9, 0xf9, 0x29, 0x33, 0x76, 0xee, 0x9c, 0x7b,
	0xf8, 0xf1, 0x97, 0x1f, 0x75, 0x7c, 0xd7, 0xf4, 0x3a, 0xfb, 0xf7, 0x0f, 0x38, 0xdf, 0xb7, 0xfc,
	0xde, 0x1d, 0xfc, 0x83, 0xdd, 0xf2, 0xdd, 0x3b, 0x8c, 0x06, 0x67, 0x8e, 0x45, 0x59, 0xce, 0x3f,
	0xf1, 0xaf, 0x16, 0x91, 0xf3, 0xee, 0xff, 0x02, 0x00, 0x00, 0xff, 0xff, 0x77, 0x59, 0x73, 0x1a,
	0xd0, 0x1f, 0x00, 0x00,
}
