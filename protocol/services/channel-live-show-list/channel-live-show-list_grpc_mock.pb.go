// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/channel-live-show-list/channel-live-show-list.proto

package channel_live_show_list

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockChannelLiveShowListClient is a mock of ChannelLiveShowListClient interface.
type MockChannelLiveShowListClient struct {
	ctrl     *gomock.Controller
	recorder *MockChannelLiveShowListClientMockRecorder
}

// MockChannelLiveShowListClientMockRecorder is the mock recorder for MockChannelLiveShowListClient.
type MockChannelLiveShowListClientMockRecorder struct {
	mock *MockChannelLiveShowListClient
}

// NewMockChannelLiveShowListClient creates a new mock instance.
func NewMockChannelLiveShowListClient(ctrl *gomock.Controller) *MockChannelLiveShowListClient {
	mock := &MockChannelLiveShowListClient{ctrl: ctrl}
	mock.recorder = &MockChannelLiveShowListClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelLiveShowListClient) EXPECT() *MockChannelLiveShowListClientMockRecorder {
	return m.recorder
}

// AddShowTag mocks base method.
func (m *MockChannelLiveShowListClient) AddShowTag(ctx context.Context, in *AddShowTagRequest, opts ...grpc.CallOption) (*AddShowTagResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddShowTag", varargs...)
	ret0, _ := ret[0].(*AddShowTagResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddShowTag indicates an expected call of AddShowTag.
func (mr *MockChannelLiveShowListClientMockRecorder) AddShowTag(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddShowTag", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).AddShowTag), varargs...)
}

// DeclareShow mocks base method.
func (m *MockChannelLiveShowListClient) DeclareShow(ctx context.Context, in *DeclareShowRequest, opts ...grpc.CallOption) (*DeclareShowResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeclareShow", varargs...)
	ret0, _ := ret[0].(*DeclareShowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeclareShow indicates an expected call of DeclareShow.
func (mr *MockChannelLiveShowListClientMockRecorder) DeclareShow(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeclareShow", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).DeclareShow), varargs...)
}

// DeleteShowTag mocks base method.
func (m *MockChannelLiveShowListClient) DeleteShowTag(ctx context.Context, in *DeleteShowTagRequest, opts ...grpc.CallOption) (*DeleteShowTagResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteShowTag", varargs...)
	ret0, _ := ret[0].(*DeleteShowTagResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteShowTag indicates an expected call of DeleteShowTag.
func (mr *MockChannelLiveShowListClientMockRecorder) DeleteShowTag(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteShowTag", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).DeleteShowTag), varargs...)
}

// GetChannelLiveShowApprovalList mocks base method.
func (m *MockChannelLiveShowListClient) GetChannelLiveShowApprovalList(ctx context.Context, in *GetChannelLiveShowApprovalListRequest, opts ...grpc.CallOption) (*GetChannelLiveShowApprovalListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelLiveShowApprovalList", varargs...)
	ret0, _ := ret[0].(*GetChannelLiveShowApprovalListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveShowApprovalList indicates an expected call of GetChannelLiveShowApprovalList.
func (mr *MockChannelLiveShowListClientMockRecorder) GetChannelLiveShowApprovalList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowApprovalList", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).GetChannelLiveShowApprovalList), varargs...)
}

// GetChannelLiveShowInfo mocks base method.
func (m *MockChannelLiveShowListClient) GetChannelLiveShowInfo(ctx context.Context, in *GetChannelLiveShowInfoRequest, opts ...grpc.CallOption) (*GetChannelLiveShowInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelLiveShowInfo", varargs...)
	ret0, _ := ret[0].(*GetChannelLiveShowInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveShowInfo indicates an expected call of GetChannelLiveShowInfo.
func (mr *MockChannelLiveShowListClientMockRecorder) GetChannelLiveShowInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowInfo", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).GetChannelLiveShowInfo), varargs...)
}

// GetLiveShowEntryInfo mocks base method.
func (m *MockChannelLiveShowListClient) GetLiveShowEntryInfo(ctx context.Context, in *GetLiveShowEntryInfoRequest, opts ...grpc.CallOption) (*GetLiveShowEntryInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLiveShowEntryInfo", varargs...)
	ret0, _ := ret[0].(*GetLiveShowEntryInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLiveShowEntryInfo indicates an expected call of GetLiveShowEntryInfo.
func (mr *MockChannelLiveShowListClientMockRecorder) GetLiveShowEntryInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveShowEntryInfo", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).GetLiveShowEntryInfo), varargs...)
}

// GetShowList mocks base method.
func (m *MockChannelLiveShowListClient) GetShowList(ctx context.Context, in *GetShowListRequest, opts ...grpc.CallOption) (*GetShowListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetShowList", varargs...)
	ret0, _ := ret[0].(*GetShowListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowList indicates an expected call of GetShowList.
func (mr *MockChannelLiveShowListClientMockRecorder) GetShowList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowList", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).GetShowList), varargs...)
}

// GetShowListUserBaseInfo mocks base method.
func (m *MockChannelLiveShowListClient) GetShowListUserBaseInfo(ctx context.Context, in *GetShowListUserBaseInfoRequest, opts ...grpc.CallOption) (*GetShowListUserBaseInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetShowListUserBaseInfo", varargs...)
	ret0, _ := ret[0].(*GetShowListUserBaseInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowListUserBaseInfo indicates an expected call of GetShowListUserBaseInfo.
func (mr *MockChannelLiveShowListClientMockRecorder) GetShowListUserBaseInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowListUserBaseInfo", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).GetShowListUserBaseInfo), varargs...)
}

// GetShowTag mocks base method.
func (m *MockChannelLiveShowListClient) GetShowTag(ctx context.Context, in *GetShowTagRequest, opts ...grpc.CallOption) (*GetShowTagResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetShowTag", varargs...)
	ret0, _ := ret[0].(*GetShowTagResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowTag indicates an expected call of GetShowTag.
func (mr *MockChannelLiveShowListClientMockRecorder) GetShowTag(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowTag", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).GetShowTag), varargs...)
}

// GetShowTime mocks base method.
func (m *MockChannelLiveShowListClient) GetShowTime(ctx context.Context, in *GetShowTimeRequest, opts ...grpc.CallOption) (*GetShowTimeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetShowTime", varargs...)
	ret0, _ := ret[0].(*GetShowTimeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowTime indicates an expected call of GetShowTime.
func (mr *MockChannelLiveShowListClientMockRecorder) GetShowTime(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowTime", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).GetShowTime), varargs...)
}

// HandleShowApproval mocks base method.
func (m *MockChannelLiveShowListClient) HandleShowApproval(ctx context.Context, in *HandleShowApprovalRequest, opts ...grpc.CallOption) (*HandelShowApprovalResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleShowApproval", varargs...)
	ret0, _ := ret[0].(*HandelShowApprovalResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleShowApproval indicates an expected call of HandleShowApproval.
func (mr *MockChannelLiveShowListClientMockRecorder) HandleShowApproval(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleShowApproval", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).HandleShowApproval), varargs...)
}

// ModifyShowApprovalTag mocks base method.
func (m *MockChannelLiveShowListClient) ModifyShowApprovalTag(ctx context.Context, in *ModifyShowApprovalTagRequest, opts ...grpc.CallOption) (*ModifyShowApprovalTagResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ModifyShowApprovalTag", varargs...)
	ret0, _ := ret[0].(*ModifyShowApprovalTagResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyShowApprovalTag indicates an expected call of ModifyShowApprovalTag.
func (mr *MockChannelLiveShowListClientMockRecorder) ModifyShowApprovalTag(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyShowApprovalTag", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).ModifyShowApprovalTag), varargs...)
}

// RateChannelLiveShow mocks base method.
func (m *MockChannelLiveShowListClient) RateChannelLiveShow(ctx context.Context, in *RateChannelLiveShowRequest, opts ...grpc.CallOption) (*RateChannelLiveShowResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RateChannelLiveShow", varargs...)
	ret0, _ := ret[0].(*RateChannelLiveShowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RateChannelLiveShow indicates an expected call of RateChannelLiveShow.
func (mr *MockChannelLiveShowListClientMockRecorder) RateChannelLiveShow(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RateChannelLiveShow", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).RateChannelLiveShow), varargs...)
}

// SetApprovalAuditType mocks base method.
func (m *MockChannelLiveShowListClient) SetApprovalAuditType(ctx context.Context, in *SetApprovalAuditTypeRequest, opts ...grpc.CallOption) (*SetApprovalAuditTypeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetApprovalAuditType", varargs...)
	ret0, _ := ret[0].(*SetApprovalAuditTypeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetApprovalAuditType indicates an expected call of SetApprovalAuditType.
func (mr *MockChannelLiveShowListClientMockRecorder) SetApprovalAuditType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetApprovalAuditType", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).SetApprovalAuditType), varargs...)
}

// TestTool mocks base method.
func (m *MockChannelLiveShowListClient) TestTool(ctx context.Context, in *TestToolRequest, opts ...grpc.CallOption) (*TestToolResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestTool", varargs...)
	ret0, _ := ret[0].(*TestToolResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestTool indicates an expected call of TestTool.
func (mr *MockChannelLiveShowListClientMockRecorder) TestTool(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestTool", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).TestTool), varargs...)
}

// UnfoldShowList mocks base method.
func (m *MockChannelLiveShowListClient) UnfoldShowList(ctx context.Context, in *UnfoldShowListRequest, opts ...grpc.CallOption) (*UnfoldShowListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnfoldShowList", varargs...)
	ret0, _ := ret[0].(*UnfoldShowListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnfoldShowList indicates an expected call of UnfoldShowList.
func (mr *MockChannelLiveShowListClientMockRecorder) UnfoldShowList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfoldShowList", reflect.TypeOf((*MockChannelLiveShowListClient)(nil).UnfoldShowList), varargs...)
}

// MockChannelLiveShowListServer is a mock of ChannelLiveShowListServer interface.
type MockChannelLiveShowListServer struct {
	ctrl     *gomock.Controller
	recorder *MockChannelLiveShowListServerMockRecorder
}

// MockChannelLiveShowListServerMockRecorder is the mock recorder for MockChannelLiveShowListServer.
type MockChannelLiveShowListServerMockRecorder struct {
	mock *MockChannelLiveShowListServer
}

// NewMockChannelLiveShowListServer creates a new mock instance.
func NewMockChannelLiveShowListServer(ctrl *gomock.Controller) *MockChannelLiveShowListServer {
	mock := &MockChannelLiveShowListServer{ctrl: ctrl}
	mock.recorder = &MockChannelLiveShowListServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelLiveShowListServer) EXPECT() *MockChannelLiveShowListServerMockRecorder {
	return m.recorder
}

// AddShowTag mocks base method.
func (m *MockChannelLiveShowListServer) AddShowTag(ctx context.Context, in *AddShowTagRequest) (*AddShowTagResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddShowTag", ctx, in)
	ret0, _ := ret[0].(*AddShowTagResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddShowTag indicates an expected call of AddShowTag.
func (mr *MockChannelLiveShowListServerMockRecorder) AddShowTag(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddShowTag", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).AddShowTag), ctx, in)
}

// DeclareShow mocks base method.
func (m *MockChannelLiveShowListServer) DeclareShow(ctx context.Context, in *DeclareShowRequest) (*DeclareShowResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeclareShow", ctx, in)
	ret0, _ := ret[0].(*DeclareShowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeclareShow indicates an expected call of DeclareShow.
func (mr *MockChannelLiveShowListServerMockRecorder) DeclareShow(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeclareShow", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).DeclareShow), ctx, in)
}

// DeleteShowTag mocks base method.
func (m *MockChannelLiveShowListServer) DeleteShowTag(ctx context.Context, in *DeleteShowTagRequest) (*DeleteShowTagResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteShowTag", ctx, in)
	ret0, _ := ret[0].(*DeleteShowTagResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteShowTag indicates an expected call of DeleteShowTag.
func (mr *MockChannelLiveShowListServerMockRecorder) DeleteShowTag(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteShowTag", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).DeleteShowTag), ctx, in)
}

// GetChannelLiveShowApprovalList mocks base method.
func (m *MockChannelLiveShowListServer) GetChannelLiveShowApprovalList(ctx context.Context, in *GetChannelLiveShowApprovalListRequest) (*GetChannelLiveShowApprovalListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveShowApprovalList", ctx, in)
	ret0, _ := ret[0].(*GetChannelLiveShowApprovalListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveShowApprovalList indicates an expected call of GetChannelLiveShowApprovalList.
func (mr *MockChannelLiveShowListServerMockRecorder) GetChannelLiveShowApprovalList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowApprovalList", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).GetChannelLiveShowApprovalList), ctx, in)
}

// GetChannelLiveShowInfo mocks base method.
func (m *MockChannelLiveShowListServer) GetChannelLiveShowInfo(ctx context.Context, in *GetChannelLiveShowInfoRequest) (*GetChannelLiveShowInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveShowInfo", ctx, in)
	ret0, _ := ret[0].(*GetChannelLiveShowInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveShowInfo indicates an expected call of GetChannelLiveShowInfo.
func (mr *MockChannelLiveShowListServerMockRecorder) GetChannelLiveShowInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowInfo", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).GetChannelLiveShowInfo), ctx, in)
}

// GetLiveShowEntryInfo mocks base method.
func (m *MockChannelLiveShowListServer) GetLiveShowEntryInfo(ctx context.Context, in *GetLiveShowEntryInfoRequest) (*GetLiveShowEntryInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiveShowEntryInfo", ctx, in)
	ret0, _ := ret[0].(*GetLiveShowEntryInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLiveShowEntryInfo indicates an expected call of GetLiveShowEntryInfo.
func (mr *MockChannelLiveShowListServerMockRecorder) GetLiveShowEntryInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveShowEntryInfo", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).GetLiveShowEntryInfo), ctx, in)
}

// GetShowList mocks base method.
func (m *MockChannelLiveShowListServer) GetShowList(ctx context.Context, in *GetShowListRequest) (*GetShowListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowList", ctx, in)
	ret0, _ := ret[0].(*GetShowListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowList indicates an expected call of GetShowList.
func (mr *MockChannelLiveShowListServerMockRecorder) GetShowList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowList", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).GetShowList), ctx, in)
}

// GetShowListUserBaseInfo mocks base method.
func (m *MockChannelLiveShowListServer) GetShowListUserBaseInfo(ctx context.Context, in *GetShowListUserBaseInfoRequest) (*GetShowListUserBaseInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowListUserBaseInfo", ctx, in)
	ret0, _ := ret[0].(*GetShowListUserBaseInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowListUserBaseInfo indicates an expected call of GetShowListUserBaseInfo.
func (mr *MockChannelLiveShowListServerMockRecorder) GetShowListUserBaseInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowListUserBaseInfo", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).GetShowListUserBaseInfo), ctx, in)
}

// GetShowTag mocks base method.
func (m *MockChannelLiveShowListServer) GetShowTag(ctx context.Context, in *GetShowTagRequest) (*GetShowTagResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowTag", ctx, in)
	ret0, _ := ret[0].(*GetShowTagResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowTag indicates an expected call of GetShowTag.
func (mr *MockChannelLiveShowListServerMockRecorder) GetShowTag(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowTag", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).GetShowTag), ctx, in)
}

// GetShowTime mocks base method.
func (m *MockChannelLiveShowListServer) GetShowTime(ctx context.Context, in *GetShowTimeRequest) (*GetShowTimeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowTime", ctx, in)
	ret0, _ := ret[0].(*GetShowTimeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowTime indicates an expected call of GetShowTime.
func (mr *MockChannelLiveShowListServerMockRecorder) GetShowTime(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowTime", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).GetShowTime), ctx, in)
}

// HandleShowApproval mocks base method.
func (m *MockChannelLiveShowListServer) HandleShowApproval(ctx context.Context, in *HandleShowApprovalRequest) (*HandelShowApprovalResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleShowApproval", ctx, in)
	ret0, _ := ret[0].(*HandelShowApprovalResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleShowApproval indicates an expected call of HandleShowApproval.
func (mr *MockChannelLiveShowListServerMockRecorder) HandleShowApproval(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleShowApproval", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).HandleShowApproval), ctx, in)
}

// ModifyShowApprovalTag mocks base method.
func (m *MockChannelLiveShowListServer) ModifyShowApprovalTag(ctx context.Context, in *ModifyShowApprovalTagRequest) (*ModifyShowApprovalTagResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyShowApprovalTag", ctx, in)
	ret0, _ := ret[0].(*ModifyShowApprovalTagResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyShowApprovalTag indicates an expected call of ModifyShowApprovalTag.
func (mr *MockChannelLiveShowListServerMockRecorder) ModifyShowApprovalTag(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyShowApprovalTag", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).ModifyShowApprovalTag), ctx, in)
}

// RateChannelLiveShow mocks base method.
func (m *MockChannelLiveShowListServer) RateChannelLiveShow(ctx context.Context, in *RateChannelLiveShowRequest) (*RateChannelLiveShowResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RateChannelLiveShow", ctx, in)
	ret0, _ := ret[0].(*RateChannelLiveShowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RateChannelLiveShow indicates an expected call of RateChannelLiveShow.
func (mr *MockChannelLiveShowListServerMockRecorder) RateChannelLiveShow(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RateChannelLiveShow", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).RateChannelLiveShow), ctx, in)
}

// SetApprovalAuditType mocks base method.
func (m *MockChannelLiveShowListServer) SetApprovalAuditType(ctx context.Context, in *SetApprovalAuditTypeRequest) (*SetApprovalAuditTypeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetApprovalAuditType", ctx, in)
	ret0, _ := ret[0].(*SetApprovalAuditTypeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetApprovalAuditType indicates an expected call of SetApprovalAuditType.
func (mr *MockChannelLiveShowListServerMockRecorder) SetApprovalAuditType(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetApprovalAuditType", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).SetApprovalAuditType), ctx, in)
}

// TestTool mocks base method.
func (m *MockChannelLiveShowListServer) TestTool(ctx context.Context, in *TestToolRequest) (*TestToolResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestTool", ctx, in)
	ret0, _ := ret[0].(*TestToolResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestTool indicates an expected call of TestTool.
func (mr *MockChannelLiveShowListServerMockRecorder) TestTool(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestTool", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).TestTool), ctx, in)
}

// UnfoldShowList mocks base method.
func (m *MockChannelLiveShowListServer) UnfoldShowList(ctx context.Context, in *UnfoldShowListRequest) (*UnfoldShowListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnfoldShowList", ctx, in)
	ret0, _ := ret[0].(*UnfoldShowListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnfoldShowList indicates an expected call of UnfoldShowList.
func (mr *MockChannelLiveShowListServerMockRecorder) UnfoldShowList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfoldShowList", reflect.TypeOf((*MockChannelLiveShowListServer)(nil).UnfoldShowList), ctx, in)
}
