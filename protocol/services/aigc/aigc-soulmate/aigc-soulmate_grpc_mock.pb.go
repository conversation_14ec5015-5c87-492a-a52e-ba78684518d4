// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-soulmate.proto

package aigc_soulmate

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAigcSoulmateClient is a mock of AigcSoulmateClient interface.
type MockAigcSoulmateClient struct {
	ctrl     *gomock.Controller
	recorder *MockAigcSoulmateClientMockRecorder
}

// MockAigcSoulmateClientMockRecorder is the mock recorder for MockAigcSoulmateClient.
type MockAigcSoulmateClientMockRecorder struct {
	mock *MockAigcSoulmateClient
}

// NewMockAigcSoulmateClient creates a new mock instance.
func NewMockAigcSoulmateClient(ctrl *gomock.Controller) *MockAigcSoulmateClient {
	mock := &MockAigcSoulmateClient{ctrl: ctrl}
	mock.recorder = &MockAigcSoulmateClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcSoulmateClient) EXPECT() *MockAigcSoulmateClientMockRecorder {
	return m.recorder
}

// AddReadHeartText mocks base method.
func (m *MockAigcSoulmateClient) AddReadHeartText(ctx context.Context, in *AddReadHeartTextRequest, opts ...grpc.CallOption) (*AddReadHeartTextResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddReadHeartText", varargs...)
	ret0, _ := ret[0].(*AddReadHeartTextResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddReadHeartText indicates an expected call of AddReadHeartText.
func (mr *MockAigcSoulmateClientMockRecorder) AddReadHeartText(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddReadHeartText", reflect.TypeOf((*MockAigcSoulmateClient)(nil).AddReadHeartText), varargs...)
}

// AllocShareIdentifier mocks base method.
func (m *MockAigcSoulmateClient) AllocShareIdentifier(ctx context.Context, in *AllocShareIdentifierReq, opts ...grpc.CallOption) (*AllocShareIdentifierResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AllocShareIdentifier", varargs...)
	ret0, _ := ret[0].(*AllocShareIdentifierResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllocShareIdentifier indicates an expected call of AllocShareIdentifier.
func (mr *MockAigcSoulmateClientMockRecorder) AllocShareIdentifier(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocShareIdentifier", reflect.TypeOf((*MockAigcSoulmateClient)(nil).AllocShareIdentifier), varargs...)
}

// BatchAddRoleUser mocks base method.
func (m *MockAigcSoulmateClient) BatchAddRoleUser(ctx context.Context, in *BatchAddRoleUserRequest, opts ...grpc.CallOption) (*BatchAddRoleUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchAddRoleUser", varargs...)
	ret0, _ := ret[0].(*BatchAddRoleUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAddRoleUser indicates an expected call of BatchAddRoleUser.
func (mr *MockAigcSoulmateClientMockRecorder) BatchAddRoleUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddRoleUser", reflect.TypeOf((*MockAigcSoulmateClient)(nil).BatchAddRoleUser), varargs...)
}

// BatchCreateRole mocks base method.
func (m *MockAigcSoulmateClient) BatchCreateRole(ctx context.Context, in *BatchCreateRoleReq, opts ...grpc.CallOption) (*BatchCreateRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchCreateRole", varargs...)
	ret0, _ := ret[0].(*BatchCreateRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCreateRole indicates an expected call of BatchCreateRole.
func (mr *MockAigcSoulmateClientMockRecorder) BatchCreateRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateRole", reflect.TypeOf((*MockAigcSoulmateClient)(nil).BatchCreateRole), varargs...)
}

// BatchDelRoleUser mocks base method.
func (m *MockAigcSoulmateClient) BatchDelRoleUser(ctx context.Context, in *BatchDelRoleUserRequest, opts ...grpc.CallOption) (*BatchDelRoleUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchDelRoleUser", varargs...)
	ret0, _ := ret[0].(*BatchDelRoleUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDelRoleUser indicates an expected call of BatchDelRoleUser.
func (mr *MockAigcSoulmateClientMockRecorder) BatchDelRoleUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelRoleUser", reflect.TypeOf((*MockAigcSoulmateClient)(nil).BatchDelRoleUser), varargs...)
}

// BatchDeleteAIChatTemplate mocks base method.
func (m *MockAigcSoulmateClient) BatchDeleteAIChatTemplate(ctx context.Context, in *BatchDeleteAIChatTemplateReq, opts ...grpc.CallOption) (*BatchDeleteAIChatTemplateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchDeleteAIChatTemplate", varargs...)
	ret0, _ := ret[0].(*BatchDeleteAIChatTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDeleteAIChatTemplate indicates an expected call of BatchDeleteAIChatTemplate.
func (mr *MockAigcSoulmateClientMockRecorder) BatchDeleteAIChatTemplate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteAIChatTemplate", reflect.TypeOf((*MockAigcSoulmateClient)(nil).BatchDeleteAIChatTemplate), varargs...)
}

// BatchDeleteInteractiveGame mocks base method.
func (m *MockAigcSoulmateClient) BatchDeleteInteractiveGame(ctx context.Context, in *BatchDeleteInteractiveGameReq, opts ...grpc.CallOption) (*BatchDeleteInteractiveGameResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchDeleteInteractiveGame", varargs...)
	ret0, _ := ret[0].(*BatchDeleteInteractiveGameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDeleteInteractiveGame indicates an expected call of BatchDeleteInteractiveGame.
func (mr *MockAigcSoulmateClientMockRecorder) BatchDeleteInteractiveGame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteInteractiveGame", reflect.TypeOf((*MockAigcSoulmateClient)(nil).BatchDeleteInteractiveGame), varargs...)
}

// BatchDeleteUserRole mocks base method.
func (m *MockAigcSoulmateClient) BatchDeleteUserRole(ctx context.Context, in *BatchDeleteUserRoleReq, opts ...grpc.CallOption) (*BatchDeleteUserRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchDeleteUserRole", varargs...)
	ret0, _ := ret[0].(*BatchDeleteUserRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDeleteUserRole indicates an expected call of BatchDeleteUserRole.
func (mr *MockAigcSoulmateClientMockRecorder) BatchDeleteUserRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteUserRole", reflect.TypeOf((*MockAigcSoulmateClient)(nil).BatchDeleteUserRole), varargs...)
}

// BatchGetAIPartner mocks base method.
func (m *MockAigcSoulmateClient) BatchGetAIPartner(ctx context.Context, in *BatchGetAIPartnerReq, opts ...grpc.CallOption) (*BatchGetAIPartnerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetAIPartner", varargs...)
	ret0, _ := ret[0].(*BatchGetAIPartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAIPartner indicates an expected call of BatchGetAIPartner.
func (mr *MockAigcSoulmateClientMockRecorder) BatchGetAIPartner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAIPartner", reflect.TypeOf((*MockAigcSoulmateClient)(nil).BatchGetAIPartner), varargs...)
}

// BatchGetAIRoleCategory mocks base method.
func (m *MockAigcSoulmateClient) BatchGetAIRoleCategory(ctx context.Context, in *BatchGetAIRoleCategoryReq, opts ...grpc.CallOption) (*BatchGetAIRoleCategoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetAIRoleCategory", varargs...)
	ret0, _ := ret[0].(*BatchGetAIRoleCategoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAIRoleCategory indicates an expected call of BatchGetAIRoleCategory.
func (mr *MockAigcSoulmateClientMockRecorder) BatchGetAIRoleCategory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAIRoleCategory", reflect.TypeOf((*MockAigcSoulmateClient)(nil).BatchGetAIRoleCategory), varargs...)
}

// BatchGetReadHeartInfo mocks base method.
func (m *MockAigcSoulmateClient) BatchGetReadHeartInfo(ctx context.Context, in *BatchGetReadHeartInfoRequest, opts ...grpc.CallOption) (*BatchGetReadHeartInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetReadHeartInfo", varargs...)
	ret0, _ := ret[0].(*BatchGetReadHeartInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetReadHeartInfo indicates an expected call of BatchGetReadHeartInfo.
func (mr *MockAigcSoulmateClientMockRecorder) BatchGetReadHeartInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetReadHeartInfo", reflect.TypeOf((*MockAigcSoulmateClient)(nil).BatchGetReadHeartInfo), varargs...)
}

// BatchGetRoleUser mocks base method.
func (m *MockAigcSoulmateClient) BatchGetRoleUser(ctx context.Context, in *BatchGetRoleUserRequest, opts ...grpc.CallOption) (*BatchGetRoleUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetRoleUser", varargs...)
	ret0, _ := ret[0].(*BatchGetRoleUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetRoleUser indicates an expected call of BatchGetRoleUser.
func (mr *MockAigcSoulmateClientMockRecorder) BatchGetRoleUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetRoleUser", reflect.TypeOf((*MockAigcSoulmateClient)(nil).BatchGetRoleUser), varargs...)
}

// BatchUpdateInteractiveGame mocks base method.
func (m *MockAigcSoulmateClient) BatchUpdateInteractiveGame(ctx context.Context, in *BatchUpdateInteractiveGameReq, opts ...grpc.CallOption) (*BatchUpdateInteractiveGameResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchUpdateInteractiveGame", varargs...)
	ret0, _ := ret[0].(*BatchUpdateInteractiveGameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdateInteractiveGame indicates an expected call of BatchUpdateInteractiveGame.
func (mr *MockAigcSoulmateClientMockRecorder) BatchUpdateInteractiveGame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateInteractiveGame", reflect.TypeOf((*MockAigcSoulmateClient)(nil).BatchUpdateInteractiveGame), varargs...)
}

// BatchUpdateRole mocks base method.
func (m *MockAigcSoulmateClient) BatchUpdateRole(ctx context.Context, in *BatchUpdateRoleReq, opts ...grpc.CallOption) (*BatchUpdateRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchUpdateRole", varargs...)
	ret0, _ := ret[0].(*BatchUpdateRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdateRole indicates an expected call of BatchUpdateRole.
func (mr *MockAigcSoulmateClientMockRecorder) BatchUpdateRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateRole", reflect.TypeOf((*MockAigcSoulmateClient)(nil).BatchUpdateRole), varargs...)
}

// BatchUpdateUserRole mocks base method.
func (m *MockAigcSoulmateClient) BatchUpdateUserRole(ctx context.Context, in *BatchUpdateUserRoleReq, opts ...grpc.CallOption) (*BatchUpdateUserRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchUpdateUserRole", varargs...)
	ret0, _ := ret[0].(*BatchUpdateUserRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdateUserRole indicates an expected call of BatchUpdateUserRole.
func (mr *MockAigcSoulmateClientMockRecorder) BatchUpdateUserRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateUserRole", reflect.TypeOf((*MockAigcSoulmateClient)(nil).BatchUpdateUserRole), varargs...)
}

// ChangeAIPartnerRole mocks base method.
func (m *MockAigcSoulmateClient) ChangeAIPartnerRole(ctx context.Context, in *ChangeAIPartnerRoleReq, opts ...grpc.CallOption) (*ChangeAIPartnerRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ChangeAIPartnerRole", varargs...)
	ret0, _ := ret[0].(*ChangeAIPartnerRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangeAIPartnerRole indicates an expected call of ChangeAIPartnerRole.
func (mr *MockAigcSoulmateClientMockRecorder) ChangeAIPartnerRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeAIPartnerRole", reflect.TypeOf((*MockAigcSoulmateClient)(nil).ChangeAIPartnerRole), varargs...)
}

// ClearAIMessage mocks base method.
func (m *MockAigcSoulmateClient) ClearAIMessage(ctx context.Context, in *ClearAIMessageReq, opts ...grpc.CallOption) (*ClearAIMessageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearAIMessage", varargs...)
	ret0, _ := ret[0].(*ClearAIMessageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearAIMessage indicates an expected call of ClearAIMessage.
func (mr *MockAigcSoulmateClientMockRecorder) ClearAIMessage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearAIMessage", reflect.TypeOf((*MockAigcSoulmateClient)(nil).ClearAIMessage), varargs...)
}

// CreateAIChatTemplate mocks base method.
func (m *MockAigcSoulmateClient) CreateAIChatTemplate(ctx context.Context, in *CreateAIChatTemplateReq, opts ...grpc.CallOption) (*CreateAIChatTemplateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAIChatTemplate", varargs...)
	ret0, _ := ret[0].(*CreateAIChatTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAIChatTemplate indicates an expected call of CreateAIChatTemplate.
func (mr *MockAigcSoulmateClientMockRecorder) CreateAIChatTemplate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAIChatTemplate", reflect.TypeOf((*MockAigcSoulmateClient)(nil).CreateAIChatTemplate), varargs...)
}

// CreateAIPartner mocks base method.
func (m *MockAigcSoulmateClient) CreateAIPartner(ctx context.Context, in *CreateAIPartnerReq, opts ...grpc.CallOption) (*CreateAIPartnerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAIPartner", varargs...)
	ret0, _ := ret[0].(*CreateAIPartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAIPartner indicates an expected call of CreateAIPartner.
func (mr *MockAigcSoulmateClientMockRecorder) CreateAIPartner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAIPartner", reflect.TypeOf((*MockAigcSoulmateClient)(nil).CreateAIPartner), varargs...)
}

// CreateAIRole mocks base method.
func (m *MockAigcSoulmateClient) CreateAIRole(ctx context.Context, in *CreateAIRoleReq, opts ...grpc.CallOption) (*CreateAIRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAIRole", varargs...)
	ret0, _ := ret[0].(*CreateAIRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAIRole indicates an expected call of CreateAIRole.
func (mr *MockAigcSoulmateClientMockRecorder) CreateAIRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAIRole", reflect.TypeOf((*MockAigcSoulmateClient)(nil).CreateAIRole), varargs...)
}

// CreateInteractiveGame mocks base method.
func (m *MockAigcSoulmateClient) CreateInteractiveGame(ctx context.Context, in *CreateInteractiveGameReq, opts ...grpc.CallOption) (*CreateInteractiveGameResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateInteractiveGame", varargs...)
	ret0, _ := ret[0].(*CreateInteractiveGameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInteractiveGame indicates an expected call of CreateInteractiveGame.
func (mr *MockAigcSoulmateClientMockRecorder) CreateInteractiveGame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInteractiveGame", reflect.TypeOf((*MockAigcSoulmateClient)(nil).CreateInteractiveGame), varargs...)
}

// DeleteAIPartner mocks base method.
func (m *MockAigcSoulmateClient) DeleteAIPartner(ctx context.Context, in *DeleteAIPartnerReq, opts ...grpc.CallOption) (*DeleteAIPartnerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteAIPartner", varargs...)
	ret0, _ := ret[0].(*DeleteAIPartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAIPartner indicates an expected call of DeleteAIPartner.
func (mr *MockAigcSoulmateClientMockRecorder) DeleteAIPartner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAIPartner", reflect.TypeOf((*MockAigcSoulmateClient)(nil).DeleteAIPartner), varargs...)
}

// DeleteAIRole mocks base method.
func (m *MockAigcSoulmateClient) DeleteAIRole(ctx context.Context, in *DeleteAIRoleReq, opts ...grpc.CallOption) (*DeleteAIRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteAIRole", varargs...)
	ret0, _ := ret[0].(*DeleteAIRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAIRole indicates an expected call of DeleteAIRole.
func (mr *MockAigcSoulmateClientMockRecorder) DeleteAIRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAIRole", reflect.TypeOf((*MockAigcSoulmateClient)(nil).DeleteAIRole), varargs...)
}

// DeleteAIRoleCategory mocks base method.
func (m *MockAigcSoulmateClient) DeleteAIRoleCategory(ctx context.Context, in *DeleteAIRoleCategoryReq, opts ...grpc.CallOption) (*DeleteAIRoleCategoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteAIRoleCategory", varargs...)
	ret0, _ := ret[0].(*DeleteAIRoleCategoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAIRoleCategory indicates an expected call of DeleteAIRoleCategory.
func (mr *MockAigcSoulmateClientMockRecorder) DeleteAIRoleCategory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAIRoleCategory", reflect.TypeOf((*MockAigcSoulmateClient)(nil).DeleteAIRoleCategory), varargs...)
}

// GetAIChatTemplateList mocks base method.
func (m *MockAigcSoulmateClient) GetAIChatTemplateList(ctx context.Context, in *GetAIChatTemplateListReq, opts ...grpc.CallOption) (*GetAIChatTemplateListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAIChatTemplateList", varargs...)
	ret0, _ := ret[0].(*GetAIChatTemplateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIChatTemplateList indicates an expected call of GetAIChatTemplateList.
func (mr *MockAigcSoulmateClientMockRecorder) GetAIChatTemplateList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIChatTemplateList", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetAIChatTemplateList), varargs...)
}

// GetAIPartner mocks base method.
func (m *MockAigcSoulmateClient) GetAIPartner(ctx context.Context, in *GetAIPartnerReq, opts ...grpc.CallOption) (*GetAIPartnerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAIPartner", varargs...)
	ret0, _ := ret[0].(*GetAIPartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIPartner indicates an expected call of GetAIPartner.
func (mr *MockAigcSoulmateClientMockRecorder) GetAIPartner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIPartner", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetAIPartner), varargs...)
}

// GetAIRole mocks base method.
func (m *MockAigcSoulmateClient) GetAIRole(ctx context.Context, in *GetAIRoleReq, opts ...grpc.CallOption) (*GetAIRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAIRole", varargs...)
	ret0, _ := ret[0].(*GetAIRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIRole indicates an expected call of GetAIRole.
func (mr *MockAigcSoulmateClientMockRecorder) GetAIRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIRole", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetAIRole), varargs...)
}

// GetAIRoleList mocks base method.
func (m *MockAigcSoulmateClient) GetAIRoleList(ctx context.Context, in *GetAIRoleListReq, opts ...grpc.CallOption) (*GetAIRoleListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAIRoleList", varargs...)
	ret0, _ := ret[0].(*GetAIRoleListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIRoleList indicates an expected call of GetAIRoleList.
func (mr *MockAigcSoulmateClientMockRecorder) GetAIRoleList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIRoleList", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetAIRoleList), varargs...)
}

// GetBannerRoleList mocks base method.
func (m *MockAigcSoulmateClient) GetBannerRoleList(ctx context.Context, in *GetBannerRoleListReq, opts ...grpc.CallOption) (*GetBannerRoleListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBannerRoleList", varargs...)
	ret0, _ := ret[0].(*GetBannerRoleListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBannerRoleList indicates an expected call of GetBannerRoleList.
func (mr *MockAigcSoulmateClientMockRecorder) GetBannerRoleList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBannerRoleList", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetBannerRoleList), varargs...)
}

// GetBindChatTemplates mocks base method.
func (m *MockAigcSoulmateClient) GetBindChatTemplates(ctx context.Context, in *GetBindChatTemplatesReq, opts ...grpc.CallOption) (*GetBindChatTemplatesResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBindChatTemplates", varargs...)
	ret0, _ := ret[0].(*GetBindChatTemplatesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindChatTemplates indicates an expected call of GetBindChatTemplates.
func (mr *MockAigcSoulmateClientMockRecorder) GetBindChatTemplates(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindChatTemplates", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetBindChatTemplates), varargs...)
}

// GetEntranceBanRoleId mocks base method.
func (m *MockAigcSoulmateClient) GetEntranceBanRoleId(ctx context.Context, in *GetEntranceBanRoleIdRequest, opts ...grpc.CallOption) (*GetEntranceBanRoleIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEntranceBanRoleId", varargs...)
	ret0, _ := ret[0].(*GetEntranceBanRoleIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntranceBanRoleId indicates an expected call of GetEntranceBanRoleId.
func (mr *MockAigcSoulmateClientMockRecorder) GetEntranceBanRoleId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntranceBanRoleId", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetEntranceBanRoleId), varargs...)
}

// GetInteractiveGame mocks base method.
func (m *MockAigcSoulmateClient) GetInteractiveGame(ctx context.Context, in *GetInteractiveGameReq, opts ...grpc.CallOption) (*GetInteractiveGameResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInteractiveGame", varargs...)
	ret0, _ := ret[0].(*GetInteractiveGameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInteractiveGame indicates an expected call of GetInteractiveGame.
func (mr *MockAigcSoulmateClientMockRecorder) GetInteractiveGame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInteractiveGame", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetInteractiveGame), varargs...)
}

// GetLatestUpdatedAIRoleList mocks base method.
func (m *MockAigcSoulmateClient) GetLatestUpdatedAIRoleList(ctx context.Context, in *GetLatestUpdatedAIRoleListReq, opts ...grpc.CallOption) (*GetLatestUpdatedAIRoleListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLatestUpdatedAIRoleList", varargs...)
	ret0, _ := ret[0].(*GetLatestUpdatedAIRoleListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestUpdatedAIRoleList indicates an expected call of GetLatestUpdatedAIRoleList.
func (mr *MockAigcSoulmateClientMockRecorder) GetLatestUpdatedAIRoleList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestUpdatedAIRoleList", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetLatestUpdatedAIRoleList), varargs...)
}

// GetOfficialAIRoleList mocks base method.
func (m *MockAigcSoulmateClient) GetOfficialAIRoleList(ctx context.Context, in *GetOfficialAIRoleListReq, opts ...grpc.CallOption) (*GetOfficialAIRoleListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOfficialAIRoleList", varargs...)
	ret0, _ := ret[0].(*GetOfficialAIRoleListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOfficialAIRoleList indicates an expected call of GetOfficialAIRoleList.
func (mr *MockAigcSoulmateClientMockRecorder) GetOfficialAIRoleList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOfficialAIRoleList", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetOfficialAIRoleList), varargs...)
}

// GetReadHeartEntrance mocks base method.
func (m *MockAigcSoulmateClient) GetReadHeartEntrance(ctx context.Context, in *GetReadHeartEntranceRequest, opts ...grpc.CallOption) (*GetReadHeartEntranceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetReadHeartEntrance", varargs...)
	ret0, _ := ret[0].(*GetReadHeartEntranceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReadHeartEntrance indicates an expected call of GetReadHeartEntrance.
func (mr *MockAigcSoulmateClientMockRecorder) GetReadHeartEntrance(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReadHeartEntrance", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetReadHeartEntrance), varargs...)
}

// GetRoleUidList mocks base method.
func (m *MockAigcSoulmateClient) GetRoleUidList(ctx context.Context, in *GetRoleUidListRequest, opts ...grpc.CallOption) (*GetRoleUidListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRoleUidList", varargs...)
	ret0, _ := ret[0].(*GetRoleUidListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRoleUidList indicates an expected call of GetRoleUidList.
func (mr *MockAigcSoulmateClientMockRecorder) GetRoleUidList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoleUidList", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetRoleUidList), varargs...)
}

// GetSharedRole mocks base method.
func (m *MockAigcSoulmateClient) GetSharedRole(ctx context.Context, in *GetSharedRoleReq, opts ...grpc.CallOption) (*GetSharedRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSharedRole", varargs...)
	ret0, _ := ret[0].(*GetSharedRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSharedRole indicates an expected call of GetSharedRole.
func (mr *MockAigcSoulmateClientMockRecorder) GetSharedRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSharedRole", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetSharedRole), varargs...)
}

// GetUserAIPartner mocks base method.
func (m *MockAigcSoulmateClient) GetUserAIPartner(ctx context.Context, in *GetUserAIPartnerReq, opts ...grpc.CallOption) (*GetUserAIPartnerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserAIPartner", varargs...)
	ret0, _ := ret[0].(*GetUserAIPartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAIPartner indicates an expected call of GetUserAIPartner.
func (mr *MockAigcSoulmateClientMockRecorder) GetUserAIPartner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAIPartner", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetUserAIPartner), varargs...)
}

// GetUserAIPartnerList mocks base method.
func (m *MockAigcSoulmateClient) GetUserAIPartnerList(ctx context.Context, in *GetUserAIPartnerListReq, opts ...grpc.CallOption) (*GetUserAIPartnerListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserAIPartnerList", varargs...)
	ret0, _ := ret[0].(*GetUserAIPartnerListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAIPartnerList indicates an expected call of GetUserAIPartnerList.
func (mr *MockAigcSoulmateClientMockRecorder) GetUserAIPartnerList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAIPartnerList", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetUserAIPartnerList), varargs...)
}

// GetUserAIRoleLikes mocks base method.
func (m *MockAigcSoulmateClient) GetUserAIRoleLikes(ctx context.Context, in *GetUserAIRoleLikesReq, opts ...grpc.CallOption) (*GetUserAIRoleLikesResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserAIRoleLikes", varargs...)
	ret0, _ := ret[0].(*GetUserAIRoleLikesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAIRoleLikes indicates an expected call of GetUserAIRoleLikes.
func (mr *MockAigcSoulmateClientMockRecorder) GetUserAIRoleLikes(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAIRoleLikes", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetUserAIRoleLikes), varargs...)
}

// GetUserAIRoleList mocks base method.
func (m *MockAigcSoulmateClient) GetUserAIRoleList(ctx context.Context, in *GetUserAIRoleListReq, opts ...grpc.CallOption) (*GetUserAIRoleListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserAIRoleList", varargs...)
	ret0, _ := ret[0].(*GetUserAIRoleListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAIRoleList indicates an expected call of GetUserAIRoleList.
func (mr *MockAigcSoulmateClientMockRecorder) GetUserAIRoleList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAIRoleList", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetUserAIRoleList), varargs...)
}

// GetUserAIRoleListWithAppoint mocks base method.
func (m *MockAigcSoulmateClient) GetUserAIRoleListWithAppoint(ctx context.Context, in *GetUserAIRoleListWithAppointReq, opts ...grpc.CallOption) (*GetUserAIRoleListWithAppointResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserAIRoleListWithAppoint", varargs...)
	ret0, _ := ret[0].(*GetUserAIRoleListWithAppointResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAIRoleListWithAppoint indicates an expected call of GetUserAIRoleListWithAppoint.
func (mr *MockAigcSoulmateClientMockRecorder) GetUserAIRoleListWithAppoint(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAIRoleListWithAppoint", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetUserAIRoleListWithAppoint), varargs...)
}

// GetUserExclusiveRoleList mocks base method.
func (m *MockAigcSoulmateClient) GetUserExclusiveRoleList(ctx context.Context, in *GetUserExclusiveRoleListRequest, opts ...grpc.CallOption) (*GetUserExclusiveRoleListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserExclusiveRoleList", varargs...)
	ret0, _ := ret[0].(*GetUserExclusiveRoleListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExclusiveRoleList indicates an expected call of GetUserExclusiveRoleList.
func (mr *MockAigcSoulmateClientMockRecorder) GetUserExclusiveRoleList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExclusiveRoleList", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetUserExclusiveRoleList), varargs...)
}

// GetUserInteractiveGameList mocks base method.
func (m *MockAigcSoulmateClient) GetUserInteractiveGameList(ctx context.Context, in *GetUserInteractiveGameListReq, opts ...grpc.CallOption) (*GetUserInteractiveGameListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserInteractiveGameList", varargs...)
	ret0, _ := ret[0].(*GetUserInteractiveGameListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInteractiveGameList indicates an expected call of GetUserInteractiveGameList.
func (mr *MockAigcSoulmateClientMockRecorder) GetUserInteractiveGameList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInteractiveGameList", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetUserInteractiveGameList), varargs...)
}

// GetUserReadHeartCnt mocks base method.
func (m *MockAigcSoulmateClient) GetUserReadHeartCnt(ctx context.Context, in *GetUserReadHeartCntRequest, opts ...grpc.CallOption) (*GetUserReadHeartCntResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserReadHeartCnt", varargs...)
	ret0, _ := ret[0].(*GetUserReadHeartCntResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserReadHeartCnt indicates an expected call of GetUserReadHeartCnt.
func (mr *MockAigcSoulmateClientMockRecorder) GetUserReadHeartCnt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserReadHeartCnt", reflect.TypeOf((*MockAigcSoulmateClient)(nil).GetUserReadHeartCnt), varargs...)
}

// LikeAIRole mocks base method.
func (m *MockAigcSoulmateClient) LikeAIRole(ctx context.Context, in *LikeAIRoleReq, opts ...grpc.CallOption) (*LikeAIRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LikeAIRole", varargs...)
	ret0, _ := ret[0].(*LikeAIRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LikeAIRole indicates an expected call of LikeAIRole.
func (mr *MockAigcSoulmateClientMockRecorder) LikeAIRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LikeAIRole", reflect.TypeOf((*MockAigcSoulmateClient)(nil).LikeAIRole), varargs...)
}

// PullAIMessage mocks base method.
func (m *MockAigcSoulmateClient) PullAIMessage(ctx context.Context, in *PullAIMessageReq, opts ...grpc.CallOption) (*PullAIMessageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PullAIMessage", varargs...)
	ret0, _ := ret[0].(*PullAIMessageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PullAIMessage indicates an expected call of PullAIMessage.
func (mr *MockAigcSoulmateClientMockRecorder) PullAIMessage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PullAIMessage", reflect.TypeOf((*MockAigcSoulmateClient)(nil).PullAIMessage), varargs...)
}

// ResortAIRoleCategory mocks base method.
func (m *MockAigcSoulmateClient) ResortAIRoleCategory(ctx context.Context, in *ResortAIRoleCategoryReq, opts ...grpc.CallOption) (*ResortAIRoleCategoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResortAIRoleCategory", varargs...)
	ret0, _ := ret[0].(*ResortAIRoleCategoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResortAIRoleCategory indicates an expected call of ResortAIRoleCategory.
func (mr *MockAigcSoulmateClientMockRecorder) ResortAIRoleCategory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResortAIRoleCategory", reflect.TypeOf((*MockAigcSoulmateClient)(nil).ResortAIRoleCategory), varargs...)
}

// SearchAIRole mocks base method.
func (m *MockAigcSoulmateClient) SearchAIRole(ctx context.Context, in *SearchAIRoleReq, opts ...grpc.CallOption) (*SearchAIRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchAIRole", varargs...)
	ret0, _ := ret[0].(*SearchAIRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAIRole indicates an expected call of SearchAIRole.
func (mr *MockAigcSoulmateClientMockRecorder) SearchAIRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAIRole", reflect.TypeOf((*MockAigcSoulmateClient)(nil).SearchAIRole), varargs...)
}

// SearchInteractiveGame mocks base method.
func (m *MockAigcSoulmateClient) SearchInteractiveGame(ctx context.Context, in *SearchInteractiveGameReq, opts ...grpc.CallOption) (*SearchInteractiveGameResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchInteractiveGame", varargs...)
	ret0, _ := ret[0].(*SearchInteractiveGameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchInteractiveGame indicates an expected call of SearchInteractiveGame.
func (mr *MockAigcSoulmateClientMockRecorder) SearchInteractiveGame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchInteractiveGame", reflect.TypeOf((*MockAigcSoulmateClient)(nil).SearchInteractiveGame), varargs...)
}

// SearchUserRole mocks base method.
func (m *MockAigcSoulmateClient) SearchUserRole(ctx context.Context, in *SearchUserRoleReq, opts ...grpc.CallOption) (*SearchUserRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchUserRole", varargs...)
	ret0, _ := ret[0].(*SearchUserRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchUserRole indicates an expected call of SearchUserRole.
func (mr *MockAigcSoulmateClientMockRecorder) SearchUserRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchUserRole", reflect.TypeOf((*MockAigcSoulmateClient)(nil).SearchUserRole), varargs...)
}

// SetBannerRoleList mocks base method.
func (m *MockAigcSoulmateClient) SetBannerRoleList(ctx context.Context, in *SetBannerRoleListReq, opts ...grpc.CallOption) (*SetBannerRoleListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetBannerRoleList", varargs...)
	ret0, _ := ret[0].(*SetBannerRoleListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetBannerRoleList indicates an expected call of SetBannerRoleList.
func (mr *MockAigcSoulmateClientMockRecorder) SetBannerRoleList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBannerRoleList", reflect.TypeOf((*MockAigcSoulmateClient)(nil).SetBannerRoleList), varargs...)
}

// ShareRole mocks base method.
func (m *MockAigcSoulmateClient) ShareRole(ctx context.Context, in *ShareRoleReq, opts ...grpc.CallOption) (*ShareRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ShareRole", varargs...)
	ret0, _ := ret[0].(*ShareRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShareRole indicates an expected call of ShareRole.
func (mr *MockAigcSoulmateClientMockRecorder) ShareRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShareRole", reflect.TypeOf((*MockAigcSoulmateClient)(nil).ShareRole), varargs...)
}

// TryCreateAIPartner mocks base method.
func (m *MockAigcSoulmateClient) TryCreateAIPartner(ctx context.Context, in *CreateAIPartnerReq, opts ...grpc.CallOption) (*CreateAIPartnerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TryCreateAIPartner", varargs...)
	ret0, _ := ret[0].(*CreateAIPartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TryCreateAIPartner indicates an expected call of TryCreateAIPartner.
func (mr *MockAigcSoulmateClientMockRecorder) TryCreateAIPartner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TryCreateAIPartner", reflect.TypeOf((*MockAigcSoulmateClient)(nil).TryCreateAIPartner), varargs...)
}

// UnlikeAIRole mocks base method.
func (m *MockAigcSoulmateClient) UnlikeAIRole(ctx context.Context, in *UnlikeAIRoleReq, opts ...grpc.CallOption) (*UnlikeAIRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnlikeAIRole", varargs...)
	ret0, _ := ret[0].(*UnlikeAIRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnlikeAIRole indicates an expected call of UnlikeAIRole.
func (mr *MockAigcSoulmateClientMockRecorder) UnlikeAIRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnlikeAIRole", reflect.TypeOf((*MockAigcSoulmateClient)(nil).UnlikeAIRole), varargs...)
}

// UpdateAIChatTemplate mocks base method.
func (m *MockAigcSoulmateClient) UpdateAIChatTemplate(ctx context.Context, in *UpdateAIChatTemplateReq, opts ...grpc.CallOption) (*UpdateAIChatTemplateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAIChatTemplate", varargs...)
	ret0, _ := ret[0].(*UpdateAIChatTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIChatTemplate indicates an expected call of UpdateAIChatTemplate.
func (mr *MockAigcSoulmateClientMockRecorder) UpdateAIChatTemplate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIChatTemplate", reflect.TypeOf((*MockAigcSoulmateClient)(nil).UpdateAIChatTemplate), varargs...)
}

// UpdateAIPartner mocks base method.
func (m *MockAigcSoulmateClient) UpdateAIPartner(ctx context.Context, in *UpdateAIPartnerReq, opts ...grpc.CallOption) (*UpdateAIPartnerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAIPartner", varargs...)
	ret0, _ := ret[0].(*UpdateAIPartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIPartner indicates an expected call of UpdateAIPartner.
func (mr *MockAigcSoulmateClientMockRecorder) UpdateAIPartner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIPartner", reflect.TypeOf((*MockAigcSoulmateClient)(nil).UpdateAIPartner), varargs...)
}

// UpdateAIPartnerChatState mocks base method.
func (m *MockAigcSoulmateClient) UpdateAIPartnerChatState(ctx context.Context, in *UpdateAIPartnerChatStateReq, opts ...grpc.CallOption) (*UpdateAIPartnerChatStateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAIPartnerChatState", varargs...)
	ret0, _ := ret[0].(*UpdateAIPartnerChatStateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIPartnerChatState indicates an expected call of UpdateAIPartnerChatState.
func (mr *MockAigcSoulmateClientMockRecorder) UpdateAIPartnerChatState(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIPartnerChatState", reflect.TypeOf((*MockAigcSoulmateClient)(nil).UpdateAIPartnerChatState), varargs...)
}

// UpdateAIRole mocks base method.
func (m *MockAigcSoulmateClient) UpdateAIRole(ctx context.Context, in *UpdateAIRoleReq, opts ...grpc.CallOption) (*UpdateAIRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAIRole", varargs...)
	ret0, _ := ret[0].(*UpdateAIRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIRole indicates an expected call of UpdateAIRole.
func (mr *MockAigcSoulmateClientMockRecorder) UpdateAIRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIRole", reflect.TypeOf((*MockAigcSoulmateClient)(nil).UpdateAIRole), varargs...)
}

// UpdateAIRoleAuditResult mocks base method.
func (m *MockAigcSoulmateClient) UpdateAIRoleAuditResult(ctx context.Context, in *UpdateAIRoleAuditResultReq, opts ...grpc.CallOption) (*UpdateAIRoleAuditResultResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAIRoleAuditResult", varargs...)
	ret0, _ := ret[0].(*UpdateAIRoleAuditResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIRoleAuditResult indicates an expected call of UpdateAIRoleAuditResult.
func (mr *MockAigcSoulmateClientMockRecorder) UpdateAIRoleAuditResult(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIRoleAuditResult", reflect.TypeOf((*MockAigcSoulmateClient)(nil).UpdateAIRoleAuditResult), varargs...)
}

// UpdateEntranceBanRoleId mocks base method.
func (m *MockAigcSoulmateClient) UpdateEntranceBanRoleId(ctx context.Context, in *UpdateEntranceBanRoleIdRequest, opts ...grpc.CallOption) (*UpdateEntranceBanRoleIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateEntranceBanRoleId", varargs...)
	ret0, _ := ret[0].(*UpdateEntranceBanRoleIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateEntranceBanRoleId indicates an expected call of UpdateEntranceBanRoleId.
func (mr *MockAigcSoulmateClientMockRecorder) UpdateEntranceBanRoleId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEntranceBanRoleId", reflect.TypeOf((*MockAigcSoulmateClient)(nil).UpdateEntranceBanRoleId), varargs...)
}

// UpdateInteractiveGame mocks base method.
func (m *MockAigcSoulmateClient) UpdateInteractiveGame(ctx context.Context, in *UpdateInteractiveGameReq, opts ...grpc.CallOption) (*UpdateInteractiveGameResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateInteractiveGame", varargs...)
	ret0, _ := ret[0].(*UpdateInteractiveGameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInteractiveGame indicates an expected call of UpdateInteractiveGame.
func (mr *MockAigcSoulmateClientMockRecorder) UpdateInteractiveGame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInteractiveGame", reflect.TypeOf((*MockAigcSoulmateClient)(nil).UpdateInteractiveGame), varargs...)
}

// UpsertAIRoleCategory mocks base method.
func (m *MockAigcSoulmateClient) UpsertAIRoleCategory(ctx context.Context, in *UpsertAIRoleCategoryReq, opts ...grpc.CallOption) (*UpsertAIRoleCategoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertAIRoleCategory", varargs...)
	ret0, _ := ret[0].(*UpsertAIRoleCategoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertAIRoleCategory indicates an expected call of UpsertAIRoleCategory.
func (mr *MockAigcSoulmateClientMockRecorder) UpsertAIRoleCategory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertAIRoleCategory", reflect.TypeOf((*MockAigcSoulmateClient)(nil).UpsertAIRoleCategory), varargs...)
}

// VerifyShareIdentifier mocks base method.
func (m *MockAigcSoulmateClient) VerifyShareIdentifier(ctx context.Context, in *VerifyShareIdentifierReq, opts ...grpc.CallOption) (*VerifyShareIdentifierResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyShareIdentifier", varargs...)
	ret0, _ := ret[0].(*VerifyShareIdentifierResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyShareIdentifier indicates an expected call of VerifyShareIdentifier.
func (mr *MockAigcSoulmateClientMockRecorder) VerifyShareIdentifier(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyShareIdentifier", reflect.TypeOf((*MockAigcSoulmateClient)(nil).VerifyShareIdentifier), varargs...)
}

// WriteAIMessage mocks base method.
func (m *MockAigcSoulmateClient) WriteAIMessage(ctx context.Context, in *WriteAIMessageReq, opts ...grpc.CallOption) (*WriteAIMessageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "WriteAIMessage", varargs...)
	ret0, _ := ret[0].(*WriteAIMessageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// WriteAIMessage indicates an expected call of WriteAIMessage.
func (mr *MockAigcSoulmateClientMockRecorder) WriteAIMessage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteAIMessage", reflect.TypeOf((*MockAigcSoulmateClient)(nil).WriteAIMessage), varargs...)
}

// MockAigcSoulmateServer is a mock of AigcSoulmateServer interface.
type MockAigcSoulmateServer struct {
	ctrl     *gomock.Controller
	recorder *MockAigcSoulmateServerMockRecorder
}

// MockAigcSoulmateServerMockRecorder is the mock recorder for MockAigcSoulmateServer.
type MockAigcSoulmateServerMockRecorder struct {
	mock *MockAigcSoulmateServer
}

// NewMockAigcSoulmateServer creates a new mock instance.
func NewMockAigcSoulmateServer(ctrl *gomock.Controller) *MockAigcSoulmateServer {
	mock := &MockAigcSoulmateServer{ctrl: ctrl}
	mock.recorder = &MockAigcSoulmateServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcSoulmateServer) EXPECT() *MockAigcSoulmateServerMockRecorder {
	return m.recorder
}

// AddReadHeartText mocks base method.
func (m *MockAigcSoulmateServer) AddReadHeartText(ctx context.Context, in *AddReadHeartTextRequest) (*AddReadHeartTextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddReadHeartText", ctx, in)
	ret0, _ := ret[0].(*AddReadHeartTextResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddReadHeartText indicates an expected call of AddReadHeartText.
func (mr *MockAigcSoulmateServerMockRecorder) AddReadHeartText(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddReadHeartText", reflect.TypeOf((*MockAigcSoulmateServer)(nil).AddReadHeartText), ctx, in)
}

// AllocShareIdentifier mocks base method.
func (m *MockAigcSoulmateServer) AllocShareIdentifier(ctx context.Context, in *AllocShareIdentifierReq) (*AllocShareIdentifierResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AllocShareIdentifier", ctx, in)
	ret0, _ := ret[0].(*AllocShareIdentifierResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllocShareIdentifier indicates an expected call of AllocShareIdentifier.
func (mr *MockAigcSoulmateServerMockRecorder) AllocShareIdentifier(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocShareIdentifier", reflect.TypeOf((*MockAigcSoulmateServer)(nil).AllocShareIdentifier), ctx, in)
}

// BatchAddRoleUser mocks base method.
func (m *MockAigcSoulmateServer) BatchAddRoleUser(ctx context.Context, in *BatchAddRoleUserRequest) (*BatchAddRoleUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddRoleUser", ctx, in)
	ret0, _ := ret[0].(*BatchAddRoleUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAddRoleUser indicates an expected call of BatchAddRoleUser.
func (mr *MockAigcSoulmateServerMockRecorder) BatchAddRoleUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddRoleUser", reflect.TypeOf((*MockAigcSoulmateServer)(nil).BatchAddRoleUser), ctx, in)
}

// BatchCreateRole mocks base method.
func (m *MockAigcSoulmateServer) BatchCreateRole(ctx context.Context, in *BatchCreateRoleReq) (*BatchCreateRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateRole", ctx, in)
	ret0, _ := ret[0].(*BatchCreateRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCreateRole indicates an expected call of BatchCreateRole.
func (mr *MockAigcSoulmateServerMockRecorder) BatchCreateRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateRole", reflect.TypeOf((*MockAigcSoulmateServer)(nil).BatchCreateRole), ctx, in)
}

// BatchDelRoleUser mocks base method.
func (m *MockAigcSoulmateServer) BatchDelRoleUser(ctx context.Context, in *BatchDelRoleUserRequest) (*BatchDelRoleUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelRoleUser", ctx, in)
	ret0, _ := ret[0].(*BatchDelRoleUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDelRoleUser indicates an expected call of BatchDelRoleUser.
func (mr *MockAigcSoulmateServerMockRecorder) BatchDelRoleUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelRoleUser", reflect.TypeOf((*MockAigcSoulmateServer)(nil).BatchDelRoleUser), ctx, in)
}

// BatchDeleteAIChatTemplate mocks base method.
func (m *MockAigcSoulmateServer) BatchDeleteAIChatTemplate(ctx context.Context, in *BatchDeleteAIChatTemplateReq) (*BatchDeleteAIChatTemplateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteAIChatTemplate", ctx, in)
	ret0, _ := ret[0].(*BatchDeleteAIChatTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDeleteAIChatTemplate indicates an expected call of BatchDeleteAIChatTemplate.
func (mr *MockAigcSoulmateServerMockRecorder) BatchDeleteAIChatTemplate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteAIChatTemplate", reflect.TypeOf((*MockAigcSoulmateServer)(nil).BatchDeleteAIChatTemplate), ctx, in)
}

// BatchDeleteInteractiveGame mocks base method.
func (m *MockAigcSoulmateServer) BatchDeleteInteractiveGame(ctx context.Context, in *BatchDeleteInteractiveGameReq) (*BatchDeleteInteractiveGameResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteInteractiveGame", ctx, in)
	ret0, _ := ret[0].(*BatchDeleteInteractiveGameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDeleteInteractiveGame indicates an expected call of BatchDeleteInteractiveGame.
func (mr *MockAigcSoulmateServerMockRecorder) BatchDeleteInteractiveGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteInteractiveGame", reflect.TypeOf((*MockAigcSoulmateServer)(nil).BatchDeleteInteractiveGame), ctx, in)
}

// BatchDeleteUserRole mocks base method.
func (m *MockAigcSoulmateServer) BatchDeleteUserRole(ctx context.Context, in *BatchDeleteUserRoleReq) (*BatchDeleteUserRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteUserRole", ctx, in)
	ret0, _ := ret[0].(*BatchDeleteUserRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDeleteUserRole indicates an expected call of BatchDeleteUserRole.
func (mr *MockAigcSoulmateServerMockRecorder) BatchDeleteUserRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteUserRole", reflect.TypeOf((*MockAigcSoulmateServer)(nil).BatchDeleteUserRole), ctx, in)
}

// BatchGetAIPartner mocks base method.
func (m *MockAigcSoulmateServer) BatchGetAIPartner(ctx context.Context, in *BatchGetAIPartnerReq) (*BatchGetAIPartnerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAIPartner", ctx, in)
	ret0, _ := ret[0].(*BatchGetAIPartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAIPartner indicates an expected call of BatchGetAIPartner.
func (mr *MockAigcSoulmateServerMockRecorder) BatchGetAIPartner(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAIPartner", reflect.TypeOf((*MockAigcSoulmateServer)(nil).BatchGetAIPartner), ctx, in)
}

// BatchGetAIRoleCategory mocks base method.
func (m *MockAigcSoulmateServer) BatchGetAIRoleCategory(ctx context.Context, in *BatchGetAIRoleCategoryReq) (*BatchGetAIRoleCategoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAIRoleCategory", ctx, in)
	ret0, _ := ret[0].(*BatchGetAIRoleCategoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAIRoleCategory indicates an expected call of BatchGetAIRoleCategory.
func (mr *MockAigcSoulmateServerMockRecorder) BatchGetAIRoleCategory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAIRoleCategory", reflect.TypeOf((*MockAigcSoulmateServer)(nil).BatchGetAIRoleCategory), ctx, in)
}

// BatchGetReadHeartInfo mocks base method.
func (m *MockAigcSoulmateServer) BatchGetReadHeartInfo(ctx context.Context, in *BatchGetReadHeartInfoRequest) (*BatchGetReadHeartInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetReadHeartInfo", ctx, in)
	ret0, _ := ret[0].(*BatchGetReadHeartInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetReadHeartInfo indicates an expected call of BatchGetReadHeartInfo.
func (mr *MockAigcSoulmateServerMockRecorder) BatchGetReadHeartInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetReadHeartInfo", reflect.TypeOf((*MockAigcSoulmateServer)(nil).BatchGetReadHeartInfo), ctx, in)
}

// BatchGetRoleUser mocks base method.
func (m *MockAigcSoulmateServer) BatchGetRoleUser(ctx context.Context, in *BatchGetRoleUserRequest) (*BatchGetRoleUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetRoleUser", ctx, in)
	ret0, _ := ret[0].(*BatchGetRoleUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetRoleUser indicates an expected call of BatchGetRoleUser.
func (mr *MockAigcSoulmateServerMockRecorder) BatchGetRoleUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetRoleUser", reflect.TypeOf((*MockAigcSoulmateServer)(nil).BatchGetRoleUser), ctx, in)
}

// BatchUpdateInteractiveGame mocks base method.
func (m *MockAigcSoulmateServer) BatchUpdateInteractiveGame(ctx context.Context, in *BatchUpdateInteractiveGameReq) (*BatchUpdateInteractiveGameResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateInteractiveGame", ctx, in)
	ret0, _ := ret[0].(*BatchUpdateInteractiveGameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdateInteractiveGame indicates an expected call of BatchUpdateInteractiveGame.
func (mr *MockAigcSoulmateServerMockRecorder) BatchUpdateInteractiveGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateInteractiveGame", reflect.TypeOf((*MockAigcSoulmateServer)(nil).BatchUpdateInteractiveGame), ctx, in)
}

// BatchUpdateRole mocks base method.
func (m *MockAigcSoulmateServer) BatchUpdateRole(ctx context.Context, in *BatchUpdateRoleReq) (*BatchUpdateRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateRole", ctx, in)
	ret0, _ := ret[0].(*BatchUpdateRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdateRole indicates an expected call of BatchUpdateRole.
func (mr *MockAigcSoulmateServerMockRecorder) BatchUpdateRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateRole", reflect.TypeOf((*MockAigcSoulmateServer)(nil).BatchUpdateRole), ctx, in)
}

// BatchUpdateUserRole mocks base method.
func (m *MockAigcSoulmateServer) BatchUpdateUserRole(ctx context.Context, in *BatchUpdateUserRoleReq) (*BatchUpdateUserRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateUserRole", ctx, in)
	ret0, _ := ret[0].(*BatchUpdateUserRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdateUserRole indicates an expected call of BatchUpdateUserRole.
func (mr *MockAigcSoulmateServerMockRecorder) BatchUpdateUserRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateUserRole", reflect.TypeOf((*MockAigcSoulmateServer)(nil).BatchUpdateUserRole), ctx, in)
}

// ChangeAIPartnerRole mocks base method.
func (m *MockAigcSoulmateServer) ChangeAIPartnerRole(ctx context.Context, in *ChangeAIPartnerRoleReq) (*ChangeAIPartnerRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeAIPartnerRole", ctx, in)
	ret0, _ := ret[0].(*ChangeAIPartnerRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangeAIPartnerRole indicates an expected call of ChangeAIPartnerRole.
func (mr *MockAigcSoulmateServerMockRecorder) ChangeAIPartnerRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeAIPartnerRole", reflect.TypeOf((*MockAigcSoulmateServer)(nil).ChangeAIPartnerRole), ctx, in)
}

// ClearAIMessage mocks base method.
func (m *MockAigcSoulmateServer) ClearAIMessage(ctx context.Context, in *ClearAIMessageReq) (*ClearAIMessageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearAIMessage", ctx, in)
	ret0, _ := ret[0].(*ClearAIMessageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearAIMessage indicates an expected call of ClearAIMessage.
func (mr *MockAigcSoulmateServerMockRecorder) ClearAIMessage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearAIMessage", reflect.TypeOf((*MockAigcSoulmateServer)(nil).ClearAIMessage), ctx, in)
}

// CreateAIChatTemplate mocks base method.
func (m *MockAigcSoulmateServer) CreateAIChatTemplate(ctx context.Context, in *CreateAIChatTemplateReq) (*CreateAIChatTemplateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAIChatTemplate", ctx, in)
	ret0, _ := ret[0].(*CreateAIChatTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAIChatTemplate indicates an expected call of CreateAIChatTemplate.
func (mr *MockAigcSoulmateServerMockRecorder) CreateAIChatTemplate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAIChatTemplate", reflect.TypeOf((*MockAigcSoulmateServer)(nil).CreateAIChatTemplate), ctx, in)
}

// CreateAIPartner mocks base method.
func (m *MockAigcSoulmateServer) CreateAIPartner(ctx context.Context, in *CreateAIPartnerReq) (*CreateAIPartnerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAIPartner", ctx, in)
	ret0, _ := ret[0].(*CreateAIPartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAIPartner indicates an expected call of CreateAIPartner.
func (mr *MockAigcSoulmateServerMockRecorder) CreateAIPartner(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAIPartner", reflect.TypeOf((*MockAigcSoulmateServer)(nil).CreateAIPartner), ctx, in)
}

// CreateAIRole mocks base method.
func (m *MockAigcSoulmateServer) CreateAIRole(ctx context.Context, in *CreateAIRoleReq) (*CreateAIRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAIRole", ctx, in)
	ret0, _ := ret[0].(*CreateAIRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAIRole indicates an expected call of CreateAIRole.
func (mr *MockAigcSoulmateServerMockRecorder) CreateAIRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAIRole", reflect.TypeOf((*MockAigcSoulmateServer)(nil).CreateAIRole), ctx, in)
}

// CreateInteractiveGame mocks base method.
func (m *MockAigcSoulmateServer) CreateInteractiveGame(ctx context.Context, in *CreateInteractiveGameReq) (*CreateInteractiveGameResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInteractiveGame", ctx, in)
	ret0, _ := ret[0].(*CreateInteractiveGameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInteractiveGame indicates an expected call of CreateInteractiveGame.
func (mr *MockAigcSoulmateServerMockRecorder) CreateInteractiveGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInteractiveGame", reflect.TypeOf((*MockAigcSoulmateServer)(nil).CreateInteractiveGame), ctx, in)
}

// DeleteAIPartner mocks base method.
func (m *MockAigcSoulmateServer) DeleteAIPartner(ctx context.Context, in *DeleteAIPartnerReq) (*DeleteAIPartnerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAIPartner", ctx, in)
	ret0, _ := ret[0].(*DeleteAIPartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAIPartner indicates an expected call of DeleteAIPartner.
func (mr *MockAigcSoulmateServerMockRecorder) DeleteAIPartner(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAIPartner", reflect.TypeOf((*MockAigcSoulmateServer)(nil).DeleteAIPartner), ctx, in)
}

// DeleteAIRole mocks base method.
func (m *MockAigcSoulmateServer) DeleteAIRole(ctx context.Context, in *DeleteAIRoleReq) (*DeleteAIRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAIRole", ctx, in)
	ret0, _ := ret[0].(*DeleteAIRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAIRole indicates an expected call of DeleteAIRole.
func (mr *MockAigcSoulmateServerMockRecorder) DeleteAIRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAIRole", reflect.TypeOf((*MockAigcSoulmateServer)(nil).DeleteAIRole), ctx, in)
}

// DeleteAIRoleCategory mocks base method.
func (m *MockAigcSoulmateServer) DeleteAIRoleCategory(ctx context.Context, in *DeleteAIRoleCategoryReq) (*DeleteAIRoleCategoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAIRoleCategory", ctx, in)
	ret0, _ := ret[0].(*DeleteAIRoleCategoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAIRoleCategory indicates an expected call of DeleteAIRoleCategory.
func (mr *MockAigcSoulmateServerMockRecorder) DeleteAIRoleCategory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAIRoleCategory", reflect.TypeOf((*MockAigcSoulmateServer)(nil).DeleteAIRoleCategory), ctx, in)
}

// GetAIChatTemplateList mocks base method.
func (m *MockAigcSoulmateServer) GetAIChatTemplateList(ctx context.Context, in *GetAIChatTemplateListReq) (*GetAIChatTemplateListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAIChatTemplateList", ctx, in)
	ret0, _ := ret[0].(*GetAIChatTemplateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIChatTemplateList indicates an expected call of GetAIChatTemplateList.
func (mr *MockAigcSoulmateServerMockRecorder) GetAIChatTemplateList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIChatTemplateList", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetAIChatTemplateList), ctx, in)
}

// GetAIPartner mocks base method.
func (m *MockAigcSoulmateServer) GetAIPartner(ctx context.Context, in *GetAIPartnerReq) (*GetAIPartnerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAIPartner", ctx, in)
	ret0, _ := ret[0].(*GetAIPartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIPartner indicates an expected call of GetAIPartner.
func (mr *MockAigcSoulmateServerMockRecorder) GetAIPartner(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIPartner", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetAIPartner), ctx, in)
}

// GetAIRole mocks base method.
func (m *MockAigcSoulmateServer) GetAIRole(ctx context.Context, in *GetAIRoleReq) (*GetAIRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAIRole", ctx, in)
	ret0, _ := ret[0].(*GetAIRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIRole indicates an expected call of GetAIRole.
func (mr *MockAigcSoulmateServerMockRecorder) GetAIRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIRole", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetAIRole), ctx, in)
}

// GetAIRoleList mocks base method.
func (m *MockAigcSoulmateServer) GetAIRoleList(ctx context.Context, in *GetAIRoleListReq) (*GetAIRoleListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAIRoleList", ctx, in)
	ret0, _ := ret[0].(*GetAIRoleListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIRoleList indicates an expected call of GetAIRoleList.
func (mr *MockAigcSoulmateServerMockRecorder) GetAIRoleList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIRoleList", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetAIRoleList), ctx, in)
}

// GetBannerRoleList mocks base method.
func (m *MockAigcSoulmateServer) GetBannerRoleList(ctx context.Context, in *GetBannerRoleListReq) (*GetBannerRoleListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBannerRoleList", ctx, in)
	ret0, _ := ret[0].(*GetBannerRoleListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBannerRoleList indicates an expected call of GetBannerRoleList.
func (mr *MockAigcSoulmateServerMockRecorder) GetBannerRoleList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBannerRoleList", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetBannerRoleList), ctx, in)
}

// GetBindChatTemplates mocks base method.
func (m *MockAigcSoulmateServer) GetBindChatTemplates(ctx context.Context, in *GetBindChatTemplatesReq) (*GetBindChatTemplatesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindChatTemplates", ctx, in)
	ret0, _ := ret[0].(*GetBindChatTemplatesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindChatTemplates indicates an expected call of GetBindChatTemplates.
func (mr *MockAigcSoulmateServerMockRecorder) GetBindChatTemplates(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindChatTemplates", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetBindChatTemplates), ctx, in)
}

// GetEntranceBanRoleId mocks base method.
func (m *MockAigcSoulmateServer) GetEntranceBanRoleId(ctx context.Context, in *GetEntranceBanRoleIdRequest) (*GetEntranceBanRoleIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntranceBanRoleId", ctx, in)
	ret0, _ := ret[0].(*GetEntranceBanRoleIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntranceBanRoleId indicates an expected call of GetEntranceBanRoleId.
func (mr *MockAigcSoulmateServerMockRecorder) GetEntranceBanRoleId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntranceBanRoleId", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetEntranceBanRoleId), ctx, in)
}

// GetInteractiveGame mocks base method.
func (m *MockAigcSoulmateServer) GetInteractiveGame(ctx context.Context, in *GetInteractiveGameReq) (*GetInteractiveGameResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInteractiveGame", ctx, in)
	ret0, _ := ret[0].(*GetInteractiveGameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInteractiveGame indicates an expected call of GetInteractiveGame.
func (mr *MockAigcSoulmateServerMockRecorder) GetInteractiveGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInteractiveGame", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetInteractiveGame), ctx, in)
}

// GetLatestUpdatedAIRoleList mocks base method.
func (m *MockAigcSoulmateServer) GetLatestUpdatedAIRoleList(ctx context.Context, in *GetLatestUpdatedAIRoleListReq) (*GetLatestUpdatedAIRoleListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestUpdatedAIRoleList", ctx, in)
	ret0, _ := ret[0].(*GetLatestUpdatedAIRoleListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestUpdatedAIRoleList indicates an expected call of GetLatestUpdatedAIRoleList.
func (mr *MockAigcSoulmateServerMockRecorder) GetLatestUpdatedAIRoleList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestUpdatedAIRoleList", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetLatestUpdatedAIRoleList), ctx, in)
}

// GetOfficialAIRoleList mocks base method.
func (m *MockAigcSoulmateServer) GetOfficialAIRoleList(ctx context.Context, in *GetOfficialAIRoleListReq) (*GetOfficialAIRoleListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOfficialAIRoleList", ctx, in)
	ret0, _ := ret[0].(*GetOfficialAIRoleListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOfficialAIRoleList indicates an expected call of GetOfficialAIRoleList.
func (mr *MockAigcSoulmateServerMockRecorder) GetOfficialAIRoleList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOfficialAIRoleList", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetOfficialAIRoleList), ctx, in)
}

// GetReadHeartEntrance mocks base method.
func (m *MockAigcSoulmateServer) GetReadHeartEntrance(ctx context.Context, in *GetReadHeartEntranceRequest) (*GetReadHeartEntranceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReadHeartEntrance", ctx, in)
	ret0, _ := ret[0].(*GetReadHeartEntranceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReadHeartEntrance indicates an expected call of GetReadHeartEntrance.
func (mr *MockAigcSoulmateServerMockRecorder) GetReadHeartEntrance(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReadHeartEntrance", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetReadHeartEntrance), ctx, in)
}

// GetRoleUidList mocks base method.
func (m *MockAigcSoulmateServer) GetRoleUidList(ctx context.Context, in *GetRoleUidListRequest) (*GetRoleUidListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoleUidList", ctx, in)
	ret0, _ := ret[0].(*GetRoleUidListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRoleUidList indicates an expected call of GetRoleUidList.
func (mr *MockAigcSoulmateServerMockRecorder) GetRoleUidList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoleUidList", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetRoleUidList), ctx, in)
}

// GetSharedRole mocks base method.
func (m *MockAigcSoulmateServer) GetSharedRole(ctx context.Context, in *GetSharedRoleReq) (*GetSharedRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSharedRole", ctx, in)
	ret0, _ := ret[0].(*GetSharedRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSharedRole indicates an expected call of GetSharedRole.
func (mr *MockAigcSoulmateServerMockRecorder) GetSharedRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSharedRole", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetSharedRole), ctx, in)
}

// GetUserAIPartner mocks base method.
func (m *MockAigcSoulmateServer) GetUserAIPartner(ctx context.Context, in *GetUserAIPartnerReq) (*GetUserAIPartnerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAIPartner", ctx, in)
	ret0, _ := ret[0].(*GetUserAIPartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAIPartner indicates an expected call of GetUserAIPartner.
func (mr *MockAigcSoulmateServerMockRecorder) GetUserAIPartner(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAIPartner", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetUserAIPartner), ctx, in)
}

// GetUserAIPartnerList mocks base method.
func (m *MockAigcSoulmateServer) GetUserAIPartnerList(ctx context.Context, in *GetUserAIPartnerListReq) (*GetUserAIPartnerListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAIPartnerList", ctx, in)
	ret0, _ := ret[0].(*GetUserAIPartnerListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAIPartnerList indicates an expected call of GetUserAIPartnerList.
func (mr *MockAigcSoulmateServerMockRecorder) GetUserAIPartnerList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAIPartnerList", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetUserAIPartnerList), ctx, in)
}

// GetUserAIRoleLikes mocks base method.
func (m *MockAigcSoulmateServer) GetUserAIRoleLikes(ctx context.Context, in *GetUserAIRoleLikesReq) (*GetUserAIRoleLikesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAIRoleLikes", ctx, in)
	ret0, _ := ret[0].(*GetUserAIRoleLikesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAIRoleLikes indicates an expected call of GetUserAIRoleLikes.
func (mr *MockAigcSoulmateServerMockRecorder) GetUserAIRoleLikes(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAIRoleLikes", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetUserAIRoleLikes), ctx, in)
}

// GetUserAIRoleList mocks base method.
func (m *MockAigcSoulmateServer) GetUserAIRoleList(ctx context.Context, in *GetUserAIRoleListReq) (*GetUserAIRoleListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAIRoleList", ctx, in)
	ret0, _ := ret[0].(*GetUserAIRoleListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAIRoleList indicates an expected call of GetUserAIRoleList.
func (mr *MockAigcSoulmateServerMockRecorder) GetUserAIRoleList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAIRoleList", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetUserAIRoleList), ctx, in)
}

// GetUserAIRoleListWithAppoint mocks base method.
func (m *MockAigcSoulmateServer) GetUserAIRoleListWithAppoint(ctx context.Context, in *GetUserAIRoleListWithAppointReq) (*GetUserAIRoleListWithAppointResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAIRoleListWithAppoint", ctx, in)
	ret0, _ := ret[0].(*GetUserAIRoleListWithAppointResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAIRoleListWithAppoint indicates an expected call of GetUserAIRoleListWithAppoint.
func (mr *MockAigcSoulmateServerMockRecorder) GetUserAIRoleListWithAppoint(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAIRoleListWithAppoint", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetUserAIRoleListWithAppoint), ctx, in)
}

// GetUserExclusiveRoleList mocks base method.
func (m *MockAigcSoulmateServer) GetUserExclusiveRoleList(ctx context.Context, in *GetUserExclusiveRoleListRequest) (*GetUserExclusiveRoleListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserExclusiveRoleList", ctx, in)
	ret0, _ := ret[0].(*GetUserExclusiveRoleListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExclusiveRoleList indicates an expected call of GetUserExclusiveRoleList.
func (mr *MockAigcSoulmateServerMockRecorder) GetUserExclusiveRoleList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExclusiveRoleList", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetUserExclusiveRoleList), ctx, in)
}

// GetUserInteractiveGameList mocks base method.
func (m *MockAigcSoulmateServer) GetUserInteractiveGameList(ctx context.Context, in *GetUserInteractiveGameListReq) (*GetUserInteractiveGameListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInteractiveGameList", ctx, in)
	ret0, _ := ret[0].(*GetUserInteractiveGameListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInteractiveGameList indicates an expected call of GetUserInteractiveGameList.
func (mr *MockAigcSoulmateServerMockRecorder) GetUserInteractiveGameList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInteractiveGameList", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetUserInteractiveGameList), ctx, in)
}

// GetUserReadHeartCnt mocks base method.
func (m *MockAigcSoulmateServer) GetUserReadHeartCnt(ctx context.Context, in *GetUserReadHeartCntRequest) (*GetUserReadHeartCntResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserReadHeartCnt", ctx, in)
	ret0, _ := ret[0].(*GetUserReadHeartCntResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserReadHeartCnt indicates an expected call of GetUserReadHeartCnt.
func (mr *MockAigcSoulmateServerMockRecorder) GetUserReadHeartCnt(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserReadHeartCnt", reflect.TypeOf((*MockAigcSoulmateServer)(nil).GetUserReadHeartCnt), ctx, in)
}

// LikeAIRole mocks base method.
func (m *MockAigcSoulmateServer) LikeAIRole(ctx context.Context, in *LikeAIRoleReq) (*LikeAIRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LikeAIRole", ctx, in)
	ret0, _ := ret[0].(*LikeAIRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LikeAIRole indicates an expected call of LikeAIRole.
func (mr *MockAigcSoulmateServerMockRecorder) LikeAIRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LikeAIRole", reflect.TypeOf((*MockAigcSoulmateServer)(nil).LikeAIRole), ctx, in)
}

// PullAIMessage mocks base method.
func (m *MockAigcSoulmateServer) PullAIMessage(ctx context.Context, in *PullAIMessageReq) (*PullAIMessageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PullAIMessage", ctx, in)
	ret0, _ := ret[0].(*PullAIMessageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PullAIMessage indicates an expected call of PullAIMessage.
func (mr *MockAigcSoulmateServerMockRecorder) PullAIMessage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PullAIMessage", reflect.TypeOf((*MockAigcSoulmateServer)(nil).PullAIMessage), ctx, in)
}

// ResortAIRoleCategory mocks base method.
func (m *MockAigcSoulmateServer) ResortAIRoleCategory(ctx context.Context, in *ResortAIRoleCategoryReq) (*ResortAIRoleCategoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResortAIRoleCategory", ctx, in)
	ret0, _ := ret[0].(*ResortAIRoleCategoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResortAIRoleCategory indicates an expected call of ResortAIRoleCategory.
func (mr *MockAigcSoulmateServerMockRecorder) ResortAIRoleCategory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResortAIRoleCategory", reflect.TypeOf((*MockAigcSoulmateServer)(nil).ResortAIRoleCategory), ctx, in)
}

// SearchAIRole mocks base method.
func (m *MockAigcSoulmateServer) SearchAIRole(ctx context.Context, in *SearchAIRoleReq) (*SearchAIRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchAIRole", ctx, in)
	ret0, _ := ret[0].(*SearchAIRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAIRole indicates an expected call of SearchAIRole.
func (mr *MockAigcSoulmateServerMockRecorder) SearchAIRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAIRole", reflect.TypeOf((*MockAigcSoulmateServer)(nil).SearchAIRole), ctx, in)
}

// SearchInteractiveGame mocks base method.
func (m *MockAigcSoulmateServer) SearchInteractiveGame(ctx context.Context, in *SearchInteractiveGameReq) (*SearchInteractiveGameResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchInteractiveGame", ctx, in)
	ret0, _ := ret[0].(*SearchInteractiveGameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchInteractiveGame indicates an expected call of SearchInteractiveGame.
func (mr *MockAigcSoulmateServerMockRecorder) SearchInteractiveGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchInteractiveGame", reflect.TypeOf((*MockAigcSoulmateServer)(nil).SearchInteractiveGame), ctx, in)
}

// SearchUserRole mocks base method.
func (m *MockAigcSoulmateServer) SearchUserRole(ctx context.Context, in *SearchUserRoleReq) (*SearchUserRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchUserRole", ctx, in)
	ret0, _ := ret[0].(*SearchUserRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchUserRole indicates an expected call of SearchUserRole.
func (mr *MockAigcSoulmateServerMockRecorder) SearchUserRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchUserRole", reflect.TypeOf((*MockAigcSoulmateServer)(nil).SearchUserRole), ctx, in)
}

// SetBannerRoleList mocks base method.
func (m *MockAigcSoulmateServer) SetBannerRoleList(ctx context.Context, in *SetBannerRoleListReq) (*SetBannerRoleListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBannerRoleList", ctx, in)
	ret0, _ := ret[0].(*SetBannerRoleListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetBannerRoleList indicates an expected call of SetBannerRoleList.
func (mr *MockAigcSoulmateServerMockRecorder) SetBannerRoleList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBannerRoleList", reflect.TypeOf((*MockAigcSoulmateServer)(nil).SetBannerRoleList), ctx, in)
}

// ShareRole mocks base method.
func (m *MockAigcSoulmateServer) ShareRole(ctx context.Context, in *ShareRoleReq) (*ShareRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShareRole", ctx, in)
	ret0, _ := ret[0].(*ShareRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShareRole indicates an expected call of ShareRole.
func (mr *MockAigcSoulmateServerMockRecorder) ShareRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShareRole", reflect.TypeOf((*MockAigcSoulmateServer)(nil).ShareRole), ctx, in)
}

// TryCreateAIPartner mocks base method.
func (m *MockAigcSoulmateServer) TryCreateAIPartner(ctx context.Context, in *CreateAIPartnerReq) (*CreateAIPartnerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TryCreateAIPartner", ctx, in)
	ret0, _ := ret[0].(*CreateAIPartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TryCreateAIPartner indicates an expected call of TryCreateAIPartner.
func (mr *MockAigcSoulmateServerMockRecorder) TryCreateAIPartner(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TryCreateAIPartner", reflect.TypeOf((*MockAigcSoulmateServer)(nil).TryCreateAIPartner), ctx, in)
}

// UnlikeAIRole mocks base method.
func (m *MockAigcSoulmateServer) UnlikeAIRole(ctx context.Context, in *UnlikeAIRoleReq) (*UnlikeAIRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnlikeAIRole", ctx, in)
	ret0, _ := ret[0].(*UnlikeAIRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnlikeAIRole indicates an expected call of UnlikeAIRole.
func (mr *MockAigcSoulmateServerMockRecorder) UnlikeAIRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnlikeAIRole", reflect.TypeOf((*MockAigcSoulmateServer)(nil).UnlikeAIRole), ctx, in)
}

// UpdateAIChatTemplate mocks base method.
func (m *MockAigcSoulmateServer) UpdateAIChatTemplate(ctx context.Context, in *UpdateAIChatTemplateReq) (*UpdateAIChatTemplateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAIChatTemplate", ctx, in)
	ret0, _ := ret[0].(*UpdateAIChatTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIChatTemplate indicates an expected call of UpdateAIChatTemplate.
func (mr *MockAigcSoulmateServerMockRecorder) UpdateAIChatTemplate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIChatTemplate", reflect.TypeOf((*MockAigcSoulmateServer)(nil).UpdateAIChatTemplate), ctx, in)
}

// UpdateAIPartner mocks base method.
func (m *MockAigcSoulmateServer) UpdateAIPartner(ctx context.Context, in *UpdateAIPartnerReq) (*UpdateAIPartnerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAIPartner", ctx, in)
	ret0, _ := ret[0].(*UpdateAIPartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIPartner indicates an expected call of UpdateAIPartner.
func (mr *MockAigcSoulmateServerMockRecorder) UpdateAIPartner(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIPartner", reflect.TypeOf((*MockAigcSoulmateServer)(nil).UpdateAIPartner), ctx, in)
}

// UpdateAIPartnerChatState mocks base method.
func (m *MockAigcSoulmateServer) UpdateAIPartnerChatState(ctx context.Context, in *UpdateAIPartnerChatStateReq) (*UpdateAIPartnerChatStateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAIPartnerChatState", ctx, in)
	ret0, _ := ret[0].(*UpdateAIPartnerChatStateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIPartnerChatState indicates an expected call of UpdateAIPartnerChatState.
func (mr *MockAigcSoulmateServerMockRecorder) UpdateAIPartnerChatState(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIPartnerChatState", reflect.TypeOf((*MockAigcSoulmateServer)(nil).UpdateAIPartnerChatState), ctx, in)
}

// UpdateAIRole mocks base method.
func (m *MockAigcSoulmateServer) UpdateAIRole(ctx context.Context, in *UpdateAIRoleReq) (*UpdateAIRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAIRole", ctx, in)
	ret0, _ := ret[0].(*UpdateAIRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIRole indicates an expected call of UpdateAIRole.
func (mr *MockAigcSoulmateServerMockRecorder) UpdateAIRole(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIRole", reflect.TypeOf((*MockAigcSoulmateServer)(nil).UpdateAIRole), ctx, in)
}

// UpdateAIRoleAuditResult mocks base method.
func (m *MockAigcSoulmateServer) UpdateAIRoleAuditResult(ctx context.Context, in *UpdateAIRoleAuditResultReq) (*UpdateAIRoleAuditResultResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAIRoleAuditResult", ctx, in)
	ret0, _ := ret[0].(*UpdateAIRoleAuditResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIRoleAuditResult indicates an expected call of UpdateAIRoleAuditResult.
func (mr *MockAigcSoulmateServerMockRecorder) UpdateAIRoleAuditResult(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIRoleAuditResult", reflect.TypeOf((*MockAigcSoulmateServer)(nil).UpdateAIRoleAuditResult), ctx, in)
}

// UpdateEntranceBanRoleId mocks base method.
func (m *MockAigcSoulmateServer) UpdateEntranceBanRoleId(ctx context.Context, in *UpdateEntranceBanRoleIdRequest) (*UpdateEntranceBanRoleIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEntranceBanRoleId", ctx, in)
	ret0, _ := ret[0].(*UpdateEntranceBanRoleIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateEntranceBanRoleId indicates an expected call of UpdateEntranceBanRoleId.
func (mr *MockAigcSoulmateServerMockRecorder) UpdateEntranceBanRoleId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEntranceBanRoleId", reflect.TypeOf((*MockAigcSoulmateServer)(nil).UpdateEntranceBanRoleId), ctx, in)
}

// UpdateInteractiveGame mocks base method.
func (m *MockAigcSoulmateServer) UpdateInteractiveGame(ctx context.Context, in *UpdateInteractiveGameReq) (*UpdateInteractiveGameResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInteractiveGame", ctx, in)
	ret0, _ := ret[0].(*UpdateInteractiveGameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInteractiveGame indicates an expected call of UpdateInteractiveGame.
func (mr *MockAigcSoulmateServerMockRecorder) UpdateInteractiveGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInteractiveGame", reflect.TypeOf((*MockAigcSoulmateServer)(nil).UpdateInteractiveGame), ctx, in)
}

// UpsertAIRoleCategory mocks base method.
func (m *MockAigcSoulmateServer) UpsertAIRoleCategory(ctx context.Context, in *UpsertAIRoleCategoryReq) (*UpsertAIRoleCategoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertAIRoleCategory", ctx, in)
	ret0, _ := ret[0].(*UpsertAIRoleCategoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertAIRoleCategory indicates an expected call of UpsertAIRoleCategory.
func (mr *MockAigcSoulmateServerMockRecorder) UpsertAIRoleCategory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertAIRoleCategory", reflect.TypeOf((*MockAigcSoulmateServer)(nil).UpsertAIRoleCategory), ctx, in)
}

// VerifyShareIdentifier mocks base method.
func (m *MockAigcSoulmateServer) VerifyShareIdentifier(ctx context.Context, in *VerifyShareIdentifierReq) (*VerifyShareIdentifierResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyShareIdentifier", ctx, in)
	ret0, _ := ret[0].(*VerifyShareIdentifierResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyShareIdentifier indicates an expected call of VerifyShareIdentifier.
func (mr *MockAigcSoulmateServerMockRecorder) VerifyShareIdentifier(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyShareIdentifier", reflect.TypeOf((*MockAigcSoulmateServer)(nil).VerifyShareIdentifier), ctx, in)
}

// WriteAIMessage mocks base method.
func (m *MockAigcSoulmateServer) WriteAIMessage(ctx context.Context, in *WriteAIMessageReq) (*WriteAIMessageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WriteAIMessage", ctx, in)
	ret0, _ := ret[0].(*WriteAIMessageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// WriteAIMessage indicates an expected call of WriteAIMessage.
func (mr *MockAigcSoulmateServerMockRecorder) WriteAIMessage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteAIMessage", reflect.TypeOf((*MockAigcSoulmateServer)(nil).WriteAIMessage), ctx, in)
}
