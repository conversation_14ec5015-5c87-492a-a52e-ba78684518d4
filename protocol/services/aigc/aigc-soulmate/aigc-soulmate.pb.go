// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-soulmate.proto

package aigc_soulmate // import "golang.52tt.com/protocol/services/aigc/aigc-soulmate"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 审核结果
type AuditResult int32

const (
	// 机审无法识别
	AuditResult_AuditResultReview AuditResult = 0
	// 通过
	AuditResult_AuditResultPass AuditResult = 1
	// 不通过
	AuditResult_AuditResultReject AuditResult = 2
)

var AuditResult_name = map[int32]string{
	0: "AuditResultReview",
	1: "AuditResultPass",
	2: "AuditResultReject",
}
var AuditResult_value = map[string]int32{
	"AuditResultReview": 0,
	"AuditResultPass":   1,
	"AuditResultReject": 2,
}

func (x AuditResult) String() string {
	return proto.EnumName(AuditResult_name, int32(x))
}
func (AuditResult) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{0}
}

// AI角色类型
type AIRoleType int32

const (
	// 树洞
	AIRoleType_AIRoleTypePartner AIRoleType = 0
	// 游戏/角色扮演
	AIRoleType_AIRoleTypeGame AIRoleType = 1
	// 桌宠
	AIRoleType_AIRoleTypePet AIRoleType = 2
	// 群聊角色
	AIRoleType_AIRoleTypeGroup AIRoleType = 3
)

var AIRoleType_name = map[int32]string{
	0: "AIRoleTypePartner",
	1: "AIRoleTypeGame",
	2: "AIRoleTypePet",
	3: "AIRoleTypeGroup",
}
var AIRoleType_value = map[string]int32{
	"AIRoleTypePartner": 0,
	"AIRoleTypeGame":    1,
	"AIRoleTypePet":     2,
	"AIRoleTypeGroup":   3,
}

func (x AIRoleType) String() string {
	return proto.EnumName(AIRoleType_name, int32(x))
}
func (AIRoleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{1}
}

// AI角色状态
type AIRoleState int32

const (
	AIRoleState_AIRoleStateNone AIRoleState = 0
	// 公开
	AIRoleState_AIRoleStatePublic AIRoleState = 1
	// 私有
	AIRoleState_AIRoleStatePrivate AIRoleState = 2
)

var AIRoleState_name = map[int32]string{
	0: "AIRoleStateNone",
	1: "AIRoleStatePublic",
	2: "AIRoleStatePrivate",
}
var AIRoleState_value = map[string]int32{
	"AIRoleStateNone":    0,
	"AIRoleStatePublic":  1,
	"AIRoleStatePrivate": 2,
}

func (x AIRoleState) String() string {
	return proto.EnumName(AIRoleState_name, int32(x))
}
func (AIRoleState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{2}
}

type AIPartnerSource int32

const (
	// 用户创建
	AIPartnerSource_AIPartnerSourceUser AIPartnerSource = 0
	// 去形象化创建
	AIPartnerSource_AIPartnerSourceDeRole AIPartnerSource = 1
	// 游戏形象
	AIPartnerSource_AIPartnerSourceGame AIPartnerSource = 2
	// 多角色
	AIPartnerSource_AIPartnerSourceMultiRole AIPartnerSource = 3
	// AIGC主动触发
	AIPartnerSource_AIPartnerSourceAIGCTrigger AIPartnerSource = 4
	// 桌宠
	AIPartnerSource_AIPartnerSourcePet AIPartnerSource = 5
)

var AIPartnerSource_name = map[int32]string{
	0: "AIPartnerSourceUser",
	1: "AIPartnerSourceDeRole",
	2: "AIPartnerSourceGame",
	3: "AIPartnerSourceMultiRole",
	4: "AIPartnerSourceAIGCTrigger",
	5: "AIPartnerSourcePet",
}
var AIPartnerSource_value = map[string]int32{
	"AIPartnerSourceUser":        0,
	"AIPartnerSourceDeRole":      1,
	"AIPartnerSourceGame":        2,
	"AIPartnerSourceMultiRole":   3,
	"AIPartnerSourceAIGCTrigger": 4,
	"AIPartnerSourcePet":         5,
}

func (x AIPartnerSource) String() string {
	return proto.EnumName(AIPartnerSource_name, int32(x))
}
func (AIPartnerSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{3}
}

// 角色来源
type AIRoleSource int32

const (
	AIRoleSource_AIRoleSourceUnknown AIRoleSource = 0
	// 官方
	AIRoleSource_AIRoleSourceOfficial AIRoleSource = 1
	// 用户
	AIRoleSource_AIRoleSourceUser AIRoleSource = 2
)

var AIRoleSource_name = map[int32]string{
	0: "AIRoleSourceUnknown",
	1: "AIRoleSourceOfficial",
	2: "AIRoleSourceUser",
}
var AIRoleSource_value = map[string]int32{
	"AIRoleSourceUnknown":  0,
	"AIRoleSourceOfficial": 1,
	"AIRoleSourceUser":     2,
}

func (x AIRoleSource) String() string {
	return proto.EnumName(AIRoleSource_name, int32(x))
}
func (AIRoleSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{4}
}

type GetCategorySource int32

const (
	GetCategorySource_GET_CATEGORY_SOURCE_UNKNOWN   GetCategorySource = 0
	GetCategorySource_GET_CATEGORY_SOURCE_BACK      GetCategorySource = 1
	GetCategorySource_GET_CATEGORY_SOURCE_H5_HOME   GetCategorySource = 2
	GetCategorySource_GET_CATEGORY_SOURCE_H5_CREATE GetCategorySource = 3
)

var GetCategorySource_name = map[int32]string{
	0: "GET_CATEGORY_SOURCE_UNKNOWN",
	1: "GET_CATEGORY_SOURCE_BACK",
	2: "GET_CATEGORY_SOURCE_H5_HOME",
	3: "GET_CATEGORY_SOURCE_H5_CREATE",
}
var GetCategorySource_value = map[string]int32{
	"GET_CATEGORY_SOURCE_UNKNOWN":   0,
	"GET_CATEGORY_SOURCE_BACK":      1,
	"GET_CATEGORY_SOURCE_H5_HOME":   2,
	"GET_CATEGORY_SOURCE_H5_CREATE": 3,
}

func (x GetCategorySource) String() string {
	return proto.EnumName(GetCategorySource_name, int32(x))
}
func (GetCategorySource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{5}
}

type InteractiveGameState int32

const (
	InteractiveGameState_InteractiveGameStateUnspecified InteractiveGameState = 0
	// 公开
	InteractiveGameState_InteractiveGameStatePublic InteractiveGameState = 1
	// 私有
	InteractiveGameState_InteractiveGameStatePrivate InteractiveGameState = 2
)

var InteractiveGameState_name = map[int32]string{
	0: "InteractiveGameStateUnspecified",
	1: "InteractiveGameStatePublic",
	2: "InteractiveGameStatePrivate",
}
var InteractiveGameState_value = map[string]int32{
	"InteractiveGameStateUnspecified": 0,
	"InteractiveGameStatePublic":      1,
	"InteractiveGameStatePrivate":     2,
}

func (x InteractiveGameState) String() string {
	return proto.EnumName(InteractiveGameState_name, int32(x))
}
func (InteractiveGameState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{6}
}

// 互动玩法来源
type InteractiveGameSource int32

const (
	InteractiveGameSource_InteractiveGameSourceUnspecified InteractiveGameSource = 0
	// 官方
	InteractiveGameSource_InteractiveGameSourceOfficial InteractiveGameSource = 1
	// 用户
	InteractiveGameSource_InteractiveGameSourceUser InteractiveGameSource = 2
)

var InteractiveGameSource_name = map[int32]string{
	0: "InteractiveGameSourceUnspecified",
	1: "InteractiveGameSourceOfficial",
	2: "InteractiveGameSourceUser",
}
var InteractiveGameSource_value = map[string]int32{
	"InteractiveGameSourceUnspecified": 0,
	"InteractiveGameSourceOfficial":    1,
	"InteractiveGameSourceUser":        2,
}

func (x InteractiveGameSource) String() string {
	return proto.EnumName(InteractiveGameSource_name, int32(x))
}
func (InteractiveGameSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{7}
}

// 角色故事模式
type AIRoleStoryMode int32

const (
	// 默认
	AIRoleStoryMode_AIRoleStoryModeDefault AIRoleStoryMode = 0
	// 纯故事
	AIRoleStoryMode_AIRoleStoryModeOnlyStory AIRoleStoryMode = 1
)

var AIRoleStoryMode_name = map[int32]string{
	0: "AIRoleStoryModeDefault",
	1: "AIRoleStoryModeOnlyStory",
}
var AIRoleStoryMode_value = map[string]int32{
	"AIRoleStoryModeDefault":   0,
	"AIRoleStoryModeOnlyStory": 1,
}

func (x AIRoleStoryMode) String() string {
	return proto.EnumName(AIRoleStoryMode_name, int32(x))
}
func (AIRoleStoryMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{8}
}

// 互动游戏外显
type InteractiveGameExposure int32

const (
	InteractiveGameExposure_InteractiveGameExposureUnspecified InteractiveGameExposure = 0
	// 外显
	InteractiveGameExposure_InteractiveGameExposureExposed InteractiveGameExposure = 1
	// 隐藏
	InteractiveGameExposure_InteractiveGameExposureUnexposed InteractiveGameExposure = 2
)

var InteractiveGameExposure_name = map[int32]string{
	0: "InteractiveGameExposureUnspecified",
	1: "InteractiveGameExposureExposed",
	2: "InteractiveGameExposureUnexposed",
}
var InteractiveGameExposure_value = map[string]int32{
	"InteractiveGameExposureUnspecified": 0,
	"InteractiveGameExposureExposed":     1,
	"InteractiveGameExposureUnexposed":   2,
}

func (x InteractiveGameExposure) String() string {
	return proto.EnumName(InteractiveGameExposure_name, int32(x))
}
func (InteractiveGameExposure) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{9}
}

// 角色使用范围
type AIRoleScope int32

const (
	AIRoleScope_AI_ROLE_SCOPE_UNSPECIFIED AIRoleScope = 0
	// 用户专属
	AIRoleScope_AI_ROLE_SCOPE_USER_EXCLUSIVE AIRoleScope = 1
)

var AIRoleScope_name = map[int32]string{
	0: "AI_ROLE_SCOPE_UNSPECIFIED",
	1: "AI_ROLE_SCOPE_USER_EXCLUSIVE",
}
var AIRoleScope_value = map[string]int32{
	"AI_ROLE_SCOPE_UNSPECIFIED":    0,
	"AI_ROLE_SCOPE_USER_EXCLUSIVE": 1,
}

func (x AIRoleScope) String() string {
	return proto.EnumName(AIRoleScope_name, int32(x))
}
func (AIRoleScope) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{10}
}

// 创作者信息展示类型
type CreatorInfoType int32

const (
	CreatorInfoType_CREATOR_INFO_TYPE_ANONYMOUS CreatorInfoType = 0
	CreatorInfoType_CREATOR_INFO_TYPE_PUBLIC    CreatorInfoType = 1
	CreatorInfoType_CREATOR_INFO_TYPE_HIDE      CreatorInfoType = 2
)

var CreatorInfoType_name = map[int32]string{
	0: "CREATOR_INFO_TYPE_ANONYMOUS",
	1: "CREATOR_INFO_TYPE_PUBLIC",
	2: "CREATOR_INFO_TYPE_HIDE",
}
var CreatorInfoType_value = map[string]int32{
	"CREATOR_INFO_TYPE_ANONYMOUS": 0,
	"CREATOR_INFO_TYPE_PUBLIC":    1,
	"CREATOR_INFO_TYPE_HIDE":      2,
}

func (x CreatorInfoType) String() string {
	return proto.EnumName(CreatorInfoType_name, int32(x))
}
func (CreatorInfoType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{11}
}

type SearchRoleSexEnum int32

const (
	SearchRoleSexEnum_SEARCH_ROLE_SEX_NONE   SearchRoleSexEnum = 0
	SearchRoleSexEnum_SEARCH_ROLE_SEX_MALE   SearchRoleSexEnum = 1
	SearchRoleSexEnum_SEARCH_ROLE_SEX_FEMALE SearchRoleSexEnum = 2
	SearchRoleSexEnum_SEARCH_ROLE_SEX_OTHER  SearchRoleSexEnum = 3
)

var SearchRoleSexEnum_name = map[int32]string{
	0: "SEARCH_ROLE_SEX_NONE",
	1: "SEARCH_ROLE_SEX_MALE",
	2: "SEARCH_ROLE_SEX_FEMALE",
	3: "SEARCH_ROLE_SEX_OTHER",
}
var SearchRoleSexEnum_value = map[string]int32{
	"SEARCH_ROLE_SEX_NONE":   0,
	"SEARCH_ROLE_SEX_MALE":   1,
	"SEARCH_ROLE_SEX_FEMALE": 2,
	"SEARCH_ROLE_SEX_OTHER":  3,
}

func (x SearchRoleSexEnum) String() string {
	return proto.EnumName(SearchRoleSexEnum_name, int32(x))
}
func (SearchRoleSexEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{12}
}

type SearchType int32

const (
	SearchType_SEARCH_TYPE_NONE  SearchType = 0
	SearchType_SEARCH_TYPE_MONGO SearchType = 1
	SearchType_SEARCH_TYPE_ES    SearchType = 2
)

var SearchType_name = map[int32]string{
	0: "SEARCH_TYPE_NONE",
	1: "SEARCH_TYPE_MONGO",
	2: "SEARCH_TYPE_ES",
}
var SearchType_value = map[string]int32{
	"SEARCH_TYPE_NONE":  0,
	"SEARCH_TYPE_MONGO": 1,
	"SEARCH_TYPE_ES":    2,
}

func (x SearchType) String() string {
	return proto.EnumName(SearchType_name, int32(x))
}
func (SearchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{13}
}

type FilterExpose int32

const (
	FilterExpose_FILTER_EXPOSE_NONE  FilterExpose = 0
	FilterExpose_FILTER_EXPOSE_TRUE  FilterExpose = 1
	FilterExpose_FILTER_EXPOSE_FALSE FilterExpose = 2
)

var FilterExpose_name = map[int32]string{
	0: "FILTER_EXPOSE_NONE",
	1: "FILTER_EXPOSE_TRUE",
	2: "FILTER_EXPOSE_FALSE",
}
var FilterExpose_value = map[string]int32{
	"FILTER_EXPOSE_NONE":  0,
	"FILTER_EXPOSE_TRUE":  1,
	"FILTER_EXPOSE_FALSE": 2,
}

func (x FilterExpose) String() string {
	return proto.EnumName(FilterExpose_name, int32(x))
}
func (FilterExpose) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{14}
}

// 扣读心次数类型
type DeductionHeartCntType int32

const (
	DeductionHeartCntType_DEDUCTION_HEART_CNT_TYPE_UNSPECIFIED DeductionHeartCntType = 0
	// 通用扣次数
	DeductionHeartCntType_DEDUCTION_HEART_CNT_TYPE_COMMON DeductionHeartCntType = 1
	// 角色扣次数
	DeductionHeartCntType_DEDUCTION_HEART_CNT_TYPE_ROLE DeductionHeartCntType = 2
)

var DeductionHeartCntType_name = map[int32]string{
	0: "DEDUCTION_HEART_CNT_TYPE_UNSPECIFIED",
	1: "DEDUCTION_HEART_CNT_TYPE_COMMON",
	2: "DEDUCTION_HEART_CNT_TYPE_ROLE",
}
var DeductionHeartCntType_value = map[string]int32{
	"DEDUCTION_HEART_CNT_TYPE_UNSPECIFIED": 0,
	"DEDUCTION_HEART_CNT_TYPE_COMMON":      1,
	"DEDUCTION_HEART_CNT_TYPE_ROLE":        2,
}

func (x DeductionHeartCntType) String() string {
	return proto.EnumName(DeductionHeartCntType_name, int32(x))
}
func (DeductionHeartCntType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{15}
}

// 控制属性、标签展示在哪些场景
type AIRoleCategory_Scene int32

const (
	AIRoleCategory_SceneUnspecified AIRoleCategory_Scene = 0
	// 快筛
	AIRoleCategory_SceneHead AIRoleCategory_Scene = 1
	// 弹窗面板
	AIRoleCategory_ScenePanel AIRoleCategory_Scene = 2
)

var AIRoleCategory_Scene_name = map[int32]string{
	0: "SceneUnspecified",
	1: "SceneHead",
	2: "ScenePanel",
}
var AIRoleCategory_Scene_value = map[string]int32{
	"SceneUnspecified": 0,
	"SceneHead":        1,
	"ScenePanel":       2,
}

func (x AIRoleCategory_Scene) String() string {
	return proto.EnumName(AIRoleCategory_Scene_name, int32(x))
}
func (AIRoleCategory_Scene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{0, 0}
}

// 属性类型
type AIRoleCategory_PropType int32

const (
	AIRoleCategory_PropTypeUnspecified AIRoleCategory_PropType = 0
	// 性别
	AIRoleCategory_PropTypeGender AIRoleCategory_PropType = 1
)

var AIRoleCategory_PropType_name = map[int32]string{
	0: "PropTypeUnspecified",
	1: "PropTypeGender",
}
var AIRoleCategory_PropType_value = map[string]int32{
	"PropTypeUnspecified": 0,
	"PropTypeGender":      1,
}

func (x AIRoleCategory_PropType) String() string {
	return proto.EnumName(AIRoleCategory_PropType_name, int32(x))
}
func (AIRoleCategory_PropType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{0, 1}
}

type AIRole_LikeState int32

const (
	AIRole_LikeStateUnspecified AIRole_LikeState = 0
	AIRole_LikeStateLiked       AIRole_LikeState = 1
	AIRole_LikeStateUnliked     AIRole_LikeState = 2
)

var AIRole_LikeState_name = map[int32]string{
	0: "LikeStateUnspecified",
	1: "LikeStateLiked",
	2: "LikeStateUnliked",
}
var AIRole_LikeState_value = map[string]int32{
	"LikeStateUnspecified": 0,
	"LikeStateLiked":       1,
	"LikeStateUnliked":     2,
}

func (x AIRole_LikeState) String() string {
	return proto.EnumName(AIRole_LikeState_name, int32(x))
}
func (AIRole_LikeState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{3, 0}
}

type AIRoleLikeInfo_LikeState int32

const (
	AIRoleLikeInfo_LikeStateUnspecified AIRoleLikeInfo_LikeState = 0
	AIRoleLikeInfo_LikeStateLiked       AIRoleLikeInfo_LikeState = 1
	AIRoleLikeInfo_LikeStateUnliked     AIRoleLikeInfo_LikeState = 2
)

var AIRoleLikeInfo_LikeState_name = map[int32]string{
	0: "LikeStateUnspecified",
	1: "LikeStateLiked",
	2: "LikeStateUnliked",
}
var AIRoleLikeInfo_LikeState_value = map[string]int32{
	"LikeStateUnspecified": 0,
	"LikeStateLiked":       1,
	"LikeStateUnliked":     2,
}

func (x AIRoleLikeInfo_LikeState) String() string {
	return proto.EnumName(AIRoleLikeInfo_LikeState_name, int32(x))
}
func (AIRoleLikeInfo_LikeState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{6, 0}
}

type UpsertAIRoleCategoryReq_Op int32

const (
	UpsertAIRoleCategoryReq_OpUnspecified UpsertAIRoleCategoryReq_Op = 0
	// 创建
	UpsertAIRoleCategoryReq_OpCreate UpsertAIRoleCategoryReq_Op = 1
	// 更新
	UpsertAIRoleCategoryReq_OpUpdate UpsertAIRoleCategoryReq_Op = 2
)

var UpsertAIRoleCategoryReq_Op_name = map[int32]string{
	0: "OpUnspecified",
	1: "OpCreate",
	2: "OpUpdate",
}
var UpsertAIRoleCategoryReq_Op_value = map[string]int32{
	"OpUnspecified": 0,
	"OpCreate":      1,
	"OpUpdate":      2,
}

func (x UpsertAIRoleCategoryReq_Op) String() string {
	return proto.EnumName(UpsertAIRoleCategoryReq_Op_name, int32(x))
}
func (UpsertAIRoleCategoryReq_Op) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{48, 0}
}

type BindEntity_EntityType int32

const (
	BindEntity_EntityType_Unspecified BindEntity_EntityType = 0
	BindEntity_EntityType_Role        BindEntity_EntityType = 1
)

var BindEntity_EntityType_name = map[int32]string{
	0: "EntityType_Unspecified",
	1: "EntityType_Role",
}
var BindEntity_EntityType_value = map[string]int32{
	"EntityType_Unspecified": 0,
	"EntityType_Role":        1,
}

func (x BindEntity_EntityType) String() string {
	return proto.EnumName(BindEntity_EntityType_name, int32(x))
}
func (BindEntity_EntityType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{108, 0}
}

type TemplateMsg_SenderType int32

const (
	TemplateMsg_SenderType_Unspecified TemplateMsg_SenderType = 0
	TemplateMsg_SenderType_AI          TemplateMsg_SenderType = 1
	TemplateMsg_SenderType_User        TemplateMsg_SenderType = 2
)

var TemplateMsg_SenderType_name = map[int32]string{
	0: "SenderType_Unspecified",
	1: "SenderType_AI",
	2: "SenderType_User",
}
var TemplateMsg_SenderType_value = map[string]int32{
	"SenderType_Unspecified": 0,
	"SenderType_AI":          1,
	"SenderType_User":        2,
}

func (x TemplateMsg_SenderType) String() string {
	return proto.EnumName(TemplateMsg_SenderType_name, int32(x))
}
func (TemplateMsg_SenderType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{110, 0}
}

// AI角色分类标签信息
type AIRoleCategory struct {
	Id                   string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Sort                 uint32                 `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	UpdateTime           int64                  `protobuf:"varint,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Tips                 string                 `protobuf:"bytes,5,opt,name=tips,proto3" json:"tips,omitempty"`
	PropScenes           []AIRoleCategory_Scene `protobuf:"varint,6,rep,packed,name=prop_scenes,json=propScenes,proto3,enum=aigc_soulmate.AIRoleCategory_Scene" json:"prop_scenes,omitempty"`
	Props                []*AIRoleCategory_Prop `protobuf:"bytes,7,rep,name=props,proto3" json:"props,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AIRoleCategory) Reset()         { *m = AIRoleCategory{} }
func (m *AIRoleCategory) String() string { return proto.CompactTextString(m) }
func (*AIRoleCategory) ProtoMessage()    {}
func (*AIRoleCategory) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{0}
}
func (m *AIRoleCategory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRoleCategory.Unmarshal(m, b)
}
func (m *AIRoleCategory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRoleCategory.Marshal(b, m, deterministic)
}
func (dst *AIRoleCategory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRoleCategory.Merge(dst, src)
}
func (m *AIRoleCategory) XXX_Size() int {
	return xxx_messageInfo_AIRoleCategory.Size(m)
}
func (m *AIRoleCategory) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRoleCategory.DiscardUnknown(m)
}

var xxx_messageInfo_AIRoleCategory proto.InternalMessageInfo

func (m *AIRoleCategory) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *AIRoleCategory) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AIRoleCategory) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *AIRoleCategory) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *AIRoleCategory) GetTips() string {
	if m != nil {
		return m.Tips
	}
	return ""
}

func (m *AIRoleCategory) GetPropScenes() []AIRoleCategory_Scene {
	if m != nil {
		return m.PropScenes
	}
	return nil
}

func (m *AIRoleCategory) GetProps() []*AIRoleCategory_Prop {
	if m != nil {
		return m.Props
	}
	return nil
}

// 属性中的标签
type AIRoleCategory_Label struct {
	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 标签可以在哪些场景显示
	Scenes               []AIRoleCategory_Scene `protobuf:"varint,3,rep,packed,name=scenes,proto3,enum=aigc_soulmate.AIRoleCategory_Scene" json:"scenes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AIRoleCategory_Label) Reset()         { *m = AIRoleCategory_Label{} }
func (m *AIRoleCategory_Label) String() string { return proto.CompactTextString(m) }
func (*AIRoleCategory_Label) ProtoMessage()    {}
func (*AIRoleCategory_Label) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{0, 0}
}
func (m *AIRoleCategory_Label) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRoleCategory_Label.Unmarshal(m, b)
}
func (m *AIRoleCategory_Label) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRoleCategory_Label.Marshal(b, m, deterministic)
}
func (dst *AIRoleCategory_Label) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRoleCategory_Label.Merge(dst, src)
}
func (m *AIRoleCategory_Label) XXX_Size() int {
	return xxx_messageInfo_AIRoleCategory_Label.Size(m)
}
func (m *AIRoleCategory_Label) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRoleCategory_Label.DiscardUnknown(m)
}

var xxx_messageInfo_AIRoleCategory_Label proto.InternalMessageInfo

func (m *AIRoleCategory_Label) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *AIRoleCategory_Label) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AIRoleCategory_Label) GetScenes() []AIRoleCategory_Scene {
	if m != nil {
		return m.Scenes
	}
	return nil
}

// 分类中的属性
type AIRoleCategory_Prop struct {
	Id                   string                  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type                 AIRoleCategory_PropType `protobuf:"varint,3,opt,name=type,proto3,enum=aigc_soulmate.AIRoleCategory_PropType" json:"type,omitempty"`
	Labels               []*AIRoleCategory_Label `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels,omitempty"`
	LabelSelectLimit     uint32                  `protobuf:"varint,5,opt,name=label_select_limit,json=labelSelectLimit,proto3" json:"label_select_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AIRoleCategory_Prop) Reset()         { *m = AIRoleCategory_Prop{} }
func (m *AIRoleCategory_Prop) String() string { return proto.CompactTextString(m) }
func (*AIRoleCategory_Prop) ProtoMessage()    {}
func (*AIRoleCategory_Prop) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{0, 1}
}
func (m *AIRoleCategory_Prop) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRoleCategory_Prop.Unmarshal(m, b)
}
func (m *AIRoleCategory_Prop) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRoleCategory_Prop.Marshal(b, m, deterministic)
}
func (dst *AIRoleCategory_Prop) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRoleCategory_Prop.Merge(dst, src)
}
func (m *AIRoleCategory_Prop) XXX_Size() int {
	return xxx_messageInfo_AIRoleCategory_Prop.Size(m)
}
func (m *AIRoleCategory_Prop) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRoleCategory_Prop.DiscardUnknown(m)
}

var xxx_messageInfo_AIRoleCategory_Prop proto.InternalMessageInfo

func (m *AIRoleCategory_Prop) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *AIRoleCategory_Prop) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AIRoleCategory_Prop) GetType() AIRoleCategory_PropType {
	if m != nil {
		return m.Type
	}
	return AIRoleCategory_PropTypeUnspecified
}

func (m *AIRoleCategory_Prop) GetLabels() []*AIRoleCategory_Label {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *AIRoleCategory_Prop) GetLabelSelectLimit() uint32 {
	if m != nil {
		return m.LabelSelectLimit
	}
	return 0
}

// AI角色进入触发文案
type AIRoleGreeting struct {
	// 第n次
	Seq uint32 `protobuf:"varint,1,opt,name=seq,proto3" json:"seq,omitempty"`
	// 第n次进入触发文案
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIRoleGreeting) Reset()         { *m = AIRoleGreeting{} }
func (m *AIRoleGreeting) String() string { return proto.CompactTextString(m) }
func (*AIRoleGreeting) ProtoMessage()    {}
func (*AIRoleGreeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{1}
}
func (m *AIRoleGreeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRoleGreeting.Unmarshal(m, b)
}
func (m *AIRoleGreeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRoleGreeting.Marshal(b, m, deterministic)
}
func (dst *AIRoleGreeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRoleGreeting.Merge(dst, src)
}
func (m *AIRoleGreeting) XXX_Size() int {
	return xxx_messageInfo_AIRoleGreeting.Size(m)
}
func (m *AIRoleGreeting) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRoleGreeting.DiscardUnknown(m)
}

var xxx_messageInfo_AIRoleGreeting proto.InternalMessageInfo

func (m *AIRoleGreeting) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

func (m *AIRoleGreeting) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

// AI角色开场白
type AIRolePrologue struct {
	// 开场白文本内容
	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// 开场白语音链接
	Audio                string   `protobuf:"bytes,2,opt,name=audio,proto3" json:"audio,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIRolePrologue) Reset()         { *m = AIRolePrologue{} }
func (m *AIRolePrologue) String() string { return proto.CompactTextString(m) }
func (*AIRolePrologue) ProtoMessage()    {}
func (*AIRolePrologue) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{2}
}
func (m *AIRolePrologue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRolePrologue.Unmarshal(m, b)
}
func (m *AIRolePrologue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRolePrologue.Marshal(b, m, deterministic)
}
func (dst *AIRolePrologue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRolePrologue.Merge(dst, src)
}
func (m *AIRolePrologue) XXX_Size() int {
	return xxx_messageInfo_AIRolePrologue.Size(m)
}
func (m *AIRolePrologue) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRolePrologue.DiscardUnknown(m)
}

var xxx_messageInfo_AIRolePrologue proto.InternalMessageInfo

func (m *AIRolePrologue) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *AIRolePrologue) GetAudio() string {
	if m != nil {
		return m.Audio
	}
	return ""
}

type AIRole struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// AI头像
	Avatar string `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// AI风格
	Style string `protobuf:"bytes,3,opt,name=style,proto3" json:"style,omitempty"`
	// AI性别 0:女 1:男
	Sex int32 `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	// AI头像大图
	Image string `protobuf:"bytes,5,opt,name=image,proto3" json:"image,omitempty"`
	// 介绍文案
	Intro string `protobuf:"bytes,6,opt,name=intro,proto3" json:"intro,omitempty"`
	// AI形象类型
	Type AIRoleType `protobuf:"varint,7,opt,name=type,proto3,enum=aigc_soulmate.AIRoleType" json:"type,omitempty"`
	// 对话框颜色
	DialogColor string `protobuf:"bytes,8,opt,name=dialog_color,json=dialogColor,proto3" json:"dialog_color,omitempty"`
	// 名称
	Name string `protobuf:"bytes,12,opt,name=name,proto3" json:"name,omitempty"`
	// 状态
	State AIRoleState `protobuf:"varint,13,opt,name=state,proto3,enum=aigc_soulmate.AIRoleState" json:"state,omitempty"`
	// 设定/说明
	Character string `protobuf:"bytes,14,opt,name=character,proto3" json:"character,omitempty"`
	// 角色所属分类
	Category *AIRoleCategory `protobuf:"bytes,15,opt,name=category,proto3" json:"category,omitempty"`
	// 标签
	Tags []string `protobuf:"bytes,16,rep,name=tags,proto3" json:"tags,omitempty"`
	// 开场白
	Prologue string `protobuf:"bytes,17,opt,name=prologue,proto3" json:"prologue,omitempty"`
	// 开场白语音 url
	PrologueAudio string `protobuf:"bytes,18,opt,name=prologue_audio,json=prologueAudio,proto3" json:"prologue_audio,omitempty"`
	// 音色
	Timbre string `protobuf:"bytes,19,opt,name=timbre,proto3" json:"timbre,omitempty"`
	// 模型
	PromptId string `protobuf:"bytes,20,opt,name=prompt_id,json=promptId,proto3" json:"prompt_id,omitempty"`
	// 模型版本
	PromptVersion string `protobuf:"bytes,21,opt,name=prompt_version,json=promptVersion,proto3" json:"prompt_version,omitempty"`
	// 是否开启推荐回复
	EnableRcmdReply bool `protobuf:"varint,22,opt,name=enable_rcmd_reply,json=enableRcmdReply,proto3" json:"enable_rcmd_reply,omitempty"`
	// 角标 url
	CornerIcon string `protobuf:"bytes,23,opt,name=corner_icon,json=cornerIcon,proto3" json:"corner_icon,omitempty"`
	// 第n次进入触发文案
	Greetings []*AIRoleGreeting `protobuf:"bytes,24,rep,name=greetings,proto3" json:"greetings,omitempty"`
	// 创建者uid
	Uid uint32 `protobuf:"varint,25,opt,name=uid,proto3" json:"uid,omitempty"`
	// 审核结果
	AuditResult AuditResult `protobuf:"varint,26,opt,name=audit_result,json=auditResult,proto3,enum=aigc_soulmate.AuditResult" json:"audit_result,omitempty"`
	// 强插位置
	InsertPos uint32 `protobuf:"varint,27,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	// 是否在首页展示/曝光 true:展示 false:不展示
	Exposed bool `protobuf:"varint,28,opt,name=exposed,proto3" json:"exposed,omitempty"`
	// 最近一次的送审时间
	AuditAt int64 `protobuf:"varint,29,opt,name=audit_at,json=auditAt,proto3" json:"audit_at,omitempty"`
	// 配置点赞数
	ConfigLikeNum int32 `protobuf:"varint,30,opt,name=config_like_num,json=configLikeNum,proto3" json:"config_like_num,omitempty"`
	// 用户点赞数
	UserLikeNum uint32 `protobuf:"varint,31,opt,name=user_like_num,json=userLikeNum,proto3" json:"user_like_num,omitempty"`
	// 角色创建时间
	CreatedAt int64 `protobuf:"varint,32,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 点赞状态
	LikeState AIRole_LikeState `protobuf:"varint,33,opt,name=like_state,json=likeState,proto3,enum=aigc_soulmate.AIRole_LikeState" json:"like_state,omitempty"`
	// 绑定的故事ID
	StoryId string `protobuf:"bytes,34,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	// 入口标签
	EntranceTag string `protobuf:"bytes,35,opt,name=entrance_tag,json=entranceTag,proto3" json:"entrance_tag,omitempty"`
	// 领取页开场白
	CollectPrologue *AIRolePrologue `protobuf:"bytes,36,opt,name=collect_prologue,json=collectPrologue,proto3" json:"collect_prologue,omitempty"`
	// 故事模式
	StoryMode AIRoleStoryMode `protobuf:"varint,37,opt,name=story_mode,json=storyMode,proto3,enum=aigc_soulmate.AIRoleStoryMode" json:"story_mode,omitempty"`
	// 群聊角色配置
	GroupRoleConfig *GroupRoleConfig `protobuf:"bytes,38,opt,name=group_role_config,json=groupRoleConfig,proto3" json:"group_role_config,omitempty"`
	CreatorInfoType uint32           `protobuf:"varint,39,opt,name=creator_info_type,json=creatorInfoType,proto3" json:"creator_info_type,omitempty"`
	AppointUid      uint32           `protobuf:"varint,40,opt,name=appoint_uid,json=appointUid,proto3" json:"appoint_uid,omitempty"`
	UserRoleSetting string           `protobuf:"bytes,41,opt,name=user_role_setting,json=userRoleSetting,proto3" json:"user_role_setting,omitempty"`
	RelationIds     []uint32         `protobuf:"varint,42,rep,packed,name=relation_ids,json=relationIds,proto3" json:"relation_ids,omitempty"`
	// 更新时间
	UpdatedAt            int64       `protobuf:"varint,43,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Scope                AIRoleScope `protobuf:"varint,44,opt,name=scope,proto3,enum=aigc_soulmate.AIRoleScope" json:"scope,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *AIRole) Reset()         { *m = AIRole{} }
func (m *AIRole) String() string { return proto.CompactTextString(m) }
func (*AIRole) ProtoMessage()    {}
func (*AIRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{3}
}
func (m *AIRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRole.Unmarshal(m, b)
}
func (m *AIRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRole.Marshal(b, m, deterministic)
}
func (dst *AIRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRole.Merge(dst, src)
}
func (m *AIRole) XXX_Size() int {
	return xxx_messageInfo_AIRole.Size(m)
}
func (m *AIRole) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRole.DiscardUnknown(m)
}

var xxx_messageInfo_AIRole proto.InternalMessageInfo

func (m *AIRole) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIRole) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *AIRole) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

func (m *AIRole) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *AIRole) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *AIRole) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *AIRole) GetType() AIRoleType {
	if m != nil {
		return m.Type
	}
	return AIRoleType_AIRoleTypePartner
}

func (m *AIRole) GetDialogColor() string {
	if m != nil {
		return m.DialogColor
	}
	return ""
}

func (m *AIRole) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AIRole) GetState() AIRoleState {
	if m != nil {
		return m.State
	}
	return AIRoleState_AIRoleStateNone
}

func (m *AIRole) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *AIRole) GetCategory() *AIRoleCategory {
	if m != nil {
		return m.Category
	}
	return nil
}

func (m *AIRole) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *AIRole) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *AIRole) GetPrologueAudio() string {
	if m != nil {
		return m.PrologueAudio
	}
	return ""
}

func (m *AIRole) GetTimbre() string {
	if m != nil {
		return m.Timbre
	}
	return ""
}

func (m *AIRole) GetPromptId() string {
	if m != nil {
		return m.PromptId
	}
	return ""
}

func (m *AIRole) GetPromptVersion() string {
	if m != nil {
		return m.PromptVersion
	}
	return ""
}

func (m *AIRole) GetEnableRcmdReply() bool {
	if m != nil {
		return m.EnableRcmdReply
	}
	return false
}

func (m *AIRole) GetCornerIcon() string {
	if m != nil {
		return m.CornerIcon
	}
	return ""
}

func (m *AIRole) GetGreetings() []*AIRoleGreeting {
	if m != nil {
		return m.Greetings
	}
	return nil
}

func (m *AIRole) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AIRole) GetAuditResult() AuditResult {
	if m != nil {
		return m.AuditResult
	}
	return AuditResult_AuditResultReview
}

func (m *AIRole) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *AIRole) GetExposed() bool {
	if m != nil {
		return m.Exposed
	}
	return false
}

func (m *AIRole) GetAuditAt() int64 {
	if m != nil {
		return m.AuditAt
	}
	return 0
}

func (m *AIRole) GetConfigLikeNum() int32 {
	if m != nil {
		return m.ConfigLikeNum
	}
	return 0
}

func (m *AIRole) GetUserLikeNum() uint32 {
	if m != nil {
		return m.UserLikeNum
	}
	return 0
}

func (m *AIRole) GetCreatedAt() int64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *AIRole) GetLikeState() AIRole_LikeState {
	if m != nil {
		return m.LikeState
	}
	return AIRole_LikeStateUnspecified
}

func (m *AIRole) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

func (m *AIRole) GetEntranceTag() string {
	if m != nil {
		return m.EntranceTag
	}
	return ""
}

func (m *AIRole) GetCollectPrologue() *AIRolePrologue {
	if m != nil {
		return m.CollectPrologue
	}
	return nil
}

func (m *AIRole) GetStoryMode() AIRoleStoryMode {
	if m != nil {
		return m.StoryMode
	}
	return AIRoleStoryMode_AIRoleStoryModeDefault
}

func (m *AIRole) GetGroupRoleConfig() *GroupRoleConfig {
	if m != nil {
		return m.GroupRoleConfig
	}
	return nil
}

func (m *AIRole) GetCreatorInfoType() uint32 {
	if m != nil {
		return m.CreatorInfoType
	}
	return 0
}

func (m *AIRole) GetAppointUid() uint32 {
	if m != nil {
		return m.AppointUid
	}
	return 0
}

func (m *AIRole) GetUserRoleSetting() string {
	if m != nil {
		return m.UserRoleSetting
	}
	return ""
}

func (m *AIRole) GetRelationIds() []uint32 {
	if m != nil {
		return m.RelationIds
	}
	return nil
}

func (m *AIRole) GetUpdatedAt() int64 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *AIRole) GetScope() AIRoleScope {
	if m != nil {
		return m.Scope
	}
	return AIRoleScope_AI_ROLE_SCOPE_UNSPECIFIED
}

type AIPartner struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ta的名字
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 你希望ta怎么称呼你
	CallName string `protobuf:"bytes,3,opt,name=call_name,json=callName,proto3" json:"call_name,omitempty"`
	// AI伴侣形象卡
	Role *AIRole `protobuf:"bytes,4,opt,name=role,proto3" json:"role,omitempty"`
	// [不再接收ta的消息]开关 true:打开 false:关闭
	Silent bool `protobuf:"varint,5,opt,name=silent,proto3" json:"silent,omitempty"`
	// AI伴侣所属用户id
	Uid uint32 `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	// AI伴侣修改形象次数
	ChangeRoleCnt uint32 `protobuf:"varint,7,opt,name=change_role_cnt,json=changeRoleCnt,proto3" json:"change_role_cnt,omitempty"`
	// AI伴侣来源
	Source AIPartnerSource `protobuf:"varint,8,opt,name=source,proto3,enum=aigc_soulmate.AIPartnerSource" json:"source,omitempty"`
	// 未设置过名字
	UnsetName bool `protobuf:"varint,9,opt,name=unset_name,json=unsetName,proto3" json:"unset_name,omitempty"`
	// 未设置过角色
	UnsetRole            bool     `protobuf:"varint,10,opt,name=unset_role,json=unsetRole,proto3" json:"unset_role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIPartner) Reset()         { *m = AIPartner{} }
func (m *AIPartner) String() string { return proto.CompactTextString(m) }
func (*AIPartner) ProtoMessage()    {}
func (*AIPartner) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{4}
}
func (m *AIPartner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIPartner.Unmarshal(m, b)
}
func (m *AIPartner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIPartner.Marshal(b, m, deterministic)
}
func (dst *AIPartner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIPartner.Merge(dst, src)
}
func (m *AIPartner) XXX_Size() int {
	return xxx_messageInfo_AIPartner.Size(m)
}
func (m *AIPartner) XXX_DiscardUnknown() {
	xxx_messageInfo_AIPartner.DiscardUnknown(m)
}

var xxx_messageInfo_AIPartner proto.InternalMessageInfo

func (m *AIPartner) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIPartner) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AIPartner) GetCallName() string {
	if m != nil {
		return m.CallName
	}
	return ""
}

func (m *AIPartner) GetRole() *AIRole {
	if m != nil {
		return m.Role
	}
	return nil
}

func (m *AIPartner) GetSilent() bool {
	if m != nil {
		return m.Silent
	}
	return false
}

func (m *AIPartner) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AIPartner) GetChangeRoleCnt() uint32 {
	if m != nil {
		return m.ChangeRoleCnt
	}
	return 0
}

func (m *AIPartner) GetSource() AIPartnerSource {
	if m != nil {
		return m.Source
	}
	return AIPartnerSource_AIPartnerSourceUser
}

func (m *AIPartner) GetUnsetName() bool {
	if m != nil {
		return m.UnsetName
	}
	return false
}

func (m *AIPartner) GetUnsetRole() bool {
	if m != nil {
		return m.UnsetRole
	}
	return false
}

type SharedRole struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Image                string   `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	Avatar               string   `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Character            string   `protobuf:"bytes,5,opt,name=character,proto3" json:"character,omitempty"`
	Tags                 []string `protobuf:"bytes,6,rep,name=tags,proto3" json:"tags,omitempty"`
	Prologue             string   `protobuf:"bytes,7,opt,name=prologue,proto3" json:"prologue,omitempty"`
	PrologueAudio        string   `protobuf:"bytes,8,opt,name=prologue_audio,json=prologueAudio,proto3" json:"prologue_audio,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SharedRole) Reset()         { *m = SharedRole{} }
func (m *SharedRole) String() string { return proto.CompactTextString(m) }
func (*SharedRole) ProtoMessage()    {}
func (*SharedRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{5}
}
func (m *SharedRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SharedRole.Unmarshal(m, b)
}
func (m *SharedRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SharedRole.Marshal(b, m, deterministic)
}
func (dst *SharedRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SharedRole.Merge(dst, src)
}
func (m *SharedRole) XXX_Size() int {
	return xxx_messageInfo_SharedRole.Size(m)
}
func (m *SharedRole) XXX_DiscardUnknown() {
	xxx_messageInfo_SharedRole.DiscardUnknown(m)
}

var xxx_messageInfo_SharedRole proto.InternalMessageInfo

func (m *SharedRole) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SharedRole) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SharedRole) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *SharedRole) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *SharedRole) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *SharedRole) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *SharedRole) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *SharedRole) GetPrologueAudio() string {
	if m != nil {
		return m.PrologueAudio
	}
	return ""
}

type AIRoleLikeInfo struct {
	Id                   uint32                   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32                   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	State                AIRoleLikeInfo_LikeState `protobuf:"varint,3,opt,name=state,proto3,enum=aigc_soulmate.AIRoleLikeInfo_LikeState" json:"state,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AIRoleLikeInfo) Reset()         { *m = AIRoleLikeInfo{} }
func (m *AIRoleLikeInfo) String() string { return proto.CompactTextString(m) }
func (*AIRoleLikeInfo) ProtoMessage()    {}
func (*AIRoleLikeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{6}
}
func (m *AIRoleLikeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRoleLikeInfo.Unmarshal(m, b)
}
func (m *AIRoleLikeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRoleLikeInfo.Marshal(b, m, deterministic)
}
func (dst *AIRoleLikeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRoleLikeInfo.Merge(dst, src)
}
func (m *AIRoleLikeInfo) XXX_Size() int {
	return xxx_messageInfo_AIRoleLikeInfo.Size(m)
}
func (m *AIRoleLikeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRoleLikeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AIRoleLikeInfo proto.InternalMessageInfo

func (m *AIRoleLikeInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIRoleLikeInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AIRoleLikeInfo) GetState() AIRoleLikeInfo_LikeState {
	if m != nil {
		return m.State
	}
	return AIRoleLikeInfo_LikeStateUnspecified
}

// 互动玩法
type InteractiveGame struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 创建来源
	Source InteractiveGameSource `protobuf:"varint,2,opt,name=source,proto3,enum=aigc_soulmate.InteractiveGameSource" json:"source,omitempty"`
	// 创建者uid
	Uid uint32 `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	// 绑定角色ID
	RoleId uint32 `protobuf:"varint,4,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 绑定主题ID
	TopicId uint32 `protobuf:"varint,5,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	// 标题
	Title string `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	// 描述
	Desc string `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`
	// 开场白
	Prologue string `protobuf:"bytes,8,opt,name=prologue,proto3" json:"prologue,omitempty"`
	// 公开/私有状态
	State InteractiveGameState `protobuf:"varint,9,opt,name=state,proto3,enum=aigc_soulmate.InteractiveGameState" json:"state,omitempty"`
	// 是否外显
	Exposure InteractiveGameExposure `protobuf:"varint,10,opt,name=exposure,proto3,enum=aigc_soulmate.InteractiveGameExposure" json:"exposure,omitempty"`
	// 创建时间
	CreatedAt int64 `protobuf:"varint,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt            int64    `protobuf:"varint,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InteractiveGame) Reset()         { *m = InteractiveGame{} }
func (m *InteractiveGame) String() string { return proto.CompactTextString(m) }
func (*InteractiveGame) ProtoMessage()    {}
func (*InteractiveGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{7}
}
func (m *InteractiveGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractiveGame.Unmarshal(m, b)
}
func (m *InteractiveGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractiveGame.Marshal(b, m, deterministic)
}
func (dst *InteractiveGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractiveGame.Merge(dst, src)
}
func (m *InteractiveGame) XXX_Size() int {
	return xxx_messageInfo_InteractiveGame.Size(m)
}
func (m *InteractiveGame) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractiveGame.DiscardUnknown(m)
}

var xxx_messageInfo_InteractiveGame proto.InternalMessageInfo

func (m *InteractiveGame) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *InteractiveGame) GetSource() InteractiveGameSource {
	if m != nil {
		return m.Source
	}
	return InteractiveGameSource_InteractiveGameSourceUnspecified
}

func (m *InteractiveGame) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *InteractiveGame) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *InteractiveGame) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *InteractiveGame) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *InteractiveGame) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *InteractiveGame) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *InteractiveGame) GetState() InteractiveGameState {
	if m != nil {
		return m.State
	}
	return InteractiveGameState_InteractiveGameStateUnspecified
}

func (m *InteractiveGame) GetExposure() InteractiveGameExposure {
	if m != nil {
		return m.Exposure
	}
	return InteractiveGameExposure_InteractiveGameExposureUnspecified
}

func (m *InteractiveGame) GetCreatedAt() int64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *InteractiveGame) GetUpdatedAt() int64 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

type CreateAIRoleReq struct {
	Role                 *CreateAIRoleReq_Role `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *CreateAIRoleReq) Reset()         { *m = CreateAIRoleReq{} }
func (m *CreateAIRoleReq) String() string { return proto.CompactTextString(m) }
func (*CreateAIRoleReq) ProtoMessage()    {}
func (*CreateAIRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{8}
}
func (m *CreateAIRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIRoleReq.Unmarshal(m, b)
}
func (m *CreateAIRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIRoleReq.Marshal(b, m, deterministic)
}
func (dst *CreateAIRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIRoleReq.Merge(dst, src)
}
func (m *CreateAIRoleReq) XXX_Size() int {
	return xxx_messageInfo_CreateAIRoleReq.Size(m)
}
func (m *CreateAIRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIRoleReq proto.InternalMessageInfo

func (m *CreateAIRoleReq) GetRole() *CreateAIRoleReq_Role {
	if m != nil {
		return m.Role
	}
	return nil
}

type CreateAIRoleReq_Role struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 角色类型
	Type AIRoleType `protobuf:"varint,2,opt,name=type,proto3,enum=aigc_soulmate.AIRoleType" json:"type,omitempty"`
	// 来源
	Source AIRoleSource `protobuf:"varint,3,opt,name=source,proto3,enum=aigc_soulmate.AIRoleSource" json:"source,omitempty"`
	// 性别 0:女 1:男 2:其他
	Sex int32 `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	// 名称
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 背景图
	Image string `protobuf:"bytes,7,opt,name=image,proto3" json:"image,omitempty"`
	// 状态
	State AIRoleState `protobuf:"varint,8,opt,name=state,proto3,enum=aigc_soulmate.AIRoleState" json:"state,omitempty"`
	// 设定/说明
	Character string `protobuf:"bytes,9,opt,name=character,proto3" json:"character,omitempty"`
	// 角色所属分类ID
	CategoryId string `protobuf:"bytes,10,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 标签
	Tags []string `protobuf:"bytes,11,rep,name=tags,proto3" json:"tags,omitempty"`
	// 开场白
	Prologue string `protobuf:"bytes,12,opt,name=prologue,proto3" json:"prologue,omitempty"`
	// 开场白语音 url
	PrologueAudio string `protobuf:"bytes,13,opt,name=prologue_audio,json=prologueAudio,proto3" json:"prologue_audio,omitempty"`
	// 音色
	Timbre string `protobuf:"bytes,14,opt,name=timbre,proto3" json:"timbre,omitempty"`
	// 模型ID
	PromptId string `protobuf:"bytes,15,opt,name=prompt_id,json=promptId,proto3" json:"prompt_id,omitempty"`
	// 模型版本
	PromptVersion string `protobuf:"bytes,16,opt,name=prompt_version,json=promptVersion,proto3" json:"prompt_version,omitempty"`
	// 角标 url
	CornerIcon string `protobuf:"bytes,17,opt,name=corner_icon,json=cornerIcon,proto3" json:"corner_icon,omitempty"`
	// 是否开启推荐回复
	EnableRcmdReply bool `protobuf:"varint,18,opt,name=enable_rcmd_reply,json=enableRcmdReply,proto3" json:"enable_rcmd_reply,omitempty"`
	// 第n次进入触发文案
	Greetings []*AIRoleGreeting `protobuf:"bytes,19,rep,name=greetings,proto3" json:"greetings,omitempty"`
	// 强插位置
	InsertPos uint32 `protobuf:"varint,21,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	// 是否展示/曝光到首页 true:展示 false:不展示
	Exposed bool `protobuf:"varint,22,opt,name=exposed,proto3" json:"exposed,omitempty"`
	// 配置点赞数
	ConfigLikeNum int32 `protobuf:"varint,23,opt,name=config_like_num,json=configLikeNum,proto3" json:"config_like_num,omitempty"`
	// 绑定的故事ID
	StoryId string `protobuf:"bytes,24,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	// 入口标签
	EntranceTag string `protobuf:"bytes,25,opt,name=entrance_tag,json=entranceTag,proto3" json:"entrance_tag,omitempty"`
	// 领取页开场白
	CollectPrologue *AIRolePrologue `protobuf:"bytes,26,opt,name=collect_prologue,json=collectPrologue,proto3" json:"collect_prologue,omitempty"`
	// 故事模式
	StoryMode AIRoleStoryMode `protobuf:"varint,27,opt,name=story_mode,json=storyMode,proto3,enum=aigc_soulmate.AIRoleStoryMode" json:"story_mode,omitempty"`
	// 群聊角色配置
	GroupRoleConfig *GroupRoleConfig `protobuf:"bytes,28,opt,name=group_role_config,json=groupRoleConfig,proto3" json:"group_role_config,omitempty"`
	CreatorInfoType uint32           `protobuf:"varint,29,opt,name=creator_info_type,json=creatorInfoType,proto3" json:"creator_info_type,omitempty"`
	AppointUid      uint32           `protobuf:"varint,30,opt,name=appoint_uid,json=appointUid,proto3" json:"appoint_uid,omitempty"`
	UserRoleSetting string           `protobuf:"bytes,31,opt,name=user_role_setting,json=userRoleSetting,proto3" json:"user_role_setting,omitempty"`
	// 关联角色ID列表
	RelationIds []uint32 `protobuf:"varint,32,rep,packed,name=relation_ids,json=relationIds,proto3" json:"relation_ids,omitempty"`
	// 使用/可见范围
	Scope                AIRoleScope `protobuf:"varint,33,opt,name=scope,proto3,enum=aigc_soulmate.AIRoleScope" json:"scope,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CreateAIRoleReq_Role) Reset()         { *m = CreateAIRoleReq_Role{} }
func (m *CreateAIRoleReq_Role) String() string { return proto.CompactTextString(m) }
func (*CreateAIRoleReq_Role) ProtoMessage()    {}
func (*CreateAIRoleReq_Role) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{8, 0}
}
func (m *CreateAIRoleReq_Role) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIRoleReq_Role.Unmarshal(m, b)
}
func (m *CreateAIRoleReq_Role) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIRoleReq_Role.Marshal(b, m, deterministic)
}
func (dst *CreateAIRoleReq_Role) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIRoleReq_Role.Merge(dst, src)
}
func (m *CreateAIRoleReq_Role) XXX_Size() int {
	return xxx_messageInfo_CreateAIRoleReq_Role.Size(m)
}
func (m *CreateAIRoleReq_Role) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIRoleReq_Role.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIRoleReq_Role proto.InternalMessageInfo

func (m *CreateAIRoleReq_Role) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CreateAIRoleReq_Role) GetType() AIRoleType {
	if m != nil {
		return m.Type
	}
	return AIRoleType_AIRoleTypePartner
}

func (m *CreateAIRoleReq_Role) GetSource() AIRoleSource {
	if m != nil {
		return m.Source
	}
	return AIRoleSource_AIRoleSourceUnknown
}

func (m *CreateAIRoleReq_Role) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *CreateAIRoleReq_Role) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateAIRoleReq_Role) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *CreateAIRoleReq_Role) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *CreateAIRoleReq_Role) GetState() AIRoleState {
	if m != nil {
		return m.State
	}
	return AIRoleState_AIRoleStateNone
}

func (m *CreateAIRoleReq_Role) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *CreateAIRoleReq_Role) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *CreateAIRoleReq_Role) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *CreateAIRoleReq_Role) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *CreateAIRoleReq_Role) GetPrologueAudio() string {
	if m != nil {
		return m.PrologueAudio
	}
	return ""
}

func (m *CreateAIRoleReq_Role) GetTimbre() string {
	if m != nil {
		return m.Timbre
	}
	return ""
}

func (m *CreateAIRoleReq_Role) GetPromptId() string {
	if m != nil {
		return m.PromptId
	}
	return ""
}

func (m *CreateAIRoleReq_Role) GetPromptVersion() string {
	if m != nil {
		return m.PromptVersion
	}
	return ""
}

func (m *CreateAIRoleReq_Role) GetCornerIcon() string {
	if m != nil {
		return m.CornerIcon
	}
	return ""
}

func (m *CreateAIRoleReq_Role) GetEnableRcmdReply() bool {
	if m != nil {
		return m.EnableRcmdReply
	}
	return false
}

func (m *CreateAIRoleReq_Role) GetGreetings() []*AIRoleGreeting {
	if m != nil {
		return m.Greetings
	}
	return nil
}

func (m *CreateAIRoleReq_Role) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *CreateAIRoleReq_Role) GetExposed() bool {
	if m != nil {
		return m.Exposed
	}
	return false
}

func (m *CreateAIRoleReq_Role) GetConfigLikeNum() int32 {
	if m != nil {
		return m.ConfigLikeNum
	}
	return 0
}

func (m *CreateAIRoleReq_Role) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

func (m *CreateAIRoleReq_Role) GetEntranceTag() string {
	if m != nil {
		return m.EntranceTag
	}
	return ""
}

func (m *CreateAIRoleReq_Role) GetCollectPrologue() *AIRolePrologue {
	if m != nil {
		return m.CollectPrologue
	}
	return nil
}

func (m *CreateAIRoleReq_Role) GetStoryMode() AIRoleStoryMode {
	if m != nil {
		return m.StoryMode
	}
	return AIRoleStoryMode_AIRoleStoryModeDefault
}

func (m *CreateAIRoleReq_Role) GetGroupRoleConfig() *GroupRoleConfig {
	if m != nil {
		return m.GroupRoleConfig
	}
	return nil
}

func (m *CreateAIRoleReq_Role) GetCreatorInfoType() uint32 {
	if m != nil {
		return m.CreatorInfoType
	}
	return 0
}

func (m *CreateAIRoleReq_Role) GetAppointUid() uint32 {
	if m != nil {
		return m.AppointUid
	}
	return 0
}

func (m *CreateAIRoleReq_Role) GetUserRoleSetting() string {
	if m != nil {
		return m.UserRoleSetting
	}
	return ""
}

func (m *CreateAIRoleReq_Role) GetRelationIds() []uint32 {
	if m != nil {
		return m.RelationIds
	}
	return nil
}

func (m *CreateAIRoleReq_Role) GetScope() AIRoleScope {
	if m != nil {
		return m.Scope
	}
	return AIRoleScope_AI_ROLE_SCOPE_UNSPECIFIED
}

// 群开场白
type AIGroupPrologue struct {
	// 开场白文本内容
	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// 开场白语音链接
	Audio string `protobuf:"bytes,2,opt,name=audio,proto3" json:"audio,omitempty"`
	// 开场白顺序
	Priority             uint32   `protobuf:"varint,3,opt,name=priority,proto3" json:"priority,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIGroupPrologue) Reset()         { *m = AIGroupPrologue{} }
func (m *AIGroupPrologue) String() string { return proto.CompactTextString(m) }
func (*AIGroupPrologue) ProtoMessage()    {}
func (*AIGroupPrologue) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{9}
}
func (m *AIGroupPrologue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIGroupPrologue.Unmarshal(m, b)
}
func (m *AIGroupPrologue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIGroupPrologue.Marshal(b, m, deterministic)
}
func (dst *AIGroupPrologue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIGroupPrologue.Merge(dst, src)
}
func (m *AIGroupPrologue) XXX_Size() int {
	return xxx_messageInfo_AIGroupPrologue.Size(m)
}
func (m *AIGroupPrologue) XXX_DiscardUnknown() {
	xxx_messageInfo_AIGroupPrologue.DiscardUnknown(m)
}

var xxx_messageInfo_AIGroupPrologue proto.InternalMessageInfo

func (m *AIGroupPrologue) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *AIGroupPrologue) GetAudio() string {
	if m != nil {
		return m.Audio
	}
	return ""
}

func (m *AIGroupPrologue) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

// 群聊角色属性配置
type GroupRoleConfig struct {
	// 群聊开场白
	Prologues []*AIGroupPrologue `protobuf:"bytes,1,rep,name=prologues,proto3" json:"prologues,omitempty"`
	// 群聊角色描述
	ChatCharacter string `protobuf:"bytes,2,opt,name=chat_character,json=chatCharacter,proto3" json:"chat_character,omitempty"`
	// 关系描述
	RelationCharacter string `protobuf:"bytes,3,opt,name=relation_character,json=relationCharacter,proto3" json:"relation_character,omitempty"`
	// 角色描述
	Desc string `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	// 欢迎语
	WelcomePrologues     []*AIGroupPrologue `protobuf:"bytes,5,rep,name=welcome_prologues,json=welcomePrologues,proto3" json:"welcome_prologues,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GroupRoleConfig) Reset()         { *m = GroupRoleConfig{} }
func (m *GroupRoleConfig) String() string { return proto.CompactTextString(m) }
func (*GroupRoleConfig) ProtoMessage()    {}
func (*GroupRoleConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{10}
}
func (m *GroupRoleConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupRoleConfig.Unmarshal(m, b)
}
func (m *GroupRoleConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupRoleConfig.Marshal(b, m, deterministic)
}
func (dst *GroupRoleConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupRoleConfig.Merge(dst, src)
}
func (m *GroupRoleConfig) XXX_Size() int {
	return xxx_messageInfo_GroupRoleConfig.Size(m)
}
func (m *GroupRoleConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupRoleConfig.DiscardUnknown(m)
}

var xxx_messageInfo_GroupRoleConfig proto.InternalMessageInfo

func (m *GroupRoleConfig) GetPrologues() []*AIGroupPrologue {
	if m != nil {
		return m.Prologues
	}
	return nil
}

func (m *GroupRoleConfig) GetChatCharacter() string {
	if m != nil {
		return m.ChatCharacter
	}
	return ""
}

func (m *GroupRoleConfig) GetRelationCharacter() string {
	if m != nil {
		return m.RelationCharacter
	}
	return ""
}

func (m *GroupRoleConfig) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *GroupRoleConfig) GetWelcomePrologues() []*AIGroupPrologue {
	if m != nil {
		return m.WelcomePrologues
	}
	return nil
}

type CreateAIRoleResp struct {
	Role                 *AIRole  `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateAIRoleResp) Reset()         { *m = CreateAIRoleResp{} }
func (m *CreateAIRoleResp) String() string { return proto.CompactTextString(m) }
func (*CreateAIRoleResp) ProtoMessage()    {}
func (*CreateAIRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{11}
}
func (m *CreateAIRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIRoleResp.Unmarshal(m, b)
}
func (m *CreateAIRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIRoleResp.Marshal(b, m, deterministic)
}
func (dst *CreateAIRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIRoleResp.Merge(dst, src)
}
func (m *CreateAIRoleResp) XXX_Size() int {
	return xxx_messageInfo_CreateAIRoleResp.Size(m)
}
func (m *CreateAIRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIRoleResp proto.InternalMessageInfo

func (m *CreateAIRoleResp) GetRole() *AIRole {
	if m != nil {
		return m.Role
	}
	return nil
}

type UpdateAIRoleReq struct {
	Role                 *UpdateAIRoleReq_Role `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *UpdateAIRoleReq) Reset()         { *m = UpdateAIRoleReq{} }
func (m *UpdateAIRoleReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAIRoleReq) ProtoMessage()    {}
func (*UpdateAIRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{12}
}
func (m *UpdateAIRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIRoleReq.Unmarshal(m, b)
}
func (m *UpdateAIRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIRoleReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAIRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIRoleReq.Merge(dst, src)
}
func (m *UpdateAIRoleReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAIRoleReq.Size(m)
}
func (m *UpdateAIRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIRoleReq proto.InternalMessageInfo

func (m *UpdateAIRoleReq) GetRole() *UpdateAIRoleReq_Role {
	if m != nil {
		return m.Role
	}
	return nil
}

type UpdateAIRoleReq_Role struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 性别 0:女 1:男 2:其他
	Sex int32 `protobuf:"varint,2,opt,name=sex,proto3" json:"sex,omitempty"`
	// 名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 背景图
	Image string `protobuf:"bytes,5,opt,name=image,proto3" json:"image,omitempty"`
	// 状态
	State AIRoleState `protobuf:"varint,6,opt,name=state,proto3,enum=aigc_soulmate.AIRoleState" json:"state,omitempty"`
	// 设定/说明
	Character string `protobuf:"bytes,7,opt,name=character,proto3" json:"character,omitempty"`
	// 角色所属分类ID
	CategoryId string `protobuf:"bytes,8,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 标签
	Tags []string `protobuf:"bytes,9,rep,name=tags,proto3" json:"tags,omitempty"`
	// 开场白
	Prologue string `protobuf:"bytes,10,opt,name=prologue,proto3" json:"prologue,omitempty"`
	// 开场白语音 url
	PrologueAudio string `protobuf:"bytes,11,opt,name=prologue_audio,json=prologueAudio,proto3" json:"prologue_audio,omitempty"`
	// 音色
	Timbre string `protobuf:"bytes,12,opt,name=timbre,proto3" json:"timbre,omitempty"`
	// 模型ID
	PromptId string `protobuf:"bytes,13,opt,name=prompt_id,json=promptId,proto3" json:"prompt_id,omitempty"`
	// 模型版本
	PromptVersion string `protobuf:"bytes,14,opt,name=prompt_version,json=promptVersion,proto3" json:"prompt_version,omitempty"`
	// 角标 url
	CornerIcon string `protobuf:"bytes,15,opt,name=corner_icon,json=cornerIcon,proto3" json:"corner_icon,omitempty"`
	// 是否开启推荐回复
	EnableRcmdReply bool `protobuf:"varint,16,opt,name=enable_rcmd_reply,json=enableRcmdReply,proto3" json:"enable_rcmd_reply,omitempty"`
	// 第n次进入触发文案
	Greetings []*AIRoleGreeting `protobuf:"bytes,17,rep,name=greetings,proto3" json:"greetings,omitempty"`
	// 强插位置
	InsertPos uint32 `protobuf:"varint,18,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	// 是否展示/曝光到首页 true:展示 false:不展示
	Exposed bool `protobuf:"varint,19,opt,name=exposed,proto3" json:"exposed,omitempty"`
	// 配置点赞数
	ConfigLikeNum int32 `protobuf:"varint,20,opt,name=config_like_num,json=configLikeNum,proto3" json:"config_like_num,omitempty"`
	// 绑定的故事ID
	StoryId string `protobuf:"bytes,21,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	// 入口标签
	EntranceTag string `protobuf:"bytes,22,opt,name=entrance_tag,json=entranceTag,proto3" json:"entrance_tag,omitempty"`
	// 领取页开场白
	CollectPrologue *AIRolePrologue `protobuf:"bytes,23,opt,name=collect_prologue,json=collectPrologue,proto3" json:"collect_prologue,omitempty"`
	// 故事模式
	StoryMode AIRoleStoryMode `protobuf:"varint,24,opt,name=story_mode,json=storyMode,proto3,enum=aigc_soulmate.AIRoleStoryMode" json:"story_mode,omitempty"`
	// 群聊角色配置
	GroupRoleConfig *GroupRoleConfig `protobuf:"bytes,25,opt,name=group_role_config,json=groupRoleConfig,proto3" json:"group_role_config,omitempty"`
	CreatorInfoType uint32           `protobuf:"varint,26,opt,name=creator_info_type,json=creatorInfoType,proto3" json:"creator_info_type,omitempty"`
	AppointUid      uint32           `protobuf:"varint,27,opt,name=appoint_uid,json=appointUid,proto3" json:"appoint_uid,omitempty"`
	UserRoleSetting string           `protobuf:"bytes,28,opt,name=user_role_setting,json=userRoleSetting,proto3" json:"user_role_setting,omitempty"`
	// 关联角色ID列表
	RelationIds []uint32 `protobuf:"varint,29,rep,packed,name=relation_ids,json=relationIds,proto3" json:"relation_ids,omitempty"`
	// 使用/可见范围
	Scope                AIRoleScope `protobuf:"varint,30,opt,name=scope,proto3,enum=aigc_soulmate.AIRoleScope" json:"scope,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpdateAIRoleReq_Role) Reset()         { *m = UpdateAIRoleReq_Role{} }
func (m *UpdateAIRoleReq_Role) String() string { return proto.CompactTextString(m) }
func (*UpdateAIRoleReq_Role) ProtoMessage()    {}
func (*UpdateAIRoleReq_Role) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{12, 0}
}
func (m *UpdateAIRoleReq_Role) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIRoleReq_Role.Unmarshal(m, b)
}
func (m *UpdateAIRoleReq_Role) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIRoleReq_Role.Marshal(b, m, deterministic)
}
func (dst *UpdateAIRoleReq_Role) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIRoleReq_Role.Merge(dst, src)
}
func (m *UpdateAIRoleReq_Role) XXX_Size() int {
	return xxx_messageInfo_UpdateAIRoleReq_Role.Size(m)
}
func (m *UpdateAIRoleReq_Role) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIRoleReq_Role.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIRoleReq_Role proto.InternalMessageInfo

func (m *UpdateAIRoleReq_Role) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateAIRoleReq_Role) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UpdateAIRoleReq_Role) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpdateAIRoleReq_Role) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *UpdateAIRoleReq_Role) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *UpdateAIRoleReq_Role) GetState() AIRoleState {
	if m != nil {
		return m.State
	}
	return AIRoleState_AIRoleStateNone
}

func (m *UpdateAIRoleReq_Role) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *UpdateAIRoleReq_Role) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *UpdateAIRoleReq_Role) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *UpdateAIRoleReq_Role) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *UpdateAIRoleReq_Role) GetPrologueAudio() string {
	if m != nil {
		return m.PrologueAudio
	}
	return ""
}

func (m *UpdateAIRoleReq_Role) GetTimbre() string {
	if m != nil {
		return m.Timbre
	}
	return ""
}

func (m *UpdateAIRoleReq_Role) GetPromptId() string {
	if m != nil {
		return m.PromptId
	}
	return ""
}

func (m *UpdateAIRoleReq_Role) GetPromptVersion() string {
	if m != nil {
		return m.PromptVersion
	}
	return ""
}

func (m *UpdateAIRoleReq_Role) GetCornerIcon() string {
	if m != nil {
		return m.CornerIcon
	}
	return ""
}

func (m *UpdateAIRoleReq_Role) GetEnableRcmdReply() bool {
	if m != nil {
		return m.EnableRcmdReply
	}
	return false
}

func (m *UpdateAIRoleReq_Role) GetGreetings() []*AIRoleGreeting {
	if m != nil {
		return m.Greetings
	}
	return nil
}

func (m *UpdateAIRoleReq_Role) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *UpdateAIRoleReq_Role) GetExposed() bool {
	if m != nil {
		return m.Exposed
	}
	return false
}

func (m *UpdateAIRoleReq_Role) GetConfigLikeNum() int32 {
	if m != nil {
		return m.ConfigLikeNum
	}
	return 0
}

func (m *UpdateAIRoleReq_Role) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

func (m *UpdateAIRoleReq_Role) GetEntranceTag() string {
	if m != nil {
		return m.EntranceTag
	}
	return ""
}

func (m *UpdateAIRoleReq_Role) GetCollectPrologue() *AIRolePrologue {
	if m != nil {
		return m.CollectPrologue
	}
	return nil
}

func (m *UpdateAIRoleReq_Role) GetStoryMode() AIRoleStoryMode {
	if m != nil {
		return m.StoryMode
	}
	return AIRoleStoryMode_AIRoleStoryModeDefault
}

func (m *UpdateAIRoleReq_Role) GetGroupRoleConfig() *GroupRoleConfig {
	if m != nil {
		return m.GroupRoleConfig
	}
	return nil
}

func (m *UpdateAIRoleReq_Role) GetCreatorInfoType() uint32 {
	if m != nil {
		return m.CreatorInfoType
	}
	return 0
}

func (m *UpdateAIRoleReq_Role) GetAppointUid() uint32 {
	if m != nil {
		return m.AppointUid
	}
	return 0
}

func (m *UpdateAIRoleReq_Role) GetUserRoleSetting() string {
	if m != nil {
		return m.UserRoleSetting
	}
	return ""
}

func (m *UpdateAIRoleReq_Role) GetRelationIds() []uint32 {
	if m != nil {
		return m.RelationIds
	}
	return nil
}

func (m *UpdateAIRoleReq_Role) GetScope() AIRoleScope {
	if m != nil {
		return m.Scope
	}
	return AIRoleScope_AI_ROLE_SCOPE_UNSPECIFIED
}

type UpdateAIRoleResp struct {
	Role                 *AIRole  `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAIRoleResp) Reset()         { *m = UpdateAIRoleResp{} }
func (m *UpdateAIRoleResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAIRoleResp) ProtoMessage()    {}
func (*UpdateAIRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{13}
}
func (m *UpdateAIRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIRoleResp.Unmarshal(m, b)
}
func (m *UpdateAIRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIRoleResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAIRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIRoleResp.Merge(dst, src)
}
func (m *UpdateAIRoleResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAIRoleResp.Size(m)
}
func (m *UpdateAIRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIRoleResp proto.InternalMessageInfo

func (m *UpdateAIRoleResp) GetRole() *AIRole {
	if m != nil {
		return m.Role
	}
	return nil
}

type DeleteAIRoleReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAIRoleReq) Reset()         { *m = DeleteAIRoleReq{} }
func (m *DeleteAIRoleReq) String() string { return proto.CompactTextString(m) }
func (*DeleteAIRoleReq) ProtoMessage()    {}
func (*DeleteAIRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{14}
}
func (m *DeleteAIRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAIRoleReq.Unmarshal(m, b)
}
func (m *DeleteAIRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAIRoleReq.Marshal(b, m, deterministic)
}
func (dst *DeleteAIRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAIRoleReq.Merge(dst, src)
}
func (m *DeleteAIRoleReq) XXX_Size() int {
	return xxx_messageInfo_DeleteAIRoleReq.Size(m)
}
func (m *DeleteAIRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAIRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAIRoleReq proto.InternalMessageInfo

func (m *DeleteAIRoleReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteAIRoleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAIRoleResp) Reset()         { *m = DeleteAIRoleResp{} }
func (m *DeleteAIRoleResp) String() string { return proto.CompactTextString(m) }
func (*DeleteAIRoleResp) ProtoMessage()    {}
func (*DeleteAIRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{15}
}
func (m *DeleteAIRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAIRoleResp.Unmarshal(m, b)
}
func (m *DeleteAIRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAIRoleResp.Marshal(b, m, deterministic)
}
func (dst *DeleteAIRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAIRoleResp.Merge(dst, src)
}
func (m *DeleteAIRoleResp) XXX_Size() int {
	return xxx_messageInfo_DeleteAIRoleResp.Size(m)
}
func (m *DeleteAIRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAIRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAIRoleResp proto.InternalMessageInfo

type GetAIRoleReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAIRoleReq) Reset()         { *m = GetAIRoleReq{} }
func (m *GetAIRoleReq) String() string { return proto.CompactTextString(m) }
func (*GetAIRoleReq) ProtoMessage()    {}
func (*GetAIRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{16}
}
func (m *GetAIRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIRoleReq.Unmarshal(m, b)
}
func (m *GetAIRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIRoleReq.Marshal(b, m, deterministic)
}
func (dst *GetAIRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIRoleReq.Merge(dst, src)
}
func (m *GetAIRoleReq) XXX_Size() int {
	return xxx_messageInfo_GetAIRoleReq.Size(m)
}
func (m *GetAIRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIRoleReq proto.InternalMessageInfo

func (m *GetAIRoleReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetAIRoleResp struct {
	Role                 *AIRole  `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAIRoleResp) Reset()         { *m = GetAIRoleResp{} }
func (m *GetAIRoleResp) String() string { return proto.CompactTextString(m) }
func (*GetAIRoleResp) ProtoMessage()    {}
func (*GetAIRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{17}
}
func (m *GetAIRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIRoleResp.Unmarshal(m, b)
}
func (m *GetAIRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIRoleResp.Marshal(b, m, deterministic)
}
func (dst *GetAIRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIRoleResp.Merge(dst, src)
}
func (m *GetAIRoleResp) XXX_Size() int {
	return xxx_messageInfo_GetAIRoleResp.Size(m)
}
func (m *GetAIRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIRoleResp proto.InternalMessageInfo

func (m *GetAIRoleResp) GetRole() *AIRole {
	if m != nil {
		return m.Role
	}
	return nil
}

type GetAIRoleListReq struct {
	RoleIdList           []uint32 `protobuf:"varint,2,rep,packed,name=role_id_list,json=roleIdList,proto3" json:"role_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAIRoleListReq) Reset()         { *m = GetAIRoleListReq{} }
func (m *GetAIRoleListReq) String() string { return proto.CompactTextString(m) }
func (*GetAIRoleListReq) ProtoMessage()    {}
func (*GetAIRoleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{18}
}
func (m *GetAIRoleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIRoleListReq.Unmarshal(m, b)
}
func (m *GetAIRoleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIRoleListReq.Marshal(b, m, deterministic)
}
func (dst *GetAIRoleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIRoleListReq.Merge(dst, src)
}
func (m *GetAIRoleListReq) XXX_Size() int {
	return xxx_messageInfo_GetAIRoleListReq.Size(m)
}
func (m *GetAIRoleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIRoleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIRoleListReq proto.InternalMessageInfo

func (m *GetAIRoleListReq) GetRoleIdList() []uint32 {
	if m != nil {
		return m.RoleIdList
	}
	return nil
}

type GetAIRoleListResp struct {
	RoleList             []*AIRole `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetAIRoleListResp) Reset()         { *m = GetAIRoleListResp{} }
func (m *GetAIRoleListResp) String() string { return proto.CompactTextString(m) }
func (*GetAIRoleListResp) ProtoMessage()    {}
func (*GetAIRoleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{19}
}
func (m *GetAIRoleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIRoleListResp.Unmarshal(m, b)
}
func (m *GetAIRoleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIRoleListResp.Marshal(b, m, deterministic)
}
func (dst *GetAIRoleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIRoleListResp.Merge(dst, src)
}
func (m *GetAIRoleListResp) XXX_Size() int {
	return xxx_messageInfo_GetAIRoleListResp.Size(m)
}
func (m *GetAIRoleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIRoleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIRoleListResp proto.InternalMessageInfo

func (m *GetAIRoleListResp) GetRoleList() []*AIRole {
	if m != nil {
		return m.RoleList
	}
	return nil
}

type GetUserAIRoleListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAIRoleListReq) Reset()         { *m = GetUserAIRoleListReq{} }
func (m *GetUserAIRoleListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAIRoleListReq) ProtoMessage()    {}
func (*GetUserAIRoleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{20}
}
func (m *GetUserAIRoleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIRoleListReq.Unmarshal(m, b)
}
func (m *GetUserAIRoleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIRoleListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAIRoleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIRoleListReq.Merge(dst, src)
}
func (m *GetUserAIRoleListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAIRoleListReq.Size(m)
}
func (m *GetUserAIRoleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIRoleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIRoleListReq proto.InternalMessageInfo

func (m *GetUserAIRoleListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserAIRoleListResp struct {
	RoleList             []*AIRole `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetUserAIRoleListResp) Reset()         { *m = GetUserAIRoleListResp{} }
func (m *GetUserAIRoleListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAIRoleListResp) ProtoMessage()    {}
func (*GetUserAIRoleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{21}
}
func (m *GetUserAIRoleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIRoleListResp.Unmarshal(m, b)
}
func (m *GetUserAIRoleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIRoleListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserAIRoleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIRoleListResp.Merge(dst, src)
}
func (m *GetUserAIRoleListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserAIRoleListResp.Size(m)
}
func (m *GetUserAIRoleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIRoleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIRoleListResp proto.InternalMessageInfo

func (m *GetUserAIRoleListResp) GetRoleList() []*AIRole {
	if m != nil {
		return m.RoleList
	}
	return nil
}

type GetUserAIRoleListWithAppointReq struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleType             AIRoleType `protobuf:"varint,2,opt,name=role_type,json=roleType,proto3,enum=aigc_soulmate.AIRoleType" json:"role_type,omitempty"`
	Limit                uint32     `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetUserAIRoleListWithAppointReq) Reset()         { *m = GetUserAIRoleListWithAppointReq{} }
func (m *GetUserAIRoleListWithAppointReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAIRoleListWithAppointReq) ProtoMessage()    {}
func (*GetUserAIRoleListWithAppointReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{22}
}
func (m *GetUserAIRoleListWithAppointReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIRoleListWithAppointReq.Unmarshal(m, b)
}
func (m *GetUserAIRoleListWithAppointReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIRoleListWithAppointReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAIRoleListWithAppointReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIRoleListWithAppointReq.Merge(dst, src)
}
func (m *GetUserAIRoleListWithAppointReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAIRoleListWithAppointReq.Size(m)
}
func (m *GetUserAIRoleListWithAppointReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIRoleListWithAppointReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIRoleListWithAppointReq proto.InternalMessageInfo

func (m *GetUserAIRoleListWithAppointReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserAIRoleListWithAppointReq) GetRoleType() AIRoleType {
	if m != nil {
		return m.RoleType
	}
	return AIRoleType_AIRoleTypePartner
}

func (m *GetUserAIRoleListWithAppointReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetUserAIRoleListWithAppointResp struct {
	RoleList             []*AIRole `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetUserAIRoleListWithAppointResp) Reset()         { *m = GetUserAIRoleListWithAppointResp{} }
func (m *GetUserAIRoleListWithAppointResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAIRoleListWithAppointResp) ProtoMessage()    {}
func (*GetUserAIRoleListWithAppointResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{23}
}
func (m *GetUserAIRoleListWithAppointResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIRoleListWithAppointResp.Unmarshal(m, b)
}
func (m *GetUserAIRoleListWithAppointResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIRoleListWithAppointResp.Marshal(b, m, deterministic)
}
func (dst *GetUserAIRoleListWithAppointResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIRoleListWithAppointResp.Merge(dst, src)
}
func (m *GetUserAIRoleListWithAppointResp) XXX_Size() int {
	return xxx_messageInfo_GetUserAIRoleListWithAppointResp.Size(m)
}
func (m *GetUserAIRoleListWithAppointResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIRoleListWithAppointResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIRoleListWithAppointResp proto.InternalMessageInfo

func (m *GetUserAIRoleListWithAppointResp) GetRoleList() []*AIRole {
	if m != nil {
		return m.RoleList
	}
	return nil
}

type GetOfficialAIRoleListReq struct {
	CategoryId           string     `protobuf:"bytes,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	RoleType             AIRoleType `protobuf:"varint,2,opt,name=role_type,json=roleType,proto3,enum=aigc_soulmate.AIRoleType" json:"role_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetOfficialAIRoleListReq) Reset()         { *m = GetOfficialAIRoleListReq{} }
func (m *GetOfficialAIRoleListReq) String() string { return proto.CompactTextString(m) }
func (*GetOfficialAIRoleListReq) ProtoMessage()    {}
func (*GetOfficialAIRoleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{24}
}
func (m *GetOfficialAIRoleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOfficialAIRoleListReq.Unmarshal(m, b)
}
func (m *GetOfficialAIRoleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOfficialAIRoleListReq.Marshal(b, m, deterministic)
}
func (dst *GetOfficialAIRoleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOfficialAIRoleListReq.Merge(dst, src)
}
func (m *GetOfficialAIRoleListReq) XXX_Size() int {
	return xxx_messageInfo_GetOfficialAIRoleListReq.Size(m)
}
func (m *GetOfficialAIRoleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOfficialAIRoleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOfficialAIRoleListReq proto.InternalMessageInfo

func (m *GetOfficialAIRoleListReq) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *GetOfficialAIRoleListReq) GetRoleType() AIRoleType {
	if m != nil {
		return m.RoleType
	}
	return AIRoleType_AIRoleTypePartner
}

type GetOfficialAIRoleListResp struct {
	RoleList             []*AIRole `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetOfficialAIRoleListResp) Reset()         { *m = GetOfficialAIRoleListResp{} }
func (m *GetOfficialAIRoleListResp) String() string { return proto.CompactTextString(m) }
func (*GetOfficialAIRoleListResp) ProtoMessage()    {}
func (*GetOfficialAIRoleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{25}
}
func (m *GetOfficialAIRoleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOfficialAIRoleListResp.Unmarshal(m, b)
}
func (m *GetOfficialAIRoleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOfficialAIRoleListResp.Marshal(b, m, deterministic)
}
func (dst *GetOfficialAIRoleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOfficialAIRoleListResp.Merge(dst, src)
}
func (m *GetOfficialAIRoleListResp) XXX_Size() int {
	return xxx_messageInfo_GetOfficialAIRoleListResp.Size(m)
}
func (m *GetOfficialAIRoleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOfficialAIRoleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOfficialAIRoleListResp proto.InternalMessageInfo

func (m *GetOfficialAIRoleListResp) GetRoleList() []*AIRole {
	if m != nil {
		return m.RoleList
	}
	return nil
}

type GetBannerRoleListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBannerRoleListReq) Reset()         { *m = GetBannerRoleListReq{} }
func (m *GetBannerRoleListReq) String() string { return proto.CompactTextString(m) }
func (*GetBannerRoleListReq) ProtoMessage()    {}
func (*GetBannerRoleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{26}
}
func (m *GetBannerRoleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannerRoleListReq.Unmarshal(m, b)
}
func (m *GetBannerRoleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannerRoleListReq.Marshal(b, m, deterministic)
}
func (dst *GetBannerRoleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannerRoleListReq.Merge(dst, src)
}
func (m *GetBannerRoleListReq) XXX_Size() int {
	return xxx_messageInfo_GetBannerRoleListReq.Size(m)
}
func (m *GetBannerRoleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannerRoleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannerRoleListReq proto.InternalMessageInfo

type GetBannerRoleListResp struct {
	BannerRoleList       []*AIRole `protobuf:"bytes,1,rep,name=banner_role_list,json=bannerRoleList,proto3" json:"banner_role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetBannerRoleListResp) Reset()         { *m = GetBannerRoleListResp{} }
func (m *GetBannerRoleListResp) String() string { return proto.CompactTextString(m) }
func (*GetBannerRoleListResp) ProtoMessage()    {}
func (*GetBannerRoleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{27}
}
func (m *GetBannerRoleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannerRoleListResp.Unmarshal(m, b)
}
func (m *GetBannerRoleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannerRoleListResp.Marshal(b, m, deterministic)
}
func (dst *GetBannerRoleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannerRoleListResp.Merge(dst, src)
}
func (m *GetBannerRoleListResp) XXX_Size() int {
	return xxx_messageInfo_GetBannerRoleListResp.Size(m)
}
func (m *GetBannerRoleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannerRoleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannerRoleListResp proto.InternalMessageInfo

func (m *GetBannerRoleListResp) GetBannerRoleList() []*AIRole {
	if m != nil {
		return m.BannerRoleList
	}
	return nil
}

type SetBannerRoleListReq struct {
	RoleIds              []uint32 `protobuf:"varint,1,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetBannerRoleListReq) Reset()         { *m = SetBannerRoleListReq{} }
func (m *SetBannerRoleListReq) String() string { return proto.CompactTextString(m) }
func (*SetBannerRoleListReq) ProtoMessage()    {}
func (*SetBannerRoleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{28}
}
func (m *SetBannerRoleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBannerRoleListReq.Unmarshal(m, b)
}
func (m *SetBannerRoleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBannerRoleListReq.Marshal(b, m, deterministic)
}
func (dst *SetBannerRoleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBannerRoleListReq.Merge(dst, src)
}
func (m *SetBannerRoleListReq) XXX_Size() int {
	return xxx_messageInfo_SetBannerRoleListReq.Size(m)
}
func (m *SetBannerRoleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBannerRoleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetBannerRoleListReq proto.InternalMessageInfo

func (m *SetBannerRoleListReq) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

type SetBannerRoleListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetBannerRoleListResp) Reset()         { *m = SetBannerRoleListResp{} }
func (m *SetBannerRoleListResp) String() string { return proto.CompactTextString(m) }
func (*SetBannerRoleListResp) ProtoMessage()    {}
func (*SetBannerRoleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{29}
}
func (m *SetBannerRoleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBannerRoleListResp.Unmarshal(m, b)
}
func (m *SetBannerRoleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBannerRoleListResp.Marshal(b, m, deterministic)
}
func (dst *SetBannerRoleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBannerRoleListResp.Merge(dst, src)
}
func (m *SetBannerRoleListResp) XXX_Size() int {
	return xxx_messageInfo_SetBannerRoleListResp.Size(m)
}
func (m *SetBannerRoleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBannerRoleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetBannerRoleListResp proto.InternalMessageInfo

type CreateAIPartnerReq struct {
	Partner              *CreateAIPartnerReq_Partner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *CreateAIPartnerReq) Reset()         { *m = CreateAIPartnerReq{} }
func (m *CreateAIPartnerReq) String() string { return proto.CompactTextString(m) }
func (*CreateAIPartnerReq) ProtoMessage()    {}
func (*CreateAIPartnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{30}
}
func (m *CreateAIPartnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIPartnerReq.Unmarshal(m, b)
}
func (m *CreateAIPartnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIPartnerReq.Marshal(b, m, deterministic)
}
func (dst *CreateAIPartnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIPartnerReq.Merge(dst, src)
}
func (m *CreateAIPartnerReq) XXX_Size() int {
	return xxx_messageInfo_CreateAIPartnerReq.Size(m)
}
func (m *CreateAIPartnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIPartnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIPartnerReq proto.InternalMessageInfo

func (m *CreateAIPartnerReq) GetPartner() *CreateAIPartnerReq_Partner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type CreateAIPartnerReq_Partner struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ta的名字
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 你希望ta怎么称呼你
	CallName string `protobuf:"bytes,3,opt,name=call_name,json=callName,proto3" json:"call_name,omitempty"`
	// AI伴侣绑定的AI角色
	RoleId uint32 `protobuf:"varint,4,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// AI伴侣来源
	Source               AIPartnerSource `protobuf:"varint,6,opt,name=source,proto3,enum=aigc_soulmate.AIPartnerSource" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CreateAIPartnerReq_Partner) Reset()         { *m = CreateAIPartnerReq_Partner{} }
func (m *CreateAIPartnerReq_Partner) String() string { return proto.CompactTextString(m) }
func (*CreateAIPartnerReq_Partner) ProtoMessage()    {}
func (*CreateAIPartnerReq_Partner) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{30, 0}
}
func (m *CreateAIPartnerReq_Partner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIPartnerReq_Partner.Unmarshal(m, b)
}
func (m *CreateAIPartnerReq_Partner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIPartnerReq_Partner.Marshal(b, m, deterministic)
}
func (dst *CreateAIPartnerReq_Partner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIPartnerReq_Partner.Merge(dst, src)
}
func (m *CreateAIPartnerReq_Partner) XXX_Size() int {
	return xxx_messageInfo_CreateAIPartnerReq_Partner.Size(m)
}
func (m *CreateAIPartnerReq_Partner) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIPartnerReq_Partner.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIPartnerReq_Partner proto.InternalMessageInfo

func (m *CreateAIPartnerReq_Partner) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CreateAIPartnerReq_Partner) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateAIPartnerReq_Partner) GetCallName() string {
	if m != nil {
		return m.CallName
	}
	return ""
}

func (m *CreateAIPartnerReq_Partner) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *CreateAIPartnerReq_Partner) GetSource() AIPartnerSource {
	if m != nil {
		return m.Source
	}
	return AIPartnerSource_AIPartnerSourceUser
}

type CreateAIPartnerResp struct {
	Partner              *AIPartner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CreateAIPartnerResp) Reset()         { *m = CreateAIPartnerResp{} }
func (m *CreateAIPartnerResp) String() string { return proto.CompactTextString(m) }
func (*CreateAIPartnerResp) ProtoMessage()    {}
func (*CreateAIPartnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{31}
}
func (m *CreateAIPartnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIPartnerResp.Unmarshal(m, b)
}
func (m *CreateAIPartnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIPartnerResp.Marshal(b, m, deterministic)
}
func (dst *CreateAIPartnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIPartnerResp.Merge(dst, src)
}
func (m *CreateAIPartnerResp) XXX_Size() int {
	return xxx_messageInfo_CreateAIPartnerResp.Size(m)
}
func (m *CreateAIPartnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIPartnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIPartnerResp proto.InternalMessageInfo

func (m *CreateAIPartnerResp) GetPartner() *AIPartner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type UpdateAIPartnerReq struct {
	Partner              *UpdateAIPartnerReq_Partner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *UpdateAIPartnerReq) Reset()         { *m = UpdateAIPartnerReq{} }
func (m *UpdateAIPartnerReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAIPartnerReq) ProtoMessage()    {}
func (*UpdateAIPartnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{32}
}
func (m *UpdateAIPartnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIPartnerReq.Unmarshal(m, b)
}
func (m *UpdateAIPartnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIPartnerReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAIPartnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIPartnerReq.Merge(dst, src)
}
func (m *UpdateAIPartnerReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAIPartnerReq.Size(m)
}
func (m *UpdateAIPartnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIPartnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIPartnerReq proto.InternalMessageInfo

func (m *UpdateAIPartnerReq) GetPartner() *UpdateAIPartnerReq_Partner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type UpdateAIPartnerReq_Partner struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ta的名字
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 你希望ta怎么称呼你
	CallName             string   `protobuf:"bytes,3,opt,name=call_name,json=callName,proto3" json:"call_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAIPartnerReq_Partner) Reset()         { *m = UpdateAIPartnerReq_Partner{} }
func (m *UpdateAIPartnerReq_Partner) String() string { return proto.CompactTextString(m) }
func (*UpdateAIPartnerReq_Partner) ProtoMessage()    {}
func (*UpdateAIPartnerReq_Partner) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{32, 0}
}
func (m *UpdateAIPartnerReq_Partner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIPartnerReq_Partner.Unmarshal(m, b)
}
func (m *UpdateAIPartnerReq_Partner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIPartnerReq_Partner.Marshal(b, m, deterministic)
}
func (dst *UpdateAIPartnerReq_Partner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIPartnerReq_Partner.Merge(dst, src)
}
func (m *UpdateAIPartnerReq_Partner) XXX_Size() int {
	return xxx_messageInfo_UpdateAIPartnerReq_Partner.Size(m)
}
func (m *UpdateAIPartnerReq_Partner) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIPartnerReq_Partner.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIPartnerReq_Partner proto.InternalMessageInfo

func (m *UpdateAIPartnerReq_Partner) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateAIPartnerReq_Partner) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpdateAIPartnerReq_Partner) GetCallName() string {
	if m != nil {
		return m.CallName
	}
	return ""
}

type UpdateAIPartnerResp struct {
	Partner              *AIPartner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UpdateAIPartnerResp) Reset()         { *m = UpdateAIPartnerResp{} }
func (m *UpdateAIPartnerResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAIPartnerResp) ProtoMessage()    {}
func (*UpdateAIPartnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{33}
}
func (m *UpdateAIPartnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIPartnerResp.Unmarshal(m, b)
}
func (m *UpdateAIPartnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIPartnerResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAIPartnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIPartnerResp.Merge(dst, src)
}
func (m *UpdateAIPartnerResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAIPartnerResp.Size(m)
}
func (m *UpdateAIPartnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIPartnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIPartnerResp proto.InternalMessageInfo

func (m *UpdateAIPartnerResp) GetPartner() *AIPartner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type DeleteAIPartnerReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAIPartnerReq) Reset()         { *m = DeleteAIPartnerReq{} }
func (m *DeleteAIPartnerReq) String() string { return proto.CompactTextString(m) }
func (*DeleteAIPartnerReq) ProtoMessage()    {}
func (*DeleteAIPartnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{34}
}
func (m *DeleteAIPartnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAIPartnerReq.Unmarshal(m, b)
}
func (m *DeleteAIPartnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAIPartnerReq.Marshal(b, m, deterministic)
}
func (dst *DeleteAIPartnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAIPartnerReq.Merge(dst, src)
}
func (m *DeleteAIPartnerReq) XXX_Size() int {
	return xxx_messageInfo_DeleteAIPartnerReq.Size(m)
}
func (m *DeleteAIPartnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAIPartnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAIPartnerReq proto.InternalMessageInfo

func (m *DeleteAIPartnerReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteAIPartnerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAIPartnerResp) Reset()         { *m = DeleteAIPartnerResp{} }
func (m *DeleteAIPartnerResp) String() string { return proto.CompactTextString(m) }
func (*DeleteAIPartnerResp) ProtoMessage()    {}
func (*DeleteAIPartnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{35}
}
func (m *DeleteAIPartnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAIPartnerResp.Unmarshal(m, b)
}
func (m *DeleteAIPartnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAIPartnerResp.Marshal(b, m, deterministic)
}
func (dst *DeleteAIPartnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAIPartnerResp.Merge(dst, src)
}
func (m *DeleteAIPartnerResp) XXX_Size() int {
	return xxx_messageInfo_DeleteAIPartnerResp.Size(m)
}
func (m *DeleteAIPartnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAIPartnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAIPartnerResp proto.InternalMessageInfo

type ChangeAIPartnerRoleReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeAIPartnerRoleReq) Reset()         { *m = ChangeAIPartnerRoleReq{} }
func (m *ChangeAIPartnerRoleReq) String() string { return proto.CompactTextString(m) }
func (*ChangeAIPartnerRoleReq) ProtoMessage()    {}
func (*ChangeAIPartnerRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{36}
}
func (m *ChangeAIPartnerRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeAIPartnerRoleReq.Unmarshal(m, b)
}
func (m *ChangeAIPartnerRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeAIPartnerRoleReq.Marshal(b, m, deterministic)
}
func (dst *ChangeAIPartnerRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeAIPartnerRoleReq.Merge(dst, src)
}
func (m *ChangeAIPartnerRoleReq) XXX_Size() int {
	return xxx_messageInfo_ChangeAIPartnerRoleReq.Size(m)
}
func (m *ChangeAIPartnerRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeAIPartnerRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeAIPartnerRoleReq proto.InternalMessageInfo

func (m *ChangeAIPartnerRoleReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChangeAIPartnerRoleReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type ChangeAIPartnerRoleResp struct {
	Partner              *AIPartner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ChangeAIPartnerRoleResp) Reset()         { *m = ChangeAIPartnerRoleResp{} }
func (m *ChangeAIPartnerRoleResp) String() string { return proto.CompactTextString(m) }
func (*ChangeAIPartnerRoleResp) ProtoMessage()    {}
func (*ChangeAIPartnerRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{37}
}
func (m *ChangeAIPartnerRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeAIPartnerRoleResp.Unmarshal(m, b)
}
func (m *ChangeAIPartnerRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeAIPartnerRoleResp.Marshal(b, m, deterministic)
}
func (dst *ChangeAIPartnerRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeAIPartnerRoleResp.Merge(dst, src)
}
func (m *ChangeAIPartnerRoleResp) XXX_Size() int {
	return xxx_messageInfo_ChangeAIPartnerRoleResp.Size(m)
}
func (m *ChangeAIPartnerRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeAIPartnerRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeAIPartnerRoleResp proto.InternalMessageInfo

func (m *ChangeAIPartnerRoleResp) GetPartner() *AIPartner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type UpdateAIPartnerChatStateReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Silent               bool     `protobuf:"varint,2,opt,name=silent,proto3" json:"silent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAIPartnerChatStateReq) Reset()         { *m = UpdateAIPartnerChatStateReq{} }
func (m *UpdateAIPartnerChatStateReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAIPartnerChatStateReq) ProtoMessage()    {}
func (*UpdateAIPartnerChatStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{38}
}
func (m *UpdateAIPartnerChatStateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIPartnerChatStateReq.Unmarshal(m, b)
}
func (m *UpdateAIPartnerChatStateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIPartnerChatStateReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAIPartnerChatStateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIPartnerChatStateReq.Merge(dst, src)
}
func (m *UpdateAIPartnerChatStateReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAIPartnerChatStateReq.Size(m)
}
func (m *UpdateAIPartnerChatStateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIPartnerChatStateReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIPartnerChatStateReq proto.InternalMessageInfo

func (m *UpdateAIPartnerChatStateReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateAIPartnerChatStateReq) GetSilent() bool {
	if m != nil {
		return m.Silent
	}
	return false
}

type UpdateAIPartnerChatStateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAIPartnerChatStateResp) Reset()         { *m = UpdateAIPartnerChatStateResp{} }
func (m *UpdateAIPartnerChatStateResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAIPartnerChatStateResp) ProtoMessage()    {}
func (*UpdateAIPartnerChatStateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{39}
}
func (m *UpdateAIPartnerChatStateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIPartnerChatStateResp.Unmarshal(m, b)
}
func (m *UpdateAIPartnerChatStateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIPartnerChatStateResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAIPartnerChatStateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIPartnerChatStateResp.Merge(dst, src)
}
func (m *UpdateAIPartnerChatStateResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAIPartnerChatStateResp.Size(m)
}
func (m *UpdateAIPartnerChatStateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIPartnerChatStateResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIPartnerChatStateResp proto.InternalMessageInfo

type GetAIPartnerReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAIPartnerReq) Reset()         { *m = GetAIPartnerReq{} }
func (m *GetAIPartnerReq) String() string { return proto.CompactTextString(m) }
func (*GetAIPartnerReq) ProtoMessage()    {}
func (*GetAIPartnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{40}
}
func (m *GetAIPartnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPartnerReq.Unmarshal(m, b)
}
func (m *GetAIPartnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPartnerReq.Marshal(b, m, deterministic)
}
func (dst *GetAIPartnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPartnerReq.Merge(dst, src)
}
func (m *GetAIPartnerReq) XXX_Size() int {
	return xxx_messageInfo_GetAIPartnerReq.Size(m)
}
func (m *GetAIPartnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPartnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPartnerReq proto.InternalMessageInfo

func (m *GetAIPartnerReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetAIPartnerResp struct {
	Partner              *AIPartner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAIPartnerResp) Reset()         { *m = GetAIPartnerResp{} }
func (m *GetAIPartnerResp) String() string { return proto.CompactTextString(m) }
func (*GetAIPartnerResp) ProtoMessage()    {}
func (*GetAIPartnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{41}
}
func (m *GetAIPartnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPartnerResp.Unmarshal(m, b)
}
func (m *GetAIPartnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPartnerResp.Marshal(b, m, deterministic)
}
func (dst *GetAIPartnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPartnerResp.Merge(dst, src)
}
func (m *GetAIPartnerResp) XXX_Size() int {
	return xxx_messageInfo_GetAIPartnerResp.Size(m)
}
func (m *GetAIPartnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPartnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPartnerResp proto.InternalMessageInfo

func (m *GetAIPartnerResp) GetPartner() *AIPartner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type BatchGetAIPartnerReq struct {
	Ids                  []uint32 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAIPartnerReq) Reset()         { *m = BatchGetAIPartnerReq{} }
func (m *BatchGetAIPartnerReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetAIPartnerReq) ProtoMessage()    {}
func (*BatchGetAIPartnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{42}
}
func (m *BatchGetAIPartnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAIPartnerReq.Unmarshal(m, b)
}
func (m *BatchGetAIPartnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAIPartnerReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetAIPartnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAIPartnerReq.Merge(dst, src)
}
func (m *BatchGetAIPartnerReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetAIPartnerReq.Size(m)
}
func (m *BatchGetAIPartnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAIPartnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAIPartnerReq proto.InternalMessageInfo

func (m *BatchGetAIPartnerReq) GetIds() []uint32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type BatchGetAIPartnerResp struct {
	PartnerMap           map[uint32]*AIPartner `protobuf:"bytes,1,rep,name=partner_map,json=partnerMap,proto3" json:"partner_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BatchGetAIPartnerResp) Reset()         { *m = BatchGetAIPartnerResp{} }
func (m *BatchGetAIPartnerResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetAIPartnerResp) ProtoMessage()    {}
func (*BatchGetAIPartnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{43}
}
func (m *BatchGetAIPartnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAIPartnerResp.Unmarshal(m, b)
}
func (m *BatchGetAIPartnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAIPartnerResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetAIPartnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAIPartnerResp.Merge(dst, src)
}
func (m *BatchGetAIPartnerResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetAIPartnerResp.Size(m)
}
func (m *BatchGetAIPartnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAIPartnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAIPartnerResp proto.InternalMessageInfo

func (m *BatchGetAIPartnerResp) GetPartnerMap() map[uint32]*AIPartner {
	if m != nil {
		return m.PartnerMap
	}
	return nil
}

type GetUserAIPartnerReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// role_type和role_id传一个即可
	RoleType             AIRoleType `protobuf:"varint,2,opt,name=role_type,json=roleType,proto3,enum=aigc_soulmate.AIRoleType" json:"role_type,omitempty"`
	RoleId               uint32     `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetUserAIPartnerReq) Reset()         { *m = GetUserAIPartnerReq{} }
func (m *GetUserAIPartnerReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAIPartnerReq) ProtoMessage()    {}
func (*GetUserAIPartnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{44}
}
func (m *GetUserAIPartnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIPartnerReq.Unmarshal(m, b)
}
func (m *GetUserAIPartnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIPartnerReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAIPartnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIPartnerReq.Merge(dst, src)
}
func (m *GetUserAIPartnerReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAIPartnerReq.Size(m)
}
func (m *GetUserAIPartnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIPartnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIPartnerReq proto.InternalMessageInfo

func (m *GetUserAIPartnerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserAIPartnerReq) GetRoleType() AIRoleType {
	if m != nil {
		return m.RoleType
	}
	return AIRoleType_AIRoleTypePartner
}

func (m *GetUserAIPartnerReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetUserAIPartnerResp struct {
	Partner              *AIPartner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetUserAIPartnerResp) Reset()         { *m = GetUserAIPartnerResp{} }
func (m *GetUserAIPartnerResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAIPartnerResp) ProtoMessage()    {}
func (*GetUserAIPartnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{45}
}
func (m *GetUserAIPartnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIPartnerResp.Unmarshal(m, b)
}
func (m *GetUserAIPartnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIPartnerResp.Marshal(b, m, deterministic)
}
func (dst *GetUserAIPartnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIPartnerResp.Merge(dst, src)
}
func (m *GetUserAIPartnerResp) XXX_Size() int {
	return xxx_messageInfo_GetUserAIPartnerResp.Size(m)
}
func (m *GetUserAIPartnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIPartnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIPartnerResp proto.InternalMessageInfo

func (m *GetUserAIPartnerResp) GetPartner() *AIPartner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type GetUserAIPartnerListReq struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleTypes            []AIRoleType `protobuf:"varint,2,rep,packed,name=role_types,json=roleTypes,proto3,enum=aigc_soulmate.AIRoleType" json:"role_types,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserAIPartnerListReq) Reset()         { *m = GetUserAIPartnerListReq{} }
func (m *GetUserAIPartnerListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAIPartnerListReq) ProtoMessage()    {}
func (*GetUserAIPartnerListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{46}
}
func (m *GetUserAIPartnerListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIPartnerListReq.Unmarshal(m, b)
}
func (m *GetUserAIPartnerListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIPartnerListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAIPartnerListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIPartnerListReq.Merge(dst, src)
}
func (m *GetUserAIPartnerListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAIPartnerListReq.Size(m)
}
func (m *GetUserAIPartnerListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIPartnerListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIPartnerListReq proto.InternalMessageInfo

func (m *GetUserAIPartnerListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserAIPartnerListReq) GetRoleTypes() []AIRoleType {
	if m != nil {
		return m.RoleTypes
	}
	return nil
}

type GetUserAIPartnerListResp struct {
	PartnerList          []*AIPartner `protobuf:"bytes,1,rep,name=partner_list,json=partnerList,proto3" json:"partner_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserAIPartnerListResp) Reset()         { *m = GetUserAIPartnerListResp{} }
func (m *GetUserAIPartnerListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAIPartnerListResp) ProtoMessage()    {}
func (*GetUserAIPartnerListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{47}
}
func (m *GetUserAIPartnerListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIPartnerListResp.Unmarshal(m, b)
}
func (m *GetUserAIPartnerListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIPartnerListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserAIPartnerListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIPartnerListResp.Merge(dst, src)
}
func (m *GetUserAIPartnerListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserAIPartnerListResp.Size(m)
}
func (m *GetUserAIPartnerListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIPartnerListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIPartnerListResp proto.InternalMessageInfo

func (m *GetUserAIPartnerListResp) GetPartnerList() []*AIPartner {
	if m != nil {
		return m.PartnerList
	}
	return nil
}

type UpsertAIRoleCategoryReq struct {
	Op                   UpsertAIRoleCategoryReq_Op        `protobuf:"varint,1,opt,name=op,proto3,enum=aigc_soulmate.UpsertAIRoleCategoryReq_Op" json:"op,omitempty"`
	Category             *UpsertAIRoleCategoryReq_Category `protobuf:"bytes,2,opt,name=category,proto3" json:"category,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *UpsertAIRoleCategoryReq) Reset()         { *m = UpsertAIRoleCategoryReq{} }
func (m *UpsertAIRoleCategoryReq) String() string { return proto.CompactTextString(m) }
func (*UpsertAIRoleCategoryReq) ProtoMessage()    {}
func (*UpsertAIRoleCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{48}
}
func (m *UpsertAIRoleCategoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertAIRoleCategoryReq.Unmarshal(m, b)
}
func (m *UpsertAIRoleCategoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertAIRoleCategoryReq.Marshal(b, m, deterministic)
}
func (dst *UpsertAIRoleCategoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertAIRoleCategoryReq.Merge(dst, src)
}
func (m *UpsertAIRoleCategoryReq) XXX_Size() int {
	return xxx_messageInfo_UpsertAIRoleCategoryReq.Size(m)
}
func (m *UpsertAIRoleCategoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertAIRoleCategoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertAIRoleCategoryReq proto.InternalMessageInfo

func (m *UpsertAIRoleCategoryReq) GetOp() UpsertAIRoleCategoryReq_Op {
	if m != nil {
		return m.Op
	}
	return UpsertAIRoleCategoryReq_OpUnspecified
}

func (m *UpsertAIRoleCategoryReq) GetCategory() *UpsertAIRoleCategoryReq_Category {
	if m != nil {
		return m.Category
	}
	return nil
}

type UpsertAIRoleCategoryReq_Label struct {
	Op                   UpsertAIRoleCategoryReq_Op `protobuf:"varint,1,opt,name=op,proto3,enum=aigc_soulmate.UpsertAIRoleCategoryReq_Op" json:"op,omitempty"`
	Id                   string                     `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                     `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Scenes               []AIRoleCategory_Scene     `protobuf:"varint,4,rep,packed,name=scenes,proto3,enum=aigc_soulmate.AIRoleCategory_Scene" json:"scenes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *UpsertAIRoleCategoryReq_Label) Reset()         { *m = UpsertAIRoleCategoryReq_Label{} }
func (m *UpsertAIRoleCategoryReq_Label) String() string { return proto.CompactTextString(m) }
func (*UpsertAIRoleCategoryReq_Label) ProtoMessage()    {}
func (*UpsertAIRoleCategoryReq_Label) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{48, 0}
}
func (m *UpsertAIRoleCategoryReq_Label) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertAIRoleCategoryReq_Label.Unmarshal(m, b)
}
func (m *UpsertAIRoleCategoryReq_Label) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertAIRoleCategoryReq_Label.Marshal(b, m, deterministic)
}
func (dst *UpsertAIRoleCategoryReq_Label) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertAIRoleCategoryReq_Label.Merge(dst, src)
}
func (m *UpsertAIRoleCategoryReq_Label) XXX_Size() int {
	return xxx_messageInfo_UpsertAIRoleCategoryReq_Label.Size(m)
}
func (m *UpsertAIRoleCategoryReq_Label) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertAIRoleCategoryReq_Label.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertAIRoleCategoryReq_Label proto.InternalMessageInfo

func (m *UpsertAIRoleCategoryReq_Label) GetOp() UpsertAIRoleCategoryReq_Op {
	if m != nil {
		return m.Op
	}
	return UpsertAIRoleCategoryReq_OpUnspecified
}

func (m *UpsertAIRoleCategoryReq_Label) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpsertAIRoleCategoryReq_Label) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpsertAIRoleCategoryReq_Label) GetScenes() []AIRoleCategory_Scene {
	if m != nil {
		return m.Scenes
	}
	return nil
}

type UpsertAIRoleCategoryReq_Prop struct {
	Op   UpsertAIRoleCategoryReq_Op `protobuf:"varint,1,opt,name=op,proto3,enum=aigc_soulmate.UpsertAIRoleCategoryReq_Op" json:"op,omitempty"`
	Id   string                     `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Name string                     `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 属性类型
	Type AIRoleCategory_PropType `protobuf:"varint,4,opt,name=type,proto3,enum=aigc_soulmate.AIRoleCategory_PropType" json:"type,omitempty"`
	// 属性下的标签
	Labels []*UpsertAIRoleCategoryReq_Label `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty"`
	// 标签选择上限数量
	LabelSelectLimit     uint32   `protobuf:"varint,6,opt,name=label_select_limit,json=labelSelectLimit,proto3" json:"label_select_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertAIRoleCategoryReq_Prop) Reset()         { *m = UpsertAIRoleCategoryReq_Prop{} }
func (m *UpsertAIRoleCategoryReq_Prop) String() string { return proto.CompactTextString(m) }
func (*UpsertAIRoleCategoryReq_Prop) ProtoMessage()    {}
func (*UpsertAIRoleCategoryReq_Prop) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{48, 1}
}
func (m *UpsertAIRoleCategoryReq_Prop) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertAIRoleCategoryReq_Prop.Unmarshal(m, b)
}
func (m *UpsertAIRoleCategoryReq_Prop) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertAIRoleCategoryReq_Prop.Marshal(b, m, deterministic)
}
func (dst *UpsertAIRoleCategoryReq_Prop) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertAIRoleCategoryReq_Prop.Merge(dst, src)
}
func (m *UpsertAIRoleCategoryReq_Prop) XXX_Size() int {
	return xxx_messageInfo_UpsertAIRoleCategoryReq_Prop.Size(m)
}
func (m *UpsertAIRoleCategoryReq_Prop) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertAIRoleCategoryReq_Prop.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertAIRoleCategoryReq_Prop proto.InternalMessageInfo

func (m *UpsertAIRoleCategoryReq_Prop) GetOp() UpsertAIRoleCategoryReq_Op {
	if m != nil {
		return m.Op
	}
	return UpsertAIRoleCategoryReq_OpUnspecified
}

func (m *UpsertAIRoleCategoryReq_Prop) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpsertAIRoleCategoryReq_Prop) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpsertAIRoleCategoryReq_Prop) GetType() AIRoleCategory_PropType {
	if m != nil {
		return m.Type
	}
	return AIRoleCategory_PropTypeUnspecified
}

func (m *UpsertAIRoleCategoryReq_Prop) GetLabels() []*UpsertAIRoleCategoryReq_Label {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *UpsertAIRoleCategoryReq_Prop) GetLabelSelectLimit() uint32 {
	if m != nil {
		return m.LabelSelectLimit
	}
	return 0
}

type UpsertAIRoleCategoryReq_Category struct {
	Id                   string                          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string                          `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Tips                 string                          `protobuf:"bytes,3,opt,name=tips,proto3" json:"tips,omitempty"`
	PropScenes           []AIRoleCategory_Scene          `protobuf:"varint,4,rep,packed,name=prop_scenes,json=propScenes,proto3,enum=aigc_soulmate.AIRoleCategory_Scene" json:"prop_scenes,omitempty"`
	Props                []*UpsertAIRoleCategoryReq_Prop `protobuf:"bytes,5,rep,name=props,proto3" json:"props,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *UpsertAIRoleCategoryReq_Category) Reset()         { *m = UpsertAIRoleCategoryReq_Category{} }
func (m *UpsertAIRoleCategoryReq_Category) String() string { return proto.CompactTextString(m) }
func (*UpsertAIRoleCategoryReq_Category) ProtoMessage()    {}
func (*UpsertAIRoleCategoryReq_Category) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{48, 2}
}
func (m *UpsertAIRoleCategoryReq_Category) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertAIRoleCategoryReq_Category.Unmarshal(m, b)
}
func (m *UpsertAIRoleCategoryReq_Category) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertAIRoleCategoryReq_Category.Marshal(b, m, deterministic)
}
func (dst *UpsertAIRoleCategoryReq_Category) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertAIRoleCategoryReq_Category.Merge(dst, src)
}
func (m *UpsertAIRoleCategoryReq_Category) XXX_Size() int {
	return xxx_messageInfo_UpsertAIRoleCategoryReq_Category.Size(m)
}
func (m *UpsertAIRoleCategoryReq_Category) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertAIRoleCategoryReq_Category.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertAIRoleCategoryReq_Category proto.InternalMessageInfo

func (m *UpsertAIRoleCategoryReq_Category) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpsertAIRoleCategoryReq_Category) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *UpsertAIRoleCategoryReq_Category) GetTips() string {
	if m != nil {
		return m.Tips
	}
	return ""
}

func (m *UpsertAIRoleCategoryReq_Category) GetPropScenes() []AIRoleCategory_Scene {
	if m != nil {
		return m.PropScenes
	}
	return nil
}

func (m *UpsertAIRoleCategoryReq_Category) GetProps() []*UpsertAIRoleCategoryReq_Prop {
	if m != nil {
		return m.Props
	}
	return nil
}

type UpsertAIRoleCategoryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertAIRoleCategoryResp) Reset()         { *m = UpsertAIRoleCategoryResp{} }
func (m *UpsertAIRoleCategoryResp) String() string { return proto.CompactTextString(m) }
func (*UpsertAIRoleCategoryResp) ProtoMessage()    {}
func (*UpsertAIRoleCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{49}
}
func (m *UpsertAIRoleCategoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertAIRoleCategoryResp.Unmarshal(m, b)
}
func (m *UpsertAIRoleCategoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertAIRoleCategoryResp.Marshal(b, m, deterministic)
}
func (dst *UpsertAIRoleCategoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertAIRoleCategoryResp.Merge(dst, src)
}
func (m *UpsertAIRoleCategoryResp) XXX_Size() int {
	return xxx_messageInfo_UpsertAIRoleCategoryResp.Size(m)
}
func (m *UpsertAIRoleCategoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertAIRoleCategoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertAIRoleCategoryResp proto.InternalMessageInfo

type DeleteAIRoleCategoryReq struct {
	CategoryId           string   `protobuf:"bytes,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAIRoleCategoryReq) Reset()         { *m = DeleteAIRoleCategoryReq{} }
func (m *DeleteAIRoleCategoryReq) String() string { return proto.CompactTextString(m) }
func (*DeleteAIRoleCategoryReq) ProtoMessage()    {}
func (*DeleteAIRoleCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{50}
}
func (m *DeleteAIRoleCategoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAIRoleCategoryReq.Unmarshal(m, b)
}
func (m *DeleteAIRoleCategoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAIRoleCategoryReq.Marshal(b, m, deterministic)
}
func (dst *DeleteAIRoleCategoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAIRoleCategoryReq.Merge(dst, src)
}
func (m *DeleteAIRoleCategoryReq) XXX_Size() int {
	return xxx_messageInfo_DeleteAIRoleCategoryReq.Size(m)
}
func (m *DeleteAIRoleCategoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAIRoleCategoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAIRoleCategoryReq proto.InternalMessageInfo

func (m *DeleteAIRoleCategoryReq) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

type DeleteAIRoleCategoryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAIRoleCategoryResp) Reset()         { *m = DeleteAIRoleCategoryResp{} }
func (m *DeleteAIRoleCategoryResp) String() string { return proto.CompactTextString(m) }
func (*DeleteAIRoleCategoryResp) ProtoMessage()    {}
func (*DeleteAIRoleCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{51}
}
func (m *DeleteAIRoleCategoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAIRoleCategoryResp.Unmarshal(m, b)
}
func (m *DeleteAIRoleCategoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAIRoleCategoryResp.Marshal(b, m, deterministic)
}
func (dst *DeleteAIRoleCategoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAIRoleCategoryResp.Merge(dst, src)
}
func (m *DeleteAIRoleCategoryResp) XXX_Size() int {
	return xxx_messageInfo_DeleteAIRoleCategoryResp.Size(m)
}
func (m *DeleteAIRoleCategoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAIRoleCategoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAIRoleCategoryResp proto.InternalMessageInfo

type BatchGetAIRoleCategoryReq struct {
	CategoryIds          []string          `protobuf:"bytes,1,rep,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	Source               GetCategorySource `protobuf:"varint,2,opt,name=source,proto3,enum=aigc_soulmate.GetCategorySource" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetAIRoleCategoryReq) Reset()         { *m = BatchGetAIRoleCategoryReq{} }
func (m *BatchGetAIRoleCategoryReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetAIRoleCategoryReq) ProtoMessage()    {}
func (*BatchGetAIRoleCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{52}
}
func (m *BatchGetAIRoleCategoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAIRoleCategoryReq.Unmarshal(m, b)
}
func (m *BatchGetAIRoleCategoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAIRoleCategoryReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetAIRoleCategoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAIRoleCategoryReq.Merge(dst, src)
}
func (m *BatchGetAIRoleCategoryReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetAIRoleCategoryReq.Size(m)
}
func (m *BatchGetAIRoleCategoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAIRoleCategoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAIRoleCategoryReq proto.InternalMessageInfo

func (m *BatchGetAIRoleCategoryReq) GetCategoryIds() []string {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

func (m *BatchGetAIRoleCategoryReq) GetSource() GetCategorySource {
	if m != nil {
		return m.Source
	}
	return GetCategorySource_GET_CATEGORY_SOURCE_UNKNOWN
}

type BatchGetAIRoleCategoryResp struct {
	CategoryInfos        []*AIRoleCategory `protobuf:"bytes,1,rep,name=category_infos,json=categoryInfos,proto3" json:"category_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetAIRoleCategoryResp) Reset()         { *m = BatchGetAIRoleCategoryResp{} }
func (m *BatchGetAIRoleCategoryResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetAIRoleCategoryResp) ProtoMessage()    {}
func (*BatchGetAIRoleCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{53}
}
func (m *BatchGetAIRoleCategoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAIRoleCategoryResp.Unmarshal(m, b)
}
func (m *BatchGetAIRoleCategoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAIRoleCategoryResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetAIRoleCategoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAIRoleCategoryResp.Merge(dst, src)
}
func (m *BatchGetAIRoleCategoryResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetAIRoleCategoryResp.Size(m)
}
func (m *BatchGetAIRoleCategoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAIRoleCategoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAIRoleCategoryResp proto.InternalMessageInfo

func (m *BatchGetAIRoleCategoryResp) GetCategoryInfos() []*AIRoleCategory {
	if m != nil {
		return m.CategoryInfos
	}
	return nil
}

type ResortAIRoleCategoryReq struct {
	Ids                  []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResortAIRoleCategoryReq) Reset()         { *m = ResortAIRoleCategoryReq{} }
func (m *ResortAIRoleCategoryReq) String() string { return proto.CompactTextString(m) }
func (*ResortAIRoleCategoryReq) ProtoMessage()    {}
func (*ResortAIRoleCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{54}
}
func (m *ResortAIRoleCategoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResortAIRoleCategoryReq.Unmarshal(m, b)
}
func (m *ResortAIRoleCategoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResortAIRoleCategoryReq.Marshal(b, m, deterministic)
}
func (dst *ResortAIRoleCategoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResortAIRoleCategoryReq.Merge(dst, src)
}
func (m *ResortAIRoleCategoryReq) XXX_Size() int {
	return xxx_messageInfo_ResortAIRoleCategoryReq.Size(m)
}
func (m *ResortAIRoleCategoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ResortAIRoleCategoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_ResortAIRoleCategoryReq proto.InternalMessageInfo

func (m *ResortAIRoleCategoryReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type ResortAIRoleCategoryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResortAIRoleCategoryResp) Reset()         { *m = ResortAIRoleCategoryResp{} }
func (m *ResortAIRoleCategoryResp) String() string { return proto.CompactTextString(m) }
func (*ResortAIRoleCategoryResp) ProtoMessage()    {}
func (*ResortAIRoleCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{55}
}
func (m *ResortAIRoleCategoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResortAIRoleCategoryResp.Unmarshal(m, b)
}
func (m *ResortAIRoleCategoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResortAIRoleCategoryResp.Marshal(b, m, deterministic)
}
func (dst *ResortAIRoleCategoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResortAIRoleCategoryResp.Merge(dst, src)
}
func (m *ResortAIRoleCategoryResp) XXX_Size() int {
	return xxx_messageInfo_ResortAIRoleCategoryResp.Size(m)
}
func (m *ResortAIRoleCategoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ResortAIRoleCategoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_ResortAIRoleCategoryResp proto.InternalMessageInfo

type ShareRoleReq struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShareRoleReq) Reset()         { *m = ShareRoleReq{} }
func (m *ShareRoleReq) String() string { return proto.CompactTextString(m) }
func (*ShareRoleReq) ProtoMessage()    {}
func (*ShareRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{56}
}
func (m *ShareRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShareRoleReq.Unmarshal(m, b)
}
func (m *ShareRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShareRoleReq.Marshal(b, m, deterministic)
}
func (dst *ShareRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShareRoleReq.Merge(dst, src)
}
func (m *ShareRoleReq) XXX_Size() int {
	return xxx_messageInfo_ShareRoleReq.Size(m)
}
func (m *ShareRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ShareRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_ShareRoleReq proto.InternalMessageInfo

func (m *ShareRoleReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type ShareRoleResp struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	ExpireAt             int64    `protobuf:"varint,2,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShareRoleResp) Reset()         { *m = ShareRoleResp{} }
func (m *ShareRoleResp) String() string { return proto.CompactTextString(m) }
func (*ShareRoleResp) ProtoMessage()    {}
func (*ShareRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{57}
}
func (m *ShareRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShareRoleResp.Unmarshal(m, b)
}
func (m *ShareRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShareRoleResp.Marshal(b, m, deterministic)
}
func (dst *ShareRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShareRoleResp.Merge(dst, src)
}
func (m *ShareRoleResp) XXX_Size() int {
	return xxx_messageInfo_ShareRoleResp.Size(m)
}
func (m *ShareRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ShareRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_ShareRoleResp proto.InternalMessageInfo

func (m *ShareRoleResp) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ShareRoleResp) GetExpireAt() int64 {
	if m != nil {
		return m.ExpireAt
	}
	return 0
}

type GetSharedRoleReq struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSharedRoleReq) Reset()         { *m = GetSharedRoleReq{} }
func (m *GetSharedRoleReq) String() string { return proto.CompactTextString(m) }
func (*GetSharedRoleReq) ProtoMessage()    {}
func (*GetSharedRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{58}
}
func (m *GetSharedRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSharedRoleReq.Unmarshal(m, b)
}
func (m *GetSharedRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSharedRoleReq.Marshal(b, m, deterministic)
}
func (dst *GetSharedRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSharedRoleReq.Merge(dst, src)
}
func (m *GetSharedRoleReq) XXX_Size() int {
	return xxx_messageInfo_GetSharedRoleReq.Size(m)
}
func (m *GetSharedRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSharedRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSharedRoleReq proto.InternalMessageInfo

func (m *GetSharedRoleReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type GetSharedRoleResp struct {
	Role                 *SharedRole `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetSharedRoleResp) Reset()         { *m = GetSharedRoleResp{} }
func (m *GetSharedRoleResp) String() string { return proto.CompactTextString(m) }
func (*GetSharedRoleResp) ProtoMessage()    {}
func (*GetSharedRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{59}
}
func (m *GetSharedRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSharedRoleResp.Unmarshal(m, b)
}
func (m *GetSharedRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSharedRoleResp.Marshal(b, m, deterministic)
}
func (dst *GetSharedRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSharedRoleResp.Merge(dst, src)
}
func (m *GetSharedRoleResp) XXX_Size() int {
	return xxx_messageInfo_GetSharedRoleResp.Size(m)
}
func (m *GetSharedRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSharedRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSharedRoleResp proto.InternalMessageInfo

func (m *GetSharedRoleResp) GetRole() *SharedRole {
	if m != nil {
		return m.Role
	}
	return nil
}

type AllocShareIdentifierReq struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AllocShareIdentifierReq) Reset()         { *m = AllocShareIdentifierReq{} }
func (m *AllocShareIdentifierReq) String() string { return proto.CompactTextString(m) }
func (*AllocShareIdentifierReq) ProtoMessage()    {}
func (*AllocShareIdentifierReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{60}
}
func (m *AllocShareIdentifierReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllocShareIdentifierReq.Unmarshal(m, b)
}
func (m *AllocShareIdentifierReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllocShareIdentifierReq.Marshal(b, m, deterministic)
}
func (dst *AllocShareIdentifierReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllocShareIdentifierReq.Merge(dst, src)
}
func (m *AllocShareIdentifierReq) XXX_Size() int {
	return xxx_messageInfo_AllocShareIdentifierReq.Size(m)
}
func (m *AllocShareIdentifierReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AllocShareIdentifierReq.DiscardUnknown(m)
}

var xxx_messageInfo_AllocShareIdentifierReq proto.InternalMessageInfo

func (m *AllocShareIdentifierReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type AllocShareIdentifierResp struct {
	Identifier           string   `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AllocShareIdentifierResp) Reset()         { *m = AllocShareIdentifierResp{} }
func (m *AllocShareIdentifierResp) String() string { return proto.CompactTextString(m) }
func (*AllocShareIdentifierResp) ProtoMessage()    {}
func (*AllocShareIdentifierResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{61}
}
func (m *AllocShareIdentifierResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllocShareIdentifierResp.Unmarshal(m, b)
}
func (m *AllocShareIdentifierResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllocShareIdentifierResp.Marshal(b, m, deterministic)
}
func (dst *AllocShareIdentifierResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllocShareIdentifierResp.Merge(dst, src)
}
func (m *AllocShareIdentifierResp) XXX_Size() int {
	return xxx_messageInfo_AllocShareIdentifierResp.Size(m)
}
func (m *AllocShareIdentifierResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AllocShareIdentifierResp.DiscardUnknown(m)
}

var xxx_messageInfo_AllocShareIdentifierResp proto.InternalMessageInfo

func (m *AllocShareIdentifierResp) GetIdentifier() string {
	if m != nil {
		return m.Identifier
	}
	return ""
}

type VerifyShareIdentifierReq struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Identifier           string   `protobuf:"bytes,2,opt,name=identifier,proto3" json:"identifier,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VerifyShareIdentifierReq) Reset()         { *m = VerifyShareIdentifierReq{} }
func (m *VerifyShareIdentifierReq) String() string { return proto.CompactTextString(m) }
func (*VerifyShareIdentifierReq) ProtoMessage()    {}
func (*VerifyShareIdentifierReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{62}
}
func (m *VerifyShareIdentifierReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VerifyShareIdentifierReq.Unmarshal(m, b)
}
func (m *VerifyShareIdentifierReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VerifyShareIdentifierReq.Marshal(b, m, deterministic)
}
func (dst *VerifyShareIdentifierReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VerifyShareIdentifierReq.Merge(dst, src)
}
func (m *VerifyShareIdentifierReq) XXX_Size() int {
	return xxx_messageInfo_VerifyShareIdentifierReq.Size(m)
}
func (m *VerifyShareIdentifierReq) XXX_DiscardUnknown() {
	xxx_messageInfo_VerifyShareIdentifierReq.DiscardUnknown(m)
}

var xxx_messageInfo_VerifyShareIdentifierReq proto.InternalMessageInfo

func (m *VerifyShareIdentifierReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *VerifyShareIdentifierReq) GetIdentifier() string {
	if m != nil {
		return m.Identifier
	}
	return ""
}

type VerifyShareIdentifierResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	VisitorId            string   `protobuf:"bytes,3,opt,name=visitor_id,json=visitorId,proto3" json:"visitor_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VerifyShareIdentifierResp) Reset()         { *m = VerifyShareIdentifierResp{} }
func (m *VerifyShareIdentifierResp) String() string { return proto.CompactTextString(m) }
func (*VerifyShareIdentifierResp) ProtoMessage()    {}
func (*VerifyShareIdentifierResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{63}
}
func (m *VerifyShareIdentifierResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VerifyShareIdentifierResp.Unmarshal(m, b)
}
func (m *VerifyShareIdentifierResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VerifyShareIdentifierResp.Marshal(b, m, deterministic)
}
func (dst *VerifyShareIdentifierResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VerifyShareIdentifierResp.Merge(dst, src)
}
func (m *VerifyShareIdentifierResp) XXX_Size() int {
	return xxx_messageInfo_VerifyShareIdentifierResp.Size(m)
}
func (m *VerifyShareIdentifierResp) XXX_DiscardUnknown() {
	xxx_messageInfo_VerifyShareIdentifierResp.DiscardUnknown(m)
}

var xxx_messageInfo_VerifyShareIdentifierResp proto.InternalMessageInfo

func (m *VerifyShareIdentifierResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VerifyShareIdentifierResp) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *VerifyShareIdentifierResp) GetVisitorId() string {
	if m != nil {
		return m.VisitorId
	}
	return ""
}

type AIMessage struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,3,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	Bin                  []byte   `protobuf:"bytes,4,opt,name=bin,proto3" json:"bin,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIMessage) Reset()         { *m = AIMessage{} }
func (m *AIMessage) String() string { return proto.CompactTextString(m) }
func (*AIMessage) ProtoMessage()    {}
func (*AIMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{64}
}
func (m *AIMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIMessage.Unmarshal(m, b)
}
func (m *AIMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIMessage.Marshal(b, m, deterministic)
}
func (dst *AIMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIMessage.Merge(dst, src)
}
func (m *AIMessage) XXX_Size() int {
	return xxx_messageInfo_AIMessage.Size(m)
}
func (m *AIMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_AIMessage.DiscardUnknown(m)
}

var xxx_messageInfo_AIMessage proto.InternalMessageInfo

func (m *AIMessage) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *AIMessage) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AIMessage) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *AIMessage) GetBin() []byte {
	if m != nil {
		return m.Bin
	}
	return nil
}

type WriteAIMessageReq struct {
	Msg                  *AIMessage `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *WriteAIMessageReq) Reset()         { *m = WriteAIMessageReq{} }
func (m *WriteAIMessageReq) String() string { return proto.CompactTextString(m) }
func (*WriteAIMessageReq) ProtoMessage()    {}
func (*WriteAIMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{65}
}
func (m *WriteAIMessageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WriteAIMessageReq.Unmarshal(m, b)
}
func (m *WriteAIMessageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WriteAIMessageReq.Marshal(b, m, deterministic)
}
func (dst *WriteAIMessageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteAIMessageReq.Merge(dst, src)
}
func (m *WriteAIMessageReq) XXX_Size() int {
	return xxx_messageInfo_WriteAIMessageReq.Size(m)
}
func (m *WriteAIMessageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteAIMessageReq.DiscardUnknown(m)
}

var xxx_messageInfo_WriteAIMessageReq proto.InternalMessageInfo

func (m *WriteAIMessageReq) GetMsg() *AIMessage {
	if m != nil {
		return m.Msg
	}
	return nil
}

type WriteAIMessageResp struct {
	MsgId                string   `protobuf:"bytes,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WriteAIMessageResp) Reset()         { *m = WriteAIMessageResp{} }
func (m *WriteAIMessageResp) String() string { return proto.CompactTextString(m) }
func (*WriteAIMessageResp) ProtoMessage()    {}
func (*WriteAIMessageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{66}
}
func (m *WriteAIMessageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WriteAIMessageResp.Unmarshal(m, b)
}
func (m *WriteAIMessageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WriteAIMessageResp.Marshal(b, m, deterministic)
}
func (dst *WriteAIMessageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteAIMessageResp.Merge(dst, src)
}
func (m *WriteAIMessageResp) XXX_Size() int {
	return xxx_messageInfo_WriteAIMessageResp.Size(m)
}
func (m *WriteAIMessageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteAIMessageResp.DiscardUnknown(m)
}

var xxx_messageInfo_WriteAIMessageResp proto.InternalMessageInfo

func (m *WriteAIMessageResp) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

type PullAIMessageReq struct {
	Uid       uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId uint32 `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	// 拉取大于msg_id的消息
	MsgId                string   `protobuf:"bytes,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PullAIMessageReq) Reset()         { *m = PullAIMessageReq{} }
func (m *PullAIMessageReq) String() string { return proto.CompactTextString(m) }
func (*PullAIMessageReq) ProtoMessage()    {}
func (*PullAIMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{67}
}
func (m *PullAIMessageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PullAIMessageReq.Unmarshal(m, b)
}
func (m *PullAIMessageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PullAIMessageReq.Marshal(b, m, deterministic)
}
func (dst *PullAIMessageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PullAIMessageReq.Merge(dst, src)
}
func (m *PullAIMessageReq) XXX_Size() int {
	return xxx_messageInfo_PullAIMessageReq.Size(m)
}
func (m *PullAIMessageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PullAIMessageReq.DiscardUnknown(m)
}

var xxx_messageInfo_PullAIMessageReq proto.InternalMessageInfo

func (m *PullAIMessageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PullAIMessageReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *PullAIMessageReq) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *PullAIMessageReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type PullAIMessageResp struct {
	MsgList              []*AIMessage `protobuf:"bytes,1,rep,name=msg_list,json=msgList,proto3" json:"msg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PullAIMessageResp) Reset()         { *m = PullAIMessageResp{} }
func (m *PullAIMessageResp) String() string { return proto.CompactTextString(m) }
func (*PullAIMessageResp) ProtoMessage()    {}
func (*PullAIMessageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{68}
}
func (m *PullAIMessageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PullAIMessageResp.Unmarshal(m, b)
}
func (m *PullAIMessageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PullAIMessageResp.Marshal(b, m, deterministic)
}
func (dst *PullAIMessageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PullAIMessageResp.Merge(dst, src)
}
func (m *PullAIMessageResp) XXX_Size() int {
	return xxx_messageInfo_PullAIMessageResp.Size(m)
}
func (m *PullAIMessageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PullAIMessageResp.DiscardUnknown(m)
}

var xxx_messageInfo_PullAIMessageResp proto.InternalMessageInfo

func (m *PullAIMessageResp) GetMsgList() []*AIMessage {
	if m != nil {
		return m.MsgList
	}
	return nil
}

type ClearAIMessageReq struct {
	Uid       uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId uint32 `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	// 删除小于等于msg_id的消息
	MsgId                string   `protobuf:"bytes,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearAIMessageReq) Reset()         { *m = ClearAIMessageReq{} }
func (m *ClearAIMessageReq) String() string { return proto.CompactTextString(m) }
func (*ClearAIMessageReq) ProtoMessage()    {}
func (*ClearAIMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{69}
}
func (m *ClearAIMessageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearAIMessageReq.Unmarshal(m, b)
}
func (m *ClearAIMessageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearAIMessageReq.Marshal(b, m, deterministic)
}
func (dst *ClearAIMessageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearAIMessageReq.Merge(dst, src)
}
func (m *ClearAIMessageReq) XXX_Size() int {
	return xxx_messageInfo_ClearAIMessageReq.Size(m)
}
func (m *ClearAIMessageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearAIMessageReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClearAIMessageReq proto.InternalMessageInfo

func (m *ClearAIMessageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ClearAIMessageReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *ClearAIMessageReq) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

type ClearAIMessageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearAIMessageResp) Reset()         { *m = ClearAIMessageResp{} }
func (m *ClearAIMessageResp) String() string { return proto.CompactTextString(m) }
func (*ClearAIMessageResp) ProtoMessage()    {}
func (*ClearAIMessageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{70}
}
func (m *ClearAIMessageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearAIMessageResp.Unmarshal(m, b)
}
func (m *ClearAIMessageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearAIMessageResp.Marshal(b, m, deterministic)
}
func (dst *ClearAIMessageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearAIMessageResp.Merge(dst, src)
}
func (m *ClearAIMessageResp) XXX_Size() int {
	return xxx_messageInfo_ClearAIMessageResp.Size(m)
}
func (m *ClearAIMessageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearAIMessageResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClearAIMessageResp proto.InternalMessageInfo

// 分页查询用户角色信息，仅运营后台使用
type SearchUserRoleReq struct {
	// 创建时间范围
	StartTime int64             `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime   int64             `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Sex       SearchRoleSexEnum `protobuf:"varint,3,opt,name=sex,proto3,enum=aigc_soulmate.SearchRoleSexEnum" json:"sex,omitempty"`
	// 展示分类id
	CategoryId string `protobuf:"bytes,4,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 状态
	State AIRoleState `protobuf:"varint,5,opt,name=state,proto3,enum=aigc_soulmate.AIRoleState" json:"state,omitempty"`
	// 是否在首页展示
	FilterExposed FilterExpose `protobuf:"varint,10,opt,name=filter_exposed,json=filterExposed,proto3,enum=aigc_soulmate.FilterExpose" json:"filter_exposed,omitempty"`
	// 角色id
	RoleId uint32 `protobuf:"varint,11,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 名称，设定走es搜索
	// 名称
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	// 设定/说明
	Character string `protobuf:"bytes,7,opt,name=character,proto3" json:"character,omitempty"`
	// 上次拉取的最后一个角色ID
	SearchAfter uint32 `protobuf:"varint,8,opt,name=search_after,json=searchAfter,proto3" json:"search_after,omitempty"`
	// searchType
	SearchType           SearchType `protobuf:"varint,9,opt,name=search_type,json=searchType,proto3,enum=aigc_soulmate.SearchType" json:"search_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SearchUserRoleReq) Reset()         { *m = SearchUserRoleReq{} }
func (m *SearchUserRoleReq) String() string { return proto.CompactTextString(m) }
func (*SearchUserRoleReq) ProtoMessage()    {}
func (*SearchUserRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{71}
}
func (m *SearchUserRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchUserRoleReq.Unmarshal(m, b)
}
func (m *SearchUserRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchUserRoleReq.Marshal(b, m, deterministic)
}
func (dst *SearchUserRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchUserRoleReq.Merge(dst, src)
}
func (m *SearchUserRoleReq) XXX_Size() int {
	return xxx_messageInfo_SearchUserRoleReq.Size(m)
}
func (m *SearchUserRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchUserRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchUserRoleReq proto.InternalMessageInfo

func (m *SearchUserRoleReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *SearchUserRoleReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SearchUserRoleReq) GetSex() SearchRoleSexEnum {
	if m != nil {
		return m.Sex
	}
	return SearchRoleSexEnum_SEARCH_ROLE_SEX_NONE
}

func (m *SearchUserRoleReq) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *SearchUserRoleReq) GetState() AIRoleState {
	if m != nil {
		return m.State
	}
	return AIRoleState_AIRoleStateNone
}

func (m *SearchUserRoleReq) GetFilterExposed() FilterExpose {
	if m != nil {
		return m.FilterExposed
	}
	return FilterExpose_FILTER_EXPOSE_NONE
}

func (m *SearchUserRoleReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *SearchUserRoleReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SearchUserRoleReq) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *SearchUserRoleReq) GetSearchAfter() uint32 {
	if m != nil {
		return m.SearchAfter
	}
	return 0
}

func (m *SearchUserRoleReq) GetSearchType() SearchType {
	if m != nil {
		return m.SearchType
	}
	return SearchType_SEARCH_TYPE_NONE
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type SearchUserRoleResp struct {
	UserRoleList         []*AIRole `protobuf:"bytes,1,rep,name=user_role_list,json=userRoleList,proto3" json:"user_role_list,omitempty"`
	LastId               uint32    `protobuf:"varint,2,opt,name=lastId,proto3" json:"lastId,omitempty"`
	LoadFinish           bool      `protobuf:"varint,3,opt,name=load_finish,json=loadFinish,proto3" json:"load_finish,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SearchUserRoleResp) Reset()         { *m = SearchUserRoleResp{} }
func (m *SearchUserRoleResp) String() string { return proto.CompactTextString(m) }
func (*SearchUserRoleResp) ProtoMessage()    {}
func (*SearchUserRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{72}
}
func (m *SearchUserRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchUserRoleResp.Unmarshal(m, b)
}
func (m *SearchUserRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchUserRoleResp.Marshal(b, m, deterministic)
}
func (dst *SearchUserRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchUserRoleResp.Merge(dst, src)
}
func (m *SearchUserRoleResp) XXX_Size() int {
	return xxx_messageInfo_SearchUserRoleResp.Size(m)
}
func (m *SearchUserRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchUserRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchUserRoleResp proto.InternalMessageInfo

func (m *SearchUserRoleResp) GetUserRoleList() []*AIRole {
	if m != nil {
		return m.UserRoleList
	}
	return nil
}

func (m *SearchUserRoleResp) GetLastId() uint32 {
	if m != nil {
		return m.LastId
	}
	return 0
}

func (m *SearchUserRoleResp) GetLoadFinish() bool {
	if m != nil {
		return m.LoadFinish
	}
	return false
}

type BatchUpdateUserRoleReq struct {
	RoleList             []*BatchUpdateUserRoleReq_UpdateRole `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *BatchUpdateUserRoleReq) Reset()         { *m = BatchUpdateUserRoleReq{} }
func (m *BatchUpdateUserRoleReq) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateUserRoleReq) ProtoMessage()    {}
func (*BatchUpdateUserRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{73}
}
func (m *BatchUpdateUserRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateUserRoleReq.Unmarshal(m, b)
}
func (m *BatchUpdateUserRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateUserRoleReq.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateUserRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateUserRoleReq.Merge(dst, src)
}
func (m *BatchUpdateUserRoleReq) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateUserRoleReq.Size(m)
}
func (m *BatchUpdateUserRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateUserRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateUserRoleReq proto.InternalMessageInfo

func (m *BatchUpdateUserRoleReq) GetRoleList() []*BatchUpdateUserRoleReq_UpdateRole {
	if m != nil {
		return m.RoleList
	}
	return nil
}

type BatchUpdateUserRoleReq_UpdateRole struct {
	RoleId uint32 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 强插位置
	InsertPos uint32 `protobuf:"varint,2,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	// 是否展示/曝光到首页 true:展示 false:不展示
	Exposed bool `protobuf:"varint,3,opt,name=exposed,proto3" json:"exposed,omitempty"`
	// 展示分类id
	CategoryId string `protobuf:"bytes,4,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 配置点赞数
	ConfigLikeNum        int32    `protobuf:"varint,5,opt,name=config_like_num,json=configLikeNum,proto3" json:"config_like_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpdateUserRoleReq_UpdateRole) Reset()         { *m = BatchUpdateUserRoleReq_UpdateRole{} }
func (m *BatchUpdateUserRoleReq_UpdateRole) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateUserRoleReq_UpdateRole) ProtoMessage()    {}
func (*BatchUpdateUserRoleReq_UpdateRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{73, 0}
}
func (m *BatchUpdateUserRoleReq_UpdateRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateUserRoleReq_UpdateRole.Unmarshal(m, b)
}
func (m *BatchUpdateUserRoleReq_UpdateRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateUserRoleReq_UpdateRole.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateUserRoleReq_UpdateRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateUserRoleReq_UpdateRole.Merge(dst, src)
}
func (m *BatchUpdateUserRoleReq_UpdateRole) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateUserRoleReq_UpdateRole.Size(m)
}
func (m *BatchUpdateUserRoleReq_UpdateRole) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateUserRoleReq_UpdateRole.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateUserRoleReq_UpdateRole proto.InternalMessageInfo

func (m *BatchUpdateUserRoleReq_UpdateRole) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *BatchUpdateUserRoleReq_UpdateRole) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *BatchUpdateUserRoleReq_UpdateRole) GetExposed() bool {
	if m != nil {
		return m.Exposed
	}
	return false
}

func (m *BatchUpdateUserRoleReq_UpdateRole) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *BatchUpdateUserRoleReq_UpdateRole) GetConfigLikeNum() int32 {
	if m != nil {
		return m.ConfigLikeNum
	}
	return 0
}

type BatchUpdateUserRoleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpdateUserRoleResp) Reset()         { *m = BatchUpdateUserRoleResp{} }
func (m *BatchUpdateUserRoleResp) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateUserRoleResp) ProtoMessage()    {}
func (*BatchUpdateUserRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{74}
}
func (m *BatchUpdateUserRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateUserRoleResp.Unmarshal(m, b)
}
func (m *BatchUpdateUserRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateUserRoleResp.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateUserRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateUserRoleResp.Merge(dst, src)
}
func (m *BatchUpdateUserRoleResp) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateUserRoleResp.Size(m)
}
func (m *BatchUpdateUserRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateUserRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateUserRoleResp proto.InternalMessageInfo

type BatchDeleteUserRoleReq struct {
	RoleIdList           []uint32 `protobuf:"varint,1,rep,packed,name=role_id_list,json=roleIdList,proto3" json:"role_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDeleteUserRoleReq) Reset()         { *m = BatchDeleteUserRoleReq{} }
func (m *BatchDeleteUserRoleReq) String() string { return proto.CompactTextString(m) }
func (*BatchDeleteUserRoleReq) ProtoMessage()    {}
func (*BatchDeleteUserRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{75}
}
func (m *BatchDeleteUserRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeleteUserRoleReq.Unmarshal(m, b)
}
func (m *BatchDeleteUserRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeleteUserRoleReq.Marshal(b, m, deterministic)
}
func (dst *BatchDeleteUserRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeleteUserRoleReq.Merge(dst, src)
}
func (m *BatchDeleteUserRoleReq) XXX_Size() int {
	return xxx_messageInfo_BatchDeleteUserRoleReq.Size(m)
}
func (m *BatchDeleteUserRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeleteUserRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeleteUserRoleReq proto.InternalMessageInfo

func (m *BatchDeleteUserRoleReq) GetRoleIdList() []uint32 {
	if m != nil {
		return m.RoleIdList
	}
	return nil
}

type BatchDeleteUserRoleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDeleteUserRoleResp) Reset()         { *m = BatchDeleteUserRoleResp{} }
func (m *BatchDeleteUserRoleResp) String() string { return proto.CompactTextString(m) }
func (*BatchDeleteUserRoleResp) ProtoMessage()    {}
func (*BatchDeleteUserRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{76}
}
func (m *BatchDeleteUserRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeleteUserRoleResp.Unmarshal(m, b)
}
func (m *BatchDeleteUserRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeleteUserRoleResp.Marshal(b, m, deterministic)
}
func (dst *BatchDeleteUserRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeleteUserRoleResp.Merge(dst, src)
}
func (m *BatchDeleteUserRoleResp) XXX_Size() int {
	return xxx_messageInfo_BatchDeleteUserRoleResp.Size(m)
}
func (m *BatchDeleteUserRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeleteUserRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeleteUserRoleResp proto.InternalMessageInfo

type UpdateAIRoleAuditResultReq struct {
	Id                   uint32      `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AuditAt              int64       `protobuf:"varint,2,opt,name=audit_at,json=auditAt,proto3" json:"audit_at,omitempty"`
	Result               AuditResult `protobuf:"varint,3,opt,name=result,proto3,enum=aigc_soulmate.AuditResult" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpdateAIRoleAuditResultReq) Reset()         { *m = UpdateAIRoleAuditResultReq{} }
func (m *UpdateAIRoleAuditResultReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAIRoleAuditResultReq) ProtoMessage()    {}
func (*UpdateAIRoleAuditResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{77}
}
func (m *UpdateAIRoleAuditResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIRoleAuditResultReq.Unmarshal(m, b)
}
func (m *UpdateAIRoleAuditResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIRoleAuditResultReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAIRoleAuditResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIRoleAuditResultReq.Merge(dst, src)
}
func (m *UpdateAIRoleAuditResultReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAIRoleAuditResultReq.Size(m)
}
func (m *UpdateAIRoleAuditResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIRoleAuditResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIRoleAuditResultReq proto.InternalMessageInfo

func (m *UpdateAIRoleAuditResultReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateAIRoleAuditResultReq) GetAuditAt() int64 {
	if m != nil {
		return m.AuditAt
	}
	return 0
}

func (m *UpdateAIRoleAuditResultReq) GetResult() AuditResult {
	if m != nil {
		return m.Result
	}
	return AuditResult_AuditResultReview
}

type UpdateAIRoleAuditResultResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAIRoleAuditResultResp) Reset()         { *m = UpdateAIRoleAuditResultResp{} }
func (m *UpdateAIRoleAuditResultResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAIRoleAuditResultResp) ProtoMessage()    {}
func (*UpdateAIRoleAuditResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{78}
}
func (m *UpdateAIRoleAuditResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIRoleAuditResultResp.Unmarshal(m, b)
}
func (m *UpdateAIRoleAuditResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIRoleAuditResultResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAIRoleAuditResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIRoleAuditResultResp.Merge(dst, src)
}
func (m *UpdateAIRoleAuditResultResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAIRoleAuditResultResp.Size(m)
}
func (m *UpdateAIRoleAuditResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIRoleAuditResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIRoleAuditResultResp proto.InternalMessageInfo

type LikeAIRoleReq struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LikeAIRoleReq) Reset()         { *m = LikeAIRoleReq{} }
func (m *LikeAIRoleReq) String() string { return proto.CompactTextString(m) }
func (*LikeAIRoleReq) ProtoMessage()    {}
func (*LikeAIRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{79}
}
func (m *LikeAIRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LikeAIRoleReq.Unmarshal(m, b)
}
func (m *LikeAIRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LikeAIRoleReq.Marshal(b, m, deterministic)
}
func (dst *LikeAIRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LikeAIRoleReq.Merge(dst, src)
}
func (m *LikeAIRoleReq) XXX_Size() int {
	return xxx_messageInfo_LikeAIRoleReq.Size(m)
}
func (m *LikeAIRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LikeAIRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_LikeAIRoleReq proto.InternalMessageInfo

func (m *LikeAIRoleReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type LikeAIRoleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LikeAIRoleResp) Reset()         { *m = LikeAIRoleResp{} }
func (m *LikeAIRoleResp) String() string { return proto.CompactTextString(m) }
func (*LikeAIRoleResp) ProtoMessage()    {}
func (*LikeAIRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{80}
}
func (m *LikeAIRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LikeAIRoleResp.Unmarshal(m, b)
}
func (m *LikeAIRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LikeAIRoleResp.Marshal(b, m, deterministic)
}
func (dst *LikeAIRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LikeAIRoleResp.Merge(dst, src)
}
func (m *LikeAIRoleResp) XXX_Size() int {
	return xxx_messageInfo_LikeAIRoleResp.Size(m)
}
func (m *LikeAIRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LikeAIRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_LikeAIRoleResp proto.InternalMessageInfo

type UnlikeAIRoleReq struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnlikeAIRoleReq) Reset()         { *m = UnlikeAIRoleReq{} }
func (m *UnlikeAIRoleReq) String() string { return proto.CompactTextString(m) }
func (*UnlikeAIRoleReq) ProtoMessage()    {}
func (*UnlikeAIRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{81}
}
func (m *UnlikeAIRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnlikeAIRoleReq.Unmarshal(m, b)
}
func (m *UnlikeAIRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnlikeAIRoleReq.Marshal(b, m, deterministic)
}
func (dst *UnlikeAIRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnlikeAIRoleReq.Merge(dst, src)
}
func (m *UnlikeAIRoleReq) XXX_Size() int {
	return xxx_messageInfo_UnlikeAIRoleReq.Size(m)
}
func (m *UnlikeAIRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnlikeAIRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnlikeAIRoleReq proto.InternalMessageInfo

func (m *UnlikeAIRoleReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type UnlikeAIRoleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnlikeAIRoleResp) Reset()         { *m = UnlikeAIRoleResp{} }
func (m *UnlikeAIRoleResp) String() string { return proto.CompactTextString(m) }
func (*UnlikeAIRoleResp) ProtoMessage()    {}
func (*UnlikeAIRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{82}
}
func (m *UnlikeAIRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnlikeAIRoleResp.Unmarshal(m, b)
}
func (m *UnlikeAIRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnlikeAIRoleResp.Marshal(b, m, deterministic)
}
func (dst *UnlikeAIRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnlikeAIRoleResp.Merge(dst, src)
}
func (m *UnlikeAIRoleResp) XXX_Size() int {
	return xxx_messageInfo_UnlikeAIRoleResp.Size(m)
}
func (m *UnlikeAIRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnlikeAIRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnlikeAIRoleResp proto.InternalMessageInfo

type GetUserAIRoleLikesReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleIdList           []uint32 `protobuf:"varint,2,rep,packed,name=role_id_list,json=roleIdList,proto3" json:"role_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAIRoleLikesReq) Reset()         { *m = GetUserAIRoleLikesReq{} }
func (m *GetUserAIRoleLikesReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAIRoleLikesReq) ProtoMessage()    {}
func (*GetUserAIRoleLikesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{83}
}
func (m *GetUserAIRoleLikesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIRoleLikesReq.Unmarshal(m, b)
}
func (m *GetUserAIRoleLikesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIRoleLikesReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAIRoleLikesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIRoleLikesReq.Merge(dst, src)
}
func (m *GetUserAIRoleLikesReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAIRoleLikesReq.Size(m)
}
func (m *GetUserAIRoleLikesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIRoleLikesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIRoleLikesReq proto.InternalMessageInfo

func (m *GetUserAIRoleLikesReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserAIRoleLikesReq) GetRoleIdList() []uint32 {
	if m != nil {
		return m.RoleIdList
	}
	return nil
}

type GetUserAIRoleLikesResp struct {
	LikeList             []*AIRoleLikeInfo `protobuf:"bytes,1,rep,name=like_list,json=likeList,proto3" json:"like_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetUserAIRoleLikesResp) Reset()         { *m = GetUserAIRoleLikesResp{} }
func (m *GetUserAIRoleLikesResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAIRoleLikesResp) ProtoMessage()    {}
func (*GetUserAIRoleLikesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{84}
}
func (m *GetUserAIRoleLikesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIRoleLikesResp.Unmarshal(m, b)
}
func (m *GetUserAIRoleLikesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIRoleLikesResp.Marshal(b, m, deterministic)
}
func (dst *GetUserAIRoleLikesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIRoleLikesResp.Merge(dst, src)
}
func (m *GetUserAIRoleLikesResp) XXX_Size() int {
	return xxx_messageInfo_GetUserAIRoleLikesResp.Size(m)
}
func (m *GetUserAIRoleLikesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIRoleLikesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIRoleLikesResp proto.InternalMessageInfo

func (m *GetUserAIRoleLikesResp) GetLikeList() []*AIRoleLikeInfo {
	if m != nil {
		return m.LikeList
	}
	return nil
}

type GetLatestUpdatedAIRoleListReq struct {
	// 大于updated_at
	UpdatedAt int64 `protobuf:"varint,1,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// 大于id
	Id uint32 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// 数量
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLatestUpdatedAIRoleListReq) Reset()         { *m = GetLatestUpdatedAIRoleListReq{} }
func (m *GetLatestUpdatedAIRoleListReq) String() string { return proto.CompactTextString(m) }
func (*GetLatestUpdatedAIRoleListReq) ProtoMessage()    {}
func (*GetLatestUpdatedAIRoleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{85}
}
func (m *GetLatestUpdatedAIRoleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLatestUpdatedAIRoleListReq.Unmarshal(m, b)
}
func (m *GetLatestUpdatedAIRoleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLatestUpdatedAIRoleListReq.Marshal(b, m, deterministic)
}
func (dst *GetLatestUpdatedAIRoleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLatestUpdatedAIRoleListReq.Merge(dst, src)
}
func (m *GetLatestUpdatedAIRoleListReq) XXX_Size() int {
	return xxx_messageInfo_GetLatestUpdatedAIRoleListReq.Size(m)
}
func (m *GetLatestUpdatedAIRoleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLatestUpdatedAIRoleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLatestUpdatedAIRoleListReq proto.InternalMessageInfo

func (m *GetLatestUpdatedAIRoleListReq) GetUpdatedAt() int64 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *GetLatestUpdatedAIRoleListReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetLatestUpdatedAIRoleListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetLatestUpdatedAIRoleListResp struct {
	List []*AIRole `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 下一页请求的id参数，0表示没有下一页了
	LastId               uint32   `protobuf:"varint,2,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLatestUpdatedAIRoleListResp) Reset()         { *m = GetLatestUpdatedAIRoleListResp{} }
func (m *GetLatestUpdatedAIRoleListResp) String() string { return proto.CompactTextString(m) }
func (*GetLatestUpdatedAIRoleListResp) ProtoMessage()    {}
func (*GetLatestUpdatedAIRoleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{86}
}
func (m *GetLatestUpdatedAIRoleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLatestUpdatedAIRoleListResp.Unmarshal(m, b)
}
func (m *GetLatestUpdatedAIRoleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLatestUpdatedAIRoleListResp.Marshal(b, m, deterministic)
}
func (dst *GetLatestUpdatedAIRoleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLatestUpdatedAIRoleListResp.Merge(dst, src)
}
func (m *GetLatestUpdatedAIRoleListResp) XXX_Size() int {
	return xxx_messageInfo_GetLatestUpdatedAIRoleListResp.Size(m)
}
func (m *GetLatestUpdatedAIRoleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLatestUpdatedAIRoleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLatestUpdatedAIRoleListResp proto.InternalMessageInfo

func (m *GetLatestUpdatedAIRoleListResp) GetList() []*AIRole {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetLatestUpdatedAIRoleListResp) GetLastId() uint32 {
	if m != nil {
		return m.LastId
	}
	return 0
}

type SearchAIRoleReq struct {
	Content              string   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	LastId               string   `protobuf:"bytes,2,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchAIRoleReq) Reset()         { *m = SearchAIRoleReq{} }
func (m *SearchAIRoleReq) String() string { return proto.CompactTextString(m) }
func (*SearchAIRoleReq) ProtoMessage()    {}
func (*SearchAIRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{87}
}
func (m *SearchAIRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchAIRoleReq.Unmarshal(m, b)
}
func (m *SearchAIRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchAIRoleReq.Marshal(b, m, deterministic)
}
func (dst *SearchAIRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchAIRoleReq.Merge(dst, src)
}
func (m *SearchAIRoleReq) XXX_Size() int {
	return xxx_messageInfo_SearchAIRoleReq.Size(m)
}
func (m *SearchAIRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchAIRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchAIRoleReq proto.InternalMessageInfo

func (m *SearchAIRoleReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *SearchAIRoleReq) GetLastId() string {
	if m != nil {
		return m.LastId
	}
	return ""
}

func (m *SearchAIRoleReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type SearchAIRoleResp struct {
	RoleList             []*AIRole `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	LastId               string    `protobuf:"bytes,2,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	LoadFinish           bool      `protobuf:"varint,3,opt,name=load_finish,json=loadFinish,proto3" json:"load_finish,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SearchAIRoleResp) Reset()         { *m = SearchAIRoleResp{} }
func (m *SearchAIRoleResp) String() string { return proto.CompactTextString(m) }
func (*SearchAIRoleResp) ProtoMessage()    {}
func (*SearchAIRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{88}
}
func (m *SearchAIRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchAIRoleResp.Unmarshal(m, b)
}
func (m *SearchAIRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchAIRoleResp.Marshal(b, m, deterministic)
}
func (dst *SearchAIRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchAIRoleResp.Merge(dst, src)
}
func (m *SearchAIRoleResp) XXX_Size() int {
	return xxx_messageInfo_SearchAIRoleResp.Size(m)
}
func (m *SearchAIRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchAIRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchAIRoleResp proto.InternalMessageInfo

func (m *SearchAIRoleResp) GetRoleList() []*AIRole {
	if m != nil {
		return m.RoleList
	}
	return nil
}

func (m *SearchAIRoleResp) GetLastId() string {
	if m != nil {
		return m.LastId
	}
	return ""
}

func (m *SearchAIRoleResp) GetLoadFinish() bool {
	if m != nil {
		return m.LoadFinish
	}
	return false
}

type CreateInteractiveGameReq struct {
	Game                 *CreateInteractiveGameReq_InteractiveGame `protobuf:"bytes,1,opt,name=game,proto3" json:"game,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *CreateInteractiveGameReq) Reset()         { *m = CreateInteractiveGameReq{} }
func (m *CreateInteractiveGameReq) String() string { return proto.CompactTextString(m) }
func (*CreateInteractiveGameReq) ProtoMessage()    {}
func (*CreateInteractiveGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{89}
}
func (m *CreateInteractiveGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateInteractiveGameReq.Unmarshal(m, b)
}
func (m *CreateInteractiveGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateInteractiveGameReq.Marshal(b, m, deterministic)
}
func (dst *CreateInteractiveGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateInteractiveGameReq.Merge(dst, src)
}
func (m *CreateInteractiveGameReq) XXX_Size() int {
	return xxx_messageInfo_CreateInteractiveGameReq.Size(m)
}
func (m *CreateInteractiveGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateInteractiveGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateInteractiveGameReq proto.InternalMessageInfo

func (m *CreateInteractiveGameReq) GetGame() *CreateInteractiveGameReq_InteractiveGame {
	if m != nil {
		return m.Game
	}
	return nil
}

type CreateInteractiveGameReq_InteractiveGame struct {
	Source               InteractiveGameSource   `protobuf:"varint,1,opt,name=source,proto3,enum=aigc_soulmate.InteractiveGameSource" json:"source,omitempty"`
	RoleId               uint32                  `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	TopicId              uint32                  `protobuf:"varint,3,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Title                string                  `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	Desc                 string                  `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,omitempty"`
	Prologue             string                  `protobuf:"bytes,6,opt,name=prologue,proto3" json:"prologue,omitempty"`
	State                InteractiveGameState    `protobuf:"varint,7,opt,name=state,proto3,enum=aigc_soulmate.InteractiveGameState" json:"state,omitempty"`
	Exposure             InteractiveGameExposure `protobuf:"varint,8,opt,name=exposure,proto3,enum=aigc_soulmate.InteractiveGameExposure" json:"exposure,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *CreateInteractiveGameReq_InteractiveGame) Reset() {
	*m = CreateInteractiveGameReq_InteractiveGame{}
}
func (m *CreateInteractiveGameReq_InteractiveGame) String() string { return proto.CompactTextString(m) }
func (*CreateInteractiveGameReq_InteractiveGame) ProtoMessage()    {}
func (*CreateInteractiveGameReq_InteractiveGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{89, 0}
}
func (m *CreateInteractiveGameReq_InteractiveGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateInteractiveGameReq_InteractiveGame.Unmarshal(m, b)
}
func (m *CreateInteractiveGameReq_InteractiveGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateInteractiveGameReq_InteractiveGame.Marshal(b, m, deterministic)
}
func (dst *CreateInteractiveGameReq_InteractiveGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateInteractiveGameReq_InteractiveGame.Merge(dst, src)
}
func (m *CreateInteractiveGameReq_InteractiveGame) XXX_Size() int {
	return xxx_messageInfo_CreateInteractiveGameReq_InteractiveGame.Size(m)
}
func (m *CreateInteractiveGameReq_InteractiveGame) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateInteractiveGameReq_InteractiveGame.DiscardUnknown(m)
}

var xxx_messageInfo_CreateInteractiveGameReq_InteractiveGame proto.InternalMessageInfo

func (m *CreateInteractiveGameReq_InteractiveGame) GetSource() InteractiveGameSource {
	if m != nil {
		return m.Source
	}
	return InteractiveGameSource_InteractiveGameSourceUnspecified
}

func (m *CreateInteractiveGameReq_InteractiveGame) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *CreateInteractiveGameReq_InteractiveGame) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CreateInteractiveGameReq_InteractiveGame) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *CreateInteractiveGameReq_InteractiveGame) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *CreateInteractiveGameReq_InteractiveGame) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *CreateInteractiveGameReq_InteractiveGame) GetState() InteractiveGameState {
	if m != nil {
		return m.State
	}
	return InteractiveGameState_InteractiveGameStateUnspecified
}

func (m *CreateInteractiveGameReq_InteractiveGame) GetExposure() InteractiveGameExposure {
	if m != nil {
		return m.Exposure
	}
	return InteractiveGameExposure_InteractiveGameExposureUnspecified
}

type CreateInteractiveGameResp struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateInteractiveGameResp) Reset()         { *m = CreateInteractiveGameResp{} }
func (m *CreateInteractiveGameResp) String() string { return proto.CompactTextString(m) }
func (*CreateInteractiveGameResp) ProtoMessage()    {}
func (*CreateInteractiveGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{90}
}
func (m *CreateInteractiveGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateInteractiveGameResp.Unmarshal(m, b)
}
func (m *CreateInteractiveGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateInteractiveGameResp.Marshal(b, m, deterministic)
}
func (dst *CreateInteractiveGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateInteractiveGameResp.Merge(dst, src)
}
func (m *CreateInteractiveGameResp) XXX_Size() int {
	return xxx_messageInfo_CreateInteractiveGameResp.Size(m)
}
func (m *CreateInteractiveGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateInteractiveGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateInteractiveGameResp proto.InternalMessageInfo

func (m *CreateInteractiveGameResp) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type UpdateInteractiveGameReq struct {
	Game                 *UpdateInteractiveGameReq_InteractiveGame `protobuf:"bytes,1,opt,name=game,proto3" json:"game,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *UpdateInteractiveGameReq) Reset()         { *m = UpdateInteractiveGameReq{} }
func (m *UpdateInteractiveGameReq) String() string { return proto.CompactTextString(m) }
func (*UpdateInteractiveGameReq) ProtoMessage()    {}
func (*UpdateInteractiveGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{91}
}
func (m *UpdateInteractiveGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateInteractiveGameReq.Unmarshal(m, b)
}
func (m *UpdateInteractiveGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateInteractiveGameReq.Marshal(b, m, deterministic)
}
func (dst *UpdateInteractiveGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateInteractiveGameReq.Merge(dst, src)
}
func (m *UpdateInteractiveGameReq) XXX_Size() int {
	return xxx_messageInfo_UpdateInteractiveGameReq.Size(m)
}
func (m *UpdateInteractiveGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateInteractiveGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateInteractiveGameReq proto.InternalMessageInfo

func (m *UpdateInteractiveGameReq) GetGame() *UpdateInteractiveGameReq_InteractiveGame {
	if m != nil {
		return m.Game
	}
	return nil
}

type UpdateInteractiveGameReq_InteractiveGame struct {
	Id                   string                  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TopicId              uint32                  `protobuf:"varint,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Title                string                  `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Desc                 string                  `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	Prologue             string                  `protobuf:"bytes,5,opt,name=prologue,proto3" json:"prologue,omitempty"`
	State                InteractiveGameState    `protobuf:"varint,6,opt,name=state,proto3,enum=aigc_soulmate.InteractiveGameState" json:"state,omitempty"`
	Exposure             InteractiveGameExposure `protobuf:"varint,7,opt,name=exposure,proto3,enum=aigc_soulmate.InteractiveGameExposure" json:"exposure,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UpdateInteractiveGameReq_InteractiveGame) Reset() {
	*m = UpdateInteractiveGameReq_InteractiveGame{}
}
func (m *UpdateInteractiveGameReq_InteractiveGame) String() string { return proto.CompactTextString(m) }
func (*UpdateInteractiveGameReq_InteractiveGame) ProtoMessage()    {}
func (*UpdateInteractiveGameReq_InteractiveGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{91, 0}
}
func (m *UpdateInteractiveGameReq_InteractiveGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateInteractiveGameReq_InteractiveGame.Unmarshal(m, b)
}
func (m *UpdateInteractiveGameReq_InteractiveGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateInteractiveGameReq_InteractiveGame.Marshal(b, m, deterministic)
}
func (dst *UpdateInteractiveGameReq_InteractiveGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateInteractiveGameReq_InteractiveGame.Merge(dst, src)
}
func (m *UpdateInteractiveGameReq_InteractiveGame) XXX_Size() int {
	return xxx_messageInfo_UpdateInteractiveGameReq_InteractiveGame.Size(m)
}
func (m *UpdateInteractiveGameReq_InteractiveGame) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateInteractiveGameReq_InteractiveGame.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateInteractiveGameReq_InteractiveGame proto.InternalMessageInfo

func (m *UpdateInteractiveGameReq_InteractiveGame) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpdateInteractiveGameReq_InteractiveGame) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *UpdateInteractiveGameReq_InteractiveGame) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *UpdateInteractiveGameReq_InteractiveGame) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *UpdateInteractiveGameReq_InteractiveGame) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *UpdateInteractiveGameReq_InteractiveGame) GetState() InteractiveGameState {
	if m != nil {
		return m.State
	}
	return InteractiveGameState_InteractiveGameStateUnspecified
}

func (m *UpdateInteractiveGameReq_InteractiveGame) GetExposure() InteractiveGameExposure {
	if m != nil {
		return m.Exposure
	}
	return InteractiveGameExposure_InteractiveGameExposureUnspecified
}

type UpdateInteractiveGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateInteractiveGameResp) Reset()         { *m = UpdateInteractiveGameResp{} }
func (m *UpdateInteractiveGameResp) String() string { return proto.CompactTextString(m) }
func (*UpdateInteractiveGameResp) ProtoMessage()    {}
func (*UpdateInteractiveGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{92}
}
func (m *UpdateInteractiveGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateInteractiveGameResp.Unmarshal(m, b)
}
func (m *UpdateInteractiveGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateInteractiveGameResp.Marshal(b, m, deterministic)
}
func (dst *UpdateInteractiveGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateInteractiveGameResp.Merge(dst, src)
}
func (m *UpdateInteractiveGameResp) XXX_Size() int {
	return xxx_messageInfo_UpdateInteractiveGameResp.Size(m)
}
func (m *UpdateInteractiveGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateInteractiveGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateInteractiveGameResp proto.InternalMessageInfo

type BatchDeleteInteractiveGameReq struct {
	IdList               []string `protobuf:"bytes,1,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDeleteInteractiveGameReq) Reset()         { *m = BatchDeleteInteractiveGameReq{} }
func (m *BatchDeleteInteractiveGameReq) String() string { return proto.CompactTextString(m) }
func (*BatchDeleteInteractiveGameReq) ProtoMessage()    {}
func (*BatchDeleteInteractiveGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{93}
}
func (m *BatchDeleteInteractiveGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeleteInteractiveGameReq.Unmarshal(m, b)
}
func (m *BatchDeleteInteractiveGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeleteInteractiveGameReq.Marshal(b, m, deterministic)
}
func (dst *BatchDeleteInteractiveGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeleteInteractiveGameReq.Merge(dst, src)
}
func (m *BatchDeleteInteractiveGameReq) XXX_Size() int {
	return xxx_messageInfo_BatchDeleteInteractiveGameReq.Size(m)
}
func (m *BatchDeleteInteractiveGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeleteInteractiveGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeleteInteractiveGameReq proto.InternalMessageInfo

func (m *BatchDeleteInteractiveGameReq) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

type BatchDeleteInteractiveGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDeleteInteractiveGameResp) Reset()         { *m = BatchDeleteInteractiveGameResp{} }
func (m *BatchDeleteInteractiveGameResp) String() string { return proto.CompactTextString(m) }
func (*BatchDeleteInteractiveGameResp) ProtoMessage()    {}
func (*BatchDeleteInteractiveGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{94}
}
func (m *BatchDeleteInteractiveGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeleteInteractiveGameResp.Unmarshal(m, b)
}
func (m *BatchDeleteInteractiveGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeleteInteractiveGameResp.Marshal(b, m, deterministic)
}
func (dst *BatchDeleteInteractiveGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeleteInteractiveGameResp.Merge(dst, src)
}
func (m *BatchDeleteInteractiveGameResp) XXX_Size() int {
	return xxx_messageInfo_BatchDeleteInteractiveGameResp.Size(m)
}
func (m *BatchDeleteInteractiveGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeleteInteractiveGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeleteInteractiveGameResp proto.InternalMessageInfo

type SearchInteractiveGameReq struct {
	// 分页游标
	Cursor string `protobuf:"bytes,1,opt,name=cursor,proto3" json:"cursor,omitempty"`
	// 每页数量，上限500
	Limit uint32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	// 搜索条件
	Uid                  uint32                  `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32                  `protobuf:"varint,4,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	TopicId              uint32                  `protobuf:"varint,5,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Title                string                  `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	Desc                 string                  `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`
	Exposure             InteractiveGameExposure `protobuf:"varint,8,opt,name=exposure,proto3,enum=aigc_soulmate.InteractiveGameExposure" json:"exposure,omitempty"`
	State                InteractiveGameState    `protobuf:"varint,9,opt,name=state,proto3,enum=aigc_soulmate.InteractiveGameState" json:"state,omitempty"`
	CreatedAtRange       []int64                 `protobuf:"varint,10,rep,packed,name=created_at_range,json=createdAtRange,proto3" json:"created_at_range,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SearchInteractiveGameReq) Reset()         { *m = SearchInteractiveGameReq{} }
func (m *SearchInteractiveGameReq) String() string { return proto.CompactTextString(m) }
func (*SearchInteractiveGameReq) ProtoMessage()    {}
func (*SearchInteractiveGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{95}
}
func (m *SearchInteractiveGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchInteractiveGameReq.Unmarshal(m, b)
}
func (m *SearchInteractiveGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchInteractiveGameReq.Marshal(b, m, deterministic)
}
func (dst *SearchInteractiveGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchInteractiveGameReq.Merge(dst, src)
}
func (m *SearchInteractiveGameReq) XXX_Size() int {
	return xxx_messageInfo_SearchInteractiveGameReq.Size(m)
}
func (m *SearchInteractiveGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchInteractiveGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchInteractiveGameReq proto.InternalMessageInfo

func (m *SearchInteractiveGameReq) GetCursor() string {
	if m != nil {
		return m.Cursor
	}
	return ""
}

func (m *SearchInteractiveGameReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchInteractiveGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SearchInteractiveGameReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *SearchInteractiveGameReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *SearchInteractiveGameReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SearchInteractiveGameReq) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *SearchInteractiveGameReq) GetExposure() InteractiveGameExposure {
	if m != nil {
		return m.Exposure
	}
	return InteractiveGameExposure_InteractiveGameExposureUnspecified
}

func (m *SearchInteractiveGameReq) GetState() InteractiveGameState {
	if m != nil {
		return m.State
	}
	return InteractiveGameState_InteractiveGameStateUnspecified
}

func (m *SearchInteractiveGameReq) GetCreatedAtRange() []int64 {
	if m != nil {
		return m.CreatedAtRange
	}
	return nil
}

type SearchInteractiveGameResp struct {
	List []*InteractiveGame `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 用于下一页请求，最后一页为空
	Cursor               string   `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchInteractiveGameResp) Reset()         { *m = SearchInteractiveGameResp{} }
func (m *SearchInteractiveGameResp) String() string { return proto.CompactTextString(m) }
func (*SearchInteractiveGameResp) ProtoMessage()    {}
func (*SearchInteractiveGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{96}
}
func (m *SearchInteractiveGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchInteractiveGameResp.Unmarshal(m, b)
}
func (m *SearchInteractiveGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchInteractiveGameResp.Marshal(b, m, deterministic)
}
func (dst *SearchInteractiveGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchInteractiveGameResp.Merge(dst, src)
}
func (m *SearchInteractiveGameResp) XXX_Size() int {
	return xxx_messageInfo_SearchInteractiveGameResp.Size(m)
}
func (m *SearchInteractiveGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchInteractiveGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchInteractiveGameResp proto.InternalMessageInfo

func (m *SearchInteractiveGameResp) GetList() []*InteractiveGame {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *SearchInteractiveGameResp) GetCursor() string {
	if m != nil {
		return m.Cursor
	}
	return ""
}

type BatchUpdateInteractiveGameReq struct {
	Games                []*BatchUpdateInteractiveGameReq_InteractiveGame `protobuf:"bytes,1,rep,name=games,proto3" json:"games,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                         `json:"-"`
	XXX_unrecognized     []byte                                           `json:"-"`
	XXX_sizecache        int32                                            `json:"-"`
}

func (m *BatchUpdateInteractiveGameReq) Reset()         { *m = BatchUpdateInteractiveGameReq{} }
func (m *BatchUpdateInteractiveGameReq) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateInteractiveGameReq) ProtoMessage()    {}
func (*BatchUpdateInteractiveGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{97}
}
func (m *BatchUpdateInteractiveGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateInteractiveGameReq.Unmarshal(m, b)
}
func (m *BatchUpdateInteractiveGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateInteractiveGameReq.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateInteractiveGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateInteractiveGameReq.Merge(dst, src)
}
func (m *BatchUpdateInteractiveGameReq) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateInteractiveGameReq.Size(m)
}
func (m *BatchUpdateInteractiveGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateInteractiveGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateInteractiveGameReq proto.InternalMessageInfo

func (m *BatchUpdateInteractiveGameReq) GetGames() []*BatchUpdateInteractiveGameReq_InteractiveGame {
	if m != nil {
		return m.Games
	}
	return nil
}

type BatchUpdateInteractiveGameReq_InteractiveGame struct {
	Id                   string                  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Exposure             InteractiveGameExposure `protobuf:"varint,3,opt,name=exposure,proto3,enum=aigc_soulmate.InteractiveGameExposure" json:"exposure,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchUpdateInteractiveGameReq_InteractiveGame) Reset() {
	*m = BatchUpdateInteractiveGameReq_InteractiveGame{}
}
func (m *BatchUpdateInteractiveGameReq_InteractiveGame) String() string {
	return proto.CompactTextString(m)
}
func (*BatchUpdateInteractiveGameReq_InteractiveGame) ProtoMessage() {}
func (*BatchUpdateInteractiveGameReq_InteractiveGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{97, 0}
}
func (m *BatchUpdateInteractiveGameReq_InteractiveGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateInteractiveGameReq_InteractiveGame.Unmarshal(m, b)
}
func (m *BatchUpdateInteractiveGameReq_InteractiveGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateInteractiveGameReq_InteractiveGame.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateInteractiveGameReq_InteractiveGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateInteractiveGameReq_InteractiveGame.Merge(dst, src)
}
func (m *BatchUpdateInteractiveGameReq_InteractiveGame) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateInteractiveGameReq_InteractiveGame.Size(m)
}
func (m *BatchUpdateInteractiveGameReq_InteractiveGame) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateInteractiveGameReq_InteractiveGame.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateInteractiveGameReq_InteractiveGame proto.InternalMessageInfo

func (m *BatchUpdateInteractiveGameReq_InteractiveGame) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *BatchUpdateInteractiveGameReq_InteractiveGame) GetExposure() InteractiveGameExposure {
	if m != nil {
		return m.Exposure
	}
	return InteractiveGameExposure_InteractiveGameExposureUnspecified
}

type BatchUpdateInteractiveGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpdateInteractiveGameResp) Reset()         { *m = BatchUpdateInteractiveGameResp{} }
func (m *BatchUpdateInteractiveGameResp) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateInteractiveGameResp) ProtoMessage()    {}
func (*BatchUpdateInteractiveGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{98}
}
func (m *BatchUpdateInteractiveGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateInteractiveGameResp.Unmarshal(m, b)
}
func (m *BatchUpdateInteractiveGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateInteractiveGameResp.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateInteractiveGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateInteractiveGameResp.Merge(dst, src)
}
func (m *BatchUpdateInteractiveGameResp) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateInteractiveGameResp.Size(m)
}
func (m *BatchUpdateInteractiveGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateInteractiveGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateInteractiveGameResp proto.InternalMessageInfo

type GetUserInteractiveGameListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInteractiveGameListReq) Reset()         { *m = GetUserInteractiveGameListReq{} }
func (m *GetUserInteractiveGameListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserInteractiveGameListReq) ProtoMessage()    {}
func (*GetUserInteractiveGameListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{99}
}
func (m *GetUserInteractiveGameListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInteractiveGameListReq.Unmarshal(m, b)
}
func (m *GetUserInteractiveGameListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInteractiveGameListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserInteractiveGameListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInteractiveGameListReq.Merge(dst, src)
}
func (m *GetUserInteractiveGameListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserInteractiveGameListReq.Size(m)
}
func (m *GetUserInteractiveGameListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInteractiveGameListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInteractiveGameListReq proto.InternalMessageInfo

func (m *GetUserInteractiveGameListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserInteractiveGameListResp struct {
	List                 []*InteractiveGame `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetUserInteractiveGameListResp) Reset()         { *m = GetUserInteractiveGameListResp{} }
func (m *GetUserInteractiveGameListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserInteractiveGameListResp) ProtoMessage()    {}
func (*GetUserInteractiveGameListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{100}
}
func (m *GetUserInteractiveGameListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInteractiveGameListResp.Unmarshal(m, b)
}
func (m *GetUserInteractiveGameListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInteractiveGameListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserInteractiveGameListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInteractiveGameListResp.Merge(dst, src)
}
func (m *GetUserInteractiveGameListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserInteractiveGameListResp.Size(m)
}
func (m *GetUserInteractiveGameListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInteractiveGameListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInteractiveGameListResp proto.InternalMessageInfo

func (m *GetUserInteractiveGameListResp) GetList() []*InteractiveGame {
	if m != nil {
		return m.List
	}
	return nil
}

type GetInteractiveGameReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInteractiveGameReq) Reset()         { *m = GetInteractiveGameReq{} }
func (m *GetInteractiveGameReq) String() string { return proto.CompactTextString(m) }
func (*GetInteractiveGameReq) ProtoMessage()    {}
func (*GetInteractiveGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{101}
}
func (m *GetInteractiveGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractiveGameReq.Unmarshal(m, b)
}
func (m *GetInteractiveGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractiveGameReq.Marshal(b, m, deterministic)
}
func (dst *GetInteractiveGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractiveGameReq.Merge(dst, src)
}
func (m *GetInteractiveGameReq) XXX_Size() int {
	return xxx_messageInfo_GetInteractiveGameReq.Size(m)
}
func (m *GetInteractiveGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractiveGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractiveGameReq proto.InternalMessageInfo

func (m *GetInteractiveGameReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type GetInteractiveGameResp struct {
	Game                 *InteractiveGame `protobuf:"bytes,1,opt,name=game,proto3" json:"game,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetInteractiveGameResp) Reset()         { *m = GetInteractiveGameResp{} }
func (m *GetInteractiveGameResp) String() string { return proto.CompactTextString(m) }
func (*GetInteractiveGameResp) ProtoMessage()    {}
func (*GetInteractiveGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{102}
}
func (m *GetInteractiveGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractiveGameResp.Unmarshal(m, b)
}
func (m *GetInteractiveGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractiveGameResp.Marshal(b, m, deterministic)
}
func (dst *GetInteractiveGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractiveGameResp.Merge(dst, src)
}
func (m *GetInteractiveGameResp) XXX_Size() int {
	return xxx_messageInfo_GetInteractiveGameResp.Size(m)
}
func (m *GetInteractiveGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractiveGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractiveGameResp proto.InternalMessageInfo

func (m *GetInteractiveGameResp) GetGame() *InteractiveGame {
	if m != nil {
		return m.Game
	}
	return nil
}

type BatchCreateRoleReq struct {
	RoleList             []*CreateAIRoleReq_Role `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchCreateRoleReq) Reset()         { *m = BatchCreateRoleReq{} }
func (m *BatchCreateRoleReq) String() string { return proto.CompactTextString(m) }
func (*BatchCreateRoleReq) ProtoMessage()    {}
func (*BatchCreateRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{103}
}
func (m *BatchCreateRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCreateRoleReq.Unmarshal(m, b)
}
func (m *BatchCreateRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCreateRoleReq.Marshal(b, m, deterministic)
}
func (dst *BatchCreateRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCreateRoleReq.Merge(dst, src)
}
func (m *BatchCreateRoleReq) XXX_Size() int {
	return xxx_messageInfo_BatchCreateRoleReq.Size(m)
}
func (m *BatchCreateRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCreateRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCreateRoleReq proto.InternalMessageInfo

func (m *BatchCreateRoleReq) GetRoleList() []*CreateAIRoleReq_Role {
	if m != nil {
		return m.RoleList
	}
	return nil
}

type BatchCreateRoleResp struct {
	RoleIds              []uint32 `protobuf:"varint,1,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchCreateRoleResp) Reset()         { *m = BatchCreateRoleResp{} }
func (m *BatchCreateRoleResp) String() string { return proto.CompactTextString(m) }
func (*BatchCreateRoleResp) ProtoMessage()    {}
func (*BatchCreateRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{104}
}
func (m *BatchCreateRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCreateRoleResp.Unmarshal(m, b)
}
func (m *BatchCreateRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCreateRoleResp.Marshal(b, m, deterministic)
}
func (dst *BatchCreateRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCreateRoleResp.Merge(dst, src)
}
func (m *BatchCreateRoleResp) XXX_Size() int {
	return xxx_messageInfo_BatchCreateRoleResp.Size(m)
}
func (m *BatchCreateRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCreateRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCreateRoleResp proto.InternalMessageInfo

func (m *BatchCreateRoleResp) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

type BatchUpdateRoleReq struct {
	RoleList             []*UpdateAIRoleReq_Role `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchUpdateRoleReq) Reset()         { *m = BatchUpdateRoleReq{} }
func (m *BatchUpdateRoleReq) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateRoleReq) ProtoMessage()    {}
func (*BatchUpdateRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{105}
}
func (m *BatchUpdateRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateRoleReq.Unmarshal(m, b)
}
func (m *BatchUpdateRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateRoleReq.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateRoleReq.Merge(dst, src)
}
func (m *BatchUpdateRoleReq) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateRoleReq.Size(m)
}
func (m *BatchUpdateRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateRoleReq proto.InternalMessageInfo

func (m *BatchUpdateRoleReq) GetRoleList() []*UpdateAIRoleReq_Role {
	if m != nil {
		return m.RoleList
	}
	return nil
}

type BatchUpdateRoleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpdateRoleResp) Reset()         { *m = BatchUpdateRoleResp{} }
func (m *BatchUpdateRoleResp) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateRoleResp) ProtoMessage()    {}
func (*BatchUpdateRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{106}
}
func (m *BatchUpdateRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateRoleResp.Unmarshal(m, b)
}
func (m *BatchUpdateRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateRoleResp.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateRoleResp.Merge(dst, src)
}
func (m *BatchUpdateRoleResp) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateRoleResp.Size(m)
}
func (m *BatchUpdateRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateRoleResp proto.InternalMessageInfo

type AIChatTemplate struct {
	BaseTemplate         *BaseChatTemplate `protobuf:"bytes,1,opt,name=base_template,json=baseTemplate,proto3" json:"base_template,omitempty"`
	BindEntities         []*BindEntity     `protobuf:"bytes,2,rep,name=bind_entities,json=bindEntities,proto3" json:"bind_entities,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AIChatTemplate) Reset()         { *m = AIChatTemplate{} }
func (m *AIChatTemplate) String() string { return proto.CompactTextString(m) }
func (*AIChatTemplate) ProtoMessage()    {}
func (*AIChatTemplate) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{107}
}
func (m *AIChatTemplate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIChatTemplate.Unmarshal(m, b)
}
func (m *AIChatTemplate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIChatTemplate.Marshal(b, m, deterministic)
}
func (dst *AIChatTemplate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIChatTemplate.Merge(dst, src)
}
func (m *AIChatTemplate) XXX_Size() int {
	return xxx_messageInfo_AIChatTemplate.Size(m)
}
func (m *AIChatTemplate) XXX_DiscardUnknown() {
	xxx_messageInfo_AIChatTemplate.DiscardUnknown(m)
}

var xxx_messageInfo_AIChatTemplate proto.InternalMessageInfo

func (m *AIChatTemplate) GetBaseTemplate() *BaseChatTemplate {
	if m != nil {
		return m.BaseTemplate
	}
	return nil
}

func (m *AIChatTemplate) GetBindEntities() []*BindEntity {
	if m != nil {
		return m.BindEntities
	}
	return nil
}

type BindEntity struct {
	EntityType           BindEntity_EntityType `protobuf:"varint,1,opt,name=entity_type,json=entityType,proto3,enum=aigc_soulmate.BindEntity_EntityType" json:"entity_type,omitempty"`
	Id                   uint32                `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BindEntity) Reset()         { *m = BindEntity{} }
func (m *BindEntity) String() string { return proto.CompactTextString(m) }
func (*BindEntity) ProtoMessage()    {}
func (*BindEntity) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{108}
}
func (m *BindEntity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindEntity.Unmarshal(m, b)
}
func (m *BindEntity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindEntity.Marshal(b, m, deterministic)
}
func (dst *BindEntity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindEntity.Merge(dst, src)
}
func (m *BindEntity) XXX_Size() int {
	return xxx_messageInfo_BindEntity.Size(m)
}
func (m *BindEntity) XXX_DiscardUnknown() {
	xxx_messageInfo_BindEntity.DiscardUnknown(m)
}

var xxx_messageInfo_BindEntity proto.InternalMessageInfo

func (m *BindEntity) GetEntityType() BindEntity_EntityType {
	if m != nil {
		return m.EntityType
	}
	return BindEntity_EntityType_Unspecified
}

func (m *BindEntity) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type BaseChatTemplate struct {
	Id                   string         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string         `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Msgs                 []*TemplateMsg `protobuf:"bytes,3,rep,name=msgs,proto3" json:"msgs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BaseChatTemplate) Reset()         { *m = BaseChatTemplate{} }
func (m *BaseChatTemplate) String() string { return proto.CompactTextString(m) }
func (*BaseChatTemplate) ProtoMessage()    {}
func (*BaseChatTemplate) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{109}
}
func (m *BaseChatTemplate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseChatTemplate.Unmarshal(m, b)
}
func (m *BaseChatTemplate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseChatTemplate.Marshal(b, m, deterministic)
}
func (dst *BaseChatTemplate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseChatTemplate.Merge(dst, src)
}
func (m *BaseChatTemplate) XXX_Size() int {
	return xxx_messageInfo_BaseChatTemplate.Size(m)
}
func (m *BaseChatTemplate) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseChatTemplate.DiscardUnknown(m)
}

var xxx_messageInfo_BaseChatTemplate proto.InternalMessageInfo

func (m *BaseChatTemplate) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *BaseChatTemplate) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *BaseChatTemplate) GetMsgs() []*TemplateMsg {
	if m != nil {
		return m.Msgs
	}
	return nil
}

type TemplateMsg struct {
	Content              string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	SenderType           TemplateMsg_SenderType `protobuf:"varint,2,opt,name=sender_type,json=senderType,proto3,enum=aigc_soulmate.TemplateMsg_SenderType" json:"sender_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *TemplateMsg) Reset()         { *m = TemplateMsg{} }
func (m *TemplateMsg) String() string { return proto.CompactTextString(m) }
func (*TemplateMsg) ProtoMessage()    {}
func (*TemplateMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{110}
}
func (m *TemplateMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TemplateMsg.Unmarshal(m, b)
}
func (m *TemplateMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TemplateMsg.Marshal(b, m, deterministic)
}
func (dst *TemplateMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TemplateMsg.Merge(dst, src)
}
func (m *TemplateMsg) XXX_Size() int {
	return xxx_messageInfo_TemplateMsg.Size(m)
}
func (m *TemplateMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_TemplateMsg.DiscardUnknown(m)
}

var xxx_messageInfo_TemplateMsg proto.InternalMessageInfo

func (m *TemplateMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *TemplateMsg) GetSenderType() TemplateMsg_SenderType {
	if m != nil {
		return m.SenderType
	}
	return TemplateMsg_SenderType_Unspecified
}

type CreateAIChatTemplateReq struct {
	ChatTemplate         *AIChatTemplate `protobuf:"bytes,1,opt,name=chat_template,json=chatTemplate,proto3" json:"chat_template,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CreateAIChatTemplateReq) Reset()         { *m = CreateAIChatTemplateReq{} }
func (m *CreateAIChatTemplateReq) String() string { return proto.CompactTextString(m) }
func (*CreateAIChatTemplateReq) ProtoMessage()    {}
func (*CreateAIChatTemplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{111}
}
func (m *CreateAIChatTemplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIChatTemplateReq.Unmarshal(m, b)
}
func (m *CreateAIChatTemplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIChatTemplateReq.Marshal(b, m, deterministic)
}
func (dst *CreateAIChatTemplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIChatTemplateReq.Merge(dst, src)
}
func (m *CreateAIChatTemplateReq) XXX_Size() int {
	return xxx_messageInfo_CreateAIChatTemplateReq.Size(m)
}
func (m *CreateAIChatTemplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIChatTemplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIChatTemplateReq proto.InternalMessageInfo

func (m *CreateAIChatTemplateReq) GetChatTemplate() *AIChatTemplate {
	if m != nil {
		return m.ChatTemplate
	}
	return nil
}

type CreateAIChatTemplateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateAIChatTemplateResp) Reset()         { *m = CreateAIChatTemplateResp{} }
func (m *CreateAIChatTemplateResp) String() string { return proto.CompactTextString(m) }
func (*CreateAIChatTemplateResp) ProtoMessage()    {}
func (*CreateAIChatTemplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{112}
}
func (m *CreateAIChatTemplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIChatTemplateResp.Unmarshal(m, b)
}
func (m *CreateAIChatTemplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIChatTemplateResp.Marshal(b, m, deterministic)
}
func (dst *CreateAIChatTemplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIChatTemplateResp.Merge(dst, src)
}
func (m *CreateAIChatTemplateResp) XXX_Size() int {
	return xxx_messageInfo_CreateAIChatTemplateResp.Size(m)
}
func (m *CreateAIChatTemplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIChatTemplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIChatTemplateResp proto.InternalMessageInfo

type UpdateAIChatTemplateReq struct {
	ChatTemplate         *AIChatTemplate `protobuf:"bytes,1,opt,name=chat_template,json=chatTemplate,proto3" json:"chat_template,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpdateAIChatTemplateReq) Reset()         { *m = UpdateAIChatTemplateReq{} }
func (m *UpdateAIChatTemplateReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAIChatTemplateReq) ProtoMessage()    {}
func (*UpdateAIChatTemplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{113}
}
func (m *UpdateAIChatTemplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIChatTemplateReq.Unmarshal(m, b)
}
func (m *UpdateAIChatTemplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIChatTemplateReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAIChatTemplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIChatTemplateReq.Merge(dst, src)
}
func (m *UpdateAIChatTemplateReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAIChatTemplateReq.Size(m)
}
func (m *UpdateAIChatTemplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIChatTemplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIChatTemplateReq proto.InternalMessageInfo

func (m *UpdateAIChatTemplateReq) GetChatTemplate() *AIChatTemplate {
	if m != nil {
		return m.ChatTemplate
	}
	return nil
}

type UpdateAIChatTemplateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAIChatTemplateResp) Reset()         { *m = UpdateAIChatTemplateResp{} }
func (m *UpdateAIChatTemplateResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAIChatTemplateResp) ProtoMessage()    {}
func (*UpdateAIChatTemplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{114}
}
func (m *UpdateAIChatTemplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIChatTemplateResp.Unmarshal(m, b)
}
func (m *UpdateAIChatTemplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIChatTemplateResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAIChatTemplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIChatTemplateResp.Merge(dst, src)
}
func (m *UpdateAIChatTemplateResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAIChatTemplateResp.Size(m)
}
func (m *UpdateAIChatTemplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIChatTemplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIChatTemplateResp proto.InternalMessageInfo

type BatchDeleteAIChatTemplateReq struct {
	IdList               []string `protobuf:"bytes,1,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDeleteAIChatTemplateReq) Reset()         { *m = BatchDeleteAIChatTemplateReq{} }
func (m *BatchDeleteAIChatTemplateReq) String() string { return proto.CompactTextString(m) }
func (*BatchDeleteAIChatTemplateReq) ProtoMessage()    {}
func (*BatchDeleteAIChatTemplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{115}
}
func (m *BatchDeleteAIChatTemplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeleteAIChatTemplateReq.Unmarshal(m, b)
}
func (m *BatchDeleteAIChatTemplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeleteAIChatTemplateReq.Marshal(b, m, deterministic)
}
func (dst *BatchDeleteAIChatTemplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeleteAIChatTemplateReq.Merge(dst, src)
}
func (m *BatchDeleteAIChatTemplateReq) XXX_Size() int {
	return xxx_messageInfo_BatchDeleteAIChatTemplateReq.Size(m)
}
func (m *BatchDeleteAIChatTemplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeleteAIChatTemplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeleteAIChatTemplateReq proto.InternalMessageInfo

func (m *BatchDeleteAIChatTemplateReq) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

type BatchDeleteAIChatTemplateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDeleteAIChatTemplateResp) Reset()         { *m = BatchDeleteAIChatTemplateResp{} }
func (m *BatchDeleteAIChatTemplateResp) String() string { return proto.CompactTextString(m) }
func (*BatchDeleteAIChatTemplateResp) ProtoMessage()    {}
func (*BatchDeleteAIChatTemplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{116}
}
func (m *BatchDeleteAIChatTemplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeleteAIChatTemplateResp.Unmarshal(m, b)
}
func (m *BatchDeleteAIChatTemplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeleteAIChatTemplateResp.Marshal(b, m, deterministic)
}
func (dst *BatchDeleteAIChatTemplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeleteAIChatTemplateResp.Merge(dst, src)
}
func (m *BatchDeleteAIChatTemplateResp) XXX_Size() int {
	return xxx_messageInfo_BatchDeleteAIChatTemplateResp.Size(m)
}
func (m *BatchDeleteAIChatTemplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeleteAIChatTemplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeleteAIChatTemplateResp proto.InternalMessageInfo

type GetAIChatTemplateListReq struct {
	// 分页页码
	Page uint32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 分页数量，上限100
	Limit uint32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	// 是否需要总数, 拉取第一页时需要
	NeedCount bool `protobuf:"varint,3,opt,name=need_count,json=needCount,proto3" json:"need_count,omitempty"`
	// 根据模版id筛选
	TemplateId           string   `protobuf:"bytes,4,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAIChatTemplateListReq) Reset()         { *m = GetAIChatTemplateListReq{} }
func (m *GetAIChatTemplateListReq) String() string { return proto.CompactTextString(m) }
func (*GetAIChatTemplateListReq) ProtoMessage()    {}
func (*GetAIChatTemplateListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{117}
}
func (m *GetAIChatTemplateListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIChatTemplateListReq.Unmarshal(m, b)
}
func (m *GetAIChatTemplateListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIChatTemplateListReq.Marshal(b, m, deterministic)
}
func (dst *GetAIChatTemplateListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIChatTemplateListReq.Merge(dst, src)
}
func (m *GetAIChatTemplateListReq) XXX_Size() int {
	return xxx_messageInfo_GetAIChatTemplateListReq.Size(m)
}
func (m *GetAIChatTemplateListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIChatTemplateListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIChatTemplateListReq proto.InternalMessageInfo

func (m *GetAIChatTemplateListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetAIChatTemplateListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetAIChatTemplateListReq) GetNeedCount() bool {
	if m != nil {
		return m.NeedCount
	}
	return false
}

func (m *GetAIChatTemplateListReq) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

type GetAIChatTemplateListResp struct {
	ChatTemplates        []*AIChatTemplate `protobuf:"bytes,1,rep,name=chat_templates,json=chatTemplates,proto3" json:"chat_templates,omitempty"`
	Total                uint32            `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAIChatTemplateListResp) Reset()         { *m = GetAIChatTemplateListResp{} }
func (m *GetAIChatTemplateListResp) String() string { return proto.CompactTextString(m) }
func (*GetAIChatTemplateListResp) ProtoMessage()    {}
func (*GetAIChatTemplateListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{118}
}
func (m *GetAIChatTemplateListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIChatTemplateListResp.Unmarshal(m, b)
}
func (m *GetAIChatTemplateListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIChatTemplateListResp.Marshal(b, m, deterministic)
}
func (dst *GetAIChatTemplateListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIChatTemplateListResp.Merge(dst, src)
}
func (m *GetAIChatTemplateListResp) XXX_Size() int {
	return xxx_messageInfo_GetAIChatTemplateListResp.Size(m)
}
func (m *GetAIChatTemplateListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIChatTemplateListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIChatTemplateListResp proto.InternalMessageInfo

func (m *GetAIChatTemplateListResp) GetChatTemplates() []*AIChatTemplate {
	if m != nil {
		return m.ChatTemplates
	}
	return nil
}

func (m *GetAIChatTemplateListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetBindChatTemplatesReq struct {
	Entity               *BindEntity `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetBindChatTemplatesReq) Reset()         { *m = GetBindChatTemplatesReq{} }
func (m *GetBindChatTemplatesReq) String() string { return proto.CompactTextString(m) }
func (*GetBindChatTemplatesReq) ProtoMessage()    {}
func (*GetBindChatTemplatesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{119}
}
func (m *GetBindChatTemplatesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindChatTemplatesReq.Unmarshal(m, b)
}
func (m *GetBindChatTemplatesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindChatTemplatesReq.Marshal(b, m, deterministic)
}
func (dst *GetBindChatTemplatesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindChatTemplatesReq.Merge(dst, src)
}
func (m *GetBindChatTemplatesReq) XXX_Size() int {
	return xxx_messageInfo_GetBindChatTemplatesReq.Size(m)
}
func (m *GetBindChatTemplatesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindChatTemplatesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindChatTemplatesReq proto.InternalMessageInfo

func (m *GetBindChatTemplatesReq) GetEntity() *BindEntity {
	if m != nil {
		return m.Entity
	}
	return nil
}

type GetBindChatTemplatesResp struct {
	ChatTemplates        []*BaseChatTemplate `protobuf:"bytes,1,rep,name=chat_templates,json=chatTemplates,proto3" json:"chat_templates,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetBindChatTemplatesResp) Reset()         { *m = GetBindChatTemplatesResp{} }
func (m *GetBindChatTemplatesResp) String() string { return proto.CompactTextString(m) }
func (*GetBindChatTemplatesResp) ProtoMessage()    {}
func (*GetBindChatTemplatesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{120}
}
func (m *GetBindChatTemplatesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindChatTemplatesResp.Unmarshal(m, b)
}
func (m *GetBindChatTemplatesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindChatTemplatesResp.Marshal(b, m, deterministic)
}
func (dst *GetBindChatTemplatesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindChatTemplatesResp.Merge(dst, src)
}
func (m *GetBindChatTemplatesResp) XXX_Size() int {
	return xxx_messageInfo_GetBindChatTemplatesResp.Size(m)
}
func (m *GetBindChatTemplatesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindChatTemplatesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindChatTemplatesResp proto.InternalMessageInfo

func (m *GetBindChatTemplatesResp) GetChatTemplates() []*BaseChatTemplate {
	if m != nil {
		return m.ChatTemplates
	}
	return nil
}

type GetReadHeartEntranceRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	MsgId                string   `protobuf:"bytes,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	PartnerId            uint32   `protobuf:"varint,4,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReadHeartEntranceRequest) Reset()         { *m = GetReadHeartEntranceRequest{} }
func (m *GetReadHeartEntranceRequest) String() string { return proto.CompactTextString(m) }
func (*GetReadHeartEntranceRequest) ProtoMessage()    {}
func (*GetReadHeartEntranceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{121}
}
func (m *GetReadHeartEntranceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReadHeartEntranceRequest.Unmarshal(m, b)
}
func (m *GetReadHeartEntranceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReadHeartEntranceRequest.Marshal(b, m, deterministic)
}
func (dst *GetReadHeartEntranceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReadHeartEntranceRequest.Merge(dst, src)
}
func (m *GetReadHeartEntranceRequest) XXX_Size() int {
	return xxx_messageInfo_GetReadHeartEntranceRequest.Size(m)
}
func (m *GetReadHeartEntranceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReadHeartEntranceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetReadHeartEntranceRequest proto.InternalMessageInfo

func (m *GetReadHeartEntranceRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetReadHeartEntranceRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetReadHeartEntranceRequest) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *GetReadHeartEntranceRequest) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type GetReadHeartEntranceResponse struct {
	IsReadHeartEntrance  bool     `protobuf:"varint,1,opt,name=is_read_heart_entrance,json=isReadHeartEntrance,proto3" json:"is_read_heart_entrance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReadHeartEntranceResponse) Reset()         { *m = GetReadHeartEntranceResponse{} }
func (m *GetReadHeartEntranceResponse) String() string { return proto.CompactTextString(m) }
func (*GetReadHeartEntranceResponse) ProtoMessage()    {}
func (*GetReadHeartEntranceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{122}
}
func (m *GetReadHeartEntranceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReadHeartEntranceResponse.Unmarshal(m, b)
}
func (m *GetReadHeartEntranceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReadHeartEntranceResponse.Marshal(b, m, deterministic)
}
func (dst *GetReadHeartEntranceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReadHeartEntranceResponse.Merge(dst, src)
}
func (m *GetReadHeartEntranceResponse) XXX_Size() int {
	return xxx_messageInfo_GetReadHeartEntranceResponse.Size(m)
}
func (m *GetReadHeartEntranceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReadHeartEntranceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetReadHeartEntranceResponse proto.InternalMessageInfo

func (m *GetReadHeartEntranceResponse) GetIsReadHeartEntrance() bool {
	if m != nil {
		return m.IsReadHeartEntrance
	}
	return false
}

type GetUserReadHeartCntRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserReadHeartCntRequest) Reset()         { *m = GetUserReadHeartCntRequest{} }
func (m *GetUserReadHeartCntRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserReadHeartCntRequest) ProtoMessage()    {}
func (*GetUserReadHeartCntRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{123}
}
func (m *GetUserReadHeartCntRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserReadHeartCntRequest.Unmarshal(m, b)
}
func (m *GetUserReadHeartCntRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserReadHeartCntRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserReadHeartCntRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserReadHeartCntRequest.Merge(dst, src)
}
func (m *GetUserReadHeartCntRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserReadHeartCntRequest.Size(m)
}
func (m *GetUserReadHeartCntRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserReadHeartCntRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserReadHeartCntRequest proto.InternalMessageInfo

func (m *GetUserReadHeartCntRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserReadHeartCntRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetUserReadHeartCntResponse struct {
	CommonCnt            uint32   `protobuf:"varint,1,opt,name=common_cnt,json=commonCnt,proto3" json:"common_cnt,omitempty"`
	RoleCnt              uint32   `protobuf:"varint,2,opt,name=role_cnt,json=roleCnt,proto3" json:"role_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserReadHeartCntResponse) Reset()         { *m = GetUserReadHeartCntResponse{} }
func (m *GetUserReadHeartCntResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserReadHeartCntResponse) ProtoMessage()    {}
func (*GetUserReadHeartCntResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{124}
}
func (m *GetUserReadHeartCntResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserReadHeartCntResponse.Unmarshal(m, b)
}
func (m *GetUserReadHeartCntResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserReadHeartCntResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserReadHeartCntResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserReadHeartCntResponse.Merge(dst, src)
}
func (m *GetUserReadHeartCntResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserReadHeartCntResponse.Size(m)
}
func (m *GetUserReadHeartCntResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserReadHeartCntResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserReadHeartCntResponse proto.InternalMessageInfo

func (m *GetUserReadHeartCntResponse) GetCommonCnt() uint32 {
	if m != nil {
		return m.CommonCnt
	}
	return 0
}

func (m *GetUserReadHeartCntResponse) GetRoleCnt() uint32 {
	if m != nil {
		return m.RoleCnt
	}
	return 0
}

type AddReadHeartTextRequest struct {
	Uid                   uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MsgId                 string   `protobuf:"bytes,2,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	Text                  string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	RoleId                uint32   `protobuf:"varint,4,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	DeductionHeartCntType uint32   `protobuf:"varint,5,opt,name=deduction_heart_cnt_type,json=deductionHeartCntType,proto3" json:"deduction_heart_cnt_type,omitempty"`
	PartnerId             uint32   `protobuf:"varint,6,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *AddReadHeartTextRequest) Reset()         { *m = AddReadHeartTextRequest{} }
func (m *AddReadHeartTextRequest) String() string { return proto.CompactTextString(m) }
func (*AddReadHeartTextRequest) ProtoMessage()    {}
func (*AddReadHeartTextRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{125}
}
func (m *AddReadHeartTextRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddReadHeartTextRequest.Unmarshal(m, b)
}
func (m *AddReadHeartTextRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddReadHeartTextRequest.Marshal(b, m, deterministic)
}
func (dst *AddReadHeartTextRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddReadHeartTextRequest.Merge(dst, src)
}
func (m *AddReadHeartTextRequest) XXX_Size() int {
	return xxx_messageInfo_AddReadHeartTextRequest.Size(m)
}
func (m *AddReadHeartTextRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddReadHeartTextRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddReadHeartTextRequest proto.InternalMessageInfo

func (m *AddReadHeartTextRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddReadHeartTextRequest) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *AddReadHeartTextRequest) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *AddReadHeartTextRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *AddReadHeartTextRequest) GetDeductionHeartCntType() uint32 {
	if m != nil {
		return m.DeductionHeartCntType
	}
	return 0
}

func (m *AddReadHeartTextRequest) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type AddReadHeartTextResponse struct {
	NewCommonCnt         uint32   `protobuf:"varint,1,opt,name=new_common_cnt,json=newCommonCnt,proto3" json:"new_common_cnt,omitempty"`
	NewRoleCnt           uint32   `protobuf:"varint,2,opt,name=new_role_cnt,json=newRoleCnt,proto3" json:"new_role_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddReadHeartTextResponse) Reset()         { *m = AddReadHeartTextResponse{} }
func (m *AddReadHeartTextResponse) String() string { return proto.CompactTextString(m) }
func (*AddReadHeartTextResponse) ProtoMessage()    {}
func (*AddReadHeartTextResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{126}
}
func (m *AddReadHeartTextResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddReadHeartTextResponse.Unmarshal(m, b)
}
func (m *AddReadHeartTextResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddReadHeartTextResponse.Marshal(b, m, deterministic)
}
func (dst *AddReadHeartTextResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddReadHeartTextResponse.Merge(dst, src)
}
func (m *AddReadHeartTextResponse) XXX_Size() int {
	return xxx_messageInfo_AddReadHeartTextResponse.Size(m)
}
func (m *AddReadHeartTextResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddReadHeartTextResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddReadHeartTextResponse proto.InternalMessageInfo

func (m *AddReadHeartTextResponse) GetNewCommonCnt() uint32 {
	if m != nil {
		return m.NewCommonCnt
	}
	return 0
}

func (m *AddReadHeartTextResponse) GetNewRoleCnt() uint32 {
	if m != nil {
		return m.NewRoleCnt
	}
	return 0
}

type UpdateEntranceBanRoleIdRequest struct {
	RoleIds              []uint32 `protobuf:"varint,1,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateEntranceBanRoleIdRequest) Reset()         { *m = UpdateEntranceBanRoleIdRequest{} }
func (m *UpdateEntranceBanRoleIdRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateEntranceBanRoleIdRequest) ProtoMessage()    {}
func (*UpdateEntranceBanRoleIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{127}
}
func (m *UpdateEntranceBanRoleIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateEntranceBanRoleIdRequest.Unmarshal(m, b)
}
func (m *UpdateEntranceBanRoleIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateEntranceBanRoleIdRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateEntranceBanRoleIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateEntranceBanRoleIdRequest.Merge(dst, src)
}
func (m *UpdateEntranceBanRoleIdRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateEntranceBanRoleIdRequest.Size(m)
}
func (m *UpdateEntranceBanRoleIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateEntranceBanRoleIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateEntranceBanRoleIdRequest proto.InternalMessageInfo

func (m *UpdateEntranceBanRoleIdRequest) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

type UpdateEntranceBanRoleIdResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateEntranceBanRoleIdResponse) Reset()         { *m = UpdateEntranceBanRoleIdResponse{} }
func (m *UpdateEntranceBanRoleIdResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateEntranceBanRoleIdResponse) ProtoMessage()    {}
func (*UpdateEntranceBanRoleIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{128}
}
func (m *UpdateEntranceBanRoleIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateEntranceBanRoleIdResponse.Unmarshal(m, b)
}
func (m *UpdateEntranceBanRoleIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateEntranceBanRoleIdResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateEntranceBanRoleIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateEntranceBanRoleIdResponse.Merge(dst, src)
}
func (m *UpdateEntranceBanRoleIdResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateEntranceBanRoleIdResponse.Size(m)
}
func (m *UpdateEntranceBanRoleIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateEntranceBanRoleIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateEntranceBanRoleIdResponse proto.InternalMessageInfo

type GetEntranceBanRoleIdRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEntranceBanRoleIdRequest) Reset()         { *m = GetEntranceBanRoleIdRequest{} }
func (m *GetEntranceBanRoleIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetEntranceBanRoleIdRequest) ProtoMessage()    {}
func (*GetEntranceBanRoleIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{129}
}
func (m *GetEntranceBanRoleIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEntranceBanRoleIdRequest.Unmarshal(m, b)
}
func (m *GetEntranceBanRoleIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEntranceBanRoleIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetEntranceBanRoleIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEntranceBanRoleIdRequest.Merge(dst, src)
}
func (m *GetEntranceBanRoleIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetEntranceBanRoleIdRequest.Size(m)
}
func (m *GetEntranceBanRoleIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEntranceBanRoleIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetEntranceBanRoleIdRequest proto.InternalMessageInfo

type GetEntranceBanRoleIdResponse struct {
	RoleIds              []uint32 `protobuf:"varint,1,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEntranceBanRoleIdResponse) Reset()         { *m = GetEntranceBanRoleIdResponse{} }
func (m *GetEntranceBanRoleIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetEntranceBanRoleIdResponse) ProtoMessage()    {}
func (*GetEntranceBanRoleIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{130}
}
func (m *GetEntranceBanRoleIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEntranceBanRoleIdResponse.Unmarshal(m, b)
}
func (m *GetEntranceBanRoleIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEntranceBanRoleIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetEntranceBanRoleIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEntranceBanRoleIdResponse.Merge(dst, src)
}
func (m *GetEntranceBanRoleIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetEntranceBanRoleIdResponse.Size(m)
}
func (m *GetEntranceBanRoleIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEntranceBanRoleIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetEntranceBanRoleIdResponse proto.InternalMessageInfo

func (m *GetEntranceBanRoleIdResponse) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

type BatchGetReadHeartInfoRequest struct {
	MsgIds               []string `protobuf:"bytes,1,rep,name=msg_ids,json=msgIds,proto3" json:"msg_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetReadHeartInfoRequest) Reset()         { *m = BatchGetReadHeartInfoRequest{} }
func (m *BatchGetReadHeartInfoRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetReadHeartInfoRequest) ProtoMessage()    {}
func (*BatchGetReadHeartInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{131}
}
func (m *BatchGetReadHeartInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetReadHeartInfoRequest.Unmarshal(m, b)
}
func (m *BatchGetReadHeartInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetReadHeartInfoRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetReadHeartInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetReadHeartInfoRequest.Merge(dst, src)
}
func (m *BatchGetReadHeartInfoRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetReadHeartInfoRequest.Size(m)
}
func (m *BatchGetReadHeartInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetReadHeartInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetReadHeartInfoRequest proto.InternalMessageInfo

func (m *BatchGetReadHeartInfoRequest) GetMsgIds() []string {
	if m != nil {
		return m.MsgIds
	}
	return nil
}

type ReadHeartInfo struct {
	MsgId                string   `protobuf:"bytes,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	IsEntrance           bool     `protobuf:"varint,2,opt,name=is_entrance,json=isEntrance,proto3" json:"is_entrance,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadHeartInfo) Reset()         { *m = ReadHeartInfo{} }
func (m *ReadHeartInfo) String() string { return proto.CompactTextString(m) }
func (*ReadHeartInfo) ProtoMessage()    {}
func (*ReadHeartInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{132}
}
func (m *ReadHeartInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadHeartInfo.Unmarshal(m, b)
}
func (m *ReadHeartInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadHeartInfo.Marshal(b, m, deterministic)
}
func (dst *ReadHeartInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadHeartInfo.Merge(dst, src)
}
func (m *ReadHeartInfo) XXX_Size() int {
	return xxx_messageInfo_ReadHeartInfo.Size(m)
}
func (m *ReadHeartInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadHeartInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReadHeartInfo proto.InternalMessageInfo

func (m *ReadHeartInfo) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *ReadHeartInfo) GetIsEntrance() bool {
	if m != nil {
		return m.IsEntrance
	}
	return false
}

func (m *ReadHeartInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type BatchGetReadHeartInfoResponse struct {
	ReadHeartInfos       []*ReadHeartInfo `protobuf:"bytes,1,rep,name=read_heart_infos,json=readHeartInfos,proto3" json:"read_heart_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchGetReadHeartInfoResponse) Reset()         { *m = BatchGetReadHeartInfoResponse{} }
func (m *BatchGetReadHeartInfoResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetReadHeartInfoResponse) ProtoMessage()    {}
func (*BatchGetReadHeartInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{133}
}
func (m *BatchGetReadHeartInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetReadHeartInfoResponse.Unmarshal(m, b)
}
func (m *BatchGetReadHeartInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetReadHeartInfoResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetReadHeartInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetReadHeartInfoResponse.Merge(dst, src)
}
func (m *BatchGetReadHeartInfoResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetReadHeartInfoResponse.Size(m)
}
func (m *BatchGetReadHeartInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetReadHeartInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetReadHeartInfoResponse proto.InternalMessageInfo

func (m *BatchGetReadHeartInfoResponse) GetReadHeartInfos() []*ReadHeartInfo {
	if m != nil {
		return m.ReadHeartInfos
	}
	return nil
}

type BatchAddRoleUserRequest struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddRoleUserRequest) Reset()         { *m = BatchAddRoleUserRequest{} }
func (m *BatchAddRoleUserRequest) String() string { return proto.CompactTextString(m) }
func (*BatchAddRoleUserRequest) ProtoMessage()    {}
func (*BatchAddRoleUserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{134}
}
func (m *BatchAddRoleUserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddRoleUserRequest.Unmarshal(m, b)
}
func (m *BatchAddRoleUserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddRoleUserRequest.Marshal(b, m, deterministic)
}
func (dst *BatchAddRoleUserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddRoleUserRequest.Merge(dst, src)
}
func (m *BatchAddRoleUserRequest) XXX_Size() int {
	return xxx_messageInfo_BatchAddRoleUserRequest.Size(m)
}
func (m *BatchAddRoleUserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddRoleUserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddRoleUserRequest proto.InternalMessageInfo

func (m *BatchAddRoleUserRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *BatchAddRoleUserRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchAddRoleUserResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddRoleUserResponse) Reset()         { *m = BatchAddRoleUserResponse{} }
func (m *BatchAddRoleUserResponse) String() string { return proto.CompactTextString(m) }
func (*BatchAddRoleUserResponse) ProtoMessage()    {}
func (*BatchAddRoleUserResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{135}
}
func (m *BatchAddRoleUserResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddRoleUserResponse.Unmarshal(m, b)
}
func (m *BatchAddRoleUserResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddRoleUserResponse.Marshal(b, m, deterministic)
}
func (dst *BatchAddRoleUserResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddRoleUserResponse.Merge(dst, src)
}
func (m *BatchAddRoleUserResponse) XXX_Size() int {
	return xxx_messageInfo_BatchAddRoleUserResponse.Size(m)
}
func (m *BatchAddRoleUserResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddRoleUserResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddRoleUserResponse proto.InternalMessageInfo

type BatchDelRoleUserRequest struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelRoleUserRequest) Reset()         { *m = BatchDelRoleUserRequest{} }
func (m *BatchDelRoleUserRequest) String() string { return proto.CompactTextString(m) }
func (*BatchDelRoleUserRequest) ProtoMessage()    {}
func (*BatchDelRoleUserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{136}
}
func (m *BatchDelRoleUserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelRoleUserRequest.Unmarshal(m, b)
}
func (m *BatchDelRoleUserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelRoleUserRequest.Marshal(b, m, deterministic)
}
func (dst *BatchDelRoleUserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelRoleUserRequest.Merge(dst, src)
}
func (m *BatchDelRoleUserRequest) XXX_Size() int {
	return xxx_messageInfo_BatchDelRoleUserRequest.Size(m)
}
func (m *BatchDelRoleUserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelRoleUserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelRoleUserRequest proto.InternalMessageInfo

func (m *BatchDelRoleUserRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *BatchDelRoleUserRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchDelRoleUserResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelRoleUserResponse) Reset()         { *m = BatchDelRoleUserResponse{} }
func (m *BatchDelRoleUserResponse) String() string { return proto.CompactTextString(m) }
func (*BatchDelRoleUserResponse) ProtoMessage()    {}
func (*BatchDelRoleUserResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{137}
}
func (m *BatchDelRoleUserResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelRoleUserResponse.Unmarshal(m, b)
}
func (m *BatchDelRoleUserResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelRoleUserResponse.Marshal(b, m, deterministic)
}
func (dst *BatchDelRoleUserResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelRoleUserResponse.Merge(dst, src)
}
func (m *BatchDelRoleUserResponse) XXX_Size() int {
	return xxx_messageInfo_BatchDelRoleUserResponse.Size(m)
}
func (m *BatchDelRoleUserResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelRoleUserResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelRoleUserResponse proto.InternalMessageInfo

type GetRoleUidListRequest struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Cursor               string   `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoleUidListRequest) Reset()         { *m = GetRoleUidListRequest{} }
func (m *GetRoleUidListRequest) String() string { return proto.CompactTextString(m) }
func (*GetRoleUidListRequest) ProtoMessage()    {}
func (*GetRoleUidListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{138}
}
func (m *GetRoleUidListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoleUidListRequest.Unmarshal(m, b)
}
func (m *GetRoleUidListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoleUidListRequest.Marshal(b, m, deterministic)
}
func (dst *GetRoleUidListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoleUidListRequest.Merge(dst, src)
}
func (m *GetRoleUidListRequest) XXX_Size() int {
	return xxx_messageInfo_GetRoleUidListRequest.Size(m)
}
func (m *GetRoleUidListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoleUidListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoleUidListRequest proto.InternalMessageInfo

func (m *GetRoleUidListRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetRoleUidListRequest) GetCursor() string {
	if m != nil {
		return m.Cursor
	}
	return ""
}

func (m *GetRoleUidListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetRoleUidListResponse struct {
	List                 []uint32 `protobuf:"varint,1,rep,packed,name=list,proto3" json:"list,omitempty"`
	HasMore              bool     `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	NextCursor           string   `protobuf:"bytes,3,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoleUidListResponse) Reset()         { *m = GetRoleUidListResponse{} }
func (m *GetRoleUidListResponse) String() string { return proto.CompactTextString(m) }
func (*GetRoleUidListResponse) ProtoMessage()    {}
func (*GetRoleUidListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{139}
}
func (m *GetRoleUidListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoleUidListResponse.Unmarshal(m, b)
}
func (m *GetRoleUidListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoleUidListResponse.Marshal(b, m, deterministic)
}
func (dst *GetRoleUidListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoleUidListResponse.Merge(dst, src)
}
func (m *GetRoleUidListResponse) XXX_Size() int {
	return xxx_messageInfo_GetRoleUidListResponse.Size(m)
}
func (m *GetRoleUidListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoleUidListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoleUidListResponse proto.InternalMessageInfo

func (m *GetRoleUidListResponse) GetList() []uint32 {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetRoleUidListResponse) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

func (m *GetRoleUidListResponse) GetNextCursor() string {
	if m != nil {
		return m.NextCursor
	}
	return ""
}

type BatchGetRoleUserRequest struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetRoleUserRequest) Reset()         { *m = BatchGetRoleUserRequest{} }
func (m *BatchGetRoleUserRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetRoleUserRequest) ProtoMessage()    {}
func (*BatchGetRoleUserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{140}
}
func (m *BatchGetRoleUserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRoleUserRequest.Unmarshal(m, b)
}
func (m *BatchGetRoleUserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRoleUserRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetRoleUserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRoleUserRequest.Merge(dst, src)
}
func (m *BatchGetRoleUserRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetRoleUserRequest.Size(m)
}
func (m *BatchGetRoleUserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRoleUserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRoleUserRequest proto.InternalMessageInfo

func (m *BatchGetRoleUserRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *BatchGetRoleUserRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetRoleUserResponse struct {
	Exists               map[uint32]bool `protobuf:"bytes,1,rep,name=exists,proto3" json:"exists,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchGetRoleUserResponse) Reset()         { *m = BatchGetRoleUserResponse{} }
func (m *BatchGetRoleUserResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetRoleUserResponse) ProtoMessage()    {}
func (*BatchGetRoleUserResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{141}
}
func (m *BatchGetRoleUserResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRoleUserResponse.Unmarshal(m, b)
}
func (m *BatchGetRoleUserResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRoleUserResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetRoleUserResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRoleUserResponse.Merge(dst, src)
}
func (m *BatchGetRoleUserResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetRoleUserResponse.Size(m)
}
func (m *BatchGetRoleUserResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRoleUserResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRoleUserResponse proto.InternalMessageInfo

func (m *BatchGetRoleUserResponse) GetExists() map[uint32]bool {
	if m != nil {
		return m.Exists
	}
	return nil
}

type GetUserExclusiveRoleListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cursor               string   `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserExclusiveRoleListRequest) Reset()         { *m = GetUserExclusiveRoleListRequest{} }
func (m *GetUserExclusiveRoleListRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserExclusiveRoleListRequest) ProtoMessage()    {}
func (*GetUserExclusiveRoleListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{142}
}
func (m *GetUserExclusiveRoleListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExclusiveRoleListRequest.Unmarshal(m, b)
}
func (m *GetUserExclusiveRoleListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExclusiveRoleListRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserExclusiveRoleListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExclusiveRoleListRequest.Merge(dst, src)
}
func (m *GetUserExclusiveRoleListRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserExclusiveRoleListRequest.Size(m)
}
func (m *GetUserExclusiveRoleListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExclusiveRoleListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExclusiveRoleListRequest proto.InternalMessageInfo

func (m *GetUserExclusiveRoleListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserExclusiveRoleListRequest) GetCursor() string {
	if m != nil {
		return m.Cursor
	}
	return ""
}

func (m *GetUserExclusiveRoleListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetUserExclusiveRoleListResponse struct {
	List                 []*AIRole `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	HasMore              bool      `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	NextCursor           string    `protobuf:"bytes,3,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetUserExclusiveRoleListResponse) Reset()         { *m = GetUserExclusiveRoleListResponse{} }
func (m *GetUserExclusiveRoleListResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserExclusiveRoleListResponse) ProtoMessage()    {}
func (*GetUserExclusiveRoleListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_b4c9fab0d915780d, []int{143}
}
func (m *GetUserExclusiveRoleListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExclusiveRoleListResponse.Unmarshal(m, b)
}
func (m *GetUserExclusiveRoleListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExclusiveRoleListResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserExclusiveRoleListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExclusiveRoleListResponse.Merge(dst, src)
}
func (m *GetUserExclusiveRoleListResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserExclusiveRoleListResponse.Size(m)
}
func (m *GetUserExclusiveRoleListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExclusiveRoleListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExclusiveRoleListResponse proto.InternalMessageInfo

func (m *GetUserExclusiveRoleListResponse) GetList() []*AIRole {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetUserExclusiveRoleListResponse) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

func (m *GetUserExclusiveRoleListResponse) GetNextCursor() string {
	if m != nil {
		return m.NextCursor
	}
	return ""
}

func init() {
	proto.RegisterType((*AIRoleCategory)(nil), "aigc_soulmate.AIRoleCategory")
	proto.RegisterType((*AIRoleCategory_Label)(nil), "aigc_soulmate.AIRoleCategory.Label")
	proto.RegisterType((*AIRoleCategory_Prop)(nil), "aigc_soulmate.AIRoleCategory.Prop")
	proto.RegisterType((*AIRoleGreeting)(nil), "aigc_soulmate.AIRoleGreeting")
	proto.RegisterType((*AIRolePrologue)(nil), "aigc_soulmate.AIRolePrologue")
	proto.RegisterType((*AIRole)(nil), "aigc_soulmate.AIRole")
	proto.RegisterType((*AIPartner)(nil), "aigc_soulmate.AIPartner")
	proto.RegisterType((*SharedRole)(nil), "aigc_soulmate.SharedRole")
	proto.RegisterType((*AIRoleLikeInfo)(nil), "aigc_soulmate.AIRoleLikeInfo")
	proto.RegisterType((*InteractiveGame)(nil), "aigc_soulmate.InteractiveGame")
	proto.RegisterType((*CreateAIRoleReq)(nil), "aigc_soulmate.CreateAIRoleReq")
	proto.RegisterType((*CreateAIRoleReq_Role)(nil), "aigc_soulmate.CreateAIRoleReq.Role")
	proto.RegisterType((*AIGroupPrologue)(nil), "aigc_soulmate.AIGroupPrologue")
	proto.RegisterType((*GroupRoleConfig)(nil), "aigc_soulmate.GroupRoleConfig")
	proto.RegisterType((*CreateAIRoleResp)(nil), "aigc_soulmate.CreateAIRoleResp")
	proto.RegisterType((*UpdateAIRoleReq)(nil), "aigc_soulmate.UpdateAIRoleReq")
	proto.RegisterType((*UpdateAIRoleReq_Role)(nil), "aigc_soulmate.UpdateAIRoleReq.Role")
	proto.RegisterType((*UpdateAIRoleResp)(nil), "aigc_soulmate.UpdateAIRoleResp")
	proto.RegisterType((*DeleteAIRoleReq)(nil), "aigc_soulmate.DeleteAIRoleReq")
	proto.RegisterType((*DeleteAIRoleResp)(nil), "aigc_soulmate.DeleteAIRoleResp")
	proto.RegisterType((*GetAIRoleReq)(nil), "aigc_soulmate.GetAIRoleReq")
	proto.RegisterType((*GetAIRoleResp)(nil), "aigc_soulmate.GetAIRoleResp")
	proto.RegisterType((*GetAIRoleListReq)(nil), "aigc_soulmate.GetAIRoleListReq")
	proto.RegisterType((*GetAIRoleListResp)(nil), "aigc_soulmate.GetAIRoleListResp")
	proto.RegisterType((*GetUserAIRoleListReq)(nil), "aigc_soulmate.GetUserAIRoleListReq")
	proto.RegisterType((*GetUserAIRoleListResp)(nil), "aigc_soulmate.GetUserAIRoleListResp")
	proto.RegisterType((*GetUserAIRoleListWithAppointReq)(nil), "aigc_soulmate.GetUserAIRoleListWithAppointReq")
	proto.RegisterType((*GetUserAIRoleListWithAppointResp)(nil), "aigc_soulmate.GetUserAIRoleListWithAppointResp")
	proto.RegisterType((*GetOfficialAIRoleListReq)(nil), "aigc_soulmate.GetOfficialAIRoleListReq")
	proto.RegisterType((*GetOfficialAIRoleListResp)(nil), "aigc_soulmate.GetOfficialAIRoleListResp")
	proto.RegisterType((*GetBannerRoleListReq)(nil), "aigc_soulmate.GetBannerRoleListReq")
	proto.RegisterType((*GetBannerRoleListResp)(nil), "aigc_soulmate.GetBannerRoleListResp")
	proto.RegisterType((*SetBannerRoleListReq)(nil), "aigc_soulmate.SetBannerRoleListReq")
	proto.RegisterType((*SetBannerRoleListResp)(nil), "aigc_soulmate.SetBannerRoleListResp")
	proto.RegisterType((*CreateAIPartnerReq)(nil), "aigc_soulmate.CreateAIPartnerReq")
	proto.RegisterType((*CreateAIPartnerReq_Partner)(nil), "aigc_soulmate.CreateAIPartnerReq.Partner")
	proto.RegisterType((*CreateAIPartnerResp)(nil), "aigc_soulmate.CreateAIPartnerResp")
	proto.RegisterType((*UpdateAIPartnerReq)(nil), "aigc_soulmate.UpdateAIPartnerReq")
	proto.RegisterType((*UpdateAIPartnerReq_Partner)(nil), "aigc_soulmate.UpdateAIPartnerReq.Partner")
	proto.RegisterType((*UpdateAIPartnerResp)(nil), "aigc_soulmate.UpdateAIPartnerResp")
	proto.RegisterType((*DeleteAIPartnerReq)(nil), "aigc_soulmate.DeleteAIPartnerReq")
	proto.RegisterType((*DeleteAIPartnerResp)(nil), "aigc_soulmate.DeleteAIPartnerResp")
	proto.RegisterType((*ChangeAIPartnerRoleReq)(nil), "aigc_soulmate.ChangeAIPartnerRoleReq")
	proto.RegisterType((*ChangeAIPartnerRoleResp)(nil), "aigc_soulmate.ChangeAIPartnerRoleResp")
	proto.RegisterType((*UpdateAIPartnerChatStateReq)(nil), "aigc_soulmate.UpdateAIPartnerChatStateReq")
	proto.RegisterType((*UpdateAIPartnerChatStateResp)(nil), "aigc_soulmate.UpdateAIPartnerChatStateResp")
	proto.RegisterType((*GetAIPartnerReq)(nil), "aigc_soulmate.GetAIPartnerReq")
	proto.RegisterType((*GetAIPartnerResp)(nil), "aigc_soulmate.GetAIPartnerResp")
	proto.RegisterType((*BatchGetAIPartnerReq)(nil), "aigc_soulmate.BatchGetAIPartnerReq")
	proto.RegisterType((*BatchGetAIPartnerResp)(nil), "aigc_soulmate.BatchGetAIPartnerResp")
	proto.RegisterMapType((map[uint32]*AIPartner)(nil), "aigc_soulmate.BatchGetAIPartnerResp.PartnerMapEntry")
	proto.RegisterType((*GetUserAIPartnerReq)(nil), "aigc_soulmate.GetUserAIPartnerReq")
	proto.RegisterType((*GetUserAIPartnerResp)(nil), "aigc_soulmate.GetUserAIPartnerResp")
	proto.RegisterType((*GetUserAIPartnerListReq)(nil), "aigc_soulmate.GetUserAIPartnerListReq")
	proto.RegisterType((*GetUserAIPartnerListResp)(nil), "aigc_soulmate.GetUserAIPartnerListResp")
	proto.RegisterType((*UpsertAIRoleCategoryReq)(nil), "aigc_soulmate.UpsertAIRoleCategoryReq")
	proto.RegisterType((*UpsertAIRoleCategoryReq_Label)(nil), "aigc_soulmate.UpsertAIRoleCategoryReq.Label")
	proto.RegisterType((*UpsertAIRoleCategoryReq_Prop)(nil), "aigc_soulmate.UpsertAIRoleCategoryReq.Prop")
	proto.RegisterType((*UpsertAIRoleCategoryReq_Category)(nil), "aigc_soulmate.UpsertAIRoleCategoryReq.Category")
	proto.RegisterType((*UpsertAIRoleCategoryResp)(nil), "aigc_soulmate.UpsertAIRoleCategoryResp")
	proto.RegisterType((*DeleteAIRoleCategoryReq)(nil), "aigc_soulmate.DeleteAIRoleCategoryReq")
	proto.RegisterType((*DeleteAIRoleCategoryResp)(nil), "aigc_soulmate.DeleteAIRoleCategoryResp")
	proto.RegisterType((*BatchGetAIRoleCategoryReq)(nil), "aigc_soulmate.BatchGetAIRoleCategoryReq")
	proto.RegisterType((*BatchGetAIRoleCategoryResp)(nil), "aigc_soulmate.BatchGetAIRoleCategoryResp")
	proto.RegisterType((*ResortAIRoleCategoryReq)(nil), "aigc_soulmate.ResortAIRoleCategoryReq")
	proto.RegisterType((*ResortAIRoleCategoryResp)(nil), "aigc_soulmate.ResortAIRoleCategoryResp")
	proto.RegisterType((*ShareRoleReq)(nil), "aigc_soulmate.ShareRoleReq")
	proto.RegisterType((*ShareRoleResp)(nil), "aigc_soulmate.ShareRoleResp")
	proto.RegisterType((*GetSharedRoleReq)(nil), "aigc_soulmate.GetSharedRoleReq")
	proto.RegisterType((*GetSharedRoleResp)(nil), "aigc_soulmate.GetSharedRoleResp")
	proto.RegisterType((*AllocShareIdentifierReq)(nil), "aigc_soulmate.AllocShareIdentifierReq")
	proto.RegisterType((*AllocShareIdentifierResp)(nil), "aigc_soulmate.AllocShareIdentifierResp")
	proto.RegisterType((*VerifyShareIdentifierReq)(nil), "aigc_soulmate.VerifyShareIdentifierReq")
	proto.RegisterType((*VerifyShareIdentifierResp)(nil), "aigc_soulmate.VerifyShareIdentifierResp")
	proto.RegisterType((*AIMessage)(nil), "aigc_soulmate.AIMessage")
	proto.RegisterType((*WriteAIMessageReq)(nil), "aigc_soulmate.WriteAIMessageReq")
	proto.RegisterType((*WriteAIMessageResp)(nil), "aigc_soulmate.WriteAIMessageResp")
	proto.RegisterType((*PullAIMessageReq)(nil), "aigc_soulmate.PullAIMessageReq")
	proto.RegisterType((*PullAIMessageResp)(nil), "aigc_soulmate.PullAIMessageResp")
	proto.RegisterType((*ClearAIMessageReq)(nil), "aigc_soulmate.ClearAIMessageReq")
	proto.RegisterType((*ClearAIMessageResp)(nil), "aigc_soulmate.ClearAIMessageResp")
	proto.RegisterType((*SearchUserRoleReq)(nil), "aigc_soulmate.SearchUserRoleReq")
	proto.RegisterType((*SearchUserRoleResp)(nil), "aigc_soulmate.SearchUserRoleResp")
	proto.RegisterType((*BatchUpdateUserRoleReq)(nil), "aigc_soulmate.BatchUpdateUserRoleReq")
	proto.RegisterType((*BatchUpdateUserRoleReq_UpdateRole)(nil), "aigc_soulmate.BatchUpdateUserRoleReq.UpdateRole")
	proto.RegisterType((*BatchUpdateUserRoleResp)(nil), "aigc_soulmate.BatchUpdateUserRoleResp")
	proto.RegisterType((*BatchDeleteUserRoleReq)(nil), "aigc_soulmate.BatchDeleteUserRoleReq")
	proto.RegisterType((*BatchDeleteUserRoleResp)(nil), "aigc_soulmate.BatchDeleteUserRoleResp")
	proto.RegisterType((*UpdateAIRoleAuditResultReq)(nil), "aigc_soulmate.UpdateAIRoleAuditResultReq")
	proto.RegisterType((*UpdateAIRoleAuditResultResp)(nil), "aigc_soulmate.UpdateAIRoleAuditResultResp")
	proto.RegisterType((*LikeAIRoleReq)(nil), "aigc_soulmate.LikeAIRoleReq")
	proto.RegisterType((*LikeAIRoleResp)(nil), "aigc_soulmate.LikeAIRoleResp")
	proto.RegisterType((*UnlikeAIRoleReq)(nil), "aigc_soulmate.UnlikeAIRoleReq")
	proto.RegisterType((*UnlikeAIRoleResp)(nil), "aigc_soulmate.UnlikeAIRoleResp")
	proto.RegisterType((*GetUserAIRoleLikesReq)(nil), "aigc_soulmate.GetUserAIRoleLikesReq")
	proto.RegisterType((*GetUserAIRoleLikesResp)(nil), "aigc_soulmate.GetUserAIRoleLikesResp")
	proto.RegisterType((*GetLatestUpdatedAIRoleListReq)(nil), "aigc_soulmate.GetLatestUpdatedAIRoleListReq")
	proto.RegisterType((*GetLatestUpdatedAIRoleListResp)(nil), "aigc_soulmate.GetLatestUpdatedAIRoleListResp")
	proto.RegisterType((*SearchAIRoleReq)(nil), "aigc_soulmate.SearchAIRoleReq")
	proto.RegisterType((*SearchAIRoleResp)(nil), "aigc_soulmate.SearchAIRoleResp")
	proto.RegisterType((*CreateInteractiveGameReq)(nil), "aigc_soulmate.CreateInteractiveGameReq")
	proto.RegisterType((*CreateInteractiveGameReq_InteractiveGame)(nil), "aigc_soulmate.CreateInteractiveGameReq.InteractiveGame")
	proto.RegisterType((*CreateInteractiveGameResp)(nil), "aigc_soulmate.CreateInteractiveGameResp")
	proto.RegisterType((*UpdateInteractiveGameReq)(nil), "aigc_soulmate.UpdateInteractiveGameReq")
	proto.RegisterType((*UpdateInteractiveGameReq_InteractiveGame)(nil), "aigc_soulmate.UpdateInteractiveGameReq.InteractiveGame")
	proto.RegisterType((*UpdateInteractiveGameResp)(nil), "aigc_soulmate.UpdateInteractiveGameResp")
	proto.RegisterType((*BatchDeleteInteractiveGameReq)(nil), "aigc_soulmate.BatchDeleteInteractiveGameReq")
	proto.RegisterType((*BatchDeleteInteractiveGameResp)(nil), "aigc_soulmate.BatchDeleteInteractiveGameResp")
	proto.RegisterType((*SearchInteractiveGameReq)(nil), "aigc_soulmate.SearchInteractiveGameReq")
	proto.RegisterType((*SearchInteractiveGameResp)(nil), "aigc_soulmate.SearchInteractiveGameResp")
	proto.RegisterType((*BatchUpdateInteractiveGameReq)(nil), "aigc_soulmate.BatchUpdateInteractiveGameReq")
	proto.RegisterType((*BatchUpdateInteractiveGameReq_InteractiveGame)(nil), "aigc_soulmate.BatchUpdateInteractiveGameReq.InteractiveGame")
	proto.RegisterType((*BatchUpdateInteractiveGameResp)(nil), "aigc_soulmate.BatchUpdateInteractiveGameResp")
	proto.RegisterType((*GetUserInteractiveGameListReq)(nil), "aigc_soulmate.GetUserInteractiveGameListReq")
	proto.RegisterType((*GetUserInteractiveGameListResp)(nil), "aigc_soulmate.GetUserInteractiveGameListResp")
	proto.RegisterType((*GetInteractiveGameReq)(nil), "aigc_soulmate.GetInteractiveGameReq")
	proto.RegisterType((*GetInteractiveGameResp)(nil), "aigc_soulmate.GetInteractiveGameResp")
	proto.RegisterType((*BatchCreateRoleReq)(nil), "aigc_soulmate.BatchCreateRoleReq")
	proto.RegisterType((*BatchCreateRoleResp)(nil), "aigc_soulmate.BatchCreateRoleResp")
	proto.RegisterType((*BatchUpdateRoleReq)(nil), "aigc_soulmate.BatchUpdateRoleReq")
	proto.RegisterType((*BatchUpdateRoleResp)(nil), "aigc_soulmate.BatchUpdateRoleResp")
	proto.RegisterType((*AIChatTemplate)(nil), "aigc_soulmate.AIChatTemplate")
	proto.RegisterType((*BindEntity)(nil), "aigc_soulmate.BindEntity")
	proto.RegisterType((*BaseChatTemplate)(nil), "aigc_soulmate.BaseChatTemplate")
	proto.RegisterType((*TemplateMsg)(nil), "aigc_soulmate.TemplateMsg")
	proto.RegisterType((*CreateAIChatTemplateReq)(nil), "aigc_soulmate.CreateAIChatTemplateReq")
	proto.RegisterType((*CreateAIChatTemplateResp)(nil), "aigc_soulmate.CreateAIChatTemplateResp")
	proto.RegisterType((*UpdateAIChatTemplateReq)(nil), "aigc_soulmate.UpdateAIChatTemplateReq")
	proto.RegisterType((*UpdateAIChatTemplateResp)(nil), "aigc_soulmate.UpdateAIChatTemplateResp")
	proto.RegisterType((*BatchDeleteAIChatTemplateReq)(nil), "aigc_soulmate.BatchDeleteAIChatTemplateReq")
	proto.RegisterType((*BatchDeleteAIChatTemplateResp)(nil), "aigc_soulmate.BatchDeleteAIChatTemplateResp")
	proto.RegisterType((*GetAIChatTemplateListReq)(nil), "aigc_soulmate.GetAIChatTemplateListReq")
	proto.RegisterType((*GetAIChatTemplateListResp)(nil), "aigc_soulmate.GetAIChatTemplateListResp")
	proto.RegisterType((*GetBindChatTemplatesReq)(nil), "aigc_soulmate.GetBindChatTemplatesReq")
	proto.RegisterType((*GetBindChatTemplatesResp)(nil), "aigc_soulmate.GetBindChatTemplatesResp")
	proto.RegisterType((*GetReadHeartEntranceRequest)(nil), "aigc_soulmate.GetReadHeartEntranceRequest")
	proto.RegisterType((*GetReadHeartEntranceResponse)(nil), "aigc_soulmate.GetReadHeartEntranceResponse")
	proto.RegisterType((*GetUserReadHeartCntRequest)(nil), "aigc_soulmate.GetUserReadHeartCntRequest")
	proto.RegisterType((*GetUserReadHeartCntResponse)(nil), "aigc_soulmate.GetUserReadHeartCntResponse")
	proto.RegisterType((*AddReadHeartTextRequest)(nil), "aigc_soulmate.AddReadHeartTextRequest")
	proto.RegisterType((*AddReadHeartTextResponse)(nil), "aigc_soulmate.AddReadHeartTextResponse")
	proto.RegisterType((*UpdateEntranceBanRoleIdRequest)(nil), "aigc_soulmate.UpdateEntranceBanRoleIdRequest")
	proto.RegisterType((*UpdateEntranceBanRoleIdResponse)(nil), "aigc_soulmate.UpdateEntranceBanRoleIdResponse")
	proto.RegisterType((*GetEntranceBanRoleIdRequest)(nil), "aigc_soulmate.GetEntranceBanRoleIdRequest")
	proto.RegisterType((*GetEntranceBanRoleIdResponse)(nil), "aigc_soulmate.GetEntranceBanRoleIdResponse")
	proto.RegisterType((*BatchGetReadHeartInfoRequest)(nil), "aigc_soulmate.BatchGetReadHeartInfoRequest")
	proto.RegisterType((*ReadHeartInfo)(nil), "aigc_soulmate.ReadHeartInfo")
	proto.RegisterType((*BatchGetReadHeartInfoResponse)(nil), "aigc_soulmate.BatchGetReadHeartInfoResponse")
	proto.RegisterType((*BatchAddRoleUserRequest)(nil), "aigc_soulmate.BatchAddRoleUserRequest")
	proto.RegisterType((*BatchAddRoleUserResponse)(nil), "aigc_soulmate.BatchAddRoleUserResponse")
	proto.RegisterType((*BatchDelRoleUserRequest)(nil), "aigc_soulmate.BatchDelRoleUserRequest")
	proto.RegisterType((*BatchDelRoleUserResponse)(nil), "aigc_soulmate.BatchDelRoleUserResponse")
	proto.RegisterType((*GetRoleUidListRequest)(nil), "aigc_soulmate.GetRoleUidListRequest")
	proto.RegisterType((*GetRoleUidListResponse)(nil), "aigc_soulmate.GetRoleUidListResponse")
	proto.RegisterType((*BatchGetRoleUserRequest)(nil), "aigc_soulmate.BatchGetRoleUserRequest")
	proto.RegisterType((*BatchGetRoleUserResponse)(nil), "aigc_soulmate.BatchGetRoleUserResponse")
	proto.RegisterMapType((map[uint32]bool)(nil), "aigc_soulmate.BatchGetRoleUserResponse.ExistsEntry")
	proto.RegisterType((*GetUserExclusiveRoleListRequest)(nil), "aigc_soulmate.GetUserExclusiveRoleListRequest")
	proto.RegisterType((*GetUserExclusiveRoleListResponse)(nil), "aigc_soulmate.GetUserExclusiveRoleListResponse")
	proto.RegisterEnum("aigc_soulmate.AuditResult", AuditResult_name, AuditResult_value)
	proto.RegisterEnum("aigc_soulmate.AIRoleType", AIRoleType_name, AIRoleType_value)
	proto.RegisterEnum("aigc_soulmate.AIRoleState", AIRoleState_name, AIRoleState_value)
	proto.RegisterEnum("aigc_soulmate.AIPartnerSource", AIPartnerSource_name, AIPartnerSource_value)
	proto.RegisterEnum("aigc_soulmate.AIRoleSource", AIRoleSource_name, AIRoleSource_value)
	proto.RegisterEnum("aigc_soulmate.GetCategorySource", GetCategorySource_name, GetCategorySource_value)
	proto.RegisterEnum("aigc_soulmate.InteractiveGameState", InteractiveGameState_name, InteractiveGameState_value)
	proto.RegisterEnum("aigc_soulmate.InteractiveGameSource", InteractiveGameSource_name, InteractiveGameSource_value)
	proto.RegisterEnum("aigc_soulmate.AIRoleStoryMode", AIRoleStoryMode_name, AIRoleStoryMode_value)
	proto.RegisterEnum("aigc_soulmate.InteractiveGameExposure", InteractiveGameExposure_name, InteractiveGameExposure_value)
	proto.RegisterEnum("aigc_soulmate.AIRoleScope", AIRoleScope_name, AIRoleScope_value)
	proto.RegisterEnum("aigc_soulmate.CreatorInfoType", CreatorInfoType_name, CreatorInfoType_value)
	proto.RegisterEnum("aigc_soulmate.SearchRoleSexEnum", SearchRoleSexEnum_name, SearchRoleSexEnum_value)
	proto.RegisterEnum("aigc_soulmate.SearchType", SearchType_name, SearchType_value)
	proto.RegisterEnum("aigc_soulmate.FilterExpose", FilterExpose_name, FilterExpose_value)
	proto.RegisterEnum("aigc_soulmate.DeductionHeartCntType", DeductionHeartCntType_name, DeductionHeartCntType_value)
	proto.RegisterEnum("aigc_soulmate.AIRoleCategory_Scene", AIRoleCategory_Scene_name, AIRoleCategory_Scene_value)
	proto.RegisterEnum("aigc_soulmate.AIRoleCategory_PropType", AIRoleCategory_PropType_name, AIRoleCategory_PropType_value)
	proto.RegisterEnum("aigc_soulmate.AIRole_LikeState", AIRole_LikeState_name, AIRole_LikeState_value)
	proto.RegisterEnum("aigc_soulmate.AIRoleLikeInfo_LikeState", AIRoleLikeInfo_LikeState_name, AIRoleLikeInfo_LikeState_value)
	proto.RegisterEnum("aigc_soulmate.UpsertAIRoleCategoryReq_Op", UpsertAIRoleCategoryReq_Op_name, UpsertAIRoleCategoryReq_Op_value)
	proto.RegisterEnum("aigc_soulmate.BindEntity_EntityType", BindEntity_EntityType_name, BindEntity_EntityType_value)
	proto.RegisterEnum("aigc_soulmate.TemplateMsg_SenderType", TemplateMsg_SenderType_name, TemplateMsg_SenderType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AigcSoulmateClient is the client API for AigcSoulmate service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AigcSoulmateClient interface {
	// 角色
	CreateAIRole(ctx context.Context, in *CreateAIRoleReq, opts ...grpc.CallOption) (*CreateAIRoleResp, error)
	UpdateAIRole(ctx context.Context, in *UpdateAIRoleReq, opts ...grpc.CallOption) (*UpdateAIRoleResp, error)
	DeleteAIRole(ctx context.Context, in *DeleteAIRoleReq, opts ...grpc.CallOption) (*DeleteAIRoleResp, error)
	GetAIRole(ctx context.Context, in *GetAIRoleReq, opts ...grpc.CallOption) (*GetAIRoleResp, error)
	GetAIRoleList(ctx context.Context, in *GetAIRoleListReq, opts ...grpc.CallOption) (*GetAIRoleListResp, error)
	GetUserAIRoleList(ctx context.Context, in *GetUserAIRoleListReq, opts ...grpc.CallOption) (*GetUserAIRoleListResp, error)
	GetOfficialAIRoleList(ctx context.Context, in *GetOfficialAIRoleListReq, opts ...grpc.CallOption) (*GetOfficialAIRoleListResp, error)
	GetLatestUpdatedAIRoleList(ctx context.Context, in *GetLatestUpdatedAIRoleListReq, opts ...grpc.CallOption) (*GetLatestUpdatedAIRoleListResp, error)
	SearchAIRole(ctx context.Context, in *SearchAIRoleReq, opts ...grpc.CallOption) (*SearchAIRoleResp, error)
	BatchCreateRole(ctx context.Context, in *BatchCreateRoleReq, opts ...grpc.CallOption) (*BatchCreateRoleResp, error)
	BatchUpdateRole(ctx context.Context, in *BatchUpdateRoleReq, opts ...grpc.CallOption) (*BatchUpdateRoleResp, error)
	GetUserAIRoleListWithAppoint(ctx context.Context, in *GetUserAIRoleListWithAppointReq, opts ...grpc.CallOption) (*GetUserAIRoleListWithAppointResp, error)
	// 角色banner
	GetBannerRoleList(ctx context.Context, in *GetBannerRoleListReq, opts ...grpc.CallOption) (*GetBannerRoleListResp, error)
	SetBannerRoleList(ctx context.Context, in *SetBannerRoleListReq, opts ...grpc.CallOption) (*SetBannerRoleListResp, error)
	// 伴侣
	TryCreateAIPartner(ctx context.Context, in *CreateAIPartnerReq, opts ...grpc.CallOption) (*CreateAIPartnerResp, error)
	CreateAIPartner(ctx context.Context, in *CreateAIPartnerReq, opts ...grpc.CallOption) (*CreateAIPartnerResp, error)
	UpdateAIPartner(ctx context.Context, in *UpdateAIPartnerReq, opts ...grpc.CallOption) (*UpdateAIPartnerResp, error)
	DeleteAIPartner(ctx context.Context, in *DeleteAIPartnerReq, opts ...grpc.CallOption) (*DeleteAIPartnerResp, error)
	ChangeAIPartnerRole(ctx context.Context, in *ChangeAIPartnerRoleReq, opts ...grpc.CallOption) (*ChangeAIPartnerRoleResp, error)
	UpdateAIPartnerChatState(ctx context.Context, in *UpdateAIPartnerChatStateReq, opts ...grpc.CallOption) (*UpdateAIPartnerChatStateResp, error)
	GetAIPartner(ctx context.Context, in *GetAIPartnerReq, opts ...grpc.CallOption) (*GetAIPartnerResp, error)
	GetUserAIPartner(ctx context.Context, in *GetUserAIPartnerReq, opts ...grpc.CallOption) (*GetUserAIPartnerResp, error)
	GetUserAIPartnerList(ctx context.Context, in *GetUserAIPartnerListReq, opts ...grpc.CallOption) (*GetUserAIPartnerListResp, error)
	BatchGetAIPartner(ctx context.Context, in *BatchGetAIPartnerReq, opts ...grpc.CallOption) (*BatchGetAIPartnerResp, error)
	// 伴侣消息
	PullAIMessage(ctx context.Context, in *PullAIMessageReq, opts ...grpc.CallOption) (*PullAIMessageResp, error)
	WriteAIMessage(ctx context.Context, in *WriteAIMessageReq, opts ...grpc.CallOption) (*WriteAIMessageResp, error)
	ClearAIMessage(ctx context.Context, in *ClearAIMessageReq, opts ...grpc.CallOption) (*ClearAIMessageResp, error)
	// 角色分类
	UpsertAIRoleCategory(ctx context.Context, in *UpsertAIRoleCategoryReq, opts ...grpc.CallOption) (*UpsertAIRoleCategoryResp, error)
	DeleteAIRoleCategory(ctx context.Context, in *DeleteAIRoleCategoryReq, opts ...grpc.CallOption) (*DeleteAIRoleCategoryResp, error)
	BatchGetAIRoleCategory(ctx context.Context, in *BatchGetAIRoleCategoryReq, opts ...grpc.CallOption) (*BatchGetAIRoleCategoryResp, error)
	ResortAIRoleCategory(ctx context.Context, in *ResortAIRoleCategoryReq, opts ...grpc.CallOption) (*ResortAIRoleCategoryResp, error)
	// 角色分享
	ShareRole(ctx context.Context, in *ShareRoleReq, opts ...grpc.CallOption) (*ShareRoleResp, error)
	GetSharedRole(ctx context.Context, in *GetSharedRoleReq, opts ...grpc.CallOption) (*GetSharedRoleResp, error)
	AllocShareIdentifier(ctx context.Context, in *AllocShareIdentifierReq, opts ...grpc.CallOption) (*AllocShareIdentifierResp, error)
	VerifyShareIdentifier(ctx context.Context, in *VerifyShareIdentifierReq, opts ...grpc.CallOption) (*VerifyShareIdentifierResp, error)
	// 角色管理运营后台
	SearchUserRole(ctx context.Context, in *SearchUserRoleReq, opts ...grpc.CallOption) (*SearchUserRoleResp, error)
	BatchUpdateUserRole(ctx context.Context, in *BatchUpdateUserRoleReq, opts ...grpc.CallOption) (*BatchUpdateUserRoleResp, error)
	BatchDeleteUserRole(ctx context.Context, in *BatchDeleteUserRoleReq, opts ...grpc.CallOption) (*BatchDeleteUserRoleResp, error)
	// 手动触发写入审核结果
	UpdateAIRoleAuditResult(ctx context.Context, in *UpdateAIRoleAuditResultReq, opts ...grpc.CallOption) (*UpdateAIRoleAuditResultResp, error)
	// 角色点赞
	LikeAIRole(ctx context.Context, in *LikeAIRoleReq, opts ...grpc.CallOption) (*LikeAIRoleResp, error)
	UnlikeAIRole(ctx context.Context, in *UnlikeAIRoleReq, opts ...grpc.CallOption) (*UnlikeAIRoleResp, error)
	GetUserAIRoleLikes(ctx context.Context, in *GetUserAIRoleLikesReq, opts ...grpc.CallOption) (*GetUserAIRoleLikesResp, error)
	// 互动玩法
	CreateInteractiveGame(ctx context.Context, in *CreateInteractiveGameReq, opts ...grpc.CallOption) (*CreateInteractiveGameResp, error)
	UpdateInteractiveGame(ctx context.Context, in *UpdateInteractiveGameReq, opts ...grpc.CallOption) (*UpdateInteractiveGameResp, error)
	GetInteractiveGame(ctx context.Context, in *GetInteractiveGameReq, opts ...grpc.CallOption) (*GetInteractiveGameResp, error)
	SearchInteractiveGame(ctx context.Context, in *SearchInteractiveGameReq, opts ...grpc.CallOption) (*SearchInteractiveGameResp, error)
	GetUserInteractiveGameList(ctx context.Context, in *GetUserInteractiveGameListReq, opts ...grpc.CallOption) (*GetUserInteractiveGameListResp, error)
	BatchDeleteInteractiveGame(ctx context.Context, in *BatchDeleteInteractiveGameReq, opts ...grpc.CallOption) (*BatchDeleteInteractiveGameResp, error)
	BatchUpdateInteractiveGame(ctx context.Context, in *BatchUpdateInteractiveGameReq, opts ...grpc.CallOption) (*BatchUpdateInteractiveGameResp, error)
	// 聊天模版
	CreateAIChatTemplate(ctx context.Context, in *CreateAIChatTemplateReq, opts ...grpc.CallOption) (*CreateAIChatTemplateResp, error)
	UpdateAIChatTemplate(ctx context.Context, in *UpdateAIChatTemplateReq, opts ...grpc.CallOption) (*UpdateAIChatTemplateResp, error)
	BatchDeleteAIChatTemplate(ctx context.Context, in *BatchDeleteAIChatTemplateReq, opts ...grpc.CallOption) (*BatchDeleteAIChatTemplateResp, error)
	GetAIChatTemplateList(ctx context.Context, in *GetAIChatTemplateListReq, opts ...grpc.CallOption) (*GetAIChatTemplateListResp, error)
	GetBindChatTemplates(ctx context.Context, in *GetBindChatTemplatesReq, opts ...grpc.CallOption) (*GetBindChatTemplatesResp, error)
	// 是否读心开启
	GetReadHeartEntrance(ctx context.Context, in *GetReadHeartEntranceRequest, opts ...grpc.CallOption) (*GetReadHeartEntranceResponse, error)
	GetUserReadHeartCnt(ctx context.Context, in *GetUserReadHeartCntRequest, opts ...grpc.CallOption) (*GetUserReadHeartCntResponse, error)
	AddReadHeartText(ctx context.Context, in *AddReadHeartTextRequest, opts ...grpc.CallOption) (*AddReadHeartTextResponse, error)
	UpdateEntranceBanRoleId(ctx context.Context, in *UpdateEntranceBanRoleIdRequest, opts ...grpc.CallOption) (*UpdateEntranceBanRoleIdResponse, error)
	GetEntranceBanRoleId(ctx context.Context, in *GetEntranceBanRoleIdRequest, opts ...grpc.CallOption) (*GetEntranceBanRoleIdResponse, error)
	BatchGetReadHeartInfo(ctx context.Context, in *BatchGetReadHeartInfoRequest, opts ...grpc.CallOption) (*BatchGetReadHeartInfoResponse, error)
	BatchAddRoleUser(ctx context.Context, in *BatchAddRoleUserRequest, opts ...grpc.CallOption) (*BatchAddRoleUserResponse, error)
	BatchDelRoleUser(ctx context.Context, in *BatchDelRoleUserRequest, opts ...grpc.CallOption) (*BatchDelRoleUserResponse, error)
	GetRoleUidList(ctx context.Context, in *GetRoleUidListRequest, opts ...grpc.CallOption) (*GetRoleUidListResponse, error)
	BatchGetRoleUser(ctx context.Context, in *BatchGetRoleUserRequest, opts ...grpc.CallOption) (*BatchGetRoleUserResponse, error)
	GetUserExclusiveRoleList(ctx context.Context, in *GetUserExclusiveRoleListRequest, opts ...grpc.CallOption) (*GetUserExclusiveRoleListResponse, error)
}

type aigcSoulmateClient struct {
	cc *grpc.ClientConn
}

func NewAigcSoulmateClient(cc *grpc.ClientConn) AigcSoulmateClient {
	return &aigcSoulmateClient{cc}
}

func (c *aigcSoulmateClient) CreateAIRole(ctx context.Context, in *CreateAIRoleReq, opts ...grpc.CallOption) (*CreateAIRoleResp, error) {
	out := new(CreateAIRoleResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/CreateAIRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) UpdateAIRole(ctx context.Context, in *UpdateAIRoleReq, opts ...grpc.CallOption) (*UpdateAIRoleResp, error) {
	out := new(UpdateAIRoleResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/UpdateAIRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) DeleteAIRole(ctx context.Context, in *DeleteAIRoleReq, opts ...grpc.CallOption) (*DeleteAIRoleResp, error) {
	out := new(DeleteAIRoleResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/DeleteAIRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetAIRole(ctx context.Context, in *GetAIRoleReq, opts ...grpc.CallOption) (*GetAIRoleResp, error) {
	out := new(GetAIRoleResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetAIRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetAIRoleList(ctx context.Context, in *GetAIRoleListReq, opts ...grpc.CallOption) (*GetAIRoleListResp, error) {
	out := new(GetAIRoleListResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetAIRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetUserAIRoleList(ctx context.Context, in *GetUserAIRoleListReq, opts ...grpc.CallOption) (*GetUserAIRoleListResp, error) {
	out := new(GetUserAIRoleListResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetUserAIRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetOfficialAIRoleList(ctx context.Context, in *GetOfficialAIRoleListReq, opts ...grpc.CallOption) (*GetOfficialAIRoleListResp, error) {
	out := new(GetOfficialAIRoleListResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetOfficialAIRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetLatestUpdatedAIRoleList(ctx context.Context, in *GetLatestUpdatedAIRoleListReq, opts ...grpc.CallOption) (*GetLatestUpdatedAIRoleListResp, error) {
	out := new(GetLatestUpdatedAIRoleListResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetLatestUpdatedAIRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) SearchAIRole(ctx context.Context, in *SearchAIRoleReq, opts ...grpc.CallOption) (*SearchAIRoleResp, error) {
	out := new(SearchAIRoleResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/SearchAIRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) BatchCreateRole(ctx context.Context, in *BatchCreateRoleReq, opts ...grpc.CallOption) (*BatchCreateRoleResp, error) {
	out := new(BatchCreateRoleResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/BatchCreateRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) BatchUpdateRole(ctx context.Context, in *BatchUpdateRoleReq, opts ...grpc.CallOption) (*BatchUpdateRoleResp, error) {
	out := new(BatchUpdateRoleResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/BatchUpdateRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetUserAIRoleListWithAppoint(ctx context.Context, in *GetUserAIRoleListWithAppointReq, opts ...grpc.CallOption) (*GetUserAIRoleListWithAppointResp, error) {
	out := new(GetUserAIRoleListWithAppointResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetUserAIRoleListWithAppoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetBannerRoleList(ctx context.Context, in *GetBannerRoleListReq, opts ...grpc.CallOption) (*GetBannerRoleListResp, error) {
	out := new(GetBannerRoleListResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetBannerRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) SetBannerRoleList(ctx context.Context, in *SetBannerRoleListReq, opts ...grpc.CallOption) (*SetBannerRoleListResp, error) {
	out := new(SetBannerRoleListResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/SetBannerRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) TryCreateAIPartner(ctx context.Context, in *CreateAIPartnerReq, opts ...grpc.CallOption) (*CreateAIPartnerResp, error) {
	out := new(CreateAIPartnerResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/TryCreateAIPartner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) CreateAIPartner(ctx context.Context, in *CreateAIPartnerReq, opts ...grpc.CallOption) (*CreateAIPartnerResp, error) {
	out := new(CreateAIPartnerResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/CreateAIPartner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) UpdateAIPartner(ctx context.Context, in *UpdateAIPartnerReq, opts ...grpc.CallOption) (*UpdateAIPartnerResp, error) {
	out := new(UpdateAIPartnerResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/UpdateAIPartner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) DeleteAIPartner(ctx context.Context, in *DeleteAIPartnerReq, opts ...grpc.CallOption) (*DeleteAIPartnerResp, error) {
	out := new(DeleteAIPartnerResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/DeleteAIPartner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) ChangeAIPartnerRole(ctx context.Context, in *ChangeAIPartnerRoleReq, opts ...grpc.CallOption) (*ChangeAIPartnerRoleResp, error) {
	out := new(ChangeAIPartnerRoleResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/ChangeAIPartnerRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) UpdateAIPartnerChatState(ctx context.Context, in *UpdateAIPartnerChatStateReq, opts ...grpc.CallOption) (*UpdateAIPartnerChatStateResp, error) {
	out := new(UpdateAIPartnerChatStateResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/UpdateAIPartnerChatState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetAIPartner(ctx context.Context, in *GetAIPartnerReq, opts ...grpc.CallOption) (*GetAIPartnerResp, error) {
	out := new(GetAIPartnerResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetAIPartner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetUserAIPartner(ctx context.Context, in *GetUserAIPartnerReq, opts ...grpc.CallOption) (*GetUserAIPartnerResp, error) {
	out := new(GetUserAIPartnerResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetUserAIPartner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetUserAIPartnerList(ctx context.Context, in *GetUserAIPartnerListReq, opts ...grpc.CallOption) (*GetUserAIPartnerListResp, error) {
	out := new(GetUserAIPartnerListResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetUserAIPartnerList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) BatchGetAIPartner(ctx context.Context, in *BatchGetAIPartnerReq, opts ...grpc.CallOption) (*BatchGetAIPartnerResp, error) {
	out := new(BatchGetAIPartnerResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/BatchGetAIPartner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) PullAIMessage(ctx context.Context, in *PullAIMessageReq, opts ...grpc.CallOption) (*PullAIMessageResp, error) {
	out := new(PullAIMessageResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/PullAIMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) WriteAIMessage(ctx context.Context, in *WriteAIMessageReq, opts ...grpc.CallOption) (*WriteAIMessageResp, error) {
	out := new(WriteAIMessageResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/WriteAIMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) ClearAIMessage(ctx context.Context, in *ClearAIMessageReq, opts ...grpc.CallOption) (*ClearAIMessageResp, error) {
	out := new(ClearAIMessageResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/ClearAIMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) UpsertAIRoleCategory(ctx context.Context, in *UpsertAIRoleCategoryReq, opts ...grpc.CallOption) (*UpsertAIRoleCategoryResp, error) {
	out := new(UpsertAIRoleCategoryResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/UpsertAIRoleCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) DeleteAIRoleCategory(ctx context.Context, in *DeleteAIRoleCategoryReq, opts ...grpc.CallOption) (*DeleteAIRoleCategoryResp, error) {
	out := new(DeleteAIRoleCategoryResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/DeleteAIRoleCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) BatchGetAIRoleCategory(ctx context.Context, in *BatchGetAIRoleCategoryReq, opts ...grpc.CallOption) (*BatchGetAIRoleCategoryResp, error) {
	out := new(BatchGetAIRoleCategoryResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/BatchGetAIRoleCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) ResortAIRoleCategory(ctx context.Context, in *ResortAIRoleCategoryReq, opts ...grpc.CallOption) (*ResortAIRoleCategoryResp, error) {
	out := new(ResortAIRoleCategoryResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/ResortAIRoleCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) ShareRole(ctx context.Context, in *ShareRoleReq, opts ...grpc.CallOption) (*ShareRoleResp, error) {
	out := new(ShareRoleResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/ShareRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetSharedRole(ctx context.Context, in *GetSharedRoleReq, opts ...grpc.CallOption) (*GetSharedRoleResp, error) {
	out := new(GetSharedRoleResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetSharedRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) AllocShareIdentifier(ctx context.Context, in *AllocShareIdentifierReq, opts ...grpc.CallOption) (*AllocShareIdentifierResp, error) {
	out := new(AllocShareIdentifierResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/AllocShareIdentifier", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) VerifyShareIdentifier(ctx context.Context, in *VerifyShareIdentifierReq, opts ...grpc.CallOption) (*VerifyShareIdentifierResp, error) {
	out := new(VerifyShareIdentifierResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/VerifyShareIdentifier", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) SearchUserRole(ctx context.Context, in *SearchUserRoleReq, opts ...grpc.CallOption) (*SearchUserRoleResp, error) {
	out := new(SearchUserRoleResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/SearchUserRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) BatchUpdateUserRole(ctx context.Context, in *BatchUpdateUserRoleReq, opts ...grpc.CallOption) (*BatchUpdateUserRoleResp, error) {
	out := new(BatchUpdateUserRoleResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/BatchUpdateUserRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) BatchDeleteUserRole(ctx context.Context, in *BatchDeleteUserRoleReq, opts ...grpc.CallOption) (*BatchDeleteUserRoleResp, error) {
	out := new(BatchDeleteUserRoleResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/BatchDeleteUserRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) UpdateAIRoleAuditResult(ctx context.Context, in *UpdateAIRoleAuditResultReq, opts ...grpc.CallOption) (*UpdateAIRoleAuditResultResp, error) {
	out := new(UpdateAIRoleAuditResultResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/UpdateAIRoleAuditResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) LikeAIRole(ctx context.Context, in *LikeAIRoleReq, opts ...grpc.CallOption) (*LikeAIRoleResp, error) {
	out := new(LikeAIRoleResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/LikeAIRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) UnlikeAIRole(ctx context.Context, in *UnlikeAIRoleReq, opts ...grpc.CallOption) (*UnlikeAIRoleResp, error) {
	out := new(UnlikeAIRoleResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/UnlikeAIRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetUserAIRoleLikes(ctx context.Context, in *GetUserAIRoleLikesReq, opts ...grpc.CallOption) (*GetUserAIRoleLikesResp, error) {
	out := new(GetUserAIRoleLikesResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetUserAIRoleLikes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) CreateInteractiveGame(ctx context.Context, in *CreateInteractiveGameReq, opts ...grpc.CallOption) (*CreateInteractiveGameResp, error) {
	out := new(CreateInteractiveGameResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/CreateInteractiveGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) UpdateInteractiveGame(ctx context.Context, in *UpdateInteractiveGameReq, opts ...grpc.CallOption) (*UpdateInteractiveGameResp, error) {
	out := new(UpdateInteractiveGameResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/UpdateInteractiveGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetInteractiveGame(ctx context.Context, in *GetInteractiveGameReq, opts ...grpc.CallOption) (*GetInteractiveGameResp, error) {
	out := new(GetInteractiveGameResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetInteractiveGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) SearchInteractiveGame(ctx context.Context, in *SearchInteractiveGameReq, opts ...grpc.CallOption) (*SearchInteractiveGameResp, error) {
	out := new(SearchInteractiveGameResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/SearchInteractiveGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetUserInteractiveGameList(ctx context.Context, in *GetUserInteractiveGameListReq, opts ...grpc.CallOption) (*GetUserInteractiveGameListResp, error) {
	out := new(GetUserInteractiveGameListResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetUserInteractiveGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) BatchDeleteInteractiveGame(ctx context.Context, in *BatchDeleteInteractiveGameReq, opts ...grpc.CallOption) (*BatchDeleteInteractiveGameResp, error) {
	out := new(BatchDeleteInteractiveGameResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/BatchDeleteInteractiveGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) BatchUpdateInteractiveGame(ctx context.Context, in *BatchUpdateInteractiveGameReq, opts ...grpc.CallOption) (*BatchUpdateInteractiveGameResp, error) {
	out := new(BatchUpdateInteractiveGameResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/BatchUpdateInteractiveGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) CreateAIChatTemplate(ctx context.Context, in *CreateAIChatTemplateReq, opts ...grpc.CallOption) (*CreateAIChatTemplateResp, error) {
	out := new(CreateAIChatTemplateResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/CreateAIChatTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) UpdateAIChatTemplate(ctx context.Context, in *UpdateAIChatTemplateReq, opts ...grpc.CallOption) (*UpdateAIChatTemplateResp, error) {
	out := new(UpdateAIChatTemplateResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/UpdateAIChatTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) BatchDeleteAIChatTemplate(ctx context.Context, in *BatchDeleteAIChatTemplateReq, opts ...grpc.CallOption) (*BatchDeleteAIChatTemplateResp, error) {
	out := new(BatchDeleteAIChatTemplateResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/BatchDeleteAIChatTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetAIChatTemplateList(ctx context.Context, in *GetAIChatTemplateListReq, opts ...grpc.CallOption) (*GetAIChatTemplateListResp, error) {
	out := new(GetAIChatTemplateListResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetAIChatTemplateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetBindChatTemplates(ctx context.Context, in *GetBindChatTemplatesReq, opts ...grpc.CallOption) (*GetBindChatTemplatesResp, error) {
	out := new(GetBindChatTemplatesResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetBindChatTemplates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetReadHeartEntrance(ctx context.Context, in *GetReadHeartEntranceRequest, opts ...grpc.CallOption) (*GetReadHeartEntranceResponse, error) {
	out := new(GetReadHeartEntranceResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetReadHeartEntrance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetUserReadHeartCnt(ctx context.Context, in *GetUserReadHeartCntRequest, opts ...grpc.CallOption) (*GetUserReadHeartCntResponse, error) {
	out := new(GetUserReadHeartCntResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetUserReadHeartCnt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) AddReadHeartText(ctx context.Context, in *AddReadHeartTextRequest, opts ...grpc.CallOption) (*AddReadHeartTextResponse, error) {
	out := new(AddReadHeartTextResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/AddReadHeartText", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) UpdateEntranceBanRoleId(ctx context.Context, in *UpdateEntranceBanRoleIdRequest, opts ...grpc.CallOption) (*UpdateEntranceBanRoleIdResponse, error) {
	out := new(UpdateEntranceBanRoleIdResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/UpdateEntranceBanRoleId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetEntranceBanRoleId(ctx context.Context, in *GetEntranceBanRoleIdRequest, opts ...grpc.CallOption) (*GetEntranceBanRoleIdResponse, error) {
	out := new(GetEntranceBanRoleIdResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetEntranceBanRoleId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) BatchGetReadHeartInfo(ctx context.Context, in *BatchGetReadHeartInfoRequest, opts ...grpc.CallOption) (*BatchGetReadHeartInfoResponse, error) {
	out := new(BatchGetReadHeartInfoResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/BatchGetReadHeartInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) BatchAddRoleUser(ctx context.Context, in *BatchAddRoleUserRequest, opts ...grpc.CallOption) (*BatchAddRoleUserResponse, error) {
	out := new(BatchAddRoleUserResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/BatchAddRoleUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) BatchDelRoleUser(ctx context.Context, in *BatchDelRoleUserRequest, opts ...grpc.CallOption) (*BatchDelRoleUserResponse, error) {
	out := new(BatchDelRoleUserResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/BatchDelRoleUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetRoleUidList(ctx context.Context, in *GetRoleUidListRequest, opts ...grpc.CallOption) (*GetRoleUidListResponse, error) {
	out := new(GetRoleUidListResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetRoleUidList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) BatchGetRoleUser(ctx context.Context, in *BatchGetRoleUserRequest, opts ...grpc.CallOption) (*BatchGetRoleUserResponse, error) {
	out := new(BatchGetRoleUserResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/BatchGetRoleUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateClient) GetUserExclusiveRoleList(ctx context.Context, in *GetUserExclusiveRoleListRequest, opts ...grpc.CallOption) (*GetUserExclusiveRoleListResponse, error) {
	out := new(GetUserExclusiveRoleListResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate.AigcSoulmate/GetUserExclusiveRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AigcSoulmateServer is the server API for AigcSoulmate service.
type AigcSoulmateServer interface {
	// 角色
	CreateAIRole(context.Context, *CreateAIRoleReq) (*CreateAIRoleResp, error)
	UpdateAIRole(context.Context, *UpdateAIRoleReq) (*UpdateAIRoleResp, error)
	DeleteAIRole(context.Context, *DeleteAIRoleReq) (*DeleteAIRoleResp, error)
	GetAIRole(context.Context, *GetAIRoleReq) (*GetAIRoleResp, error)
	GetAIRoleList(context.Context, *GetAIRoleListReq) (*GetAIRoleListResp, error)
	GetUserAIRoleList(context.Context, *GetUserAIRoleListReq) (*GetUserAIRoleListResp, error)
	GetOfficialAIRoleList(context.Context, *GetOfficialAIRoleListReq) (*GetOfficialAIRoleListResp, error)
	GetLatestUpdatedAIRoleList(context.Context, *GetLatestUpdatedAIRoleListReq) (*GetLatestUpdatedAIRoleListResp, error)
	SearchAIRole(context.Context, *SearchAIRoleReq) (*SearchAIRoleResp, error)
	BatchCreateRole(context.Context, *BatchCreateRoleReq) (*BatchCreateRoleResp, error)
	BatchUpdateRole(context.Context, *BatchUpdateRoleReq) (*BatchUpdateRoleResp, error)
	GetUserAIRoleListWithAppoint(context.Context, *GetUserAIRoleListWithAppointReq) (*GetUserAIRoleListWithAppointResp, error)
	// 角色banner
	GetBannerRoleList(context.Context, *GetBannerRoleListReq) (*GetBannerRoleListResp, error)
	SetBannerRoleList(context.Context, *SetBannerRoleListReq) (*SetBannerRoleListResp, error)
	// 伴侣
	TryCreateAIPartner(context.Context, *CreateAIPartnerReq) (*CreateAIPartnerResp, error)
	CreateAIPartner(context.Context, *CreateAIPartnerReq) (*CreateAIPartnerResp, error)
	UpdateAIPartner(context.Context, *UpdateAIPartnerReq) (*UpdateAIPartnerResp, error)
	DeleteAIPartner(context.Context, *DeleteAIPartnerReq) (*DeleteAIPartnerResp, error)
	ChangeAIPartnerRole(context.Context, *ChangeAIPartnerRoleReq) (*ChangeAIPartnerRoleResp, error)
	UpdateAIPartnerChatState(context.Context, *UpdateAIPartnerChatStateReq) (*UpdateAIPartnerChatStateResp, error)
	GetAIPartner(context.Context, *GetAIPartnerReq) (*GetAIPartnerResp, error)
	GetUserAIPartner(context.Context, *GetUserAIPartnerReq) (*GetUserAIPartnerResp, error)
	GetUserAIPartnerList(context.Context, *GetUserAIPartnerListReq) (*GetUserAIPartnerListResp, error)
	BatchGetAIPartner(context.Context, *BatchGetAIPartnerReq) (*BatchGetAIPartnerResp, error)
	// 伴侣消息
	PullAIMessage(context.Context, *PullAIMessageReq) (*PullAIMessageResp, error)
	WriteAIMessage(context.Context, *WriteAIMessageReq) (*WriteAIMessageResp, error)
	ClearAIMessage(context.Context, *ClearAIMessageReq) (*ClearAIMessageResp, error)
	// 角色分类
	UpsertAIRoleCategory(context.Context, *UpsertAIRoleCategoryReq) (*UpsertAIRoleCategoryResp, error)
	DeleteAIRoleCategory(context.Context, *DeleteAIRoleCategoryReq) (*DeleteAIRoleCategoryResp, error)
	BatchGetAIRoleCategory(context.Context, *BatchGetAIRoleCategoryReq) (*BatchGetAIRoleCategoryResp, error)
	ResortAIRoleCategory(context.Context, *ResortAIRoleCategoryReq) (*ResortAIRoleCategoryResp, error)
	// 角色分享
	ShareRole(context.Context, *ShareRoleReq) (*ShareRoleResp, error)
	GetSharedRole(context.Context, *GetSharedRoleReq) (*GetSharedRoleResp, error)
	AllocShareIdentifier(context.Context, *AllocShareIdentifierReq) (*AllocShareIdentifierResp, error)
	VerifyShareIdentifier(context.Context, *VerifyShareIdentifierReq) (*VerifyShareIdentifierResp, error)
	// 角色管理运营后台
	SearchUserRole(context.Context, *SearchUserRoleReq) (*SearchUserRoleResp, error)
	BatchUpdateUserRole(context.Context, *BatchUpdateUserRoleReq) (*BatchUpdateUserRoleResp, error)
	BatchDeleteUserRole(context.Context, *BatchDeleteUserRoleReq) (*BatchDeleteUserRoleResp, error)
	// 手动触发写入审核结果
	UpdateAIRoleAuditResult(context.Context, *UpdateAIRoleAuditResultReq) (*UpdateAIRoleAuditResultResp, error)
	// 角色点赞
	LikeAIRole(context.Context, *LikeAIRoleReq) (*LikeAIRoleResp, error)
	UnlikeAIRole(context.Context, *UnlikeAIRoleReq) (*UnlikeAIRoleResp, error)
	GetUserAIRoleLikes(context.Context, *GetUserAIRoleLikesReq) (*GetUserAIRoleLikesResp, error)
	// 互动玩法
	CreateInteractiveGame(context.Context, *CreateInteractiveGameReq) (*CreateInteractiveGameResp, error)
	UpdateInteractiveGame(context.Context, *UpdateInteractiveGameReq) (*UpdateInteractiveGameResp, error)
	GetInteractiveGame(context.Context, *GetInteractiveGameReq) (*GetInteractiveGameResp, error)
	SearchInteractiveGame(context.Context, *SearchInteractiveGameReq) (*SearchInteractiveGameResp, error)
	GetUserInteractiveGameList(context.Context, *GetUserInteractiveGameListReq) (*GetUserInteractiveGameListResp, error)
	BatchDeleteInteractiveGame(context.Context, *BatchDeleteInteractiveGameReq) (*BatchDeleteInteractiveGameResp, error)
	BatchUpdateInteractiveGame(context.Context, *BatchUpdateInteractiveGameReq) (*BatchUpdateInteractiveGameResp, error)
	// 聊天模版
	CreateAIChatTemplate(context.Context, *CreateAIChatTemplateReq) (*CreateAIChatTemplateResp, error)
	UpdateAIChatTemplate(context.Context, *UpdateAIChatTemplateReq) (*UpdateAIChatTemplateResp, error)
	BatchDeleteAIChatTemplate(context.Context, *BatchDeleteAIChatTemplateReq) (*BatchDeleteAIChatTemplateResp, error)
	GetAIChatTemplateList(context.Context, *GetAIChatTemplateListReq) (*GetAIChatTemplateListResp, error)
	GetBindChatTemplates(context.Context, *GetBindChatTemplatesReq) (*GetBindChatTemplatesResp, error)
	// 是否读心开启
	GetReadHeartEntrance(context.Context, *GetReadHeartEntranceRequest) (*GetReadHeartEntranceResponse, error)
	GetUserReadHeartCnt(context.Context, *GetUserReadHeartCntRequest) (*GetUserReadHeartCntResponse, error)
	AddReadHeartText(context.Context, *AddReadHeartTextRequest) (*AddReadHeartTextResponse, error)
	UpdateEntranceBanRoleId(context.Context, *UpdateEntranceBanRoleIdRequest) (*UpdateEntranceBanRoleIdResponse, error)
	GetEntranceBanRoleId(context.Context, *GetEntranceBanRoleIdRequest) (*GetEntranceBanRoleIdResponse, error)
	BatchGetReadHeartInfo(context.Context, *BatchGetReadHeartInfoRequest) (*BatchGetReadHeartInfoResponse, error)
	BatchAddRoleUser(context.Context, *BatchAddRoleUserRequest) (*BatchAddRoleUserResponse, error)
	BatchDelRoleUser(context.Context, *BatchDelRoleUserRequest) (*BatchDelRoleUserResponse, error)
	GetRoleUidList(context.Context, *GetRoleUidListRequest) (*GetRoleUidListResponse, error)
	BatchGetRoleUser(context.Context, *BatchGetRoleUserRequest) (*BatchGetRoleUserResponse, error)
	GetUserExclusiveRoleList(context.Context, *GetUserExclusiveRoleListRequest) (*GetUserExclusiveRoleListResponse, error)
}

func RegisterAigcSoulmateServer(s *grpc.Server, srv AigcSoulmateServer) {
	s.RegisterService(&_AigcSoulmate_serviceDesc, srv)
}

func _AigcSoulmate_CreateAIRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAIRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).CreateAIRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/CreateAIRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).CreateAIRole(ctx, req.(*CreateAIRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_UpdateAIRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAIRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).UpdateAIRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/UpdateAIRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).UpdateAIRole(ctx, req.(*UpdateAIRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_DeleteAIRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAIRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).DeleteAIRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/DeleteAIRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).DeleteAIRole(ctx, req.(*DeleteAIRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetAIRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAIRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetAIRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetAIRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetAIRole(ctx, req.(*GetAIRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetAIRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAIRoleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetAIRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetAIRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetAIRoleList(ctx, req.(*GetAIRoleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetUserAIRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAIRoleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetUserAIRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetUserAIRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetUserAIRoleList(ctx, req.(*GetUserAIRoleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetOfficialAIRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOfficialAIRoleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetOfficialAIRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetOfficialAIRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetOfficialAIRoleList(ctx, req.(*GetOfficialAIRoleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetLatestUpdatedAIRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLatestUpdatedAIRoleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetLatestUpdatedAIRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetLatestUpdatedAIRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetLatestUpdatedAIRoleList(ctx, req.(*GetLatestUpdatedAIRoleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_SearchAIRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAIRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).SearchAIRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/SearchAIRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).SearchAIRole(ctx, req.(*SearchAIRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_BatchCreateRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCreateRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).BatchCreateRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/BatchCreateRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).BatchCreateRole(ctx, req.(*BatchCreateRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_BatchUpdateRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).BatchUpdateRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/BatchUpdateRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).BatchUpdateRole(ctx, req.(*BatchUpdateRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetUserAIRoleListWithAppoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAIRoleListWithAppointReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetUserAIRoleListWithAppoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetUserAIRoleListWithAppoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetUserAIRoleListWithAppoint(ctx, req.(*GetUserAIRoleListWithAppointReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetBannerRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannerRoleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetBannerRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetBannerRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetBannerRoleList(ctx, req.(*GetBannerRoleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_SetBannerRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBannerRoleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).SetBannerRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/SetBannerRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).SetBannerRoleList(ctx, req.(*SetBannerRoleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_TryCreateAIPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAIPartnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).TryCreateAIPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/TryCreateAIPartner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).TryCreateAIPartner(ctx, req.(*CreateAIPartnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_CreateAIPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAIPartnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).CreateAIPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/CreateAIPartner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).CreateAIPartner(ctx, req.(*CreateAIPartnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_UpdateAIPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAIPartnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).UpdateAIPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/UpdateAIPartner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).UpdateAIPartner(ctx, req.(*UpdateAIPartnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_DeleteAIPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAIPartnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).DeleteAIPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/DeleteAIPartner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).DeleteAIPartner(ctx, req.(*DeleteAIPartnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_ChangeAIPartnerRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeAIPartnerRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).ChangeAIPartnerRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/ChangeAIPartnerRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).ChangeAIPartnerRole(ctx, req.(*ChangeAIPartnerRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_UpdateAIPartnerChatState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAIPartnerChatStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).UpdateAIPartnerChatState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/UpdateAIPartnerChatState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).UpdateAIPartnerChatState(ctx, req.(*UpdateAIPartnerChatStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetAIPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAIPartnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetAIPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetAIPartner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetAIPartner(ctx, req.(*GetAIPartnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetUserAIPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAIPartnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetUserAIPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetUserAIPartner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetUserAIPartner(ctx, req.(*GetUserAIPartnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetUserAIPartnerList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAIPartnerListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetUserAIPartnerList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetUserAIPartnerList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetUserAIPartnerList(ctx, req.(*GetUserAIPartnerListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_BatchGetAIPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAIPartnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).BatchGetAIPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/BatchGetAIPartner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).BatchGetAIPartner(ctx, req.(*BatchGetAIPartnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_PullAIMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PullAIMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).PullAIMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/PullAIMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).PullAIMessage(ctx, req.(*PullAIMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_WriteAIMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteAIMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).WriteAIMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/WriteAIMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).WriteAIMessage(ctx, req.(*WriteAIMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_ClearAIMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearAIMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).ClearAIMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/ClearAIMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).ClearAIMessage(ctx, req.(*ClearAIMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_UpsertAIRoleCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertAIRoleCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).UpsertAIRoleCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/UpsertAIRoleCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).UpsertAIRoleCategory(ctx, req.(*UpsertAIRoleCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_DeleteAIRoleCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAIRoleCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).DeleteAIRoleCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/DeleteAIRoleCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).DeleteAIRoleCategory(ctx, req.(*DeleteAIRoleCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_BatchGetAIRoleCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAIRoleCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).BatchGetAIRoleCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/BatchGetAIRoleCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).BatchGetAIRoleCategory(ctx, req.(*BatchGetAIRoleCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_ResortAIRoleCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResortAIRoleCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).ResortAIRoleCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/ResortAIRoleCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).ResortAIRoleCategory(ctx, req.(*ResortAIRoleCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_ShareRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShareRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).ShareRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/ShareRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).ShareRole(ctx, req.(*ShareRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetSharedRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSharedRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetSharedRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetSharedRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetSharedRole(ctx, req.(*GetSharedRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_AllocShareIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllocShareIdentifierReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).AllocShareIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/AllocShareIdentifier",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).AllocShareIdentifier(ctx, req.(*AllocShareIdentifierReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_VerifyShareIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyShareIdentifierReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).VerifyShareIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/VerifyShareIdentifier",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).VerifyShareIdentifier(ctx, req.(*VerifyShareIdentifierReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_SearchUserRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchUserRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).SearchUserRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/SearchUserRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).SearchUserRole(ctx, req.(*SearchUserRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_BatchUpdateUserRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateUserRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).BatchUpdateUserRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/BatchUpdateUserRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).BatchUpdateUserRole(ctx, req.(*BatchUpdateUserRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_BatchDeleteUserRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteUserRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).BatchDeleteUserRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/BatchDeleteUserRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).BatchDeleteUserRole(ctx, req.(*BatchDeleteUserRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_UpdateAIRoleAuditResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAIRoleAuditResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).UpdateAIRoleAuditResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/UpdateAIRoleAuditResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).UpdateAIRoleAuditResult(ctx, req.(*UpdateAIRoleAuditResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_LikeAIRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LikeAIRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).LikeAIRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/LikeAIRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).LikeAIRole(ctx, req.(*LikeAIRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_UnlikeAIRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnlikeAIRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).UnlikeAIRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/UnlikeAIRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).UnlikeAIRole(ctx, req.(*UnlikeAIRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetUserAIRoleLikes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAIRoleLikesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetUserAIRoleLikes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetUserAIRoleLikes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetUserAIRoleLikes(ctx, req.(*GetUserAIRoleLikesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_CreateInteractiveGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInteractiveGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).CreateInteractiveGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/CreateInteractiveGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).CreateInteractiveGame(ctx, req.(*CreateInteractiveGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_UpdateInteractiveGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInteractiveGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).UpdateInteractiveGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/UpdateInteractiveGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).UpdateInteractiveGame(ctx, req.(*UpdateInteractiveGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetInteractiveGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInteractiveGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetInteractiveGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetInteractiveGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetInteractiveGame(ctx, req.(*GetInteractiveGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_SearchInteractiveGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchInteractiveGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).SearchInteractiveGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/SearchInteractiveGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).SearchInteractiveGame(ctx, req.(*SearchInteractiveGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetUserInteractiveGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInteractiveGameListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetUserInteractiveGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetUserInteractiveGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetUserInteractiveGameList(ctx, req.(*GetUserInteractiveGameListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_BatchDeleteInteractiveGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteInteractiveGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).BatchDeleteInteractiveGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/BatchDeleteInteractiveGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).BatchDeleteInteractiveGame(ctx, req.(*BatchDeleteInteractiveGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_BatchUpdateInteractiveGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateInteractiveGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).BatchUpdateInteractiveGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/BatchUpdateInteractiveGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).BatchUpdateInteractiveGame(ctx, req.(*BatchUpdateInteractiveGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_CreateAIChatTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAIChatTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).CreateAIChatTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/CreateAIChatTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).CreateAIChatTemplate(ctx, req.(*CreateAIChatTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_UpdateAIChatTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAIChatTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).UpdateAIChatTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/UpdateAIChatTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).UpdateAIChatTemplate(ctx, req.(*UpdateAIChatTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_BatchDeleteAIChatTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteAIChatTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).BatchDeleteAIChatTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/BatchDeleteAIChatTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).BatchDeleteAIChatTemplate(ctx, req.(*BatchDeleteAIChatTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetAIChatTemplateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAIChatTemplateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetAIChatTemplateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetAIChatTemplateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetAIChatTemplateList(ctx, req.(*GetAIChatTemplateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetBindChatTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBindChatTemplatesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetBindChatTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetBindChatTemplates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetBindChatTemplates(ctx, req.(*GetBindChatTemplatesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetReadHeartEntrance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReadHeartEntranceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetReadHeartEntrance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetReadHeartEntrance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetReadHeartEntrance(ctx, req.(*GetReadHeartEntranceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetUserReadHeartCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserReadHeartCntRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetUserReadHeartCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetUserReadHeartCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetUserReadHeartCnt(ctx, req.(*GetUserReadHeartCntRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_AddReadHeartText_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddReadHeartTextRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).AddReadHeartText(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/AddReadHeartText",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).AddReadHeartText(ctx, req.(*AddReadHeartTextRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_UpdateEntranceBanRoleId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEntranceBanRoleIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).UpdateEntranceBanRoleId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/UpdateEntranceBanRoleId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).UpdateEntranceBanRoleId(ctx, req.(*UpdateEntranceBanRoleIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetEntranceBanRoleId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEntranceBanRoleIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetEntranceBanRoleId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetEntranceBanRoleId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetEntranceBanRoleId(ctx, req.(*GetEntranceBanRoleIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_BatchGetReadHeartInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetReadHeartInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).BatchGetReadHeartInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/BatchGetReadHeartInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).BatchGetReadHeartInfo(ctx, req.(*BatchGetReadHeartInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_BatchAddRoleUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddRoleUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).BatchAddRoleUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/BatchAddRoleUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).BatchAddRoleUser(ctx, req.(*BatchAddRoleUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_BatchDelRoleUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelRoleUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).BatchDelRoleUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/BatchDelRoleUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).BatchDelRoleUser(ctx, req.(*BatchDelRoleUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetRoleUidList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRoleUidListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetRoleUidList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetRoleUidList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetRoleUidList(ctx, req.(*GetRoleUidListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_BatchGetRoleUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetRoleUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).BatchGetRoleUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/BatchGetRoleUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).BatchGetRoleUser(ctx, req.(*BatchGetRoleUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmate_GetUserExclusiveRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserExclusiveRoleListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateServer).GetUserExclusiveRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate.AigcSoulmate/GetUserExclusiveRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateServer).GetUserExclusiveRoleList(ctx, req.(*GetUserExclusiveRoleListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _AigcSoulmate_serviceDesc = grpc.ServiceDesc{
	ServiceName: "aigc_soulmate.AigcSoulmate",
	HandlerType: (*AigcSoulmateServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAIRole",
			Handler:    _AigcSoulmate_CreateAIRole_Handler,
		},
		{
			MethodName: "UpdateAIRole",
			Handler:    _AigcSoulmate_UpdateAIRole_Handler,
		},
		{
			MethodName: "DeleteAIRole",
			Handler:    _AigcSoulmate_DeleteAIRole_Handler,
		},
		{
			MethodName: "GetAIRole",
			Handler:    _AigcSoulmate_GetAIRole_Handler,
		},
		{
			MethodName: "GetAIRoleList",
			Handler:    _AigcSoulmate_GetAIRoleList_Handler,
		},
		{
			MethodName: "GetUserAIRoleList",
			Handler:    _AigcSoulmate_GetUserAIRoleList_Handler,
		},
		{
			MethodName: "GetOfficialAIRoleList",
			Handler:    _AigcSoulmate_GetOfficialAIRoleList_Handler,
		},
		{
			MethodName: "GetLatestUpdatedAIRoleList",
			Handler:    _AigcSoulmate_GetLatestUpdatedAIRoleList_Handler,
		},
		{
			MethodName: "SearchAIRole",
			Handler:    _AigcSoulmate_SearchAIRole_Handler,
		},
		{
			MethodName: "BatchCreateRole",
			Handler:    _AigcSoulmate_BatchCreateRole_Handler,
		},
		{
			MethodName: "BatchUpdateRole",
			Handler:    _AigcSoulmate_BatchUpdateRole_Handler,
		},
		{
			MethodName: "GetUserAIRoleListWithAppoint",
			Handler:    _AigcSoulmate_GetUserAIRoleListWithAppoint_Handler,
		},
		{
			MethodName: "GetBannerRoleList",
			Handler:    _AigcSoulmate_GetBannerRoleList_Handler,
		},
		{
			MethodName: "SetBannerRoleList",
			Handler:    _AigcSoulmate_SetBannerRoleList_Handler,
		},
		{
			MethodName: "TryCreateAIPartner",
			Handler:    _AigcSoulmate_TryCreateAIPartner_Handler,
		},
		{
			MethodName: "CreateAIPartner",
			Handler:    _AigcSoulmate_CreateAIPartner_Handler,
		},
		{
			MethodName: "UpdateAIPartner",
			Handler:    _AigcSoulmate_UpdateAIPartner_Handler,
		},
		{
			MethodName: "DeleteAIPartner",
			Handler:    _AigcSoulmate_DeleteAIPartner_Handler,
		},
		{
			MethodName: "ChangeAIPartnerRole",
			Handler:    _AigcSoulmate_ChangeAIPartnerRole_Handler,
		},
		{
			MethodName: "UpdateAIPartnerChatState",
			Handler:    _AigcSoulmate_UpdateAIPartnerChatState_Handler,
		},
		{
			MethodName: "GetAIPartner",
			Handler:    _AigcSoulmate_GetAIPartner_Handler,
		},
		{
			MethodName: "GetUserAIPartner",
			Handler:    _AigcSoulmate_GetUserAIPartner_Handler,
		},
		{
			MethodName: "GetUserAIPartnerList",
			Handler:    _AigcSoulmate_GetUserAIPartnerList_Handler,
		},
		{
			MethodName: "BatchGetAIPartner",
			Handler:    _AigcSoulmate_BatchGetAIPartner_Handler,
		},
		{
			MethodName: "PullAIMessage",
			Handler:    _AigcSoulmate_PullAIMessage_Handler,
		},
		{
			MethodName: "WriteAIMessage",
			Handler:    _AigcSoulmate_WriteAIMessage_Handler,
		},
		{
			MethodName: "ClearAIMessage",
			Handler:    _AigcSoulmate_ClearAIMessage_Handler,
		},
		{
			MethodName: "UpsertAIRoleCategory",
			Handler:    _AigcSoulmate_UpsertAIRoleCategory_Handler,
		},
		{
			MethodName: "DeleteAIRoleCategory",
			Handler:    _AigcSoulmate_DeleteAIRoleCategory_Handler,
		},
		{
			MethodName: "BatchGetAIRoleCategory",
			Handler:    _AigcSoulmate_BatchGetAIRoleCategory_Handler,
		},
		{
			MethodName: "ResortAIRoleCategory",
			Handler:    _AigcSoulmate_ResortAIRoleCategory_Handler,
		},
		{
			MethodName: "ShareRole",
			Handler:    _AigcSoulmate_ShareRole_Handler,
		},
		{
			MethodName: "GetSharedRole",
			Handler:    _AigcSoulmate_GetSharedRole_Handler,
		},
		{
			MethodName: "AllocShareIdentifier",
			Handler:    _AigcSoulmate_AllocShareIdentifier_Handler,
		},
		{
			MethodName: "VerifyShareIdentifier",
			Handler:    _AigcSoulmate_VerifyShareIdentifier_Handler,
		},
		{
			MethodName: "SearchUserRole",
			Handler:    _AigcSoulmate_SearchUserRole_Handler,
		},
		{
			MethodName: "BatchUpdateUserRole",
			Handler:    _AigcSoulmate_BatchUpdateUserRole_Handler,
		},
		{
			MethodName: "BatchDeleteUserRole",
			Handler:    _AigcSoulmate_BatchDeleteUserRole_Handler,
		},
		{
			MethodName: "UpdateAIRoleAuditResult",
			Handler:    _AigcSoulmate_UpdateAIRoleAuditResult_Handler,
		},
		{
			MethodName: "LikeAIRole",
			Handler:    _AigcSoulmate_LikeAIRole_Handler,
		},
		{
			MethodName: "UnlikeAIRole",
			Handler:    _AigcSoulmate_UnlikeAIRole_Handler,
		},
		{
			MethodName: "GetUserAIRoleLikes",
			Handler:    _AigcSoulmate_GetUserAIRoleLikes_Handler,
		},
		{
			MethodName: "CreateInteractiveGame",
			Handler:    _AigcSoulmate_CreateInteractiveGame_Handler,
		},
		{
			MethodName: "UpdateInteractiveGame",
			Handler:    _AigcSoulmate_UpdateInteractiveGame_Handler,
		},
		{
			MethodName: "GetInteractiveGame",
			Handler:    _AigcSoulmate_GetInteractiveGame_Handler,
		},
		{
			MethodName: "SearchInteractiveGame",
			Handler:    _AigcSoulmate_SearchInteractiveGame_Handler,
		},
		{
			MethodName: "GetUserInteractiveGameList",
			Handler:    _AigcSoulmate_GetUserInteractiveGameList_Handler,
		},
		{
			MethodName: "BatchDeleteInteractiveGame",
			Handler:    _AigcSoulmate_BatchDeleteInteractiveGame_Handler,
		},
		{
			MethodName: "BatchUpdateInteractiveGame",
			Handler:    _AigcSoulmate_BatchUpdateInteractiveGame_Handler,
		},
		{
			MethodName: "CreateAIChatTemplate",
			Handler:    _AigcSoulmate_CreateAIChatTemplate_Handler,
		},
		{
			MethodName: "UpdateAIChatTemplate",
			Handler:    _AigcSoulmate_UpdateAIChatTemplate_Handler,
		},
		{
			MethodName: "BatchDeleteAIChatTemplate",
			Handler:    _AigcSoulmate_BatchDeleteAIChatTemplate_Handler,
		},
		{
			MethodName: "GetAIChatTemplateList",
			Handler:    _AigcSoulmate_GetAIChatTemplateList_Handler,
		},
		{
			MethodName: "GetBindChatTemplates",
			Handler:    _AigcSoulmate_GetBindChatTemplates_Handler,
		},
		{
			MethodName: "GetReadHeartEntrance",
			Handler:    _AigcSoulmate_GetReadHeartEntrance_Handler,
		},
		{
			MethodName: "GetUserReadHeartCnt",
			Handler:    _AigcSoulmate_GetUserReadHeartCnt_Handler,
		},
		{
			MethodName: "AddReadHeartText",
			Handler:    _AigcSoulmate_AddReadHeartText_Handler,
		},
		{
			MethodName: "UpdateEntranceBanRoleId",
			Handler:    _AigcSoulmate_UpdateEntranceBanRoleId_Handler,
		},
		{
			MethodName: "GetEntranceBanRoleId",
			Handler:    _AigcSoulmate_GetEntranceBanRoleId_Handler,
		},
		{
			MethodName: "BatchGetReadHeartInfo",
			Handler:    _AigcSoulmate_BatchGetReadHeartInfo_Handler,
		},
		{
			MethodName: "BatchAddRoleUser",
			Handler:    _AigcSoulmate_BatchAddRoleUser_Handler,
		},
		{
			MethodName: "BatchDelRoleUser",
			Handler:    _AigcSoulmate_BatchDelRoleUser_Handler,
		},
		{
			MethodName: "GetRoleUidList",
			Handler:    _AigcSoulmate_GetRoleUidList_Handler,
		},
		{
			MethodName: "BatchGetRoleUser",
			Handler:    _AigcSoulmate_BatchGetRoleUser_Handler,
		},
		{
			MethodName: "GetUserExclusiveRoleList",
			Handler:    _AigcSoulmate_GetUserExclusiveRoleList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/aigc/aigc-soulmate.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/aigc/aigc-soulmate.proto", fileDescriptor_aigc_soulmate_b4c9fab0d915780d)
}

var fileDescriptor_aigc_soulmate_b4c9fab0d915780d = []byte{
	// 6641 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x7d, 0xcb, 0x73, 0x1c, 0xc9,
	0x71, 0x37, 0x7a, 0x06, 0xaf, 0x49, 0x60, 0x80, 0x46, 0x11, 0x20, 0x06, 0x03, 0xe2, 0xc1, 0x26,
	0x77, 0x89, 0x05, 0x77, 0xb1, 0x12, 0x29, 0xed, 0x83, 0xbb, 0x2b, 0x7d, 0xe0, 0x70, 0x08, 0x8e,
	0x88, 0x97, 0x1a, 0xc0, 0x52, 0x8f, 0x90, 0xfa, 0x6b, 0xcc, 0x14, 0x06, 0x2d, 0xce, 0x74, 0x37,
	0xbb, 0x7a, 0x48, 0xc2, 0x72, 0xc8, 0x56, 0x84, 0xc3, 0x11, 0xb2, 0x4e, 0x3e, 0x58, 0x17, 0xdf,
	0xe4, 0x70, 0xc8, 0x67, 0x87, 0xce, 0x76, 0x58, 0x17, 0xf9, 0xe2, 0x83, 0x43, 0x37, 0xdd, 0x1c,
	0xe1, 0x83, 0x23, 0xfc, 0x07, 0xf8, 0xea, 0xa8, 0xaa, 0x7e, 0x57, 0xf5, 0xcc, 0x00, 0xe4, 0xfa,
	0xb2, 0x3b, 0x5d, 0x95, 0x95, 0x59, 0x95, 0x95, 0x95, 0x99, 0x55, 0xf5, 0x2b, 0x10, 0xee, 0xf8,
	0xfe, 0x87, 0x2f, 0x7a, 0x56, 0xf3, 0x39, 0xb1, 0x3a, 0x2f, 0xb1, 0xf7, 0xa1, 0x69, 0xb5, 0x9b,
	0xec, 0x3f, 0x1f, 0x10, 0xa7, 0xd7, 0xe9, 0x9a, 0x3e, 0xde, 0x72, 0x3d, 0xc7, 0x77, 0x50, 0x99,
	0x16, 0x1a, 0x61, 0xa1, 0xf6, 0xdb, 0x31, 0x98, 0xd9, 0x6e, 0xe8, 0x4e, 0x07, 0xd7, 0x4c, 0x1f,
	0xb7, 0x1d, 0xef, 0x02, 0xcd, 0x40, 0xc1, 0x6a, 0x55, 0x94, 0x75, 0x65, 0xa3, 0xa4, 0x17, 0xac,
	0x16, 0x9a, 0x87, 0x31, 0xdf, 0xf2, 0x3b, 0xb8, 0x52, 0x60, 0x45, 0xfc, 0x03, 0x21, 0x18, 0x25,
	0x8e, 0xe7, 0x57, 0x8a, 0xeb, 0xca, 0x46, 0x59, 0x67, 0xbf, 0xd1, 0x1a, 0x4c, 0xf5, 0xdc, 0x96,
	0xe9, 0x63, 0xc3, 0xb7, 0xba, 0xb8, 0x32, 0xba, 0xae, 0x6c, 0x14, 0x75, 0xe0, 0x45, 0xc7, 0x56,
	0x97, 0x35, 0xf2, 0x2d, 0x97, 0x54, 0xc6, 0x18, 0x27, 0xf6, 0x1b, 0x3d, 0x82, 0x29, 0xd7, 0x73,
	0x5c, 0x83, 0x34, 0xb1, 0x8d, 0x49, 0x65, 0x7c, 0xbd, 0xb8, 0x31, 0x73, 0xef, 0xd6, 0x56, 0xaa,
	0x9b, 0x5b, 0xe9, 0x2e, 0x6e, 0x1d, 0x51, 0x5a, 0x1d, 0x68, 0x3b, 0xf6, 0x93, 0xa0, 0x4f, 0x60,
	0x8c, 0x7e, 0x91, 0xca, 0xc4, 0x7a, 0x71, 0x63, 0xea, 0x9e, 0xd6, 0xbf, 0xfd, 0xa1, 0xe7, 0xb8,
	0x3a, 0x6f, 0x50, 0x3d, 0x87, 0xb1, 0x5d, 0xf3, 0x14, 0x77, 0x84, 0x71, 0x23, 0x18, 0xb5, 0xcd,
	0x6e, 0x38, 0x6c, 0xf6, 0x1b, 0x7d, 0x06, 0xe3, 0x41, 0x3f, 0x8b, 0xc3, 0xf7, 0x33, 0x68, 0x52,
	0xfd, 0x77, 0x05, 0x46, 0xa9, 0xe4, 0xa1, 0x24, 0x3d, 0x80, 0x51, 0xff, 0xc2, 0xc5, 0x4c, 0xbf,
	0x33, 0xf7, 0xde, 0x1d, 0x3c, 0x9e, 0xe3, 0x0b, 0x17, 0xeb, 0xac, 0x0d, 0xed, 0x65, 0x87, 0x0e,
	0x89, 0x54, 0x46, 0x99, 0x36, 0x06, 0xf4, 0x92, 0x0d, 0x5f, 0x0f, 0x9a, 0xa0, 0xf7, 0x01, 0xb1,
	0x5f, 0x06, 0xc1, 0x1d, 0xdc, 0xf4, 0x8d, 0x8e, 0xd5, 0xb5, 0x7c, 0x36, 0x63, 0x65, 0x5d, 0x65,
	0x35, 0x47, 0xac, 0x62, 0x97, 0x96, 0x6b, 0x9f, 0xc3, 0x18, 0x1b, 0x24, 0x9a, 0x07, 0x95, 0xfd,
	0x38, 0xb1, 0x89, 0x8b, 0x9b, 0xd6, 0x99, 0x85, 0x5b, 0xea, 0x08, 0x2a, 0x43, 0x89, 0x95, 0x3e,
	0xc1, 0x66, 0x4b, 0x55, 0xd0, 0x0c, 0x00, 0xfb, 0x3c, 0x34, 0x6d, 0xdc, 0x51, 0x0b, 0xda, 0xc7,
	0x30, 0x19, 0x76, 0x1d, 0x2d, 0xc2, 0xb5, 0xf0, 0x77, 0x9a, 0x07, 0x82, 0x99, 0xb0, 0x62, 0x07,
	0xdb, 0x2d, 0xec, 0xa9, 0x8a, 0xf6, 0x51, 0x68, 0xb5, 0x3b, 0x1e, 0xc6, 0xbe, 0x65, 0xb7, 0x91,
	0x0a, 0x45, 0x82, 0x5f, 0x30, 0xa5, 0x96, 0x75, 0xfa, 0x93, 0x19, 0x1b, 0x7e, 0xed, 0x87, 0x5a,
	0xa5, 0xbf, 0xb5, 0x07, 0x61, 0xbb, 0x43, 0xcf, 0xe9, 0x38, 0xed, 0x1e, 0x8e, 0xa8, 0x94, 0x98,
	0x8a, 0x5a, 0xbc, 0xd9, 0x6b, 0x59, 0x4e, 0x68, 0xf1, 0xec, 0x43, 0xfb, 0xc3, 0x34, 0x8c, 0xf3,
	0xc6, 0x89, 0x09, 0x2c, 0xb3, 0x09, 0xbc, 0x0e, 0xe3, 0xe6, 0x4b, 0xd3, 0x37, 0xbd, 0xa0, 0x45,
	0xf0, 0x45, 0x19, 0x11, 0xff, 0xa2, 0xc3, 0x67, 0xb1, 0xa4, 0xf3, 0x0f, 0xde, 0xd5, 0xd7, 0x6c,
	0x79, 0x8c, 0xd1, 0xae, 0xbe, 0xa6, 0x74, 0x56, 0xd7, 0x6c, 0xe3, 0x60, 0x61, 0xf0, 0x0f, 0x56,
	0x6a, 0xfb, 0x9e, 0x53, 0x19, 0x0f, 0x4a, 0xe9, 0x07, 0xfa, 0x20, 0x30, 0x8c, 0x09, 0x66, 0x18,
	0x4b, 0xd2, 0xa9, 0x4d, 0xd8, 0xc2, 0x4d, 0x98, 0x6e, 0x59, 0x66, 0xc7, 0x69, 0x1b, 0x4d, 0xa7,
	0xe3, 0x78, 0x95, 0x49, 0xc6, 0x6b, 0x8a, 0x97, 0xd5, 0x68, 0x51, 0x64, 0x7e, 0xd3, 0x09, 0xf3,
	0xfb, 0x1a, 0xed, 0xb9, 0xe9, 0xe3, 0x4a, 0x99, 0x89, 0xa9, 0x4a, 0xc5, 0x1c, 0x51, 0x0a, 0x9d,
	0x13, 0xa2, 0x1b, 0x50, 0x6a, 0x9e, 0x9b, 0x9e, 0xd9, 0xf4, 0xb1, 0x57, 0x99, 0x61, 0xac, 0xe2,
	0x02, 0xf4, 0x29, 0x4c, 0x36, 0x03, 0x7b, 0xab, 0xcc, 0xae, 0x2b, 0x1b, 0x53, 0xf7, 0x56, 0xfa,
	0x1a, 0xa5, 0x1e, 0x91, 0xb3, 0x19, 0x32, 0xdb, 0xa4, 0xa2, 0xae, 0x17, 0xd9, 0x0c, 0x99, 0x6d,
	0x82, 0xaa, 0x30, 0xe9, 0x06, 0x33, 0x58, 0x99, 0x63, 0xb2, 0xa2, 0x6f, 0xf4, 0x0e, 0xcc, 0x84,
	0xbf, 0x0d, 0x3e, 0x8d, 0x88, 0x51, 0x94, 0xc3, 0xd2, 0x6d, 0x5a, 0x48, 0xe7, 0xcc, 0xb7, 0xba,
	0xa7, 0x1e, 0xae, 0x5c, 0xe3, 0x73, 0xc6, 0xbf, 0xd0, 0x32, 0x94, 0x5c, 0xcf, 0xe9, 0xba, 0xbe,
	0x61, 0xb5, 0x2a, 0xf3, 0x11, 0xef, 0xae, 0xeb, 0x37, 0x5a, 0x01, 0x6f, 0x5a, 0xf9, 0x12, 0x7b,
	0xc4, 0x72, 0xec, 0xca, 0x42, 0xc4, 0xbb, 0xeb, 0xfa, 0x5f, 0xf2, 0x42, 0xb4, 0x09, 0x73, 0xd8,
	0x36, 0x4f, 0x3b, 0xd8, 0xf0, 0x9a, 0xdd, 0x96, 0xe1, 0x61, 0xb7, 0x73, 0x51, 0xb9, 0xbe, 0xae,
	0x6c, 0x4c, 0xea, 0xb3, 0xbc, 0x42, 0x6f, 0x76, 0x5b, 0x3a, 0x2d, 0xa6, 0x4e, 0xb3, 0xe9, 0x78,
	0x36, 0xf6, 0x0c, 0xab, 0xe9, 0xd8, 0x95, 0x45, 0xc6, 0x0f, 0x78, 0x51, 0xa3, 0xe9, 0xd8, 0xe8,
	0x33, 0x28, 0xb5, 0x03, 0x2b, 0x27, 0x95, 0x0a, 0x5b, 0xd0, 0x72, 0xdd, 0x85, 0x6b, 0x41, 0x8f,
	0xe9, 0xa9, 0xad, 0xf5, 0xac, 0x56, 0x65, 0x89, 0x2f, 0x8b, 0x9e, 0xd5, 0x42, 0x5f, 0xc0, 0x34,
	0xd5, 0x8a, 0x6f, 0x78, 0x98, 0xf4, 0x3a, 0x7e, 0xa5, 0x2a, 0x9f, 0x60, 0x4a, 0xa2, 0x33, 0x0a,
	0x7d, 0xca, 0x8c, 0x3f, 0xd0, 0x0a, 0x80, 0x65, 0x13, 0xec, 0xf9, 0x86, 0xeb, 0x90, 0xca, 0x32,
	0xe3, 0x5b, 0xe2, 0x25, 0x87, 0x0e, 0x41, 0x15, 0x98, 0xc0, 0xaf, 0x5d, 0x87, 0xe0, 0x56, 0xe5,
	0x06, 0x1b, 0x6f, 0xf8, 0x89, 0x96, 0x60, 0x92, 0xcb, 0x35, 0xfd, 0xca, 0x0a, 0x8b, 0x0c, 0x13,
	0xec, 0x7b, 0xdb, 0x47, 0xef, 0xc2, 0x6c, 0xd3, 0xb1, 0xcf, 0xac, 0xb6, 0xd1, 0xb1, 0x9e, 0x63,
	0xc3, 0xee, 0x75, 0x2b, 0xab, 0x6c, 0x71, 0x94, 0x79, 0xf1, 0xae, 0xf5, 0x1c, 0xef, 0xf7, 0xba,
	0x48, 0x83, 0x72, 0x8f, 0x60, 0x2f, 0xa6, 0x5a, 0x63, 0xe2, 0xa7, 0x68, 0x61, 0x48, 0xb3, 0x02,
	0xd0, 0xf4, 0xb0, 0xe9, 0xe3, 0x16, 0x15, 0xb4, 0xce, 0x04, 0x95, 0x82, 0x92, 0x6d, 0x1f, 0x7d,
	0x0b, 0x80, 0xb5, 0xe6, 0xc6, 0x7d, 0x93, 0x8d, 0x7d, 0x4d, 0xaa, 0xcd, 0x2d, 0xca, 0x91, 0x5b,
	0x78, 0xa9, 0x13, 0xfe, 0xa4, 0xa3, 0x20, 0xbe, 0xe3, 0x5d, 0x50, 0xe3, 0xd0, 0xd8, 0x54, 0x4d,
	0xb0, 0xef, 0x46, 0x8b, 0xae, 0x34, 0x6c, 0xfb, 0x9e, 0x69, 0x37, 0xb1, 0xe1, 0x9b, 0xed, 0xca,
	0x2d, 0xbe, 0xd2, 0xc2, 0xb2, 0x63, 0xb3, 0x8d, 0x9e, 0x80, 0xda, 0x74, 0x3a, 0xcc, 0xad, 0x46,
	0xe6, 0x7b, 0xbb, 0xcf, 0x6a, 0x08, 0xbd, 0x94, 0x3e, 0x1b, 0x34, 0x8b, 0xdc, 0xd6, 0x17, 0x00,
	0xbc, 0x1f, 0x5d, 0xa7, 0x85, 0x2b, 0xef, 0xb0, 0x71, 0xac, 0xe6, 0x2c, 0x52, 0xc7, 0xbb, 0xd8,
	0x73, 0x5a, 0x58, 0x2f, 0x91, 0xf0, 0x27, 0xfa, 0x0e, 0xcc, 0xb5, 0x3d, 0xa7, 0xe7, 0x1a, 0x9e,
	0xd3, 0xc1, 0x06, 0xd7, 0x72, 0xe5, 0x5d, 0xd6, 0x93, 0x2c, 0x97, 0x1d, 0x4a, 0xc7, 0x96, 0x26,
	0xa3, 0xd2, 0x67, 0xdb, 0xe9, 0x02, 0x6a, 0xec, 0x4c, 0xbf, 0x8e, 0x67, 0x58, 0xf6, 0x99, 0x63,
	0x30, 0xef, 0x74, 0x87, 0xcd, 0xcc, 0x6c, 0x50, 0xd1, 0xb0, 0xcf, 0x1c, 0xe6, 0xe4, 0xd7, 0x60,
	0xca, 0x74, 0x5d, 0xc7, 0xb2, 0x7d, 0x83, 0x9a, 0xe5, 0x06, 0xa3, 0x82, 0xa0, 0xe8, 0xc4, 0x6a,
	0x51, 0x66, 0x6c, 0x8a, 0x59, 0xbf, 0x08, 0xf6, 0xa9, 0x15, 0x57, 0xde, 0x63, 0x9a, 0x9c, 0xa5,
	0x15, 0x6c, 0x44, 0xbc, 0x98, 0x2a, 0xdc, 0xc3, 0x1d, 0xd3, 0xb7, 0x1c, 0xdb, 0xb0, 0x5a, 0xa4,
	0xb2, 0xb9, 0x5e, 0xa4, 0xd6, 0x10, 0x96, 0x35, 0x5a, 0x84, 0x5a, 0x03, 0x4f, 0x3f, 0x98, 0x35,
	0xdc, 0xe5, 0xd6, 0x10, 0x94, 0x6c, 0xfb, 0xcc, 0xcb, 0x35, 0x1d, 0x17, 0x57, 0xde, 0xef, 0xe7,
	0xe5, 0x28, 0x85, 0xce, 0x09, 0xb5, 0x03, 0x28, 0x45, 0x76, 0x81, 0x2a, 0x30, 0x1f, 0x7d, 0x08,
	0x31, 0x2b, 0xaa, 0xa1, 0x3f, 0x68, 0xf0, 0x9b, 0x07, 0x35, 0x41, 0xdd, 0x61, 0xa5, 0x05, 0xed,
	0x9f, 0x0b, 0x50, 0xda, 0x6e, 0x1c, 0x9a, 0x9e, 0x6f, 0x63, 0x4f, 0x08, 0x2c, 0xb2, 0xcc, 0x60,
	0x19, 0x4a, 0x4d, 0xb3, 0xd3, 0x31, 0x58, 0x05, 0x0f, 0x2c, 0x93, 0xb4, 0x60, 0x9f, 0x56, 0xbe,
	0x07, 0xa3, 0x54, 0x75, 0x2c, 0xb8, 0x4c, 0xdd, 0x5b, 0x90, 0x0e, 0x48, 0x67, 0x24, 0xd4, 0x01,
	0x12, 0xab, 0x83, 0x6d, 0x1e, 0xdc, 0x27, 0xf5, 0xe0, 0x2b, 0x74, 0x19, 0xe3, 0xb1, 0xcb, 0xa0,
	0xeb, 0xf3, 0xdc, 0xb4, 0xdb, 0x38, 0x30, 0x17, 0xdb, 0x67, 0xd1, 0xa7, 0xac, 0x97, 0x79, 0x31,
	0x33, 0x06, 0xdb, 0x47, 0x1f, 0xc1, 0x38, 0x71, 0x7a, 0x5e, 0x13, 0xb3, 0x28, 0x23, 0x33, 0xc8,
	0x60, 0x9c, 0x47, 0x8c, 0x4a, 0x0f, 0xa8, 0xd9, 0x2c, 0xd9, 0x04, 0xfb, 0x7c, 0x48, 0x25, 0xd6,
	0x9b, 0x12, 0x2b, 0x61, 0x63, 0x8a, 0xaa, 0xd9, 0xc8, 0x20, 0x51, 0x4d, 0x05, 0x6b, 0x7f, 0x50,
	0x00, 0x8e, 0xce, 0x4d, 0x0f, 0xb7, 0xa4, 0xb1, 0x59, 0xa6, 0xc2, 0x28, 0xde, 0x16, 0x93, 0xf1,
	0x36, 0x8e, 0xe2, 0xa3, 0xa9, 0x28, 0x9e, 0x8a, 0x6c, 0x63, 0xd9, 0xc8, 0x16, 0x86, 0xa7, 0xf1,
	0x9c, 0xf0, 0x34, 0x31, 0x30, 0x3c, 0x4d, 0x4a, 0xc2, 0x93, 0xf6, 0x3b, 0x25, 0x4c, 0x55, 0xa8,
	0xd1, 0xd0, 0x05, 0x24, 0x8c, 0x2c, 0x98, 0xa8, 0x42, 0xd2, 0xb7, 0x07, 0x51, 0x9b, 0x67, 0x8d,
	0x77, 0xa4, 0xd3, 0x1f, 0xf2, 0x4b, 0x38, 0x38, 0xde, 0xea, 0xed, 0x1b, 0xf7, 0x3f, 0x14, 0x61,
	0xb6, 0x61, 0xfb, 0x98, 0xaa, 0xca, 0x7a, 0x89, 0x77, 0xa8, 0xee, 0xb3, 0xc9, 0xef, 0xe7, 0x91,
	0xd1, 0x14, 0x58, 0xa7, 0x6f, 0x67, 0x3a, 0x9d, 0x69, 0x9f, 0x31, 0x9d, 0x40, 0x07, 0xc5, 0x58,
	0x07, 0x8b, 0x30, 0xc1, 0xac, 0xd4, 0x6a, 0xb1, 0x69, 0x2c, 0xeb, 0xe3, 0xf4, 0xb3, 0xc1, 0x02,
	0x90, 0xef, 0xb8, 0x56, 0x93, 0xd6, 0xf0, 0x74, 0x76, 0x82, 0x7d, 0x37, 0x12, 0x5b, 0x9c, 0xf1,
	0xcc, 0x16, 0xa7, 0x85, 0x49, 0x33, 0x98, 0x41, 0xf6, 0x3b, 0x35, 0xb3, 0x93, 0x99, 0x99, 0xfd,
	0x34, 0xd4, 0x7e, 0x89, 0x0d, 0xe4, 0xd6, 0x80, 0x81, 0x24, 0x93, 0xa7, 0x87, 0x30, 0xc9, 0xe2,
	0x64, 0xcf, 0xe3, 0x06, 0x2e, 0x66, 0xfc, 0x99, 0xd6, 0xf5, 0x80, 0x5a, 0x8f, 0xda, 0x65, 0x22,
	0xdf, 0x54, 0x36, 0xf2, 0xa5, 0x5d, 0xe1, 0x74, 0xc6, 0x15, 0x6a, 0xbf, 0x04, 0x98, 0xad, 0x31,
	0xe2, 0xc0, 0x49, 0xe0, 0x17, 0xe8, 0xe3, 0xc0, 0x99, 0x28, 0xcc, 0x99, 0x64, 0xc7, 0x93, 0xa1,
	0xde, 0x8a, 0x5d, 0x4b, 0xf5, 0x9f, 0x4a, 0x30, 0x2a, 0x5d, 0x8c, 0x61, 0xf2, 0x5a, 0x18, 0x2e,
	0x79, 0xbd, 0x1f, 0xd9, 0x06, 0x37, 0xe8, 0x65, 0xb9, 0x83, 0x16, 0x4c, 0x22, 0x93, 0x5e, 0x87,
	0x2e, 0x60, 0x2c, 0xe1, 0x02, 0xe2, 0xc5, 0x3e, 0x9e, 0x4d, 0xd9, 0xb9, 0x6b, 0x98, 0x48, 0xba,
	0x86, 0x28, 0x1d, 0x9e, 0xbc, 0x52, 0x3a, 0x5c, 0xca, 0x3a, 0x0d, 0x9a, 0xf4, 0x05, 0xf9, 0x2d,
	0x35, 0x47, 0x08, 0x92, 0xbe, 0xa0, 0xa8, 0xd1, 0x8a, 0xbc, 0xca, 0x54, 0x8e, 0x57, 0x99, 0x1e,
	0xe8, 0x55, 0xca, 0xfd, 0x93, 0xde, 0x99, 0xfc, 0xa4, 0x77, 0x76, 0x60, 0xd2, 0xab, 0xca, 0x92,
	0xde, 0x4c, 0x22, 0x3b, 0x27, 0x24, 0xb2, 0xd2, 0xac, 0x18, 0xc9, 0xb3, 0xe2, 0x54, 0xd2, 0x7b,
	0xed, 0x92, 0x49, 0x6f, 0x3a, 0x47, 0x5d, 0xe8, 0x93, 0xa3, 0x5e, 0x4f, 0xe7, 0xa8, 0x92, 0x44,
	0x74, 0x51, 0x96, 0x88, 0x26, 0xb3, 0xc0, 0x4a, 0xff, 0x2c, 0x70, 0x69, 0xb8, 0x2c, 0xb0, 0xfa,
	0x16, 0xb2, 0xc0, 0xe5, 0xb7, 0x92, 0x05, 0xde, 0x78, 0x8b, 0x59, 0xe0, 0xca, 0x50, 0x59, 0xe0,
	0xea, 0x70, 0x59, 0xe0, 0xda, 0x70, 0x59, 0xe0, 0xba, 0x98, 0x05, 0x46, 0x69, 0xde, 0xcd, 0x61,
	0xd3, 0xbc, 0x67, 0x30, 0xbb, 0xdd, 0x60, 0x63, 0xbe, 0xfc, 0x41, 0x01, 0x5f, 0xa7, 0x96, 0xe3,
	0x59, 0xfe, 0x45, 0x10, 0x98, 0xa2, 0x6f, 0xed, 0x2f, 0x0b, 0x30, 0x9b, 0xd1, 0x25, 0xfa, 0x9c,
	0x2d, 0x3e, 0x26, 0x85, 0x54, 0x14, 0x66, 0xeb, 0xe2, 0x24, 0xa6, 0x3a, 0xa3, 0xc7, 0x0d, 0xe8,
	0xea, 0x6c, 0x9e, 0x9b, 0xbe, 0x11, 0x7b, 0x1b, 0xde, 0x19, 0x9a, 0x9b, 0xf9, 0xb5, 0xc8, 0xe3,
	0x7c, 0x00, 0x28, 0x52, 0x53, 0x4c, 0xca, 0xf3, 0x9f, 0xb9, 0xb0, 0xa6, 0x96, 0xcc, 0x6a, 0x58,
	0xec, 0x1b, 0x4d, 0xc4, 0xbe, 0xa7, 0x30, 0xf7, 0x0a, 0x77, 0x9a, 0x4e, 0x17, 0x1b, 0x71, 0x7f,
	0xc7, 0x86, 0xea, 0xaf, 0x1a, 0x34, 0x0c, 0x0b, 0x88, 0xf6, 0x05, 0xa8, 0xe9, 0x00, 0x42, 0xdc,
	0x28, 0x79, 0x55, 0x06, 0x26, 0xaf, 0xda, 0x6f, 0x4a, 0x30, 0x7b, 0xc2, 0x82, 0xd7, 0xb0, 0xe1,
	0x2a, 0x43, 0x9d, 0x0c, 0x57, 0x7f, 0x9c, 0xcc, 0x09, 0x57, 0x41, 0x28, 0x29, 0x88, 0xa1, 0xa4,
	0x28, 0x0d, 0x25, 0xa3, 0xf2, 0x50, 0x32, 0x26, 0x0d, 0x25, 0xe3, 0x57, 0x0a, 0x25, 0x13, 0x03,
	0x42, 0xc9, 0x64, 0x6e, 0x28, 0x29, 0xe5, 0x84, 0x12, 0x18, 0x18, 0x4a, 0xa6, 0xfa, 0x87, 0x92,
	0xe9, 0xfc, 0x50, 0x52, 0x1e, 0x18, 0x4a, 0x66, 0x86, 0x08, 0x25, 0xb3, 0xc3, 0x85, 0x12, 0x75,
	0x88, 0x50, 0x32, 0xf7, 0x46, 0xa1, 0x04, 0xf5, 0x09, 0x25, 0xd7, 0x06, 0x86, 0x92, 0xf9, 0x41,
	0xa1, 0x64, 0xa1, 0x7f, 0x28, 0xb9, 0x3e, 0x5c, 0x28, 0x59, 0x7c, 0x0b, 0xa1, 0xa4, 0xf2, 0x56,
	0x42, 0xc9, 0xd2, 0x5b, 0x0c, 0x25, 0xd5, 0xa1, 0x42, 0xc9, 0xf2, 0x70, 0xa1, 0xe4, 0xc6, 0x70,
	0xa1, 0x64, 0xa5, 0x4f, 0x28, 0x59, 0x1d, 0x36, 0x94, 0x7c, 0x01, 0x6a, 0xda, 0xf5, 0x5c, 0xce,
	0xd1, 0xdd, 0x84, 0xd9, 0x47, 0xb8, 0x83, 0x93, 0x7e, 0x2e, 0xe3, 0xa5, 0x34, 0x04, 0x6a, 0x9a,
	0x84, 0xb8, 0xda, 0x2a, 0x4c, 0xef, 0x60, 0x3f, 0xbf, 0xcd, 0x03, 0x28, 0x27, 0xea, 0x2f, 0xd7,
	0xa5, 0x6f, 0x80, 0x1a, 0xb5, 0xdd, 0xb5, 0x88, 0x4f, 0xf9, 0xaf, 0xc3, 0x74, 0xb0, 0xeb, 0x32,
	0x3a, 0x16, 0xf1, 0x2b, 0x05, 0xa6, 0x3a, 0xe0, 0x5b, 0x2f, 0x4a, 0xa4, 0xed, 0xc0, 0x5c, 0xa6,
	0x15, 0x71, 0xd1, 0x3d, 0x28, 0xb1, 0x66, 0xac, 0x0d, 0x0f, 0x7d, 0x39, 0xa2, 0x27, 0xbd, 0xa0,
	0x9d, 0xb6, 0x01, 0xf3, 0x3b, 0xd8, 0x3f, 0x21, 0xd8, 0x4b, 0x77, 0x21, 0xd8, 0x0a, 0x2a, 0xd1,
	0x56, 0x50, 0x7b, 0x0a, 0x0b, 0x12, 0xca, 0x2b, 0x8a, 0xfd, 0xb9, 0x02, 0x6b, 0x02, 0xb7, 0x67,
	0x96, 0x7f, 0xbe, 0xcd, 0x8d, 0x4d, 0xda, 0x05, 0xf4, 0x51, 0x20, 0x69, 0xb8, 0x5d, 0x0f, 0x93,
	0xc6, 0xec, 0x7a, 0x1e, 0xc6, 0xf8, 0xc5, 0x0b, 0x4f, 0x20, 0xf8, 0x87, 0xf6, 0x25, 0xac, 0xf7,
	0xef, 0xc2, 0x15, 0xc7, 0x46, 0xa0, 0xb2, 0x83, 0xfd, 0x83, 0xb3, 0x33, 0xab, 0x69, 0x99, 0x9d,
	0xb4, 0x5a, 0x33, 0xf1, 0x45, 0x11, 0xe2, 0xcb, 0x15, 0x87, 0xa8, 0x1d, 0xc0, 0x52, 0x8e, 0xd0,
	0x2b, 0x8e, 0xe2, 0x3a, 0x33, 0x8c, 0x87, 0xa6, 0x6d, 0xf3, 0x65, 0x1d, 0x8c, 0x40, 0xfb, 0x1e,
	0x33, 0x83, 0x6c, 0x39, 0x71, 0xd1, 0xb7, 0x41, 0x3d, 0x65, 0xa5, 0xc6, 0x90, 0xb2, 0x66, 0x4e,
	0x53, 0x4c, 0xb4, 0xaf, 0xc3, 0xfc, 0x91, 0x44, 0x22, 0x75, 0xea, 0xc1, 0x6a, 0xe0, 0x09, 0x5d,
	0x59, 0x9f, 0xe0, 0x2b, 0x81, 0x68, 0x8b, 0xb0, 0x70, 0x24, 0xeb, 0x8c, 0xf6, 0x3f, 0x0a, 0xa0,
	0x30, 0x23, 0x0a, 0x8e, 0xc9, 0x28, 0xab, 0x1a, 0x4c, 0xb8, 0xfc, 0x2b, 0x58, 0x9a, 0xef, 0xe5,
	0x6c, 0xc3, 0xe3, 0x36, 0x5b, 0xe1, 0xcf, 0xb0, 0x65, 0xf5, 0x6f, 0x15, 0x98, 0x78, 0x6b, 0x47,
	0x8c, 0xb9, 0x07, 0x2c, 0xf1, 0xf1, 0xdf, 0xf8, 0x65, 0x8e, 0xff, 0xb4, 0x06, 0x5c, 0x13, 0x06,
	0xc1, 0x4c, 0x20, 0x33, 0xf2, 0x4a, 0x1e, 0xbf, 0x68, 0xa0, 0xda, 0xdf, 0x2b, 0x80, 0x42, 0x6f,
	0x7b, 0x19, 0x25, 0x8a, 0x6d, 0x44, 0x25, 0x7e, 0xe7, 0xed, 0xe9, 0x90, 0x0e, 0x59, 0x10, 0x79,
	0xc5, 0x21, 0xdf, 0x06, 0x14, 0x7a, 0xff, 0xc4, 0x88, 0xb3, 0xfe, 0x7e, 0x01, 0xae, 0x09, 0x54,
	0xc4, 0xd5, 0xb6, 0xe1, 0x7a, 0x8d, 0x1d, 0xe1, 0xc6, 0xc5, 0xf2, 0x80, 0x91, 0x9c, 0xf5, 0x42,
	0x72, 0xd6, 0xb5, 0x3d, 0x58, 0x94, 0xb2, 0xb8, 0xe2, 0x70, 0xea, 0xb0, 0x9c, 0xd1, 0x4c, 0xed,
	0xdc, 0xf4, 0x79, 0x3e, 0x2c, 0xe9, 0x56, 0x7c, 0x88, 0x5d, 0x48, 0x1e, 0x62, 0x6b, 0xab, 0x70,
	0x23, 0x9f, 0x0d, 0x71, 0x69, 0x58, 0x65, 0xd1, 0xa8, 0x8f, 0xca, 0x1e, 0x07, 0x61, 0xee, 0x4d,
	0x27, 0x68, 0x03, 0xe6, 0x1f, 0x9a, 0x7e, 0xf3, 0x3c, 0x2b, 0x4f, 0x85, 0x62, 0xec, 0x1f, 0xe8,
	0x4f, 0xed, 0xf7, 0x0a, 0x2c, 0x48, 0x48, 0x89, 0x8b, 0x4e, 0x60, 0x2a, 0x60, 0x67, 0x74, 0x4d,
	0x37, 0x70, 0x52, 0xdf, 0xc8, 0xc8, 0x96, 0x36, 0x0d, 0xed, 0x78, 0xcf, 0x74, 0xeb, 0xb6, 0xef,
	0x5d, 0xe8, 0xe0, 0x46, 0x05, 0xd5, 0x67, 0x30, 0x9b, 0xa9, 0xa6, 0xbd, 0x7a, 0x8e, 0x2f, 0xc2,
	0x10, 0xf6, 0x1c, 0x5f, 0xa0, 0x2d, 0x18, 0x7b, 0x69, 0x76, 0x7a, 0xdc, 0xba, 0xfb, 0x8d, 0x98,
	0x93, 0x3d, 0x28, 0x7c, 0xa2, 0x68, 0xaf, 0xe1, 0x5a, 0x14, 0xa8, 0xd2, 0x43, 0x7e, 0x4b, 0xf1,
	0x31, 0x61, 0x8e, 0xc5, 0x94, 0x39, 0x7e, 0x27, 0x91, 0x1d, 0xbc, 0xe9, 0xcc, 0x61, 0x58, 0xcc,
	0xf2, 0xca, 0x4d, 0x36, 0xd0, 0x27, 0x00, 0xd1, 0x48, 0x08, 0xcb, 0x7f, 0xfa, 0x0e, 0xa5, 0x14,
	0x0e, 0x85, 0x68, 0xcf, 0x58, 0xf4, 0x95, 0x88, 0x21, 0x2e, 0xfa, 0x0c, 0xa6, 0xc3, 0x89, 0x4f,
	0x84, 0xa7, 0xfc, 0xbe, 0x87, 0x66, 0xc2, 0xc2, 0xd3, 0x7f, 0x8e, 0xc3, 0xe2, 0x89, 0x4b, 0xf7,
	0x2a, 0x99, 0xcb, 0x75, 0xfc, 0x02, 0x7d, 0x0a, 0x05, 0xc7, 0x65, 0xfd, 0x9f, 0x91, 0x78, 0x43,
	0x69, 0x9b, 0xad, 0x03, 0x57, 0x2f, 0x38, 0x2e, 0x7a, 0x9a, 0xb8, 0xcb, 0xe7, 0x36, 0xf1, 0xe1,
	0x90, 0x0c, 0xc4, 0xdb, 0xfd, 0xea, 0xdf, 0x29, 0x21, 0xfe, 0xe6, 0x0d, 0x7a, 0xc4, 0x97, 0x6e,
	0x41, 0x00, 0xd4, 0x14, 0xa5, 0xd0, 0x9d, 0xd1, 0xcb, 0x43, 0x77, 0xfe, 0xa6, 0x10, 0x40, 0x77,
	0xbe, 0xe2, 0x4e, 0x86, 0xa8, 0x9f, 0xd1, 0x2b, 0xa0, 0x7e, 0x1e, 0x45, 0xa8, 0x1f, 0x7e, 0x26,
	0xf3, 0xfe, 0x90, 0xdd, 0x1b, 0x06, 0xfe, 0x33, 0x2e, 0x87, 0xff, 0x54, 0xff, 0x4d, 0x81, 0xc9,
	0xcb, 0x03, 0xc7, 0x18, 0x06, 0xac, 0x98, 0x8f, 0x01, 0x1b, 0xbd, 0x1a, 0x06, 0x6c, 0x3b, 0xc4,
	0x80, 0xf1, 0xf1, 0xdf, 0x1d, 0x72, 0xfc, 0x09, 0x30, 0x98, 0x76, 0x1f, 0x0a, 0x07, 0x2e, 0x9a,
	0x83, 0xf2, 0x81, 0x9b, 0xbe, 0xf3, 0x9a, 0x86, 0xc9, 0x03, 0x97, 0x67, 0x29, 0xaa, 0xc2, 0xbf,
	0x78, 0x7c, 0x51, 0x0b, 0x5a, 0x15, 0x2a, 0x72, 0xde, 0xc4, 0xd5, 0x1e, 0xc0, 0x62, 0x72, 0x6f,
	0x96, 0x5c, 0x81, 0x83, 0x12, 0x6b, 0xca, 0x57, 0xde, 0x96, 0xb8, 0xda, 0x6b, 0x58, 0x8a, 0xdd,
	0x7d, 0x96, 0xf3, 0x4d, 0x98, 0x4e, 0x70, 0xe6, 0x21, 0xa6, 0xa4, 0x4f, 0xc5, 0xac, 0x09, 0xfa,
	0x24, 0x73, 0xeb, 0xb6, 0x9e, 0xdd, 0xa4, 0x63, 0x3f, 0xe4, 0x98, 0xc9, 0xd6, 0x4e, 0xa1, 0x9a,
	0x27, 0x99, 0xb8, 0xe8, 0x11, 0xcc, 0xc4, 0xa2, 0xed, 0x33, 0x27, 0x3c, 0xd0, 0x1c, 0x80, 0xf6,
	0x29, 0x47, 0x7d, 0xa3, 0x6d, 0xb4, 0xbb, 0xb0, 0xa8, 0x63, 0xe2, 0xc8, 0xfc, 0x56, 0x22, 0x6a,
	0x96, 0x78, 0xd4, 0xac, 0x42, 0x45, 0x4e, 0x4c, 0x5c, 0xed, 0x0e, 0x4c, 0xb3, 0xab, 0xe1, 0x30,
	0xab, 0x49, 0x84, 0x0d, 0x25, 0x15, 0x36, 0xbe, 0x05, 0xe5, 0x04, 0x21, 0x71, 0x93, 0x71, 0xb0,
	0xc4, 0xe3, 0xe0, 0x32, 0x94, 0xf0, 0x6b, 0xd7, 0xf2, 0xb0, 0x61, 0xf2, 0x6c, 0xa3, 0xc8, 0x2e,
	0xdf, 0x2c, 0x0f, 0x6f, 0xfb, 0xda, 0x6d, 0x96, 0x2c, 0xc4, 0xd7, 0xd0, 0x41, 0x57, 0xd3, 0x2c,
	0xb4, 0x87, 0x6c, 0x0f, 0x9c, 0xa4, 0x22, 0x2e, 0xfa, 0x20, 0xb5, 0xf3, 0xce, 0x86, 0x8c, 0x04,
	0x31, 0xdf, 0x7d, 0xdf, 0x85, 0xc5, 0xed, 0x4e, 0xc7, 0x69, 0xb2, 0x8a, 0x46, 0x0b, 0xdb, 0x3e,
	0xb5, 0x51, 0x4f, 0x2e, 0xf0, 0x01, 0x54, 0xe4, 0xc4, 0xc4, 0x45, 0xab, 0x00, 0x56, 0x54, 0x12,
	0x9a, 0x5f, 0x5c, 0xa2, 0xed, 0x42, 0xe5, 0x4b, 0xec, 0x59, 0x67, 0x17, 0xc3, 0x48, 0xca, 0x70,
	0x2b, 0x08, 0xdc, 0x30, 0x2c, 0xe5, 0x70, 0xe3, 0xca, 0xce, 0x44, 0xd3, 0xbc, 0x74, 0x13, 0xad,
	0x00, 0xbc, 0xb4, 0x88, 0xc5, 0x0e, 0x87, 0x5a, 0x81, 0x13, 0x29, 0x05, 0x25, 0x8d, 0x96, 0xf6,
	0x63, 0x28, 0x6d, 0x37, 0xf6, 0x30, 0x21, 0x66, 0x5b, 0xbc, 0x6a, 0x16, 0x2f, 0xcc, 0x57, 0x20,
	0x4c, 0x87, 0xe2, 0x4c, 0xa2, 0x14, 0x94, 0x34, 0x58, 0x83, 0x53, 0xcb, 0x66, 0xde, 0x78, 0x5a,
	0xa7, 0x3f, 0xb5, 0x6f, 0xc3, 0xdc, 0x33, 0xcf, 0xa2, 0x4b, 0x32, 0x10, 0x42, 0xb5, 0xb1, 0x09,
	0xc5, 0x2e, 0x69, 0xe7, 0xe6, 0x15, 0x21, 0x25, 0x25, 0xd2, 0xee, 0x02, 0xca, 0x32, 0x20, 0x2e,
	0x5a, 0x80, 0xf1, 0x2e, 0x69, 0xc7, 0x6e, 0x60, 0xac, 0x4b, 0xda, 0x8d, 0x96, 0xe6, 0x82, 0x7a,
	0xd8, 0xeb, 0x74, 0x52, 0xc2, 0x44, 0x5d, 0xa5, 0x07, 0x51, 0xc8, 0x0e, 0x22, 0xe6, 0x5d, 0x4c,
	0xf0, 0x8e, 0x4f, 0x18, 0x46, 0x93, 0x27, 0x0c, 0x4f, 0x60, 0x2e, 0x23, 0x91, 0xb8, 0xe8, 0x3e,
	0x4c, 0x52, 0x0e, 0x7d, 0x13, 0x90, 0x90, 0x7e, 0xa2, 0x4b, 0xda, 0x2c, 0xf9, 0xf8, 0x21, 0xcc,
	0xd5, 0x3a, 0xd8, 0xf4, 0xbe, 0x8a, 0xce, 0x6b, 0xf3, 0x80, 0xb2, 0xcc, 0x89, 0xab, 0xfd, 0xae,
	0x08, 0x73, 0x47, 0xd8, 0xf4, 0x9a, 0xe7, 0x27, 0x24, 0xde, 0xc9, 0xac, 0x00, 0x10, 0xdf, 0xf4,
	0x7c, 0x0e, 0x4a, 0x56, 0xf8, 0xc5, 0x37, 0x2b, 0x61, 0x98, 0xe4, 0x25, 0x98, 0xc4, 0x76, 0x8b,
	0x57, 0xf2, 0x55, 0x3d, 0x81, 0xed, 0x16, 0xab, 0xba, 0xc7, 0x8f, 0xff, 0x8b, 0x52, 0x0f, 0xc9,
	0x05, 0xf1, 0xe3, 0xc3, 0xd7, 0x75, 0xbb, 0xd7, 0xe5, 0x17, 0x04, 0x19, 0xaf, 0x3e, 0x2a, 0x1c,
	0x97, 0x44, 0xe7, 0xff, 0x63, 0xc3, 0x9e, 0xff, 0x3f, 0x84, 0x99, 0x33, 0xab, 0xe3, 0x63, 0xcf,
	0x08, 0xcf, 0x9a, 0x41, 0x7a, 0x1b, 0xfe, 0x98, 0x11, 0x31, 0x64, 0x00, 0xd6, 0xcb, 0x67, 0x89,
	0xaf, 0xd4, 0x7a, 0x9a, 0x4a, 0xad, 0xa7, 0x30, 0x0b, 0x19, 0x4f, 0x64, 0x21, 0xfd, 0x2f, 0x1c,
	0x6e, 0xc2, 0x34, 0x61, 0x63, 0x37, 0xcc, 0x33, 0x4a, 0x30, 0xc9, 0x41, 0x78, 0xbc, 0x6c, 0x9b,
	0x16, 0xa1, 0x07, 0x10, 0x7c, 0xf2, 0xbc, 0xbe, 0x24, 0xcd, 0xeb, 0xb9, 0x02, 0x59, 0x02, 0x03,
	0x24, 0xfa, 0xad, 0xfd, 0x95, 0x02, 0x28, 0x3b, 0x89, 0x2c, 0x11, 0x9e, 0x89, 0xcf, 0x71, 0x07,
	0x9f, 0xd4, 0x4c, 0x87, 0x67, 0xbb, 0xd4, 0x16, 0xe9, 0x2e, 0xb1, 0x63, 0x12, 0xbf, 0x11, 0x39,
	0x13, 0xfe, 0x45, 0x27, 0xab, 0xe3, 0x98, 0x2d, 0xe3, 0xcc, 0xb2, 0x2d, 0x72, 0xce, 0x26, 0x7a,
	0x52, 0x07, 0x5a, 0xf4, 0x98, 0x95, 0x68, 0x7f, 0x5d, 0x80, 0xeb, 0x2c, 0xda, 0xf1, 0x60, 0x9f,
	0x34, 0xab, 0x3d, 0xf1, 0x84, 0xea, 0x6b, 0xb2, 0x0d, 0x99, 0xd0, 0x32, 0x38, 0x6c, 0x48, 0x1f,
	0x5e, 0x55, 0x7f, 0xa3, 0x00, 0xc4, 0x15, 0xb9, 0x81, 0x2a, 0x73, 0x21, 0x51, 0xe8, 0x73, 0x21,
	0x51, 0x4c, 0x5f, 0x48, 0x0c, 0x34, 0x4c, 0xc9, 0x8d, 0xc5, 0x98, 0xe4, 0xc6, 0x42, 0x5b, 0x82,
	0x45, 0xe9, 0xc0, 0x58, 0xb6, 0xc3, 0xb5, 0xc5, 0xd3, 0x96, 0xa4, 0xb6, 0xb2, 0xe7, 0xc3, 0x8a,
	0x70, 0x3e, 0x1c, 0xb2, 0xcd, 0xb6, 0x25, 0xae, 0xf6, 0x53, 0xa8, 0x26, 0x8f, 0xd0, 0x93, 0xd8,
	0x54, 0xc9, 0x91, 0x40, 0x12, 0x68, 0x5a, 0x48, 0x03, 0x4d, 0xef, 0xc1, 0x78, 0x80, 0x7a, 0x2d,
	0x0e, 0x44, 0xbd, 0x06, 0x94, 0xda, 0x4a, 0x7c, 0x20, 0x21, 0x08, 0x27, 0xae, 0xb6, 0x01, 0x65,
	0xaa, 0x98, 0xf8, 0xa4, 0x3d, 0x37, 0xc5, 0x50, 0x39, 0x6c, 0x2a, 0x71, 0x48, 0xbf, 0x09, 0xb3,
	0x1c, 0x2b, 0x35, 0x44, 0x6b, 0x04, 0x6a, 0x9a, 0x96, 0xb8, 0x92, 0xf3, 0xed, 0xe7, 0x98, 0xc8,
	0xdd, 0xec, 0xe0, 0xf3, 0xf9, 0x63, 0xb8, 0x2e, 0x63, 0x46, 0x5c, 0xf4, 0x00, 0x18, 0x00, 0x36,
	0x69, 0xe9, 0x2b, 0x7d, 0x91, 0x65, 0xfa, 0x24, 0xa5, 0x67, 0x5c, 0x5b, 0xb0, 0xb2, 0x83, 0xfd,
	0x5d, 0xd3, 0xc7, 0xc4, 0x3f, 0x09, 0xc0, 0x46, 0xa9, 0xe3, 0xe5, 0x34, 0x2c, 0x49, 0xc9, 0x22,
	0x34, 0xe3, 0x4d, 0x53, 0x39, 0xdc, 0x53, 0x48, 0xce, 0xc5, 0x5b, 0xb0, 0xda, 0x4f, 0x0a, 0xbf,
	0xde, 0x18, 0xec, 0x34, 0x18, 0x09, 0x9d, 0x02, 0xea, 0x1e, 0x12, 0xa9, 0x07, 0xf7, 0x16, 0xda,
	0x0f, 0x60, 0x96, 0x3b, 0xa6, 0x78, 0xba, 0x2a, 0x30, 0xd1, 0x74, 0x6c, 0x1f, 0xdb, 0x21, 0x2e,
	0x20, 0xfc, 0xcc, 0x72, 0x29, 0x45, 0x3e, 0x47, 0x3e, 0x82, 0x3f, 0x57, 0x40, 0x4d, 0x33, 0xbf,
	0xda, 0x21, 0x78, 0xbe, 0xdc, 0x81, 0xbe, 0xee, 0x1f, 0x8b, 0x50, 0xe1, 0x3b, 0x9c, 0x0c, 0xd6,
	0x8c, 0x0e, 0xf4, 0x29, 0x8c, 0xb6, 0xcd, 0x6e, 0x98, 0xa4, 0x7e, 0x2c, 0x3d, 0x83, 0x16, 0x9b,
	0x65, 0x51, 0x6b, 0x3a, 0x63, 0x52, 0xfd, 0xd7, 0x82, 0x08, 0x0b, 0x8c, 0x61, 0x80, 0xca, 0x15,
	0x60, 0x80, 0xb9, 0xe9, 0x62, 0x12, 0xf4, 0x57, 0xcc, 0x01, 0xfd, 0x8d, 0xca, 0x40, 0x7f, 0x63,
	0x39, 0xa0, 0xbf, 0xf1, 0x3c, 0xd0, 0xdf, 0xc4, 0x1b, 0x81, 0xfe, 0x26, 0xaf, 0x06, 0xfa, 0xd3,
	0xee, 0xc2, 0x52, 0x8e, 0xf2, 0x89, 0xf0, 0xce, 0x48, 0xfb, 0xef, 0x02, 0xdd, 0xa9, 0xb6, 0xae,
	0x32, 0xc3, 0x79, 0xcd, 0x72, 0x66, 0xf8, 0xe7, 0x85, 0xc1, 0xc0, 0xcf, 0xe4, 0xd4, 0x14, 0x72,
	0xa6, 0xa6, 0x28, 0x9b, 0x9a, 0xd1, 0x9c, 0xa9, 0x19, 0xcb, 0x9b, 0x9a, 0xf1, 0x37, 0x9a, 0x9a,
	0x89, 0x2b, 0x4e, 0xcd, 0x32, 0x2c, 0xe5, 0x68, 0x8d, 0xb8, 0xda, 0x27, 0xb0, 0x92, 0x88, 0x76,
	0x92, 0xe9, 0x58, 0x84, 0x89, 0x64, 0xac, 0x2c, 0xe9, 0xe3, 0x16, 0xf7, 0xd3, 0xeb, 0xb0, 0xda,
	0xaf, 0x25, 0x71, 0xb5, 0xff, 0x28, 0x40, 0x85, 0xfb, 0x12, 0x09, 0xdf, 0xeb, 0x30, 0xde, 0xec,
	0x79, 0xc4, 0x09, 0x77, 0x7c, 0xc1, 0x57, 0xec, 0x96, 0x0a, 0x09, 0xb7, 0xf4, 0x7f, 0x0e, 0xaf,
	0x7d, 0x0b, 0x4b, 0xe2, 0x4d, 0x60, 0xb8, 0x1b, 0xa0, 0xc6, 0x10, 0x5a, 0xc3, 0x33, 0xed, 0x36,
	0xae, 0xc0, 0x7a, 0x71, 0xa3, 0xa8, 0xcf, 0x44, 0x40, 0x5a, 0x9d, 0x96, 0x6a, 0x6d, 0x58, 0xca,
	0x51, 0x31, 0xf3, 0xdb, 0xc9, 0x60, 0xb3, 0xda, 0xbf, 0x03, 0x41, 0xd4, 0x89, 0xe7, 0xa5, 0x90,
	0x9c, 0x17, 0xed, 0x8f, 0x4a, 0x60, 0x29, 0xb9, 0x0b, 0x57, 0x87, 0x31, 0xba, 0xe6, 0xc2, 0x93,
	0x96, 0xcf, 0xf3, 0x93, 0xd0, 0x21, 0x96, 0x2f, 0x67, 0x55, 0xc5, 0x83, 0x97, 0x6f, 0x72, 0xaa,
	0x8a, 0x57, 0x5c, 0x22, 0xa1, 0x2d, 0xe7, 0xaf, 0x93, 0xaf, 0xb3, 0xfc, 0x81, 0x66, 0x25, 0x99,
	0xda, 0xfc, 0x5b, 0xff, 0x63, 0x96, 0x0c, 0xe4, 0x36, 0xb9, 0xda, 0xfc, 0x68, 0x77, 0x58, 0xae,
	0x25, 0x51, 0x7f, 0xd6, 0xc9, 0xee, 0xb2, 0x3c, 0x2a, 0xc7, 0x2c, 0x12, 0x1e, 0x76, 0xa0, 0x58,
	0x4a, 0xab, 0x7d, 0x09, 0x88, 0x69, 0x88, 0x3b, 0xf9, 0x30, 0xed, 0xf8, 0x7f, 0x62, 0x62, 0x30,
	0x14, 0x3a, 0x3b, 0xbe, 0x2b, 0xff, 0x1a, 0x5c, 0x13, 0xf8, 0x12, 0xb7, 0xdf, 0xc5, 0x75, 0xd8,
	0x93, 0xc4, 0xee, 0x65, 0xb8, 0x9e, 0x48, 0x81, 0x77, 0x71, 0x4f, 0x16, 0x82, 0x9e, 0x24, 0xf9,
	0x12, 0x57, 0xfb, 0x15, 0x7b, 0xff, 0x50, 0x3b, 0x37, 0xfd, 0x63, 0xdc, 0x75, 0x3b, 0x74, 0x75,
	0x3e, 0x82, 0xf2, 0xa9, 0x49, 0xb0, 0xe1, 0x07, 0x05, 0x81, 0x22, 0xd7, 0x04, 0x83, 0x27, 0x38,
	0xd9, 0x4e, 0x9f, 0xa6, 0xad, 0x22, 0x2e, 0xdf, 0x82, 0xf2, 0xa9, 0x65, 0xb7, 0x0c, 0x6c, 0xfb,
	0x96, 0x6f, 0x05, 0x57, 0x35, 0xe2, 0xb9, 0xdb, 0x43, 0xcb, 0x6e, 0xd5, 0x29, 0xc9, 0x85, 0x3e,
	0x7d, 0x1a, 0xfe, 0xb6, 0x30, 0xd1, 0x7e, 0xad, 0x00, 0xc4, 0x95, 0xa8, 0x0e, 0x53, 0x8c, 0xd3,
	0x05, 0xdf, 0xea, 0xca, 0x93, 0x97, 0x98, 0x7e, 0x8b, 0xff, 0x8f, 0xef, 0x7a, 0x71, 0xf4, 0x3b,
	0x9b, 0xe7, 0x6a, 0x5f, 0x00, 0xc4, 0x94, 0xa8, 0x0a, 0xd7, 0xe3, 0x2f, 0x23, 0x7d, 0x32, 0x7d,
	0x0d, 0x66, 0x13, 0x75, 0x54, 0x7f, 0xaa, 0xa2, 0x9d, 0x81, 0x9a, 0x55, 0xc3, 0x50, 0xaf, 0x8e,
	0xb7, 0x60, 0xb4, 0x4b, 0xda, 0xfc, 0x75, 0xf3, 0x94, 0xb0, 0x3d, 0x0a, 0x59, 0xed, 0x91, 0xb6,
	0xce, 0xe8, 0xb4, 0x7f, 0x51, 0x60, 0x2a, 0x51, 0xda, 0x27, 0x1f, 0x7e, 0x0c, 0x53, 0x84, 0xbd,
	0xde, 0x4d, 0x5e, 0xf5, 0xbd, 0x93, 0x2f, 0x60, 0xeb, 0x88, 0x51, 0x87, 0xc7, 0x03, 0xe1, 0x6f,
	0xed, 0x10, 0x20, 0xae, 0xa1, 0x8a, 0x89, 0xbf, 0x32, 0x8a, 0x99, 0x83, 0x72, 0xa2, 0x6e, 0xbb,
	0xa1, 0x2a, 0x54, 0x57, 0x49, 0x72, 0x82, 0x3d, 0xb5, 0xa0, 0xfd, 0x08, 0x16, 0xc3, 0xc5, 0x92,
	0x32, 0x1b, 0xfc, 0x02, 0x3d, 0x04, 0x86, 0xa2, 0xcd, 0x5a, 0x9c, 0xb8, 0xfb, 0x49, 0xdb, 0x5b,
	0x33, 0xf1, 0xa5, 0x55, 0xc3, 0xac, 0x3a, 0xcb, 0x9e, 0xb8, 0x54, 0x74, 0xb8, 0x3a, 0xbe, 0x22,
	0xd1, 0x72, 0xf6, 0xc4, 0xd5, 0x3e, 0x86, 0x1b, 0x89, 0x34, 0x42, 0x94, 0x9f, 0x9b, 0x7f, 0xac,
	0xa5, 0x32, 0x17, 0x09, 0xe7, 0xbf, 0x50, 0xd8, 0x7d, 0x66, 0xba, 0x26, 0x74, 0xd7, 0x08, 0x46,
	0x5d, 0xb3, 0x8d, 0x03, 0x7f, 0xcd, 0x7e, 0xe7, 0xa4, 0x1e, 0x2b, 0x00, 0x36, 0xc6, 0x2d, 0xa3,
	0xe9, 0xf4, 0x6c, 0x3f, 0xd8, 0xae, 0x94, 0x68, 0x49, 0x8d, 0x16, 0xd0, 0xed, 0x4c, 0xa8, 0x9a,
	0xc4, 0x71, 0x46, 0x58, 0xd4, 0x68, 0x69, 0xaf, 0x18, 0xbc, 0x48, 0xd6, 0x8b, 0xe0, 0x9a, 0x22,
	0xa9, 0xdd, 0xfc, 0x6b, 0x8a, 0xd4, 0xf0, 0xca, 0x49, 0xf5, 0x12, 0x96, 0xd7, 0x38, 0xbe, 0xd9,
	0x09, 0x3b, 0xce, 0x3e, 0xb4, 0x5d, 0x76, 0x6b, 0x4c, 0x97, 0x7c, 0xb2, 0x2d, 0xdb, 0x97, 0x7f,
	0x1d, 0xc6, 0xf9, 0x9a, 0xcf, 0x39, 0xec, 0x4f, 0x38, 0x9d, 0x80, 0x50, 0x3b, 0x65, 0xca, 0x94,
	0x70, 0x23, 0x2e, 0x7a, 0x9c, 0x33, 0x8a, 0x81, 0x1e, 0x31, 0x3d, 0x0e, 0xed, 0x67, 0xb0, 0xbc,
	0x83, 0x7d, 0x1d, 0x9b, 0xad, 0x27, 0xd8, 0xf4, 0xfc, 0x7a, 0x00, 0x30, 0xd5, 0xf1, 0x8b, 0x1e,
	0x26, 0xfe, 0x65, 0x4e, 0xe7, 0x73, 0xce, 0x9a, 0xd3, 0x87, 0xbc, 0xa3, 0x99, 0x43, 0x5e, 0xed,
	0x08, 0x6e, 0xc8, 0xe5, 0x13, 0xd7, 0xb1, 0x09, 0x46, 0xf7, 0xe1, 0xba, 0x45, 0x0c, 0x0f, 0x9b,
	0x2d, 0xe3, 0x9c, 0x12, 0x18, 0x21, 0x04, 0x96, 0xf5, 0x69, 0x52, 0xbf, 0x66, 0x11, 0xa1, 0xb1,
	0xb6, 0x03, 0xd5, 0x20, 0x0d, 0x88, 0xea, 0x6a, 0x0c, 0xa5, 0x77, 0xc9, 0x31, 0x69, 0xcf, 0x98,
	0x76, 0x44, 0x46, 0x41, 0xe7, 0x56, 0x00, 0x9a, 0x4e, 0xb7, 0xeb, 0xd8, 0xec, 0x5d, 0x24, 0x67,
	0x58, 0xe2, 0x25, 0x35, 0xdb, 0x8f, 0x22, 0x6a, 0xd3, 0x0e, 0xed, 0x9b, 0x89, 0xa9, 0xd9, 0xbe,
	0xf6, 0x7b, 0x05, 0x16, 0xb7, 0x5b, 0xad, 0x88, 0xeb, 0x31, 0x7e, 0xdd, 0xa7, 0x7f, 0xb1, 0x6a,
	0x0b, 0x49, 0xd5, 0x86, 0xcf, 0x12, 0x8a, 0x89, 0x67, 0x09, 0xb9, 0x39, 0xfa, 0xc7, 0x50, 0x69,
	0xe1, 0x56, 0xaf, 0xc9, 0x10, 0xae, 0x5c, 0x95, 0x4d, 0xdb, 0xe7, 0x1e, 0x99, 0xe7, 0xec, 0x0b,
	0x51, 0x7d, 0x38, 0x50, 0xe6, 0x67, 0xd3, 0x13, 0x38, 0x9e, 0x9d, 0xc0, 0x53, 0xa8, 0x88, 0x03,
	0x09, 0xf4, 0x73, 0x1b, 0x66, 0x6c, 0xfc, 0xca, 0x10, 0x74, 0x34, 0x6d, 0xe3, 0x57, 0xb5, 0x48,
	0x4d, 0xeb, 0x40, 0xbf, 0x8d, 0x8c, 0xaa, 0xc0, 0xc6, 0xaf, 0x82, 0xc7, 0xa5, 0xda, 0x67, 0xb0,
	0xca, 0x9d, 0x59, 0x38, 0xc3, 0x0f, 0x4d, 0x5b, 0x67, 0xc3, 0x0a, 0x75, 0xd6, 0x27, 0x79, 0xb9,
	0x09, 0x6b, 0xb9, 0x8d, 0x79, 0x3f, 0xb5, 0x15, 0x36, 0xcd, 0x79, 0xcc, 0xb5, 0x4f, 0x99, 0x8d,
	0xe6, 0x36, 0xef, 0x27, 0x3c, 0x74, 0xb5, 0x49, 0x1b, 0x67, 0xc7, 0x64, 0x41, 0xbf, 0x17, 0x61,
	0x82, 0xcf, 0x6c, 0x78, 0xad, 0x39, 0xce, 0xa6, 0x96, 0x68, 0x3f, 0x84, 0x72, 0xaa, 0x41, 0xce,
	0x35, 0x11, 0xf5, 0x85, 0x16, 0x89, 0x17, 0x05, 0x47, 0x42, 0x81, 0x45, 0xc2, 0xde, 0xca, 0x8c,
	0x44, 0x6b, 0x07, 0x7e, 0x5c, 0xec, 0x55, 0x30, 0xa2, 0xc7, 0xa0, 0x26, 0x96, 0x5c, 0xf2, 0x32,
	0xf7, 0x46, 0xc6, 0xbf, 0xa4, 0xdb, 0xcf, 0x78, 0xc9, 0x4f, 0xa2, 0xed, 0x05, 0x07, 0xbb, 0xd4,
	0x42, 0x9c, 0x0e, 0x3f, 0xd9, 0x8d, 0x47, 0x2e, 0x3f, 0xe5, 0x5e, 0x82, 0xc9, 0x5e, 0xfa, 0xa8,
	0x72, 0xa2, 0x17, 0xc4, 0x9f, 0x2a, 0x54, 0x44, 0x76, 0xc1, 0x1c, 0xee, 0xc5, 0x67, 0xc8, 0x6f,
	0x53, 0x54, 0x8a, 0x5d, 0x20, 0xea, 0xc7, 0x6c, 0x3f, 0xc0, 0x8a, 0x39, 0xf5, 0x40, 0x41, 0x39,
	0x3b, 0xbc, 0x9c, 0x03, 0xc1, 0x73, 0xb6, 0x8d, 0x48, 0xf1, 0x0f, 0xe6, 0x05, 0x25, 0x76, 0x2f,
	0xe5, 0x60, 0xf7, 0xb8, 0x04, 0x93, 0xe7, 0x26, 0x31, 0xba, 0x8e, 0x17, 0x4e, 0xff, 0xc4, 0xb9,
	0x49, 0xf6, 0x1c, 0x8f, 0x21, 0xe4, 0x6d, 0xfc, 0xda, 0x37, 0x02, 0xd9, 0xdc, 0x04, 0x80, 0x16,
	0xd5, 0xf8, 0x0e, 0x33, 0x54, 0x5a, 0x28, 0xee, 0x0d, 0x95, 0xf6, 0x6b, 0x25, 0xd0, 0x5a, 0x8a,
	0x5f, 0xd0, 0xf7, 0xa7, 0x30, 0x8e, 0x5f, 0x5b, 0xc4, 0x0f, 0x2d, 0xe9, 0x7e, 0x0e, 0x84, 0x2d,
	0xdb, 0x70, 0xab, 0xce, 0x5a, 0x71, 0x04, 0x5b, 0xc0, 0xa2, 0xfa, 0x29, 0x4c, 0x25, 0x8a, 0x25,
	0xc8, 0xb5, 0xf9, 0x24, 0x72, 0x6d, 0x32, 0x89, 0x4f, 0x33, 0x23, 0x2c, 0x77, 0xfd, 0x75, 0xb3,
	0xd3, 0x23, 0xd6, 0x4b, 0x9c, 0x38, 0x94, 0xce, 0xf1, 0xc0, 0x97, 0x9b, 0xc0, 0x5f, 0x28, 0x11,
	0x58, 0x5b, 0x22, 0x23, 0xd0, 0xc7, 0x25, 0x8e, 0xa5, 0xdf, 0x60, 0x8a, 0x37, 0x0f, 0x61, 0x2a,
	0x71, 0x6f, 0x81, 0x16, 0x60, 0x2e, 0x75, 0x8d, 0xf1, 0xd2, 0xc2, 0xaf, 0xf8, 0x4e, 0x22, 0x51,
	0x7c, 0x68, 0x12, 0xa2, 0x2a, 0x02, 0xed, 0x4f, 0x70, 0xd3, 0x57, 0x0b, 0x9b, 0x06, 0x40, 0x0c,
	0x66, 0x63, 0x44, 0xd1, 0x57, 0x00, 0x45, 0xe3, 0x0f, 0xc5, 0xe3, 0x62, 0xba, 0xa9, 0x55, 0x15,
	0x9a, 0x95, 0x27, 0x48, 0xb1, 0xaf, 0x16, 0x98, 0xdc, 0x98, 0xcc, 0x73, 0x7a, 0xae, 0x5a, 0xdc,
	0xfc, 0x2e, 0x4c, 0x25, 0xae, 0x42, 0x63, 0x1a, 0xf6, 0xb9, 0xef, 0xd8, 0x58, 0x1d, 0x89, 0xc5,
	0xb2, 0xc2, 0xc3, 0xde, 0x69, 0xc7, 0x6a, 0xaa, 0x0a, 0xba, 0x0e, 0x28, 0x59, 0xec, 0x59, 0x2f,
	0x19, 0x4e, 0x67, 0xf3, 0xb7, 0x0a, 0x65, 0x92, 0xc2, 0x20, 0xa3, 0x45, 0xb8, 0x96, 0x29, 0x62,
	0xbb, 0x82, 0x11, 0xb4, 0x04, 0x0b, 0x99, 0x8a, 0x47, 0x98, 0x6f, 0xae, 0x24, 0x6d, 0xd8, 0xd8,
	0x0a, 0xe8, 0x06, 0x54, 0x32, 0x15, 0x7b, 0xbd, 0x8e, 0x6f, 0xb1, 0x66, 0x45, 0xb4, 0x0a, 0xd5,
	0x4c, 0xed, 0x76, 0x63, 0xa7, 0x76, 0xec, 0x59, 0xed, 0x36, 0xf6, 0xd4, 0x51, 0xde, 0xed, 0x54,
	0x3d, 0x55, 0xcf, 0xd8, 0xe6, 0x33, 0x98, 0x4e, 0xbe, 0x73, 0xe6, 0xe2, 0xe3, 0xef, 0x13, 0xfb,
	0xb9, 0xed, 0xbc, 0xb2, 0xd5, 0x11, 0x54, 0x81, 0xf9, 0x64, 0x45, 0x88, 0xac, 0xe7, 0xaf, 0xf3,
	0x53, 0x4d, 0xd8, 0xc6, 0x67, 0xf3, 0x57, 0x0a, 0x83, 0xa3, 0xa4, 0x71, 0x3e, 0x68, 0x0d, 0x96,
	0x77, 0xea, 0xc7, 0x46, 0x6d, 0xfb, 0xb8, 0xbe, 0x73, 0xa0, 0x7f, 0xdf, 0x38, 0x3a, 0x38, 0xd1,
	0x6b, 0x75, 0xe3, 0x64, 0xff, 0xe9, 0xfe, 0xc1, 0xb3, 0x7d, 0x75, 0x84, 0x8e, 0x52, 0x46, 0xf0,
	0x70, 0xbb, 0xf6, 0x54, 0x55, 0xf2, 0x9a, 0x3f, 0xf9, 0xa6, 0xf1, 0xe4, 0x60, 0xaf, 0xae, 0x16,
	0xd0, 0x4d, 0x58, 0xc9, 0x21, 0xa8, 0xe9, 0xf5, 0xed, 0xe3, 0xba, 0x5a, 0xdc, 0xfc, 0x53, 0x98,
	0x97, 0x9d, 0xd2, 0xa1, 0x5b, 0xb0, 0x26, 0x2b, 0x4f, 0x6f, 0xfb, 0x56, 0xa1, 0x2a, 0x23, 0x8a,
	0xac, 0x63, 0x0d, 0x96, 0xa5, 0xf5, 0x91, 0x99, 0xfc, 0x19, 0x2c, 0x48, 0x2f, 0x1b, 0xd0, 0x6d,
	0x58, 0x97, 0x56, 0xa4, 0xe5, 0xdf, 0x84, 0x15, 0x29, 0x55, 0x62, 0x3a, 0x56, 0x60, 0x49, 0xce,
	0x88, 0xcf, 0xcb, 0xd3, 0xd8, 0xd6, 0xc3, 0xe7, 0x55, 0x55, 0xb8, 0x9e, 0x29, 0x7a, 0x84, 0xcf,
	0xcc, 0x5e, 0xc7, 0xe7, 0xf3, 0x91, 0xa9, 0x3b, 0xb0, 0x3b, 0x17, 0xec, 0x43, 0x55, 0x36, 0x7f,
	0xa9, 0xc0, 0x62, 0xce, 0x41, 0x1c, 0x7a, 0x17, 0xb4, 0x9c, 0xaa, 0xf4, 0x90, 0x34, 0x58, 0xcd,
	0xa1, 0x0b, 0xe0, 0x05, 0xaa, 0x22, 0x51, 0x4e, 0xcc, 0x2b, 0xb8, 0x82, 0x56, 0x0b, 0x9b, 0xfb,
	0xd1, 0xaa, 0x6e, 0x3a, 0x2c, 0xad, 0x5c, 0xda, 0x6e, 0x18, 0xfa, 0xc1, 0x6e, 0xdd, 0x38, 0xaa,
	0x1d, 0x1c, 0x52, 0x2b, 0x3b, 0x3a, 0xac, 0xd7, 0x1a, 0x8f, 0x1b, 0xf5, 0x47, 0xea, 0x08, 0x5a,
	0x87, 0x1b, 0x99, 0xea, 0xa3, 0xba, 0x6e, 0xd4, 0xbf, 0x57, 0xdb, 0x3d, 0x39, 0x6a, 0x7c, 0x59,
	0x57, 0x95, 0xcd, 0x4e, 0xf0, 0x47, 0x0b, 0x52, 0x2f, 0xc2, 0x96, 0x99, 0x21, 0x1d, 0xe8, 0x46,
	0x63, 0xff, 0xf1, 0x81, 0x71, 0xfc, 0xfd, 0xc3, 0xba, 0xb1, 0xbd, 0x7f, 0xb0, 0xff, 0xfd, 0xbd,
	0x83, 0x93, 0x23, 0xae, 0x2f, 0x91, 0xe0, 0xf0, 0xe4, 0xe1, 0x6e, 0xa3, 0xa6, 0x2a, 0x54, 0xd3,
	0x62, 0xed, 0x93, 0xc6, 0xa3, 0xba, 0x5a, 0xd8, 0xfc, 0x59, 0x08, 0x2f, 0x49, 0xa0, 0x3e, 0xe8,
	0xaa, 0x3b, 0xaa, 0x6f, 0xeb, 0xb5, 0x27, 0x41, 0x47, 0xeb, 0xdf, 0x33, 0xf6, 0x0f, 0xf6, 0xeb,
	0x7c, 0x3d, 0x66, 0x6b, 0xf6, 0xb6, 0x77, 0xeb, 0x5c, 0x48, 0xb6, 0xe6, 0x71, 0x9d, 0xd5, 0x15,
	0xa8, 0xe3, 0xc9, 0xd6, 0x1d, 0x1c, 0x3f, 0xa9, 0xeb, 0x6a, 0x71, 0x73, 0x0f, 0x20, 0x06, 0x4d,
	0xb0, 0xbf, 0xb8, 0xc6, 0x09, 0x59, 0x1f, 0x03, 0xa1, 0x0b, 0x30, 0x97, 0x2c, 0xdd, 0x3b, 0xd8,
	0xdf, 0x39, 0x50, 0x15, 0xea, 0x8a, 0x93, 0xc5, 0xf5, 0x23, 0xb5, 0x40, 0x1d, 0x4b, 0x12, 0x32,
	0x42, 0x1d, 0xd0, 0xe3, 0xc6, 0xee, 0x31, 0x53, 0xf1, 0xe1, 0xc1, 0x51, 0xc4, 0x52, 0x28, 0x3f,
	0xd6, 0x4f, 0xea, 0xdc, 0x0f, 0xa6, 0xcb, 0x1f, 0x6f, 0xef, 0x1e, 0x51, 0x3d, 0xfd, 0x42, 0x81,
	0x85, 0x47, 0xd2, 0x7d, 0xc4, 0x06, 0xdc, 0x7e, 0x54, 0x7f, 0x74, 0x52, 0x3b, 0x6e, 0x1c, 0xec,
	0x1b, 0x4f, 0xea, 0xdb, 0xfa, 0xb1, 0x51, 0xdb, 0x3f, 0xe6, 0x7d, 0x4a, 0xcf, 0xfd, 0x2d, 0x58,
	0xcb, 0xa5, 0xac, 0x1d, 0xec, 0xed, 0x1d, 0xec, 0xab, 0x0a, 0x5d, 0x6b, 0xb9, 0x44, 0x54, 0x7b,
	0x6a, 0xe1, 0xde, 0x7f, 0xdd, 0x87, 0xe9, 0x6d, 0xab, 0xdd, 0x3c, 0x0a, 0x82, 0x2a, 0xfa, 0x2e,
	0x4c, 0x27, 0xcf, 0x46, 0xd1, 0x6a, 0xff, 0x83, 0xd3, 0xea, 0x5a, 0xdf, 0x7a, 0xe2, 0x6a, 0x23,
	0x94, 0x65, 0xf2, 0x90, 0x53, 0x60, 0x99, 0x39, 0x01, 0x15, 0x58, 0x66, 0xdf, 0x07, 0x72, 0x96,
	0x49, 0xec, 0xa7, 0xc0, 0x32, 0xf3, 0x26, 0x50, 0x60, 0x29, 0x3c, 0x08, 0x1c, 0x41, 0x4f, 0xa0,
	0x14, 0x61, 0x36, 0xd1, 0xb2, 0x88, 0xf7, 0x8c, 0x99, 0xdd, 0xc8, 0xaf, 0x64, 0x9c, 0x8e, 0x13,
	0x8f, 0x07, 0xd9, 0xa5, 0xf3, 0x5a, 0x5e, 0x83, 0x20, 0xa1, 0xaa, 0xae, 0xf7, 0x27, 0x60, 0x5c,
	0xff, 0x3f, 0x8b, 0x46, 0xe9, 0xc7, 0x6d, 0xe8, 0x96, 0xd8, 0x50, 0x78, 0xf9, 0x57, 0xbd, 0x3d,
	0x98, 0x88, 0x49, 0xe8, 0xb0, 0x9c, 0x5d, 0x7c, 0x71, 0x86, 0xee, 0x88, 0x0c, 0xa4, 0x8f, 0xe1,
	0xaa, 0x1b, 0xc3, 0x11, 0x32, 0x69, 0x3f, 0x65, 0x07, 0x10, 0x39, 0xa0, 0x04, 0xf4, 0xbe, 0xc8,
	0x29, 0x1f, 0x25, 0x51, 0xfd, 0xe0, 0x12, 0xd4, 0xa1, 0xfd, 0x24, 0xe1, 0x04, 0x82, 0xfd, 0x64,
	0x80, 0x0c, 0x82, 0xfd, 0x64, 0xb1, 0x08, 0xda, 0x08, 0xfa, 0x01, 0xcc, 0x66, 0xae, 0x0c, 0xd0,
	0x4d, 0x59, 0xfa, 0x9e, 0xba, 0xaa, 0xa8, 0x6a, 0x83, 0x48, 0x52, 0xbc, 0x13, 0x08, 0xa8, 0x9b,
	0xf9, 0xf7, 0x58, 0x7d, 0x79, 0x67, 0xee, 0x11, 0x46, 0xd0, 0xcf, 0x15, 0xb6, 0x75, 0xcf, 0x7d,
	0x35, 0x89, 0xb6, 0x06, 0x99, 0x4f, 0xfa, 0x95, 0x67, 0xf5, 0xc3, 0x4b, 0xd1, 0x27, 0x6c, 0x3b,
	0xfd, 0xea, 0x4f, 0x66, 0xdb, 0xc2, 0x53, 0x42, 0x99, 0x6d, 0x4b, 0x1e, 0x0f, 0x32, 0x09, 0x47,
	0x03, 0x25, 0x1c, 0x0d, 0x23, 0xe1, 0x28, 0x47, 0xc2, 0x8f, 0x00, 0x1d, 0x7b, 0x17, 0x99, 0x97,
	0x7a, 0xc2, 0x34, 0x89, 0xcf, 0x11, 0x85, 0x69, 0x92, 0x3c, 0xf6, 0xe3, 0x26, 0xf0, 0x55, 0xf2,
	0xce, 0xbc, 0x06, 0x13, 0x78, 0x8b, 0x2f, 0x00, 0x05, 0xde, 0x92, 0x17, 0x7b, 0x9c, 0x77, 0xe6,
	0x65, 0x9d, 0xc0, 0x5b, 0x7c, 0x9f, 0x27, 0xf0, 0x96, 0x3d, 0xce, 0x1b, 0x41, 0x67, 0x70, 0x4d,
	0xf2, 0xb6, 0x0e, 0x65, 0xaf, 0x4d, 0xe4, 0x4f, 0xf8, 0xaa, 0xef, 0x0e, 0x43, 0xc6, 0xe4, 0xf4,
	0xe2, 0x8b, 0x82, 0xec, 0x6b, 0x39, 0xb4, 0xd9, 0x5f, 0x0b, 0xc9, 0xd7, 0x79, 0xd5, 0xbb, 0x43,
	0xd3, 0x86, 0x4e, 0x2a, 0xf9, 0x5c, 0x4d, 0x70, 0x52, 0x99, 0x17, 0x73, 0xd5, 0xb5, 0xbe, 0xf5,
	0x81, 0x91, 0xaa, 0xd9, 0xb7, 0x54, 0x48, 0xcb, 0x5b, 0xaf, 0x09, 0xd6, 0xb7, 0x06, 0xd2, 0x30,
	0xf6, 0x96, 0xf8, 0xba, 0x8c, 0x2d, 0xb4, 0x77, 0x07, 0x34, 0x0f, 0xd7, 0xda, 0x9d, 0xa1, 0xe8,
	0xc2, 0x05, 0x2d, 0x3c, 0xe8, 0x13, 0x16, 0xb4, 0xec, 0x61, 0xa1, 0xb0, 0xa0, 0xa5, 0xef, 0x02,
	0x79, 0x18, 0x4f, 0x61, 0xbd, 0x85, 0x30, 0x9e, 0xc5, 0x9e, 0x0b, 0x61, 0x5c, 0x80, 0x8a, 0x6b,
	0x23, 0xe8, 0x19, 0xcc, 0xa4, 0x01, 0xee, 0x28, 0xdb, 0x4a, 0x00, 0xd0, 0x57, 0x6f, 0x0e, 0xa0,
	0x08, 0x19, 0xa7, 0x31, 0xdf, 0x02, 0x63, 0x01, 0x6f, 0x2e, 0x30, 0x96, 0x80, 0xc6, 0xd9, 0xa4,
	0xca, 0xde, 0xef, 0x08, 0x93, 0x9a, 0xf3, 0x80, 0x48, 0x98, 0xd4, 0xdc, 0xc7, 0x40, 0x4c, 0x94,
	0xec, 0x49, 0x8f, 0x20, 0x2a, 0xe7, 0xcd, 0x90, 0x20, 0x2a, 0xf7, 0x7d, 0xd0, 0x08, 0x72, 0x02,
	0x2c, 0xae, 0xf0, 0x4e, 0x07, 0x6d, 0xe4, 0xda, 0x47, 0x56, 0xdc, 0x7b, 0x43, 0x52, 0x86, 0x63,
	0x93, 0xbd, 0xc3, 0x11, 0xc6, 0x96, 0xf3, 0xb2, 0x47, 0x18, 0x5b, 0xee, 0xa3, 0x1e, 0x96, 0xca,
	0x46, 0xaf, 0x75, 0x84, 0x54, 0x36, 0xf9, 0xe0, 0x47, 0x48, 0x65, 0x53, 0x8f, 0x7c, 0xa2, 0x54,
	0x36, 0xf1, 0xe7, 0x23, 0x25, 0x3e, 0x26, 0xf5, 0xaa, 0x47, 0x96, 0xca, 0xa6, 0x1f, 0xf4, 0x70,
	0x55, 0xc8, 0x9e, 0xdd, 0x08, 0xaa, 0xc8, 0x79, 0xc8, 0x23, 0xa8, 0x22, 0xef, 0x0d, 0x0f, 0xcf,
	0x69, 0xa5, 0xef, 0x6a, 0x84, 0x9c, 0x36, 0xef, 0x2d, 0x8f, 0x90, 0xd3, 0xe6, 0x3e, 0xd3, 0xe1,
	0x6b, 0x30, 0x8d, 0xcd, 0x47, 0xf2, 0x67, 0x11, 0x09, 0xe8, 0xb7, 0xb0, 0x06, 0x45, 0x70, 0x3f,
	0x8f, 0x74, 0x12, 0x50, 0xb9, 0x10, 0xe9, 0xe4, 0x88, 0x7a, 0x21, 0xd2, 0xe5, 0xe1, 0xd3, 0x63,
	0x39, 0x69, 0x94, 0xb9, 0x5c, 0x8e, 0x80, 0x62, 0x97, 0xcb, 0x91, 0x00, 0xd6, 0x47, 0x90, 0x17,
	0xdf, 0xec, 0x67, 0x50, 0xe3, 0xe8, 0xbd, 0x3e, 0xbb, 0xbf, 0x34, 0xb4, 0xbd, 0xba, 0x39, 0x2c,
	0x29, 0x93, 0xf9, 0x14, 0x20, 0x06, 0x98, 0xa3, 0xac, 0xe5, 0xa7, 0x50, 0xea, 0xd5, 0x95, 0x3e,
	0xb5, 0xd1, 0x9e, 0x36, 0x81, 0x37, 0x17, 0xf7, 0xb4, 0x69, 0xe0, 0xba, 0xb8, 0xa7, 0xcd, 0x82,
	0xd5, 0x47, 0x50, 0x13, 0x90, 0x88, 0x30, 0x47, 0x03, 0x36, 0x6f, 0x1c, 0xd1, 0x5e, 0x7d, 0x67,
	0x08, 0x2a, 0x26, 0xe4, 0x27, 0xb0, 0x20, 0x05, 0xc4, 0x0a, 0xeb, 0x21, 0x0f, 0xb3, 0x2c, 0xac,
	0x87, 0x7c, 0x7c, 0xed, 0x4f, 0x60, 0x41, 0x8a, 0x5c, 0x43, 0x77, 0x86, 0x44, 0xcf, 0x0a, 0xb2,
	0x72, 0x81, 0x70, 0xc8, 0x64, 0xca, 0xcb, 0x0a, 0x92, 0x28, 0x4f, 0x22, 0xe5, 0x9d, 0x21, 0xa8,
	0xf8, 0x70, 0xa4, 0x98, 0x46, 0x61, 0x38, 0x79, 0xe0, 0x52, 0x61, 0x38, 0xf9, 0x10, 0xc9, 0x8b,
	0xe8, 0x76, 0x5e, 0x02, 0xd2, 0x93, 0x6d, 0x8e, 0xf3, 0x21, 0x80, 0xb2, 0xcd, 0x71, 0x3f, 0xf4,
	0xdf, 0x45, 0xf0, 0x80, 0x55, 0x0a, 0xa0, 0x15, 0x44, 0xf7, 0x45, 0xe9, 0x0a, 0xa2, 0xfb, 0x23,
	0x73, 0x23, 0xd1, 0x72, 0xab, 0x79, 0xff, 0x32, 0xc8, 0x4d, 0xb9, 0xe8, 0x7c, 0xfb, 0xb1, 0x60,
	0x5e, 0x06, 0x43, 0x12, 0x42, 0x52, 0x0e, 0x14, 0xaa, 0x7a, 0x67, 0x28, 0xba, 0x38, 0x9f, 0x6a,
	0x0d, 0x16, 0x95, 0x03, 0x7d, 0xaa, 0xde, 0x19, 0x8a, 0x8e, 0x89, 0x0a, 0x9f, 0x41, 0xcb, 0xc0,
	0x48, 0xe8, 0x6e, 0xfe, 0xe4, 0x88, 0x42, 0xdf, 0x1f, 0x9e, 0x38, 0x71, 0x96, 0x24, 0xc2, 0x8b,
	0x64, 0x67, 0x49, 0x52, 0x28, 0x94, 0xec, 0x2c, 0x49, 0x8e, 0x56, 0x8a, 0xf6, 0x1d, 0x02, 0x0a,
	0x48, 0xb6, 0xef, 0x90, 0x01, 0x8f, 0x64, 0xfb, 0x0e, 0x29, 0xa4, 0x48, 0x1b, 0x41, 0x2f, 0x98,
	0x28, 0x01, 0x4f, 0x23, 0xec, 0x03, 0xfb, 0x20, 0x86, 0x84, 0x7d, 0x60, 0x3f, 0x74, 0x8f, 0x36,
	0x82, 0xec, 0xe8, 0xaf, 0x85, 0x24, 0x11, 0x36, 0x42, 0xa0, 0xcc, 0x87, 0xf3, 0x54, 0x37, 0x87,
	0x21, 0x8d, 0xe4, 0x61, 0x50, 0xb3, 0x70, 0x15, 0x31, 0x35, 0x93, 0x03, 0x73, 0xc4, 0xd4, 0x2c,
	0x07, 0xf7, 0xa2, 0x8d, 0xa0, 0x3f, 0x09, 0x73, 0x00, 0x01, 0x35, 0x82, 0x3e, 0x90, 0x9a, 0x78,
	0x1e, 0xf8, 0xa4, 0xba, 0x35, 0x2c, 0x79, 0x24, 0x9b, 0xcf, 0xa2, 0x28, 0x58, 0xa2, 0xa8, 0x5c,
	0xa9, 0x77, 0x87, 0xa2, 0x8d, 0x44, 0xfa, 0xf1, 0x1f, 0xaf, 0x49, 0xa3, 0x56, 0xee, 0xe6, 0x5d,
	0xf2, 0x4b, 0xc0, 0x30, 0xf2, 0x75, 0x98, 0x87, 0x51, 0x61, 0x49, 0x85, 0x9a, 0x85, 0x83, 0x20,
	0x69, 0x9a, 0x26, 0xc2, 0x4f, 0x84, 0xb9, 0xcc, 0xc3, 0x95, 0x44, 0x42, 0x12, 0x40, 0x10, 0x94,
	0x97, 0x0b, 0x0e, 0x25, 0x44, 0x82, 0x28, 0x41, 0x3f, 0x82, 0x99, 0x34, 0xe2, 0x43, 0x16, 0xdd,
	0x45, 0xc0, 0x89, 0x2c, 0xba, 0xcb, 0x60, 0x23, 0xe1, 0x18, 0x12, 0xe8, 0x0a, 0xf9, 0x18, 0x44,
	0x1c, 0x88, 0x7c, 0x0c, 0x32, 0x7c, 0xc7, 0x4f, 0xa3, 0x3f, 0x65, 0x23, 0x60, 0x1e, 0xf2, 0x8e,
	0x59, 0xf3, 0x00, 0x18, 0x79, 0xc7, 0xac, 0xb9, 0x60, 0x8a, 0x87, 0x1f, 0xfd, 0xe0, 0x1b, 0x6d,
	0xa7, 0x63, 0xda, 0xed, 0xad, 0x6f, 0xde, 0xf3, 0xfd, 0xad, 0xa6, 0xd3, 0xfd, 0x90, 0xfd, 0x9b,
	0x57, 0x4d, 0xa7, 0xf3, 0x21, 0xc1, 0xde, 0x4b, 0xab, 0x89, 0x89, 0xe4, 0x1f, 0xc6, 0x3a, 0x1d,
	0x67, 0x54, 0xf7, 0xff, 0x37, 0x00, 0x00, 0xff, 0xff, 0xc8, 0x32, 0x3b, 0xee, 0x44, 0x6b, 0x00,
	0x00,
}
