// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/userpresent-go/userpresent-go.proto

package userpresent_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	grpc "google.golang.org/grpc"
)

// MockUserPresentGOClient is a mock of UserPresentGOClient interface.
type MockUserPresentGOClient struct {
	ctrl     *gomock.Controller
	recorder *MockUserPresentGOClientMockRecorder
}

// MockUserPresentGOClientMockRecorder is the mock recorder for MockUserPresentGOClient.
type MockUserPresentGOClientMockRecorder struct {
	mock *MockUserPresentGOClient
}

// NewMockUserPresentGOClient creates a new mock instance.
func NewMockUserPresentGOClient(ctrl *gomock.Controller) *MockUserPresentGOClient {
	mock := &MockUserPresentGOClient{ctrl: ctrl}
	mock.recorder = &MockUserPresentGOClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserPresentGOClient) EXPECT() *MockUserPresentGOClientMockRecorder {
	return m.recorder
}

// AddChanceItemSource mocks base method.
func (m *MockUserPresentGOClient) AddChanceItemSource(ctx context.Context, in *AddChanceItemSourceReq, opts ...grpc.CallOption) (*AddChanceItemSourceResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddChanceItemSource", varargs...)
	ret0, _ := ret[0].(*AddChanceItemSourceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddChanceItemSource indicates an expected call of AddChanceItemSource.
func (mr *MockUserPresentGOClientMockRecorder) AddChanceItemSource(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChanceItemSource", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddChanceItemSource), varargs...)
}

// AddDynamicEffectTemplate mocks base method.
func (m *MockUserPresentGOClient) AddDynamicEffectTemplate(ctx context.Context, in *AddDynamicEffectTemplateReq, opts ...grpc.CallOption) (*AddDynamicEffectTemplateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddDynamicEffectTemplate", varargs...)
	ret0, _ := ret[0].(*AddDynamicEffectTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddDynamicEffectTemplate indicates an expected call of AddDynamicEffectTemplate.
func (mr *MockUserPresentGOClientMockRecorder) AddDynamicEffectTemplate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddDynamicEffectTemplate", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddDynamicEffectTemplate), varargs...)
}

// AddNamingPresentInfo mocks base method.
func (m *MockUserPresentGOClient) AddNamingPresentInfo(ctx context.Context, in *AddNamingPresentInfoReq, opts ...grpc.CallOption) (*AddNamingPresentInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddNamingPresentInfo", varargs...)
	ret0, _ := ret[0].(*AddNamingPresentInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddNamingPresentInfo indicates an expected call of AddNamingPresentInfo.
func (mr *MockUserPresentGOClientMockRecorder) AddNamingPresentInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNamingPresentInfo", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddNamingPresentInfo), varargs...)
}

// AddPresentActivityConfig mocks base method.
func (m *MockUserPresentGOClient) AddPresentActivityConfig(ctx context.Context, in *AddPresentActivityConfigReq, opts ...grpc.CallOption) (*AddPresentActivityConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPresentActivityConfig", varargs...)
	ret0, _ := ret[0].(*AddPresentActivityConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentActivityConfig indicates an expected call of AddPresentActivityConfig.
func (mr *MockUserPresentGOClientMockRecorder) AddPresentActivityConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentActivityConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddPresentActivityConfig), varargs...)
}

// AddPresentConfig mocks base method.
func (m *MockUserPresentGOClient) AddPresentConfig(ctx context.Context, in *AddPresentConfigReq, opts ...grpc.CallOption) (*AddPresentConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPresentConfig", varargs...)
	ret0, _ := ret[0].(*AddPresentConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentConfig indicates an expected call of AddPresentConfig.
func (mr *MockUserPresentGOClientMockRecorder) AddPresentConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddPresentConfig), varargs...)
}

// AddPresentConfigBackend mocks base method.
func (m *MockUserPresentGOClient) AddPresentConfigBackend(ctx context.Context, in *AddPresentConfigBackendReq, opts ...grpc.CallOption) (*AddPresentConfigBackendResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPresentConfigBackend", varargs...)
	ret0, _ := ret[0].(*AddPresentConfigBackendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentConfigBackend indicates an expected call of AddPresentConfigBackend.
func (mr *MockUserPresentGOClientMockRecorder) AddPresentConfigBackend(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentConfigBackend", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddPresentConfigBackend), varargs...)
}

// AddPresentEffectTemplateConfig mocks base method.
func (m *MockUserPresentGOClient) AddPresentEffectTemplateConfig(ctx context.Context, in *AddPresentEffectTemplateConfigReq, opts ...grpc.CallOption) (*AddPresentEffectTemplateConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPresentEffectTemplateConfig", varargs...)
	ret0, _ := ret[0].(*AddPresentEffectTemplateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentEffectTemplateConfig indicates an expected call of AddPresentEffectTemplateConfig.
func (mr *MockUserPresentGOClientMockRecorder) AddPresentEffectTemplateConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentEffectTemplateConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddPresentEffectTemplateConfig), varargs...)
}

// AddPresentFlowConfig mocks base method.
func (m *MockUserPresentGOClient) AddPresentFlowConfig(ctx context.Context, in *AddPresentFlowConfigReq, opts ...grpc.CallOption) (*AddPresentFlowConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPresentFlowConfig", varargs...)
	ret0, _ := ret[0].(*AddPresentFlowConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentFlowConfig indicates an expected call of AddPresentFlowConfig.
func (mr *MockUserPresentGOClientMockRecorder) AddPresentFlowConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentFlowConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddPresentFlowConfig), varargs...)
}

// AddPresentMarkConfig mocks base method.
func (m *MockUserPresentGOClient) AddPresentMarkConfig(ctx context.Context, in *AddPresentMarkConfigReq, opts ...grpc.CallOption) (*AddPresentMarkConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPresentMarkConfig", varargs...)
	ret0, _ := ret[0].(*AddPresentMarkConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentMarkConfig indicates an expected call of AddPresentMarkConfig.
func (mr *MockUserPresentGOClientMockRecorder) AddPresentMarkConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentMarkConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddPresentMarkConfig), varargs...)
}

// BatchAddPresentMarkConfig mocks base method.
func (m *MockUserPresentGOClient) BatchAddPresentMarkConfig(ctx context.Context, in *BatchAddPresentMarkConfigReq, opts ...grpc.CallOption) (*BatchAddPresentMarkConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchAddPresentMarkConfig", varargs...)
	ret0, _ := ret[0].(*BatchAddPresentMarkConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAddPresentMarkConfig indicates an expected call of BatchAddPresentMarkConfig.
func (mr *MockUserPresentGOClientMockRecorder) BatchAddPresentMarkConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddPresentMarkConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).BatchAddPresentMarkConfig), varargs...)
}

// BatchSendPresent mocks base method.
func (m *MockUserPresentGOClient) BatchSendPresent(ctx context.Context, in *BatchSendPresentReq, opts ...grpc.CallOption) (*BatchSendPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchSendPresent", varargs...)
	ret0, _ := ret[0].(*BatchSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchSendPresent indicates an expected call of BatchSendPresent.
func (mr *MockUserPresentGOClientMockRecorder) BatchSendPresent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSendPresent", reflect.TypeOf((*MockUserPresentGOClient)(nil).BatchSendPresent), varargs...)
}

// CheckActivityItemConflict mocks base method.
func (m *MockUserPresentGOClient) CheckActivityItemConflict(ctx context.Context, in *CheckActivityItemConflictReq, opts ...grpc.CallOption) (*CheckActivityItemConflictResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckActivityItemConflict", varargs...)
	ret0, _ := ret[0].(*CheckActivityItemConflictResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckActivityItemConflict indicates an expected call of CheckActivityItemConflict.
func (mr *MockUserPresentGOClientMockRecorder) CheckActivityItemConflict(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckActivityItemConflict", reflect.TypeOf((*MockUserPresentGOClient)(nil).CheckActivityItemConflict), varargs...)
}

// ClearScenePresent mocks base method.
func (m *MockUserPresentGOClient) ClearScenePresent(ctx context.Context, in *ClearScenePresentReq, opts ...grpc.CallOption) (*ClearScenePresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearScenePresent", varargs...)
	ret0, _ := ret[0].(*ClearScenePresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearScenePresent indicates an expected call of ClearScenePresent.
func (mr *MockUserPresentGOClientMockRecorder) ClearScenePresent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearScenePresent", reflect.TypeOf((*MockUserPresentGOClient)(nil).ClearScenePresent), varargs...)
}

// DelChanceItemSource mocks base method.
func (m *MockUserPresentGOClient) DelChanceItemSource(ctx context.Context, in *DelChanceItemSourceReq, opts ...grpc.CallOption) (*DelChanceItemSourceResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelChanceItemSource", varargs...)
	ret0, _ := ret[0].(*DelChanceItemSourceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelChanceItemSource indicates an expected call of DelChanceItemSource.
func (mr *MockUserPresentGOClientMockRecorder) DelChanceItemSource(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChanceItemSource", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelChanceItemSource), varargs...)
}

// DelDynamicEffectTemplate mocks base method.
func (m *MockUserPresentGOClient) DelDynamicEffectTemplate(ctx context.Context, in *DelDynamicEffectTemplateReq, opts ...grpc.CallOption) (*DelDynamicEffectTemplateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelDynamicEffectTemplate", varargs...)
	ret0, _ := ret[0].(*DelDynamicEffectTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelDynamicEffectTemplate indicates an expected call of DelDynamicEffectTemplate.
func (mr *MockUserPresentGOClientMockRecorder) DelDynamicEffectTemplate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelDynamicEffectTemplate", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelDynamicEffectTemplate), varargs...)
}

// DelNamingPresentInfo mocks base method.
func (m *MockUserPresentGOClient) DelNamingPresentInfo(ctx context.Context, in *DelNamingPresentInfoReq, opts ...grpc.CallOption) (*DelNamingPresentInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelNamingPresentInfo", varargs...)
	ret0, _ := ret[0].(*DelNamingPresentInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelNamingPresentInfo indicates an expected call of DelNamingPresentInfo.
func (mr *MockUserPresentGOClientMockRecorder) DelNamingPresentInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelNamingPresentInfo", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelNamingPresentInfo), varargs...)
}

// DelPresentConfig mocks base method.
func (m *MockUserPresentGOClient) DelPresentConfig(ctx context.Context, in *DelPresentConfigReq, opts ...grpc.CallOption) (*DelPresentConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelPresentConfig", varargs...)
	ret0, _ := ret[0].(*DelPresentConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentConfig indicates an expected call of DelPresentConfig.
func (mr *MockUserPresentGOClientMockRecorder) DelPresentConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelPresentConfig), varargs...)
}

// DelPresentConfigBackend mocks base method.
func (m *MockUserPresentGOClient) DelPresentConfigBackend(ctx context.Context, in *DelPresentConfigBackendReq, opts ...grpc.CallOption) (*DelPresentConfigBackendResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelPresentConfigBackend", varargs...)
	ret0, _ := ret[0].(*DelPresentConfigBackendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentConfigBackend indicates an expected call of DelPresentConfigBackend.
func (mr *MockUserPresentGOClientMockRecorder) DelPresentConfigBackend(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentConfigBackend", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelPresentConfigBackend), varargs...)
}

// DelPresentEffectTemplateConfig mocks base method.
func (m *MockUserPresentGOClient) DelPresentEffectTemplateConfig(ctx context.Context, in *DelPresentEffectTemplateConfigReq, opts ...grpc.CallOption) (*DelPresentEffectTemplateConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelPresentEffectTemplateConfig", varargs...)
	ret0, _ := ret[0].(*DelPresentEffectTemplateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentEffectTemplateConfig indicates an expected call of DelPresentEffectTemplateConfig.
func (mr *MockUserPresentGOClientMockRecorder) DelPresentEffectTemplateConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentEffectTemplateConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelPresentEffectTemplateConfig), varargs...)
}

// DelPresentFlowConfig mocks base method.
func (m *MockUserPresentGOClient) DelPresentFlowConfig(ctx context.Context, in *DelPresentFlowConfigReq, opts ...grpc.CallOption) (*DelPresentFlowConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelPresentFlowConfig", varargs...)
	ret0, _ := ret[0].(*DelPresentFlowConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentFlowConfig indicates an expected call of DelPresentFlowConfig.
func (mr *MockUserPresentGOClientMockRecorder) DelPresentFlowConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentFlowConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelPresentFlowConfig), varargs...)
}

// DelPresentMarkConfig mocks base method.
func (m *MockUserPresentGOClient) DelPresentMarkConfig(ctx context.Context, in *DelPresentMarkConfigReq, opts ...grpc.CallOption) (*DelPresentMarkConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelPresentMarkConfig", varargs...)
	ret0, _ := ret[0].(*DelPresentMarkConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentMarkConfig indicates an expected call of DelPresentMarkConfig.
func (mr *MockUserPresentGOClientMockRecorder) DelPresentMarkConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentMarkConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelPresentMarkConfig), varargs...)
}

// FinishSendPresentToAi mocks base method.
func (m *MockUserPresentGOClient) FinishSendPresentToAi(ctx context.Context, in *FinishSendPresentToAiReq, opts ...grpc.CallOption) (*FinishSendPresentToAiResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FinishSendPresentToAi", varargs...)
	ret0, _ := ret[0].(*FinishSendPresentToAiResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FinishSendPresentToAi indicates an expected call of FinishSendPresentToAi.
func (mr *MockUserPresentGOClientMockRecorder) FinishSendPresentToAi(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FinishSendPresentToAi", reflect.TypeOf((*MockUserPresentGOClient)(nil).FinishSendPresentToAi), varargs...)
}

// GetAiPresentDetail mocks base method.
func (m *MockUserPresentGOClient) GetAiPresentDetail(ctx context.Context, in *GetAiPresentDetailReq, opts ...grpc.CallOption) (*GetAiPresentDetailResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAiPresentDetail", varargs...)
	ret0, _ := ret[0].(*GetAiPresentDetailResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiPresentDetail indicates an expected call of GetAiPresentDetail.
func (mr *MockUserPresentGOClientMockRecorder) GetAiPresentDetail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiPresentDetail", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetAiPresentDetail), varargs...)
}

// GetAiPresentOrderIds mocks base method.
func (m *MockUserPresentGOClient) GetAiPresentOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAiPresentOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiPresentOrderIds indicates an expected call of GetAiPresentOrderIds.
func (mr *MockUserPresentGOClientMockRecorder) GetAiPresentOrderIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiPresentOrderIds", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetAiPresentOrderIds), varargs...)
}

// GetAiPresentTotalCount mocks base method.
func (m *MockUserPresentGOClient) GetAiPresentTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAiPresentTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiPresentTotalCount indicates an expected call of GetAiPresentTotalCount.
func (mr *MockUserPresentGOClientMockRecorder) GetAiPresentTotalCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiPresentTotalCount", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetAiPresentTotalCount), varargs...)
}

// GetAllFellowPresent mocks base method.
func (m *MockUserPresentGOClient) GetAllFellowPresent(ctx context.Context, in *GetAllFellowPresentReq, opts ...grpc.CallOption) (*GetAllFellowPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllFellowPresent", varargs...)
	ret0, _ := ret[0].(*GetAllFellowPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllFellowPresent indicates an expected call of GetAllFellowPresent.
func (mr *MockUserPresentGOClientMockRecorder) GetAllFellowPresent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllFellowPresent", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetAllFellowPresent), varargs...)
}

// GetDynamicEffectTemplateList mocks base method.
func (m *MockUserPresentGOClient) GetDynamicEffectTemplateList(ctx context.Context, in *GetDynamicEffectTemplateListReq, opts ...grpc.CallOption) (*GetDynamicEffectTemplateListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDynamicEffectTemplateList", varargs...)
	ret0, _ := ret[0].(*GetDynamicEffectTemplateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDynamicEffectTemplateList indicates an expected call of GetDynamicEffectTemplateList.
func (mr *MockUserPresentGOClientMockRecorder) GetDynamicEffectTemplateList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDynamicEffectTemplateList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetDynamicEffectTemplateList), varargs...)
}

// GetItemBoundActivity mocks base method.
func (m *MockUserPresentGOClient) GetItemBoundActivity(ctx context.Context, in *GetItemBoundActivityReq, opts ...grpc.CallOption) (*GetItemBoundActivityResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetItemBoundActivity", varargs...)
	ret0, _ := ret[0].(*GetItemBoundActivityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetItemBoundActivity indicates an expected call of GetItemBoundActivity.
func (mr *MockUserPresentGOClientMockRecorder) GetItemBoundActivity(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetItemBoundActivity", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetItemBoundActivity), varargs...)
}

// GetItemLinkedActivityConfig mocks base method.
func (m *MockUserPresentGOClient) GetItemLinkedActivityConfig(ctx context.Context, in *GetItemLinkedActivityConfigReq, opts ...grpc.CallOption) (*GetItemLinkedActivityConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetItemLinkedActivityConfig", varargs...)
	ret0, _ := ret[0].(*GetItemLinkedActivityConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetItemLinkedActivityConfig indicates an expected call of GetItemLinkedActivityConfig.
func (mr *MockUserPresentGOClientMockRecorder) GetItemLinkedActivityConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetItemLinkedActivityConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetItemLinkedActivityConfig), varargs...)
}

// GetLivePresentOrderList mocks base method.
func (m *MockUserPresentGOClient) GetLivePresentOrderList(ctx context.Context, in *GetLivePresentOrderListReq, opts ...grpc.CallOption) (*GetLivePresentOrderListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLivePresentOrderList", varargs...)
	ret0, _ := ret[0].(*GetLivePresentOrderListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivePresentOrderList indicates an expected call of GetLivePresentOrderList.
func (mr *MockUserPresentGOClientMockRecorder) GetLivePresentOrderList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivePresentOrderList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetLivePresentOrderList), varargs...)
}

// GetNamingPresentInfoList mocks base method.
func (m *MockUserPresentGOClient) GetNamingPresentInfoList(ctx context.Context, in *GetNamingPresentInfoListReq, opts ...grpc.CallOption) (*GetNamingPresentInfoListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNamingPresentInfoList", varargs...)
	ret0, _ := ret[0].(*GetNamingPresentInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNamingPresentInfoList indicates an expected call of GetNamingPresentInfoList.
func (mr *MockUserPresentGOClientMockRecorder) GetNamingPresentInfoList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNamingPresentInfoList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetNamingPresentInfoList), varargs...)
}

// GetOrderLogByOrderIds mocks base method.
func (m *MockUserPresentGOClient) GetOrderLogByOrderIds(ctx context.Context, in *GetOrderLogByOrderIdsReq, opts ...grpc.CallOption) (*GetOrderLogByOrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOrderLogByOrderIds", varargs...)
	ret0, _ := ret[0].(*GetOrderLogByOrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderLogByOrderIds indicates an expected call of GetOrderLogByOrderIds.
func (mr *MockUserPresentGOClientMockRecorder) GetOrderLogByOrderIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderLogByOrderIds", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetOrderLogByOrderIds), varargs...)
}

// GetPresentActivityConfigList mocks base method.
func (m *MockUserPresentGOClient) GetPresentActivityConfigList(ctx context.Context, in *GetPresentActivityConfigListReq, opts ...grpc.CallOption) (*GetPresentActivityConfigListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentActivityConfigList", varargs...)
	ret0, _ := ret[0].(*GetPresentActivityConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentActivityConfigList indicates an expected call of GetPresentActivityConfigList.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentActivityConfigList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentActivityConfigList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentActivityConfigList), varargs...)
}

// GetPresentConfigById mocks base method.
func (m *MockUserPresentGOClient) GetPresentConfigById(ctx context.Context, in *GetPresentConfigByIdReq, opts ...grpc.CallOption) (*GetPresentConfigByIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentConfigById", varargs...)
	ret0, _ := ret[0].(*GetPresentConfigByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigById indicates an expected call of GetPresentConfigById.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentConfigById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigById", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentConfigById), varargs...)
}

// GetPresentConfigByIdBackend mocks base method.
func (m *MockUserPresentGOClient) GetPresentConfigByIdBackend(ctx context.Context, in *GetPresentConfigByIdBackendReq, opts ...grpc.CallOption) (*GetPresentConfigByIdBackendResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentConfigByIdBackend", varargs...)
	ret0, _ := ret[0].(*GetPresentConfigByIdBackendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigByIdBackend indicates an expected call of GetPresentConfigByIdBackend.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentConfigByIdBackend(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigByIdBackend", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentConfigByIdBackend), varargs...)
}

// GetPresentConfigList mocks base method.
func (m *MockUserPresentGOClient) GetPresentConfigList(ctx context.Context, in *GetPresentConfigListReq, opts ...grpc.CallOption) (*GetPresentConfigListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentConfigList", varargs...)
	ret0, _ := ret[0].(*GetPresentConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigList indicates an expected call of GetPresentConfigList.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentConfigList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentConfigList), varargs...)
}

// GetPresentConfigListBackend mocks base method.
func (m *MockUserPresentGOClient) GetPresentConfigListBackend(ctx context.Context, in *GetPresentConfigListBackendReq, opts ...grpc.CallOption) (*GetPresentConfigListBackendResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentConfigListBackend", varargs...)
	ret0, _ := ret[0].(*GetPresentConfigListBackendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigListBackend indicates an expected call of GetPresentConfigListBackend.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentConfigListBackend(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigListBackend", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentConfigListBackend), varargs...)
}

// GetPresentConfigListByIdList mocks base method.
func (m *MockUserPresentGOClient) GetPresentConfigListByIdList(ctx context.Context, in *GetPresentConfigListByIdListReq, opts ...grpc.CallOption) (*GetPresentConfigListByIdListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentConfigListByIdList", varargs...)
	ret0, _ := ret[0].(*GetPresentConfigListByIdListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigListByIdList indicates an expected call of GetPresentConfigListByIdList.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentConfigListByIdList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigListByIdList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentConfigListByIdList), varargs...)
}

// GetPresentConfigListV3 mocks base method.
func (m *MockUserPresentGOClient) GetPresentConfigListV3(ctx context.Context, in *GetPresentConfigListV3Req, opts ...grpc.CallOption) (*GetPresentConfigListV3Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentConfigListV3", varargs...)
	ret0, _ := ret[0].(*GetPresentConfigListV3Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigListV3 indicates an expected call of GetPresentConfigListV3.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentConfigListV3(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigListV3", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentConfigListV3), varargs...)
}

// GetPresentConfigUpdateTime mocks base method.
func (m *MockUserPresentGOClient) GetPresentConfigUpdateTime(ctx context.Context, in *GetPresentConfigUpdateTimeReq, opts ...grpc.CallOption) (*GetPresentConfigUpdateTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentConfigUpdateTime", varargs...)
	ret0, _ := ret[0].(*GetPresentConfigUpdateTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigUpdateTime indicates an expected call of GetPresentConfigUpdateTime.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentConfigUpdateTime(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigUpdateTime", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentConfigUpdateTime), varargs...)
}

// GetPresentDETConfigById mocks base method.
func (m *MockUserPresentGOClient) GetPresentDETConfigById(ctx context.Context, in *GetPresentDETConfigByIdReq, opts ...grpc.CallOption) (*GetPresentDETConfigByIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentDETConfigById", varargs...)
	ret0, _ := ret[0].(*GetPresentDETConfigByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentDETConfigById indicates an expected call of GetPresentDETConfigById.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentDETConfigById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentDETConfigById", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentDETConfigById), varargs...)
}

// GetPresentDynamicEffectTemplateConfig mocks base method.
func (m *MockUserPresentGOClient) GetPresentDynamicEffectTemplateConfig(ctx context.Context, in *GetPresentDynamicEffectTemplateConfigReq, opts ...grpc.CallOption) (*GetPresentDynamicEffectTemplateConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentDynamicEffectTemplateConfig", varargs...)
	ret0, _ := ret[0].(*GetPresentDynamicEffectTemplateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentDynamicEffectTemplateConfig indicates an expected call of GetPresentDynamicEffectTemplateConfig.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentDynamicEffectTemplateConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentDynamicEffectTemplateConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentDynamicEffectTemplateConfig), varargs...)
}

// GetPresentDynamicTemplateConfUpdateTime mocks base method.
func (m *MockUserPresentGOClient) GetPresentDynamicTemplateConfUpdateTime(ctx context.Context, in *GetPresentDynamicTemplateConfUpdateTimeReq, opts ...grpc.CallOption) (*GetPresentDynamicTemplateConfUpdateTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentDynamicTemplateConfUpdateTime", varargs...)
	ret0, _ := ret[0].(*GetPresentDynamicTemplateConfUpdateTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentDynamicTemplateConfUpdateTime indicates an expected call of GetPresentDynamicTemplateConfUpdateTime.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentDynamicTemplateConfUpdateTime(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentDynamicTemplateConfUpdateTime", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentDynamicTemplateConfUpdateTime), varargs...)
}

// GetPresentEffectTemplateConfigList mocks base method.
func (m *MockUserPresentGOClient) GetPresentEffectTemplateConfigList(ctx context.Context, in *GetPresentEffectTemplateConfigListReq, opts ...grpc.CallOption) (*GetPresentEffectTemplateConfigListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentEffectTemplateConfigList", varargs...)
	ret0, _ := ret[0].(*GetPresentEffectTemplateConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentEffectTemplateConfigList indicates an expected call of GetPresentEffectTemplateConfigList.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentEffectTemplateConfigList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentEffectTemplateConfigList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentEffectTemplateConfigList), varargs...)
}

// GetPresentFlowConfigById mocks base method.
func (m *MockUserPresentGOClient) GetPresentFlowConfigById(ctx context.Context, in *GetPresentFlowConfigByIdReq, opts ...grpc.CallOption) (*GetPresentFlowConfigByIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentFlowConfigById", varargs...)
	ret0, _ := ret[0].(*GetPresentFlowConfigByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentFlowConfigById indicates an expected call of GetPresentFlowConfigById.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentFlowConfigById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigById", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentFlowConfigById), varargs...)
}

// GetPresentFlowConfigList mocks base method.
func (m *MockUserPresentGOClient) GetPresentFlowConfigList(ctx context.Context, in *GetPresentFlowConfigListReq, opts ...grpc.CallOption) (*GetPresentFlowConfigListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentFlowConfigList", varargs...)
	ret0, _ := ret[0].(*GetPresentFlowConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentFlowConfigList indicates an expected call of GetPresentFlowConfigList.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentFlowConfigList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentFlowConfigList), varargs...)
}

// GetPresentFlowConfigUpdateTime mocks base method.
func (m *MockUserPresentGOClient) GetPresentFlowConfigUpdateTime(ctx context.Context, in *GetPresentFlowConfigUpdateTimeReq, opts ...grpc.CallOption) (*GetPresentFlowConfigUpdateTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentFlowConfigUpdateTime", varargs...)
	ret0, _ := ret[0].(*GetPresentFlowConfigUpdateTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentFlowConfigUpdateTime indicates an expected call of GetPresentFlowConfigUpdateTime.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentFlowConfigUpdateTime(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigUpdateTime", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentFlowConfigUpdateTime), varargs...)
}

// GetPresentMarkConfigList mocks base method.
func (m *MockUserPresentGOClient) GetPresentMarkConfigList(ctx context.Context, in *GetPresentMarkConfigListReq, opts ...grpc.CallOption) (*GetPresentMarkConfigListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentMarkConfigList", varargs...)
	ret0, _ := ret[0].(*GetPresentMarkConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentMarkConfigList indicates an expected call of GetPresentMarkConfigList.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentMarkConfigList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentMarkConfigList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentMarkConfigList), varargs...)
}

// GetPresentMarkIconByPresentId mocks base method.
func (m *MockUserPresentGOClient) GetPresentMarkIconByPresentId(ctx context.Context, in *GetPresentMarkIconByPresentIdReq, opts ...grpc.CallOption) (*GetPresentMarkIconByPresentIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentMarkIconByPresentId", varargs...)
	ret0, _ := ret[0].(*GetPresentMarkIconByPresentIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentMarkIconByPresentId indicates an expected call of GetPresentMarkIconByPresentId.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentMarkIconByPresentId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentMarkIconByPresentId", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentMarkIconByPresentId), varargs...)
}

// GetPresentOrderStatus mocks base method.
func (m *MockUserPresentGOClient) GetPresentOrderStatus(ctx context.Context, in *GetPresentOrderStatusReq, opts ...grpc.CallOption) (*GetPresentOrderStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentOrderStatus", varargs...)
	ret0, _ := ret[0].(*GetPresentOrderStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentOrderStatus indicates an expected call of GetPresentOrderStatus.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentOrderStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentOrderStatus", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentOrderStatus), varargs...)
}

// GetScenePresentDetailList mocks base method.
func (m *MockUserPresentGOClient) GetScenePresentDetailList(ctx context.Context, in *GetScenePresentDetailListReq, opts ...grpc.CallOption) (*GetScenePresentDetailListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScenePresentDetailList", varargs...)
	ret0, _ := ret[0].(*GetScenePresentDetailListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScenePresentDetailList indicates an expected call of GetScenePresentDetailList.
func (mr *MockUserPresentGOClientMockRecorder) GetScenePresentDetailList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScenePresentDetailList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetScenePresentDetailList), varargs...)
}

// GetScenePresentSummary mocks base method.
func (m *MockUserPresentGOClient) GetScenePresentSummary(ctx context.Context, in *GetScenePresentSummaryReq, opts ...grpc.CallOption) (*GetScenePresentSummaryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScenePresentSummary", varargs...)
	ret0, _ := ret[0].(*GetScenePresentSummaryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScenePresentSummary indicates an expected call of GetScenePresentSummary.
func (mr *MockUserPresentGOClientMockRecorder) GetScenePresentSummary(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScenePresentSummary", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetScenePresentSummary), varargs...)
}

// GetUserPresentDetailList mocks base method.
func (m *MockUserPresentGOClient) GetUserPresentDetailList(ctx context.Context, in *GetUserPresentDetailListReq, opts ...grpc.CallOption) (*GetUserPresentDetailListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPresentDetailList", varargs...)
	ret0, _ := ret[0].(*GetUserPresentDetailListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentDetailList indicates an expected call of GetUserPresentDetailList.
func (mr *MockUserPresentGOClientMockRecorder) GetUserPresentDetailList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentDetailList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetUserPresentDetailList), varargs...)
}

// GetUserPresentReceive mocks base method.
func (m *MockUserPresentGOClient) GetUserPresentReceive(ctx context.Context, in *GetUserPresentReceiveReq, opts ...grpc.CallOption) (*GetUserPresentReceiveResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPresentReceive", varargs...)
	ret0, _ := ret[0].(*GetUserPresentReceiveResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentReceive indicates an expected call of GetUserPresentReceive.
func (mr *MockUserPresentGOClientMockRecorder) GetUserPresentReceive(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentReceive", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetUserPresentReceive), varargs...)
}

// GetUserPresentSend mocks base method.
func (m *MockUserPresentGOClient) GetUserPresentSend(ctx context.Context, in *GetUserPresentSendReq, opts ...grpc.CallOption) (*GetUserPresentSendResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPresentSend", varargs...)
	ret0, _ := ret[0].(*GetUserPresentSendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSend indicates an expected call of GetUserPresentSend.
func (mr *MockUserPresentGOClientMockRecorder) GetUserPresentSend(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSend", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetUserPresentSend), varargs...)
}

// GetUserPresentSendDetailList mocks base method.
func (m *MockUserPresentGOClient) GetUserPresentSendDetailList(ctx context.Context, in *GetUserPresentSendDetailListReq, opts ...grpc.CallOption) (*GetUserPresentSendDetailListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPresentSendDetailList", varargs...)
	ret0, _ := ret[0].(*GetUserPresentSendDetailListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSendDetailList indicates an expected call of GetUserPresentSendDetailList.
func (mr *MockUserPresentGOClientMockRecorder) GetUserPresentSendDetailList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSendDetailList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetUserPresentSendDetailList), varargs...)
}

// GetUserPresentSummary mocks base method.
func (m *MockUserPresentGOClient) GetUserPresentSummary(ctx context.Context, in *GetUserPresentSummaryReq, opts ...grpc.CallOption) (*GetUserPresentSummaryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPresentSummary", varargs...)
	ret0, _ := ret[0].(*GetUserPresentSummaryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSummary indicates an expected call of GetUserPresentSummary.
func (mr *MockUserPresentGOClientMockRecorder) GetUserPresentSummary(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSummary", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetUserPresentSummary), varargs...)
}

// GetUserPresentSummaryByItemList mocks base method.
func (m *MockUserPresentGOClient) GetUserPresentSummaryByItemList(ctx context.Context, in *GetUserPresentSummaryByItemListReq, opts ...grpc.CallOption) (*GetUserPresentSummaryByItemListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPresentSummaryByItemList", varargs...)
	ret0, _ := ret[0].(*GetUserPresentSummaryByItemListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSummaryByItemList indicates an expected call of GetUserPresentSummaryByItemList.
func (mr *MockUserPresentGOClientMockRecorder) GetUserPresentSummaryByItemList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSummaryByItemList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetUserPresentSummaryByItemList), varargs...)
}

// GetValidNamingPresentInfos mocks base method.
func (m *MockUserPresentGOClient) GetValidNamingPresentInfos(ctx context.Context, in *GetValidNamingPresentInfosReq, opts ...grpc.CallOption) (*GetValidNamingPresentInfosResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetValidNamingPresentInfos", varargs...)
	ret0, _ := ret[0].(*GetValidNamingPresentInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetValidNamingPresentInfos indicates an expected call of GetValidNamingPresentInfos.
func (mr *MockUserPresentGOClientMockRecorder) GetValidNamingPresentInfos(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetValidNamingPresentInfos", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetValidNamingPresentInfos), varargs...)
}

// LinkItemToActivity mocks base method.
func (m *MockUserPresentGOClient) LinkItemToActivity(ctx context.Context, in *LinkItemToActivityReq, opts ...grpc.CallOption) (*LinkItemToActivityResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LinkItemToActivity", varargs...)
	ret0, _ := ret[0].(*LinkItemToActivityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LinkItemToActivity indicates an expected call of LinkItemToActivity.
func (mr *MockUserPresentGOClientMockRecorder) LinkItemToActivity(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkItemToActivity", reflect.TypeOf((*MockUserPresentGOClient)(nil).LinkItemToActivity), varargs...)
}

// PrepareSendPresentToAi mocks base method.
func (m *MockUserPresentGOClient) PrepareSendPresentToAi(ctx context.Context, in *SendPresentToAiReq, opts ...grpc.CallOption) (*SendPresentToAiResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PrepareSendPresentToAi", varargs...)
	ret0, _ := ret[0].(*SendPresentToAiResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PrepareSendPresentToAi indicates an expected call of PrepareSendPresentToAi.
func (mr *MockUserPresentGOClientMockRecorder) PrepareSendPresentToAi(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrepareSendPresentToAi", reflect.TypeOf((*MockUserPresentGOClient)(nil).PrepareSendPresentToAi), varargs...)
}

// RecordSceneSendPresent mocks base method.
func (m *MockUserPresentGOClient) RecordSceneSendPresent(ctx context.Context, in *RecordSceneSendPresentReq, opts ...grpc.CallOption) (*RecordSceneSendPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordSceneSendPresent", varargs...)
	ret0, _ := ret[0].(*RecordSceneSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordSceneSendPresent indicates an expected call of RecordSceneSendPresent.
func (mr *MockUserPresentGOClientMockRecorder) RecordSceneSendPresent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordSceneSendPresent", reflect.TypeOf((*MockUserPresentGOClient)(nil).RecordSceneSendPresent), varargs...)
}

// ReissueAiPresentOrder mocks base method.
func (m *MockUserPresentGOClient) ReissueAiPresentOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReissueAiPresentOrder", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReissueAiPresentOrder indicates an expected call of ReissueAiPresentOrder.
func (mr *MockUserPresentGOClientMockRecorder) ReissueAiPresentOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReissueAiPresentOrder", reflect.TypeOf((*MockUserPresentGOClient)(nil).ReissueAiPresentOrder), varargs...)
}

// SendPresent mocks base method.
func (m *MockUserPresentGOClient) SendPresent(ctx context.Context, in *SendPresentReq, opts ...grpc.CallOption) (*SendPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendPresent", varargs...)
	ret0, _ := ret[0].(*SendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPresent indicates an expected call of SendPresent.
func (mr *MockUserPresentGOClientMockRecorder) SendPresent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPresent", reflect.TypeOf((*MockUserPresentGOClient)(nil).SendPresent), varargs...)
}

// SendPresentToAi mocks base method.
func (m *MockUserPresentGOClient) SendPresentToAi(ctx context.Context, in *SendPresentToAiReq, opts ...grpc.CallOption) (*SendPresentToAiResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendPresentToAi", varargs...)
	ret0, _ := ret[0].(*SendPresentToAiResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPresentToAi indicates an expected call of SendPresentToAi.
func (mr *MockUserPresentGOClientMockRecorder) SendPresentToAi(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPresentToAi", reflect.TypeOf((*MockUserPresentGOClient)(nil).SendPresentToAi), varargs...)
}

// UpdateDynamicEffectTemplate mocks base method.
func (m *MockUserPresentGOClient) UpdateDynamicEffectTemplate(ctx context.Context, in *UpdateDynamicEffectTemplateReq, opts ...grpc.CallOption) (*UpdateDynamicEffectTemplateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateDynamicEffectTemplate", varargs...)
	ret0, _ := ret[0].(*UpdateDynamicEffectTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateDynamicEffectTemplate indicates an expected call of UpdateDynamicEffectTemplate.
func (mr *MockUserPresentGOClientMockRecorder) UpdateDynamicEffectTemplate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDynamicEffectTemplate", reflect.TypeOf((*MockUserPresentGOClient)(nil).UpdateDynamicEffectTemplate), varargs...)
}

// UpdateNamingPresentInfo mocks base method.
func (m *MockUserPresentGOClient) UpdateNamingPresentInfo(ctx context.Context, in *UpdateNamingPresentInfoReq, opts ...grpc.CallOption) (*UpdateNamingPresentInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateNamingPresentInfo", varargs...)
	ret0, _ := ret[0].(*UpdateNamingPresentInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateNamingPresentInfo indicates an expected call of UpdateNamingPresentInfo.
func (mr *MockUserPresentGOClientMockRecorder) UpdateNamingPresentInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNamingPresentInfo", reflect.TypeOf((*MockUserPresentGOClient)(nil).UpdateNamingPresentInfo), varargs...)
}

// UpdatePresentActivityConfig mocks base method.
func (m *MockUserPresentGOClient) UpdatePresentActivityConfig(ctx context.Context, in *UpdatePresentActivityConfigReq, opts ...grpc.CallOption) (*UpdatePresentActivityConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePresentActivityConfig", varargs...)
	ret0, _ := ret[0].(*UpdatePresentActivityConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentActivityConfig indicates an expected call of UpdatePresentActivityConfig.
func (mr *MockUserPresentGOClientMockRecorder) UpdatePresentActivityConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentActivityConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).UpdatePresentActivityConfig), varargs...)
}

// UpdatePresentConfig mocks base method.
func (m *MockUserPresentGOClient) UpdatePresentConfig(ctx context.Context, in *UpdatePresentConfigReq, opts ...grpc.CallOption) (*UpdatePresentConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePresentConfig", varargs...)
	ret0, _ := ret[0].(*UpdatePresentConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentConfig indicates an expected call of UpdatePresentConfig.
func (mr *MockUserPresentGOClientMockRecorder) UpdatePresentConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).UpdatePresentConfig), varargs...)
}

// UpdatePresentConfigBackend mocks base method.
func (m *MockUserPresentGOClient) UpdatePresentConfigBackend(ctx context.Context, in *UpdatePresentConfigBackendReq, opts ...grpc.CallOption) (*UpdatePresentConfigBackendResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePresentConfigBackend", varargs...)
	ret0, _ := ret[0].(*UpdatePresentConfigBackendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentConfigBackend indicates an expected call of UpdatePresentConfigBackend.
func (mr *MockUserPresentGOClientMockRecorder) UpdatePresentConfigBackend(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentConfigBackend", reflect.TypeOf((*MockUserPresentGOClient)(nil).UpdatePresentConfigBackend), varargs...)
}

// UpdatePresentEffectTemplateConfig mocks base method.
func (m *MockUserPresentGOClient) UpdatePresentEffectTemplateConfig(ctx context.Context, in *UpdatePresentEffectTemplateConfigReq, opts ...grpc.CallOption) (*UpdatePresentEffectTemplateConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePresentEffectTemplateConfig", varargs...)
	ret0, _ := ret[0].(*UpdatePresentEffectTemplateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentEffectTemplateConfig indicates an expected call of UpdatePresentEffectTemplateConfig.
func (mr *MockUserPresentGOClientMockRecorder) UpdatePresentEffectTemplateConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentEffectTemplateConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).UpdatePresentEffectTemplateConfig), varargs...)
}

// UpdatePresentFlowConfig mocks base method.
func (m *MockUserPresentGOClient) UpdatePresentFlowConfig(ctx context.Context, in *UpdatePresentFlowConfigReq, opts ...grpc.CallOption) (*UpdatePresentFlowConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePresentFlowConfig", varargs...)
	ret0, _ := ret[0].(*UpdatePresentFlowConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentFlowConfig indicates an expected call of UpdatePresentFlowConfig.
func (mr *MockUserPresentGOClientMockRecorder) UpdatePresentFlowConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentFlowConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).UpdatePresentFlowConfig), varargs...)
}

// MockUserPresentGOServer is a mock of UserPresentGOServer interface.
type MockUserPresentGOServer struct {
	ctrl     *gomock.Controller
	recorder *MockUserPresentGOServerMockRecorder
}

// MockUserPresentGOServerMockRecorder is the mock recorder for MockUserPresentGOServer.
type MockUserPresentGOServerMockRecorder struct {
	mock *MockUserPresentGOServer
}

// NewMockUserPresentGOServer creates a new mock instance.
func NewMockUserPresentGOServer(ctrl *gomock.Controller) *MockUserPresentGOServer {
	mock := &MockUserPresentGOServer{ctrl: ctrl}
	mock.recorder = &MockUserPresentGOServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserPresentGOServer) EXPECT() *MockUserPresentGOServerMockRecorder {
	return m.recorder
}

// AddChanceItemSource mocks base method.
func (m *MockUserPresentGOServer) AddChanceItemSource(ctx context.Context, in *AddChanceItemSourceReq) (*AddChanceItemSourceResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChanceItemSource", ctx, in)
	ret0, _ := ret[0].(*AddChanceItemSourceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddChanceItemSource indicates an expected call of AddChanceItemSource.
func (mr *MockUserPresentGOServerMockRecorder) AddChanceItemSource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChanceItemSource", reflect.TypeOf((*MockUserPresentGOServer)(nil).AddChanceItemSource), ctx, in)
}

// AddDynamicEffectTemplate mocks base method.
func (m *MockUserPresentGOServer) AddDynamicEffectTemplate(ctx context.Context, in *AddDynamicEffectTemplateReq) (*AddDynamicEffectTemplateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddDynamicEffectTemplate", ctx, in)
	ret0, _ := ret[0].(*AddDynamicEffectTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddDynamicEffectTemplate indicates an expected call of AddDynamicEffectTemplate.
func (mr *MockUserPresentGOServerMockRecorder) AddDynamicEffectTemplate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddDynamicEffectTemplate", reflect.TypeOf((*MockUserPresentGOServer)(nil).AddDynamicEffectTemplate), ctx, in)
}

// AddNamingPresentInfo mocks base method.
func (m *MockUserPresentGOServer) AddNamingPresentInfo(ctx context.Context, in *AddNamingPresentInfoReq) (*AddNamingPresentInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddNamingPresentInfo", ctx, in)
	ret0, _ := ret[0].(*AddNamingPresentInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddNamingPresentInfo indicates an expected call of AddNamingPresentInfo.
func (mr *MockUserPresentGOServerMockRecorder) AddNamingPresentInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNamingPresentInfo", reflect.TypeOf((*MockUserPresentGOServer)(nil).AddNamingPresentInfo), ctx, in)
}

// AddPresentActivityConfig mocks base method.
func (m *MockUserPresentGOServer) AddPresentActivityConfig(ctx context.Context, in *AddPresentActivityConfigReq) (*AddPresentActivityConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentActivityConfig", ctx, in)
	ret0, _ := ret[0].(*AddPresentActivityConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentActivityConfig indicates an expected call of AddPresentActivityConfig.
func (mr *MockUserPresentGOServerMockRecorder) AddPresentActivityConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentActivityConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).AddPresentActivityConfig), ctx, in)
}

// AddPresentConfig mocks base method.
func (m *MockUserPresentGOServer) AddPresentConfig(ctx context.Context, in *AddPresentConfigReq) (*AddPresentConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentConfig", ctx, in)
	ret0, _ := ret[0].(*AddPresentConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentConfig indicates an expected call of AddPresentConfig.
func (mr *MockUserPresentGOServerMockRecorder) AddPresentConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).AddPresentConfig), ctx, in)
}

// AddPresentConfigBackend mocks base method.
func (m *MockUserPresentGOServer) AddPresentConfigBackend(ctx context.Context, in *AddPresentConfigBackendReq) (*AddPresentConfigBackendResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentConfigBackend", ctx, in)
	ret0, _ := ret[0].(*AddPresentConfigBackendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentConfigBackend indicates an expected call of AddPresentConfigBackend.
func (mr *MockUserPresentGOServerMockRecorder) AddPresentConfigBackend(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentConfigBackend", reflect.TypeOf((*MockUserPresentGOServer)(nil).AddPresentConfigBackend), ctx, in)
}

// AddPresentEffectTemplateConfig mocks base method.
func (m *MockUserPresentGOServer) AddPresentEffectTemplateConfig(ctx context.Context, in *AddPresentEffectTemplateConfigReq) (*AddPresentEffectTemplateConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentEffectTemplateConfig", ctx, in)
	ret0, _ := ret[0].(*AddPresentEffectTemplateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentEffectTemplateConfig indicates an expected call of AddPresentEffectTemplateConfig.
func (mr *MockUserPresentGOServerMockRecorder) AddPresentEffectTemplateConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentEffectTemplateConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).AddPresentEffectTemplateConfig), ctx, in)
}

// AddPresentFlowConfig mocks base method.
func (m *MockUserPresentGOServer) AddPresentFlowConfig(ctx context.Context, in *AddPresentFlowConfigReq) (*AddPresentFlowConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentFlowConfig", ctx, in)
	ret0, _ := ret[0].(*AddPresentFlowConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentFlowConfig indicates an expected call of AddPresentFlowConfig.
func (mr *MockUserPresentGOServerMockRecorder) AddPresentFlowConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentFlowConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).AddPresentFlowConfig), ctx, in)
}

// AddPresentMarkConfig mocks base method.
func (m *MockUserPresentGOServer) AddPresentMarkConfig(ctx context.Context, in *AddPresentMarkConfigReq) (*AddPresentMarkConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentMarkConfig", ctx, in)
	ret0, _ := ret[0].(*AddPresentMarkConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentMarkConfig indicates an expected call of AddPresentMarkConfig.
func (mr *MockUserPresentGOServerMockRecorder) AddPresentMarkConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentMarkConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).AddPresentMarkConfig), ctx, in)
}

// BatchAddPresentMarkConfig mocks base method.
func (m *MockUserPresentGOServer) BatchAddPresentMarkConfig(ctx context.Context, in *BatchAddPresentMarkConfigReq) (*BatchAddPresentMarkConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddPresentMarkConfig", ctx, in)
	ret0, _ := ret[0].(*BatchAddPresentMarkConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAddPresentMarkConfig indicates an expected call of BatchAddPresentMarkConfig.
func (mr *MockUserPresentGOServerMockRecorder) BatchAddPresentMarkConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddPresentMarkConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).BatchAddPresentMarkConfig), ctx, in)
}

// BatchSendPresent mocks base method.
func (m *MockUserPresentGOServer) BatchSendPresent(ctx context.Context, in *BatchSendPresentReq) (*BatchSendPresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSendPresent", ctx, in)
	ret0, _ := ret[0].(*BatchSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchSendPresent indicates an expected call of BatchSendPresent.
func (mr *MockUserPresentGOServerMockRecorder) BatchSendPresent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSendPresent", reflect.TypeOf((*MockUserPresentGOServer)(nil).BatchSendPresent), ctx, in)
}

// CheckActivityItemConflict mocks base method.
func (m *MockUserPresentGOServer) CheckActivityItemConflict(ctx context.Context, in *CheckActivityItemConflictReq) (*CheckActivityItemConflictResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckActivityItemConflict", ctx, in)
	ret0, _ := ret[0].(*CheckActivityItemConflictResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckActivityItemConflict indicates an expected call of CheckActivityItemConflict.
func (mr *MockUserPresentGOServerMockRecorder) CheckActivityItemConflict(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckActivityItemConflict", reflect.TypeOf((*MockUserPresentGOServer)(nil).CheckActivityItemConflict), ctx, in)
}

// ClearScenePresent mocks base method.
func (m *MockUserPresentGOServer) ClearScenePresent(ctx context.Context, in *ClearScenePresentReq) (*ClearScenePresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearScenePresent", ctx, in)
	ret0, _ := ret[0].(*ClearScenePresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearScenePresent indicates an expected call of ClearScenePresent.
func (mr *MockUserPresentGOServerMockRecorder) ClearScenePresent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearScenePresent", reflect.TypeOf((*MockUserPresentGOServer)(nil).ClearScenePresent), ctx, in)
}

// DelChanceItemSource mocks base method.
func (m *MockUserPresentGOServer) DelChanceItemSource(ctx context.Context, in *DelChanceItemSourceReq) (*DelChanceItemSourceResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelChanceItemSource", ctx, in)
	ret0, _ := ret[0].(*DelChanceItemSourceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelChanceItemSource indicates an expected call of DelChanceItemSource.
func (mr *MockUserPresentGOServerMockRecorder) DelChanceItemSource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChanceItemSource", reflect.TypeOf((*MockUserPresentGOServer)(nil).DelChanceItemSource), ctx, in)
}

// DelDynamicEffectTemplate mocks base method.
func (m *MockUserPresentGOServer) DelDynamicEffectTemplate(ctx context.Context, in *DelDynamicEffectTemplateReq) (*DelDynamicEffectTemplateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelDynamicEffectTemplate", ctx, in)
	ret0, _ := ret[0].(*DelDynamicEffectTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelDynamicEffectTemplate indicates an expected call of DelDynamicEffectTemplate.
func (mr *MockUserPresentGOServerMockRecorder) DelDynamicEffectTemplate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelDynamicEffectTemplate", reflect.TypeOf((*MockUserPresentGOServer)(nil).DelDynamicEffectTemplate), ctx, in)
}

// DelNamingPresentInfo mocks base method.
func (m *MockUserPresentGOServer) DelNamingPresentInfo(ctx context.Context, in *DelNamingPresentInfoReq) (*DelNamingPresentInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelNamingPresentInfo", ctx, in)
	ret0, _ := ret[0].(*DelNamingPresentInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelNamingPresentInfo indicates an expected call of DelNamingPresentInfo.
func (mr *MockUserPresentGOServerMockRecorder) DelNamingPresentInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelNamingPresentInfo", reflect.TypeOf((*MockUserPresentGOServer)(nil).DelNamingPresentInfo), ctx, in)
}

// DelPresentConfig mocks base method.
func (m *MockUserPresentGOServer) DelPresentConfig(ctx context.Context, in *DelPresentConfigReq) (*DelPresentConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPresentConfig", ctx, in)
	ret0, _ := ret[0].(*DelPresentConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentConfig indicates an expected call of DelPresentConfig.
func (mr *MockUserPresentGOServerMockRecorder) DelPresentConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).DelPresentConfig), ctx, in)
}

// DelPresentConfigBackend mocks base method.
func (m *MockUserPresentGOServer) DelPresentConfigBackend(ctx context.Context, in *DelPresentConfigBackendReq) (*DelPresentConfigBackendResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPresentConfigBackend", ctx, in)
	ret0, _ := ret[0].(*DelPresentConfigBackendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentConfigBackend indicates an expected call of DelPresentConfigBackend.
func (mr *MockUserPresentGOServerMockRecorder) DelPresentConfigBackend(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentConfigBackend", reflect.TypeOf((*MockUserPresentGOServer)(nil).DelPresentConfigBackend), ctx, in)
}

// DelPresentEffectTemplateConfig mocks base method.
func (m *MockUserPresentGOServer) DelPresentEffectTemplateConfig(ctx context.Context, in *DelPresentEffectTemplateConfigReq) (*DelPresentEffectTemplateConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPresentEffectTemplateConfig", ctx, in)
	ret0, _ := ret[0].(*DelPresentEffectTemplateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentEffectTemplateConfig indicates an expected call of DelPresentEffectTemplateConfig.
func (mr *MockUserPresentGOServerMockRecorder) DelPresentEffectTemplateConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentEffectTemplateConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).DelPresentEffectTemplateConfig), ctx, in)
}

// DelPresentFlowConfig mocks base method.
func (m *MockUserPresentGOServer) DelPresentFlowConfig(ctx context.Context, in *DelPresentFlowConfigReq) (*DelPresentFlowConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPresentFlowConfig", ctx, in)
	ret0, _ := ret[0].(*DelPresentFlowConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentFlowConfig indicates an expected call of DelPresentFlowConfig.
func (mr *MockUserPresentGOServerMockRecorder) DelPresentFlowConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentFlowConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).DelPresentFlowConfig), ctx, in)
}

// DelPresentMarkConfig mocks base method.
func (m *MockUserPresentGOServer) DelPresentMarkConfig(ctx context.Context, in *DelPresentMarkConfigReq) (*DelPresentMarkConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPresentMarkConfig", ctx, in)
	ret0, _ := ret[0].(*DelPresentMarkConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentMarkConfig indicates an expected call of DelPresentMarkConfig.
func (mr *MockUserPresentGOServerMockRecorder) DelPresentMarkConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentMarkConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).DelPresentMarkConfig), ctx, in)
}

// FinishSendPresentToAi mocks base method.
func (m *MockUserPresentGOServer) FinishSendPresentToAi(ctx context.Context, in *FinishSendPresentToAiReq) (*FinishSendPresentToAiResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FinishSendPresentToAi", ctx, in)
	ret0, _ := ret[0].(*FinishSendPresentToAiResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FinishSendPresentToAi indicates an expected call of FinishSendPresentToAi.
func (mr *MockUserPresentGOServerMockRecorder) FinishSendPresentToAi(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FinishSendPresentToAi", reflect.TypeOf((*MockUserPresentGOServer)(nil).FinishSendPresentToAi), ctx, in)
}

// GetAiPresentDetail mocks base method.
func (m *MockUserPresentGOServer) GetAiPresentDetail(ctx context.Context, in *GetAiPresentDetailReq) (*GetAiPresentDetailResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAiPresentDetail", ctx, in)
	ret0, _ := ret[0].(*GetAiPresentDetailResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiPresentDetail indicates an expected call of GetAiPresentDetail.
func (mr *MockUserPresentGOServerMockRecorder) GetAiPresentDetail(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiPresentDetail", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetAiPresentDetail), ctx, in)
}

// GetAiPresentOrderIds mocks base method.
func (m *MockUserPresentGOServer) GetAiPresentOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAiPresentOrderIds", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiPresentOrderIds indicates an expected call of GetAiPresentOrderIds.
func (mr *MockUserPresentGOServerMockRecorder) GetAiPresentOrderIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiPresentOrderIds", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetAiPresentOrderIds), ctx, in)
}

// GetAiPresentTotalCount mocks base method.
func (m *MockUserPresentGOServer) GetAiPresentTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAiPresentTotalCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiPresentTotalCount indicates an expected call of GetAiPresentTotalCount.
func (mr *MockUserPresentGOServerMockRecorder) GetAiPresentTotalCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiPresentTotalCount", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetAiPresentTotalCount), ctx, in)
}

// GetAllFellowPresent mocks base method.
func (m *MockUserPresentGOServer) GetAllFellowPresent(ctx context.Context, in *GetAllFellowPresentReq) (*GetAllFellowPresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllFellowPresent", ctx, in)
	ret0, _ := ret[0].(*GetAllFellowPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllFellowPresent indicates an expected call of GetAllFellowPresent.
func (mr *MockUserPresentGOServerMockRecorder) GetAllFellowPresent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllFellowPresent", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetAllFellowPresent), ctx, in)
}

// GetDynamicEffectTemplateList mocks base method.
func (m *MockUserPresentGOServer) GetDynamicEffectTemplateList(ctx context.Context, in *GetDynamicEffectTemplateListReq) (*GetDynamicEffectTemplateListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDynamicEffectTemplateList", ctx, in)
	ret0, _ := ret[0].(*GetDynamicEffectTemplateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDynamicEffectTemplateList indicates an expected call of GetDynamicEffectTemplateList.
func (mr *MockUserPresentGOServerMockRecorder) GetDynamicEffectTemplateList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDynamicEffectTemplateList", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetDynamicEffectTemplateList), ctx, in)
}

// GetItemBoundActivity mocks base method.
func (m *MockUserPresentGOServer) GetItemBoundActivity(ctx context.Context, in *GetItemBoundActivityReq) (*GetItemBoundActivityResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetItemBoundActivity", ctx, in)
	ret0, _ := ret[0].(*GetItemBoundActivityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetItemBoundActivity indicates an expected call of GetItemBoundActivity.
func (mr *MockUserPresentGOServerMockRecorder) GetItemBoundActivity(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetItemBoundActivity", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetItemBoundActivity), ctx, in)
}

// GetItemLinkedActivityConfig mocks base method.
func (m *MockUserPresentGOServer) GetItemLinkedActivityConfig(ctx context.Context, in *GetItemLinkedActivityConfigReq) (*GetItemLinkedActivityConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetItemLinkedActivityConfig", ctx, in)
	ret0, _ := ret[0].(*GetItemLinkedActivityConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetItemLinkedActivityConfig indicates an expected call of GetItemLinkedActivityConfig.
func (mr *MockUserPresentGOServerMockRecorder) GetItemLinkedActivityConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetItemLinkedActivityConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetItemLinkedActivityConfig), ctx, in)
}

// GetLivePresentOrderList mocks base method.
func (m *MockUserPresentGOServer) GetLivePresentOrderList(ctx context.Context, in *GetLivePresentOrderListReq) (*GetLivePresentOrderListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLivePresentOrderList", ctx, in)
	ret0, _ := ret[0].(*GetLivePresentOrderListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivePresentOrderList indicates an expected call of GetLivePresentOrderList.
func (mr *MockUserPresentGOServerMockRecorder) GetLivePresentOrderList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivePresentOrderList", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetLivePresentOrderList), ctx, in)
}

// GetNamingPresentInfoList mocks base method.
func (m *MockUserPresentGOServer) GetNamingPresentInfoList(ctx context.Context, in *GetNamingPresentInfoListReq) (*GetNamingPresentInfoListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNamingPresentInfoList", ctx, in)
	ret0, _ := ret[0].(*GetNamingPresentInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNamingPresentInfoList indicates an expected call of GetNamingPresentInfoList.
func (mr *MockUserPresentGOServerMockRecorder) GetNamingPresentInfoList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNamingPresentInfoList", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetNamingPresentInfoList), ctx, in)
}

// GetOrderLogByOrderIds mocks base method.
func (m *MockUserPresentGOServer) GetOrderLogByOrderIds(ctx context.Context, in *GetOrderLogByOrderIdsReq) (*GetOrderLogByOrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderLogByOrderIds", ctx, in)
	ret0, _ := ret[0].(*GetOrderLogByOrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderLogByOrderIds indicates an expected call of GetOrderLogByOrderIds.
func (mr *MockUserPresentGOServerMockRecorder) GetOrderLogByOrderIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderLogByOrderIds", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetOrderLogByOrderIds), ctx, in)
}

// GetPresentActivityConfigList mocks base method.
func (m *MockUserPresentGOServer) GetPresentActivityConfigList(ctx context.Context, in *GetPresentActivityConfigListReq) (*GetPresentActivityConfigListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentActivityConfigList", ctx, in)
	ret0, _ := ret[0].(*GetPresentActivityConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentActivityConfigList indicates an expected call of GetPresentActivityConfigList.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentActivityConfigList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentActivityConfigList", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentActivityConfigList), ctx, in)
}

// GetPresentConfigById mocks base method.
func (m *MockUserPresentGOServer) GetPresentConfigById(ctx context.Context, in *GetPresentConfigByIdReq) (*GetPresentConfigByIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigById", ctx, in)
	ret0, _ := ret[0].(*GetPresentConfigByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigById indicates an expected call of GetPresentConfigById.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentConfigById(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigById", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentConfigById), ctx, in)
}

// GetPresentConfigByIdBackend mocks base method.
func (m *MockUserPresentGOServer) GetPresentConfigByIdBackend(ctx context.Context, in *GetPresentConfigByIdBackendReq) (*GetPresentConfigByIdBackendResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigByIdBackend", ctx, in)
	ret0, _ := ret[0].(*GetPresentConfigByIdBackendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigByIdBackend indicates an expected call of GetPresentConfigByIdBackend.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentConfigByIdBackend(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigByIdBackend", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentConfigByIdBackend), ctx, in)
}

// GetPresentConfigList mocks base method.
func (m *MockUserPresentGOServer) GetPresentConfigList(ctx context.Context, in *GetPresentConfigListReq) (*GetPresentConfigListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigList", ctx, in)
	ret0, _ := ret[0].(*GetPresentConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigList indicates an expected call of GetPresentConfigList.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentConfigList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigList", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentConfigList), ctx, in)
}

// GetPresentConfigListBackend mocks base method.
func (m *MockUserPresentGOServer) GetPresentConfigListBackend(ctx context.Context, in *GetPresentConfigListBackendReq) (*GetPresentConfigListBackendResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigListBackend", ctx, in)
	ret0, _ := ret[0].(*GetPresentConfigListBackendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigListBackend indicates an expected call of GetPresentConfigListBackend.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentConfigListBackend(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigListBackend", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentConfigListBackend), ctx, in)
}

// GetPresentConfigListByIdList mocks base method.
func (m *MockUserPresentGOServer) GetPresentConfigListByIdList(ctx context.Context, in *GetPresentConfigListByIdListReq) (*GetPresentConfigListByIdListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigListByIdList", ctx, in)
	ret0, _ := ret[0].(*GetPresentConfigListByIdListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigListByIdList indicates an expected call of GetPresentConfigListByIdList.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentConfigListByIdList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigListByIdList", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentConfigListByIdList), ctx, in)
}

// GetPresentConfigListV3 mocks base method.
func (m *MockUserPresentGOServer) GetPresentConfigListV3(ctx context.Context, in *GetPresentConfigListV3Req) (*GetPresentConfigListV3Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigListV3", ctx, in)
	ret0, _ := ret[0].(*GetPresentConfigListV3Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigListV3 indicates an expected call of GetPresentConfigListV3.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentConfigListV3(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigListV3", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentConfigListV3), ctx, in)
}

// GetPresentConfigUpdateTime mocks base method.
func (m *MockUserPresentGOServer) GetPresentConfigUpdateTime(ctx context.Context, in *GetPresentConfigUpdateTimeReq) (*GetPresentConfigUpdateTimeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigUpdateTime", ctx, in)
	ret0, _ := ret[0].(*GetPresentConfigUpdateTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigUpdateTime indicates an expected call of GetPresentConfigUpdateTime.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentConfigUpdateTime(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigUpdateTime", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentConfigUpdateTime), ctx, in)
}

// GetPresentDETConfigById mocks base method.
func (m *MockUserPresentGOServer) GetPresentDETConfigById(ctx context.Context, in *GetPresentDETConfigByIdReq) (*GetPresentDETConfigByIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentDETConfigById", ctx, in)
	ret0, _ := ret[0].(*GetPresentDETConfigByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentDETConfigById indicates an expected call of GetPresentDETConfigById.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentDETConfigById(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentDETConfigById", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentDETConfigById), ctx, in)
}

// GetPresentDynamicEffectTemplateConfig mocks base method.
func (m *MockUserPresentGOServer) GetPresentDynamicEffectTemplateConfig(ctx context.Context, in *GetPresentDynamicEffectTemplateConfigReq) (*GetPresentDynamicEffectTemplateConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentDynamicEffectTemplateConfig", ctx, in)
	ret0, _ := ret[0].(*GetPresentDynamicEffectTemplateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentDynamicEffectTemplateConfig indicates an expected call of GetPresentDynamicEffectTemplateConfig.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentDynamicEffectTemplateConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentDynamicEffectTemplateConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentDynamicEffectTemplateConfig), ctx, in)
}

// GetPresentDynamicTemplateConfUpdateTime mocks base method.
func (m *MockUserPresentGOServer) GetPresentDynamicTemplateConfUpdateTime(ctx context.Context, in *GetPresentDynamicTemplateConfUpdateTimeReq) (*GetPresentDynamicTemplateConfUpdateTimeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentDynamicTemplateConfUpdateTime", ctx, in)
	ret0, _ := ret[0].(*GetPresentDynamicTemplateConfUpdateTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentDynamicTemplateConfUpdateTime indicates an expected call of GetPresentDynamicTemplateConfUpdateTime.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentDynamicTemplateConfUpdateTime(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentDynamicTemplateConfUpdateTime", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentDynamicTemplateConfUpdateTime), ctx, in)
}

// GetPresentEffectTemplateConfigList mocks base method.
func (m *MockUserPresentGOServer) GetPresentEffectTemplateConfigList(ctx context.Context, in *GetPresentEffectTemplateConfigListReq) (*GetPresentEffectTemplateConfigListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentEffectTemplateConfigList", ctx, in)
	ret0, _ := ret[0].(*GetPresentEffectTemplateConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentEffectTemplateConfigList indicates an expected call of GetPresentEffectTemplateConfigList.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentEffectTemplateConfigList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentEffectTemplateConfigList", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentEffectTemplateConfigList), ctx, in)
}

// GetPresentFlowConfigById mocks base method.
func (m *MockUserPresentGOServer) GetPresentFlowConfigById(ctx context.Context, in *GetPresentFlowConfigByIdReq) (*GetPresentFlowConfigByIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentFlowConfigById", ctx, in)
	ret0, _ := ret[0].(*GetPresentFlowConfigByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentFlowConfigById indicates an expected call of GetPresentFlowConfigById.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentFlowConfigById(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigById", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentFlowConfigById), ctx, in)
}

// GetPresentFlowConfigList mocks base method.
func (m *MockUserPresentGOServer) GetPresentFlowConfigList(ctx context.Context, in *GetPresentFlowConfigListReq) (*GetPresentFlowConfigListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentFlowConfigList", ctx, in)
	ret0, _ := ret[0].(*GetPresentFlowConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentFlowConfigList indicates an expected call of GetPresentFlowConfigList.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentFlowConfigList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigList", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentFlowConfigList), ctx, in)
}

// GetPresentFlowConfigUpdateTime mocks base method.
func (m *MockUserPresentGOServer) GetPresentFlowConfigUpdateTime(ctx context.Context, in *GetPresentFlowConfigUpdateTimeReq) (*GetPresentFlowConfigUpdateTimeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentFlowConfigUpdateTime", ctx, in)
	ret0, _ := ret[0].(*GetPresentFlowConfigUpdateTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentFlowConfigUpdateTime indicates an expected call of GetPresentFlowConfigUpdateTime.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentFlowConfigUpdateTime(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigUpdateTime", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentFlowConfigUpdateTime), ctx, in)
}

// GetPresentMarkConfigList mocks base method.
func (m *MockUserPresentGOServer) GetPresentMarkConfigList(ctx context.Context, in *GetPresentMarkConfigListReq) (*GetPresentMarkConfigListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentMarkConfigList", ctx, in)
	ret0, _ := ret[0].(*GetPresentMarkConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentMarkConfigList indicates an expected call of GetPresentMarkConfigList.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentMarkConfigList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentMarkConfigList", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentMarkConfigList), ctx, in)
}

// GetPresentMarkIconByPresentId mocks base method.
func (m *MockUserPresentGOServer) GetPresentMarkIconByPresentId(ctx context.Context, in *GetPresentMarkIconByPresentIdReq) (*GetPresentMarkIconByPresentIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentMarkIconByPresentId", ctx, in)
	ret0, _ := ret[0].(*GetPresentMarkIconByPresentIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentMarkIconByPresentId indicates an expected call of GetPresentMarkIconByPresentId.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentMarkIconByPresentId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentMarkIconByPresentId", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentMarkIconByPresentId), ctx, in)
}

// GetPresentOrderStatus mocks base method.
func (m *MockUserPresentGOServer) GetPresentOrderStatus(ctx context.Context, in *GetPresentOrderStatusReq) (*GetPresentOrderStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentOrderStatus", ctx, in)
	ret0, _ := ret[0].(*GetPresentOrderStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentOrderStatus indicates an expected call of GetPresentOrderStatus.
func (mr *MockUserPresentGOServerMockRecorder) GetPresentOrderStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentOrderStatus", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetPresentOrderStatus), ctx, in)
}

// GetScenePresentDetailList mocks base method.
func (m *MockUserPresentGOServer) GetScenePresentDetailList(ctx context.Context, in *GetScenePresentDetailListReq) (*GetScenePresentDetailListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScenePresentDetailList", ctx, in)
	ret0, _ := ret[0].(*GetScenePresentDetailListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScenePresentDetailList indicates an expected call of GetScenePresentDetailList.
func (mr *MockUserPresentGOServerMockRecorder) GetScenePresentDetailList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScenePresentDetailList", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetScenePresentDetailList), ctx, in)
}

// GetScenePresentSummary mocks base method.
func (m *MockUserPresentGOServer) GetScenePresentSummary(ctx context.Context, in *GetScenePresentSummaryReq) (*GetScenePresentSummaryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScenePresentSummary", ctx, in)
	ret0, _ := ret[0].(*GetScenePresentSummaryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScenePresentSummary indicates an expected call of GetScenePresentSummary.
func (mr *MockUserPresentGOServerMockRecorder) GetScenePresentSummary(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScenePresentSummary", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetScenePresentSummary), ctx, in)
}

// GetUserPresentDetailList mocks base method.
func (m *MockUserPresentGOServer) GetUserPresentDetailList(ctx context.Context, in *GetUserPresentDetailListReq) (*GetUserPresentDetailListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentDetailList", ctx, in)
	ret0, _ := ret[0].(*GetUserPresentDetailListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentDetailList indicates an expected call of GetUserPresentDetailList.
func (mr *MockUserPresentGOServerMockRecorder) GetUserPresentDetailList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentDetailList", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetUserPresentDetailList), ctx, in)
}

// GetUserPresentReceive mocks base method.
func (m *MockUserPresentGOServer) GetUserPresentReceive(ctx context.Context, in *GetUserPresentReceiveReq) (*GetUserPresentReceiveResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentReceive", ctx, in)
	ret0, _ := ret[0].(*GetUserPresentReceiveResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentReceive indicates an expected call of GetUserPresentReceive.
func (mr *MockUserPresentGOServerMockRecorder) GetUserPresentReceive(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentReceive", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetUserPresentReceive), ctx, in)
}

// GetUserPresentSend mocks base method.
func (m *MockUserPresentGOServer) GetUserPresentSend(ctx context.Context, in *GetUserPresentSendReq) (*GetUserPresentSendResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentSend", ctx, in)
	ret0, _ := ret[0].(*GetUserPresentSendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSend indicates an expected call of GetUserPresentSend.
func (mr *MockUserPresentGOServerMockRecorder) GetUserPresentSend(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSend", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetUserPresentSend), ctx, in)
}

// GetUserPresentSendDetailList mocks base method.
func (m *MockUserPresentGOServer) GetUserPresentSendDetailList(ctx context.Context, in *GetUserPresentSendDetailListReq) (*GetUserPresentSendDetailListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentSendDetailList", ctx, in)
	ret0, _ := ret[0].(*GetUserPresentSendDetailListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSendDetailList indicates an expected call of GetUserPresentSendDetailList.
func (mr *MockUserPresentGOServerMockRecorder) GetUserPresentSendDetailList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSendDetailList", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetUserPresentSendDetailList), ctx, in)
}

// GetUserPresentSummary mocks base method.
func (m *MockUserPresentGOServer) GetUserPresentSummary(ctx context.Context, in *GetUserPresentSummaryReq) (*GetUserPresentSummaryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentSummary", ctx, in)
	ret0, _ := ret[0].(*GetUserPresentSummaryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSummary indicates an expected call of GetUserPresentSummary.
func (mr *MockUserPresentGOServerMockRecorder) GetUserPresentSummary(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSummary", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetUserPresentSummary), ctx, in)
}

// GetUserPresentSummaryByItemList mocks base method.
func (m *MockUserPresentGOServer) GetUserPresentSummaryByItemList(ctx context.Context, in *GetUserPresentSummaryByItemListReq) (*GetUserPresentSummaryByItemListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentSummaryByItemList", ctx, in)
	ret0, _ := ret[0].(*GetUserPresentSummaryByItemListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSummaryByItemList indicates an expected call of GetUserPresentSummaryByItemList.
func (mr *MockUserPresentGOServerMockRecorder) GetUserPresentSummaryByItemList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSummaryByItemList", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetUserPresentSummaryByItemList), ctx, in)
}

// GetValidNamingPresentInfos mocks base method.
func (m *MockUserPresentGOServer) GetValidNamingPresentInfos(ctx context.Context, in *GetValidNamingPresentInfosReq) (*GetValidNamingPresentInfosResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetValidNamingPresentInfos", ctx, in)
	ret0, _ := ret[0].(*GetValidNamingPresentInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetValidNamingPresentInfos indicates an expected call of GetValidNamingPresentInfos.
func (mr *MockUserPresentGOServerMockRecorder) GetValidNamingPresentInfos(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetValidNamingPresentInfos", reflect.TypeOf((*MockUserPresentGOServer)(nil).GetValidNamingPresentInfos), ctx, in)
}

// LinkItemToActivity mocks base method.
func (m *MockUserPresentGOServer) LinkItemToActivity(ctx context.Context, in *LinkItemToActivityReq) (*LinkItemToActivityResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LinkItemToActivity", ctx, in)
	ret0, _ := ret[0].(*LinkItemToActivityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LinkItemToActivity indicates an expected call of LinkItemToActivity.
func (mr *MockUserPresentGOServerMockRecorder) LinkItemToActivity(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkItemToActivity", reflect.TypeOf((*MockUserPresentGOServer)(nil).LinkItemToActivity), ctx, in)
}

// PrepareSendPresentToAi mocks base method.
func (m *MockUserPresentGOServer) PrepareSendPresentToAi(ctx context.Context, in *SendPresentToAiReq) (*SendPresentToAiResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrepareSendPresentToAi", ctx, in)
	ret0, _ := ret[0].(*SendPresentToAiResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PrepareSendPresentToAi indicates an expected call of PrepareSendPresentToAi.
func (mr *MockUserPresentGOServerMockRecorder) PrepareSendPresentToAi(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrepareSendPresentToAi", reflect.TypeOf((*MockUserPresentGOServer)(nil).PrepareSendPresentToAi), ctx, in)
}

// RecordSceneSendPresent mocks base method.
func (m *MockUserPresentGOServer) RecordSceneSendPresent(ctx context.Context, in *RecordSceneSendPresentReq) (*RecordSceneSendPresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordSceneSendPresent", ctx, in)
	ret0, _ := ret[0].(*RecordSceneSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordSceneSendPresent indicates an expected call of RecordSceneSendPresent.
func (mr *MockUserPresentGOServerMockRecorder) RecordSceneSendPresent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordSceneSendPresent", reflect.TypeOf((*MockUserPresentGOServer)(nil).RecordSceneSendPresent), ctx, in)
}

// ReissueAiPresentOrder mocks base method.
func (m *MockUserPresentGOServer) ReissueAiPresentOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReissueAiPresentOrder", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReissueAiPresentOrder indicates an expected call of ReissueAiPresentOrder.
func (mr *MockUserPresentGOServerMockRecorder) ReissueAiPresentOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReissueAiPresentOrder", reflect.TypeOf((*MockUserPresentGOServer)(nil).ReissueAiPresentOrder), ctx, in)
}

// SendPresent mocks base method.
func (m *MockUserPresentGOServer) SendPresent(ctx context.Context, in *SendPresentReq) (*SendPresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendPresent", ctx, in)
	ret0, _ := ret[0].(*SendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPresent indicates an expected call of SendPresent.
func (mr *MockUserPresentGOServerMockRecorder) SendPresent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPresent", reflect.TypeOf((*MockUserPresentGOServer)(nil).SendPresent), ctx, in)
}

// SendPresentToAi mocks base method.
func (m *MockUserPresentGOServer) SendPresentToAi(ctx context.Context, in *SendPresentToAiReq) (*SendPresentToAiResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendPresentToAi", ctx, in)
	ret0, _ := ret[0].(*SendPresentToAiResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPresentToAi indicates an expected call of SendPresentToAi.
func (mr *MockUserPresentGOServerMockRecorder) SendPresentToAi(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPresentToAi", reflect.TypeOf((*MockUserPresentGOServer)(nil).SendPresentToAi), ctx, in)
}

// UpdateDynamicEffectTemplate mocks base method.
func (m *MockUserPresentGOServer) UpdateDynamicEffectTemplate(ctx context.Context, in *UpdateDynamicEffectTemplateReq) (*UpdateDynamicEffectTemplateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDynamicEffectTemplate", ctx, in)
	ret0, _ := ret[0].(*UpdateDynamicEffectTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateDynamicEffectTemplate indicates an expected call of UpdateDynamicEffectTemplate.
func (mr *MockUserPresentGOServerMockRecorder) UpdateDynamicEffectTemplate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDynamicEffectTemplate", reflect.TypeOf((*MockUserPresentGOServer)(nil).UpdateDynamicEffectTemplate), ctx, in)
}

// UpdateNamingPresentInfo mocks base method.
func (m *MockUserPresentGOServer) UpdateNamingPresentInfo(ctx context.Context, in *UpdateNamingPresentInfoReq) (*UpdateNamingPresentInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNamingPresentInfo", ctx, in)
	ret0, _ := ret[0].(*UpdateNamingPresentInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateNamingPresentInfo indicates an expected call of UpdateNamingPresentInfo.
func (mr *MockUserPresentGOServerMockRecorder) UpdateNamingPresentInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNamingPresentInfo", reflect.TypeOf((*MockUserPresentGOServer)(nil).UpdateNamingPresentInfo), ctx, in)
}

// UpdatePresentActivityConfig mocks base method.
func (m *MockUserPresentGOServer) UpdatePresentActivityConfig(ctx context.Context, in *UpdatePresentActivityConfigReq) (*UpdatePresentActivityConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresentActivityConfig", ctx, in)
	ret0, _ := ret[0].(*UpdatePresentActivityConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentActivityConfig indicates an expected call of UpdatePresentActivityConfig.
func (mr *MockUserPresentGOServerMockRecorder) UpdatePresentActivityConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentActivityConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).UpdatePresentActivityConfig), ctx, in)
}

// UpdatePresentConfig mocks base method.
func (m *MockUserPresentGOServer) UpdatePresentConfig(ctx context.Context, in *UpdatePresentConfigReq) (*UpdatePresentConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresentConfig", ctx, in)
	ret0, _ := ret[0].(*UpdatePresentConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentConfig indicates an expected call of UpdatePresentConfig.
func (mr *MockUserPresentGOServerMockRecorder) UpdatePresentConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).UpdatePresentConfig), ctx, in)
}

// UpdatePresentConfigBackend mocks base method.
func (m *MockUserPresentGOServer) UpdatePresentConfigBackend(ctx context.Context, in *UpdatePresentConfigBackendReq) (*UpdatePresentConfigBackendResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresentConfigBackend", ctx, in)
	ret0, _ := ret[0].(*UpdatePresentConfigBackendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentConfigBackend indicates an expected call of UpdatePresentConfigBackend.
func (mr *MockUserPresentGOServerMockRecorder) UpdatePresentConfigBackend(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentConfigBackend", reflect.TypeOf((*MockUserPresentGOServer)(nil).UpdatePresentConfigBackend), ctx, in)
}

// UpdatePresentEffectTemplateConfig mocks base method.
func (m *MockUserPresentGOServer) UpdatePresentEffectTemplateConfig(ctx context.Context, in *UpdatePresentEffectTemplateConfigReq) (*UpdatePresentEffectTemplateConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresentEffectTemplateConfig", ctx, in)
	ret0, _ := ret[0].(*UpdatePresentEffectTemplateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentEffectTemplateConfig indicates an expected call of UpdatePresentEffectTemplateConfig.
func (mr *MockUserPresentGOServerMockRecorder) UpdatePresentEffectTemplateConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentEffectTemplateConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).UpdatePresentEffectTemplateConfig), ctx, in)
}

// UpdatePresentFlowConfig mocks base method.
func (m *MockUserPresentGOServer) UpdatePresentFlowConfig(ctx context.Context, in *UpdatePresentFlowConfigReq) (*UpdatePresentFlowConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresentFlowConfig", ctx, in)
	ret0, _ := ret[0].(*UpdatePresentFlowConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentFlowConfig indicates an expected call of UpdatePresentFlowConfig.
func (mr *MockUserPresentGOServerMockRecorder) UpdatePresentFlowConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentFlowConfig", reflect.TypeOf((*MockUserPresentGOServer)(nil).UpdatePresentFlowConfig), ctx, in)
}
