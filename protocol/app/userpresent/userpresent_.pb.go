// Code generated by protoc-gen-gogo.
// source: userpresent/userpresent_.proto
// DO NOT EDIT!

/*
	Package userpresent is a generated protocol buffer package.

	It is generated from these files:
		userpresent/userpresent_.proto

	It has these top-level messages:
		PresentPoint
		PresentLine
		DrawPresentPicture
		SendPresentReq
		SendPresentResp
		IMSendPresentReq
		IMSendPresentResp
		PresentTargetUserInfo
		BatchSendPresentReq
		BatchSendPresentResp
		UserPresentDetail
		UserPresentSendDetail
		GetUserPresentInfoReq
		GetUserPresentInfoResp
		GetUserPresentDetailListReq
		GetUserPresentDetailListResp
		GetPresentConfigListReq
		GetPresentConfigListResp
		GetPresentConfigByIdReq
		GetPresentConfigByIdResp
		PresentSendItemInfo
		PresentSendMsg
		PresentBatchTargetInfo
		PresentBatchInfoMsg
		GetPresentFlowConfigListReq
		GetPresentFlowConfigListResp
		GetPresentFlowConfigByIdReq
		GetPresentFlowConfigByIdResp
		DrawPresentPara
		GetDrawPresentParaReq
		GetDrawPresentParaResp
		NamingPresentConfig
		GetNamingPresentConfigListReq
		GetNamingPresentConfigListResp
		GetPresentDynamicTemplateConfigReq
		GetPresentDynamicTemplateConfigResp
		GetImPresentItemIdListReq
		GetImPresentItemIdListResp
		GetStangerImItemIdListReq
		GetStangerImItemIdListResp
		FansPresentMessage
		PresentBoxInfo
		PresentBoxDetail
		UnpackPresentBoxReq
		UnpackPresentBoxResp
		PresentBoxOpenMsg
		BackpackIntimatePresentContractPush
*/
package userpresent

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import ga "golang.52tt.com/protocol/app"

import math3 "math"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import math4 "math"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 礼物来源类型
type PresentSourceType int32

const (
	PresentSourceType_PRESENT_SOURCE_BUY             PresentSourceType = 0
	PresentSourceType_PRESENT_SOURCE_PACKAGE         PresentSourceType = 1
	PresentSourceType_PRESENT_SOURCE_PACKAGE_FIRST   PresentSourceType = 2
	PresentSourceType_PRESENT_SOURCE_FELLOW          PresentSourceType = 3
	PresentSourceType_PRESENT_SOURCE_MAGIC           PresentSourceType = 4
	PresentSourceType_PRESENT_SOURCE_LOTTERY_BUY     PresentSourceType = 5
	PresentSourceType_PRESENT_SOURCE_LOTTERY_PACKAGE PresentSourceType = 6
	PresentSourceType_PRESENT_SOURCE_LOTTERY_MAGIC   PresentSourceType = 7
	PresentSourceType_PRESENT_SOURCE_OFFERING_ROOM   PresentSourceType = 8
	PresentSourceType_PRESENT_SOURCE_CHANNEL_GIFT_PK PresentSourceType = 9
	PresentSourceType_PRESENT_SOURCE_GRAB_CHAIR_GAME PresentSourceType = 10
)

var PresentSourceType_name = map[int32]string{
	0:  "PRESENT_SOURCE_BUY",
	1:  "PRESENT_SOURCE_PACKAGE",
	2:  "PRESENT_SOURCE_PACKAGE_FIRST",
	3:  "PRESENT_SOURCE_FELLOW",
	4:  "PRESENT_SOURCE_MAGIC",
	5:  "PRESENT_SOURCE_LOTTERY_BUY",
	6:  "PRESENT_SOURCE_LOTTERY_PACKAGE",
	7:  "PRESENT_SOURCE_LOTTERY_MAGIC",
	8:  "PRESENT_SOURCE_OFFERING_ROOM",
	9:  "PRESENT_SOURCE_CHANNEL_GIFT_PK",
	10: "PRESENT_SOURCE_GRAB_CHAIR_GAME",
}
var PresentSourceType_value = map[string]int32{
	"PRESENT_SOURCE_BUY":             0,
	"PRESENT_SOURCE_PACKAGE":         1,
	"PRESENT_SOURCE_PACKAGE_FIRST":   2,
	"PRESENT_SOURCE_FELLOW":          3,
	"PRESENT_SOURCE_MAGIC":           4,
	"PRESENT_SOURCE_LOTTERY_BUY":     5,
	"PRESENT_SOURCE_LOTTERY_PACKAGE": 6,
	"PRESENT_SOURCE_LOTTERY_MAGIC":   7,
	"PRESENT_SOURCE_OFFERING_ROOM":   8,
	"PRESENT_SOURCE_CHANNEL_GIFT_PK": 9,
	"PRESENT_SOURCE_GRAB_CHAIR_GAME": 10,
}

func (x PresentSourceType) Enum() *PresentSourceType {
	p := new(PresentSourceType)
	*p = x
	return p
}
func (x PresentSourceType) String() string {
	return proto.EnumName(PresentSourceType_name, int32(x))
}
func (x *PresentSourceType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PresentSourceType_value, data, "PresentSourceType")
	if err != nil {
		return err
	}
	*x = PresentSourceType(value)
	return nil
}
func (PresentSourceType) EnumDescriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{0} }

// 批量送礼类型
type PresentBatchSendType int32

const (
	PresentBatchSendType_PRESENT_SOURCE_NONE     PresentBatchSendType = 0
	PresentBatchSendType_PRESENT_SOURCE_ALL_MIC  PresentBatchSendType = 1
	PresentBatchSendType_PRESENT_SOURCE_WITH_UID PresentBatchSendType = 2
)

var PresentBatchSendType_name = map[int32]string{
	0: "PRESENT_SOURCE_NONE",
	1: "PRESENT_SOURCE_ALL_MIC",
	2: "PRESENT_SOURCE_WITH_UID",
}
var PresentBatchSendType_value = map[string]int32{
	"PRESENT_SOURCE_NONE":     0,
	"PRESENT_SOURCE_ALL_MIC":  1,
	"PRESENT_SOURCE_WITH_UID": 2,
}

func (x PresentBatchSendType) Enum() *PresentBatchSendType {
	p := new(PresentBatchSendType)
	*p = x
	return p
}
func (x PresentBatchSendType) String() string {
	return proto.EnumName(PresentBatchSendType_name, int32(x))
}
func (x *PresentBatchSendType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PresentBatchSendType_value, data, "PresentBatchSendType")
	if err != nil {
		return err
	}
	*x = PresentBatchSendType(value)
	return nil
}
func (PresentBatchSendType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{1}
}

// 送礼类型
type PresentSendType int32

const (
	PresentSendType_PRESENT_SEND_NORMAL       PresentSendType = 0
	PresentSendType_PRESENT_SEND_DRAW         PresentSendType = 1
	PresentSendType_PRESENT_SENT_FANS_LOTTERY PresentSendType = 2
)

var PresentSendType_name = map[int32]string{
	0: "PRESENT_SEND_NORMAL",
	1: "PRESENT_SEND_DRAW",
	2: "PRESENT_SENT_FANS_LOTTERY",
}
var PresentSendType_value = map[string]int32{
	"PRESENT_SEND_NORMAL":       0,
	"PRESENT_SEND_DRAW":         1,
	"PRESENT_SENT_FANS_LOTTERY": 2,
}

func (x PresentSendType) Enum() *PresentSendType {
	p := new(PresentSendType)
	*p = x
	return p
}
func (x PresentSendType) String() string {
	return proto.EnumName(PresentSendType_name, int32(x))
}
func (x *PresentSendType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PresentSendType_value, data, "PresentSendType")
	if err != nil {
		return err
	}
	*x = PresentSendType(value)
	return nil
}
func (PresentSendType) EnumDescriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{2} }

// 送礼来源类型
type PresentSendSourceType int32

const (
	PresentSendSourceType_E_SEND_SOURCE_DEFLAUTE         PresentSendSourceType = 0
	PresentSendSourceType_E_SEND_SOURCE_GIFT_TURNTABLE   PresentSendSourceType = 1
	PresentSendSourceType_E_SEND_SOURCE_GIFT_SHELF       PresentSendSourceType = 2
	PresentSendSourceType_E_SEND_SOURCE_SPEECH_BALL      PresentSendSourceType = 3
	PresentSendSourceType_E_SEND_SOURCE_DRAW_GIFT        PresentSendSourceType = 4
	PresentSendSourceType_E_SEND_SOURCE_MASKED_CALL      PresentSendSourceType = 5
	PresentSendSourceType_E_SEND_SOURCE_IM               PresentSendSourceType = 6
	PresentSendSourceType_E_SEND_SOURCE_OFFICIAL_CHANNEL PresentSendSourceType = 7
	PresentSendSourceType_E_SEND_SOURCE_FELLOW           PresentSendSourceType = 8
	PresentSendSourceType_E_SEND_SOURCE_MAGIC            PresentSendSourceType = 9
	PresentSendSourceType_E_SEND_SOURCE_LOTTERY          PresentSendSourceType = 10
	PresentSendSourceType_E_SEND_SOURCE_OFFERING_ROOM    PresentSendSourceType = 11
	PresentSendSourceType_E_SEND_SOURCE_CHANNEL_GIFT_PK  PresentSendSourceType = 12
	PresentSendSourceType_E_SEND_SOURCE_CHANNEL_BIRTHDAY PresentSendSourceType = 13
)

var PresentSendSourceType_name = map[int32]string{
	0:  "E_SEND_SOURCE_DEFLAUTE",
	1:  "E_SEND_SOURCE_GIFT_TURNTABLE",
	2:  "E_SEND_SOURCE_GIFT_SHELF",
	3:  "E_SEND_SOURCE_SPEECH_BALL",
	4:  "E_SEND_SOURCE_DRAW_GIFT",
	5:  "E_SEND_SOURCE_MASKED_CALL",
	6:  "E_SEND_SOURCE_IM",
	7:  "E_SEND_SOURCE_OFFICIAL_CHANNEL",
	8:  "E_SEND_SOURCE_FELLOW",
	9:  "E_SEND_SOURCE_MAGIC",
	10: "E_SEND_SOURCE_LOTTERY",
	11: "E_SEND_SOURCE_OFFERING_ROOM",
	12: "E_SEND_SOURCE_CHANNEL_GIFT_PK",
	13: "E_SEND_SOURCE_CHANNEL_BIRTHDAY",
}
var PresentSendSourceType_value = map[string]int32{
	"E_SEND_SOURCE_DEFLAUTE":         0,
	"E_SEND_SOURCE_GIFT_TURNTABLE":   1,
	"E_SEND_SOURCE_GIFT_SHELF":       2,
	"E_SEND_SOURCE_SPEECH_BALL":      3,
	"E_SEND_SOURCE_DRAW_GIFT":        4,
	"E_SEND_SOURCE_MASKED_CALL":      5,
	"E_SEND_SOURCE_IM":               6,
	"E_SEND_SOURCE_OFFICIAL_CHANNEL": 7,
	"E_SEND_SOURCE_FELLOW":           8,
	"E_SEND_SOURCE_MAGIC":            9,
	"E_SEND_SOURCE_LOTTERY":          10,
	"E_SEND_SOURCE_OFFERING_ROOM":    11,
	"E_SEND_SOURCE_CHANNEL_GIFT_PK":  12,
	"E_SEND_SOURCE_CHANNEL_BIRTHDAY": 13,
}

func (x PresentSendSourceType) Enum() *PresentSendSourceType {
	p := new(PresentSendSourceType)
	*p = x
	return p
}
func (x PresentSendSourceType) String() string {
	return proto.EnumName(PresentSendSourceType_name, int32(x))
}
func (x *PresentSendSourceType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PresentSendSourceType_value, data, "PresentSendSourceType")
	if err != nil {
		return err
	}
	*x = PresentSendSourceType(value)
	return nil
}
func (PresentSendSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{3}
}

// 送礼方式
type PresentSendMethodType int32

const (
	PresentSendMethodType_PRESENT_TYPE_ROOM   PresentSendMethodType = 0
	PresentSendMethodType_PRESENT_TYPE_IM     PresentSendMethodType = 1
	PresentSendMethodType_PRESENT_TYPE_FELLOW PresentSendMethodType = 2
)

var PresentSendMethodType_name = map[int32]string{
	0: "PRESENT_TYPE_ROOM",
	1: "PRESENT_TYPE_IM",
	2: "PRESENT_TYPE_FELLOW",
}
var PresentSendMethodType_value = map[string]int32{
	"PRESENT_TYPE_ROOM":   0,
	"PRESENT_TYPE_IM":     1,
	"PRESENT_TYPE_FELLOW": 2,
}

func (x PresentSendMethodType) Enum() *PresentSendMethodType {
	p := new(PresentSendMethodType)
	*p = x
	return p
}
func (x PresentSendMethodType) String() string {
	return proto.EnumName(PresentSendMethodType_name, int32(x))
}
func (x *PresentSendMethodType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PresentSendMethodType_value, data, "PresentSendMethodType")
	if err != nil {
		return err
	}
	*x = PresentSendMethodType(value)
	return nil
}
func (PresentSendMethodType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{4}
}

type PresentTextType int32

const (
	PresentTextType_E_TEXT_TYPE_DEFAULT  PresentTextType = 0
	PresentTextType_E_TEXT_TYPE_INTIMACY PresentTextType = 1
)

var PresentTextType_name = map[int32]string{
	0: "E_TEXT_TYPE_DEFAULT",
	1: "E_TEXT_TYPE_INTIMACY",
}
var PresentTextType_value = map[string]int32{
	"E_TEXT_TYPE_DEFAULT":  0,
	"E_TEXT_TYPE_INTIMACY": 1,
}

func (x PresentTextType) Enum() *PresentTextType {
	p := new(PresentTextType)
	*p = x
	return p
}
func (x PresentTextType) String() string {
	return proto.EnumName(PresentTextType_name, int32(x))
}
func (x *PresentTextType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PresentTextType_value, data, "PresentTextType")
	if err != nil {
		return err
	}
	*x = PresentTextType(value)
	return nil
}
func (PresentTextType) EnumDescriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{5} }

type SendPresentTargetType int32

const (
	SendPresentTargetType_PRESENT_TARGET_TYPE_USER    SendPresentTargetType = 0
	SendPresentTargetType_PRESENT_TARGET_TYPE_AI_ROLE SendPresentTargetType = 1
)

var SendPresentTargetType_name = map[int32]string{
	0: "PRESENT_TARGET_TYPE_USER",
	1: "PRESENT_TARGET_TYPE_AI_ROLE",
}
var SendPresentTargetType_value = map[string]int32{
	"PRESENT_TARGET_TYPE_USER":    0,
	"PRESENT_TARGET_TYPE_AI_ROLE": 1,
}

func (x SendPresentTargetType) Enum() *SendPresentTargetType {
	p := new(SendPresentTargetType)
	*p = x
	return p
}
func (x SendPresentTargetType) String() string {
	return proto.EnumName(SendPresentTargetType_name, int32(x))
}
func (x *SendPresentTargetType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SendPresentTargetType_value, data, "SendPresentTargetType")
	if err != nil {
		return err
	}
	*x = SendPresentTargetType(value)
	return nil
}
func (SendPresentTargetType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{6}
}

type PresentBusinessType int32

const (
	PresentBusinessType_E_PRESENT_BUSINESS_TYPE_DEFAULT          PresentBusinessType = 0
	PresentBusinessType_E_PRESENT_BUSINESS_TYPE_INTIMATE_PRESENT PresentBusinessType = 1
)

var PresentBusinessType_name = map[int32]string{
	0: "E_PRESENT_BUSINESS_TYPE_DEFAULT",
	1: "E_PRESENT_BUSINESS_TYPE_INTIMATE_PRESENT",
}
var PresentBusinessType_value = map[string]int32{
	"E_PRESENT_BUSINESS_TYPE_DEFAULT":          0,
	"E_PRESENT_BUSINESS_TYPE_INTIMATE_PRESENT": 1,
}

func (x PresentBusinessType) Enum() *PresentBusinessType {
	p := new(PresentBusinessType)
	*p = x
	return p
}
func (x PresentBusinessType) String() string {
	return proto.EnumName(PresentBusinessType_name, int32(x))
}
func (x *PresentBusinessType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PresentBusinessType_value, data, "PresentBusinessType")
	if err != nil {
		return err
	}
	*x = PresentBusinessType(value)
	return nil
}
func (PresentBusinessType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{7}
}

// 要获取的明细类型
type PresentDetailType int32

const (
	PresentDetailType_E_DETAIL_PRESENT_RECEIVE PresentDetailType = 0
	PresentDetailType_E_DETAIL_PRESENT_SEND    PresentDetailType = 1
	PresentDetailType_E_DETAIL_PRESENT_ALL     PresentDetailType = 2
)

var PresentDetailType_name = map[int32]string{
	0: "E_DETAIL_PRESENT_RECEIVE",
	1: "E_DETAIL_PRESENT_SEND",
	2: "E_DETAIL_PRESENT_ALL",
}
var PresentDetailType_value = map[string]int32{
	"E_DETAIL_PRESENT_RECEIVE": 0,
	"E_DETAIL_PRESENT_SEND":    1,
	"E_DETAIL_PRESENT_ALL":     2,
}

func (x PresentDetailType) Enum() *PresentDetailType {
	p := new(PresentDetailType)
	*p = x
	return p
}
func (x PresentDetailType) String() string {
	return proto.EnumName(PresentDetailType_name, int32(x))
}
func (x *PresentDetailType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PresentDetailType_value, data, "PresentDetailType")
	if err != nil {
		return err
	}
	*x = PresentDetailType(value)
	return nil
}
func (PresentDetailType) EnumDescriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{8} }

type PresentPoint struct {
	X float32 `protobuf:"fixed32,1,req,name=x" json:"x"`
	Y float32 `protobuf:"fixed32,2,req,name=y" json:"y"`
}

func (m *PresentPoint) Reset()                    { *m = PresentPoint{} }
func (m *PresentPoint) String() string            { return proto.CompactTextString(m) }
func (*PresentPoint) ProtoMessage()               {}
func (*PresentPoint) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{0} }

func (m *PresentPoint) GetX() float32 {
	if m != nil {
		return m.X
	}
	return 0
}

func (m *PresentPoint) GetY() float32 {
	if m != nil {
		return m.Y
	}
	return 0
}

type PresentLine struct {
	ItemId    uint32          `protobuf:"varint,1,req,name=item_id,json=itemId" json:"item_id"`
	PointList []*PresentPoint `protobuf:"bytes,2,rep,name=point_list,json=pointList" json:"point_list,omitempty"`
}

func (m *PresentLine) Reset()                    { *m = PresentLine{} }
func (m *PresentLine) String() string            { return proto.CompactTextString(m) }
func (*PresentLine) ProtoMessage()               {}
func (*PresentLine) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{1} }

func (m *PresentLine) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentLine) GetPointList() []*PresentPoint {
	if m != nil {
		return m.PointList
	}
	return nil
}

// 涂鸦礼物图
type DrawPresentPicture struct {
	LineList []*PresentLine `protobuf:"bytes,1,rep,name=line_list,json=lineList" json:"line_list,omitempty"`
}

func (m *DrawPresentPicture) Reset()                    { *m = DrawPresentPicture{} }
func (m *DrawPresentPicture) String() string            { return proto.CompactTextString(m) }
func (*DrawPresentPicture) ProtoMessage()               {}
func (*DrawPresentPicture) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{2} }

func (m *DrawPresentPicture) GetLineList() []*PresentLine {
	if m != nil {
		return m.LineList
	}
	return nil
}

// 赠送礼物
type SendPresentReq struct {
	BaseReq          *ga.BaseReq         `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetUid        uint32              `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
	ItemId           uint32              `protobuf:"varint,3,req,name=item_id,json=itemId" json:"item_id"`
	ChannelId        uint32              `protobuf:"varint,4,opt,name=channel_id,json=channelId" json:"channel_id"`
	ConfigUpdateTime uint32              `protobuf:"varint,5,req,name=config_update_time,json=configUpdateTime" json:"config_update_time"`
	Count            uint32              `protobuf:"varint,6,opt,name=count" json:"count"`
	SendSource       uint32              `protobuf:"varint,7,opt,name=send_source,json=sendSource" json:"send_source"`
	ItemSource       uint32              `protobuf:"varint,8,opt,name=item_source,json=itemSource" json:"item_source"`
	SourceId         uint32              `protobuf:"varint,9,opt,name=source_id,json=sourceId" json:"source_id"`
	SendType         uint32              `protobuf:"varint,10,opt,name=send_type,json=sendType" json:"send_type"`
	DrawPresentPic   *DrawPresentPicture `protobuf:"bytes,11,opt,name=draw_present_pic,json=drawPresentPic" json:"draw_present_pic,omitempty"`
}

func (m *SendPresentReq) Reset()                    { *m = SendPresentReq{} }
func (m *SendPresentReq) String() string            { return proto.CompactTextString(m) }
func (*SendPresentReq) ProtoMessage()               {}
func (*SendPresentReq) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{3} }

func (m *SendPresentReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendPresentReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SendPresentReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *SendPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SendPresentReq) GetConfigUpdateTime() uint32 {
	if m != nil {
		return m.ConfigUpdateTime
	}
	return 0
}

func (m *SendPresentReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *SendPresentReq) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *SendPresentReq) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *SendPresentReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *SendPresentReq) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *SendPresentReq) GetDrawPresentPic() *DrawPresentPicture {
	if m != nil {
		return m.DrawPresentPic
	}
	return nil
}

type SendPresentResp struct {
	BaseResp                *ga.BaseResp      `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ItemId                  uint32            `protobuf:"varint,2,req,name=item_id,json=itemId" json:"item_id"`
	MsgInfo                 *PresentSendMsg   `protobuf:"bytes,3,opt,name=msg_info,json=msgInfo" json:"msg_info,omitempty"`
	MemberContributionAdded uint32            `protobuf:"varint,4,opt,name=member_contribution_added,json=memberContributionAdded" json:"member_contribution_added"`
	Count                   uint32            `protobuf:"varint,5,opt,name=count" json:"count"`
	CurTbeans               uint64            `protobuf:"varint,6,opt,name=cur_tbeans,json=curTbeans" json:"cur_tbeans"`
	ItemSource              uint32            `protobuf:"varint,7,opt,name=item_source,json=itemSource" json:"item_source"`
	SourceId                uint32            `protobuf:"varint,8,opt,name=source_id,json=sourceId" json:"source_id"`
	SourceRemain            uint32            `protobuf:"varint,9,opt,name=source_remain,json=sourceRemain" json:"source_remain"`
	ExpireTime              uint32            `protobuf:"varint,10,opt,name=expire_time,json=expireTime" json:"expire_time"`
	BoxDetail               *PresentBoxDetail `protobuf:"bytes,11,opt,name=box_detail,json=boxDetail" json:"box_detail,omitempty"`
}

func (m *SendPresentResp) Reset()                    { *m = SendPresentResp{} }
func (m *SendPresentResp) String() string            { return proto.CompactTextString(m) }
func (*SendPresentResp) ProtoMessage()               {}
func (*SendPresentResp) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{4} }

func (m *SendPresentResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SendPresentResp) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *SendPresentResp) GetMsgInfo() *PresentSendMsg {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

func (m *SendPresentResp) GetMemberContributionAdded() uint32 {
	if m != nil {
		return m.MemberContributionAdded
	}
	return 0
}

func (m *SendPresentResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *SendPresentResp) GetCurTbeans() uint64 {
	if m != nil {
		return m.CurTbeans
	}
	return 0
}

func (m *SendPresentResp) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *SendPresentResp) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *SendPresentResp) GetSourceRemain() uint32 {
	if m != nil {
		return m.SourceRemain
	}
	return 0
}

func (m *SendPresentResp) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *SendPresentResp) GetBoxDetail() *PresentBoxDetail {
	if m != nil {
		return m.BoxDetail
	}
	return nil
}

// IM赠送礼物
type IMSendPresentReq struct {
	BaseReq         *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetUid       uint32      `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
	ItemId          uint32      `protobuf:"varint,3,req,name=item_id,json=itemId" json:"item_id"`
	Count           uint32      `protobuf:"varint,4,opt,name=count" json:"count"`
	SendSource      uint32      `protobuf:"varint,5,opt,name=send_source,json=sendSource" json:"send_source"`
	ItemSource      uint32      `protobuf:"varint,6,opt,name=item_source,json=itemSource" json:"item_source"`
	SourceId        uint32      `protobuf:"varint,7,opt,name=source_id,json=sourceId" json:"source_id"`
	SendType        uint32      `protobuf:"varint,8,opt,name=send_type,json=sendType" json:"send_type"`
	PresentTextType uint32      `protobuf:"varint,9,opt,name=present_text_type,json=presentTextType" json:"present_text_type"`
	PreEffectText   string      `protobuf:"bytes,10,opt,name=pre_effect_text,json=preEffectText" json:"pre_effect_text"`
	TargetUidList   []uint32    `protobuf:"varint,11,rep,name=target_uid_list,json=targetUidList" json:"target_uid_list,omitempty"`
	BusinessType    uint32      `protobuf:"varint,12,opt,name=business_type,json=businessType" json:"business_type"`
	TargetType      uint32      `protobuf:"varint,13,opt,name=target_type,json=targetType" json:"target_type"`
	RoleId          uint32      `protobuf:"varint,14,opt,name=role_id,json=roleId" json:"role_id"`
	PartnerId       uint32      `protobuf:"varint,15,opt,name=partner_id,json=partnerId" json:"partner_id"`
}

func (m *IMSendPresentReq) Reset()                    { *m = IMSendPresentReq{} }
func (m *IMSendPresentReq) String() string            { return proto.CompactTextString(m) }
func (*IMSendPresentReq) ProtoMessage()               {}
func (*IMSendPresentReq) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{5} }

func (m *IMSendPresentReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *IMSendPresentReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *IMSendPresentReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *IMSendPresentReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *IMSendPresentReq) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *IMSendPresentReq) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *IMSendPresentReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *IMSendPresentReq) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *IMSendPresentReq) GetPresentTextType() uint32 {
	if m != nil {
		return m.PresentTextType
	}
	return 0
}

func (m *IMSendPresentReq) GetPreEffectText() string {
	if m != nil {
		return m.PreEffectText
	}
	return ""
}

func (m *IMSendPresentReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

func (m *IMSendPresentReq) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *IMSendPresentReq) GetTargetType() uint32 {
	if m != nil {
		return m.TargetType
	}
	return 0
}

func (m *IMSendPresentReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *IMSendPresentReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type IMSendPresentResp struct {
	BaseResp                *ga.BaseResp    `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ItemId                  uint32          `protobuf:"varint,2,req,name=item_id,json=itemId" json:"item_id"`
	MsgInfo                 *PresentSendMsg `protobuf:"bytes,3,opt,name=msg_info,json=msgInfo" json:"msg_info,omitempty"`
	MemberContributionAdded uint32          `protobuf:"varint,4,opt,name=member_contribution_added,json=memberContributionAdded" json:"member_contribution_added"`
	Count                   uint32          `protobuf:"varint,5,opt,name=count" json:"count"`
	CurTbeans               uint64          `protobuf:"varint,6,opt,name=cur_tbeans,json=curTbeans" json:"cur_tbeans"`
	ItemSource              uint32          `protobuf:"varint,7,opt,name=item_source,json=itemSource" json:"item_source"`
	SourceId                uint32          `protobuf:"varint,8,opt,name=source_id,json=sourceId" json:"source_id"`
	SourceRemain            uint32          `protobuf:"varint,9,opt,name=source_remain,json=sourceRemain" json:"source_remain"`
	ExpireTime              uint32          `protobuf:"varint,10,opt,name=expire_time,json=expireTime" json:"expire_time"`
}

func (m *IMSendPresentResp) Reset()                    { *m = IMSendPresentResp{} }
func (m *IMSendPresentResp) String() string            { return proto.CompactTextString(m) }
func (*IMSendPresentResp) ProtoMessage()               {}
func (*IMSendPresentResp) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{6} }

func (m *IMSendPresentResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *IMSendPresentResp) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *IMSendPresentResp) GetMsgInfo() *PresentSendMsg {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

func (m *IMSendPresentResp) GetMemberContributionAdded() uint32 {
	if m != nil {
		return m.MemberContributionAdded
	}
	return 0
}

func (m *IMSendPresentResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *IMSendPresentResp) GetCurTbeans() uint64 {
	if m != nil {
		return m.CurTbeans
	}
	return 0
}

func (m *IMSendPresentResp) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *IMSendPresentResp) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *IMSendPresentResp) GetSourceRemain() uint32 {
	if m != nil {
		return m.SourceRemain
	}
	return 0
}

func (m *IMSendPresentResp) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type PresentTargetUserInfo struct {
	Uid         uint32          `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account     string          `protobuf:"bytes,2,req,name=account" json:"account"`
	Name        string          `protobuf:"bytes,3,req,name=name" json:"name"`
	UserProfile *ga.UserProfile `protobuf:"bytes,4,opt,name=user_profile,json=userProfile" json:"user_profile,omitempty"`
	CustomText  string          `protobuf:"bytes,5,opt,name=custom_text,json=customText" json:"custom_text"`
}

func (m *PresentTargetUserInfo) Reset()         { *m = PresentTargetUserInfo{} }
func (m *PresentTargetUserInfo) String() string { return proto.CompactTextString(m) }
func (*PresentTargetUserInfo) ProtoMessage()    {}
func (*PresentTargetUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{7}
}

func (m *PresentTargetUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PresentTargetUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *PresentTargetUserInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PresentTargetUserInfo) GetUserProfile() *ga.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *PresentTargetUserInfo) GetCustomText() string {
	if m != nil {
		return m.CustomText
	}
	return ""
}

// 批量赠送礼物
type BatchSendPresentReq struct {
	BaseReq        *ga.BaseReq         `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ItemId         uint32              `protobuf:"varint,2,req,name=item_id,json=itemId" json:"item_id"`
	ChannelId      uint32              `protobuf:"varint,3,opt,name=channel_id,json=channelId" json:"channel_id"`
	Count          uint32              `protobuf:"varint,4,req,name=count" json:"count"`
	SendSource     uint32              `protobuf:"varint,5,req,name=send_source,json=sendSource" json:"send_source"`
	ItemSource     uint32              `protobuf:"varint,6,req,name=item_source,json=itemSource" json:"item_source"`
	SourceId       uint32              `protobuf:"varint,7,opt,name=source_id,json=sourceId" json:"source_id"`
	BatchType      uint32              `protobuf:"varint,8,req,name=batch_type,json=batchType" json:"batch_type"`
	SendType       uint32              `protobuf:"varint,9,opt,name=send_type,json=sendType" json:"send_type"`
	DrawPresentPic *DrawPresentPicture `protobuf:"bytes,10,opt,name=draw_present_pic,json=drawPresentPic" json:"draw_present_pic,omitempty"`
	UidList        []uint32            `protobuf:"varint,11,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatchSendPresentReq) Reset()                    { *m = BatchSendPresentReq{} }
func (m *BatchSendPresentReq) String() string            { return proto.CompactTextString(m) }
func (*BatchSendPresentReq) ProtoMessage()               {}
func (*BatchSendPresentReq) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{8} }

func (m *BatchSendPresentReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatchSendPresentReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *BatchSendPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatchSendPresentReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *BatchSendPresentReq) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *BatchSendPresentReq) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *BatchSendPresentReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *BatchSendPresentReq) GetBatchType() uint32 {
	if m != nil {
		return m.BatchType
	}
	return 0
}

func (m *BatchSendPresentReq) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *BatchSendPresentReq) GetDrawPresentPic() *DrawPresentPicture {
	if m != nil {
		return m.DrawPresentPic
	}
	return nil
}

func (m *BatchSendPresentReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchSendPresentResp struct {
	BaseResp     *ga.BaseResp             `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	MsgInfo      *PresentBatchInfoMsg     `protobuf:"bytes,2,opt,name=msg_info,json=msgInfo" json:"msg_info,omitempty"`
	CurTbeans    uint64                   `protobuf:"varint,3,opt,name=cur_tbeans,json=curTbeans" json:"cur_tbeans"`
	ItemSource   uint32                   `protobuf:"varint,4,opt,name=item_source,json=itemSource" json:"item_source"`
	SourceId     uint32                   `protobuf:"varint,5,opt,name=source_id,json=sourceId" json:"source_id"`
	SourceRemain uint32                   `protobuf:"varint,6,opt,name=source_remain,json=sourceRemain" json:"source_remain"`
	TargetList   []*PresentTargetUserInfo `protobuf:"bytes,7,rep,name=target_list,json=targetList" json:"target_list,omitempty"`
	ItemInfo     *PresentSendItemInfo     `protobuf:"bytes,8,opt,name=item_info,json=itemInfo" json:"item_info,omitempty"`
	ExpireTime   uint32                   `protobuf:"varint,9,opt,name=expire_time,json=expireTime" json:"expire_time"`
}

func (m *BatchSendPresentResp) Reset()                    { *m = BatchSendPresentResp{} }
func (m *BatchSendPresentResp) String() string            { return proto.CompactTextString(m) }
func (*BatchSendPresentResp) ProtoMessage()               {}
func (*BatchSendPresentResp) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{9} }

func (m *BatchSendPresentResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BatchSendPresentResp) GetMsgInfo() *PresentBatchInfoMsg {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

func (m *BatchSendPresentResp) GetCurTbeans() uint64 {
	if m != nil {
		return m.CurTbeans
	}
	return 0
}

func (m *BatchSendPresentResp) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *BatchSendPresentResp) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *BatchSendPresentResp) GetSourceRemain() uint32 {
	if m != nil {
		return m.SourceRemain
	}
	return 0
}

func (m *BatchSendPresentResp) GetTargetList() []*PresentTargetUserInfo {
	if m != nil {
		return m.TargetList
	}
	return nil
}

func (m *BatchSendPresentResp) GetItemInfo() *PresentSendItemInfo {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

func (m *BatchSendPresentResp) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

// 用户收到的礼物明细
type UserPresentDetail struct {
	Uid             uint32                     `protobuf:"varint,1,req,name=uid" json:"uid"`
	FromUid         uint32                     `protobuf:"varint,2,req,name=from_uid,json=fromUid" json:"from_uid"`
	FromAccount     string                     `protobuf:"bytes,3,req,name=from_account,json=fromAccount" json:"from_account"`
	FromName        string                     `protobuf:"bytes,4,req,name=from_name,json=fromName" json:"from_name"`
	FromFaceMd5     string                     `protobuf:"bytes,5,req,name=from_face_md5,json=fromFaceMd5" json:"from_face_md5"`
	ItemBriefConfig *ga.PresentItemBriefConfig `protobuf:"bytes,6,req,name=item_brief_config,json=itemBriefConfig" json:"item_brief_config,omitempty"`
	SendTime        uint32                     `protobuf:"varint,7,req,name=send_time,json=sendTime" json:"send_time"`
	ItemCount       uint32                     `protobuf:"varint,8,opt,name=item_count,json=itemCount" json:"item_count"`
	ItemId          uint32                     `protobuf:"varint,9,opt,name=item_id,json=itemId" json:"item_id"`
	Charm           uint32                     `protobuf:"varint,10,opt,name=charm" json:"charm"`
	SendSource      uint32                     `protobuf:"varint,11,opt,name=send_source,json=sendSource" json:"send_source"`
	FromSex         int32                      `protobuf:"varint,12,opt,name=from_sex,json=fromSex" json:"from_sex"`
	SendMethod      int32                      `protobuf:"varint,13,opt,name=send_method,json=sendMethod" json:"send_method"`
	UserProfile     *ga.UserProfile            `protobuf:"bytes,14,opt,name=user_profile,json=userProfile" json:"user_profile,omitempty"`
	UserUkwInfo     *ga.UserUKWInfo            `protobuf:"bytes,15,opt,name=user_ukw_info,json=userUkwInfo" json:"user_ukw_info,omitempty"`
	BusinessType    uint32                     `protobuf:"varint,16,opt,name=business_type,json=businessType" json:"business_type"`
}

func (m *UserPresentDetail) Reset()                    { *m = UserPresentDetail{} }
func (m *UserPresentDetail) String() string            { return proto.CompactTextString(m) }
func (*UserPresentDetail) ProtoMessage()               {}
func (*UserPresentDetail) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{10} }

func (m *UserPresentDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserPresentDetail) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *UserPresentDetail) GetFromAccount() string {
	if m != nil {
		return m.FromAccount
	}
	return ""
}

func (m *UserPresentDetail) GetFromName() string {
	if m != nil {
		return m.FromName
	}
	return ""
}

func (m *UserPresentDetail) GetFromFaceMd5() string {
	if m != nil {
		return m.FromFaceMd5
	}
	return ""
}

func (m *UserPresentDetail) GetItemBriefConfig() *ga.PresentItemBriefConfig {
	if m != nil {
		return m.ItemBriefConfig
	}
	return nil
}

func (m *UserPresentDetail) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *UserPresentDetail) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *UserPresentDetail) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *UserPresentDetail) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *UserPresentDetail) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *UserPresentDetail) GetFromSex() int32 {
	if m != nil {
		return m.FromSex
	}
	return 0
}

func (m *UserPresentDetail) GetSendMethod() int32 {
	if m != nil {
		return m.SendMethod
	}
	return 0
}

func (m *UserPresentDetail) GetUserProfile() *ga.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *UserPresentDetail) GetUserUkwInfo() *ga.UserUKWInfo {
	if m != nil {
		return m.UserUkwInfo
	}
	return nil
}

func (m *UserPresentDetail) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

// 用户送出的礼物明细
type UserPresentSendDetail struct {
	Uid             uint32                     `protobuf:"varint,1,req,name=uid" json:"uid"`
	ToUid           uint32                     `protobuf:"varint,2,req,name=to_uid,json=toUid" json:"to_uid"`
	ToAccount       string                     `protobuf:"bytes,3,req,name=to_account,json=toAccount" json:"to_account"`
	ToName          string                     `protobuf:"bytes,4,req,name=to_name,json=toName" json:"to_name"`
	ToFaceMd5       string                     `protobuf:"bytes,5,req,name=to_face_md5,json=toFaceMd5" json:"to_face_md5"`
	ItemBriefConfig *ga.PresentItemBriefConfig `protobuf:"bytes,6,req,name=item_brief_config,json=itemBriefConfig" json:"item_brief_config,omitempty"`
	SendTime        uint32                     `protobuf:"varint,7,req,name=send_time,json=sendTime" json:"send_time"`
	ItemCount       uint32                     `protobuf:"varint,8,opt,name=item_count,json=itemCount" json:"item_count"`
	ItemId          uint32                     `protobuf:"varint,9,opt,name=item_id,json=itemId" json:"item_id"`
	Rich            uint32                     `protobuf:"varint,10,opt,name=rich" json:"rich"`
	SendSource      uint32                     `protobuf:"varint,11,opt,name=send_source,json=sendSource" json:"send_source"`
	ToSex           int32                      `protobuf:"varint,12,opt,name=to_sex,json=toSex" json:"to_sex"`
	SendMethod      int32                      `protobuf:"varint,13,opt,name=send_method,json=sendMethod" json:"send_method"`
	UserProfile     *ga.UserProfile            `protobuf:"bytes,14,opt,name=user_profile,json=userProfile" json:"user_profile,omitempty"`
	UserUkwInfo     *ga.UserUKWInfo            `protobuf:"bytes,15,opt,name=user_ukw_info,json=userUkwInfo" json:"user_ukw_info,omitempty"`
	BusinessType    uint32                     `protobuf:"varint,16,opt,name=business_type,json=businessType" json:"business_type"`
	TargetType      uint32                     `protobuf:"varint,17,opt,name=target_type,json=targetType" json:"target_type"`
	ToHeadImageUrl  string                     `protobuf:"bytes,18,opt,name=to_head_image_url,json=toHeadImageUrl" json:"to_head_image_url"`
}

func (m *UserPresentSendDetail) Reset()         { *m = UserPresentSendDetail{} }
func (m *UserPresentSendDetail) String() string { return proto.CompactTextString(m) }
func (*UserPresentSendDetail) ProtoMessage()    {}
func (*UserPresentSendDetail) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{11}
}

func (m *UserPresentSendDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserPresentSendDetail) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *UserPresentSendDetail) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

func (m *UserPresentSendDetail) GetToName() string {
	if m != nil {
		return m.ToName
	}
	return ""
}

func (m *UserPresentSendDetail) GetToFaceMd5() string {
	if m != nil {
		return m.ToFaceMd5
	}
	return ""
}

func (m *UserPresentSendDetail) GetItemBriefConfig() *ga.PresentItemBriefConfig {
	if m != nil {
		return m.ItemBriefConfig
	}
	return nil
}

func (m *UserPresentSendDetail) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *UserPresentSendDetail) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *UserPresentSendDetail) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *UserPresentSendDetail) GetRich() uint32 {
	if m != nil {
		return m.Rich
	}
	return 0
}

func (m *UserPresentSendDetail) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *UserPresentSendDetail) GetToSex() int32 {
	if m != nil {
		return m.ToSex
	}
	return 0
}

func (m *UserPresentSendDetail) GetSendMethod() int32 {
	if m != nil {
		return m.SendMethod
	}
	return 0
}

func (m *UserPresentSendDetail) GetUserProfile() *ga.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *UserPresentSendDetail) GetUserUkwInfo() *ga.UserUKWInfo {
	if m != nil {
		return m.UserUkwInfo
	}
	return nil
}

func (m *UserPresentSendDetail) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *UserPresentSendDetail) GetTargetType() uint32 {
	if m != nil {
		return m.TargetType
	}
	return 0
}

func (m *UserPresentSendDetail) GetToHeadImageUrl() string {
	if m != nil {
		return m.ToHeadImageUrl
	}
	return ""
}

// 获取用户的礼物信息
type GetUserPresentInfoReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetUid uint32      `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
}

func (m *GetUserPresentInfoReq) Reset()         { *m = GetUserPresentInfoReq{} }
func (m *GetUserPresentInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentInfoReq) ProtoMessage()    {}
func (*GetUserPresentInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{12}
}

func (m *GetUserPresentInfoReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserPresentInfoReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetUserPresentInfoResp struct {
	BaseResp         *ga.BaseResp       `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	TotalValue       uint32             `protobuf:"varint,2,req,name=total_value,json=totalValue" json:"total_value"`
	TotalCount       uint32             `protobuf:"varint,3,req,name=total_count,json=totalCount" json:"total_count"`
	PresentCountList []*ga.PresentCount `protobuf:"bytes,4,rep,name=present_count_list,json=presentCountList" json:"present_count_list,omitempty"`
	TargetUid        uint32             `protobuf:"varint,5,req,name=target_uid,json=targetUid" json:"target_uid"`
}

func (m *GetUserPresentInfoResp) Reset()         { *m = GetUserPresentInfoResp{} }
func (m *GetUserPresentInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentInfoResp) ProtoMessage()    {}
func (*GetUserPresentInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{13}
}

func (m *GetUserPresentInfoResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserPresentInfoResp) GetTotalValue() uint32 {
	if m != nil {
		return m.TotalValue
	}
	return 0
}

func (m *GetUserPresentInfoResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetUserPresentInfoResp) GetPresentCountList() []*ga.PresentCount {
	if m != nil {
		return m.PresentCountList
	}
	return nil
}

func (m *GetUserPresentInfoResp) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

// 获取用户的礼物明细列表
type GetUserPresentDetailListReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	DetailType uint32      `protobuf:"varint,2,opt,name=detail_type,json=detailType" json:"detail_type"`
}

func (m *GetUserPresentDetailListReq) Reset()         { *m = GetUserPresentDetailListReq{} }
func (m *GetUserPresentDetailListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentDetailListReq) ProtoMessage()    {}
func (*GetUserPresentDetailListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{14}
}

func (m *GetUserPresentDetailListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserPresentDetailListReq) GetDetailType() uint32 {
	if m != nil {
		return m.DetailType
	}
	return 0
}

type GetUserPresentDetailListResp struct {
	BaseResp              *ga.BaseResp             `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	PresentDetailList     []*UserPresentDetail     `protobuf:"bytes,2,rep,name=present_detail_list,json=presentDetailList" json:"present_detail_list,omitempty"`
	PresentSendDetailList []*UserPresentSendDetail `protobuf:"bytes,3,rep,name=present_send_detail_list,json=presentSendDetailList" json:"present_send_detail_list,omitempty"`
}

func (m *GetUserPresentDetailListResp) Reset()         { *m = GetUserPresentDetailListResp{} }
func (m *GetUserPresentDetailListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentDetailListResp) ProtoMessage()    {}
func (*GetUserPresentDetailListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{15}
}

func (m *GetUserPresentDetailListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserPresentDetailListResp) GetPresentDetailList() []*UserPresentDetail {
	if m != nil {
		return m.PresentDetailList
	}
	return nil
}

func (m *GetUserPresentDetailListResp) GetPresentSendDetailList() []*UserPresentSendDetail {
	if m != nil {
		return m.PresentSendDetailList
	}
	return nil
}

// 获取礼物配置列表
type GetPresentConfigListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetPresentConfigListReq) Reset()         { *m = GetPresentConfigListReq{} }
func (m *GetPresentConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListReq) ProtoMessage()    {}
func (*GetPresentConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{16}
}

func (m *GetPresentConfigListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetPresentConfigListResp struct {
	BaseResp       *ga.BaseResp            `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ConfigList     []*ga.PresentItemConfig `protobuf:"bytes,2,rep,name=config_list,json=configList" json:"config_list,omitempty"`
	LastUpdateTime uint32                  `protobuf:"varint,3,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
}

func (m *GetPresentConfigListResp) Reset()         { *m = GetPresentConfigListResp{} }
func (m *GetPresentConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListResp) ProtoMessage()    {}
func (*GetPresentConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{17}
}

func (m *GetPresentConfigListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPresentConfigListResp) GetConfigList() []*ga.PresentItemConfig {
	if m != nil {
		return m.ConfigList
	}
	return nil
}

func (m *GetPresentConfigListResp) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

type GetPresentConfigByIdReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ItemId  uint32      `protobuf:"varint,2,req,name=item_id,json=itemId" json:"item_id"`
}

func (m *GetPresentConfigByIdReq) Reset()         { *m = GetPresentConfigByIdReq{} }
func (m *GetPresentConfigByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigByIdReq) ProtoMessage()    {}
func (*GetPresentConfigByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{18}
}

func (m *GetPresentConfigByIdReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPresentConfigByIdReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type GetPresentConfigByIdResp struct {
	BaseResp   *ga.BaseResp          `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ItemConfig *ga.PresentItemConfig `protobuf:"bytes,2,opt,name=item_config,json=itemConfig" json:"item_config,omitempty"`
}

func (m *GetPresentConfigByIdResp) Reset()         { *m = GetPresentConfigByIdResp{} }
func (m *GetPresentConfigByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigByIdResp) ProtoMessage()    {}
func (*GetPresentConfigByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{19}
}

func (m *GetPresentConfigByIdResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPresentConfigByIdResp) GetItemConfig() *ga.PresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

type PresentSendItemInfo struct {
	ItemId            uint32              `protobuf:"varint,1,req,name=item_id,json=itemId" json:"item_id"`
	Count             uint32              `protobuf:"varint,2,req,name=count" json:"count"`
	ShowEffect        uint32              `protobuf:"varint,3,opt,name=show_effect,json=showEffect" json:"show_effect"`
	ShowEffectV2      uint32              `protobuf:"varint,4,opt,name=show_effect_v2,json=showEffectV2" json:"show_effect_v2"`
	FlowId            uint32              `protobuf:"varint,5,opt,name=flow_id,json=flowId" json:"flow_id"`
	IsBatch           bool                `protobuf:"varint,6,opt,name=is_batch,json=isBatch" json:"is_batch"`
	ShowBatchEffect   bool                `protobuf:"varint,7,opt,name=show_batch_effect,json=showBatchEffect" json:"show_batch_effect"`
	SendType          uint32              `protobuf:"varint,8,opt,name=send_type,json=sendType" json:"send_type"`
	DrawPresentPic    *DrawPresentPicture `protobuf:"bytes,9,opt,name=draw_present_pic,json=drawPresentPic" json:"draw_present_pic,omitempty"`
	DynamicTemplateId uint32              `protobuf:"varint,10,opt,name=dynamic_template_id,json=dynamicTemplateId" json:"dynamic_template_id"`
	IsVisibleToSender bool                `protobuf:"varint,11,opt,name=is_visible_to_sender,json=isVisibleToSender" json:"is_visible_to_sender"`
	IsShowSurprise    bool                `protobuf:"varint,12,opt,name=is_show_surprise,json=isShowSurprise" json:"is_show_surprise"`
	SurpriseCount     uint32              `protobuf:"varint,13,opt,name=surprise_count,json=surpriseCount" json:"surprise_count"`
	CustomTextJson    string              `protobuf:"bytes,14,opt,name=custom_text_json,json=customTextJson" json:"custom_text_json"`
	ShowImPreEffect   bool                `protobuf:"varint,15,opt,name=show_im_pre_effect,json=showImPreEffect" json:"show_im_pre_effect"`
	PreEffectText     string              `protobuf:"bytes,16,opt,name=pre_effect_text,json=preEffectText" json:"pre_effect_text"`
}

func (m *PresentSendItemInfo) Reset()                    { *m = PresentSendItemInfo{} }
func (m *PresentSendItemInfo) String() string            { return proto.CompactTextString(m) }
func (*PresentSendItemInfo) ProtoMessage()               {}
func (*PresentSendItemInfo) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{20} }

func (m *PresentSendItemInfo) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentSendItemInfo) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *PresentSendItemInfo) GetShowEffect() uint32 {
	if m != nil {
		return m.ShowEffect
	}
	return 0
}

func (m *PresentSendItemInfo) GetShowEffectV2() uint32 {
	if m != nil {
		return m.ShowEffectV2
	}
	return 0
}

func (m *PresentSendItemInfo) GetFlowId() uint32 {
	if m != nil {
		return m.FlowId
	}
	return 0
}

func (m *PresentSendItemInfo) GetIsBatch() bool {
	if m != nil {
		return m.IsBatch
	}
	return false
}

func (m *PresentSendItemInfo) GetShowBatchEffect() bool {
	if m != nil {
		return m.ShowBatchEffect
	}
	return false
}

func (m *PresentSendItemInfo) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *PresentSendItemInfo) GetDrawPresentPic() *DrawPresentPicture {
	if m != nil {
		return m.DrawPresentPic
	}
	return nil
}

func (m *PresentSendItemInfo) GetDynamicTemplateId() uint32 {
	if m != nil {
		return m.DynamicTemplateId
	}
	return 0
}

func (m *PresentSendItemInfo) GetIsVisibleToSender() bool {
	if m != nil {
		return m.IsVisibleToSender
	}
	return false
}

func (m *PresentSendItemInfo) GetIsShowSurprise() bool {
	if m != nil {
		return m.IsShowSurprise
	}
	return false
}

func (m *PresentSendItemInfo) GetSurpriseCount() uint32 {
	if m != nil {
		return m.SurpriseCount
	}
	return 0
}

func (m *PresentSendItemInfo) GetCustomTextJson() string {
	if m != nil {
		return m.CustomTextJson
	}
	return ""
}

func (m *PresentSendItemInfo) GetShowImPreEffect() bool {
	if m != nil {
		return m.ShowImPreEffect
	}
	return false
}

func (m *PresentSendItemInfo) GetPreEffectText() string {
	if m != nil {
		return m.PreEffectText
	}
	return ""
}

// 可靠礼物消息
type PresentSendMsg struct {
	ItemInfo        *PresentSendItemInfo `protobuf:"bytes,1,req,name=item_info,json=itemInfo" json:"item_info,omitempty"`
	SendTime        uint64               `protobuf:"varint,2,req,name=send_time,json=sendTime" json:"send_time"`
	ChannelId       uint32               `protobuf:"varint,3,opt,name=channel_id,json=channelId" json:"channel_id"`
	SendUid         uint32               `protobuf:"varint,4,req,name=send_uid,json=sendUid" json:"send_uid"`
	SendAccount     string               `protobuf:"bytes,5,req,name=send_account,json=sendAccount" json:"send_account"`
	SendNickname    string               `protobuf:"bytes,6,req,name=send_nickname,json=sendNickname" json:"send_nickname"`
	TargetUid       uint32               `protobuf:"varint,7,req,name=target_uid,json=targetUid" json:"target_uid"`
	TargetAccount   string               `protobuf:"bytes,8,req,name=target_account,json=targetAccount" json:"target_account"`
	TargetNickname  string               `protobuf:"bytes,9,req,name=target_nickname,json=targetNickname" json:"target_nickname"`
	ExtendJson      string               `protobuf:"bytes,10,opt,name=extend_json,json=extendJson" json:"extend_json"`
	FromUserProfile *ga.UserProfile      `protobuf:"bytes,11,opt,name=from_user_profile,json=fromUserProfile" json:"from_user_profile,omitempty"`
	ToUserProfile   *ga.UserProfile      `protobuf:"bytes,12,opt,name=to_user_profile,json=toUserProfile" json:"to_user_profile,omitempty"`
	OnlyShowMsg     bool                 `protobuf:"varint,13,opt,name=only_show_msg,json=onlyShowMsg" json:"only_show_msg"`
}

func (m *PresentSendMsg) Reset()                    { *m = PresentSendMsg{} }
func (m *PresentSendMsg) String() string            { return proto.CompactTextString(m) }
func (*PresentSendMsg) ProtoMessage()               {}
func (*PresentSendMsg) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{21} }

func (m *PresentSendMsg) GetItemInfo() *PresentSendItemInfo {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

func (m *PresentSendMsg) GetSendTime() uint64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *PresentSendMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PresentSendMsg) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *PresentSendMsg) GetSendAccount() string {
	if m != nil {
		return m.SendAccount
	}
	return ""
}

func (m *PresentSendMsg) GetSendNickname() string {
	if m != nil {
		return m.SendNickname
	}
	return ""
}

func (m *PresentSendMsg) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *PresentSendMsg) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *PresentSendMsg) GetTargetNickname() string {
	if m != nil {
		return m.TargetNickname
	}
	return ""
}

func (m *PresentSendMsg) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *PresentSendMsg) GetFromUserProfile() *ga.UserProfile {
	if m != nil {
		return m.FromUserProfile
	}
	return nil
}

func (m *PresentSendMsg) GetToUserProfile() *ga.UserProfile {
	if m != nil {
		return m.ToUserProfile
	}
	return nil
}

func (m *PresentSendMsg) GetOnlyShowMsg() bool {
	if m != nil {
		return m.OnlyShowMsg
	}
	return false
}

type PresentBatchTargetInfo struct {
	Uid         uint32          `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account     string          `protobuf:"bytes,2,req,name=account" json:"account"`
	Nickname    string          `protobuf:"bytes,3,req,name=nickname" json:"nickname"`
	ExtendJson  string          `protobuf:"bytes,4,opt,name=extend_json,json=extendJson" json:"extend_json"`
	UserProfile *ga.UserProfile `protobuf:"bytes,5,opt,name=user_profile,json=userProfile" json:"user_profile,omitempty"`
	CustomText  string          `protobuf:"bytes,6,opt,name=custom_text,json=customText" json:"custom_text"`
}

func (m *PresentBatchTargetInfo) Reset()         { *m = PresentBatchTargetInfo{} }
func (m *PresentBatchTargetInfo) String() string { return proto.CompactTextString(m) }
func (*PresentBatchTargetInfo) ProtoMessage()    {}
func (*PresentBatchTargetInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{22}
}

func (m *PresentBatchTargetInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PresentBatchTargetInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *PresentBatchTargetInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *PresentBatchTargetInfo) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *PresentBatchTargetInfo) GetUserProfile() *ga.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *PresentBatchTargetInfo) GetCustomText() string {
	if m != nil {
		return m.CustomText
	}
	return ""
}

// 批量送礼信息
type PresentBatchInfoMsg struct {
	ItemId          uint32                    `protobuf:"varint,1,req,name=item_id,json=itemId" json:"item_id"`
	TotalItemCount  uint32                    `protobuf:"varint,2,req,name=total_item_count,json=totalItemCount" json:"total_item_count"`
	BatchType       uint32                    `protobuf:"varint,3,req,name=batch_type,json=batchType" json:"batch_type"`
	SendTime        uint64                    `protobuf:"varint,4,req,name=send_time,json=sendTime" json:"send_time"`
	ChannelId       uint32                    `protobuf:"varint,5,opt,name=channel_id,json=channelId" json:"channel_id"`
	SendUid         uint32                    `protobuf:"varint,6,req,name=send_uid,json=sendUid" json:"send_uid"`
	SendAccount     string                    `protobuf:"bytes,7,req,name=send_account,json=sendAccount" json:"send_account"`
	SendNickname    string                    `protobuf:"bytes,8,req,name=send_nickname,json=sendNickname" json:"send_nickname"`
	ExtendJson      string                    `protobuf:"bytes,9,opt,name=extend_json,json=extendJson" json:"extend_json"`
	TargetList      []*PresentBatchTargetInfo `protobuf:"bytes,10,rep,name=target_list,json=targetList" json:"target_list,omitempty"`
	ItemInfo        *PresentSendItemInfo      `protobuf:"bytes,11,opt,name=item_info,json=itemInfo" json:"item_info,omitempty"`
	FromUserProfile *ga.UserProfile           `protobuf:"bytes,12,opt,name=from_user_profile,json=fromUserProfile" json:"from_user_profile,omitempty"`
	IsMulti         bool                      `protobuf:"varint,13,opt,name=is_multi,json=isMulti" json:"is_multi"`
}

func (m *PresentBatchInfoMsg) Reset()                    { *m = PresentBatchInfoMsg{} }
func (m *PresentBatchInfoMsg) String() string            { return proto.CompactTextString(m) }
func (*PresentBatchInfoMsg) ProtoMessage()               {}
func (*PresentBatchInfoMsg) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{23} }

func (m *PresentBatchInfoMsg) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetTotalItemCount() uint32 {
	if m != nil {
		return m.TotalItemCount
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetBatchType() uint32 {
	if m != nil {
		return m.BatchType
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetSendTime() uint64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetSendAccount() string {
	if m != nil {
		return m.SendAccount
	}
	return ""
}

func (m *PresentBatchInfoMsg) GetSendNickname() string {
	if m != nil {
		return m.SendNickname
	}
	return ""
}

func (m *PresentBatchInfoMsg) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *PresentBatchInfoMsg) GetTargetList() []*PresentBatchTargetInfo {
	if m != nil {
		return m.TargetList
	}
	return nil
}

func (m *PresentBatchInfoMsg) GetItemInfo() *PresentSendItemInfo {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

func (m *PresentBatchInfoMsg) GetFromUserProfile() *ga.UserProfile {
	if m != nil {
		return m.FromUserProfile
	}
	return nil
}

func (m *PresentBatchInfoMsg) GetIsMulti() bool {
	if m != nil {
		return m.IsMulti
	}
	return false
}

// 获取礼物流光配置列表
type GetPresentFlowConfigListReq struct {
	BaseReq        *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	LastUpdateTime uint32      `protobuf:"varint,2,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
}

func (m *GetPresentFlowConfigListReq) Reset()         { *m = GetPresentFlowConfigListReq{} }
func (m *GetPresentFlowConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentFlowConfigListReq) ProtoMessage()    {}
func (*GetPresentFlowConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{24}
}

func (m *GetPresentFlowConfigListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPresentFlowConfigListReq) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

type GetPresentFlowConfigListResp struct {
	BaseResp       *ga.BaseResp            `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ConfigList     []*ga.PresentFlowConfig `protobuf:"bytes,2,rep,name=config_list,json=configList" json:"config_list,omitempty"`
	LastUpdateTime uint32                  `protobuf:"varint,3,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
}

func (m *GetPresentFlowConfigListResp) Reset()         { *m = GetPresentFlowConfigListResp{} }
func (m *GetPresentFlowConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentFlowConfigListResp) ProtoMessage()    {}
func (*GetPresentFlowConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{25}
}

func (m *GetPresentFlowConfigListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPresentFlowConfigListResp) GetConfigList() []*ga.PresentFlowConfig {
	if m != nil {
		return m.ConfigList
	}
	return nil
}

func (m *GetPresentFlowConfigListResp) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

type GetPresentFlowConfigByIdReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	FlowId  uint32      `protobuf:"varint,2,req,name=flow_id,json=flowId" json:"flow_id"`
}

func (m *GetPresentFlowConfigByIdReq) Reset()         { *m = GetPresentFlowConfigByIdReq{} }
func (m *GetPresentFlowConfigByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentFlowConfigByIdReq) ProtoMessage()    {}
func (*GetPresentFlowConfigByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{26}
}

func (m *GetPresentFlowConfigByIdReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPresentFlowConfigByIdReq) GetFlowId() uint32 {
	if m != nil {
		return m.FlowId
	}
	return 0
}

type GetPresentFlowConfigByIdResp struct {
	BaseResp   *ga.BaseResp          `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	FlowConfig *ga.PresentFlowConfig `protobuf:"bytes,2,opt,name=flow_config,json=flowConfig" json:"flow_config,omitempty"`
}

func (m *GetPresentFlowConfigByIdResp) Reset()         { *m = GetPresentFlowConfigByIdResp{} }
func (m *GetPresentFlowConfigByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentFlowConfigByIdResp) ProtoMessage()    {}
func (*GetPresentFlowConfigByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{27}
}

func (m *GetPresentFlowConfigByIdResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPresentFlowConfigByIdResp) GetFlowConfig() *ga.PresentFlowConfig {
	if m != nil {
		return m.FlowConfig
	}
	return nil
}

type DrawPresentPara struct {
	ItemId uint32 `protobuf:"varint,1,req,name=item_id,json=itemId" json:"item_id"`
	ImgUrl string `protobuf:"bytes,2,req,name=img_url,json=imgUrl" json:"img_url"`
}

func (m *DrawPresentPara) Reset()                    { *m = DrawPresentPara{} }
func (m *DrawPresentPara) String() string            { return proto.CompactTextString(m) }
func (*DrawPresentPara) ProtoMessage()               {}
func (*DrawPresentPara) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{28} }

func (m *DrawPresentPara) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *DrawPresentPara) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

// 获取涂鸦礼物参数信息
type GetDrawPresentParaReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetDrawPresentParaReq) Reset()         { *m = GetDrawPresentParaReq{} }
func (m *GetDrawPresentParaReq) String() string { return proto.CompactTextString(m) }
func (*GetDrawPresentParaReq) ProtoMessage()    {}
func (*GetDrawPresentParaReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{29}
}

func (m *GetDrawPresentParaReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetDrawPresentParaResp struct {
	BaseResp *ga.BaseResp       `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ParaList []*DrawPresentPara `protobuf:"bytes,2,rep,name=para_list,json=paraList" json:"para_list,omitempty"`
}

func (m *GetDrawPresentParaResp) Reset()         { *m = GetDrawPresentParaResp{} }
func (m *GetDrawPresentParaResp) String() string { return proto.CompactTextString(m) }
func (*GetDrawPresentParaResp) ProtoMessage()    {}
func (*GetDrawPresentParaResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{30}
}

func (m *GetDrawPresentParaResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetDrawPresentParaResp) GetParaList() []*DrawPresentPara {
	if m != nil {
		return m.ParaList
	}
	return nil
}

// 礼物冠名配置信息
type NamingPresentConfig struct {
	Uid           uint32 `protobuf:"varint,1,opt,name=uid" json:"uid"`
	GiftId        uint32 `protobuf:"varint,2,opt,name=gift_id,json=giftId" json:"gift_id"`
	NamingContent string `protobuf:"bytes,3,opt,name=naming_content,json=namingContent" json:"naming_content"`
	Account       string `protobuf:"bytes,4,opt,name=account" json:"account"`
}

func (m *NamingPresentConfig) Reset()                    { *m = NamingPresentConfig{} }
func (m *NamingPresentConfig) String() string            { return proto.CompactTextString(m) }
func (*NamingPresentConfig) ProtoMessage()               {}
func (*NamingPresentConfig) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{31} }

func (m *NamingPresentConfig) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NamingPresentConfig) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *NamingPresentConfig) GetNamingContent() string {
	if m != nil {
		return m.NamingContent
	}
	return ""
}

func (m *NamingPresentConfig) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type GetNamingPresentConfigListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetNamingPresentConfigListReq) Reset()         { *m = GetNamingPresentConfigListReq{} }
func (m *GetNamingPresentConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetNamingPresentConfigListReq) ProtoMessage()    {}
func (*GetNamingPresentConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{32}
}

func (m *GetNamingPresentConfigListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetNamingPresentConfigListResp struct {
	BaseResp   *ga.BaseResp           `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ConfigList []*NamingPresentConfig `protobuf:"bytes,2,rep,name=config_list,json=configList" json:"config_list,omitempty"`
}

func (m *GetNamingPresentConfigListResp) Reset()         { *m = GetNamingPresentConfigListResp{} }
func (m *GetNamingPresentConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetNamingPresentConfigListResp) ProtoMessage()    {}
func (*GetNamingPresentConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{33}
}

func (m *GetNamingPresentConfigListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetNamingPresentConfigListResp) GetConfigList() []*NamingPresentConfig {
	if m != nil {
		return m.ConfigList
	}
	return nil
}

// 获取非全屏礼物送礼动效模板配置
type GetPresentDynamicTemplateConfigReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetPresentDynamicTemplateConfigReq) Reset()         { *m = GetPresentDynamicTemplateConfigReq{} }
func (m *GetPresentDynamicTemplateConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentDynamicTemplateConfigReq) ProtoMessage()    {}
func (*GetPresentDynamicTemplateConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{34}
}

func (m *GetPresentDynamicTemplateConfigReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetPresentDynamicTemplateConfigResp struct {
	BaseResp *ga.BaseResp               `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Configs  *ga.PresentTemplateConfigs `protobuf:"bytes,2,opt,name=configs" json:"configs,omitempty"`
}

func (m *GetPresentDynamicTemplateConfigResp) Reset()         { *m = GetPresentDynamicTemplateConfigResp{} }
func (m *GetPresentDynamicTemplateConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentDynamicTemplateConfigResp) ProtoMessage()    {}
func (*GetPresentDynamicTemplateConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{35}
}

func (m *GetPresentDynamicTemplateConfigResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPresentDynamicTemplateConfigResp) GetConfigs() *ga.PresentTemplateConfigs {
	if m != nil {
		return m.Configs
	}
	return nil
}

type GetImPresentItemIdListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetImPresentItemIdListReq) Reset()         { *m = GetImPresentItemIdListReq{} }
func (m *GetImPresentItemIdListReq) String() string { return proto.CompactTextString(m) }
func (*GetImPresentItemIdListReq) ProtoMessage()    {}
func (*GetImPresentItemIdListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{36}
}

func (m *GetImPresentItemIdListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetImPresentItemIdListResp struct {
	BaseResp   *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ItemIdList []uint32     `protobuf:"varint,2,rep,name=item_id_list,json=itemIdList" json:"item_id_list,omitempty"`
}

func (m *GetImPresentItemIdListResp) Reset()         { *m = GetImPresentItemIdListResp{} }
func (m *GetImPresentItemIdListResp) String() string { return proto.CompactTextString(m) }
func (*GetImPresentItemIdListResp) ProtoMessage()    {}
func (*GetImPresentItemIdListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{37}
}

func (m *GetImPresentItemIdListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetImPresentItemIdListResp) GetItemIdList() []uint32 {
	if m != nil {
		return m.ItemIdList
	}
	return nil
}

type GetStangerImItemIdListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetStangerImItemIdListReq) Reset()         { *m = GetStangerImItemIdListReq{} }
func (m *GetStangerImItemIdListReq) String() string { return proto.CompactTextString(m) }
func (*GetStangerImItemIdListReq) ProtoMessage()    {}
func (*GetStangerImItemIdListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{38}
}

func (m *GetStangerImItemIdListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetStangerImItemIdListResp struct {
	BaseResp   *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ItemIdList []uint32     `protobuf:"varint,2,rep,name=item_id_list,json=itemIdList" json:"item_id_list,omitempty"`
}

func (m *GetStangerImItemIdListResp) Reset()         { *m = GetStangerImItemIdListResp{} }
func (m *GetStangerImItemIdListResp) String() string { return proto.CompactTextString(m) }
func (*GetStangerImItemIdListResp) ProtoMessage()    {}
func (*GetStangerImItemIdListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{39}
}

func (m *GetStangerImItemIdListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetStangerImItemIdListResp) GetItemIdList() []uint32 {
	if m != nil {
		return m.ItemIdList
	}
	return nil
}

// 粉丝团抽奖的中奖信息
type FansPresentMessage struct {
	Account     []string `protobuf:"bytes,1,rep,name=account" json:"account,omitempty"`
	PresentName string   `protobuf:"bytes,2,req,name=present_name,json=presentName" json:"present_name"`
	PresentIcon string   `protobuf:"bytes,3,req,name=present_icon,json=presentIcon" json:"present_icon"`
}

func (m *FansPresentMessage) Reset()                    { *m = FansPresentMessage{} }
func (m *FansPresentMessage) String() string            { return proto.CompactTextString(m) }
func (*FansPresentMessage) ProtoMessage()               {}
func (*FansPresentMessage) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{40} }

func (m *FansPresentMessage) GetAccount() []string {
	if m != nil {
		return m.Account
	}
	return nil
}

func (m *FansPresentMessage) GetPresentName() string {
	if m != nil {
		return m.PresentName
	}
	return ""
}

func (m *FansPresentMessage) GetPresentIcon() string {
	if m != nil {
		return m.PresentIcon
	}
	return ""
}

// 展示礼物盒会用到的信息
type PresentBoxInfo struct {
	ItemMsg   *PresentSendMsg   `protobuf:"bytes,1,req,name=item_msg,json=itemMsg" json:"item_msg,omitempty"`
	BoxDetail *PresentBoxDetail `protobuf:"bytes,2,req,name=box_detail,json=boxDetail" json:"box_detail,omitempty"`
}

func (m *PresentBoxInfo) Reset()                    { *m = PresentBoxInfo{} }
func (m *PresentBoxInfo) String() string            { return proto.CompactTextString(m) }
func (*PresentBoxInfo) ProtoMessage()               {}
func (*PresentBoxInfo) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{41} }

func (m *PresentBoxInfo) GetItemMsg() *PresentSendMsg {
	if m != nil {
		return m.ItemMsg
	}
	return nil
}

func (m *PresentBoxInfo) GetBoxDetail() *PresentBoxDetail {
	if m != nil {
		return m.BoxDetail
	}
	return nil
}

type PresentBoxDetail struct {
	BoxId             string          `protobuf:"bytes,1,req,name=box_id,json=boxId" json:"box_id"`
	FromUserProfile   *ga.UserProfile `protobuf:"bytes,2,req,name=from_user_profile,json=fromUserProfile" json:"from_user_profile,omitempty"`
	ToUserProfile     *ga.UserProfile `protobuf:"bytes,3,req,name=to_user_profile,json=toUserProfile" json:"to_user_profile,omitempty"`
	ItemId            uint32          `protobuf:"varint,4,req,name=item_id,json=itemId" json:"item_id"`
	ItemName          string          `protobuf:"bytes,5,req,name=item_name,json=itemName" json:"item_name"`
	SendTime          uint64          `protobuf:"varint,6,req,name=send_time,json=sendTime" json:"send_time"`
	ExtendJson        string          `protobuf:"bytes,7,req,name=extend_json,json=extendJson" json:"extend_json"`
	DelayTime         uint64          `protobuf:"varint,8,req,name=delay_time,json=delayTime" json:"delay_time"`
	IsVisibleToSender bool            `protobuf:"varint,9,req,name=is_visible_to_sender,json=isVisibleToSender" json:"is_visible_to_sender"`
}

func (m *PresentBoxDetail) Reset()                    { *m = PresentBoxDetail{} }
func (m *PresentBoxDetail) String() string            { return proto.CompactTextString(m) }
func (*PresentBoxDetail) ProtoMessage()               {}
func (*PresentBoxDetail) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{42} }

func (m *PresentBoxDetail) GetBoxId() string {
	if m != nil {
		return m.BoxId
	}
	return ""
}

func (m *PresentBoxDetail) GetFromUserProfile() *ga.UserProfile {
	if m != nil {
		return m.FromUserProfile
	}
	return nil
}

func (m *PresentBoxDetail) GetToUserProfile() *ga.UserProfile {
	if m != nil {
		return m.ToUserProfile
	}
	return nil
}

func (m *PresentBoxDetail) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentBoxDetail) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *PresentBoxDetail) GetSendTime() uint64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *PresentBoxDetail) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *PresentBoxDetail) GetDelayTime() uint64 {
	if m != nil {
		return m.DelayTime
	}
	return 0
}

func (m *PresentBoxDetail) GetIsVisibleToSender() bool {
	if m != nil {
		return m.IsVisibleToSender
	}
	return false
}

// 全服礼物前置特效 - 开盒
type UnpackPresentBoxReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	BoxId     string      `protobuf:"bytes,2,req,name=box_id,json=boxId" json:"box_id"`
	ChannelId uint32      `protobuf:"varint,3,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *UnpackPresentBoxReq) Reset()                    { *m = UnpackPresentBoxReq{} }
func (m *UnpackPresentBoxReq) String() string            { return proto.CompactTextString(m) }
func (*UnpackPresentBoxReq) ProtoMessage()               {}
func (*UnpackPresentBoxReq) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{43} }

func (m *UnpackPresentBoxReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UnpackPresentBoxReq) GetBoxId() string {
	if m != nil {
		return m.BoxId
	}
	return ""
}

func (m *UnpackPresentBoxReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type UnpackPresentBoxResp struct {
	BaseResp *ga.BaseResp    `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	BoxInfo  *PresentBoxInfo `protobuf:"bytes,2,req,name=box_info,json=boxInfo" json:"box_info,omitempty"`
}

func (m *UnpackPresentBoxResp) Reset()         { *m = UnpackPresentBoxResp{} }
func (m *UnpackPresentBoxResp) String() string { return proto.CompactTextString(m) }
func (*UnpackPresentBoxResp) ProtoMessage()    {}
func (*UnpackPresentBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{44}
}

func (m *UnpackPresentBoxResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UnpackPresentBoxResp) GetBoxInfo() *PresentBoxInfo {
	if m != nil {
		return m.BoxInfo
	}
	return nil
}

// 展示礼物盒会用到的信息
type PresentBoxOpenMsg struct {
	BoxId string `protobuf:"bytes,1,req,name=box_id,json=boxId" json:"box_id"`
}

func (m *PresentBoxOpenMsg) Reset()                    { *m = PresentBoxOpenMsg{} }
func (m *PresentBoxOpenMsg) String() string            { return proto.CompactTextString(m) }
func (*PresentBoxOpenMsg) ProtoMessage()               {}
func (*PresentBoxOpenMsg) Descriptor() ([]byte, []int) { return fileDescriptorUserpresent_, []int{45} }

func (m *PresentBoxOpenMsg) GetBoxId() string {
	if m != nil {
		return m.BoxId
	}
	return ""
}

type BackpackIntimatePresentContractPush struct {
	Content string `protobuf:"bytes,1,req,name=content" json:"content"`
}

func (m *BackpackIntimatePresentContractPush) Reset()         { *m = BackpackIntimatePresentContractPush{} }
func (m *BackpackIntimatePresentContractPush) String() string { return proto.CompactTextString(m) }
func (*BackpackIntimatePresentContractPush) ProtoMessage()    {}
func (*BackpackIntimatePresentContractPush) Descriptor() ([]byte, []int) {
	return fileDescriptorUserpresent_, []int{46}
}

func (m *BackpackIntimatePresentContractPush) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func init() {
	proto.RegisterType((*PresentPoint)(nil), "ga.userpresent.PresentPoint")
	proto.RegisterType((*PresentLine)(nil), "ga.userpresent.PresentLine")
	proto.RegisterType((*DrawPresentPicture)(nil), "ga.userpresent.DrawPresentPicture")
	proto.RegisterType((*SendPresentReq)(nil), "ga.userpresent.SendPresentReq")
	proto.RegisterType((*SendPresentResp)(nil), "ga.userpresent.SendPresentResp")
	proto.RegisterType((*IMSendPresentReq)(nil), "ga.userpresent.IMSendPresentReq")
	proto.RegisterType((*IMSendPresentResp)(nil), "ga.userpresent.IMSendPresentResp")
	proto.RegisterType((*PresentTargetUserInfo)(nil), "ga.userpresent.PresentTargetUserInfo")
	proto.RegisterType((*BatchSendPresentReq)(nil), "ga.userpresent.BatchSendPresentReq")
	proto.RegisterType((*BatchSendPresentResp)(nil), "ga.userpresent.BatchSendPresentResp")
	proto.RegisterType((*UserPresentDetail)(nil), "ga.userpresent.UserPresentDetail")
	proto.RegisterType((*UserPresentSendDetail)(nil), "ga.userpresent.UserPresentSendDetail")
	proto.RegisterType((*GetUserPresentInfoReq)(nil), "ga.userpresent.GetUserPresentInfoReq")
	proto.RegisterType((*GetUserPresentInfoResp)(nil), "ga.userpresent.GetUserPresentInfoResp")
	proto.RegisterType((*GetUserPresentDetailListReq)(nil), "ga.userpresent.GetUserPresentDetailListReq")
	proto.RegisterType((*GetUserPresentDetailListResp)(nil), "ga.userpresent.GetUserPresentDetailListResp")
	proto.RegisterType((*GetPresentConfigListReq)(nil), "ga.userpresent.GetPresentConfigListReq")
	proto.RegisterType((*GetPresentConfigListResp)(nil), "ga.userpresent.GetPresentConfigListResp")
	proto.RegisterType((*GetPresentConfigByIdReq)(nil), "ga.userpresent.GetPresentConfigByIdReq")
	proto.RegisterType((*GetPresentConfigByIdResp)(nil), "ga.userpresent.GetPresentConfigByIdResp")
	proto.RegisterType((*PresentSendItemInfo)(nil), "ga.userpresent.PresentSendItemInfo")
	proto.RegisterType((*PresentSendMsg)(nil), "ga.userpresent.PresentSendMsg")
	proto.RegisterType((*PresentBatchTargetInfo)(nil), "ga.userpresent.PresentBatchTargetInfo")
	proto.RegisterType((*PresentBatchInfoMsg)(nil), "ga.userpresent.PresentBatchInfoMsg")
	proto.RegisterType((*GetPresentFlowConfigListReq)(nil), "ga.userpresent.GetPresentFlowConfigListReq")
	proto.RegisterType((*GetPresentFlowConfigListResp)(nil), "ga.userpresent.GetPresentFlowConfigListResp")
	proto.RegisterType((*GetPresentFlowConfigByIdReq)(nil), "ga.userpresent.GetPresentFlowConfigByIdReq")
	proto.RegisterType((*GetPresentFlowConfigByIdResp)(nil), "ga.userpresent.GetPresentFlowConfigByIdResp")
	proto.RegisterType((*DrawPresentPara)(nil), "ga.userpresent.DrawPresentPara")
	proto.RegisterType((*GetDrawPresentParaReq)(nil), "ga.userpresent.GetDrawPresentParaReq")
	proto.RegisterType((*GetDrawPresentParaResp)(nil), "ga.userpresent.GetDrawPresentParaResp")
	proto.RegisterType((*NamingPresentConfig)(nil), "ga.userpresent.NamingPresentConfig")
	proto.RegisterType((*GetNamingPresentConfigListReq)(nil), "ga.userpresent.GetNamingPresentConfigListReq")
	proto.RegisterType((*GetNamingPresentConfigListResp)(nil), "ga.userpresent.GetNamingPresentConfigListResp")
	proto.RegisterType((*GetPresentDynamicTemplateConfigReq)(nil), "ga.userpresent.GetPresentDynamicTemplateConfigReq")
	proto.RegisterType((*GetPresentDynamicTemplateConfigResp)(nil), "ga.userpresent.GetPresentDynamicTemplateConfigResp")
	proto.RegisterType((*GetImPresentItemIdListReq)(nil), "ga.userpresent.GetImPresentItemIdListReq")
	proto.RegisterType((*GetImPresentItemIdListResp)(nil), "ga.userpresent.GetImPresentItemIdListResp")
	proto.RegisterType((*GetStangerImItemIdListReq)(nil), "ga.userpresent.GetStangerImItemIdListReq")
	proto.RegisterType((*GetStangerImItemIdListResp)(nil), "ga.userpresent.GetStangerImItemIdListResp")
	proto.RegisterType((*FansPresentMessage)(nil), "ga.userpresent.FansPresentMessage")
	proto.RegisterType((*PresentBoxInfo)(nil), "ga.userpresent.PresentBoxInfo")
	proto.RegisterType((*PresentBoxDetail)(nil), "ga.userpresent.PresentBoxDetail")
	proto.RegisterType((*UnpackPresentBoxReq)(nil), "ga.userpresent.UnpackPresentBoxReq")
	proto.RegisterType((*UnpackPresentBoxResp)(nil), "ga.userpresent.UnpackPresentBoxResp")
	proto.RegisterType((*PresentBoxOpenMsg)(nil), "ga.userpresent.PresentBoxOpenMsg")
	proto.RegisterType((*BackpackIntimatePresentContractPush)(nil), "ga.userpresent.BackpackIntimatePresentContractPush")
	proto.RegisterEnum("ga.userpresent.PresentSourceType", PresentSourceType_name, PresentSourceType_value)
	proto.RegisterEnum("ga.userpresent.PresentBatchSendType", PresentBatchSendType_name, PresentBatchSendType_value)
	proto.RegisterEnum("ga.userpresent.PresentSendType", PresentSendType_name, PresentSendType_value)
	proto.RegisterEnum("ga.userpresent.PresentSendSourceType", PresentSendSourceType_name, PresentSendSourceType_value)
	proto.RegisterEnum("ga.userpresent.PresentSendMethodType", PresentSendMethodType_name, PresentSendMethodType_value)
	proto.RegisterEnum("ga.userpresent.PresentTextType", PresentTextType_name, PresentTextType_value)
	proto.RegisterEnum("ga.userpresent.SendPresentTargetType", SendPresentTargetType_name, SendPresentTargetType_value)
	proto.RegisterEnum("ga.userpresent.PresentBusinessType", PresentBusinessType_name, PresentBusinessType_value)
	proto.RegisterEnum("ga.userpresent.PresentDetailType", PresentDetailType_name, PresentDetailType_value)
}
func (m *PresentPoint) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresentPoint) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xd
	i++
	i = encodeFixed32Userpresent_(dAtA, i, uint32(math3.Float32bits(float32(m.X))))
	dAtA[i] = 0x15
	i++
	i = encodeFixed32Userpresent_(dAtA, i, uint32(math3.Float32bits(float32(m.Y))))
	return i, nil
}

func (m *PresentLine) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresentLine) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemId))
	if len(m.PointList) > 0 {
		for _, msg := range m.PointList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintUserpresent_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DrawPresentPicture) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DrawPresentPicture) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LineList) > 0 {
		for _, msg := range m.LineList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintUserpresent_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SendPresentReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendPresentReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ConfigUpdateTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x38
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendSource))
	dAtA[i] = 0x40
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemSource))
	dAtA[i] = 0x48
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SourceId))
	dAtA[i] = 0x50
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendType))
	if m.DrawPresentPic != nil {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.DrawPresentPic.Size()))
		n2, err := m.DrawPresentPic.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *SendPresentResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendPresentResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseResp.Size()))
		n3, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemId))
	if m.MsgInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.MsgInfo.Size()))
		n4, err := m.MsgInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.MemberContributionAdded))
	dAtA[i] = 0x28
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x30
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.CurTbeans))
	dAtA[i] = 0x38
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemSource))
	dAtA[i] = 0x40
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SourceId))
	dAtA[i] = 0x48
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SourceRemain))
	dAtA[i] = 0x50
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ExpireTime))
	if m.BoxDetail != nil {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BoxDetail.Size()))
		n5, err := m.BoxDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *IMSendPresentReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IMSendPresentReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseReq.Size()))
		n6, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x28
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendSource))
	dAtA[i] = 0x30
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemSource))
	dAtA[i] = 0x38
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SourceId))
	dAtA[i] = 0x40
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendType))
	dAtA[i] = 0x48
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.PresentTextType))
	dAtA[i] = 0x52
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.PreEffectText)))
	i += copy(dAtA[i:], m.PreEffectText)
	if len(m.TargetUidList) > 0 {
		for _, num := range m.TargetUidList {
			dAtA[i] = 0x58
			i++
			i = encodeVarintUserpresent_(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x60
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.BusinessType))
	dAtA[i] = 0x68
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.TargetType))
	dAtA[i] = 0x70
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.RoleId))
	dAtA[i] = 0x78
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.PartnerId))
	return i, nil
}

func (m *IMSendPresentResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IMSendPresentResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseResp.Size()))
		n7, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemId))
	if m.MsgInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.MsgInfo.Size()))
		n8, err := m.MsgInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.MemberContributionAdded))
	dAtA[i] = 0x28
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x30
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.CurTbeans))
	dAtA[i] = 0x38
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemSource))
	dAtA[i] = 0x40
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SourceId))
	dAtA[i] = 0x48
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SourceRemain))
	dAtA[i] = 0x50
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ExpireTime))
	return i, nil
}

func (m *PresentTargetUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresentTargetUserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	if m.UserProfile != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.UserProfile.Size()))
		n9, err := m.UserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x2a
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.CustomText)))
	i += copy(dAtA[i:], m.CustomText)
	return i, nil
}

func (m *BatchSendPresentReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchSendPresentReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseReq.Size()))
		n10, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x28
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendSource))
	dAtA[i] = 0x30
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemSource))
	dAtA[i] = 0x38
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SourceId))
	dAtA[i] = 0x40
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.BatchType))
	dAtA[i] = 0x48
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendType))
	if m.DrawPresentPic != nil {
		dAtA[i] = 0x52
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.DrawPresentPic.Size()))
		n11, err := m.DrawPresentPic.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x58
			i++
			i = encodeVarintUserpresent_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchSendPresentResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchSendPresentResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseResp.Size()))
		n12, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	if m.MsgInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.MsgInfo.Size()))
		n13, err := m.MsgInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.CurTbeans))
	dAtA[i] = 0x20
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemSource))
	dAtA[i] = 0x28
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SourceId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SourceRemain))
	if len(m.TargetList) > 0 {
		for _, msg := range m.TargetList {
			dAtA[i] = 0x3a
			i++
			i = encodeVarintUserpresent_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.ItemInfo != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemInfo.Size()))
		n14, err := m.ItemInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	dAtA[i] = 0x48
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ExpireTime))
	return i, nil
}

func (m *UserPresentDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserPresentDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.FromUid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.FromAccount)))
	i += copy(dAtA[i:], m.FromAccount)
	dAtA[i] = 0x22
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.FromName)))
	i += copy(dAtA[i:], m.FromName)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.FromFaceMd5)))
	i += copy(dAtA[i:], m.FromFaceMd5)
	if m.ItemBriefConfig == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_brief_config")
	} else {
		dAtA[i] = 0x32
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemBriefConfig.Size()))
		n15, err := m.ItemBriefConfig.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	dAtA[i] = 0x38
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendTime))
	dAtA[i] = 0x40
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemCount))
	dAtA[i] = 0x48
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x50
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.Charm))
	dAtA[i] = 0x58
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendSource))
	dAtA[i] = 0x60
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.FromSex))
	dAtA[i] = 0x68
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendMethod))
	if m.UserProfile != nil {
		dAtA[i] = 0x72
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.UserProfile.Size()))
		n16, err := m.UserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	if m.UserUkwInfo != nil {
		dAtA[i] = 0x7a
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.UserUkwInfo.Size()))
		n17, err := m.UserUkwInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.BusinessType))
	return i, nil
}

func (m *UserPresentSendDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserPresentSendDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ToUid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.ToAccount)))
	i += copy(dAtA[i:], m.ToAccount)
	dAtA[i] = 0x22
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.ToName)))
	i += copy(dAtA[i:], m.ToName)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.ToFaceMd5)))
	i += copy(dAtA[i:], m.ToFaceMd5)
	if m.ItemBriefConfig == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_brief_config")
	} else {
		dAtA[i] = 0x32
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemBriefConfig.Size()))
		n18, err := m.ItemBriefConfig.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	dAtA[i] = 0x38
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendTime))
	dAtA[i] = 0x40
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemCount))
	dAtA[i] = 0x48
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x50
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.Rich))
	dAtA[i] = 0x58
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendSource))
	dAtA[i] = 0x60
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ToSex))
	dAtA[i] = 0x68
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendMethod))
	if m.UserProfile != nil {
		dAtA[i] = 0x72
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.UserProfile.Size()))
		n19, err := m.UserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	if m.UserUkwInfo != nil {
		dAtA[i] = 0x7a
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.UserUkwInfo.Size()))
		n20, err := m.UserUkwInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.BusinessType))
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.TargetType))
	dAtA[i] = 0x92
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.ToHeadImageUrl)))
	i += copy(dAtA[i:], m.ToHeadImageUrl)
	return i, nil
}

func (m *GetUserPresentInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserPresentInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseReq.Size()))
		n21, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.TargetUid))
	return i, nil
}

func (m *GetUserPresentInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserPresentInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseResp.Size()))
		n22, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n22
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.TotalValue))
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.TotalCount))
	if len(m.PresentCountList) > 0 {
		for _, msg := range m.PresentCountList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintUserpresent_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.TargetUid))
	return i, nil
}

func (m *GetUserPresentDetailListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserPresentDetailListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseReq.Size()))
		n23, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n23
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.DetailType))
	return i, nil
}

func (m *GetUserPresentDetailListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserPresentDetailListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseResp.Size()))
		n24, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n24
	}
	if len(m.PresentDetailList) > 0 {
		for _, msg := range m.PresentDetailList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintUserpresent_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.PresentSendDetailList) > 0 {
		for _, msg := range m.PresentSendDetailList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintUserpresent_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetPresentConfigListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentConfigListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseReq.Size()))
		n25, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n25
	}
	return i, nil
}

func (m *GetPresentConfigListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentConfigListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseResp.Size()))
		n26, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n26
	}
	if len(m.ConfigList) > 0 {
		for _, msg := range m.ConfigList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintUserpresent_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.LastUpdateTime))
	return i, nil
}

func (m *GetPresentConfigByIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentConfigByIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseReq.Size()))
		n27, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n27
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemId))
	return i, nil
}

func (m *GetPresentConfigByIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentConfigByIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseResp.Size()))
		n28, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n28
	}
	if m.ItemConfig != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemConfig.Size()))
		n29, err := m.ItemConfig.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n29
	}
	return i, nil
}

func (m *PresentSendItemInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresentSendItemInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ShowEffect))
	dAtA[i] = 0x20
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ShowEffectV2))
	dAtA[i] = 0x28
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.FlowId))
	dAtA[i] = 0x30
	i++
	if m.IsBatch {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	if m.ShowBatchEffect {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x40
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendType))
	if m.DrawPresentPic != nil {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.DrawPresentPic.Size()))
		n30, err := m.DrawPresentPic.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n30
	}
	dAtA[i] = 0x50
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.DynamicTemplateId))
	dAtA[i] = 0x58
	i++
	if m.IsVisibleToSender {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x60
	i++
	if m.IsShowSurprise {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x68
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SurpriseCount))
	dAtA[i] = 0x72
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.CustomTextJson)))
	i += copy(dAtA[i:], m.CustomTextJson)
	dAtA[i] = 0x78
	i++
	if m.ShowImPreEffect {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x82
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.PreEffectText)))
	i += copy(dAtA[i:], m.PreEffectText)
	return i, nil
}

func (m *PresentSendMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresentSendMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemInfo == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemInfo.Size()))
		n31, err := m.ItemInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n31
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendUid))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.SendAccount)))
	i += copy(dAtA[i:], m.SendAccount)
	dAtA[i] = 0x32
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.SendNickname)))
	i += copy(dAtA[i:], m.SendNickname)
	dAtA[i] = 0x38
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x42
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.TargetNickname)))
	i += copy(dAtA[i:], m.TargetNickname)
	dAtA[i] = 0x52
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.ExtendJson)))
	i += copy(dAtA[i:], m.ExtendJson)
	if m.FromUserProfile != nil {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.FromUserProfile.Size()))
		n32, err := m.FromUserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n32
	}
	if m.ToUserProfile != nil {
		dAtA[i] = 0x62
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.ToUserProfile.Size()))
		n33, err := m.ToUserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n33
	}
	dAtA[i] = 0x68
	i++
	if m.OnlyShowMsg {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *PresentBatchTargetInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresentBatchTargetInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x22
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.ExtendJson)))
	i += copy(dAtA[i:], m.ExtendJson)
	if m.UserProfile != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.UserProfile.Size()))
		n34, err := m.UserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n34
	}
	dAtA[i] = 0x32
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.CustomText)))
	i += copy(dAtA[i:], m.CustomText)
	return i, nil
}

func (m *PresentBatchInfoMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresentBatchInfoMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.TotalItemCount))
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.BatchType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendUid))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.SendAccount)))
	i += copy(dAtA[i:], m.SendAccount)
	dAtA[i] = 0x42
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.SendNickname)))
	i += copy(dAtA[i:], m.SendNickname)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.ExtendJson)))
	i += copy(dAtA[i:], m.ExtendJson)
	if len(m.TargetList) > 0 {
		for _, msg := range m.TargetList {
			dAtA[i] = 0x52
			i++
			i = encodeVarintUserpresent_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.ItemInfo != nil {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemInfo.Size()))
		n35, err := m.ItemInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n35
	}
	if m.FromUserProfile != nil {
		dAtA[i] = 0x62
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.FromUserProfile.Size()))
		n36, err := m.FromUserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n36
	}
	dAtA[i] = 0x68
	i++
	if m.IsMulti {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetPresentFlowConfigListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentFlowConfigListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseReq.Size()))
		n37, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n37
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.LastUpdateTime))
	return i, nil
}

func (m *GetPresentFlowConfigListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentFlowConfigListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseResp.Size()))
		n38, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n38
	}
	if len(m.ConfigList) > 0 {
		for _, msg := range m.ConfigList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintUserpresent_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.LastUpdateTime))
	return i, nil
}

func (m *GetPresentFlowConfigByIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentFlowConfigByIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseReq.Size()))
		n39, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n39
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.FlowId))
	return i, nil
}

func (m *GetPresentFlowConfigByIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentFlowConfigByIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseResp.Size()))
		n40, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n40
	}
	if m.FlowConfig != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.FlowConfig.Size()))
		n41, err := m.FlowConfig.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n41
	}
	return i, nil
}

func (m *DrawPresentPara) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DrawPresentPara) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.ImgUrl)))
	i += copy(dAtA[i:], m.ImgUrl)
	return i, nil
}

func (m *GetDrawPresentParaReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDrawPresentParaReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseReq.Size()))
		n42, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n42
	}
	return i, nil
}

func (m *GetDrawPresentParaResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDrawPresentParaResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseResp.Size()))
		n43, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n43
	}
	if len(m.ParaList) > 0 {
		for _, msg := range m.ParaList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintUserpresent_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *NamingPresentConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NamingPresentConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.GiftId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.NamingContent)))
	i += copy(dAtA[i:], m.NamingContent)
	dAtA[i] = 0x22
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	return i, nil
}

func (m *GetNamingPresentConfigListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetNamingPresentConfigListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseReq.Size()))
		n44, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n44
	}
	return i, nil
}

func (m *GetNamingPresentConfigListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetNamingPresentConfigListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseResp.Size()))
		n45, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n45
	}
	if len(m.ConfigList) > 0 {
		for _, msg := range m.ConfigList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintUserpresent_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetPresentDynamicTemplateConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentDynamicTemplateConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseReq.Size()))
		n46, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n46
	}
	return i, nil
}

func (m *GetPresentDynamicTemplateConfigResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentDynamicTemplateConfigResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseResp.Size()))
		n47, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n47
	}
	if m.Configs != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.Configs.Size()))
		n48, err := m.Configs.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n48
	}
	return i, nil
}

func (m *GetImPresentItemIdListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetImPresentItemIdListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseReq.Size()))
		n49, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n49
	}
	return i, nil
}

func (m *GetImPresentItemIdListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetImPresentItemIdListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseResp.Size()))
		n50, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n50
	}
	if len(m.ItemIdList) > 0 {
		for _, num := range m.ItemIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintUserpresent_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetStangerImItemIdListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetStangerImItemIdListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseReq.Size()))
		n51, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n51
	}
	return i, nil
}

func (m *GetStangerImItemIdListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetStangerImItemIdListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseResp.Size()))
		n52, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n52
	}
	if len(m.ItemIdList) > 0 {
		for _, num := range m.ItemIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintUserpresent_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *FansPresentMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FansPresentMessage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Account) > 0 {
		for _, s := range m.Account {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.PresentName)))
	i += copy(dAtA[i:], m.PresentName)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.PresentIcon)))
	i += copy(dAtA[i:], m.PresentIcon)
	return i, nil
}

func (m *PresentBoxInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresentBoxInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemMsg == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_msg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemMsg.Size()))
		n53, err := m.ItemMsg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n53
	}
	if m.BoxDetail == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("box_detail")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BoxDetail.Size()))
		n54, err := m.BoxDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n54
	}
	return i, nil
}

func (m *PresentBoxDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresentBoxDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.BoxId)))
	i += copy(dAtA[i:], m.BoxId)
	if m.FromUserProfile == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("from_user_profile")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.FromUserProfile.Size()))
		n55, err := m.FromUserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n55
	}
	if m.ToUserProfile == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("to_user_profile")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.ToUserProfile.Size()))
		n56, err := m.ToUserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n56
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.ItemName)))
	i += copy(dAtA[i:], m.ItemName)
	dAtA[i] = 0x30
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.SendTime))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.ExtendJson)))
	i += copy(dAtA[i:], m.ExtendJson)
	dAtA[i] = 0x40
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.DelayTime))
	dAtA[i] = 0x48
	i++
	if m.IsVisibleToSender {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *UnpackPresentBoxReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnpackPresentBoxReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseReq.Size()))
		n57, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n57
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.BoxId)))
	i += copy(dAtA[i:], m.BoxId)
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *UnpackPresentBoxResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnpackPresentBoxResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BaseResp.Size()))
		n58, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n58
	}
	if m.BoxInfo == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("box_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUserpresent_(dAtA, i, uint64(m.BoxInfo.Size()))
		n59, err := m.BoxInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n59
	}
	return i, nil
}

func (m *PresentBoxOpenMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresentBoxOpenMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.BoxId)))
	i += copy(dAtA[i:], m.BoxId)
	return i, nil
}

func (m *BackpackIntimatePresentContractPush) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BackpackIntimatePresentContractPush) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintUserpresent_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	return i, nil
}

func encodeFixed64Userpresent_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Userpresent_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintUserpresent_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *PresentPoint) Size() (n int) {
	var l int
	_ = l
	n += 5
	n += 5
	return n
}

func (m *PresentLine) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserpresent_(uint64(m.ItemId))
	if len(m.PointList) > 0 {
		for _, e := range m.PointList {
			l = e.Size()
			n += 1 + l + sovUserpresent_(uint64(l))
		}
	}
	return n
}

func (m *DrawPresentPicture) Size() (n int) {
	var l int
	_ = l
	if len(m.LineList) > 0 {
		for _, e := range m.LineList {
			l = e.Size()
			n += 1 + l + sovUserpresent_(uint64(l))
		}
	}
	return n
}

func (m *SendPresentReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.TargetUid))
	n += 1 + sovUserpresent_(uint64(m.ItemId))
	n += 1 + sovUserpresent_(uint64(m.ChannelId))
	n += 1 + sovUserpresent_(uint64(m.ConfigUpdateTime))
	n += 1 + sovUserpresent_(uint64(m.Count))
	n += 1 + sovUserpresent_(uint64(m.SendSource))
	n += 1 + sovUserpresent_(uint64(m.ItemSource))
	n += 1 + sovUserpresent_(uint64(m.SourceId))
	n += 1 + sovUserpresent_(uint64(m.SendType))
	if m.DrawPresentPic != nil {
		l = m.DrawPresentPic.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	return n
}

func (m *SendPresentResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.ItemId))
	if m.MsgInfo != nil {
		l = m.MsgInfo.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.MemberContributionAdded))
	n += 1 + sovUserpresent_(uint64(m.Count))
	n += 1 + sovUserpresent_(uint64(m.CurTbeans))
	n += 1 + sovUserpresent_(uint64(m.ItemSource))
	n += 1 + sovUserpresent_(uint64(m.SourceId))
	n += 1 + sovUserpresent_(uint64(m.SourceRemain))
	n += 1 + sovUserpresent_(uint64(m.ExpireTime))
	if m.BoxDetail != nil {
		l = m.BoxDetail.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	return n
}

func (m *IMSendPresentReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.TargetUid))
	n += 1 + sovUserpresent_(uint64(m.ItemId))
	n += 1 + sovUserpresent_(uint64(m.Count))
	n += 1 + sovUserpresent_(uint64(m.SendSource))
	n += 1 + sovUserpresent_(uint64(m.ItemSource))
	n += 1 + sovUserpresent_(uint64(m.SourceId))
	n += 1 + sovUserpresent_(uint64(m.SendType))
	n += 1 + sovUserpresent_(uint64(m.PresentTextType))
	l = len(m.PreEffectText)
	n += 1 + l + sovUserpresent_(uint64(l))
	if len(m.TargetUidList) > 0 {
		for _, e := range m.TargetUidList {
			n += 1 + sovUserpresent_(uint64(e))
		}
	}
	n += 1 + sovUserpresent_(uint64(m.BusinessType))
	n += 1 + sovUserpresent_(uint64(m.TargetType))
	n += 1 + sovUserpresent_(uint64(m.RoleId))
	n += 1 + sovUserpresent_(uint64(m.PartnerId))
	return n
}

func (m *IMSendPresentResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.ItemId))
	if m.MsgInfo != nil {
		l = m.MsgInfo.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.MemberContributionAdded))
	n += 1 + sovUserpresent_(uint64(m.Count))
	n += 1 + sovUserpresent_(uint64(m.CurTbeans))
	n += 1 + sovUserpresent_(uint64(m.ItemSource))
	n += 1 + sovUserpresent_(uint64(m.SourceId))
	n += 1 + sovUserpresent_(uint64(m.SourceRemain))
	n += 1 + sovUserpresent_(uint64(m.ExpireTime))
	return n
}

func (m *PresentTargetUserInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserpresent_(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovUserpresent_(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovUserpresent_(uint64(l))
	if m.UserProfile != nil {
		l = m.UserProfile.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	l = len(m.CustomText)
	n += 1 + l + sovUserpresent_(uint64(l))
	return n
}

func (m *BatchSendPresentReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.ItemId))
	n += 1 + sovUserpresent_(uint64(m.ChannelId))
	n += 1 + sovUserpresent_(uint64(m.Count))
	n += 1 + sovUserpresent_(uint64(m.SendSource))
	n += 1 + sovUserpresent_(uint64(m.ItemSource))
	n += 1 + sovUserpresent_(uint64(m.SourceId))
	n += 1 + sovUserpresent_(uint64(m.BatchType))
	n += 1 + sovUserpresent_(uint64(m.SendType))
	if m.DrawPresentPic != nil {
		l = m.DrawPresentPic.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovUserpresent_(uint64(e))
		}
	}
	return n
}

func (m *BatchSendPresentResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if m.MsgInfo != nil {
		l = m.MsgInfo.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.CurTbeans))
	n += 1 + sovUserpresent_(uint64(m.ItemSource))
	n += 1 + sovUserpresent_(uint64(m.SourceId))
	n += 1 + sovUserpresent_(uint64(m.SourceRemain))
	if len(m.TargetList) > 0 {
		for _, e := range m.TargetList {
			l = e.Size()
			n += 1 + l + sovUserpresent_(uint64(l))
		}
	}
	if m.ItemInfo != nil {
		l = m.ItemInfo.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.ExpireTime))
	return n
}

func (m *UserPresentDetail) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserpresent_(uint64(m.Uid))
	n += 1 + sovUserpresent_(uint64(m.FromUid))
	l = len(m.FromAccount)
	n += 1 + l + sovUserpresent_(uint64(l))
	l = len(m.FromName)
	n += 1 + l + sovUserpresent_(uint64(l))
	l = len(m.FromFaceMd5)
	n += 1 + l + sovUserpresent_(uint64(l))
	if m.ItemBriefConfig != nil {
		l = m.ItemBriefConfig.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.SendTime))
	n += 1 + sovUserpresent_(uint64(m.ItemCount))
	n += 1 + sovUserpresent_(uint64(m.ItemId))
	n += 1 + sovUserpresent_(uint64(m.Charm))
	n += 1 + sovUserpresent_(uint64(m.SendSource))
	n += 1 + sovUserpresent_(uint64(m.FromSex))
	n += 1 + sovUserpresent_(uint64(m.SendMethod))
	if m.UserProfile != nil {
		l = m.UserProfile.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if m.UserUkwInfo != nil {
		l = m.UserUkwInfo.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 2 + sovUserpresent_(uint64(m.BusinessType))
	return n
}

func (m *UserPresentSendDetail) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserpresent_(uint64(m.Uid))
	n += 1 + sovUserpresent_(uint64(m.ToUid))
	l = len(m.ToAccount)
	n += 1 + l + sovUserpresent_(uint64(l))
	l = len(m.ToName)
	n += 1 + l + sovUserpresent_(uint64(l))
	l = len(m.ToFaceMd5)
	n += 1 + l + sovUserpresent_(uint64(l))
	if m.ItemBriefConfig != nil {
		l = m.ItemBriefConfig.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.SendTime))
	n += 1 + sovUserpresent_(uint64(m.ItemCount))
	n += 1 + sovUserpresent_(uint64(m.ItemId))
	n += 1 + sovUserpresent_(uint64(m.Rich))
	n += 1 + sovUserpresent_(uint64(m.SendSource))
	n += 1 + sovUserpresent_(uint64(m.ToSex))
	n += 1 + sovUserpresent_(uint64(m.SendMethod))
	if m.UserProfile != nil {
		l = m.UserProfile.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if m.UserUkwInfo != nil {
		l = m.UserUkwInfo.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 2 + sovUserpresent_(uint64(m.BusinessType))
	n += 2 + sovUserpresent_(uint64(m.TargetType))
	l = len(m.ToHeadImageUrl)
	n += 2 + l + sovUserpresent_(uint64(l))
	return n
}

func (m *GetUserPresentInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.TargetUid))
	return n
}

func (m *GetUserPresentInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.TotalValue))
	n += 1 + sovUserpresent_(uint64(m.TotalCount))
	if len(m.PresentCountList) > 0 {
		for _, e := range m.PresentCountList {
			l = e.Size()
			n += 1 + l + sovUserpresent_(uint64(l))
		}
	}
	n += 1 + sovUserpresent_(uint64(m.TargetUid))
	return n
}

func (m *GetUserPresentDetailListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.DetailType))
	return n
}

func (m *GetUserPresentDetailListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if len(m.PresentDetailList) > 0 {
		for _, e := range m.PresentDetailList {
			l = e.Size()
			n += 1 + l + sovUserpresent_(uint64(l))
		}
	}
	if len(m.PresentSendDetailList) > 0 {
		for _, e := range m.PresentSendDetailList {
			l = e.Size()
			n += 1 + l + sovUserpresent_(uint64(l))
		}
	}
	return n
}

func (m *GetPresentConfigListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	return n
}

func (m *GetPresentConfigListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if len(m.ConfigList) > 0 {
		for _, e := range m.ConfigList {
			l = e.Size()
			n += 1 + l + sovUserpresent_(uint64(l))
		}
	}
	n += 1 + sovUserpresent_(uint64(m.LastUpdateTime))
	return n
}

func (m *GetPresentConfigByIdReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.ItemId))
	return n
}

func (m *GetPresentConfigByIdResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if m.ItemConfig != nil {
		l = m.ItemConfig.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	return n
}

func (m *PresentSendItemInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserpresent_(uint64(m.ItemId))
	n += 1 + sovUserpresent_(uint64(m.Count))
	n += 1 + sovUserpresent_(uint64(m.ShowEffect))
	n += 1 + sovUserpresent_(uint64(m.ShowEffectV2))
	n += 1 + sovUserpresent_(uint64(m.FlowId))
	n += 2
	n += 2
	n += 1 + sovUserpresent_(uint64(m.SendType))
	if m.DrawPresentPic != nil {
		l = m.DrawPresentPic.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.DynamicTemplateId))
	n += 2
	n += 2
	n += 1 + sovUserpresent_(uint64(m.SurpriseCount))
	l = len(m.CustomTextJson)
	n += 1 + l + sovUserpresent_(uint64(l))
	n += 2
	l = len(m.PreEffectText)
	n += 2 + l + sovUserpresent_(uint64(l))
	return n
}

func (m *PresentSendMsg) Size() (n int) {
	var l int
	_ = l
	if m.ItemInfo != nil {
		l = m.ItemInfo.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.SendTime))
	n += 1 + sovUserpresent_(uint64(m.ChannelId))
	n += 1 + sovUserpresent_(uint64(m.SendUid))
	l = len(m.SendAccount)
	n += 1 + l + sovUserpresent_(uint64(l))
	l = len(m.SendNickname)
	n += 1 + l + sovUserpresent_(uint64(l))
	n += 1 + sovUserpresent_(uint64(m.TargetUid))
	l = len(m.TargetAccount)
	n += 1 + l + sovUserpresent_(uint64(l))
	l = len(m.TargetNickname)
	n += 1 + l + sovUserpresent_(uint64(l))
	l = len(m.ExtendJson)
	n += 1 + l + sovUserpresent_(uint64(l))
	if m.FromUserProfile != nil {
		l = m.FromUserProfile.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if m.ToUserProfile != nil {
		l = m.ToUserProfile.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 2
	return n
}

func (m *PresentBatchTargetInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserpresent_(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovUserpresent_(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovUserpresent_(uint64(l))
	l = len(m.ExtendJson)
	n += 1 + l + sovUserpresent_(uint64(l))
	if m.UserProfile != nil {
		l = m.UserProfile.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	l = len(m.CustomText)
	n += 1 + l + sovUserpresent_(uint64(l))
	return n
}

func (m *PresentBatchInfoMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserpresent_(uint64(m.ItemId))
	n += 1 + sovUserpresent_(uint64(m.TotalItemCount))
	n += 1 + sovUserpresent_(uint64(m.BatchType))
	n += 1 + sovUserpresent_(uint64(m.SendTime))
	n += 1 + sovUserpresent_(uint64(m.ChannelId))
	n += 1 + sovUserpresent_(uint64(m.SendUid))
	l = len(m.SendAccount)
	n += 1 + l + sovUserpresent_(uint64(l))
	l = len(m.SendNickname)
	n += 1 + l + sovUserpresent_(uint64(l))
	l = len(m.ExtendJson)
	n += 1 + l + sovUserpresent_(uint64(l))
	if len(m.TargetList) > 0 {
		for _, e := range m.TargetList {
			l = e.Size()
			n += 1 + l + sovUserpresent_(uint64(l))
		}
	}
	if m.ItemInfo != nil {
		l = m.ItemInfo.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if m.FromUserProfile != nil {
		l = m.FromUserProfile.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 2
	return n
}

func (m *GetPresentFlowConfigListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.LastUpdateTime))
	return n
}

func (m *GetPresentFlowConfigListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if len(m.ConfigList) > 0 {
		for _, e := range m.ConfigList {
			l = e.Size()
			n += 1 + l + sovUserpresent_(uint64(l))
		}
	}
	n += 1 + sovUserpresent_(uint64(m.LastUpdateTime))
	return n
}

func (m *GetPresentFlowConfigByIdReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.FlowId))
	return n
}

func (m *GetPresentFlowConfigByIdResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if m.FlowConfig != nil {
		l = m.FlowConfig.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	return n
}

func (m *DrawPresentPara) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserpresent_(uint64(m.ItemId))
	l = len(m.ImgUrl)
	n += 1 + l + sovUserpresent_(uint64(l))
	return n
}

func (m *GetDrawPresentParaReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	return n
}

func (m *GetDrawPresentParaResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if len(m.ParaList) > 0 {
		for _, e := range m.ParaList {
			l = e.Size()
			n += 1 + l + sovUserpresent_(uint64(l))
		}
	}
	return n
}

func (m *NamingPresentConfig) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserpresent_(uint64(m.Uid))
	n += 1 + sovUserpresent_(uint64(m.GiftId))
	l = len(m.NamingContent)
	n += 1 + l + sovUserpresent_(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovUserpresent_(uint64(l))
	return n
}

func (m *GetNamingPresentConfigListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	return n
}

func (m *GetNamingPresentConfigListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if len(m.ConfigList) > 0 {
		for _, e := range m.ConfigList {
			l = e.Size()
			n += 1 + l + sovUserpresent_(uint64(l))
		}
	}
	return n
}

func (m *GetPresentDynamicTemplateConfigReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	return n
}

func (m *GetPresentDynamicTemplateConfigResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if m.Configs != nil {
		l = m.Configs.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	return n
}

func (m *GetImPresentItemIdListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	return n
}

func (m *GetImPresentItemIdListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if len(m.ItemIdList) > 0 {
		for _, e := range m.ItemIdList {
			n += 1 + sovUserpresent_(uint64(e))
		}
	}
	return n
}

func (m *GetStangerImItemIdListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	return n
}

func (m *GetStangerImItemIdListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if len(m.ItemIdList) > 0 {
		for _, e := range m.ItemIdList {
			n += 1 + sovUserpresent_(uint64(e))
		}
	}
	return n
}

func (m *FansPresentMessage) Size() (n int) {
	var l int
	_ = l
	if len(m.Account) > 0 {
		for _, s := range m.Account {
			l = len(s)
			n += 1 + l + sovUserpresent_(uint64(l))
		}
	}
	l = len(m.PresentName)
	n += 1 + l + sovUserpresent_(uint64(l))
	l = len(m.PresentIcon)
	n += 1 + l + sovUserpresent_(uint64(l))
	return n
}

func (m *PresentBoxInfo) Size() (n int) {
	var l int
	_ = l
	if m.ItemMsg != nil {
		l = m.ItemMsg.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if m.BoxDetail != nil {
		l = m.BoxDetail.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	return n
}

func (m *PresentBoxDetail) Size() (n int) {
	var l int
	_ = l
	l = len(m.BoxId)
	n += 1 + l + sovUserpresent_(uint64(l))
	if m.FromUserProfile != nil {
		l = m.FromUserProfile.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if m.ToUserProfile != nil {
		l = m.ToUserProfile.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	n += 1 + sovUserpresent_(uint64(m.ItemId))
	l = len(m.ItemName)
	n += 1 + l + sovUserpresent_(uint64(l))
	n += 1 + sovUserpresent_(uint64(m.SendTime))
	l = len(m.ExtendJson)
	n += 1 + l + sovUserpresent_(uint64(l))
	n += 1 + sovUserpresent_(uint64(m.DelayTime))
	n += 2
	return n
}

func (m *UnpackPresentBoxReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	l = len(m.BoxId)
	n += 1 + l + sovUserpresent_(uint64(l))
	n += 1 + sovUserpresent_(uint64(m.ChannelId))
	return n
}

func (m *UnpackPresentBoxResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	if m.BoxInfo != nil {
		l = m.BoxInfo.Size()
		n += 1 + l + sovUserpresent_(uint64(l))
	}
	return n
}

func (m *PresentBoxOpenMsg) Size() (n int) {
	var l int
	_ = l
	l = len(m.BoxId)
	n += 1 + l + sovUserpresent_(uint64(l))
	return n
}

func (m *BackpackIntimatePresentContractPush) Size() (n int) {
	var l int
	_ = l
	l = len(m.Content)
	n += 1 + l + sovUserpresent_(uint64(l))
	return n
}

func sovUserpresent_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozUserpresent_(x uint64) (n int) {
	return sovUserpresent_(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *PresentPoint) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PresentPoint: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PresentPoint: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt2.Errorf("proto: wrong wireType = %d for field X", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 4
			v = uint32(dAtA[iNdEx-4])
			v |= uint32(dAtA[iNdEx-3]) << 8
			v |= uint32(dAtA[iNdEx-2]) << 16
			v |= uint32(dAtA[iNdEx-1]) << 24
			m.X = float32(math4.Float32frombits(v))
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 5 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Y", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 4
			v = uint32(dAtA[iNdEx-4])
			v |= uint32(dAtA[iNdEx-3]) << 8
			v |= uint32(dAtA[iNdEx-2]) << 16
			v |= uint32(dAtA[iNdEx-1]) << 24
			m.Y = float32(math4.Float32frombits(v))
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("x")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("y")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresentLine) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PresentLine: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PresentLine: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PointList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PointList = append(m.PointList, &PresentPoint{})
			if err := m.PointList[len(m.PointList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DrawPresentPicture) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DrawPresentPicture: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DrawPresentPicture: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LineList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LineList = append(m.LineList, &PresentLine{})
			if err := m.LineList[len(m.LineList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendPresentReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendPresentReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendPresentReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigUpdateTime", wireType)
			}
			m.ConfigUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConfigUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendSource", wireType)
			}
			m.SendSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendSource |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemSource", wireType)
			}
			m.ItemSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemSource |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendType", wireType)
			}
			m.SendType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DrawPresentPic", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.DrawPresentPic == nil {
				m.DrawPresentPic = &DrawPresentPicture{}
			}
			if err := m.DrawPresentPic.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("config_update_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendPresentResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendPresentResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendPresentResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MsgInfo == nil {
				m.MsgInfo = &PresentSendMsg{}
			}
			if err := m.MsgInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberContributionAdded", wireType)
			}
			m.MemberContributionAdded = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberContributionAdded |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurTbeans", wireType)
			}
			m.CurTbeans = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurTbeans |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemSource", wireType)
			}
			m.ItemSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemSource |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceRemain", wireType)
			}
			m.SourceRemain = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceRemain |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BoxDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BoxDetail == nil {
				m.BoxDetail = &PresentBoxDetail{}
			}
			if err := m.BoxDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IMSendPresentReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IMSendPresentReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IMSendPresentReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendSource", wireType)
			}
			m.SendSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendSource |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemSource", wireType)
			}
			m.ItemSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemSource |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendType", wireType)
			}
			m.SendType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresentTextType", wireType)
			}
			m.PresentTextType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PresentTextType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PreEffectText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PreEffectText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserpresent_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TargetUidList = append(m.TargetUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserpresent_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUserpresent_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUserpresent_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TargetUidList = append(m.TargetUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUidList", wireType)
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BusinessType", wireType)
			}
			m.BusinessType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BusinessType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetType", wireType)
			}
			m.TargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoleId", wireType)
			}
			m.RoleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PartnerId", wireType)
			}
			m.PartnerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PartnerId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IMSendPresentResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IMSendPresentResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IMSendPresentResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MsgInfo == nil {
				m.MsgInfo = &PresentSendMsg{}
			}
			if err := m.MsgInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberContributionAdded", wireType)
			}
			m.MemberContributionAdded = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberContributionAdded |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurTbeans", wireType)
			}
			m.CurTbeans = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurTbeans |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemSource", wireType)
			}
			m.ItemSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemSource |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceRemain", wireType)
			}
			m.SourceRemain = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceRemain |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresentTargetUserInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PresentTargetUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PresentTargetUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserProfile == nil {
				m.UserProfile = &ga.UserProfile{}
			}
			if err := m.UserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CustomText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CustomText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchSendPresentReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchSendPresentReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchSendPresentReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendSource", wireType)
			}
			m.SendSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendSource |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemSource", wireType)
			}
			m.ItemSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemSource |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BatchType", wireType)
			}
			m.BatchType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BatchType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendType", wireType)
			}
			m.SendType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DrawPresentPic", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.DrawPresentPic == nil {
				m.DrawPresentPic = &DrawPresentPicture{}
			}
			if err := m.DrawPresentPic.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserpresent_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserpresent_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUserpresent_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUserpresent_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("send_source")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_source")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("batch_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchSendPresentResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchSendPresentResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchSendPresentResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MsgInfo == nil {
				m.MsgInfo = &PresentBatchInfoMsg{}
			}
			if err := m.MsgInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurTbeans", wireType)
			}
			m.CurTbeans = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurTbeans |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemSource", wireType)
			}
			m.ItemSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemSource |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceRemain", wireType)
			}
			m.SourceRemain = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceRemain |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetList = append(m.TargetList, &PresentTargetUserInfo{})
			if err := m.TargetList[len(m.TargetList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemInfo == nil {
				m.ItemInfo = &PresentSendItemInfo{}
			}
			if err := m.ItemInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserPresentDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserPresentDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserPresentDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromUid", wireType)
			}
			m.FromUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FromAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FromName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromFaceMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FromFaceMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemBriefConfig", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemBriefConfig == nil {
				m.ItemBriefConfig = &ga.PresentItemBriefConfig{}
			}
			if err := m.ItemBriefConfig.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendTime", wireType)
			}
			m.SendTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Charm", wireType)
			}
			m.Charm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Charm |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendSource", wireType)
			}
			m.SendSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendSource |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromSex", wireType)
			}
			m.FromSex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromSex |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendMethod", wireType)
			}
			m.SendMethod = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendMethod |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserProfile == nil {
				m.UserProfile = &ga.UserProfile{}
			}
			if err := m.UserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserUkwInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserUkwInfo == nil {
				m.UserUkwInfo = &ga.UserUKWInfo{}
			}
			if err := m.UserUkwInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 16:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BusinessType", wireType)
			}
			m.BusinessType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BusinessType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("from_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("from_account")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("from_name")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("from_face_md5")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_brief_config")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("send_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserPresentSendDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserPresentSendDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserPresentSendDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToUid", wireType)
			}
			m.ToUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ToAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ToName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToFaceMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ToFaceMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemBriefConfig", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemBriefConfig == nil {
				m.ItemBriefConfig = &ga.PresentItemBriefConfig{}
			}
			if err := m.ItemBriefConfig.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendTime", wireType)
			}
			m.SendTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rich", wireType)
			}
			m.Rich = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rich |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendSource", wireType)
			}
			m.SendSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendSource |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToSex", wireType)
			}
			m.ToSex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToSex |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendMethod", wireType)
			}
			m.SendMethod = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendMethod |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserProfile == nil {
				m.UserProfile = &ga.UserProfile{}
			}
			if err := m.UserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserUkwInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserUkwInfo == nil {
				m.UserUkwInfo = &ga.UserUKWInfo{}
			}
			if err := m.UserUkwInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 16:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BusinessType", wireType)
			}
			m.BusinessType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BusinessType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 17:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetType", wireType)
			}
			m.TargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 18:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToHeadImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ToHeadImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("to_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("to_account")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("to_name")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("to_face_md5")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_brief_config")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("send_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserPresentInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserPresentInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserPresentInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserPresentInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserPresentInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserPresentInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalValue", wireType)
			}
			m.TotalValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalCount", wireType)
			}
			m.TotalCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresentCountList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PresentCountList = append(m.PresentCountList, &ga.PresentCount{})
			if err := m.PresentCountList[len(m.PresentCountList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("total_value")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("total_count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserPresentDetailListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserPresentDetailListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserPresentDetailListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DetailType", wireType)
			}
			m.DetailType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DetailType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserPresentDetailListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserPresentDetailListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserPresentDetailListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresentDetailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PresentDetailList = append(m.PresentDetailList, &UserPresentDetail{})
			if err := m.PresentDetailList[len(m.PresentDetailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresentSendDetailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PresentSendDetailList = append(m.PresentSendDetailList, &UserPresentSendDetail{})
			if err := m.PresentSendDetailList[len(m.PresentSendDetailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentConfigListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentConfigListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentConfigListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentConfigListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentConfigListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentConfigListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ConfigList = append(m.ConfigList, &ga.PresentItemConfig{})
			if err := m.ConfigList[len(m.ConfigList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("last_update_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentConfigByIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentConfigByIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentConfigByIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentConfigByIdResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentConfigByIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentConfigByIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemConfig", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemConfig == nil {
				m.ItemConfig = &ga.PresentItemConfig{}
			}
			if err := m.ItemConfig.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresentSendItemInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PresentSendItemInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PresentSendItemInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShowEffect", wireType)
			}
			m.ShowEffect = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ShowEffect |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShowEffectV2", wireType)
			}
			m.ShowEffectV2 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ShowEffectV2 |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FlowId", wireType)
			}
			m.FlowId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FlowId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsBatch", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsBatch = bool(v != 0)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShowBatchEffect", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShowBatchEffect = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendType", wireType)
			}
			m.SendType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DrawPresentPic", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.DrawPresentPic == nil {
				m.DrawPresentPic = &DrawPresentPicture{}
			}
			if err := m.DrawPresentPic.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DynamicTemplateId", wireType)
			}
			m.DynamicTemplateId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DynamicTemplateId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsVisibleToSender", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsVisibleToSender = bool(v != 0)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsShowSurprise", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsShowSurprise = bool(v != 0)
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SurpriseCount", wireType)
			}
			m.SurpriseCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SurpriseCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CustomTextJson", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CustomTextJson = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShowImPreEffect", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShowImPreEffect = bool(v != 0)
		case 16:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PreEffectText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PreEffectText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresentSendMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PresentSendMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PresentSendMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemInfo == nil {
				m.ItemInfo = &PresentSendItemInfo{}
			}
			if err := m.ItemInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendTime", wireType)
			}
			m.SendTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendUid", wireType)
			}
			m.SendUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SendAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SendNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtendJson", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtendJson = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromUserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FromUserProfile == nil {
				m.FromUserProfile = &ga.UserProfile{}
			}
			if err := m.FromUserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToUserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ToUserProfile == nil {
				m.ToUserProfile = &ga.UserProfile{}
			}
			if err := m.ToUserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlyShowMsg", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.OnlyShowMsg = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_info")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("send_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("send_uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("send_account")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("send_nickname")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_uid")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_account")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_nickname")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresentBatchTargetInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PresentBatchTargetInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PresentBatchTargetInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtendJson", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtendJson = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserProfile == nil {
				m.UserProfile = &ga.UserProfile{}
			}
			if err := m.UserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CustomText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CustomText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("nickname")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresentBatchInfoMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PresentBatchInfoMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PresentBatchInfoMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalItemCount", wireType)
			}
			m.TotalItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BatchType", wireType)
			}
			m.BatchType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BatchType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendTime", wireType)
			}
			m.SendTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendUid", wireType)
			}
			m.SendUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SendAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SendNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtendJson", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtendJson = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetList = append(m.TargetList, &PresentBatchTargetInfo{})
			if err := m.TargetList[len(m.TargetList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemInfo == nil {
				m.ItemInfo = &PresentSendItemInfo{}
			}
			if err := m.ItemInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromUserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FromUserProfile == nil {
				m.FromUserProfile = &ga.UserProfile{}
			}
			if err := m.FromUserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsMulti", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsMulti = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("total_item_count")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("batch_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("send_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("send_uid")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("send_account")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("send_nickname")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentFlowConfigListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentFlowConfigListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentFlowConfigListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("last_update_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentFlowConfigListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentFlowConfigListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentFlowConfigListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ConfigList = append(m.ConfigList, &ga.PresentFlowConfig{})
			if err := m.ConfigList[len(m.ConfigList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("last_update_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentFlowConfigByIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentFlowConfigByIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentFlowConfigByIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FlowId", wireType)
			}
			m.FlowId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FlowId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("flow_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentFlowConfigByIdResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentFlowConfigByIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentFlowConfigByIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FlowConfig", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FlowConfig == nil {
				m.FlowConfig = &ga.PresentFlowConfig{}
			}
			if err := m.FlowConfig.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DrawPresentPara) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DrawPresentPara: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DrawPresentPara: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("img_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDrawPresentParaReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDrawPresentParaReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDrawPresentParaReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDrawPresentParaResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDrawPresentParaResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDrawPresentParaResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParaList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ParaList = append(m.ParaList, &DrawPresentPara{})
			if err := m.ParaList[len(m.ParaList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NamingPresentConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NamingPresentConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NamingPresentConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftId", wireType)
			}
			m.GiftId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NamingContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NamingContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetNamingPresentConfigListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetNamingPresentConfigListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetNamingPresentConfigListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetNamingPresentConfigListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetNamingPresentConfigListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetNamingPresentConfigListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ConfigList = append(m.ConfigList, &NamingPresentConfig{})
			if err := m.ConfigList[len(m.ConfigList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentDynamicTemplateConfigReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentDynamicTemplateConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentDynamicTemplateConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentDynamicTemplateConfigResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentDynamicTemplateConfigResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentDynamicTemplateConfigResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Configs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Configs == nil {
				m.Configs = &ga.PresentTemplateConfigs{}
			}
			if err := m.Configs.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetImPresentItemIdListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetImPresentItemIdListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetImPresentItemIdListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetImPresentItemIdListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetImPresentItemIdListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetImPresentItemIdListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserpresent_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ItemIdList = append(m.ItemIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserpresent_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUserpresent_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUserpresent_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ItemIdList = append(m.ItemIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetStangerImItemIdListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetStangerImItemIdListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetStangerImItemIdListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetStangerImItemIdListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetStangerImItemIdListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetStangerImItemIdListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserpresent_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ItemIdList = append(m.ItemIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserpresent_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUserpresent_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUserpresent_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ItemIdList = append(m.ItemIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FansPresentMessage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FansPresentMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FansPresentMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = append(m.Account, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresentName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PresentName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresentIcon", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PresentIcon = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("present_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("present_icon")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresentBoxInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PresentBoxInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PresentBoxInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemMsg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemMsg == nil {
				m.ItemMsg = &PresentSendMsg{}
			}
			if err := m.ItemMsg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BoxDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BoxDetail == nil {
				m.BoxDetail = &PresentBoxDetail{}
			}
			if err := m.BoxDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_msg")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("box_detail")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresentBoxDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PresentBoxDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PresentBoxDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BoxId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BoxId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromUserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FromUserProfile == nil {
				m.FromUserProfile = &ga.UserProfile{}
			}
			if err := m.FromUserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToUserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ToUserProfile == nil {
				m.ToUserProfile = &ga.UserProfile{}
			}
			if err := m.ToUserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendTime", wireType)
			}
			m.SendTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtendJson", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtendJson = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DelayTime", wireType)
			}
			m.DelayTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DelayTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsVisibleToSender", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsVisibleToSender = bool(v != 0)
			hasFields[0] |= uint64(0x00000100)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("box_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("from_user_profile")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("to_user_profile")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item_name")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("send_time")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("extend_json")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("delay_time")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_visible_to_sender")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnpackPresentBoxReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UnpackPresentBoxReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UnpackPresentBoxReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BoxId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BoxId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("box_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnpackPresentBoxResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UnpackPresentBoxResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UnpackPresentBoxResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BoxInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BoxInfo == nil {
				m.BoxInfo = &PresentBoxInfo{}
			}
			if err := m.BoxInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("box_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresentBoxOpenMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PresentBoxOpenMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PresentBoxOpenMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BoxId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BoxId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("box_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BackpackIntimatePresentContractPush) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BackpackIntimatePresentContractPush: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BackpackIntimatePresentContractPush: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserpresent_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserpresent_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserpresent_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipUserpresent_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUserpresent_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserpresent_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthUserpresent_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowUserpresent_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipUserpresent_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthUserpresent_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUserpresent_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("userpresent/userpresent_.proto", fileDescriptorUserpresent_) }

var fileDescriptorUserpresent_ = []byte{
	// 3421 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5b, 0xcd, 0x6f, 0xe3, 0xd6,
	0xb5, 0x1f, 0x7d, 0x4b, 0x47, 0xfe, 0xa0, 0xe9, 0xf1, 0x8c, 0xe6, 0xcb, 0xe3, 0xa1, 0xdf, 0x24,
	0x8e, 0x93, 0xe7, 0xc9, 0xf3, 0xcb, 0xe4, 0xbd, 0x20, 0x0f, 0x49, 0x24, 0x59, 0xf2, 0xf0, 0x8d,
	0x24, 0xbb, 0x94, 0x3c, 0xd3, 0x29, 0x8a, 0x32, 0xb4, 0x78, 0x2d, 0xb3, 0x23, 0x91, 0x0a, 0x49,
	0xc5, 0x36, 0xd0, 0xa2, 0xcd, 0xa6, 0xab, 0x16, 0x48, 0x97, 0xfd, 0x23, 0xba, 0xe8, 0xaa, 0xbb,
	0x76, 0xd3, 0x45, 0x16, 0x59, 0x14, 0x28, 0x90, 0xee, 0x8a, 0x36, 0xdd, 0x77, 0x55, 0x64, 0xd5,
	0x45, 0x71, 0xcf, 0x25, 0xa9, 0x4b, 0x8a, 0x1a, 0x5b, 0xce, 0x00, 0x45, 0x80, 0xee, 0x34, 0xe7,
	0x9c, 0x7b, 0xee, 0xbd, 0xe7, 0xeb, 0xfe, 0xce, 0xa1, 0x07, 0x56, 0x47, 0x0e, 0xb1, 0x87, 0x36,
	0x71, 0x88, 0xe9, 0x3e, 0xe0, 0x7e, 0xab, 0x5b, 0x43, 0xdb, 0x72, 0x2d, 0x71, 0xa1, 0xa7, 0x6d,
	0x71, 0xe4, 0x9b, 0xf3, 0x3d, 0x4d, 0x3d, 0xd4, 0x1c, 0xc2, 0xd8, 0xd2, 0xdb, 0x30, 0xb7, 0xcf,
	0x38, 0xfb, 0x96, 0x61, 0xba, 0xa2, 0x08, 0x89, 0xd3, 0x52, 0x62, 0x2d, 0xb9, 0x91, 0xac, 0xa4,
	0x3f, 0xfb, 0xd3, 0xdd, 0x2b, 0x4a, 0xe2, 0x94, 0xd2, 0xce, 0x4a, 0x49, 0x9e, 0x76, 0x26, 0x19,
	0x50, 0xf4, 0xd6, 0x35, 0x0c, 0x93, 0x88, 0x77, 0x20, 0x67, 0xb8, 0x64, 0xa0, 0x1a, 0x3a, 0x2e,
	0x9e, 0xf7, 0x04, 0xb3, 0x94, 0x28, 0xeb, 0xe2, 0xbb, 0x00, 0x43, 0xaa, 0x5e, 0xed, 0x1b, 0x8e,
	0x5b, 0x4a, 0xae, 0xa5, 0x36, 0x8a, 0xdb, 0xb7, 0xb7, 0xc2, 0x27, 0xdb, 0xe2, 0xcf, 0xa1, 0x14,
	0x50, 0xbe, 0x61, 0x38, 0xae, 0xd4, 0x02, 0x71, 0xc7, 0xd6, 0x4e, 0x7c, 0xb6, 0xd1, 0x75, 0x47,
	0x36, 0x11, 0xff, 0x17, 0x0a, 0x7d, 0xc3, 0x24, 0x4c, 0x63, 0x02, 0x35, 0xde, 0x9a, 0xa2, 0x91,
	0x9e, 0x50, 0xc9, 0x53, 0x69, 0xd4, 0xf7, 0x87, 0x14, 0x2c, 0xb4, 0x89, 0xa9, 0x7b, 0x5c, 0x85,
	0x7c, 0x24, 0xbe, 0x02, 0x79, 0x6a, 0x13, 0xd5, 0x26, 0x1f, 0xe1, 0xf9, 0x8b, 0xdb, 0x45, 0xaa,
	0xab, 0xa2, 0x39, 0x44, 0x21, 0x1f, 0x29, 0xb9, 0x43, 0xf6, 0x43, 0x5c, 0x07, 0x70, 0x35, 0xbb,
	0x47, 0x5c, 0x75, 0x64, 0xe8, 0x68, 0x12, 0xff, 0xa6, 0x05, 0x46, 0x3f, 0x30, 0x74, 0xde, 0x16,
	0xa9, 0x18, 0x5b, 0xac, 0x03, 0x74, 0x8f, 0x35, 0xd3, 0x24, 0x7d, 0x2a, 0x91, 0x5e, 0x4b, 0x8c,
	0x75, 0x78, 0x74, 0x59, 0x17, 0xb7, 0x41, 0xec, 0x5a, 0xe6, 0x91, 0xd1, 0x53, 0x47, 0x43, 0x5d,
	0x73, 0x89, 0xea, 0x1a, 0x03, 0x52, 0xca, 0x70, 0xea, 0x04, 0xc6, 0x3f, 0x40, 0x76, 0xc7, 0x18,
	0x10, 0xf1, 0x26, 0x64, 0xba, 0xd6, 0xc8, 0x74, 0x4b, 0x59, 0x4e, 0x27, 0x23, 0x89, 0xf7, 0xa1,
	0xe8, 0x10, 0x53, 0x57, 0x1d, 0x6b, 0x64, 0x77, 0x49, 0x29, 0xc7, 0x49, 0x00, 0x65, 0xb4, 0x91,
	0x4e, 0xc5, 0xf0, 0xe8, 0x9e, 0x58, 0x9e, 0x17, 0xa3, 0x0c, 0x4f, 0xec, 0x1e, 0x14, 0x98, 0x04,
	0xbd, 0x41, 0x81, 0x13, 0xca, 0x33, 0xb2, 0xac, 0xa3, 0x08, 0xdd, 0xd0, 0x3d, 0x1b, 0x92, 0x12,
	0x84, 0x44, 0x88, 0xa9, 0x77, 0xce, 0x86, 0x44, 0x6c, 0x80, 0xa0, 0xdb, 0xda, 0x89, 0xea, 0x07,
	0xec, 0xd0, 0xe8, 0x96, 0x8a, 0x6b, 0x89, 0x8d, 0xe2, 0xb6, 0x14, 0x75, 0xe4, 0xa4, 0xff, 0x95,
	0x05, 0x3d, 0x44, 0x93, 0xbe, 0x4a, 0xc1, 0x62, 0xc8, 0xab, 0xce, 0x50, 0x7c, 0x0d, 0x0a, 0x9e,
	0x5b, 0x9d, 0xa1, 0xe7, 0xd7, 0xb9, 0xb1, 0x5f, 0x9d, 0xa1, 0x92, 0x3f, 0xf4, 0x7e, 0xf1, 0x4e,
	0x4b, 0xc6, 0x38, 0xed, 0x1d, 0xc8, 0x0f, 0x9c, 0x9e, 0x6a, 0x98, 0x47, 0x56, 0x29, 0x85, 0x67,
	0x5c, 0x9d, 0x12, 0x6c, 0xf4, 0x0c, 0x4d, 0xa7, 0xa7, 0xe4, 0x06, 0x4e, 0x4f, 0x36, 0x8f, 0x2c,
	0xf1, 0x03, 0xb8, 0x31, 0x20, 0x83, 0x43, 0x62, 0xab, 0x5d, 0xcb, 0x74, 0x6d, 0xe3, 0x70, 0xe4,
	0x1a, 0x96, 0xa9, 0x6a, 0xba, 0x4e, 0xc2, 0xee, 0xbf, 0xce, 0xc4, 0xaa, 0x9c, 0x54, 0x99, 0x0a,
	0x8d, 0x1d, 0x9b, 0x99, 0x74, 0x2c, 0x8d, 0xa6, 0x91, 0xad, 0xba, 0x87, 0x44, 0x33, 0x1d, 0xf4,
	0x7c, 0x3a, 0x88, 0xa6, 0x91, 0xdd, 0x41, 0x72, 0xd4, 0xad, 0xb9, 0x8b, 0xb8, 0x35, 0x1f, 0xeb,
	0xd6, 0xd7, 0x60, 0xde, 0x13, 0xb1, 0xc9, 0x40, 0x33, 0xcc, 0x90, 0xf7, 0xe7, 0x18, 0x4b, 0x41,
	0x0e, 0xdd, 0x94, 0x9c, 0x0e, 0x0d, 0xdb, 0x8b, 0x5d, 0x3e, 0x06, 0x80, 0x31, 0x30, 0x6a, 0xdf,
	0x07, 0x38, 0xb4, 0x4e, 0x55, 0x9d, 0xb8, 0x9a, 0xd1, 0xf7, 0xfc, 0xbf, 0x36, 0xc5, 0xb6, 0x15,
	0xeb, 0x74, 0x07, 0xe5, 0x94, 0xc2, 0xa1, 0xff, 0x53, 0xfa, 0x3c, 0x0d, 0x82, 0xdc, 0xfc, 0x17,
	0x26, 0x74, 0xe0, 0x9e, 0xf4, 0xb9, 0x79, 0x97, 0xb9, 0x58, 0xde, 0x65, 0x2f, 0xe2, 0xa0, 0xdc,
	0xf9, 0x79, 0x97, 0x8f, 0xcd, 0xbb, 0x37, 0x61, 0xc9, 0x4f, 0x39, 0x97, 0x9c, 0xba, 0x4c, 0x94,
	0xf7, 0xe3, 0xa2, 0xc7, 0xee, 0x90, 0x53, 0x17, 0x57, 0xbc, 0x01, 0x94, 0xa4, 0x92, 0xa3, 0x23,
	0xd2, 0x65, 0x8b, 0xd0, 0x9d, 0x05, 0x4f, 0x7e, 0x7e, 0x68, 0x93, 0x1a, 0xf2, 0xe8, 0x0a, 0xf1,
	0x15, 0x58, 0x1c, 0xdb, 0x94, 0xd5, 0xe7, 0xe2, 0x5a, 0x6a, 0x63, 0x5e, 0x99, 0x0f, 0x4c, 0x4a,
	0xeb, 0x30, 0x8d, 0xa5, 0xc3, 0x91, 0x63, 0x98, 0xc4, 0x71, 0xd8, 0x19, 0xe6, 0xf8, 0x58, 0xf2,
	0x59, 0x78, 0x80, 0xfb, 0x50, 0xf4, 0x54, 0xa2, 0xe0, 0x3c, 0x6f, 0x1f, 0xc6, 0x40, 0xb1, 0x3b,
	0x90, 0xb3, 0xad, 0x3e, 0x5a, 0x67, 0x81, 0x13, 0xc9, 0x52, 0x22, 0xab, 0xbc, 0x43, 0xcd, 0x76,
	0x4d, 0x62, 0x53, 0x89, 0x45, 0xbe, 0xf2, 0x7a, 0x74, 0x59, 0x97, 0x7e, 0x9d, 0x82, 0xa5, 0x48,
	0x38, 0xfd, 0xbb, 0x92, 0x7c, 0x13, 0x2a, 0x89, 0xf4, 0xbb, 0x04, 0xac, 0x78, 0xa6, 0xeb, 0xb0,
	0x40, 0x73, 0x88, 0x8d, 0x86, 0xbb, 0x06, 0xa9, 0x51, 0x04, 0x99, 0x50, 0x82, 0xb8, 0x0a, 0x39,
	0xad, 0xcb, 0x0c, 0x42, 0x5d, 0xe5, 0xc7, 0xb3, 0x4f, 0x14, 0x4b, 0x90, 0x36, 0xb5, 0x01, 0xc1,
	0xac, 0xf7, 0x99, 0x48, 0x11, 0xb7, 0x61, 0x8e, 0x7a, 0x4c, 0x1d, 0xda, 0xd6, 0x91, 0xd1, 0x27,
	0x68, 0xfd, 0xe2, 0xf6, 0x22, 0xf5, 0x24, 0xdd, 0x75, 0x9f, 0x91, 0x95, 0xe2, 0x68, 0xfc, 0x0f,
	0x7a, 0x8d, 0xee, 0xc8, 0x71, 0xad, 0x01, 0xcb, 0xa0, 0x0c, 0x97, 0x41, 0xc0, 0x18, 0x34, 0x7d,
	0xa4, 0xdf, 0xa6, 0x60, 0xb9, 0xa2, 0xb9, 0xdd, 0xe3, 0x4b, 0x96, 0xb4, 0x73, 0xe2, 0x2f, 0x0c,
	0x3f, 0x52, 0xf1, 0xf0, 0x83, 0x2b, 0x69, 0xc9, 0x73, 0x4b, 0x5a, 0xf2, 0x62, 0x25, 0x2d, 0x79,
	0xd9, 0x92, 0xb6, 0x0e, 0x70, 0x48, 0xed, 0xe1, 0xd7, 0x34, 0xae, 0x46, 0x23, 0x1d, 0x53, 0x3f,
	0x54, 0xf7, 0x0a, 0x17, 0xc6, 0x1b, 0x70, 0x59, 0xbc, 0x21, 0xde, 0x80, 0x7c, 0xa4, 0xbc, 0xe5,
	0x46, 0xac, 0xb0, 0x49, 0x9f, 0xa7, 0xe0, 0xea, 0xa4, 0x07, 0x67, 0xab, 0x22, 0xef, 0x71, 0x65,
	0x22, 0x89, 0x87, 0x5c, 0x9f, 0xf6, 0x28, 0xd2, 0x9d, 0x68, 0x98, 0x87, 0x6a, 0x45, 0x38, 0x9b,
	0x53, 0x17, 0xca, 0xe6, 0xf4, 0x45, 0xb2, 0x39, 0x73, 0xb1, 0x6c, 0xce, 0x4e, 0xcd, 0xe6, 0x7a,
	0x50, 0xcb, 0xd1, 0x76, 0x39, 0x84, 0xee, 0xf7, 0xa7, 0x5c, 0x2e, 0x9c, 0xc8, 0x7e, 0xb1, 0xc7,
	0xe7, 0xe3, 0x03, 0x28, 0xb0, 0x38, 0xa7, 0x26, 0xca, 0xbf, 0xd0, 0x44, 0xd4, 0x0f, 0x32, 0x0d,
	0x7f, 0xaa, 0x23, 0x6f, 0x78, 0xbf, 0xa2, 0x75, 0xa5, 0x30, 0xa5, 0xae, 0x7c, 0x9a, 0x81, 0x25,
	0x96, 0xd4, 0xa8, 0x8c, 0xc1, 0x8e, 0xa9, 0x35, 0xe5, 0x2e, 0xe4, 0x8f, 0x6c, 0x6b, 0x30, 0x81,
	0x27, 0x72, 0x94, 0x4a, 0xd1, 0xc4, 0xab, 0x30, 0x87, 0x02, 0x7e, 0xe5, 0xe1, 0x8b, 0x4b, 0x91,
	0x72, 0xca, 0x5e, 0xf5, 0xb9, 0x07, 0x05, 0x14, 0xc4, 0x12, 0x94, 0xe6, 0xa4, 0x70, 0x83, 0x16,
	0x2d, 0x43, 0x1b, 0x30, 0x8f, 0x22, 0x47, 0x5a, 0x97, 0xa8, 0x03, 0xfd, 0x21, 0x66, 0x63, 0x48,
	0x59, 0x5d, 0xeb, 0x92, 0xa6, 0xfe, 0x50, 0xac, 0xc3, 0x12, 0x5a, 0xeb, 0xd0, 0x36, 0xc8, 0x91,
	0xca, 0x7a, 0x07, 0x4c, 0xca, 0xe2, 0xf6, 0x4d, 0x6a, 0x35, 0xef, 0x72, 0xd4, 0x4a, 0x15, 0x2a,
	0x52, 0x45, 0x09, 0x65, 0xd1, 0x08, 0x13, 0xc6, 0x79, 0x46, 0x2d, 0x96, 0xe3, 0xee, 0xc7, 0xf2,
	0x8c, 0x22, 0xba, 0x75, 0xc0, 0xe0, 0x51, 0xd9, 0xf5, 0xf8, 0xea, 0x8f, 0x0e, 0xab, 0xe2, 0xe5,
	0xb8, 0x2a, 0xc5, 0xdb, 0x9d, 0xc7, 0x54, 0xc7, 0x9a, 0x3d, 0x08, 0x15, 0x7b, 0x46, 0x8a, 0x16,
	0xa0, 0xe2, 0x14, 0x4c, 0xe5, 0x3b, 0xc2, 0x21, 0xa7, 0x88, 0x2c, 0x32, 0xbc, 0x23, 0xda, 0xe4,
	0x34, 0xd0, 0x33, 0x20, 0xee, 0xb1, 0xa5, 0x23, 0xa8, 0xc8, 0xf0, 0x7a, 0x9a, 0x48, 0x9f, 0x28,
	0xf5, 0x0b, 0x17, 0x28, 0xf5, 0xff, 0x0d, 0xf3, 0xb8, 0x66, 0xf4, 0xfc, 0x84, 0xc5, 0xe7, 0x62,
	0x78, 0xd1, 0xc1, 0xe3, 0xa7, 0x18, 0x8b, 0xb8, 0xe8, 0xe0, 0xf9, 0x09, 0x86, 0xe3, 0x04, 0x1e,
	0x12, 0xa6, 0xe1, 0x21, 0xe9, 0x8b, 0x0c, 0xac, 0x70, 0x21, 0x49, 0xe3, 0xfb, 0x9c, 0xb0, 0xbc,
	0x05, 0x59, 0xd7, 0x9a, 0x08, 0xca, 0x8c, 0x6b, 0xd1, 0x90, 0xa4, 0x28, 0xd8, 0x8a, 0x0d, 0xc8,
	0x82, 0x6b, 0xf9, 0xe1, 0x78, 0x07, 0x72, 0xae, 0x35, 0x19, 0x8c, 0x59, 0xd7, 0xc2, 0x50, 0xfc,
	0x0f, 0x28, 0xba, 0x56, 0x7c, 0x20, 0x16, 0x5c, 0xeb, 0x1b, 0x1a, 0x86, 0x25, 0x48, 0xdb, 0x46,
	0xf7, 0x38, 0x14, 0x85, 0x48, 0xb9, 0x68, 0x10, 0x32, 0xb3, 0x47, 0x43, 0x30, 0xe3, 0x5a, 0xdf,
	0xec, 0x00, 0x8c, 0x02, 0xf2, 0xa5, 0x29, 0x80, 0xfc, 0x01, 0x2c, 0xb9, 0x96, 0x7a, 0x4c, 0x34,
	0x5d, 0x35, 0x06, 0x5a, 0x8f, 0xa8, 0x23, 0xbb, 0x5f, 0x12, 0x39, 0xe0, 0xb3, 0xe0, 0x5a, 0x8f,
	0x88, 0xa6, 0xcb, 0x94, 0x79, 0x60, 0xf7, 0x25, 0x1d, 0x56, 0x76, 0x59, 0xbd, 0xf7, 0x23, 0x81,
	0x9e, 0xf2, 0x25, 0x37, 0x74, 0xd2, 0x57, 0x09, 0xb8, 0x16, 0xb7, 0xcd, 0x6c, 0x4f, 0x34, 0xb5,
	0x81, 0xe5, 0x6a, 0x7d, 0xf5, 0x63, 0xad, 0x3f, 0x22, 0xa1, 0xbd, 0x00, 0x19, 0x4f, 0x28, 0x7d,
	0x2c, 0x36, 0xce, 0xae, 0xb0, 0x18, 0x8b, 0xc4, 0xf7, 0x40, 0xf4, 0x81, 0x09, 0x0a, 0xb2, 0xd7,
	0x31, 0x8d, 0xaf, 0xa3, 0xc0, 0xa5, 0x06, 0x4a, 0x2b, 0xc2, 0x90, 0xfb, 0x17, 0x3e, 0x87, 0xe1,
	0x8b, 0x67, 0xe2, 0x2f, 0xde, 0x87, 0x5b, 0xe1, 0x7b, 0xb3, 0xaa, 0x41, 0x15, 0xcc, 0x62, 0xe4,
	0xfb, 0x50, 0x64, 0xfd, 0x3a, 0xf3, 0x7e, 0x92, 0xf7, 0x3e, 0x63, 0x60, 0x95, 0xfa, 0x47, 0x02,
	0x6e, 0x4f, 0xdf, 0x6e, 0x36, 0x63, 0x7f, 0x0b, 0x96, 0x7d, 0xf3, 0x78, 0x5b, 0x73, 0xa3, 0xc4,
	0x7b, 0xd1, 0x77, 0x7f, 0x62, 0x4b, 0xc5, 0x6f, 0x79, 0xc7, 0x27, 0x10, 0xbf, 0x07, 0x25, 0x5f,
	0x25, 0xa6, 0x21, 0xaf, 0x37, 0x15, 0x8f, 0x4a, 0x62, 0x6b, 0xae, 0xb2, 0x32, 0x8c, 0x92, 0x10,
	0x06, 0x96, 0xe1, 0xfa, 0x2e, 0x71, 0x03, 0xb7, 0xd1, 0xba, 0x35, 0xa3, 0xa1, 0xa5, 0x5f, 0x26,
	0xa0, 0x14, 0xaf, 0x63, 0x36, 0xeb, 0xbd, 0x0d, 0x45, 0x6f, 0x9c, 0xc8, 0x59, 0x6d, 0x25, 0x52,
	0x70, 0xbd, 0x5a, 0x0b, 0xdd, 0x60, 0x1b, 0x71, 0x0b, 0x84, 0xbe, 0xe6, 0xb8, 0xa1, 0x21, 0x24,
	0x1f, 0xc0, 0x0b, 0x94, 0x3b, 0x1e, 0x41, 0x4a, 0x1f, 0x4e, 0x5e, 0xb9, 0x72, 0x26, 0xeb, 0x2f,
	0xaf, 0x7d, 0x91, 0x7e, 0x38, 0x69, 0x10, 0xb6, 0xc3, 0xcc, 0x06, 0xf1, 0x1e, 0x07, 0x7c, 0x81,
	0x18, 0xc2, 0x9e, 0x66, 0x10, 0x23, 0xf8, 0x2d, 0xfd, 0x25, 0x03, 0xcb, 0x31, 0xa0, 0xf2, 0xbc,
	0xf9, 0x77, 0xd0, 0x4f, 0x25, 0xe3, 0xfb, 0xa9, 0x63, 0xeb, 0xc4, 0x9b, 0xae, 0x84, 0x3a, 0x32,
	0xa0, 0x0c, 0x36, 0x59, 0x11, 0x37, 0x61, 0x81, 0x13, 0x53, 0x3f, 0xde, 0x0e, 0xc1, 0xf5, 0xb9,
	0xb1, 0xe4, 0x93, 0x6d, 0x7a, 0x9a, 0xa3, 0xbe, 0x75, 0x12, 0x85, 0xeb, 0x59, 0x4a, 0x94, 0x11,
	0xa2, 0x1a, 0x8e, 0x8a, 0xbd, 0x13, 0xe2, 0xf4, 0xbc, 0x8f, 0x8c, 0x0c, 0x07, 0x9b, 0x09, 0xf1,
	0x4d, 0x58, 0xc2, 0xbd, 0x58, 0xdb, 0xe5, 0x1d, 0x2c, 0xc7, 0x49, 0x2e, 0x52, 0x36, 0xca, 0x7a,
	0xa7, 0xbb, 0xc0, 0xd8, 0x29, 0xae, 0xfd, 0x2a, 0x5c, 0xba, 0xfd, 0x7a, 0x0b, 0x96, 0xf5, 0x33,
	0x53, 0x1b, 0x18, 0x5d, 0xd5, 0x25, 0x83, 0x61, 0x9f, 0x86, 0xa7, 0xa1, 0x87, 0x1e, 0xea, 0x25,
	0x4f, 0xa0, 0xe3, 0xf1, 0x65, 0x5d, 0x7c, 0x08, 0x57, 0x0d, 0x47, 0xfd, 0xd8, 0x70, 0x8c, 0xc3,
	0x3e, 0x51, 0xf1, 0x65, 0x36, 0x75, 0x62, 0xe3, 0xf3, 0xed, 0xdf, 0x6d, 0xc9, 0x70, 0x9e, 0x30,
	0x81, 0x8e, 0xd5, 0x46, 0x36, 0x4d, 0x03, 0xc3, 0x51, 0xd1, 0x24, 0xce, 0xc8, 0x1e, 0xda, 0x86,
	0xc3, 0x86, 0x55, 0xfe, 0x92, 0x05, 0xc3, 0x69, 0x1f, 0x5b, 0x27, 0x6d, 0x8f, 0x27, 0xbe, 0x0e,
	0x0b, 0xbe, 0x9c, 0x57, 0xf5, 0xf9, 0x89, 0xd5, 0xbc, 0xcf, 0x63, 0x85, 0x7f, 0x0b, 0x04, 0x6e,
	0x2c, 0xa0, 0x7e, 0xdf, 0xb1, 0x4c, 0x7c, 0xe2, 0x83, 0x27, 0x72, 0x3c, 0x1b, 0xf8, 0x7f, 0xc7,
	0x32, 0xc5, 0xff, 0x02, 0x11, 0x4f, 0x62, 0x0c, 0xd4, 0xf1, 0x50, 0x0e, 0xdf, 0xf7, 0x90, 0x77,
	0xe4, 0xc1, 0xbe, 0x3f, 0x95, 0x8b, 0x9b, 0xdf, 0x09, 0x53, 0xe7, 0x77, 0xd2, 0x1f, 0xd3, 0xb0,
	0x10, 0x1e, 0x41, 0x85, 0x7b, 0x2d, 0x96, 0x59, 0x33, 0xf6, 0x5a, 0x21, 0xc0, 0x46, 0xb3, 0x20,
	0x1d, 0x07, 0xd8, 0xce, 0x9f, 0x4c, 0xdc, 0x05, 0x5c, 0x80, 0x8f, 0x1c, 0x3f, 0x9c, 0xc8, 0x51,
	0xaa, 0xd7, 0x5e, 0xa1, 0x80, 0x8f, 0x66, 0x43, 0x1d, 0x11, 0xe5, 0xf8, 0x78, 0x96, 0xb6, 0xac,
	0x54, 0xd0, 0x34, 0xba, 0xcf, 0x11, 0xd5, 0x66, 0x39, 0x49, 0xd4, 0xd1, 0xf2, 0x38, 0x91, 0xb7,
	0x35, 0x17, 0x3f, 0x25, 0x7e, 0x1d, 0x16, 0x3c, 0x21, 0x7f, 0xeb, 0x3c, 0xa7, 0xd0, 0x9b, 0x7d,
	0xfa, 0x9b, 0xff, 0x67, 0x30, 0x23, 0x0d, 0xb6, 0x2f, 0x70, 0xd2, 0x9e, 0xa6, 0xe0, 0x00, 0xd8,
	0xa9, 0xba, 0xf4, 0xb4, 0x18, 0x1e, 0xfc, 0xf0, 0x15, 0x18, 0x03, 0x43, 0xe3, 0x5d, 0x58, 0x62,
	0xbd, 0x27, 0x0f, 0x17, 0x8b, 0xf1, 0x70, 0x71, 0x11, 0xfb, 0x51, 0x0e, 0x32, 0xfe, 0x0f, 0x2c,
	0xd2, 0x0e, 0x81, 0x5f, 0x3a, 0x17, 0xbf, 0x74, 0xde, 0xb5, 0xf8, 0x85, 0x1b, 0x30, 0x6f, 0x99,
	0xfd, 0x33, 0x96, 0x1f, 0x03, 0xa7, 0x87, 0xc1, 0xee, 0xc7, 0x62, 0x91, 0xb2, 0x68, 0x72, 0x34,
	0x9d, 0x9e, 0xf4, 0xf7, 0x04, 0x5c, 0xe3, 0xa7, 0x16, 0xac, 0xbb, 0xff, 0x5a, 0x23, 0xba, 0x35,
	0xc8, 0x07, 0x16, 0xe4, 0x1b, 0x97, 0x80, 0x1a, 0xb5, 0x5d, 0x7a, 0x8a, 0xed, 0xa2, 0x28, 0x3b,
	0x33, 0xfb, 0x44, 0x2f, 0x3b, 0x65, 0xa2, 0xf7, 0x45, 0x3a, 0x78, 0x34, 0xf8, 0x61, 0xcd, 0x79,
	0x8f, 0xc6, 0x16, 0x08, 0x0c, 0x38, 0x72, 0x6d, 0x0c, 0xff, 0x7e, 0x2c, 0x20, 0x57, 0x0e, 0x7a,
	0x99, 0xf0, 0x9c, 0x2c, 0x75, 0xce, 0x9c, 0xcc, 0xf0, 0xfa, 0xb8, 0xf3, 0xf2, 0x30, 0x73, 0x7e,
	0x1e, 0x66, 0x2f, 0x92, 0x87, 0xb9, 0x0b, 0xe7, 0x61, 0x7e, 0x6a, 0x1e, 0x46, 0x5c, 0x59, 0x98,
	0xe2, 0xca, 0xdd, 0xf0, 0x84, 0x09, 0x10, 0xed, 0xbc, 0xf2, 0xa2, 0xf1, 0xd9, 0x38, 0x10, 0xa7,
	0x8f, 0x98, 0x8a, 0x97, 0x19, 0x31, 0xc5, 0x66, 0xe4, 0xdc, 0x05, 0x33, 0x92, 0xbd, 0xd3, 0x83,
	0x51, 0xdf, 0x35, 0x42, 0x39, 0x95, 0x33, 0x9c, 0x26, 0x25, 0x4a, 0x23, 0x84, 0xf3, 0xde, 0x09,
	0xea, 0x7d, 0xeb, 0xe4, 0x52, 0x28, 0x33, 0x16, 0xe5, 0x25, 0x5f, 0x80, 0xf2, 0x7e, 0xc5, 0x70,
	0xfd, 0x94, 0x7d, 0x5f, 0x32, 0x32, 0x1d, 0xab, 0xff, 0x5a, 0xc8, 0x54, 0x8f, 0x37, 0xd5, 0x25,
	0xd0, 0xa9, 0x8f, 0xac, 0x42, 0xe8, 0x94, 0x21, 0x2b, 0xe9, 0x93, 0x29, 0x96, 0xb9, 0x24, 0x44,
	0xc5, 0xad, 0xa6, 0x42, 0x54, 0xde, 0x32, 0x47, 0xc1, 0x6f, 0x69, 0x0f, 0x16, 0x79, 0xfc, 0xa4,
	0xd9, 0xda, 0x79, 0x85, 0x86, 0xb2, 0x07, 0x3d, 0xec, 0xcd, 0xf9, 0x1a, 0x9b, 0x35, 0x06, 0x3d,
	0xda, 0x93, 0xbf, 0x8f, 0x3d, 0x79, 0x44, 0xe7, 0x2c, 0x5d, 0xcc, 0x27, 0xac, 0xdd, 0x9e, 0xd0,
	0x30, 0x9b, 0x3d, 0xfe, 0x0f, 0x0a, 0x43, 0xcd, 0xd6, 0xf8, 0x38, 0xb9, 0xfb, 0x22, 0xe0, 0x48,
	0xb7, 0xc8, 0xd3, 0x15, 0xd8, 0x8c, 0xfd, 0x22, 0x01, 0xcb, 0x2d, 0x6d, 0x60, 0x98, 0xbd, 0x50,
	0xef, 0x30, 0x7e, 0x77, 0x12, 0xe1, 0x77, 0xe7, 0x0e, 0xe4, 0x7a, 0xc6, 0x91, 0xcb, 0x1c, 0xcd,
	0x41, 0x68, 0x4a, 0x94, 0xf1, 0xb1, 0x37, 0x51, 0x1b, 0x7e, 0x8a, 0x23, 0x26, 0xc3, 0xed, 0xc1,
	0x63, 0xcf, 0x78, 0x55, 0xc6, 0xe2, 0xdf, 0x30, 0xfe, 0xf5, 0xf1, 0x89, 0xd2, 0x2e, 0xdc, 0xd9,
	0x25, 0x6e, 0xcc, 0xe9, 0x66, 0x6d, 0x17, 0x7f, 0x9e, 0x80, 0xd5, 0x17, 0x69, 0x9a, 0xcd, 0xe0,
	0x3b, 0x71, 0xa9, 0x39, 0x51, 0xff, 0x62, 0x36, 0xe3, 0x13, 0x55, 0x6a, 0x80, 0x34, 0xce, 0x88,
	0x9d, 0x30, 0x22, 0xf7, 0xc4, 0x67, 0xb8, 0xe1, 0x4f, 0x12, 0xb0, 0x7e, 0xae, 0xba, 0xd9, 0xae,
	0xf9, 0x16, 0xe4, 0xd8, 0x71, 0x1d, 0x2f, 0xc7, 0xf8, 0x41, 0x64, 0x58, 0xb5, 0xa3, 0xf8, 0xa2,
	0x52, 0x15, 0x6e, 0xec, 0x12, 0x17, 0x41, 0xb6, 0xdf, 0x2e, 0xca, 0xfa, 0xac, 0xfe, 0x32, 0xe0,
	0xe6, 0x34, 0x25, 0xb3, 0xdd, 0x61, 0x0d, 0xe6, 0xbc, 0x04, 0x1f, 0xfb, 0x6a, 0x9e, 0x35, 0xae,
	0x4c, 0xa1, 0x77, 0xde, 0xb6, 0xab, 0x99, 0x3d, 0x62, 0xcb, 0x83, 0xaf, 0x73, 0xde, 0x58, 0x25,
	0x2f, 0xfb, 0xbc, 0x3f, 0x4e, 0x80, 0x58, 0xd7, 0x4c, 0xc7, 0x33, 0x4d, 0x93, 0x38, 0x8e, 0xd6,
	0x23, 0x62, 0x69, 0x9c, 0x4a, 0x89, 0xb5, 0xd4, 0x46, 0x61, 0x0c, 0x04, 0x5f, 0x85, 0x39, 0xbf,
	0xb3, 0x44, 0x14, 0xc1, 0x57, 0xb2, 0xa2, 0xc7, 0xc1, 0x41, 0x35, 0x27, 0x68, 0x74, 0x2d, 0x33,
	0xfc, 0xfd, 0xc5, 0xe3, 0xc8, 0x5d, 0xcb, 0x94, 0x7e, 0x9a, 0x08, 0xfa, 0xa0, 0x8a, 0x75, 0x8a,
	0xcf, 0xf9, 0x3b, 0x80, 0x4f, 0x3b, 0xa2, 0x5c, 0x76, 0xc3, 0x73, 0x3f, 0xde, 0x53, 0x79, 0x0a,
	0xf6, 0xc2, 0x7f, 0xe7, 0x92, 0xc4, 0xc5, 0x33, 0xfd, 0x9d, 0xcb, 0xcf, 0x52, 0x20, 0x44, 0xf9,
	0xe2, 0x2d, 0xc8, 0x52, 0xad, 0x5e, 0x61, 0xf7, 0xaf, 0x91, 0x39, 0xb4, 0x4e, 0xf1, 0xaf, 0xee,
	0x62, 0xc0, 0x07, 0xdb, 0xf9, 0x52, 0xed, 0x40, 0x2a, 0x7e, 0x69, 0xa4, 0x1d, 0xe0, 0x1e, 0x9b,
	0x74, 0xcc, 0x63, 0x73, 0xcf, 0xc3, 0x54, 0xe8, 0x24, 0xbe, 0x39, 0x43, 0xcb, 0xa2, 0x87, 0x42,
	0x18, 0x35, 0x1b, 0x8b, 0x51, 0x23, 0x48, 0x90, 0x07, 0x97, 0x3c, 0x12, 0x5c, 0x07, 0xd0, 0x49,
	0x5f, 0x3b, 0x63, 0xaa, 0xf2, 0x9c, 0xaa, 0x02, 0xd2, 0x51, 0xd7, 0xb4, 0xa1, 0x00, 0x6d, 0xc8,
	0xa6, 0x0f, 0x05, 0xa4, 0x1f, 0xc1, 0xf2, 0x81, 0x39, 0xd4, 0xba, 0xcf, 0xc7, 0x4e, 0x99, 0x05,
	0x49, 0x8c, 0x3d, 0x97, 0x9c, 0xf4, 0x5c, 0xb4, 0x15, 0x4e, 0xc6, 0x40, 0x70, 0xe9, 0x07, 0x70,
	0x75, 0xf2, 0x00, 0xb3, 0xe5, 0xe1, 0x3b, 0x90, 0xc7, 0x43, 0xb0, 0xaf, 0xcc, 0x2f, 0x8a, 0x67,
	0x2f, 0x03, 0x94, 0xdc, 0x21, 0xfb, 0x21, 0xbd, 0x09, 0x4b, 0x63, 0xd6, 0xde, 0x90, 0x98, 0x34,
	0xc8, 0x5f, 0x14, 0x8e, 0x52, 0x0d, 0xd6, 0x2b, 0x5a, 0xf7, 0x39, 0x3d, 0xb1, 0x6c, 0xba, 0xc6,
	0x40, 0x73, 0xc9, 0xf8, 0xd9, 0x70, 0x6d, 0xad, 0xeb, 0xee, 0x8f, 0x9c, 0x63, 0xfa, 0x5a, 0xfa,
	0x6f, 0x2a, 0xaf, 0xc4, 0x27, 0x6e, 0xfe, 0x2d, 0x19, 0xec, 0xcc, 0x3e, 0xb2, 0x60, 0x5f, 0x73,
	0x0d, 0xc4, 0x7d, 0xa5, 0xd6, 0xae, 0xb5, 0x3a, 0x6a, 0x7b, 0xef, 0x40, 0xa9, 0xd6, 0xd4, 0xca,
	0xc1, 0x33, 0xe1, 0x8a, 0x78, 0x13, 0xae, 0x45, 0xe8, 0xfb, 0xe5, 0xea, 0xe3, 0xf2, 0x6e, 0x4d,
	0x48, 0x88, 0x6b, 0x70, 0x3b, 0x9e, 0xa7, 0xd6, 0x65, 0xa5, 0xdd, 0x11, 0x92, 0xe2, 0x0d, 0x58,
	0x89, 0x48, 0xd4, 0x6b, 0x8d, 0xc6, 0xde, 0x53, 0x21, 0x25, 0x96, 0xe0, 0x6a, 0x84, 0xd5, 0x2c,
	0xef, 0xca, 0x55, 0x21, 0x2d, 0xae, 0xc2, 0xcd, 0x08, 0xa7, 0xb1, 0xd7, 0xe9, 0xd4, 0x94, 0x67,
	0x78, 0xa4, 0x8c, 0x28, 0xc1, 0xea, 0x14, 0xbe, 0x7f, 0xb4, 0x6c, 0xcc, 0xd1, 0x7c, 0x19, 0xb6,
	0x4b, 0x2e, 0x46, 0x62, 0xaf, 0x5e, 0xaf, 0x29, 0x72, 0x6b, 0x57, 0x55, 0xf6, 0xf6, 0x9a, 0x42,
	0x3e, 0x66, 0x9f, 0xea, 0xa3, 0x72, 0xab, 0x55, 0x6b, 0xa8, 0xbb, 0x72, 0xbd, 0xa3, 0xee, 0x3f,
	0x16, 0x0a, 0x31, 0x32, 0xbb, 0x4a, 0xb9, 0x42, 0x05, 0x65, 0x45, 0xdd, 0x2d, 0x37, 0x6b, 0x02,
	0x6c, 0x1e, 0xc3, 0x55, 0xbe, 0x57, 0x6a, 0xfb, 0x03, 0xbd, 0xeb, 0xb0, 0x1c, 0x59, 0xdb, 0xda,
	0x6b, 0xd5, 0x62, 0x6d, 0x5e, 0x6e, 0x34, 0xd4, 0xa6, 0x5c, 0x15, 0x12, 0xe2, 0x2d, 0xb8, 0x1e,
	0xe1, 0x3d, 0x95, 0x3b, 0x8f, 0xd4, 0x03, 0x79, 0x47, 0x48, 0x6e, 0x7e, 0x08, 0x8b, 0x5c, 0xf9,
	0x9c, 0xd8, 0xa4, 0xd6, 0xda, 0x51, 0x5b, 0x7b, 0x4a, 0xb3, 0xdc, 0x10, 0xae, 0x88, 0x2b, 0xb0,
	0x14, 0x62, 0xec, 0x28, 0xe5, 0xa7, 0x42, 0x42, 0xbc, 0x03, 0x37, 0x38, 0x72, 0x47, 0xad, 0x97,
	0x5b, 0x6d, 0xdf, 0x76, 0x42, 0x72, 0xf3, 0x37, 0xa9, 0xe0, 0x6f, 0x84, 0xda, 0xc1, 0x57, 0x3a,
	0xdc, 0xe8, 0x26, 0x5c, 0xab, 0x31, 0x4d, 0xde, 0xb9, 0x76, 0x6a, 0xf5, 0x46, 0xf9, 0xa0, 0x43,
	0x2f, 0xb4, 0x06, 0xb7, 0xc3, 0x3c, 0x34, 0x60, 0xe7, 0x40, 0x69, 0x75, 0xca, 0x95, 0x06, 0x0d,
	0xa5, 0xdb, 0x50, 0x8a, 0x91, 0x68, 0x3f, 0xaa, 0x35, 0xea, 0x42, 0x92, 0x1e, 0x2a, 0xcc, 0x6d,
	0xef, 0xd7, 0x6a, 0xd5, 0x47, 0x6a, 0xa5, 0xdc, 0x68, 0x08, 0x29, 0x6a, 0x93, 0xc8, 0xd6, 0x4a,
	0xf9, 0x29, 0x6a, 0x10, 0xd2, 0x93, 0x6b, 0x9b, 0xe5, 0xf6, 0xe3, 0xda, 0x8e, 0x5a, 0xa5, 0x6b,
	0x33, 0xe2, 0x55, 0x10, 0xc2, 0x6c, 0xb9, 0x29, 0x64, 0xa9, 0x5b, 0xc3, 0xd4, 0xbd, 0x7a, 0x5d,
	0xae, 0xca, 0xe5, 0x86, 0x1f, 0x02, 0x42, 0x8e, 0x06, 0x70, 0x58, 0xc6, 0x0b, 0xed, 0x3c, 0xb5,
	0x79, 0x74, 0x4b, 0x1a, 0x73, 0x05, 0x9a, 0x0e, 0x61, 0x86, 0x6f, 0x58, 0x10, 0xef, 0xc2, 0xad,
	0x89, 0x1d, 0xb9, 0x68, 0x2c, 0x8a, 0xf7, 0xe0, 0x4e, 0x58, 0x20, 0x1a, 0x8c, 0x73, 0x93, 0xa7,
	0xf6, 0x45, 0x2a, 0xb2, 0xd2, 0x79, 0xb4, 0x53, 0x7e, 0x26, 0xcc, 0x6f, 0x7e, 0x37, 0xe4, 0x3f,
	0xf6, 0x85, 0x14, 0xfd, 0xc7, 0xc5, 0x43, 0xe7, 0xd9, 0x7e, 0x8d, 0x6d, 0x7b, 0x45, 0x5c, 0x86,
	0xc5, 0x10, 0x59, 0x6e, 0x0a, 0x09, 0x3e, 0xa8, 0x90, 0xe8, 0xdd, 0x3c, 0xb9, 0xb9, 0x13, 0x04,
	0x60, 0xf0, 0xb7, 0x8f, 0x68, 0x8c, 0x4e, 0xed, 0xdb, 0x9e, 0xe8, 0x4e, 0xad, 0x5e, 0x3e, 0x68,
	0x74, 0x84, 0x2b, 0xcc, 0x7e, 0x63, 0x86, 0xdc, 0xea, 0xc8, 0xcd, 0x72, 0xf5, 0x99, 0x90, 0xd8,
	0x7c, 0x02, 0x2b, 0xdc, 0x5f, 0xfe, 0x74, 0xc6, 0x9f, 0x43, 0x6f, 0x43, 0x29, 0xd8, 0xb7, 0xac,
	0xec, 0xd6, 0xbc, 0xa5, 0x07, 0xed, 0x9a, 0x22, 0x5c, 0xa1, 0x26, 0x8c, 0xe3, 0x96, 0x65, 0x55,
	0xd9, 0xa3, 0x41, 0xb6, 0x79, 0x3c, 0x1e, 0x23, 0xf1, 0xdf, 0x62, 0xd7, 0xe1, 0x6e, 0x4d, 0xf5,
	0x57, 0x56, 0x0e, 0xda, 0x72, 0xab, 0xd6, 0x6e, 0x47, 0x4f, 0xfb, 0x06, 0x6c, 0x4c, 0x13, 0x62,
	0x27, 0xef, 0x04, 0x6c, 0x21, 0xb1, 0x79, 0x14, 0x94, 0xd8, 0x9d, 0xe0, 0x73, 0x1e, 0x8b, 0xf1,
	0x9d, 0x5a, 0xa7, 0x2c, 0x37, 0x02, 0x4d, 0x4a, 0xad, 0x5a, 0x93, 0x9f, 0xd0, 0x1c, 0xc1, 0xd8,
	0x88, 0x70, 0xa9, 0x2f, 0x85, 0x04, 0xb3, 0x54, 0x84, 0x45, 0xa3, 0x37, 0x59, 0xe9, 0x7c, 0xf6,
	0xe5, 0x6a, 0xe2, 0xf7, 0x5f, 0xae, 0x26, 0xfe, 0xfc, 0xe5, 0x6a, 0xe2, 0xd3, 0xbf, 0xae, 0x5e,
	0x81, 0x52, 0xd7, 0x1a, 0x6c, 0x9d, 0x19, 0x67, 0xd6, 0x88, 0x3e, 0x44, 0x03, 0x4b, 0x27, 0x7d,
	0xf6, 0x3f, 0x15, 0xbe, 0xb3, 0xd1, 0xb3, 0xfa, 0x9a, 0xd9, 0xdb, 0x7a, 0xb8, 0xed, 0xba, 0x5b,
	0x5d, 0x6b, 0xf0, 0x00, 0xc9, 0x5d, 0xab, 0xff, 0x40, 0x1b, 0x0e, 0xf9, 0xff, 0xf9, 0xf0, 0xcf,
	0x00, 0x00, 0x00, 0xff, 0xff, 0x09, 0xd1, 0x1e, 0x6e, 0x13, 0x31, 0x00, 0x00,
}
