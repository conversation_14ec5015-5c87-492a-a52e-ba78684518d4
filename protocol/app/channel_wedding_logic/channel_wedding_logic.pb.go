// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel_wedding_logic/channel_wedding_logic.proto

package channel_wedding_logic // import "golang.52tt.com/protocol/app/channel_wedding_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 婚礼阶段枚举
type WeddingStage int32

const (
	WeddingStage_WEDDING_STAGE_UNSPECIFIED       WeddingStage = 0
	WeddingStage_WEDDING_STAGE_WELCOME_GUEST     WeddingStage = 1
	WeddingStage_WEDDING_STAGE_BRIDE_GROOM_ENTER WeddingStage = 2
	WeddingStage_WEDDING_STAGE_LOVE_DECLARATION  WeddingStage = 3
	WeddingStage_WEDDING_STAGE_EXCHANGE_RING     WeddingStage = 4
	WeddingStage_WEDDING_STAGE_HIGHLIGHT         WeddingStage = 5
	WeddingStage_WEDDING_STAGE_GROUP_PHOTO       WeddingStage = 6
)

var WeddingStage_name = map[int32]string{
	0: "WEDDING_STAGE_UNSPECIFIED",
	1: "WEDDING_STAGE_WELCOME_GUEST",
	2: "WEDDING_STAGE_BRIDE_GROOM_ENTER",
	3: "WEDDING_STAGE_LOVE_DECLARATION",
	4: "WEDDING_STAGE_EXCHANGE_RING",
	5: "WEDDING_STAGE_HIGHLIGHT",
	6: "WEDDING_STAGE_GROUP_PHOTO",
}
var WeddingStage_value = map[string]int32{
	"WEDDING_STAGE_UNSPECIFIED":       0,
	"WEDDING_STAGE_WELCOME_GUEST":     1,
	"WEDDING_STAGE_BRIDE_GROOM_ENTER": 2,
	"WEDDING_STAGE_LOVE_DECLARATION":  3,
	"WEDDING_STAGE_EXCHANGE_RING":     4,
	"WEDDING_STAGE_HIGHLIGHT":         5,
	"WEDDING_STAGE_GROUP_PHOTO":       6,
}

func (x WeddingStage) String() string {
	return proto.EnumName(WeddingStage_name, int32(x))
}
func (WeddingStage) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{0}
}

type GroupPhotoSubStage int32

const (
	GroupPhotoSubStage_GROUP_PHOTO_SUB_STAGE_UNSPECIFIED GroupPhotoSubStage = 0
	GroupPhotoSubStage_GROUP_PHOTO_SUB_STAGE_PHOTOGRAPH  GroupPhotoSubStage = 1
)

var GroupPhotoSubStage_name = map[int32]string{
	0: "GROUP_PHOTO_SUB_STAGE_UNSPECIFIED",
	1: "GROUP_PHOTO_SUB_STAGE_PHOTOGRAPH",
}
var GroupPhotoSubStage_value = map[string]int32{
	"GROUP_PHOTO_SUB_STAGE_UNSPECIFIED": 0,
	"GROUP_PHOTO_SUB_STAGE_PHOTOGRAPH":  1,
}

func (x GroupPhotoSubStage) String() string {
	return proto.EnumName(GroupPhotoSubStage_name, int32(x))
}
func (GroupPhotoSubStage) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{1}
}

// 婚礼场景动画枚举
type WeddingScene int32

const (
	WeddingScene_WEDDING_SCENE_UNSPECIFIED       WeddingScene = 0
	WeddingScene_WEDDING_SCENE_BRIDE_GROOM_ENTER WeddingScene = 1
	WeddingScene_WEDDING_SCENE_EXCHANGE_RING     WeddingScene = 2
	WeddingScene_WEDDING_SCENE_HIGHLIGHT         WeddingScene = 3
	WeddingScene_WEDDING_SCENE_GROUP_PHOTO       WeddingScene = 4
	WeddingScene_WEDDING_SCENE_LOVE_DECLARATION  WeddingScene = 5
)

var WeddingScene_name = map[int32]string{
	0: "WEDDING_SCENE_UNSPECIFIED",
	1: "WEDDING_SCENE_BRIDE_GROOM_ENTER",
	2: "WEDDING_SCENE_EXCHANGE_RING",
	3: "WEDDING_SCENE_HIGHLIGHT",
	4: "WEDDING_SCENE_GROUP_PHOTO",
	5: "WEDDING_SCENE_LOVE_DECLARATION",
}
var WeddingScene_value = map[string]int32{
	"WEDDING_SCENE_UNSPECIFIED":       0,
	"WEDDING_SCENE_BRIDE_GROOM_ENTER": 1,
	"WEDDING_SCENE_EXCHANGE_RING":     2,
	"WEDDING_SCENE_HIGHLIGHT":         3,
	"WEDDING_SCENE_GROUP_PHOTO":       4,
	"WEDDING_SCENE_LOVE_DECLARATION":  5,
}

func (x WeddingScene) String() string {
	return proto.EnumName(WeddingScene_name, int32(x))
}
func (WeddingScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{2}
}

// 嘉宾类型
type WeddingGuestType int32

const (
	WeddingGuestType_WEDDING_GUEST_TYPE_UNSPECIFIED WeddingGuestType = 0
	WeddingGuestType_WEDDING_GUEST_TYPE_BRIDE       WeddingGuestType = 1
	WeddingGuestType_WEDDING_GUEST_TYPE_GROOM       WeddingGuestType = 2
	WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES      WeddingGuestType = 3
	WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID  WeddingGuestType = 4
	WeddingGuestType_WEDDING_GUEST_TYPE_FRIENDS     WeddingGuestType = 5
)

var WeddingGuestType_name = map[int32]string{
	0: "WEDDING_GUEST_TYPE_UNSPECIFIED",
	1: "WEDDING_GUEST_TYPE_BRIDE",
	2: "WEDDING_GUEST_TYPE_GROOM",
	3: "WEDDING_GUEST_TYPE_BRIDES",
	4: "WEDDING_GUEST_TYPE_BRIDESMAID",
	5: "WEDDING_GUEST_TYPE_FRIENDS",
}
var WeddingGuestType_value = map[string]int32{
	"WEDDING_GUEST_TYPE_UNSPECIFIED": 0,
	"WEDDING_GUEST_TYPE_BRIDE":       1,
	"WEDDING_GUEST_TYPE_GROOM":       2,
	"WEDDING_GUEST_TYPE_BRIDES":      3,
	"WEDDING_GUEST_TYPE_BRIDESMAID":  4,
	"WEDDING_GUEST_TYPE_FRIENDS":     5,
}

func (x WeddingGuestType) String() string {
	return proto.EnumName(WeddingGuestType_name, int32(x))
}
func (WeddingGuestType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{3}
}

type ChairRoundStatus int32

const (
	ChairRoundStatus_CHAIR_ROUND_STATUS_UNSPECIFIED    ChairRoundStatus = 0
	ChairRoundStatus_CHAIR_ROUND_STATUS_ROUND_BEGIN    ChairRoundStatus = 1
	ChairRoundStatus_CHAIR_ROUND_STATUS_ROUND_GRABBING ChairRoundStatus = 2
	ChairRoundStatus_CHAIR_ROUND_STATUS_ROUND_END      ChairRoundStatus = 3
	ChairRoundStatus_CHAIR_ROUND_STATUS_GAME_OVER      ChairRoundStatus = 4
)

var ChairRoundStatus_name = map[int32]string{
	0: "CHAIR_ROUND_STATUS_UNSPECIFIED",
	1: "CHAIR_ROUND_STATUS_ROUND_BEGIN",
	2: "CHAIR_ROUND_STATUS_ROUND_GRABBING",
	3: "CHAIR_ROUND_STATUS_ROUND_END",
	4: "CHAIR_ROUND_STATUS_GAME_OVER",
}
var ChairRoundStatus_value = map[string]int32{
	"CHAIR_ROUND_STATUS_UNSPECIFIED":    0,
	"CHAIR_ROUND_STATUS_ROUND_BEGIN":    1,
	"CHAIR_ROUND_STATUS_ROUND_GRABBING": 2,
	"CHAIR_ROUND_STATUS_ROUND_END":      3,
	"CHAIR_ROUND_STATUS_GAME_OVER":      4,
}

func (x ChairRoundStatus) String() string {
	return proto.EnumName(ChairRoundStatus_name, int32(x))
}
func (ChairRoundStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{4}
}

type WeddingInviteType int32

const (
	WeddingInviteType_WEDDING_INVITE_TYPE_UNSPECIFIED WeddingInviteType = 0
	WeddingInviteType_WEDDING_INVITE_TYPE_GROOMSMAN   WeddingInviteType = 1
	WeddingInviteType_WEDDING_INVITE_TYPE_BRIDESMAID  WeddingInviteType = 2
	WeddingInviteType_WEDDING_INVITE_TYPE_FRIEND      WeddingInviteType = 3
)

var WeddingInviteType_name = map[int32]string{
	0: "WEDDING_INVITE_TYPE_UNSPECIFIED",
	1: "WEDDING_INVITE_TYPE_GROOMSMAN",
	2: "WEDDING_INVITE_TYPE_BRIDESMAID",
	3: "WEDDING_INVITE_TYPE_FRIEND",
}
var WeddingInviteType_value = map[string]int32{
	"WEDDING_INVITE_TYPE_UNSPECIFIED": 0,
	"WEDDING_INVITE_TYPE_GROOMSMAN":   1,
	"WEDDING_INVITE_TYPE_BRIDESMAID":  2,
	"WEDDING_INVITE_TYPE_FRIEND":      3,
}

func (x WeddingInviteType) String() string {
	return proto.EnumName(WeddingInviteType_name, int32(x))
}
func (WeddingInviteType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{5}
}

type WeddingInviteStatus int32

const (
	WeddingInviteStatus_WEDDING_INVITE_STATUS_UNSPECIFIED WeddingInviteStatus = 0
	WeddingInviteStatus_WEDDING_INVITE_STATUS_WAITING     WeddingInviteStatus = 1
	WeddingInviteStatus_WEDDING_INVITE_STATUS_ACCEPTED    WeddingInviteStatus = 2
	WeddingInviteStatus_WEDDING_INVITE_STATUS_REFUSED     WeddingInviteStatus = 3
	WeddingInviteStatus_WEDDING_INVITE_STATUS_CANCELED    WeddingInviteStatus = 4
)

var WeddingInviteStatus_name = map[int32]string{
	0: "WEDDING_INVITE_STATUS_UNSPECIFIED",
	1: "WEDDING_INVITE_STATUS_WAITING",
	2: "WEDDING_INVITE_STATUS_ACCEPTED",
	3: "WEDDING_INVITE_STATUS_REFUSED",
	4: "WEDDING_INVITE_STATUS_CANCELED",
}
var WeddingInviteStatus_value = map[string]int32{
	"WEDDING_INVITE_STATUS_UNSPECIFIED": 0,
	"WEDDING_INVITE_STATUS_WAITING":     1,
	"WEDDING_INVITE_STATUS_ACCEPTED":    2,
	"WEDDING_INVITE_STATUS_REFUSED":     3,
	"WEDDING_INVITE_STATUS_CANCELED":    4,
}

func (x WeddingInviteStatus) String() string {
	return proto.EnumName(WeddingInviteStatus_name, int32(x))
}
func (WeddingInviteStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{6}
}

// 价格类型, 与 PRESENT_PRICE_TYPE 保持一致
type WeddingPriceType int32

const (
	WeddingPriceType_WEDDING_PRICE_TYPE_UNSPECIFIED WeddingPriceType = 0
	WeddingPriceType_WEDDING_PRICE_TYPE_RED_DIAMOND WeddingPriceType = 1
	WeddingPriceType_WEDDING_PRICE_TYPE_T_BEAN      WeddingPriceType = 2
)

var WeddingPriceType_name = map[int32]string{
	0: "WEDDING_PRICE_TYPE_UNSPECIFIED",
	1: "WEDDING_PRICE_TYPE_RED_DIAMOND",
	2: "WEDDING_PRICE_TYPE_T_BEAN",
}
var WeddingPriceType_value = map[string]int32{
	"WEDDING_PRICE_TYPE_UNSPECIFIED": 0,
	"WEDDING_PRICE_TYPE_RED_DIAMOND": 1,
	"WEDDING_PRICE_TYPE_T_BEAN":      2,
}

func (x WeddingPriceType) String() string {
	return proto.EnumName(WeddingPriceType_name, int32(x))
}
func (WeddingPriceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{7}
}

type WeddingAnimationType int32

const (
	WeddingAnimationType_WEDDING_ANIMATION_TYPE_UNSPECIFIED WeddingAnimationType = 0
	WeddingAnimationType_WEDDING_ANIMATION_TYPE_STATIC      WeddingAnimationType = 1
	WeddingAnimationType_WEDDING_ANIMATION_TYPE_LOTTIE      WeddingAnimationType = 2
	WeddingAnimationType_WEDDING_ANIMATION_TYPE_VAP         WeddingAnimationType = 3
)

var WeddingAnimationType_name = map[int32]string{
	0: "WEDDING_ANIMATION_TYPE_UNSPECIFIED",
	1: "WEDDING_ANIMATION_TYPE_STATIC",
	2: "WEDDING_ANIMATION_TYPE_LOTTIE",
	3: "WEDDING_ANIMATION_TYPE_VAP",
}
var WeddingAnimationType_value = map[string]int32{
	"WEDDING_ANIMATION_TYPE_UNSPECIFIED": 0,
	"WEDDING_ANIMATION_TYPE_STATIC":      1,
	"WEDDING_ANIMATION_TYPE_LOTTIE":      2,
	"WEDDING_ANIMATION_TYPE_VAP":         3,
}

func (x WeddingAnimationType) String() string {
	return proto.EnumName(WeddingAnimationType_name, int32(x))
}
func (WeddingAnimationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{8}
}

type WeddingLevelType int32

const (
	WeddingLevelType_WEDDING_LEVEL_TYPE_UNSPECIFIED WeddingLevelType = 0
	// 初级婚礼
	WeddingLevelType_WEDDING_LEVEL_TYPE_PRIMARY WeddingLevelType = 1
	// 中级婚礼
	WeddingLevelType_WEDDING_LEVEL_TYPE_INTERMEDIATE WeddingLevelType = 2
	// 高级婚礼
	WeddingLevelType_WEDDING_LEVEL_TYPE_ADVANCED WeddingLevelType = 3
)

var WeddingLevelType_name = map[int32]string{
	0: "WEDDING_LEVEL_TYPE_UNSPECIFIED",
	1: "WEDDING_LEVEL_TYPE_PRIMARY",
	2: "WEDDING_LEVEL_TYPE_INTERMEDIATE",
	3: "WEDDING_LEVEL_TYPE_ADVANCED",
}
var WeddingLevelType_value = map[string]int32{
	"WEDDING_LEVEL_TYPE_UNSPECIFIED":  0,
	"WEDDING_LEVEL_TYPE_PRIMARY":      1,
	"WEDDING_LEVEL_TYPE_INTERMEDIATE": 2,
	"WEDDING_LEVEL_TYPE_ADVANCED":     3,
}

func (x WeddingLevelType) String() string {
	return proto.EnumName(WeddingLevelType_name, int32(x))
}
func (WeddingLevelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{9}
}

type WeddingChargeType int32

const (
	WeddingChargeType_WEDDING_CHARGE_TYPE_UNSPECIFIED WeddingChargeType = 0
	WeddingChargeType_WEDDING_CHARGE_TYPE_PAID        WeddingChargeType = 1
	WeddingChargeType_WEDDING_CHARGE_TYPE_FREE        WeddingChargeType = 2
	WeddingChargeType_WEDDING_CHARGE_TYPE_MIX         WeddingChargeType = 3
)

var WeddingChargeType_name = map[int32]string{
	0: "WEDDING_CHARGE_TYPE_UNSPECIFIED",
	1: "WEDDING_CHARGE_TYPE_PAID",
	2: "WEDDING_CHARGE_TYPE_FREE",
	3: "WEDDING_CHARGE_TYPE_MIX",
}
var WeddingChargeType_value = map[string]int32{
	"WEDDING_CHARGE_TYPE_UNSPECIFIED": 0,
	"WEDDING_CHARGE_TYPE_PAID":        1,
	"WEDDING_CHARGE_TYPE_FREE":        2,
	"WEDDING_CHARGE_TYPE_MIX":         3,
}

func (x WeddingChargeType) String() string {
	return proto.EnumName(WeddingChargeType_name, int32(x))
}
func (WeddingChargeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{10}
}

type WeddingTimeStatus int32

const (
	WeddingTimeStatus_WEDDING_TIME_STATUS_UNSPECIFIED WeddingTimeStatus = 0
	WeddingTimeStatus_WEDDING_TIME_STATUS_GOING       WeddingTimeStatus = 1
	WeddingTimeStatus_WEDDING_TIME_STATUS_COMING      WeddingTimeStatus = 2
)

var WeddingTimeStatus_name = map[int32]string{
	0: "WEDDING_TIME_STATUS_UNSPECIFIED",
	1: "WEDDING_TIME_STATUS_GOING",
	2: "WEDDING_TIME_STATUS_COMING",
}
var WeddingTimeStatus_value = map[string]int32{
	"WEDDING_TIME_STATUS_UNSPECIFIED": 0,
	"WEDDING_TIME_STATUS_GOING":       1,
	"WEDDING_TIME_STATUS_COMING":      2,
}

func (x WeddingTimeStatus) String() string {
	return proto.EnumName(WeddingTimeStatus_name, int32(x))
}
func (WeddingTimeStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{11}
}

type WeddingSubscribeStatus int32

const (
	WeddingSubscribeStatus_WEDDING_SUBSCRIBE_STATUS_UNSPECIFIED    WeddingSubscribeStatus = 0
	WeddingSubscribeStatus_WEDDING_SUBSCRIBE_STATUS_SUBSCRIBED     WeddingSubscribeStatus = 1
	WeddingSubscribeStatus_WEDDING_SUBSCRIBE_STATUS_NOT_SUBSCRIBED WeddingSubscribeStatus = 2
)

var WeddingSubscribeStatus_name = map[int32]string{
	0: "WEDDING_SUBSCRIBE_STATUS_UNSPECIFIED",
	1: "WEDDING_SUBSCRIBE_STATUS_SUBSCRIBED",
	2: "WEDDING_SUBSCRIBE_STATUS_NOT_SUBSCRIBED",
}
var WeddingSubscribeStatus_value = map[string]int32{
	"WEDDING_SUBSCRIBE_STATUS_UNSPECIFIED":    0,
	"WEDDING_SUBSCRIBE_STATUS_SUBSCRIBED":     1,
	"WEDDING_SUBSCRIBE_STATUS_NOT_SUBSCRIBED": 2,
}

func (x WeddingSubscribeStatus) String() string {
	return proto.EnumName(WeddingSubscribeStatus_name, int32(x))
}
func (WeddingSubscribeStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{12}
}

type EndRelationShipSource int32

const (
	EndRelationShipSource_END_RELATION_SHIP_SOURCE_UNSPECIFIED       EndRelationShipSource = 0
	EndRelationShipSource_END_RELATION_SHIP_SOURCE_WEDDING_PAGE      EndRelationShipSource = 1
	EndRelationShipSource_END_RELATION_SHIP_SOURCE_PERSONAL_HOMEPAGE EndRelationShipSource = 2
)

var EndRelationShipSource_name = map[int32]string{
	0: "END_RELATION_SHIP_SOURCE_UNSPECIFIED",
	1: "END_RELATION_SHIP_SOURCE_WEDDING_PAGE",
	2: "END_RELATION_SHIP_SOURCE_PERSONAL_HOMEPAGE",
}
var EndRelationShipSource_value = map[string]int32{
	"END_RELATION_SHIP_SOURCE_UNSPECIFIED":       0,
	"END_RELATION_SHIP_SOURCE_WEDDING_PAGE":      1,
	"END_RELATION_SHIP_SOURCE_PERSONAL_HOMEPAGE": 2,
}

func (x EndRelationShipSource) String() string {
	return proto.EnumName(EndRelationShipSource_name, int32(x))
}
func (EndRelationShipSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{13}
}

// 求婚状态枚举 Propose
type ProposeStatus int32

const (
	ProposeStatus_PROPOSE_STATUS_UNSPECIFIED ProposeStatus = 0
	ProposeStatus_PROPOSE_STATUS_INVITED     ProposeStatus = 1
	ProposeStatus_PROPOSE_STATUS_SUCCESS     ProposeStatus = 2
	ProposeStatus_PROPOSE_STATUS_FAILED      ProposeStatus = 3
	ProposeStatus_PROPOSE_STATUS_CANCELED    ProposeStatus = 4
	ProposeStatus_PROPOSE_STATUS_TIMEOUT     ProposeStatus = 5
)

var ProposeStatus_name = map[int32]string{
	0: "PROPOSE_STATUS_UNSPECIFIED",
	1: "PROPOSE_STATUS_INVITED",
	2: "PROPOSE_STATUS_SUCCESS",
	3: "PROPOSE_STATUS_FAILED",
	4: "PROPOSE_STATUS_CANCELED",
	5: "PROPOSE_STATUS_TIMEOUT",
}
var ProposeStatus_value = map[string]int32{
	"PROPOSE_STATUS_UNSPECIFIED": 0,
	"PROPOSE_STATUS_INVITED":     1,
	"PROPOSE_STATUS_SUCCESS":     2,
	"PROPOSE_STATUS_FAILED":      3,
	"PROPOSE_STATUS_CANCELED":    4,
	"PROPOSE_STATUS_TIMEOUT":     5,
}

func (x ProposeStatus) String() string {
	return proto.EnumName(ProposeStatus_name, int32(x))
}
func (ProposeStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{14}
}

type HideOpType int32

const (
	HideOpType_HIDE_OP_TYPE_UNSPECIFIED HideOpType = 0
	HideOpType_HIDE_OP_TYPE_HIDE        HideOpType = 1
	HideOpType_HIDE_OP_TYPE_SHOW        HideOpType = 2
)

var HideOpType_name = map[int32]string{
	0: "HIDE_OP_TYPE_UNSPECIFIED",
	1: "HIDE_OP_TYPE_HIDE",
	2: "HIDE_OP_TYPE_SHOW",
}
var HideOpType_value = map[string]int32{
	"HIDE_OP_TYPE_UNSPECIFIED": 0,
	"HIDE_OP_TYPE_HIDE":        1,
	"HIDE_OP_TYPE_SHOW":        2,
}

func (x HideOpType) String() string {
	return proto.EnumName(HideOpType_name, int32(x))
}
func (HideOpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{15}
}

type WeddingPreviewResourceScene int32

const (
	WeddingPreviewResourceScene_WEDDING_PREVIEW_RESOURCE_SCENE_UNSPECIFIED         WeddingPreviewResourceScene = 0
	WeddingPreviewResourceScene_WEDDING_PREVIEW_RESOURCE_SCENE_RESERVE_PAGE        WeddingPreviewResourceScene = 1
	WeddingPreviewResourceScene_WEDDING_PREVIEW_RESOURCE_SCENE_CHANNEL_PRE_WEDDING WeddingPreviewResourceScene = 2
)

var WeddingPreviewResourceScene_name = map[int32]string{
	0: "WEDDING_PREVIEW_RESOURCE_SCENE_UNSPECIFIED",
	1: "WEDDING_PREVIEW_RESOURCE_SCENE_RESERVE_PAGE",
	2: "WEDDING_PREVIEW_RESOURCE_SCENE_CHANNEL_PRE_WEDDING",
}
var WeddingPreviewResourceScene_value = map[string]int32{
	"WEDDING_PREVIEW_RESOURCE_SCENE_UNSPECIFIED":         0,
	"WEDDING_PREVIEW_RESOURCE_SCENE_RESERVE_PAGE":        1,
	"WEDDING_PREVIEW_RESOURCE_SCENE_CHANNEL_PRE_WEDDING": 2,
}

func (x WeddingPreviewResourceScene) String() string {
	return proto.EnumName(WeddingPreviewResourceScene_name, int32(x))
}
func (WeddingPreviewResourceScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{16}
}

type WeddingReserveIMStatus int32

const (
	WeddingReserveIMStatus_WEDDING_RESERVE_IM_STATUS_UNSPECIFIED WeddingReserveIMStatus = 0
	WeddingReserveIMStatus_WEDDING_RESERVE_IM_STATUS_VALID       WeddingReserveIMStatus = 1
	WeddingReserveIMStatus_WEDDING_RESERVE_IM_STATUS_EXPIRED     WeddingReserveIMStatus = 2
)

var WeddingReserveIMStatus_name = map[int32]string{
	0: "WEDDING_RESERVE_IM_STATUS_UNSPECIFIED",
	1: "WEDDING_RESERVE_IM_STATUS_VALID",
	2: "WEDDING_RESERVE_IM_STATUS_EXPIRED",
}
var WeddingReserveIMStatus_value = map[string]int32{
	"WEDDING_RESERVE_IM_STATUS_UNSPECIFIED": 0,
	"WEDDING_RESERVE_IM_STATUS_VALID":       1,
	"WEDDING_RESERVE_IM_STATUS_EXPIRED":     2,
}

func (x WeddingReserveIMStatus) String() string {
	return proto.EnumName(WeddingReserveIMStatus_name, int32(x))
}
func (WeddingReserveIMStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{17}
}

type WeddingPreProgressStage int32

const (
	WeddingPreProgressStage_WEDDING_PRE_PROGRESS_STAGE_UNSPECIFIED     WeddingPreProgressStage = 0
	WeddingPreProgressStage_WEDDING_PRE_PROGRESS_STAGE_ON_MIC          WeddingPreProgressStage = 1
	WeddingPreProgressStage_WEDDING_PRE_PROGRESS_STAGE_SEND_GIFT       WeddingPreProgressStage = 2
	WeddingPreProgressStage_WEDDING_PRE_PROGRESS_STAGE_WEDDING_PREPARE WeddingPreProgressStage = 3
)

var WeddingPreProgressStage_name = map[int32]string{
	0: "WEDDING_PRE_PROGRESS_STAGE_UNSPECIFIED",
	1: "WEDDING_PRE_PROGRESS_STAGE_ON_MIC",
	2: "WEDDING_PRE_PROGRESS_STAGE_SEND_GIFT",
	3: "WEDDING_PRE_PROGRESS_STAGE_WEDDING_PREPARE",
}
var WeddingPreProgressStage_value = map[string]int32{
	"WEDDING_PRE_PROGRESS_STAGE_UNSPECIFIED":     0,
	"WEDDING_PRE_PROGRESS_STAGE_ON_MIC":          1,
	"WEDDING_PRE_PROGRESS_STAGE_SEND_GIFT":       2,
	"WEDDING_PRE_PROGRESS_STAGE_WEDDING_PREPARE": 3,
}

func (x WeddingPreProgressStage) String() string {
	return proto.EnumName(WeddingPreProgressStage_name, int32(x))
}
func (WeddingPreProgressStage) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{18}
}

type SaveWeddingBigScreenRequest_BigScreenOperation int32

const (
	SaveWeddingBigScreenRequest_BIG_SCREEN_OPERATION_UNSPECIFIED SaveWeddingBigScreenRequest_BigScreenOperation = 0
	SaveWeddingBigScreenRequest_BIG_SCREEN_OPERATION_ADD         SaveWeddingBigScreenRequest_BigScreenOperation = 1
	SaveWeddingBigScreenRequest_BIG_SCREEN_OPERATION_DEL         SaveWeddingBigScreenRequest_BigScreenOperation = 2
)

var SaveWeddingBigScreenRequest_BigScreenOperation_name = map[int32]string{
	0: "BIG_SCREEN_OPERATION_UNSPECIFIED",
	1: "BIG_SCREEN_OPERATION_ADD",
	2: "BIG_SCREEN_OPERATION_DEL",
}
var SaveWeddingBigScreenRequest_BigScreenOperation_value = map[string]int32{
	"BIG_SCREEN_OPERATION_UNSPECIFIED": 0,
	"BIG_SCREEN_OPERATION_ADD":         1,
	"BIG_SCREEN_OPERATION_DEL":         2,
}

func (x SaveWeddingBigScreenRequest_BigScreenOperation) String() string {
	return proto.EnumName(SaveWeddingBigScreenRequest_BigScreenOperation_name, int32(x))
}
func (SaveWeddingBigScreenRequest_BigScreenOperation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{85, 0}
}

// 婚礼房阶段配置
type WeddingStageCfg struct {
	Stage                uint32   `protobuf:"varint,1,opt,name=stage,proto3" json:"stage,omitempty"`
	StageName            string   `protobuf:"bytes,2,opt,name=stage_name,json=stageName,proto3" json:"stage_name,omitempty"`
	SubStage             uint32   `protobuf:"varint,3,opt,name=sub_stage,json=subStage,proto3" json:"sub_stage,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingStageCfg) Reset()         { *m = WeddingStageCfg{} }
func (m *WeddingStageCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingStageCfg) ProtoMessage()    {}
func (*WeddingStageCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{0}
}
func (m *WeddingStageCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingStageCfg.Unmarshal(m, b)
}
func (m *WeddingStageCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingStageCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingStageCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingStageCfg.Merge(dst, src)
}
func (m *WeddingStageCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingStageCfg.Size(m)
}
func (m *WeddingStageCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingStageCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingStageCfg proto.InternalMessageInfo

func (m *WeddingStageCfg) GetStage() uint32 {
	if m != nil {
		return m.Stage
	}
	return 0
}

func (m *WeddingStageCfg) GetStageName() string {
	if m != nil {
		return m.StageName
	}
	return ""
}

func (m *WeddingStageCfg) GetSubStage() uint32 {
	if m != nil {
		return m.SubStage
	}
	return 0
}

// 当前婚礼阶段信息
type WeddingStageInfo struct {
	StageCfgList         []*WeddingStageCfg `protobuf:"bytes,1,rep,name=stage_cfg_list,json=stageCfgList,proto3" json:"stage_cfg_list,omitempty"`
	CurrStage            uint32             `protobuf:"varint,2,opt,name=curr_stage,json=currStage,proto3" json:"curr_stage,omitempty"`
	SubStage             uint32             `protobuf:"varint,3,opt,name=sub_stage,json=subStage,proto3" json:"sub_stage,omitempty"`
	StageBeginTime       int64              `protobuf:"varint,4,opt,name=stage_begin_time,json=stageBeginTime,proto3" json:"stage_begin_time,omitempty"`
	StageEndTime         int64              `protobuf:"varint,5,opt,name=stage_end_time,json=stageEndTime,proto3" json:"stage_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *WeddingStageInfo) Reset()         { *m = WeddingStageInfo{} }
func (m *WeddingStageInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingStageInfo) ProtoMessage()    {}
func (*WeddingStageInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{1}
}
func (m *WeddingStageInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingStageInfo.Unmarshal(m, b)
}
func (m *WeddingStageInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingStageInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingStageInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingStageInfo.Merge(dst, src)
}
func (m *WeddingStageInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingStageInfo.Size(m)
}
func (m *WeddingStageInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingStageInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingStageInfo proto.InternalMessageInfo

func (m *WeddingStageInfo) GetStageCfgList() []*WeddingStageCfg {
	if m != nil {
		return m.StageCfgList
	}
	return nil
}

func (m *WeddingStageInfo) GetCurrStage() uint32 {
	if m != nil {
		return m.CurrStage
	}
	return 0
}

func (m *WeddingStageInfo) GetSubStage() uint32 {
	if m != nil {
		return m.SubStage
	}
	return 0
}

func (m *WeddingStageInfo) GetStageBeginTime() int64 {
	if m != nil {
		return m.StageBeginTime
	}
	return 0
}

func (m *WeddingStageInfo) GetStageEndTime() int64 {
	if m != nil {
		return m.StageEndTime
	}
	return 0
}

// 婚礼等级服装信息
type WeddingLevelClothes struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	GroomClothes         []uint32 `protobuf:"varint,2,rep,packed,name=groom_clothes,json=groomClothes,proto3" json:"groom_clothes,omitempty"`
	BrideClothes         []uint32 `protobuf:"varint,3,rep,packed,name=bride_clothes,json=brideClothes,proto3" json:"bride_clothes,omitempty"`
	GroomsmanClothes     []uint32 `protobuf:"varint,4,rep,packed,name=groomsman_clothes,json=groomsmanClothes,proto3" json:"groomsman_clothes,omitempty"`
	BridesmaidClothes    []uint32 `protobuf:"varint,5,rep,packed,name=bridesmaid_clothes,json=bridesmaidClothes,proto3" json:"bridesmaid_clothes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingLevelClothes) Reset()         { *m = WeddingLevelClothes{} }
func (m *WeddingLevelClothes) String() string { return proto.CompactTextString(m) }
func (*WeddingLevelClothes) ProtoMessage()    {}
func (*WeddingLevelClothes) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{2}
}
func (m *WeddingLevelClothes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingLevelClothes.Unmarshal(m, b)
}
func (m *WeddingLevelClothes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingLevelClothes.Marshal(b, m, deterministic)
}
func (dst *WeddingLevelClothes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingLevelClothes.Merge(dst, src)
}
func (m *WeddingLevelClothes) XXX_Size() int {
	return xxx_messageInfo_WeddingLevelClothes.Size(m)
}
func (m *WeddingLevelClothes) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingLevelClothes.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingLevelClothes proto.InternalMessageInfo

func (m *WeddingLevelClothes) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WeddingLevelClothes) GetGroomClothes() []uint32 {
	if m != nil {
		return m.GroomClothes
	}
	return nil
}

func (m *WeddingLevelClothes) GetBrideClothes() []uint32 {
	if m != nil {
		return m.BrideClothes
	}
	return nil
}

func (m *WeddingLevelClothes) GetGroomsmanClothes() []uint32 {
	if m != nil {
		return m.GroomsmanClothes
	}
	return nil
}

func (m *WeddingLevelClothes) GetBridesmaidClothes() []uint32 {
	if m != nil {
		return m.BridesmaidClothes
	}
	return nil
}

type WeddingSceneBoneCfg struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	SeqIndex             uint32   `protobuf:"varint,2,opt,name=seq_index,json=seqIndex,proto3" json:"seq_index,omitempty"`
	AnimationName        string   `protobuf:"bytes,3,opt,name=animation_name,json=animationName,proto3" json:"animation_name,omitempty"`
	BoneId               uint32   `protobuf:"varint,4,opt,name=bone_id,json=boneId,proto3" json:"bone_id,omitempty"`
	BaseBoneId           uint32   `protobuf:"varint,5,opt,name=base_bone_id,json=baseBoneId,proto3" json:"base_bone_id,omitempty"`
	CpItemIdList         []uint32 `protobuf:"varint,6,rep,packed,name=cp_item_id_list,json=cpItemIdList,proto3" json:"cp_item_id_list,omitempty"`
	MaleItemIdList       []uint32 `protobuf:"varint,7,rep,packed,name=male_item_id_list,json=maleItemIdList,proto3" json:"male_item_id_list,omitempty"`
	FemaleItemIdList     []uint32 `protobuf:"varint,8,rep,packed,name=female_item_id_list,json=femaleItemIdList,proto3" json:"female_item_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingSceneBoneCfg) Reset()         { *m = WeddingSceneBoneCfg{} }
func (m *WeddingSceneBoneCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingSceneBoneCfg) ProtoMessage()    {}
func (*WeddingSceneBoneCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{3}
}
func (m *WeddingSceneBoneCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingSceneBoneCfg.Unmarshal(m, b)
}
func (m *WeddingSceneBoneCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingSceneBoneCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingSceneBoneCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingSceneBoneCfg.Merge(dst, src)
}
func (m *WeddingSceneBoneCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingSceneBoneCfg.Size(m)
}
func (m *WeddingSceneBoneCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingSceneBoneCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingSceneBoneCfg proto.InternalMessageInfo

func (m *WeddingSceneBoneCfg) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WeddingSceneBoneCfg) GetSeqIndex() uint32 {
	if m != nil {
		return m.SeqIndex
	}
	return 0
}

func (m *WeddingSceneBoneCfg) GetAnimationName() string {
	if m != nil {
		return m.AnimationName
	}
	return ""
}

func (m *WeddingSceneBoneCfg) GetBoneId() uint32 {
	if m != nil {
		return m.BoneId
	}
	return 0
}

func (m *WeddingSceneBoneCfg) GetBaseBoneId() uint32 {
	if m != nil {
		return m.BaseBoneId
	}
	return 0
}

func (m *WeddingSceneBoneCfg) GetCpItemIdList() []uint32 {
	if m != nil {
		return m.CpItemIdList
	}
	return nil
}

func (m *WeddingSceneBoneCfg) GetMaleItemIdList() []uint32 {
	if m != nil {
		return m.MaleItemIdList
	}
	return nil
}

func (m *WeddingSceneBoneCfg) GetFemaleItemIdList() []uint32 {
	if m != nil {
		return m.FemaleItemIdList
	}
	return nil
}

type WeddingSceneCfg struct {
	Scene                uint32                 `protobuf:"varint,1,opt,name=scene,proto3" json:"scene,omitempty"`
	SceneResource        string                 `protobuf:"bytes,2,opt,name=scene_resource,json=sceneResource,proto3" json:"scene_resource,omitempty"`
	SceneResourceMd5     string                 `protobuf:"bytes,3,opt,name=scene_resource_md5,json=sceneResourceMd5,proto3" json:"scene_resource_md5,omitempty"`
	BoneCfgList          []*WeddingSceneBoneCfg `protobuf:"bytes,4,rep,name=bone_cfg_list,json=boneCfgList,proto3" json:"bone_cfg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *WeddingSceneCfg) Reset()         { *m = WeddingSceneCfg{} }
func (m *WeddingSceneCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingSceneCfg) ProtoMessage()    {}
func (*WeddingSceneCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{4}
}
func (m *WeddingSceneCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingSceneCfg.Unmarshal(m, b)
}
func (m *WeddingSceneCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingSceneCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingSceneCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingSceneCfg.Merge(dst, src)
}
func (m *WeddingSceneCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingSceneCfg.Size(m)
}
func (m *WeddingSceneCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingSceneCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingSceneCfg proto.InternalMessageInfo

func (m *WeddingSceneCfg) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *WeddingSceneCfg) GetSceneResource() string {
	if m != nil {
		return m.SceneResource
	}
	return ""
}

func (m *WeddingSceneCfg) GetSceneResourceMd5() string {
	if m != nil {
		return m.SceneResourceMd5
	}
	return ""
}

func (m *WeddingSceneCfg) GetBoneCfgList() []*WeddingSceneBoneCfg {
	if m != nil {
		return m.BoneCfgList
	}
	return nil
}

// 幸福值等级配置信息
type HappinessLevelInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	LevelValue           uint32   `protobuf:"varint,2,opt,name=level_value,json=levelValue,proto3" json:"level_value,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Background           string   `protobuf:"bytes,4,opt,name=background,proto3" json:"background,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HappinessLevelInfo) Reset()         { *m = HappinessLevelInfo{} }
func (m *HappinessLevelInfo) String() string { return proto.CompactTextString(m) }
func (*HappinessLevelInfo) ProtoMessage()    {}
func (*HappinessLevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{5}
}
func (m *HappinessLevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HappinessLevelInfo.Unmarshal(m, b)
}
func (m *HappinessLevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HappinessLevelInfo.Marshal(b, m, deterministic)
}
func (dst *HappinessLevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HappinessLevelInfo.Merge(dst, src)
}
func (m *HappinessLevelInfo) XXX_Size() int {
	return xxx_messageInfo_HappinessLevelInfo.Size(m)
}
func (m *HappinessLevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HappinessLevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HappinessLevelInfo proto.InternalMessageInfo

func (m *HappinessLevelInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *HappinessLevelInfo) GetLevelValue() uint32 {
	if m != nil {
		return m.LevelValue
	}
	return 0
}

func (m *HappinessLevelInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *HappinessLevelInfo) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

// 幸福值配置信息
type HappinessConfigInfo struct {
	Config               []*HappinessLevelInfo `protobuf:"bytes,1,rep,name=config,proto3" json:"config,omitempty"`
	UpgradeVideo         string                `protobuf:"bytes,2,opt,name=upgrade_video,json=upgradeVideo,proto3" json:"upgrade_video,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *HappinessConfigInfo) Reset()         { *m = HappinessConfigInfo{} }
func (m *HappinessConfigInfo) String() string { return proto.CompactTextString(m) }
func (*HappinessConfigInfo) ProtoMessage()    {}
func (*HappinessConfigInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{6}
}
func (m *HappinessConfigInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HappinessConfigInfo.Unmarshal(m, b)
}
func (m *HappinessConfigInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HappinessConfigInfo.Marshal(b, m, deterministic)
}
func (dst *HappinessConfigInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HappinessConfigInfo.Merge(dst, src)
}
func (m *HappinessConfigInfo) XXX_Size() int {
	return xxx_messageInfo_HappinessConfigInfo.Size(m)
}
func (m *HappinessConfigInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HappinessConfigInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HappinessConfigInfo proto.InternalMessageInfo

func (m *HappinessConfigInfo) GetConfig() []*HappinessLevelInfo {
	if m != nil {
		return m.Config
	}
	return nil
}

func (m *HappinessConfigInfo) GetUpgradeVideo() string {
	if m != nil {
		return m.UpgradeVideo
	}
	return ""
}

// 婚礼房等级背景配置
type WeddingLevelBackgroundCfg struct {
	Level                    uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	BackgroundPicture        string   `protobuf:"bytes,2,opt,name=background_picture,json=backgroundPicture,proto3" json:"background_picture,omitempty"`
	BackgroundMp4Url         string   `protobuf:"bytes,3,opt,name=background_mp4_url,json=backgroundMp4Url,proto3" json:"background_mp4_url,omitempty"`
	SpecialBackgroundPicture string   `protobuf:"bytes,4,opt,name=special_background_picture,json=specialBackgroundPicture,proto3" json:"special_background_picture,omitempty"`
	SpecialBackgroundMp4Url  string   `protobuf:"bytes,5,opt,name=special_background_mp4_url,json=specialBackgroundMp4Url,proto3" json:"special_background_mp4_url,omitempty"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *WeddingLevelBackgroundCfg) Reset()         { *m = WeddingLevelBackgroundCfg{} }
func (m *WeddingLevelBackgroundCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingLevelBackgroundCfg) ProtoMessage()    {}
func (*WeddingLevelBackgroundCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{7}
}
func (m *WeddingLevelBackgroundCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingLevelBackgroundCfg.Unmarshal(m, b)
}
func (m *WeddingLevelBackgroundCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingLevelBackgroundCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingLevelBackgroundCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingLevelBackgroundCfg.Merge(dst, src)
}
func (m *WeddingLevelBackgroundCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingLevelBackgroundCfg.Size(m)
}
func (m *WeddingLevelBackgroundCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingLevelBackgroundCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingLevelBackgroundCfg proto.InternalMessageInfo

func (m *WeddingLevelBackgroundCfg) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WeddingLevelBackgroundCfg) GetBackgroundPicture() string {
	if m != nil {
		return m.BackgroundPicture
	}
	return ""
}

func (m *WeddingLevelBackgroundCfg) GetBackgroundMp4Url() string {
	if m != nil {
		return m.BackgroundMp4Url
	}
	return ""
}

func (m *WeddingLevelBackgroundCfg) GetSpecialBackgroundPicture() string {
	if m != nil {
		return m.SpecialBackgroundPicture
	}
	return ""
}

func (m *WeddingLevelBackgroundCfg) GetSpecialBackgroundMp4Url() string {
	if m != nil {
		return m.SpecialBackgroundMp4Url
	}
	return ""
}

type WeddingResource struct {
	ResourceUrl          string   `protobuf:"bytes,1,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	ResourceMd5          string   `protobuf:"bytes,2,opt,name=resource_md5,json=resourceMd5,proto3" json:"resource_md5,omitempty"`
	CpBoneId             uint32   `protobuf:"varint,3,opt,name=cp_bone_id,json=cpBoneId,proto3" json:"cp_bone_id,omitempty"`
	ItemIdList           []uint32 `protobuf:"varint,4,rep,packed,name=item_id_list,json=itemIdList,proto3" json:"item_id_list,omitempty"`
	BaseCpBoneId         uint32   `protobuf:"varint,5,opt,name=base_cp_bone_id,json=baseCpBoneId,proto3" json:"base_cp_bone_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingResource) Reset()         { *m = WeddingResource{} }
func (m *WeddingResource) String() string { return proto.CompactTextString(m) }
func (*WeddingResource) ProtoMessage()    {}
func (*WeddingResource) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{8}
}
func (m *WeddingResource) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingResource.Unmarshal(m, b)
}
func (m *WeddingResource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingResource.Marshal(b, m, deterministic)
}
func (dst *WeddingResource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingResource.Merge(dst, src)
}
func (m *WeddingResource) XXX_Size() int {
	return xxx_messageInfo_WeddingResource.Size(m)
}
func (m *WeddingResource) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingResource.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingResource proto.InternalMessageInfo

func (m *WeddingResource) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *WeddingResource) GetResourceMd5() string {
	if m != nil {
		return m.ResourceMd5
	}
	return ""
}

func (m *WeddingResource) GetCpBoneId() uint32 {
	if m != nil {
		return m.CpBoneId
	}
	return 0
}

func (m *WeddingResource) GetItemIdList() []uint32 {
	if m != nil {
		return m.ItemIdList
	}
	return nil
}

func (m *WeddingResource) GetBaseCpBoneId() uint32 {
	if m != nil {
		return m.BaseCpBoneId
	}
	return 0
}

// 婚礼房主题配置
type WeddingRoomThemeCfg struct {
	ThemeId                uint32                       `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	ThemeResource          string                       `protobuf:"bytes,2,opt,name=theme_resource,json=themeResource,proto3" json:"theme_resource,omitempty"`
	ThemeResourceMd5       string                       `protobuf:"bytes,3,opt,name=theme_resource_md5,json=themeResourceMd5,proto3" json:"theme_resource_md5,omitempty"`
	SceneCfgList           []*WeddingSceneCfg           `protobuf:"bytes,4,rep,name=scene_cfg_list,json=sceneCfgList,proto3" json:"scene_cfg_list,omitempty"`
	ChairGameResource      string                       `protobuf:"bytes,5,opt,name=chair_game_resource,json=chairGameResource,proto3" json:"chair_game_resource,omitempty"`            // Deprecated: Do not use.
	ChairGameResourceMd5   string                       `protobuf:"bytes,6,opt,name=chair_game_resource_md5,json=chairGameResourceMd5,proto3" json:"chair_game_resource_md5,omitempty"` // Deprecated: Do not use.
	LevelClothesList       []*WeddingLevelClothes       `protobuf:"bytes,7,rep,name=level_clothes_list,json=levelClothesList,proto3" json:"level_clothes_list,omitempty"`
	LevelBackgroundList    []*WeddingLevelBackgroundCfg `protobuf:"bytes,8,rep,name=level_background_list,json=levelBackgroundList,proto3" json:"level_background_list,omitempty"`
	WeddingPreviewResource *WeddingResource             `protobuf:"bytes,9,opt,name=wedding_preview_resource,json=weddingPreviewResource,proto3" json:"wedding_preview_resource,omitempty"`
	ChairResourceId        uint32                       `protobuf:"varint,10,opt,name=chair_resource_id,json=chairResourceId,proto3" json:"chair_resource_id,omitempty"` // Deprecated: Do not use.
	IsFreeTheme            bool                         `protobuf:"varint,11,opt,name=is_free_theme,json=isFreeTheme,proto3" json:"is_free_theme,omitempty"`
	ChairResCfg            *ChairGameResourceCfg        `protobuf:"bytes,12,opt,name=chair_res_cfg,json=chairResCfg,proto3" json:"chair_res_cfg,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                     `json:"-"`
	XXX_unrecognized       []byte                       `json:"-"`
	XXX_sizecache          int32                        `json:"-"`
}

func (m *WeddingRoomThemeCfg) Reset()         { *m = WeddingRoomThemeCfg{} }
func (m *WeddingRoomThemeCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingRoomThemeCfg) ProtoMessage()    {}
func (*WeddingRoomThemeCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{9}
}
func (m *WeddingRoomThemeCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingRoomThemeCfg.Unmarshal(m, b)
}
func (m *WeddingRoomThemeCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingRoomThemeCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingRoomThemeCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingRoomThemeCfg.Merge(dst, src)
}
func (m *WeddingRoomThemeCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingRoomThemeCfg.Size(m)
}
func (m *WeddingRoomThemeCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingRoomThemeCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingRoomThemeCfg proto.InternalMessageInfo

func (m *WeddingRoomThemeCfg) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *WeddingRoomThemeCfg) GetThemeResource() string {
	if m != nil {
		return m.ThemeResource
	}
	return ""
}

func (m *WeddingRoomThemeCfg) GetThemeResourceMd5() string {
	if m != nil {
		return m.ThemeResourceMd5
	}
	return ""
}

func (m *WeddingRoomThemeCfg) GetSceneCfgList() []*WeddingSceneCfg {
	if m != nil {
		return m.SceneCfgList
	}
	return nil
}

// Deprecated: Do not use.
func (m *WeddingRoomThemeCfg) GetChairGameResource() string {
	if m != nil {
		return m.ChairGameResource
	}
	return ""
}

// Deprecated: Do not use.
func (m *WeddingRoomThemeCfg) GetChairGameResourceMd5() string {
	if m != nil {
		return m.ChairGameResourceMd5
	}
	return ""
}

func (m *WeddingRoomThemeCfg) GetLevelClothesList() []*WeddingLevelClothes {
	if m != nil {
		return m.LevelClothesList
	}
	return nil
}

func (m *WeddingRoomThemeCfg) GetLevelBackgroundList() []*WeddingLevelBackgroundCfg {
	if m != nil {
		return m.LevelBackgroundList
	}
	return nil
}

func (m *WeddingRoomThemeCfg) GetWeddingPreviewResource() *WeddingResource {
	if m != nil {
		return m.WeddingPreviewResource
	}
	return nil
}

// Deprecated: Do not use.
func (m *WeddingRoomThemeCfg) GetChairResourceId() uint32 {
	if m != nil {
		return m.ChairResourceId
	}
	return 0
}

func (m *WeddingRoomThemeCfg) GetIsFreeTheme() bool {
	if m != nil {
		return m.IsFreeTheme
	}
	return false
}

func (m *WeddingRoomThemeCfg) GetChairResCfg() *ChairGameResourceCfg {
	if m != nil {
		return m.ChairResCfg
	}
	return nil
}

// 抢椅子 椅子资源配置
type ChairGameResourceCfg struct {
	ChairPic             string   `protobuf:"bytes,1,opt,name=chair_pic,json=chairPic,proto3" json:"chair_pic,omitempty"`
	SittingPoseFemaleId  uint32   `protobuf:"varint,2,opt,name=sitting_pose_female_id,json=sittingPoseFemaleId,proto3" json:"sitting_pose_female_id,omitempty"`
	SittingPoseMaleId    uint32   `protobuf:"varint,3,opt,name=sitting_pose_male_id,json=sittingPoseMaleId,proto3" json:"sitting_pose_male_id,omitempty"`
	StandbyFemaleId      uint32   `protobuf:"varint,4,opt,name=standby_female_id,json=standbyFemaleId,proto3" json:"standby_female_id,omitempty"`
	StandbyMaleId        uint32   `protobuf:"varint,5,opt,name=standby_male_id,json=standbyMaleId,proto3" json:"standby_male_id,omitempty"`
	FailFemaleIds        []uint32 `protobuf:"varint,6,rep,packed,name=fail_female_ids,json=failFemaleIds,proto3" json:"fail_female_ids,omitempty"`
	FailMaleIds          []uint32 `protobuf:"varint,7,rep,packed,name=fail_male_ids,json=failMaleIds,proto3" json:"fail_male_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChairGameResourceCfg) Reset()         { *m = ChairGameResourceCfg{} }
func (m *ChairGameResourceCfg) String() string { return proto.CompactTextString(m) }
func (*ChairGameResourceCfg) ProtoMessage()    {}
func (*ChairGameResourceCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{10}
}
func (m *ChairGameResourceCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChairGameResourceCfg.Unmarshal(m, b)
}
func (m *ChairGameResourceCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChairGameResourceCfg.Marshal(b, m, deterministic)
}
func (dst *ChairGameResourceCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChairGameResourceCfg.Merge(dst, src)
}
func (m *ChairGameResourceCfg) XXX_Size() int {
	return xxx_messageInfo_ChairGameResourceCfg.Size(m)
}
func (m *ChairGameResourceCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ChairGameResourceCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ChairGameResourceCfg proto.InternalMessageInfo

func (m *ChairGameResourceCfg) GetChairPic() string {
	if m != nil {
		return m.ChairPic
	}
	return ""
}

func (m *ChairGameResourceCfg) GetSittingPoseFemaleId() uint32 {
	if m != nil {
		return m.SittingPoseFemaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetSittingPoseMaleId() uint32 {
	if m != nil {
		return m.SittingPoseMaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetStandbyFemaleId() uint32 {
	if m != nil {
		return m.StandbyFemaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetStandbyMaleId() uint32 {
	if m != nil {
		return m.StandbyMaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetFailFemaleIds() []uint32 {
	if m != nil {
		return m.FailFemaleIds
	}
	return nil
}

func (m *ChairGameResourceCfg) GetFailMaleIds() []uint32 {
	if m != nil {
		return m.FailMaleIds
	}
	return nil
}

// 婚礼新人信息
type WeddingCpMemInfo struct {
	UserProfile          *app.UserProfile `protobuf:"bytes,1,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WeddingCpMemInfo) Reset()         { *m = WeddingCpMemInfo{} }
func (m *WeddingCpMemInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingCpMemInfo) ProtoMessage()    {}
func (*WeddingCpMemInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{11}
}
func (m *WeddingCpMemInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingCpMemInfo.Unmarshal(m, b)
}
func (m *WeddingCpMemInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingCpMemInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingCpMemInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingCpMemInfo.Merge(dst, src)
}
func (m *WeddingCpMemInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingCpMemInfo.Size(m)
}
func (m *WeddingCpMemInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingCpMemInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingCpMemInfo proto.InternalMessageInfo

func (m *WeddingCpMemInfo) GetUserProfile() *app.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

// 新人纪念视频
type WeddingMemorialVideo struct {
	ResourceUrl          string   `protobuf:"bytes,1,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	ResourceMd5          string   `protobuf:"bytes,2,opt,name=resource_md5,json=resourceMd5,proto3" json:"resource_md5,omitempty"`
	ResourceJson         string   `protobuf:"bytes,3,opt,name=resource_json,json=resourceJson,proto3" json:"resource_json,omitempty"`
	UserPictures         []string `protobuf:"bytes,4,rep,name=user_pictures,json=userPictures,proto3" json:"user_pictures,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingMemorialVideo) Reset()         { *m = WeddingMemorialVideo{} }
func (m *WeddingMemorialVideo) String() string { return proto.CompactTextString(m) }
func (*WeddingMemorialVideo) ProtoMessage()    {}
func (*WeddingMemorialVideo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{12}
}
func (m *WeddingMemorialVideo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingMemorialVideo.Unmarshal(m, b)
}
func (m *WeddingMemorialVideo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingMemorialVideo.Marshal(b, m, deterministic)
}
func (dst *WeddingMemorialVideo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingMemorialVideo.Merge(dst, src)
}
func (m *WeddingMemorialVideo) XXX_Size() int {
	return xxx_messageInfo_WeddingMemorialVideo.Size(m)
}
func (m *WeddingMemorialVideo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingMemorialVideo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingMemorialVideo proto.InternalMessageInfo

func (m *WeddingMemorialVideo) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *WeddingMemorialVideo) GetResourceMd5() string {
	if m != nil {
		return m.ResourceMd5
	}
	return ""
}

func (m *WeddingMemorialVideo) GetResourceJson() string {
	if m != nil {
		return m.ResourceJson
	}
	return ""
}

func (m *WeddingMemorialVideo) GetUserPictures() []string {
	if m != nil {
		return m.UserPictures
	}
	return nil
}

// 骨骼配置
type WeddingBoneCfg struct {
	MaleBoneId           uint32   `protobuf:"varint,1,opt,name=male_bone_id,json=maleBoneId,proto3" json:"male_bone_id,omitempty"`
	FemaleBoneId         uint32   `protobuf:"varint,2,opt,name=female_bone_id,json=femaleBoneId,proto3" json:"female_bone_id,omitempty"`
	BaseMaleBoneId       uint32   `protobuf:"varint,3,opt,name=base_male_bone_id,json=baseMaleBoneId,proto3" json:"base_male_bone_id,omitempty"`
	BaseFemaleBoneId     uint32   `protobuf:"varint,4,opt,name=base_female_bone_id,json=baseFemaleBoneId,proto3" json:"base_female_bone_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingBoneCfg) Reset()         { *m = WeddingBoneCfg{} }
func (m *WeddingBoneCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingBoneCfg) ProtoMessage()    {}
func (*WeddingBoneCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{13}
}
func (m *WeddingBoneCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingBoneCfg.Unmarshal(m, b)
}
func (m *WeddingBoneCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingBoneCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingBoneCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingBoneCfg.Merge(dst, src)
}
func (m *WeddingBoneCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingBoneCfg.Size(m)
}
func (m *WeddingBoneCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingBoneCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingBoneCfg proto.InternalMessageInfo

func (m *WeddingBoneCfg) GetMaleBoneId() uint32 {
	if m != nil {
		return m.MaleBoneId
	}
	return 0
}

func (m *WeddingBoneCfg) GetFemaleBoneId() uint32 {
	if m != nil {
		return m.FemaleBoneId
	}
	return 0
}

func (m *WeddingBoneCfg) GetBaseMaleBoneId() uint32 {
	if m != nil {
		return m.BaseMaleBoneId
	}
	return 0
}

func (m *WeddingBoneCfg) GetBaseFemaleBoneId() uint32 {
	if m != nil {
		return m.BaseFemaleBoneId
	}
	return 0
}

// 婚礼信息
type WeddingInfo struct {
	WeddingId            int64                 `protobuf:"varint,1,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	StageInfo            *WeddingStageInfo     `protobuf:"bytes,2,opt,name=stage_info,json=stageInfo,proto3" json:"stage_info,omitempty"`
	ThemeCfg             *WeddingRoomThemeCfg  `protobuf:"bytes,3,opt,name=theme_cfg,json=themeCfg,proto3" json:"theme_cfg,omitempty"`
	CurrLevel            uint32                `protobuf:"varint,4,opt,name=curr_level,json=currLevel,proto3" json:"curr_level,omitempty"`
	Bride                *WeddingCpMemInfo     `protobuf:"bytes,5,opt,name=bride,proto3" json:"bride,omitempty"`
	Groom                *WeddingCpMemInfo     `protobuf:"bytes,6,opt,name=groom,proto3" json:"groom,omitempty"`
	StartTime            int64                 `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64                 `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	WeddingMemorialVideo *WeddingMemorialVideo `protobuf:"bytes,9,opt,name=wedding_memorial_video,json=weddingMemorialVideo,proto3" json:"wedding_memorial_video,omitempty"`
	ChairGameEntry       bool                  `protobuf:"varint,10,opt,name=chair_game_entry,json=chairGameEntry,proto3" json:"chair_game_entry,omitempty"`
	BridesmaidManList    []uint32              `protobuf:"varint,11,rep,packed,name=bridesmaid_man_list,json=bridesmaidManList,proto3" json:"bridesmaid_man_list,omitempty"`
	HappinessConfig      *HappinessConfigInfo  `protobuf:"bytes,12,opt,name=happiness_config,json=happinessConfig,proto3" json:"happiness_config,omitempty"`
	HappinessValue       uint32                `protobuf:"varint,13,opt,name=happiness_value,json=happinessValue,proto3" json:"happiness_value,omitempty"`
	BoneCfg              *WeddingBoneCfg       `protobuf:"bytes,14,opt,name=bone_cfg,json=boneCfg,proto3" json:"bone_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *WeddingInfo) Reset()         { *m = WeddingInfo{} }
func (m *WeddingInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingInfo) ProtoMessage()    {}
func (*WeddingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{14}
}
func (m *WeddingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingInfo.Unmarshal(m, b)
}
func (m *WeddingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingInfo.Merge(dst, src)
}
func (m *WeddingInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingInfo.Size(m)
}
func (m *WeddingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingInfo proto.InternalMessageInfo

func (m *WeddingInfo) GetWeddingId() int64 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *WeddingInfo) GetStageInfo() *WeddingStageInfo {
	if m != nil {
		return m.StageInfo
	}
	return nil
}

func (m *WeddingInfo) GetThemeCfg() *WeddingRoomThemeCfg {
	if m != nil {
		return m.ThemeCfg
	}
	return nil
}

func (m *WeddingInfo) GetCurrLevel() uint32 {
	if m != nil {
		return m.CurrLevel
	}
	return 0
}

func (m *WeddingInfo) GetBride() *WeddingCpMemInfo {
	if m != nil {
		return m.Bride
	}
	return nil
}

func (m *WeddingInfo) GetGroom() *WeddingCpMemInfo {
	if m != nil {
		return m.Groom
	}
	return nil
}

func (m *WeddingInfo) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *WeddingInfo) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *WeddingInfo) GetWeddingMemorialVideo() *WeddingMemorialVideo {
	if m != nil {
		return m.WeddingMemorialVideo
	}
	return nil
}

func (m *WeddingInfo) GetChairGameEntry() bool {
	if m != nil {
		return m.ChairGameEntry
	}
	return false
}

func (m *WeddingInfo) GetBridesmaidManList() []uint32 {
	if m != nil {
		return m.BridesmaidManList
	}
	return nil
}

func (m *WeddingInfo) GetHappinessConfig() *HappinessConfigInfo {
	if m != nil {
		return m.HappinessConfig
	}
	return nil
}

func (m *WeddingInfo) GetHappinessValue() uint32 {
	if m != nil {
		return m.HappinessValue
	}
	return 0
}

func (m *WeddingInfo) GetBoneCfg() *WeddingBoneCfg {
	if m != nil {
		return m.BoneCfg
	}
	return nil
}

type WeddingClothesInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ClothesIdList        []uint32 `protobuf:"varint,2,rep,packed,name=clothes_id_list,json=clothesIdList,proto3" json:"clothes_id_list,omitempty"`
	Orientation          uint32   `protobuf:"varint,3,opt,name=orientation,proto3" json:"orientation,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingClothesInfo) Reset()         { *m = WeddingClothesInfo{} }
func (m *WeddingClothesInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingClothesInfo) ProtoMessage()    {}
func (*WeddingClothesInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{15}
}
func (m *WeddingClothesInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingClothesInfo.Unmarshal(m, b)
}
func (m *WeddingClothesInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingClothesInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingClothesInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingClothesInfo.Merge(dst, src)
}
func (m *WeddingClothesInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingClothesInfo.Size(m)
}
func (m *WeddingClothesInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingClothesInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingClothesInfo proto.InternalMessageInfo

func (m *WeddingClothesInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WeddingClothesInfo) GetClothesIdList() []uint32 {
	if m != nil {
		return m.ClothesIdList
	}
	return nil
}

func (m *WeddingClothesInfo) GetOrientation() uint32 {
	if m != nil {
		return m.Orientation
	}
	return 0
}

// 批量获取用户服装信息请求
type BatchGetUserWeddingClothesRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	UidList              []uint32     `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchGetUserWeddingClothesRequest) Reset()         { *m = BatchGetUserWeddingClothesRequest{} }
func (m *BatchGetUserWeddingClothesRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserWeddingClothesRequest) ProtoMessage()    {}
func (*BatchGetUserWeddingClothesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{16}
}
func (m *BatchGetUserWeddingClothesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserWeddingClothesRequest.Unmarshal(m, b)
}
func (m *BatchGetUserWeddingClothesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserWeddingClothesRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserWeddingClothesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserWeddingClothesRequest.Merge(dst, src)
}
func (m *BatchGetUserWeddingClothesRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserWeddingClothesRequest.Size(m)
}
func (m *BatchGetUserWeddingClothesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserWeddingClothesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserWeddingClothesRequest proto.InternalMessageInfo

func (m *BatchGetUserWeddingClothesRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatchGetUserWeddingClothesRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *BatchGetUserWeddingClothesRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// 批量获取用户服装信息响应
type BatchGetUserWeddingClothesResponse struct {
	BaseResp             *app.BaseResp         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ClothesInfoList      []*WeddingClothesInfo `protobuf:"bytes,2,rep,name=clothes_info_list,json=clothesInfoList,proto3" json:"clothes_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BatchGetUserWeddingClothesResponse) Reset()         { *m = BatchGetUserWeddingClothesResponse{} }
func (m *BatchGetUserWeddingClothesResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserWeddingClothesResponse) ProtoMessage()    {}
func (*BatchGetUserWeddingClothesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{17}
}
func (m *BatchGetUserWeddingClothesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserWeddingClothesResponse.Unmarshal(m, b)
}
func (m *BatchGetUserWeddingClothesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserWeddingClothesResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserWeddingClothesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserWeddingClothesResponse.Merge(dst, src)
}
func (m *BatchGetUserWeddingClothesResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserWeddingClothesResponse.Size(m)
}
func (m *BatchGetUserWeddingClothesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserWeddingClothesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserWeddingClothesResponse proto.InternalMessageInfo

func (m *BatchGetUserWeddingClothesResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BatchGetUserWeddingClothesResponse) GetClothesInfoList() []*WeddingClothesInfo {
	if m != nil {
		return m.ClothesInfoList
	}
	return nil
}

// 婚礼房麦上用户服装变更通知信息
type WeddingClothesChangeOpt struct {
	Cid                  uint32                `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	ServerTimeMs         int64                 `protobuf:"varint,2,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	ClothesInfoList      []*WeddingClothesInfo `protobuf:"bytes,3,rep,name=clothes_info_list,json=clothesInfoList,proto3" json:"clothes_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *WeddingClothesChangeOpt) Reset()         { *m = WeddingClothesChangeOpt{} }
func (m *WeddingClothesChangeOpt) String() string { return proto.CompactTextString(m) }
func (*WeddingClothesChangeOpt) ProtoMessage()    {}
func (*WeddingClothesChangeOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{18}
}
func (m *WeddingClothesChangeOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingClothesChangeOpt.Unmarshal(m, b)
}
func (m *WeddingClothesChangeOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingClothesChangeOpt.Marshal(b, m, deterministic)
}
func (dst *WeddingClothesChangeOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingClothesChangeOpt.Merge(dst, src)
}
func (m *WeddingClothesChangeOpt) XXX_Size() int {
	return xxx_messageInfo_WeddingClothesChangeOpt.Size(m)
}
func (m *WeddingClothesChangeOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingClothesChangeOpt.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingClothesChangeOpt proto.InternalMessageInfo

func (m *WeddingClothesChangeOpt) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *WeddingClothesChangeOpt) GetServerTimeMs() int64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *WeddingClothesChangeOpt) GetClothesInfoList() []*WeddingClothesInfo {
	if m != nil {
		return m.ClothesInfoList
	}
	return nil
}

// 婚礼阶段变化通知信息
type WeddingStageChangeOpt struct {
	Cid                  uint32                `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	ServerTimeMs         int64                 `protobuf:"varint,2,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	WeddingInfo          *WeddingInfo          `protobuf:"bytes,3,opt,name=wedding_info,json=weddingInfo,proto3" json:"wedding_info,omitempty"`
	ClothesInfoList      []*WeddingClothesInfo `protobuf:"bytes,4,rep,name=clothes_info_list,json=clothesInfoList,proto3" json:"clothes_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *WeddingStageChangeOpt) Reset()         { *m = WeddingStageChangeOpt{} }
func (m *WeddingStageChangeOpt) String() string { return proto.CompactTextString(m) }
func (*WeddingStageChangeOpt) ProtoMessage()    {}
func (*WeddingStageChangeOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{19}
}
func (m *WeddingStageChangeOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingStageChangeOpt.Unmarshal(m, b)
}
func (m *WeddingStageChangeOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingStageChangeOpt.Marshal(b, m, deterministic)
}
func (dst *WeddingStageChangeOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingStageChangeOpt.Merge(dst, src)
}
func (m *WeddingStageChangeOpt) XXX_Size() int {
	return xxx_messageInfo_WeddingStageChangeOpt.Size(m)
}
func (m *WeddingStageChangeOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingStageChangeOpt.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingStageChangeOpt proto.InternalMessageInfo

func (m *WeddingStageChangeOpt) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *WeddingStageChangeOpt) GetServerTimeMs() int64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *WeddingStageChangeOpt) GetWeddingInfo() *WeddingInfo {
	if m != nil {
		return m.WeddingInfo
	}
	return nil
}

func (m *WeddingStageChangeOpt) GetClothesInfoList() []*WeddingClothesInfo {
	if m != nil {
		return m.ClothesInfoList
	}
	return nil
}

// 婚礼等级变化通知信息
type WeddingLevelChangeOpt struct {
	Cid                  uint32                `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	ServerTimeMs         int64                 `protobuf:"varint,2,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	CurrLevel            uint32                `protobuf:"varint,3,opt,name=curr_level,json=currLevel,proto3" json:"curr_level,omitempty"`
	ClothesInfoList      []*WeddingClothesInfo `protobuf:"bytes,4,rep,name=clothes_info_list,json=clothesInfoList,proto3" json:"clothes_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *WeddingLevelChangeOpt) Reset()         { *m = WeddingLevelChangeOpt{} }
func (m *WeddingLevelChangeOpt) String() string { return proto.CompactTextString(m) }
func (*WeddingLevelChangeOpt) ProtoMessage()    {}
func (*WeddingLevelChangeOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{20}
}
func (m *WeddingLevelChangeOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingLevelChangeOpt.Unmarshal(m, b)
}
func (m *WeddingLevelChangeOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingLevelChangeOpt.Marshal(b, m, deterministic)
}
func (dst *WeddingLevelChangeOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingLevelChangeOpt.Merge(dst, src)
}
func (m *WeddingLevelChangeOpt) XXX_Size() int {
	return xxx_messageInfo_WeddingLevelChangeOpt.Size(m)
}
func (m *WeddingLevelChangeOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingLevelChangeOpt.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingLevelChangeOpt proto.InternalMessageInfo

func (m *WeddingLevelChangeOpt) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *WeddingLevelChangeOpt) GetServerTimeMs() int64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *WeddingLevelChangeOpt) GetCurrLevel() uint32 {
	if m != nil {
		return m.CurrLevel
	}
	return 0
}

func (m *WeddingLevelChangeOpt) GetClothesInfoList() []*WeddingClothesInfo {
	if m != nil {
		return m.ClothesInfoList
	}
	return nil
}

// 婚礼场景动画通知信息
type WeddingSceneNotifyOpt struct {
	Cid                  uint32                 `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	ServerTimeMs         int64                  `protobuf:"varint,2,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	SceneCfg             *WeddingSceneCfg       `protobuf:"bytes,3,opt,name=scene_cfg,json=sceneCfg,proto3" json:"scene_cfg,omitempty"`
	CurrLevel            uint32                 `protobuf:"varint,4,opt,name=curr_level,json=currLevel,proto3" json:"curr_level,omitempty"`
	ExtOpt               string                 `protobuf:"bytes,5,opt,name=ext_opt,json=extOpt,proto3" json:"ext_opt,omitempty"`
	ExtOptBytes          []byte                 `protobuf:"bytes,6,opt,name=ext_opt_bytes,json=extOptBytes,proto3" json:"ext_opt_bytes,omitempty"`
	ReservePresent       *WeddingReservePresent `protobuf:"bytes,7,opt,name=reserve_present,json=reservePresent,proto3" json:"reserve_present,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *WeddingSceneNotifyOpt) Reset()         { *m = WeddingSceneNotifyOpt{} }
func (m *WeddingSceneNotifyOpt) String() string { return proto.CompactTextString(m) }
func (*WeddingSceneNotifyOpt) ProtoMessage()    {}
func (*WeddingSceneNotifyOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{21}
}
func (m *WeddingSceneNotifyOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingSceneNotifyOpt.Unmarshal(m, b)
}
func (m *WeddingSceneNotifyOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingSceneNotifyOpt.Marshal(b, m, deterministic)
}
func (dst *WeddingSceneNotifyOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingSceneNotifyOpt.Merge(dst, src)
}
func (m *WeddingSceneNotifyOpt) XXX_Size() int {
	return xxx_messageInfo_WeddingSceneNotifyOpt.Size(m)
}
func (m *WeddingSceneNotifyOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingSceneNotifyOpt.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingSceneNotifyOpt proto.InternalMessageInfo

func (m *WeddingSceneNotifyOpt) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *WeddingSceneNotifyOpt) GetServerTimeMs() int64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *WeddingSceneNotifyOpt) GetSceneCfg() *WeddingSceneCfg {
	if m != nil {
		return m.SceneCfg
	}
	return nil
}

func (m *WeddingSceneNotifyOpt) GetCurrLevel() uint32 {
	if m != nil {
		return m.CurrLevel
	}
	return 0
}

func (m *WeddingSceneNotifyOpt) GetExtOpt() string {
	if m != nil {
		return m.ExtOpt
	}
	return ""
}

func (m *WeddingSceneNotifyOpt) GetExtOptBytes() []byte {
	if m != nil {
		return m.ExtOptBytes
	}
	return nil
}

func (m *WeddingSceneNotifyOpt) GetReservePresent() *WeddingReservePresent {
	if m != nil {
		return m.ReservePresent
	}
	return nil
}

// 合影留恋场景附加消息
type WeddingSceneGroupPhotoOpt struct {
	PhotoUrl             string   `protobuf:"bytes,1,opt,name=photo_url,json=photoUrl,proto3" json:"photo_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingSceneGroupPhotoOpt) Reset()         { *m = WeddingSceneGroupPhotoOpt{} }
func (m *WeddingSceneGroupPhotoOpt) String() string { return proto.CompactTextString(m) }
func (*WeddingSceneGroupPhotoOpt) ProtoMessage()    {}
func (*WeddingSceneGroupPhotoOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{22}
}
func (m *WeddingSceneGroupPhotoOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingSceneGroupPhotoOpt.Unmarshal(m, b)
}
func (m *WeddingSceneGroupPhotoOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingSceneGroupPhotoOpt.Marshal(b, m, deterministic)
}
func (dst *WeddingSceneGroupPhotoOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingSceneGroupPhotoOpt.Merge(dst, src)
}
func (m *WeddingSceneGroupPhotoOpt) XXX_Size() int {
	return xxx_messageInfo_WeddingSceneGroupPhotoOpt.Size(m)
}
func (m *WeddingSceneGroupPhotoOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingSceneGroupPhotoOpt.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingSceneGroupPhotoOpt proto.InternalMessageInfo

func (m *WeddingSceneGroupPhotoOpt) GetPhotoUrl() string {
	if m != nil {
		return m.PhotoUrl
	}
	return ""
}

// 高光时刻抢捧花附加消息
type WeddingSceneHighLightOpt struct {
	PresentId            uint32   `protobuf:"varint,1,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	DayCount             uint32   `protobuf:"varint,2,opt,name=day_count,json=dayCount,proto3" json:"day_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingSceneHighLightOpt) Reset()         { *m = WeddingSceneHighLightOpt{} }
func (m *WeddingSceneHighLightOpt) String() string { return proto.CompactTextString(m) }
func (*WeddingSceneHighLightOpt) ProtoMessage()    {}
func (*WeddingSceneHighLightOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{23}
}
func (m *WeddingSceneHighLightOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingSceneHighLightOpt.Unmarshal(m, b)
}
func (m *WeddingSceneHighLightOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingSceneHighLightOpt.Marshal(b, m, deterministic)
}
func (dst *WeddingSceneHighLightOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingSceneHighLightOpt.Merge(dst, src)
}
func (m *WeddingSceneHighLightOpt) XXX_Size() int {
	return xxx_messageInfo_WeddingSceneHighLightOpt.Size(m)
}
func (m *WeddingSceneHighLightOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingSceneHighLightOpt.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingSceneHighLightOpt proto.InternalMessageInfo

func (m *WeddingSceneHighLightOpt) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *WeddingSceneHighLightOpt) GetDayCount() uint32 {
	if m != nil {
		return m.DayCount
	}
	return 0
}

// 付费预约礼物
type WeddingReservePresent struct {
	PresentId            uint32   `protobuf:"varint,1,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	BuyerUid             uint32   `protobuf:"varint,2,opt,name=buyer_uid,json=buyerUid,proto3" json:"buyer_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingReservePresent) Reset()         { *m = WeddingReservePresent{} }
func (m *WeddingReservePresent) String() string { return proto.CompactTextString(m) }
func (*WeddingReservePresent) ProtoMessage()    {}
func (*WeddingReservePresent) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{24}
}
func (m *WeddingReservePresent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingReservePresent.Unmarshal(m, b)
}
func (m *WeddingReservePresent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingReservePresent.Marshal(b, m, deterministic)
}
func (dst *WeddingReservePresent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingReservePresent.Merge(dst, src)
}
func (m *WeddingReservePresent) XXX_Size() int {
	return xxx_messageInfo_WeddingReservePresent.Size(m)
}
func (m *WeddingReservePresent) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingReservePresent.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingReservePresent proto.InternalMessageInfo

func (m *WeddingReservePresent) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *WeddingReservePresent) GetBuyerUid() uint32 {
	if m != nil {
		return m.BuyerUid
	}
	return 0
}

// 婚礼幸福值变化通知信息
type WeddingHappinessChangeOpt struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	ServerTimeMs         int64    `protobuf:"varint,2,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	CurrHappinessValue   uint32   `protobuf:"varint,3,opt,name=curr_happiness_value,json=currHappinessValue,proto3" json:"curr_happiness_value,omitempty"`
	NextLevelTips        uint32   `protobuf:"varint,4,opt,name=next_level_tips,json=nextLevelTips,proto3" json:"next_level_tips,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingHappinessChangeOpt) Reset()         { *m = WeddingHappinessChangeOpt{} }
func (m *WeddingHappinessChangeOpt) String() string { return proto.CompactTextString(m) }
func (*WeddingHappinessChangeOpt) ProtoMessage()    {}
func (*WeddingHappinessChangeOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{25}
}
func (m *WeddingHappinessChangeOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingHappinessChangeOpt.Unmarshal(m, b)
}
func (m *WeddingHappinessChangeOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingHappinessChangeOpt.Marshal(b, m, deterministic)
}
func (dst *WeddingHappinessChangeOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingHappinessChangeOpt.Merge(dst, src)
}
func (m *WeddingHappinessChangeOpt) XXX_Size() int {
	return xxx_messageInfo_WeddingHappinessChangeOpt.Size(m)
}
func (m *WeddingHappinessChangeOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingHappinessChangeOpt.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingHappinessChangeOpt proto.InternalMessageInfo

func (m *WeddingHappinessChangeOpt) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *WeddingHappinessChangeOpt) GetServerTimeMs() int64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *WeddingHappinessChangeOpt) GetCurrHappinessValue() uint32 {
	if m != nil {
		return m.CurrHappinessValue
	}
	return 0
}

func (m *WeddingHappinessChangeOpt) GetNextLevelTips() uint32 {
	if m != nil {
		return m.NextLevelTips
	}
	return 0
}

// 获取房间婚礼信息请求
type GetChannelWeddingInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelWeddingInfoRequest) Reset()         { *m = GetChannelWeddingInfoRequest{} }
func (m *GetChannelWeddingInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetChannelWeddingInfoRequest) ProtoMessage()    {}
func (*GetChannelWeddingInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{26}
}
func (m *GetChannelWeddingInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelWeddingInfoRequest.Unmarshal(m, b)
}
func (m *GetChannelWeddingInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelWeddingInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetChannelWeddingInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelWeddingInfoRequest.Merge(dst, src)
}
func (m *GetChannelWeddingInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetChannelWeddingInfoRequest.Size(m)
}
func (m *GetChannelWeddingInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelWeddingInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelWeddingInfoRequest proto.InternalMessageInfo

func (m *GetChannelWeddingInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelWeddingInfoRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

// 获取房间婚礼信息响应
type GetChannelWeddingInfoResponse struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	WeddingInfo          *WeddingInfo             `protobuf:"bytes,2,opt,name=wedding_info,json=weddingInfo,proto3" json:"wedding_info,omitempty"`
	WelcomeTextPrefix    string                   `protobuf:"bytes,3,opt,name=welcome_text_prefix,json=welcomeTextPrefix,proto3" json:"welcome_text_prefix,omitempty"`
	WelcomeTextSuffix    string                   `protobuf:"bytes,4,opt,name=welcome_text_suffix,json=welcomeTextSuffix,proto3" json:"welcome_text_suffix,omitempty"`
	PresentCountInfo     *WeddingPresentCountInfo `protobuf:"bytes,5,opt,name=present_count_info,json=presentCountInfo,proto3" json:"present_count_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetChannelWeddingInfoResponse) Reset()         { *m = GetChannelWeddingInfoResponse{} }
func (m *GetChannelWeddingInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetChannelWeddingInfoResponse) ProtoMessage()    {}
func (*GetChannelWeddingInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{27}
}
func (m *GetChannelWeddingInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelWeddingInfoResponse.Unmarshal(m, b)
}
func (m *GetChannelWeddingInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelWeddingInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetChannelWeddingInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelWeddingInfoResponse.Merge(dst, src)
}
func (m *GetChannelWeddingInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetChannelWeddingInfoResponse.Size(m)
}
func (m *GetChannelWeddingInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelWeddingInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelWeddingInfoResponse proto.InternalMessageInfo

func (m *GetChannelWeddingInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelWeddingInfoResponse) GetWeddingInfo() *WeddingInfo {
	if m != nil {
		return m.WeddingInfo
	}
	return nil
}

func (m *GetChannelWeddingInfoResponse) GetWelcomeTextPrefix() string {
	if m != nil {
		return m.WelcomeTextPrefix
	}
	return ""
}

func (m *GetChannelWeddingInfoResponse) GetWelcomeTextSuffix() string {
	if m != nil {
		return m.WelcomeTextSuffix
	}
	return ""
}

func (m *GetChannelWeddingInfoResponse) GetPresentCountInfo() *WeddingPresentCountInfo {
	if m != nil {
		return m.PresentCountInfo
	}
	return nil
}

// 切换婚礼阶段请求
type SwitchWeddingStageRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	Stage                uint32       `protobuf:"varint,3,opt,name=stage,proto3" json:"stage,omitempty"`
	SubStage             uint32       `protobuf:"varint,4,opt,name=sub_stage,json=subStage,proto3" json:"sub_stage,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SwitchWeddingStageRequest) Reset()         { *m = SwitchWeddingStageRequest{} }
func (m *SwitchWeddingStageRequest) String() string { return proto.CompactTextString(m) }
func (*SwitchWeddingStageRequest) ProtoMessage()    {}
func (*SwitchWeddingStageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{28}
}
func (m *SwitchWeddingStageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchWeddingStageRequest.Unmarshal(m, b)
}
func (m *SwitchWeddingStageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchWeddingStageRequest.Marshal(b, m, deterministic)
}
func (dst *SwitchWeddingStageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchWeddingStageRequest.Merge(dst, src)
}
func (m *SwitchWeddingStageRequest) XXX_Size() int {
	return xxx_messageInfo_SwitchWeddingStageRequest.Size(m)
}
func (m *SwitchWeddingStageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchWeddingStageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchWeddingStageRequest proto.InternalMessageInfo

func (m *SwitchWeddingStageRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SwitchWeddingStageRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SwitchWeddingStageRequest) GetStage() uint32 {
	if m != nil {
		return m.Stage
	}
	return 0
}

func (m *SwitchWeddingStageRequest) GetSubStage() uint32 {
	if m != nil {
		return m.SubStage
	}
	return 0
}

// 切换婚礼阶段响应
type SwitchWeddingStageResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SwitchWeddingStageResponse) Reset()         { *m = SwitchWeddingStageResponse{} }
func (m *SwitchWeddingStageResponse) String() string { return proto.CompactTextString(m) }
func (*SwitchWeddingStageResponse) ProtoMessage()    {}
func (*SwitchWeddingStageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{29}
}
func (m *SwitchWeddingStageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchWeddingStageResponse.Unmarshal(m, b)
}
func (m *SwitchWeddingStageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchWeddingStageResponse.Marshal(b, m, deterministic)
}
func (dst *SwitchWeddingStageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchWeddingStageResponse.Merge(dst, src)
}
func (m *SwitchWeddingStageResponse) XXX_Size() int {
	return xxx_messageInfo_SwitchWeddingStageResponse.Size(m)
}
func (m *SwitchWeddingStageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchWeddingStageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchWeddingStageResponse proto.InternalMessageInfo

func (m *SwitchWeddingStageResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 拍合照
type TakeWeddingGroupPhotoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	PhotoRul             string       `protobuf:"bytes,3,opt,name=photo_rul,json=photoRul,proto3" json:"photo_rul,omitempty"`
	Signature            string       `protobuf:"bytes,4,opt,name=signature,proto3" json:"signature,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *TakeWeddingGroupPhotoRequest) Reset()         { *m = TakeWeddingGroupPhotoRequest{} }
func (m *TakeWeddingGroupPhotoRequest) String() string { return proto.CompactTextString(m) }
func (*TakeWeddingGroupPhotoRequest) ProtoMessage()    {}
func (*TakeWeddingGroupPhotoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{30}
}
func (m *TakeWeddingGroupPhotoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TakeWeddingGroupPhotoRequest.Unmarshal(m, b)
}
func (m *TakeWeddingGroupPhotoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TakeWeddingGroupPhotoRequest.Marshal(b, m, deterministic)
}
func (dst *TakeWeddingGroupPhotoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TakeWeddingGroupPhotoRequest.Merge(dst, src)
}
func (m *TakeWeddingGroupPhotoRequest) XXX_Size() int {
	return xxx_messageInfo_TakeWeddingGroupPhotoRequest.Size(m)
}
func (m *TakeWeddingGroupPhotoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TakeWeddingGroupPhotoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TakeWeddingGroupPhotoRequest proto.InternalMessageInfo

func (m *TakeWeddingGroupPhotoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *TakeWeddingGroupPhotoRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *TakeWeddingGroupPhotoRequest) GetPhotoRul() string {
	if m != nil {
		return m.PhotoRul
	}
	return ""
}

func (m *TakeWeddingGroupPhotoRequest) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

type TakeWeddingGroupPhotoResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *TakeWeddingGroupPhotoResponse) Reset()         { *m = TakeWeddingGroupPhotoResponse{} }
func (m *TakeWeddingGroupPhotoResponse) String() string { return proto.CompactTextString(m) }
func (*TakeWeddingGroupPhotoResponse) ProtoMessage()    {}
func (*TakeWeddingGroupPhotoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{31}
}
func (m *TakeWeddingGroupPhotoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TakeWeddingGroupPhotoResponse.Unmarshal(m, b)
}
func (m *TakeWeddingGroupPhotoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TakeWeddingGroupPhotoResponse.Marshal(b, m, deterministic)
}
func (dst *TakeWeddingGroupPhotoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TakeWeddingGroupPhotoResponse.Merge(dst, src)
}
func (m *TakeWeddingGroupPhotoResponse) XXX_Size() int {
	return xxx_messageInfo_TakeWeddingGroupPhotoResponse.Size(m)
}
func (m *TakeWeddingGroupPhotoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TakeWeddingGroupPhotoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TakeWeddingGroupPhotoResponse proto.InternalMessageInfo

func (m *TakeWeddingGroupPhotoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取用户婚礼站姿请求
type GetUserWeddingPoseRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserWeddingPoseRequest) Reset()         { *m = GetUserWeddingPoseRequest{} }
func (m *GetUserWeddingPoseRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserWeddingPoseRequest) ProtoMessage()    {}
func (*GetUserWeddingPoseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{32}
}
func (m *GetUserWeddingPoseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWeddingPoseRequest.Unmarshal(m, b)
}
func (m *GetUserWeddingPoseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWeddingPoseRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserWeddingPoseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWeddingPoseRequest.Merge(dst, src)
}
func (m *GetUserWeddingPoseRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserWeddingPoseRequest.Size(m)
}
func (m *GetUserWeddingPoseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWeddingPoseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWeddingPoseRequest proto.InternalMessageInfo

func (m *GetUserWeddingPoseRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserWeddingPoseRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

// 获取用户婚礼站姿响应
type GetUserWeddingPoseResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PoseIdList           []uint32      `protobuf:"varint,2,rep,packed,name=pose_id_list,json=poseIdList,proto3" json:"pose_id_list,omitempty"`
	CurrPoseId           uint32        `protobuf:"varint,3,opt,name=curr_pose_id,json=currPoseId,proto3" json:"curr_pose_id,omitempty"`
	Orientation          uint32        `protobuf:"varint,4,opt,name=orientation,proto3" json:"orientation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserWeddingPoseResponse) Reset()         { *m = GetUserWeddingPoseResponse{} }
func (m *GetUserWeddingPoseResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserWeddingPoseResponse) ProtoMessage()    {}
func (*GetUserWeddingPoseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{33}
}
func (m *GetUserWeddingPoseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWeddingPoseResponse.Unmarshal(m, b)
}
func (m *GetUserWeddingPoseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWeddingPoseResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserWeddingPoseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWeddingPoseResponse.Merge(dst, src)
}
func (m *GetUserWeddingPoseResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserWeddingPoseResponse.Size(m)
}
func (m *GetUserWeddingPoseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWeddingPoseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWeddingPoseResponse proto.InternalMessageInfo

func (m *GetUserWeddingPoseResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserWeddingPoseResponse) GetPoseIdList() []uint32 {
	if m != nil {
		return m.PoseIdList
	}
	return nil
}

func (m *GetUserWeddingPoseResponse) GetCurrPoseId() uint32 {
	if m != nil {
		return m.CurrPoseId
	}
	return 0
}

func (m *GetUserWeddingPoseResponse) GetOrientation() uint32 {
	if m != nil {
		return m.Orientation
	}
	return 0
}

// 设置用户婚礼站姿请求
type SetUserInuseWeddingPoseRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	PoseId               uint32       `protobuf:"varint,3,opt,name=pose_id,json=poseId,proto3" json:"pose_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetUserInuseWeddingPoseRequest) Reset()         { *m = SetUserInuseWeddingPoseRequest{} }
func (m *SetUserInuseWeddingPoseRequest) String() string { return proto.CompactTextString(m) }
func (*SetUserInuseWeddingPoseRequest) ProtoMessage()    {}
func (*SetUserInuseWeddingPoseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{34}
}
func (m *SetUserInuseWeddingPoseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserInuseWeddingPoseRequest.Unmarshal(m, b)
}
func (m *SetUserInuseWeddingPoseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserInuseWeddingPoseRequest.Marshal(b, m, deterministic)
}
func (dst *SetUserInuseWeddingPoseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserInuseWeddingPoseRequest.Merge(dst, src)
}
func (m *SetUserInuseWeddingPoseRequest) XXX_Size() int {
	return xxx_messageInfo_SetUserInuseWeddingPoseRequest.Size(m)
}
func (m *SetUserInuseWeddingPoseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserInuseWeddingPoseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserInuseWeddingPoseRequest proto.InternalMessageInfo

func (m *SetUserInuseWeddingPoseRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetUserInuseWeddingPoseRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SetUserInuseWeddingPoseRequest) GetPoseId() uint32 {
	if m != nil {
		return m.PoseId
	}
	return 0
}

// 设置用户婚礼站姿响应
type SetUserInuseWeddingPoseResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetUserInuseWeddingPoseResponse) Reset()         { *m = SetUserInuseWeddingPoseResponse{} }
func (m *SetUserInuseWeddingPoseResponse) String() string { return proto.CompactTextString(m) }
func (*SetUserInuseWeddingPoseResponse) ProtoMessage()    {}
func (*SetUserInuseWeddingPoseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{35}
}
func (m *SetUserInuseWeddingPoseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserInuseWeddingPoseResponse.Unmarshal(m, b)
}
func (m *SetUserInuseWeddingPoseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserInuseWeddingPoseResponse.Marshal(b, m, deterministic)
}
func (dst *SetUserInuseWeddingPoseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserInuseWeddingPoseResponse.Merge(dst, src)
}
func (m *SetUserInuseWeddingPoseResponse) XXX_Size() int {
	return xxx_messageInfo_SetUserInuseWeddingPoseResponse.Size(m)
}
func (m *SetUserInuseWeddingPoseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserInuseWeddingPoseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserInuseWeddingPoseResponse proto.InternalMessageInfo

func (m *SetUserInuseWeddingPoseResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 批量获取用户婚礼正在使用的站姿请求
type BatchGetUserInuseWeddingPoseRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	UidList              []uint32     `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchGetUserInuseWeddingPoseRequest) Reset()         { *m = BatchGetUserInuseWeddingPoseRequest{} }
func (m *BatchGetUserInuseWeddingPoseRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserInuseWeddingPoseRequest) ProtoMessage()    {}
func (*BatchGetUserInuseWeddingPoseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{36}
}
func (m *BatchGetUserInuseWeddingPoseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserInuseWeddingPoseRequest.Unmarshal(m, b)
}
func (m *BatchGetUserInuseWeddingPoseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserInuseWeddingPoseRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserInuseWeddingPoseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserInuseWeddingPoseRequest.Merge(dst, src)
}
func (m *BatchGetUserInuseWeddingPoseRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserInuseWeddingPoseRequest.Size(m)
}
func (m *BatchGetUserInuseWeddingPoseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserInuseWeddingPoseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserInuseWeddingPoseRequest proto.InternalMessageInfo

func (m *BatchGetUserInuseWeddingPoseRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatchGetUserInuseWeddingPoseRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *BatchGetUserInuseWeddingPoseRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// 设置用户朝向
type SetUserWeddingOrientationRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	Orientation          uint32       `protobuf:"varint,3,opt,name=orientation,proto3" json:"orientation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetUserWeddingOrientationRequest) Reset()         { *m = SetUserWeddingOrientationRequest{} }
func (m *SetUserWeddingOrientationRequest) String() string { return proto.CompactTextString(m) }
func (*SetUserWeddingOrientationRequest) ProtoMessage()    {}
func (*SetUserWeddingOrientationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{37}
}
func (m *SetUserWeddingOrientationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserWeddingOrientationRequest.Unmarshal(m, b)
}
func (m *SetUserWeddingOrientationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserWeddingOrientationRequest.Marshal(b, m, deterministic)
}
func (dst *SetUserWeddingOrientationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserWeddingOrientationRequest.Merge(dst, src)
}
func (m *SetUserWeddingOrientationRequest) XXX_Size() int {
	return xxx_messageInfo_SetUserWeddingOrientationRequest.Size(m)
}
func (m *SetUserWeddingOrientationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserWeddingOrientationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserWeddingOrientationRequest proto.InternalMessageInfo

func (m *SetUserWeddingOrientationRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetUserWeddingOrientationRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SetUserWeddingOrientationRequest) GetOrientation() uint32 {
	if m != nil {
		return m.Orientation
	}
	return 0
}

// 设置用户朝向响应
type SetUserWeddingOrientationResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetUserWeddingOrientationResponse) Reset()         { *m = SetUserWeddingOrientationResponse{} }
func (m *SetUserWeddingOrientationResponse) String() string { return proto.CompactTextString(m) }
func (*SetUserWeddingOrientationResponse) ProtoMessage()    {}
func (*SetUserWeddingOrientationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{38}
}
func (m *SetUserWeddingOrientationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserWeddingOrientationResponse.Unmarshal(m, b)
}
func (m *SetUserWeddingOrientationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserWeddingOrientationResponse.Marshal(b, m, deterministic)
}
func (dst *SetUserWeddingOrientationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserWeddingOrientationResponse.Merge(dst, src)
}
func (m *SetUserWeddingOrientationResponse) XXX_Size() int {
	return xxx_messageInfo_SetUserWeddingOrientationResponse.Size(m)
}
func (m *SetUserWeddingOrientationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserWeddingOrientationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserWeddingOrientationResponse proto.InternalMessageInfo

func (m *SetUserWeddingOrientationResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type UserWeddingPose struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PoseId               uint32   `protobuf:"varint,2,opt,name=pose_id,json=poseId,proto3" json:"pose_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserWeddingPose) Reset()         { *m = UserWeddingPose{} }
func (m *UserWeddingPose) String() string { return proto.CompactTextString(m) }
func (*UserWeddingPose) ProtoMessage()    {}
func (*UserWeddingPose) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{39}
}
func (m *UserWeddingPose) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserWeddingPose.Unmarshal(m, b)
}
func (m *UserWeddingPose) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserWeddingPose.Marshal(b, m, deterministic)
}
func (dst *UserWeddingPose) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserWeddingPose.Merge(dst, src)
}
func (m *UserWeddingPose) XXX_Size() int {
	return xxx_messageInfo_UserWeddingPose.Size(m)
}
func (m *UserWeddingPose) XXX_DiscardUnknown() {
	xxx_messageInfo_UserWeddingPose.DiscardUnknown(m)
}

var xxx_messageInfo_UserWeddingPose proto.InternalMessageInfo

func (m *UserWeddingPose) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserWeddingPose) GetPoseId() uint32 {
	if m != nil {
		return m.PoseId
	}
	return 0
}

// 批量获取用户婚礼正在使用的站姿响应
type BatchGetUserInuseWeddingPoseResponse struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UserPose             []*UserWeddingPose `protobuf:"bytes,2,rep,name=user_pose,json=userPose,proto3" json:"user_pose,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchGetUserInuseWeddingPoseResponse) Reset()         { *m = BatchGetUserInuseWeddingPoseResponse{} }
func (m *BatchGetUserInuseWeddingPoseResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserInuseWeddingPoseResponse) ProtoMessage()    {}
func (*BatchGetUserInuseWeddingPoseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{40}
}
func (m *BatchGetUserInuseWeddingPoseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserInuseWeddingPoseResponse.Unmarshal(m, b)
}
func (m *BatchGetUserInuseWeddingPoseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserInuseWeddingPoseResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserInuseWeddingPoseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserInuseWeddingPoseResponse.Merge(dst, src)
}
func (m *BatchGetUserInuseWeddingPoseResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserInuseWeddingPoseResponse.Size(m)
}
func (m *BatchGetUserInuseWeddingPoseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserInuseWeddingPoseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserInuseWeddingPoseResponse proto.InternalMessageInfo

func (m *BatchGetUserInuseWeddingPoseResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BatchGetUserInuseWeddingPoseResponse) GetUserPose() []*UserWeddingPose {
	if m != nil {
		return m.UserPose
	}
	return nil
}

// [废弃]，用户婚礼站姿变更通知
type UserWeddingPoseChangeOpt struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	PoseId               uint32   `protobuf:"varint,3,opt,name=pose_id,json=poseId,proto3" json:"pose_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserWeddingPoseChangeOpt) Reset()         { *m = UserWeddingPoseChangeOpt{} }
func (m *UserWeddingPoseChangeOpt) String() string { return proto.CompactTextString(m) }
func (*UserWeddingPoseChangeOpt) ProtoMessage()    {}
func (*UserWeddingPoseChangeOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{41}
}
func (m *UserWeddingPoseChangeOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserWeddingPoseChangeOpt.Unmarshal(m, b)
}
func (m *UserWeddingPoseChangeOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserWeddingPoseChangeOpt.Marshal(b, m, deterministic)
}
func (dst *UserWeddingPoseChangeOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserWeddingPoseChangeOpt.Merge(dst, src)
}
func (m *UserWeddingPoseChangeOpt) XXX_Size() int {
	return xxx_messageInfo_UserWeddingPoseChangeOpt.Size(m)
}
func (m *UserWeddingPoseChangeOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_UserWeddingPoseChangeOpt.DiscardUnknown(m)
}

var xxx_messageInfo_UserWeddingPoseChangeOpt proto.InternalMessageInfo

func (m *UserWeddingPoseChangeOpt) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *UserWeddingPoseChangeOpt) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserWeddingPoseChangeOpt) GetPoseId() uint32 {
	if m != nil {
		return m.PoseId
	}
	return 0
}

// 获取房间合照麦位位置映射请求
type GetWeddingGroupPhotoSeatMapRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWeddingGroupPhotoSeatMapRequest) Reset()         { *m = GetWeddingGroupPhotoSeatMapRequest{} }
func (m *GetWeddingGroupPhotoSeatMapRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingGroupPhotoSeatMapRequest) ProtoMessage()    {}
func (*GetWeddingGroupPhotoSeatMapRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{42}
}
func (m *GetWeddingGroupPhotoSeatMapRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingGroupPhotoSeatMapRequest.Unmarshal(m, b)
}
func (m *GetWeddingGroupPhotoSeatMapRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingGroupPhotoSeatMapRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingGroupPhotoSeatMapRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingGroupPhotoSeatMapRequest.Merge(dst, src)
}
func (m *GetWeddingGroupPhotoSeatMapRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingGroupPhotoSeatMapRequest.Size(m)
}
func (m *GetWeddingGroupPhotoSeatMapRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingGroupPhotoSeatMapRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingGroupPhotoSeatMapRequest proto.InternalMessageInfo

func (m *GetWeddingGroupPhotoSeatMapRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetWeddingGroupPhotoSeatMapRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

// 合照麦位位置映射
type WeddingGroupPhotoSeat struct {
	MicId                uint32   `protobuf:"varint,1,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingGroupPhotoSeat) Reset()         { *m = WeddingGroupPhotoSeat{} }
func (m *WeddingGroupPhotoSeat) String() string { return proto.CompactTextString(m) }
func (*WeddingGroupPhotoSeat) ProtoMessage()    {}
func (*WeddingGroupPhotoSeat) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{43}
}
func (m *WeddingGroupPhotoSeat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingGroupPhotoSeat.Unmarshal(m, b)
}
func (m *WeddingGroupPhotoSeat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingGroupPhotoSeat.Marshal(b, m, deterministic)
}
func (dst *WeddingGroupPhotoSeat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingGroupPhotoSeat.Merge(dst, src)
}
func (m *WeddingGroupPhotoSeat) XXX_Size() int {
	return xxx_messageInfo_WeddingGroupPhotoSeat.Size(m)
}
func (m *WeddingGroupPhotoSeat) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingGroupPhotoSeat.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingGroupPhotoSeat proto.InternalMessageInfo

func (m *WeddingGroupPhotoSeat) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

// 获取房间合照麦位位置映射响应
type GetWeddingGroupPhotoSeatMapResponse struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SeatList             []*WeddingGroupPhotoSeat `protobuf:"bytes,2,rep,name=seat_list,json=seatList,proto3" json:"seat_list,omitempty"`
	PoseConfirmedUidList []uint32                 `protobuf:"varint,3,rep,packed,name=pose_confirmed_uid_list,json=poseConfirmedUidList,proto3" json:"pose_confirmed_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetWeddingGroupPhotoSeatMapResponse) Reset()         { *m = GetWeddingGroupPhotoSeatMapResponse{} }
func (m *GetWeddingGroupPhotoSeatMapResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingGroupPhotoSeatMapResponse) ProtoMessage()    {}
func (*GetWeddingGroupPhotoSeatMapResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{44}
}
func (m *GetWeddingGroupPhotoSeatMapResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingGroupPhotoSeatMapResponse.Unmarshal(m, b)
}
func (m *GetWeddingGroupPhotoSeatMapResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingGroupPhotoSeatMapResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingGroupPhotoSeatMapResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingGroupPhotoSeatMapResponse.Merge(dst, src)
}
func (m *GetWeddingGroupPhotoSeatMapResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingGroupPhotoSeatMapResponse.Size(m)
}
func (m *GetWeddingGroupPhotoSeatMapResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingGroupPhotoSeatMapResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingGroupPhotoSeatMapResponse proto.InternalMessageInfo

func (m *GetWeddingGroupPhotoSeatMapResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWeddingGroupPhotoSeatMapResponse) GetSeatList() []*WeddingGroupPhotoSeat {
	if m != nil {
		return m.SeatList
	}
	return nil
}

func (m *GetWeddingGroupPhotoSeatMapResponse) GetPoseConfirmedUidList() []uint32 {
	if m != nil {
		return m.PoseConfirmedUidList
	}
	return nil
}

// 设置用户合照位置请求
type SetUserWeddingGroupPhotoSeatRequest struct {
	BaseReq              *app.BaseReq             `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32                   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	SeatList             []*WeddingGroupPhotoSeat `protobuf:"bytes,3,rep,name=seat_list,json=seatList,proto3" json:"seat_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *SetUserWeddingGroupPhotoSeatRequest) Reset()         { *m = SetUserWeddingGroupPhotoSeatRequest{} }
func (m *SetUserWeddingGroupPhotoSeatRequest) String() string { return proto.CompactTextString(m) }
func (*SetUserWeddingGroupPhotoSeatRequest) ProtoMessage()    {}
func (*SetUserWeddingGroupPhotoSeatRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{45}
}
func (m *SetUserWeddingGroupPhotoSeatRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserWeddingGroupPhotoSeatRequest.Unmarshal(m, b)
}
func (m *SetUserWeddingGroupPhotoSeatRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserWeddingGroupPhotoSeatRequest.Marshal(b, m, deterministic)
}
func (dst *SetUserWeddingGroupPhotoSeatRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserWeddingGroupPhotoSeatRequest.Merge(dst, src)
}
func (m *SetUserWeddingGroupPhotoSeatRequest) XXX_Size() int {
	return xxx_messageInfo_SetUserWeddingGroupPhotoSeatRequest.Size(m)
}
func (m *SetUserWeddingGroupPhotoSeatRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserWeddingGroupPhotoSeatRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserWeddingGroupPhotoSeatRequest proto.InternalMessageInfo

func (m *SetUserWeddingGroupPhotoSeatRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetUserWeddingGroupPhotoSeatRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SetUserWeddingGroupPhotoSeatRequest) GetSeatList() []*WeddingGroupPhotoSeat {
	if m != nil {
		return m.SeatList
	}
	return nil
}

// 设置用户合照位置响应
type SetUserWeddingGroupPhotoSeatResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetUserWeddingGroupPhotoSeatResponse) Reset()         { *m = SetUserWeddingGroupPhotoSeatResponse{} }
func (m *SetUserWeddingGroupPhotoSeatResponse) String() string { return proto.CompactTextString(m) }
func (*SetUserWeddingGroupPhotoSeatResponse) ProtoMessage()    {}
func (*SetUserWeddingGroupPhotoSeatResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{46}
}
func (m *SetUserWeddingGroupPhotoSeatResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserWeddingGroupPhotoSeatResponse.Unmarshal(m, b)
}
func (m *SetUserWeddingGroupPhotoSeatResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserWeddingGroupPhotoSeatResponse.Marshal(b, m, deterministic)
}
func (dst *SetUserWeddingGroupPhotoSeatResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserWeddingGroupPhotoSeatResponse.Merge(dst, src)
}
func (m *SetUserWeddingGroupPhotoSeatResponse) XXX_Size() int {
	return xxx_messageInfo_SetUserWeddingGroupPhotoSeatResponse.Size(m)
}
func (m *SetUserWeddingGroupPhotoSeatResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserWeddingGroupPhotoSeatResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserWeddingGroupPhotoSeatResponse proto.InternalMessageInfo

func (m *SetUserWeddingGroupPhotoSeatResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 用户合照变化通知
type WeddingGroupPhotoSeatChangeOpt struct {
	Cid                  uint32                   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	SeatList             []*WeddingGroupPhotoSeat `protobuf:"bytes,2,rep,name=seat_list,json=seatList,proto3" json:"seat_list,omitempty"`
	PoseConfirmedUidList []uint32                 `protobuf:"varint,3,rep,packed,name=pose_confirmed_uid_list,json=poseConfirmedUidList,proto3" json:"pose_confirmed_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *WeddingGroupPhotoSeatChangeOpt) Reset()         { *m = WeddingGroupPhotoSeatChangeOpt{} }
func (m *WeddingGroupPhotoSeatChangeOpt) String() string { return proto.CompactTextString(m) }
func (*WeddingGroupPhotoSeatChangeOpt) ProtoMessage()    {}
func (*WeddingGroupPhotoSeatChangeOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{47}
}
func (m *WeddingGroupPhotoSeatChangeOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingGroupPhotoSeatChangeOpt.Unmarshal(m, b)
}
func (m *WeddingGroupPhotoSeatChangeOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingGroupPhotoSeatChangeOpt.Marshal(b, m, deterministic)
}
func (dst *WeddingGroupPhotoSeatChangeOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingGroupPhotoSeatChangeOpt.Merge(dst, src)
}
func (m *WeddingGroupPhotoSeatChangeOpt) XXX_Size() int {
	return xxx_messageInfo_WeddingGroupPhotoSeatChangeOpt.Size(m)
}
func (m *WeddingGroupPhotoSeatChangeOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingGroupPhotoSeatChangeOpt.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingGroupPhotoSeatChangeOpt proto.InternalMessageInfo

func (m *WeddingGroupPhotoSeatChangeOpt) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *WeddingGroupPhotoSeatChangeOpt) GetSeatList() []*WeddingGroupPhotoSeat {
	if m != nil {
		return m.SeatList
	}
	return nil
}

func (m *WeddingGroupPhotoSeatChangeOpt) GetPoseConfirmedUidList() []uint32 {
	if m != nil {
		return m.PoseConfirmedUidList
	}
	return nil
}

// 新人/嘉宾进房通知
type WeddingGuestEnterRoomOpt struct {
	Cid                  uint32           `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	User                 *app.UserProfile `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	ServerTimeMs         int64            `protobuf:"varint,3,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	PublicTextPrefix     string           `protobuf:"bytes,4,opt,name=public_text_prefix,json=publicTextPrefix,proto3" json:"public_text_prefix,omitempty"`
	PublicTextSuffix     string           `protobuf:"bytes,5,opt,name=public_text_suffix,json=publicTextSuffix,proto3" json:"public_text_suffix,omitempty"`
	GuestType            uint32           `protobuf:"varint,6,opt,name=guest_type,json=guestType,proto3" json:"guest_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WeddingGuestEnterRoomOpt) Reset()         { *m = WeddingGuestEnterRoomOpt{} }
func (m *WeddingGuestEnterRoomOpt) String() string { return proto.CompactTextString(m) }
func (*WeddingGuestEnterRoomOpt) ProtoMessage()    {}
func (*WeddingGuestEnterRoomOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{48}
}
func (m *WeddingGuestEnterRoomOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingGuestEnterRoomOpt.Unmarshal(m, b)
}
func (m *WeddingGuestEnterRoomOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingGuestEnterRoomOpt.Marshal(b, m, deterministic)
}
func (dst *WeddingGuestEnterRoomOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingGuestEnterRoomOpt.Merge(dst, src)
}
func (m *WeddingGuestEnterRoomOpt) XXX_Size() int {
	return xxx_messageInfo_WeddingGuestEnterRoomOpt.Size(m)
}
func (m *WeddingGuestEnterRoomOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingGuestEnterRoomOpt.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingGuestEnterRoomOpt proto.InternalMessageInfo

func (m *WeddingGuestEnterRoomOpt) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *WeddingGuestEnterRoomOpt) GetUser() *app.UserProfile {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *WeddingGuestEnterRoomOpt) GetServerTimeMs() int64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *WeddingGuestEnterRoomOpt) GetPublicTextPrefix() string {
	if m != nil {
		return m.PublicTextPrefix
	}
	return ""
}

func (m *WeddingGuestEnterRoomOpt) GetPublicTextSuffix() string {
	if m != nil {
		return m.PublicTextSuffix
	}
	return ""
}

func (m *WeddingGuestEnterRoomOpt) GetGuestType() uint32 {
	if m != nil {
		return m.GuestType
	}
	return 0
}

type WeddingRankEntry struct {
	ShowEntry            bool     `protobuf:"varint,1,opt,name=show_entry,json=showEntry,proto3" json:"show_entry,omitempty"`
	BrideAccount         string   `protobuf:"bytes,2,opt,name=bride_account,json=brideAccount,proto3" json:"bride_account,omitempty"`
	GroomAccount         string   `protobuf:"bytes,3,opt,name=groom_account,json=groomAccount,proto3" json:"groom_account,omitempty"`
	H5Url                string   `protobuf:"bytes,4,opt,name=h5_url,json=h5Url,proto3" json:"h5_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingRankEntry) Reset()         { *m = WeddingRankEntry{} }
func (m *WeddingRankEntry) String() string { return proto.CompactTextString(m) }
func (*WeddingRankEntry) ProtoMessage()    {}
func (*WeddingRankEntry) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{49}
}
func (m *WeddingRankEntry) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingRankEntry.Unmarshal(m, b)
}
func (m *WeddingRankEntry) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingRankEntry.Marshal(b, m, deterministic)
}
func (dst *WeddingRankEntry) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingRankEntry.Merge(dst, src)
}
func (m *WeddingRankEntry) XXX_Size() int {
	return xxx_messageInfo_WeddingRankEntry.Size(m)
}
func (m *WeddingRankEntry) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingRankEntry.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingRankEntry proto.InternalMessageInfo

func (m *WeddingRankEntry) GetShowEntry() bool {
	if m != nil {
		return m.ShowEntry
	}
	return false
}

func (m *WeddingRankEntry) GetBrideAccount() string {
	if m != nil {
		return m.BrideAccount
	}
	return ""
}

func (m *WeddingRankEntry) GetGroomAccount() string {
	if m != nil {
		return m.GroomAccount
	}
	return ""
}

func (m *WeddingRankEntry) GetH5Url() string {
	if m != nil {
		return m.H5Url
	}
	return ""
}

type GetWeddingRankEntryRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWeddingRankEntryRequest) Reset()         { *m = GetWeddingRankEntryRequest{} }
func (m *GetWeddingRankEntryRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingRankEntryRequest) ProtoMessage()    {}
func (*GetWeddingRankEntryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{50}
}
func (m *GetWeddingRankEntryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingRankEntryRequest.Unmarshal(m, b)
}
func (m *GetWeddingRankEntryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingRankEntryRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingRankEntryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingRankEntryRequest.Merge(dst, src)
}
func (m *GetWeddingRankEntryRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingRankEntryRequest.Size(m)
}
func (m *GetWeddingRankEntryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingRankEntryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingRankEntryRequest proto.InternalMessageInfo

func (m *GetWeddingRankEntryRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetWeddingRankEntryRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetWeddingRankEntryResponse struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	WeddingRankEntry     *WeddingRankEntry `protobuf:"bytes,2,opt,name=wedding_rank_entry,json=weddingRankEntry,proto3" json:"wedding_rank_entry,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetWeddingRankEntryResponse) Reset()         { *m = GetWeddingRankEntryResponse{} }
func (m *GetWeddingRankEntryResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingRankEntryResponse) ProtoMessage()    {}
func (*GetWeddingRankEntryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{51}
}
func (m *GetWeddingRankEntryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingRankEntryResponse.Unmarshal(m, b)
}
func (m *GetWeddingRankEntryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingRankEntryResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingRankEntryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingRankEntryResponse.Merge(dst, src)
}
func (m *GetWeddingRankEntryResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingRankEntryResponse.Size(m)
}
func (m *GetWeddingRankEntryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingRankEntryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingRankEntryResponse proto.InternalMessageInfo

func (m *GetWeddingRankEntryResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWeddingRankEntryResponse) GetWeddingRankEntry() *WeddingRankEntry {
	if m != nil {
		return m.WeddingRankEntry
	}
	return nil
}

// 获取婚礼主题列表请求
type GetWeddingThemeCfgListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWeddingThemeCfgListRequest) Reset()         { *m = GetWeddingThemeCfgListRequest{} }
func (m *GetWeddingThemeCfgListRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingThemeCfgListRequest) ProtoMessage()    {}
func (*GetWeddingThemeCfgListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{52}
}
func (m *GetWeddingThemeCfgListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingThemeCfgListRequest.Unmarshal(m, b)
}
func (m *GetWeddingThemeCfgListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingThemeCfgListRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingThemeCfgListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingThemeCfgListRequest.Merge(dst, src)
}
func (m *GetWeddingThemeCfgListRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingThemeCfgListRequest.Size(m)
}
func (m *GetWeddingThemeCfgListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingThemeCfgListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingThemeCfgListRequest proto.InternalMessageInfo

func (m *GetWeddingThemeCfgListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetWeddingThemeCfgListResponse struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ThemeCfgList         []*WeddingRoomThemeCfg `protobuf:"bytes,2,rep,name=theme_cfg_list,json=themeCfgList,proto3" json:"theme_cfg_list,omitempty"`
	DownloadDelaySec     uint32                 `protobuf:"varint,3,opt,name=download_delay_sec,json=downloadDelaySec,proto3" json:"download_delay_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetWeddingThemeCfgListResponse) Reset()         { *m = GetWeddingThemeCfgListResponse{} }
func (m *GetWeddingThemeCfgListResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingThemeCfgListResponse) ProtoMessage()    {}
func (*GetWeddingThemeCfgListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{53}
}
func (m *GetWeddingThemeCfgListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingThemeCfgListResponse.Unmarshal(m, b)
}
func (m *GetWeddingThemeCfgListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingThemeCfgListResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingThemeCfgListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingThemeCfgListResponse.Merge(dst, src)
}
func (m *GetWeddingThemeCfgListResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingThemeCfgListResponse.Size(m)
}
func (m *GetWeddingThemeCfgListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingThemeCfgListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingThemeCfgListResponse proto.InternalMessageInfo

func (m *GetWeddingThemeCfgListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWeddingThemeCfgListResponse) GetThemeCfgList() []*WeddingRoomThemeCfg {
	if m != nil {
		return m.ThemeCfgList
	}
	return nil
}

func (m *GetWeddingThemeCfgListResponse) GetDownloadDelaySec() uint32 {
	if m != nil {
		return m.DownloadDelaySec
	}
	return 0
}

// 申请参加
type ApplyToJoinChairGameRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IsCancel             bool         `protobuf:"varint,3,opt,name=is_cancel,json=isCancel,proto3" json:"is_cancel,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ApplyToJoinChairGameRequest) Reset()         { *m = ApplyToJoinChairGameRequest{} }
func (m *ApplyToJoinChairGameRequest) String() string { return proto.CompactTextString(m) }
func (*ApplyToJoinChairGameRequest) ProtoMessage()    {}
func (*ApplyToJoinChairGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{54}
}
func (m *ApplyToJoinChairGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyToJoinChairGameRequest.Unmarshal(m, b)
}
func (m *ApplyToJoinChairGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyToJoinChairGameRequest.Marshal(b, m, deterministic)
}
func (dst *ApplyToJoinChairGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyToJoinChairGameRequest.Merge(dst, src)
}
func (m *ApplyToJoinChairGameRequest) XXX_Size() int {
	return xxx_messageInfo_ApplyToJoinChairGameRequest.Size(m)
}
func (m *ApplyToJoinChairGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyToJoinChairGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyToJoinChairGameRequest proto.InternalMessageInfo

func (m *ApplyToJoinChairGameRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ApplyToJoinChairGameRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ApplyToJoinChairGameRequest) GetIsCancel() bool {
	if m != nil {
		return m.IsCancel
	}
	return false
}

type ApplyToJoinChairGameResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ApplyToJoinChairGameResponse) Reset()         { *m = ApplyToJoinChairGameResponse{} }
func (m *ApplyToJoinChairGameResponse) String() string { return proto.CompactTextString(m) }
func (*ApplyToJoinChairGameResponse) ProtoMessage()    {}
func (*ApplyToJoinChairGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{55}
}
func (m *ApplyToJoinChairGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyToJoinChairGameResponse.Unmarshal(m, b)
}
func (m *ApplyToJoinChairGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyToJoinChairGameResponse.Marshal(b, m, deterministic)
}
func (dst *ApplyToJoinChairGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyToJoinChairGameResponse.Merge(dst, src)
}
func (m *ApplyToJoinChairGameResponse) XXX_Size() int {
	return xxx_messageInfo_ApplyToJoinChairGameResponse.Size(m)
}
func (m *ApplyToJoinChairGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyToJoinChairGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyToJoinChairGameResponse proto.InternalMessageInfo

func (m *ApplyToJoinChairGameResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ChairGameUserInfo struct {
	UserInfo             *app.UserProfile `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	IsInChannel          bool             `protobuf:"varint,2,opt,name=is_in_channel,json=isInChannel,proto3" json:"is_in_channel,omitempty"`
	WeddingGuestType     uint32           `protobuf:"varint,3,opt,name=wedding_guest_type,json=weddingGuestType,proto3" json:"wedding_guest_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ChairGameUserInfo) Reset()         { *m = ChairGameUserInfo{} }
func (m *ChairGameUserInfo) String() string { return proto.CompactTextString(m) }
func (*ChairGameUserInfo) ProtoMessage()    {}
func (*ChairGameUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{56}
}
func (m *ChairGameUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChairGameUserInfo.Unmarshal(m, b)
}
func (m *ChairGameUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChairGameUserInfo.Marshal(b, m, deterministic)
}
func (dst *ChairGameUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChairGameUserInfo.Merge(dst, src)
}
func (m *ChairGameUserInfo) XXX_Size() int {
	return xxx_messageInfo_ChairGameUserInfo.Size(m)
}
func (m *ChairGameUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChairGameUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChairGameUserInfo proto.InternalMessageInfo

func (m *ChairGameUserInfo) GetUserInfo() *app.UserProfile {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *ChairGameUserInfo) GetIsInChannel() bool {
	if m != nil {
		return m.IsInChannel
	}
	return false
}

func (m *ChairGameUserInfo) GetWeddingGuestType() uint32 {
	if m != nil {
		return m.WeddingGuestType
	}
	return 0
}

type ChairGamePublicText struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	InviteText           string   `protobuf:"bytes,3,opt,name=invite_text,json=inviteText,proto3" json:"invite_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChairGamePublicText) Reset()         { *m = ChairGamePublicText{} }
func (m *ChairGamePublicText) String() string { return proto.CompactTextString(m) }
func (*ChairGamePublicText) ProtoMessage()    {}
func (*ChairGamePublicText) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{57}
}
func (m *ChairGamePublicText) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChairGamePublicText.Unmarshal(m, b)
}
func (m *ChairGamePublicText) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChairGamePublicText.Marshal(b, m, deterministic)
}
func (dst *ChairGamePublicText) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChairGamePublicText.Merge(dst, src)
}
func (m *ChairGamePublicText) XXX_Size() int {
	return xxx_messageInfo_ChairGamePublicText.Size(m)
}
func (m *ChairGamePublicText) XXX_DiscardUnknown() {
	xxx_messageInfo_ChairGamePublicText.DiscardUnknown(m)
}

var xxx_messageInfo_ChairGamePublicText proto.InternalMessageInfo

func (m *ChairGamePublicText) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChairGamePublicText) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ChairGamePublicText) GetInviteText() string {
	if m != nil {
		return m.InviteText
	}
	return ""
}

// 申请成功消息
type ChairGameApplyMsgOpt struct {
	PublicText           *ChairGamePublicText `protobuf:"bytes,1,opt,name=public_text,json=publicText,proto3" json:"public_text,omitempty"`
	TotalApplyNum        uint32               `protobuf:"varint,2,opt,name=total_apply_num,json=totalApplyNum,proto3" json:"total_apply_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ChairGameApplyMsgOpt) Reset()         { *m = ChairGameApplyMsgOpt{} }
func (m *ChairGameApplyMsgOpt) String() string { return proto.CompactTextString(m) }
func (*ChairGameApplyMsgOpt) ProtoMessage()    {}
func (*ChairGameApplyMsgOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{58}
}
func (m *ChairGameApplyMsgOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChairGameApplyMsgOpt.Unmarshal(m, b)
}
func (m *ChairGameApplyMsgOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChairGameApplyMsgOpt.Marshal(b, m, deterministic)
}
func (dst *ChairGameApplyMsgOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChairGameApplyMsgOpt.Merge(dst, src)
}
func (m *ChairGameApplyMsgOpt) XXX_Size() int {
	return xxx_messageInfo_ChairGameApplyMsgOpt.Size(m)
}
func (m *ChairGameApplyMsgOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_ChairGameApplyMsgOpt.DiscardUnknown(m)
}

var xxx_messageInfo_ChairGameApplyMsgOpt proto.InternalMessageInfo

func (m *ChairGameApplyMsgOpt) GetPublicText() *ChairGamePublicText {
	if m != nil {
		return m.PublicText
	}
	return nil
}

func (m *ChairGameApplyMsgOpt) GetTotalApplyNum() uint32 {
	if m != nil {
		return m.TotalApplyNum
	}
	return 0
}

type ChairGamePlayerOpt struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChairGamePlayerOpt) Reset()         { *m = ChairGamePlayerOpt{} }
func (m *ChairGamePlayerOpt) String() string { return proto.CompactTextString(m) }
func (*ChairGamePlayerOpt) ProtoMessage()    {}
func (*ChairGamePlayerOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{59}
}
func (m *ChairGamePlayerOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChairGamePlayerOpt.Unmarshal(m, b)
}
func (m *ChairGamePlayerOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChairGamePlayerOpt.Marshal(b, m, deterministic)
}
func (dst *ChairGamePlayerOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChairGamePlayerOpt.Merge(dst, src)
}
func (m *ChairGamePlayerOpt) XXX_Size() int {
	return xxx_messageInfo_ChairGamePlayerOpt.Size(m)
}
func (m *ChairGamePlayerOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_ChairGamePlayerOpt.DiscardUnknown(m)
}

var xxx_messageInfo_ChairGamePlayerOpt proto.InternalMessageInfo

func (m *ChairGamePlayerOpt) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// 获取报名列表
type GetChairGameApplyListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChairGameApplyListRequest) Reset()         { *m = GetChairGameApplyListRequest{} }
func (m *GetChairGameApplyListRequest) String() string { return proto.CompactTextString(m) }
func (*GetChairGameApplyListRequest) ProtoMessage()    {}
func (*GetChairGameApplyListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{60}
}
func (m *GetChairGameApplyListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChairGameApplyListRequest.Unmarshal(m, b)
}
func (m *GetChairGameApplyListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChairGameApplyListRequest.Marshal(b, m, deterministic)
}
func (dst *GetChairGameApplyListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChairGameApplyListRequest.Merge(dst, src)
}
func (m *GetChairGameApplyListRequest) XXX_Size() int {
	return xxx_messageInfo_GetChairGameApplyListRequest.Size(m)
}
func (m *GetChairGameApplyListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChairGameApplyListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChairGameApplyListRequest proto.InternalMessageInfo

func (m *GetChairGameApplyListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChairGameApplyListRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChairGameApplyListResponse struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ApplyUserList        []*ChairGameUserInfo `protobuf:"bytes,2,rep,name=apply_user_list,json=applyUserList,proto3" json:"apply_user_list,omitempty"`
	TotalApplyNum        uint32               `protobuf:"varint,3,opt,name=total_apply_num,json=totalApplyNum,proto3" json:"total_apply_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetChairGameApplyListResponse) Reset()         { *m = GetChairGameApplyListResponse{} }
func (m *GetChairGameApplyListResponse) String() string { return proto.CompactTextString(m) }
func (*GetChairGameApplyListResponse) ProtoMessage()    {}
func (*GetChairGameApplyListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{61}
}
func (m *GetChairGameApplyListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChairGameApplyListResponse.Unmarshal(m, b)
}
func (m *GetChairGameApplyListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChairGameApplyListResponse.Marshal(b, m, deterministic)
}
func (dst *GetChairGameApplyListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChairGameApplyListResponse.Merge(dst, src)
}
func (m *GetChairGameApplyListResponse) XXX_Size() int {
	return xxx_messageInfo_GetChairGameApplyListResponse.Size(m)
}
func (m *GetChairGameApplyListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChairGameApplyListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChairGameApplyListResponse proto.InternalMessageInfo

func (m *GetChairGameApplyListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChairGameApplyListResponse) GetApplyUserList() []*ChairGameUserInfo {
	if m != nil {
		return m.ApplyUserList
	}
	return nil
}

func (m *GetChairGameApplyListResponse) GetTotalApplyNum() uint32 {
	if m != nil {
		return m.TotalApplyNum
	}
	return 0
}

type ChairGameRewardSetting struct {
	GiftType             []uint32 `protobuf:"varint,1,rep,packed,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	SupportMagicGift     bool     `protobuf:"varint,2,opt,name=support_magic_gift,json=supportMagicGift,proto3" json:"support_magic_gift,omitempty"`
	PriceLimit           uint32   `protobuf:"varint,3,opt,name=price_limit,json=priceLimit,proto3" json:"price_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChairGameRewardSetting) Reset()         { *m = ChairGameRewardSetting{} }
func (m *ChairGameRewardSetting) String() string { return proto.CompactTextString(m) }
func (*ChairGameRewardSetting) ProtoMessage()    {}
func (*ChairGameRewardSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{62}
}
func (m *ChairGameRewardSetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChairGameRewardSetting.Unmarshal(m, b)
}
func (m *ChairGameRewardSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChairGameRewardSetting.Marshal(b, m, deterministic)
}
func (dst *ChairGameRewardSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChairGameRewardSetting.Merge(dst, src)
}
func (m *ChairGameRewardSetting) XXX_Size() int {
	return xxx_messageInfo_ChairGameRewardSetting.Size(m)
}
func (m *ChairGameRewardSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_ChairGameRewardSetting.DiscardUnknown(m)
}

var xxx_messageInfo_ChairGameRewardSetting proto.InternalMessageInfo

func (m *ChairGameRewardSetting) GetGiftType() []uint32 {
	if m != nil {
		return m.GiftType
	}
	return nil
}

func (m *ChairGameRewardSetting) GetSupportMagicGift() bool {
	if m != nil {
		return m.SupportMagicGift
	}
	return false
}

func (m *ChairGameRewardSetting) GetPriceLimit() uint32 {
	if m != nil {
		return m.PriceLimit
	}
	return 0
}

// 抢椅子奖励信息
type ChairGameRewardInfo struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Value                uint32   `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`
	RewardUnit           string   `protobuf:"bytes,4,opt,name=reward_unit,json=rewardUnit,proto3" json:"reward_unit,omitempty"`
	Amount               uint32   `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChairGameRewardInfo) Reset()         { *m = ChairGameRewardInfo{} }
func (m *ChairGameRewardInfo) String() string { return proto.CompactTextString(m) }
func (*ChairGameRewardInfo) ProtoMessage()    {}
func (*ChairGameRewardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{63}
}
func (m *ChairGameRewardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChairGameRewardInfo.Unmarshal(m, b)
}
func (m *ChairGameRewardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChairGameRewardInfo.Marshal(b, m, deterministic)
}
func (dst *ChairGameRewardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChairGameRewardInfo.Merge(dst, src)
}
func (m *ChairGameRewardInfo) XXX_Size() int {
	return xxx_messageInfo_ChairGameRewardInfo.Size(m)
}
func (m *ChairGameRewardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChairGameRewardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChairGameRewardInfo proto.InternalMessageInfo

func (m *ChairGameRewardInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ChairGameRewardInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChairGameRewardInfo) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *ChairGameRewardInfo) GetRewardUnit() string {
	if m != nil {
		return m.RewardUnit
	}
	return ""
}

func (m *ChairGameRewardInfo) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

// 新人设置游戏奖励
type SetChairGameRewardRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AwardGiftId          uint32       `protobuf:"varint,3,opt,name=award_gift_id,json=awardGiftId,proto3" json:"award_gift_id,omitempty"`
	GiftType             uint32       `protobuf:"varint,4,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	AwardGiftNum         uint32       `protobuf:"varint,5,opt,name=award_gift_num,json=awardGiftNum,proto3" json:"award_gift_num,omitempty"`
	IsMagicGift          bool         `protobuf:"varint,6,opt,name=is_magic_gift,json=isMagicGift,proto3" json:"is_magic_gift,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetChairGameRewardRequest) Reset()         { *m = SetChairGameRewardRequest{} }
func (m *SetChairGameRewardRequest) String() string { return proto.CompactTextString(m) }
func (*SetChairGameRewardRequest) ProtoMessage()    {}
func (*SetChairGameRewardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{64}
}
func (m *SetChairGameRewardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChairGameRewardRequest.Unmarshal(m, b)
}
func (m *SetChairGameRewardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChairGameRewardRequest.Marshal(b, m, deterministic)
}
func (dst *SetChairGameRewardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChairGameRewardRequest.Merge(dst, src)
}
func (m *SetChairGameRewardRequest) XXX_Size() int {
	return xxx_messageInfo_SetChairGameRewardRequest.Size(m)
}
func (m *SetChairGameRewardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChairGameRewardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetChairGameRewardRequest proto.InternalMessageInfo

func (m *SetChairGameRewardRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetChairGameRewardRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChairGameRewardRequest) GetAwardGiftId() uint32 {
	if m != nil {
		return m.AwardGiftId
	}
	return 0
}

func (m *SetChairGameRewardRequest) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *SetChairGameRewardRequest) GetAwardGiftNum() uint32 {
	if m != nil {
		return m.AwardGiftNum
	}
	return 0
}

func (m *SetChairGameRewardRequest) GetIsMagicGift() bool {
	if m != nil {
		return m.IsMagicGift
	}
	return false
}

type SetChairGameRewardResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetChairGameRewardResponse) Reset()         { *m = SetChairGameRewardResponse{} }
func (m *SetChairGameRewardResponse) String() string { return proto.CompactTextString(m) }
func (*SetChairGameRewardResponse) ProtoMessage()    {}
func (*SetChairGameRewardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{65}
}
func (m *SetChairGameRewardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChairGameRewardResponse.Unmarshal(m, b)
}
func (m *SetChairGameRewardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChairGameRewardResponse.Marshal(b, m, deterministic)
}
func (dst *SetChairGameRewardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChairGameRewardResponse.Merge(dst, src)
}
func (m *SetChairGameRewardResponse) XXX_Size() int {
	return xxx_messageInfo_SetChairGameRewardResponse.Size(m)
}
func (m *SetChairGameRewardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChairGameRewardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetChairGameRewardResponse proto.InternalMessageInfo

func (m *SetChairGameRewardResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取抢椅子游戏奖励配置信息
type GetChairGameRewardSettingRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChairGameRewardSettingRequest) Reset()         { *m = GetChairGameRewardSettingRequest{} }
func (m *GetChairGameRewardSettingRequest) String() string { return proto.CompactTextString(m) }
func (*GetChairGameRewardSettingRequest) ProtoMessage()    {}
func (*GetChairGameRewardSettingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{66}
}
func (m *GetChairGameRewardSettingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChairGameRewardSettingRequest.Unmarshal(m, b)
}
func (m *GetChairGameRewardSettingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChairGameRewardSettingRequest.Marshal(b, m, deterministic)
}
func (dst *GetChairGameRewardSettingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChairGameRewardSettingRequest.Merge(dst, src)
}
func (m *GetChairGameRewardSettingRequest) XXX_Size() int {
	return xxx_messageInfo_GetChairGameRewardSettingRequest.Size(m)
}
func (m *GetChairGameRewardSettingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChairGameRewardSettingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChairGameRewardSettingRequest proto.InternalMessageInfo

func (m *GetChairGameRewardSettingRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChairGameRewardSettingRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChairGameRewardSettingResponse struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RewardConf           *ChairGameRewardSetting `protobuf:"bytes,2,opt,name=reward_conf,json=rewardConf,proto3" json:"reward_conf,omitempty"`
	RewardList           []*ChairGameRewardInfo  `protobuf:"bytes,3,rep,name=reward_list,json=rewardList,proto3" json:"reward_list,omitempty"`
	SponsorUid           uint32                  `protobuf:"varint,4,opt,name=sponsor_uid,json=sponsorUid,proto3" json:"sponsor_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetChairGameRewardSettingResponse) Reset()         { *m = GetChairGameRewardSettingResponse{} }
func (m *GetChairGameRewardSettingResponse) String() string { return proto.CompactTextString(m) }
func (*GetChairGameRewardSettingResponse) ProtoMessage()    {}
func (*GetChairGameRewardSettingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{67}
}
func (m *GetChairGameRewardSettingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChairGameRewardSettingResponse.Unmarshal(m, b)
}
func (m *GetChairGameRewardSettingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChairGameRewardSettingResponse.Marshal(b, m, deterministic)
}
func (dst *GetChairGameRewardSettingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChairGameRewardSettingResponse.Merge(dst, src)
}
func (m *GetChairGameRewardSettingResponse) XXX_Size() int {
	return xxx_messageInfo_GetChairGameRewardSettingResponse.Size(m)
}
func (m *GetChairGameRewardSettingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChairGameRewardSettingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChairGameRewardSettingResponse proto.InternalMessageInfo

func (m *GetChairGameRewardSettingResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChairGameRewardSettingResponse) GetRewardConf() *ChairGameRewardSetting {
	if m != nil {
		return m.RewardConf
	}
	return nil
}

func (m *GetChairGameRewardSettingResponse) GetRewardList() []*ChairGameRewardInfo {
	if m != nil {
		return m.RewardList
	}
	return nil
}

func (m *GetChairGameRewardSettingResponse) GetSponsorUid() uint32 {
	if m != nil {
		return m.SponsorUid
	}
	return 0
}

// 游戏进程
type ChairGameProgress struct {
	GameId       uint32 `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CurRound     uint32 `protobuf:"varint,2,opt,name=cur_round,json=curRound,proto3" json:"cur_round,omitempty"`
	ChairNum     uint32 `protobuf:"varint,3,opt,name=chair_num,json=chairNum,proto3" json:"chair_num,omitempty"`
	RoundStatus  uint32 `protobuf:"varint,4,opt,name=round_status,json=roundStatus,proto3" json:"round_status,omitempty"`
	ShowRoundTip bool   `protobuf:"varint,5,opt,name=show_round_tip,json=showRoundTip,proto3" json:"show_round_tip,omitempty"`
	// 本轮参与用户
	RoundPalyerUids      []uint32 `protobuf:"varint,6,rep,packed,name=round_palyer_uids,json=roundPalyerUids,proto3" json:"round_palyer_uids,omitempty"`
	RoundWinnerUids      []uint32 `protobuf:"varint,7,rep,packed,name=round_winner_uids,json=roundWinnerUids,proto3" json:"round_winner_uids,omitempty"`
	NextRoundChairNum    uint32   `protobuf:"varint,8,opt,name=next_round_chair_num,json=nextRoundChairNum,proto3" json:"next_round_chair_num,omitempty"`
	ServerTimeMs         int64    `protobuf:"varint,9,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	HostStartButDuration int64    `protobuf:"varint,10,opt,name=host_start_but_duration,json=hostStartButDuration,proto3" json:"host_start_but_duration,omitempty"`
	HostButtonEndTs      int64    `protobuf:"varint,11,opt,name=host_button_end_ts,json=hostButtonEndTs,proto3" json:"host_button_end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChairGameProgress) Reset()         { *m = ChairGameProgress{} }
func (m *ChairGameProgress) String() string { return proto.CompactTextString(m) }
func (*ChairGameProgress) ProtoMessage()    {}
func (*ChairGameProgress) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{68}
}
func (m *ChairGameProgress) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChairGameProgress.Unmarshal(m, b)
}
func (m *ChairGameProgress) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChairGameProgress.Marshal(b, m, deterministic)
}
func (dst *ChairGameProgress) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChairGameProgress.Merge(dst, src)
}
func (m *ChairGameProgress) XXX_Size() int {
	return xxx_messageInfo_ChairGameProgress.Size(m)
}
func (m *ChairGameProgress) XXX_DiscardUnknown() {
	xxx_messageInfo_ChairGameProgress.DiscardUnknown(m)
}

var xxx_messageInfo_ChairGameProgress proto.InternalMessageInfo

func (m *ChairGameProgress) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ChairGameProgress) GetCurRound() uint32 {
	if m != nil {
		return m.CurRound
	}
	return 0
}

func (m *ChairGameProgress) GetChairNum() uint32 {
	if m != nil {
		return m.ChairNum
	}
	return 0
}

func (m *ChairGameProgress) GetRoundStatus() uint32 {
	if m != nil {
		return m.RoundStatus
	}
	return 0
}

func (m *ChairGameProgress) GetShowRoundTip() bool {
	if m != nil {
		return m.ShowRoundTip
	}
	return false
}

func (m *ChairGameProgress) GetRoundPalyerUids() []uint32 {
	if m != nil {
		return m.RoundPalyerUids
	}
	return nil
}

func (m *ChairGameProgress) GetRoundWinnerUids() []uint32 {
	if m != nil {
		return m.RoundWinnerUids
	}
	return nil
}

func (m *ChairGameProgress) GetNextRoundChairNum() uint32 {
	if m != nil {
		return m.NextRoundChairNum
	}
	return 0
}

func (m *ChairGameProgress) GetServerTimeMs() int64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *ChairGameProgress) GetHostStartButDuration() int64 {
	if m != nil {
		return m.HostStartButDuration
	}
	return 0
}

func (m *ChairGameProgress) GetHostButtonEndTs() int64 {
	if m != nil {
		return m.HostButtonEndTs
	}
	return 0
}

type ChairGameInfo struct {
	GameId               uint32                 `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	ShowGameBeginAnim    bool                   `protobuf:"varint,2,opt,name=show_game_begin_anim,json=showGameBeginAnim,proto3" json:"show_game_begin_anim,omitempty"`
	GameProgress         *ChairGameProgress     `protobuf:"bytes,3,opt,name=game_progress,json=gameProgress,proto3" json:"game_progress,omitempty"`
	RewardList           []*ChairGameRewardInfo `protobuf:"bytes,4,rep,name=reward_list,json=rewardList,proto3" json:"reward_list,omitempty"`
	PlayerList           []*app.UserProfile     `protobuf:"bytes,5,rep,name=player_list,json=playerList,proto3" json:"player_list,omitempty"` // Deprecated: Do not use.
	Players              []*ChairGamePlayerInfo `protobuf:"bytes,6,rep,name=players,proto3" json:"players,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ChairGameInfo) Reset()         { *m = ChairGameInfo{} }
func (m *ChairGameInfo) String() string { return proto.CompactTextString(m) }
func (*ChairGameInfo) ProtoMessage()    {}
func (*ChairGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{69}
}
func (m *ChairGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChairGameInfo.Unmarshal(m, b)
}
func (m *ChairGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChairGameInfo.Marshal(b, m, deterministic)
}
func (dst *ChairGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChairGameInfo.Merge(dst, src)
}
func (m *ChairGameInfo) XXX_Size() int {
	return xxx_messageInfo_ChairGameInfo.Size(m)
}
func (m *ChairGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChairGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChairGameInfo proto.InternalMessageInfo

func (m *ChairGameInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ChairGameInfo) GetShowGameBeginAnim() bool {
	if m != nil {
		return m.ShowGameBeginAnim
	}
	return false
}

func (m *ChairGameInfo) GetGameProgress() *ChairGameProgress {
	if m != nil {
		return m.GameProgress
	}
	return nil
}

func (m *ChairGameInfo) GetRewardList() []*ChairGameRewardInfo {
	if m != nil {
		return m.RewardList
	}
	return nil
}

// Deprecated: Do not use.
func (m *ChairGameInfo) GetPlayerList() []*app.UserProfile {
	if m != nil {
		return m.PlayerList
	}
	return nil
}

func (m *ChairGameInfo) GetPlayers() []*ChairGamePlayerInfo {
	if m != nil {
		return m.Players
	}
	return nil
}

type ChairGamePlayerInfo struct {
	UserInfo             *app.UserProfile `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	MicId                uint32           `protobuf:"varint,2,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ChairGamePlayerInfo) Reset()         { *m = ChairGamePlayerInfo{} }
func (m *ChairGamePlayerInfo) String() string { return proto.CompactTextString(m) }
func (*ChairGamePlayerInfo) ProtoMessage()    {}
func (*ChairGamePlayerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{70}
}
func (m *ChairGamePlayerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChairGamePlayerInfo.Unmarshal(m, b)
}
func (m *ChairGamePlayerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChairGamePlayerInfo.Marshal(b, m, deterministic)
}
func (dst *ChairGamePlayerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChairGamePlayerInfo.Merge(dst, src)
}
func (m *ChairGamePlayerInfo) XXX_Size() int {
	return xxx_messageInfo_ChairGamePlayerInfo.Size(m)
}
func (m *ChairGamePlayerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChairGamePlayerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChairGamePlayerInfo proto.InternalMessageInfo

func (m *ChairGamePlayerInfo) GetUserInfo() *app.UserProfile {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *ChairGamePlayerInfo) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

// 获取房内抢椅子游戏信息
type GetChairGameInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChairGameInfoRequest) Reset()         { *m = GetChairGameInfoRequest{} }
func (m *GetChairGameInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetChairGameInfoRequest) ProtoMessage()    {}
func (*GetChairGameInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{71}
}
func (m *GetChairGameInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChairGameInfoRequest.Unmarshal(m, b)
}
func (m *GetChairGameInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChairGameInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetChairGameInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChairGameInfoRequest.Merge(dst, src)
}
func (m *GetChairGameInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetChairGameInfoRequest.Size(m)
}
func (m *GetChairGameInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChairGameInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChairGameInfoRequest proto.InternalMessageInfo

func (m *GetChairGameInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChairGameInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChairGameInfoResponse struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GameInfo             *ChairGameInfo `protobuf:"bytes,2,opt,name=game_info,json=gameInfo,proto3" json:"game_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetChairGameInfoResponse) Reset()         { *m = GetChairGameInfoResponse{} }
func (m *GetChairGameInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetChairGameInfoResponse) ProtoMessage()    {}
func (*GetChairGameInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{72}
}
func (m *GetChairGameInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChairGameInfoResponse.Unmarshal(m, b)
}
func (m *GetChairGameInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChairGameInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetChairGameInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChairGameInfoResponse.Merge(dst, src)
}
func (m *GetChairGameInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetChairGameInfoResponse.Size(m)
}
func (m *GetChairGameInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChairGameInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChairGameInfoResponse proto.InternalMessageInfo

func (m *GetChairGameInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChairGameInfoResponse) GetGameInfo() *ChairGameInfo {
	if m != nil {
		return m.GameInfo
	}
	return nil
}

// 抢座
type GrabChairRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32       `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GrabChairRequest) Reset()         { *m = GrabChairRequest{} }
func (m *GrabChairRequest) String() string { return proto.CompactTextString(m) }
func (*GrabChairRequest) ProtoMessage()    {}
func (*GrabChairRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{73}
}
func (m *GrabChairRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrabChairRequest.Unmarshal(m, b)
}
func (m *GrabChairRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrabChairRequest.Marshal(b, m, deterministic)
}
func (dst *GrabChairRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrabChairRequest.Merge(dst, src)
}
func (m *GrabChairRequest) XXX_Size() int {
	return xxx_messageInfo_GrabChairRequest.Size(m)
}
func (m *GrabChairRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GrabChairRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GrabChairRequest proto.InternalMessageInfo

func (m *GrabChairRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GrabChairRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GrabChairRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GrabChairResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GrabChairResponse) Reset()         { *m = GrabChairResponse{} }
func (m *GrabChairResponse) String() string { return proto.CompactTextString(m) }
func (*GrabChairResponse) ProtoMessage()    {}
func (*GrabChairResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{74}
}
func (m *GrabChairResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrabChairResponse.Unmarshal(m, b)
}
func (m *GrabChairResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrabChairResponse.Marshal(b, m, deterministic)
}
func (dst *GrabChairResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrabChairResponse.Merge(dst, src)
}
func (m *GrabChairResponse) XXX_Size() int {
	return xxx_messageInfo_GrabChairResponse.Size(m)
}
func (m *GrabChairResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GrabChairResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GrabChairResponse proto.InternalMessageInfo

func (m *GrabChairResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// --------------- 主持人侧 ------------------
// 开启新的一局
type StartChairGameRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PlayerUids           []uint32     `protobuf:"varint,3,rep,packed,name=player_uids,json=playerUids,proto3" json:"player_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StartChairGameRequest) Reset()         { *m = StartChairGameRequest{} }
func (m *StartChairGameRequest) String() string { return proto.CompactTextString(m) }
func (*StartChairGameRequest) ProtoMessage()    {}
func (*StartChairGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{75}
}
func (m *StartChairGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartChairGameRequest.Unmarshal(m, b)
}
func (m *StartChairGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartChairGameRequest.Marshal(b, m, deterministic)
}
func (dst *StartChairGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartChairGameRequest.Merge(dst, src)
}
func (m *StartChairGameRequest) XXX_Size() int {
	return xxx_messageInfo_StartChairGameRequest.Size(m)
}
func (m *StartChairGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StartChairGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StartChairGameRequest proto.InternalMessageInfo

func (m *StartChairGameRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *StartChairGameRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StartChairGameRequest) GetPlayerUids() []uint32 {
	if m != nil {
		return m.PlayerUids
	}
	return nil
}

type StartChairGameResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *StartChairGameResponse) Reset()         { *m = StartChairGameResponse{} }
func (m *StartChairGameResponse) String() string { return proto.CompactTextString(m) }
func (*StartChairGameResponse) ProtoMessage()    {}
func (*StartChairGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{76}
}
func (m *StartChairGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartChairGameResponse.Unmarshal(m, b)
}
func (m *StartChairGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartChairGameResponse.Marshal(b, m, deterministic)
}
func (dst *StartChairGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartChairGameResponse.Merge(dst, src)
}
func (m *StartChairGameResponse) XXX_Size() int {
	return xxx_messageInfo_StartChairGameResponse.Size(m)
}
func (m *StartChairGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StartChairGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StartChairGameResponse proto.InternalMessageInfo

func (m *StartChairGameResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 进入下一轮
type SetChairGameToNextRoundRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetChairGameToNextRoundRequest) Reset()         { *m = SetChairGameToNextRoundRequest{} }
func (m *SetChairGameToNextRoundRequest) String() string { return proto.CompactTextString(m) }
func (*SetChairGameToNextRoundRequest) ProtoMessage()    {}
func (*SetChairGameToNextRoundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{77}
}
func (m *SetChairGameToNextRoundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChairGameToNextRoundRequest.Unmarshal(m, b)
}
func (m *SetChairGameToNextRoundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChairGameToNextRoundRequest.Marshal(b, m, deterministic)
}
func (dst *SetChairGameToNextRoundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChairGameToNextRoundRequest.Merge(dst, src)
}
func (m *SetChairGameToNextRoundRequest) XXX_Size() int {
	return xxx_messageInfo_SetChairGameToNextRoundRequest.Size(m)
}
func (m *SetChairGameToNextRoundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChairGameToNextRoundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetChairGameToNextRoundRequest proto.InternalMessageInfo

func (m *SetChairGameToNextRoundRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetChairGameToNextRoundRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SetChairGameToNextRoundResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetChairGameToNextRoundResponse) Reset()         { *m = SetChairGameToNextRoundResponse{} }
func (m *SetChairGameToNextRoundResponse) String() string { return proto.CompactTextString(m) }
func (*SetChairGameToNextRoundResponse) ProtoMessage()    {}
func (*SetChairGameToNextRoundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{78}
}
func (m *SetChairGameToNextRoundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChairGameToNextRoundResponse.Unmarshal(m, b)
}
func (m *SetChairGameToNextRoundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChairGameToNextRoundResponse.Marshal(b, m, deterministic)
}
func (dst *SetChairGameToNextRoundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChairGameToNextRoundResponse.Merge(dst, src)
}
func (m *SetChairGameToNextRoundResponse) XXX_Size() int {
	return xxx_messageInfo_SetChairGameToNextRoundResponse.Size(m)
}
func (m *SetChairGameToNextRoundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChairGameToNextRoundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetChairGameToNextRoundResponse proto.InternalMessageInfo

func (m *SetChairGameToNextRoundResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 开抢
type StartGrabChairRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StartGrabChairRequest) Reset()         { *m = StartGrabChairRequest{} }
func (m *StartGrabChairRequest) String() string { return proto.CompactTextString(m) }
func (*StartGrabChairRequest) ProtoMessage()    {}
func (*StartGrabChairRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{79}
}
func (m *StartGrabChairRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartGrabChairRequest.Unmarshal(m, b)
}
func (m *StartGrabChairRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartGrabChairRequest.Marshal(b, m, deterministic)
}
func (dst *StartGrabChairRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartGrabChairRequest.Merge(dst, src)
}
func (m *StartGrabChairRequest) XXX_Size() int {
	return xxx_messageInfo_StartGrabChairRequest.Size(m)
}
func (m *StartGrabChairRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StartGrabChairRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StartGrabChairRequest proto.InternalMessageInfo

func (m *StartGrabChairRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *StartGrabChairRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type StartGrabChairResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *StartGrabChairResponse) Reset()         { *m = StartGrabChairResponse{} }
func (m *StartGrabChairResponse) String() string { return proto.CompactTextString(m) }
func (*StartGrabChairResponse) ProtoMessage()    {}
func (*StartGrabChairResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{80}
}
func (m *StartGrabChairResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartGrabChairResponse.Unmarshal(m, b)
}
func (m *StartGrabChairResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartGrabChairResponse.Marshal(b, m, deterministic)
}
func (dst *StartGrabChairResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartGrabChairResponse.Merge(dst, src)
}
func (m *StartGrabChairResponse) XXX_Size() int {
	return xxx_messageInfo_StartGrabChairResponse.Size(m)
}
func (m *StartGrabChairResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StartGrabChairResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StartGrabChairResponse proto.InternalMessageInfo

func (m *StartGrabChairResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 婚礼安排信息
type SimpleWeddingPlanInfo struct {
	WeddingPlanId              uint32             `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	ThemeId                    uint32             `protobuf:"varint,2,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	ScheduleWeddingDurationStr string             `protobuf:"bytes,3,opt,name=schedule_wedding_duration_str,json=scheduleWeddingDurationStr,proto3" json:"schedule_wedding_duration_str,omitempty"`
	GroomsmanList              []*app.UserProfile `protobuf:"bytes,4,rep,name=groomsman_list,json=groomsmanList,proto3" json:"groomsman_list,omitempty"`
	FamilyList                 []*app.UserProfile `protobuf:"bytes,5,rep,name=family_list,json=familyList,proto3" json:"family_list,omitempty"`
	AlreadyUploadBigScreenNum  uint32             `protobuf:"varint,6,opt,name=already_upload_big_screen_num,json=alreadyUploadBigScreenNum,proto3" json:"already_upload_big_screen_num,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}           `json:"-"`
	XXX_unrecognized           []byte             `json:"-"`
	XXX_sizecache              int32              `json:"-"`
}

func (m *SimpleWeddingPlanInfo) Reset()         { *m = SimpleWeddingPlanInfo{} }
func (m *SimpleWeddingPlanInfo) String() string { return proto.CompactTextString(m) }
func (*SimpleWeddingPlanInfo) ProtoMessage()    {}
func (*SimpleWeddingPlanInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{81}
}
func (m *SimpleWeddingPlanInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleWeddingPlanInfo.Unmarshal(m, b)
}
func (m *SimpleWeddingPlanInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleWeddingPlanInfo.Marshal(b, m, deterministic)
}
func (dst *SimpleWeddingPlanInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleWeddingPlanInfo.Merge(dst, src)
}
func (m *SimpleWeddingPlanInfo) XXX_Size() int {
	return xxx_messageInfo_SimpleWeddingPlanInfo.Size(m)
}
func (m *SimpleWeddingPlanInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleWeddingPlanInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleWeddingPlanInfo proto.InternalMessageInfo

func (m *SimpleWeddingPlanInfo) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *SimpleWeddingPlanInfo) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *SimpleWeddingPlanInfo) GetScheduleWeddingDurationStr() string {
	if m != nil {
		return m.ScheduleWeddingDurationStr
	}
	return ""
}

func (m *SimpleWeddingPlanInfo) GetGroomsmanList() []*app.UserProfile {
	if m != nil {
		return m.GroomsmanList
	}
	return nil
}

func (m *SimpleWeddingPlanInfo) GetFamilyList() []*app.UserProfile {
	if m != nil {
		return m.FamilyList
	}
	return nil
}

func (m *SimpleWeddingPlanInfo) GetAlreadyUploadBigScreenNum() uint32 {
	if m != nil {
		return m.AlreadyUploadBigScreenNum
	}
	return 0
}

type BigScreenImage struct {
	ImgUrl               string   `protobuf:"bytes,1,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	IsUnderReview        bool     `protobuf:"varint,2,opt,name=is_under_review,json=isUnderReview,proto3" json:"is_under_review,omitempty"`
	UploadByUid          uint32   `protobuf:"varint,3,opt,name=upload_by_uid,json=uploadByUid,proto3" json:"upload_by_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BigScreenImage) Reset()         { *m = BigScreenImage{} }
func (m *BigScreenImage) String() string { return proto.CompactTextString(m) }
func (*BigScreenImage) ProtoMessage()    {}
func (*BigScreenImage) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{82}
}
func (m *BigScreenImage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BigScreenImage.Unmarshal(m, b)
}
func (m *BigScreenImage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BigScreenImage.Marshal(b, m, deterministic)
}
func (dst *BigScreenImage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BigScreenImage.Merge(dst, src)
}
func (m *BigScreenImage) XXX_Size() int {
	return xxx_messageInfo_BigScreenImage.Size(m)
}
func (m *BigScreenImage) XXX_DiscardUnknown() {
	xxx_messageInfo_BigScreenImage.DiscardUnknown(m)
}

var xxx_messageInfo_BigScreenImage proto.InternalMessageInfo

func (m *BigScreenImage) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *BigScreenImage) GetIsUnderReview() bool {
	if m != nil {
		return m.IsUnderReview
	}
	return false
}

func (m *BigScreenImage) GetUploadByUid() uint32 {
	if m != nil {
		return m.UploadByUid
	}
	return 0
}

type GetWeddingBigScreenRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	WeddingPlanId        uint32       `protobuf:"varint,2,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWeddingBigScreenRequest) Reset()         { *m = GetWeddingBigScreenRequest{} }
func (m *GetWeddingBigScreenRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingBigScreenRequest) ProtoMessage()    {}
func (*GetWeddingBigScreenRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{83}
}
func (m *GetWeddingBigScreenRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingBigScreenRequest.Unmarshal(m, b)
}
func (m *GetWeddingBigScreenRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingBigScreenRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingBigScreenRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingBigScreenRequest.Merge(dst, src)
}
func (m *GetWeddingBigScreenRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingBigScreenRequest.Size(m)
}
func (m *GetWeddingBigScreenRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingBigScreenRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingBigScreenRequest proto.InternalMessageInfo

func (m *GetWeddingBigScreenRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetWeddingBigScreenRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type GetWeddingBigScreenResponse struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ImgList              []*BigScreenImage `protobuf:"bytes,2,rep,name=img_list,json=imgList,proto3" json:"img_list,omitempty"`
	ServerTs             int64             `protobuf:"varint,3,opt,name=server_ts,json=serverTs,proto3" json:"server_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetWeddingBigScreenResponse) Reset()         { *m = GetWeddingBigScreenResponse{} }
func (m *GetWeddingBigScreenResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingBigScreenResponse) ProtoMessage()    {}
func (*GetWeddingBigScreenResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{84}
}
func (m *GetWeddingBigScreenResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingBigScreenResponse.Unmarshal(m, b)
}
func (m *GetWeddingBigScreenResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingBigScreenResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingBigScreenResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingBigScreenResponse.Merge(dst, src)
}
func (m *GetWeddingBigScreenResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingBigScreenResponse.Size(m)
}
func (m *GetWeddingBigScreenResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingBigScreenResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingBigScreenResponse proto.InternalMessageInfo

func (m *GetWeddingBigScreenResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWeddingBigScreenResponse) GetImgList() []*BigScreenImage {
	if m != nil {
		return m.ImgList
	}
	return nil
}

func (m *GetWeddingBigScreenResponse) GetServerTs() int64 {
	if m != nil {
		return m.ServerTs
	}
	return 0
}

type SaveWeddingBigScreenRequest struct {
	BaseReq              *app.BaseReq                                   `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	WeddingPlanId        uint32                                         `protobuf:"varint,2,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	ImgUrl               string                                         `protobuf:"bytes,3,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	Operation            SaveWeddingBigScreenRequest_BigScreenOperation `protobuf:"varint,4,opt,name=operation,proto3,enum=ga.channel_wedding_logic.SaveWeddingBigScreenRequest_BigScreenOperation" json:"operation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                       `json:"-"`
	XXX_unrecognized     []byte                                         `json:"-"`
	XXX_sizecache        int32                                          `json:"-"`
}

func (m *SaveWeddingBigScreenRequest) Reset()         { *m = SaveWeddingBigScreenRequest{} }
func (m *SaveWeddingBigScreenRequest) String() string { return proto.CompactTextString(m) }
func (*SaveWeddingBigScreenRequest) ProtoMessage()    {}
func (*SaveWeddingBigScreenRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{85}
}
func (m *SaveWeddingBigScreenRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveWeddingBigScreenRequest.Unmarshal(m, b)
}
func (m *SaveWeddingBigScreenRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveWeddingBigScreenRequest.Marshal(b, m, deterministic)
}
func (dst *SaveWeddingBigScreenRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveWeddingBigScreenRequest.Merge(dst, src)
}
func (m *SaveWeddingBigScreenRequest) XXX_Size() int {
	return xxx_messageInfo_SaveWeddingBigScreenRequest.Size(m)
}
func (m *SaveWeddingBigScreenRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveWeddingBigScreenRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SaveWeddingBigScreenRequest proto.InternalMessageInfo

func (m *SaveWeddingBigScreenRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SaveWeddingBigScreenRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *SaveWeddingBigScreenRequest) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *SaveWeddingBigScreenRequest) GetOperation() SaveWeddingBigScreenRequest_BigScreenOperation {
	if m != nil {
		return m.Operation
	}
	return SaveWeddingBigScreenRequest_BIG_SCREEN_OPERATION_UNSPECIFIED
}

type SaveWeddingBigScreenResponse struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ImgList              []*BigScreenImage `protobuf:"bytes,2,rep,name=img_list,json=imgList,proto3" json:"img_list,omitempty"`
	ServerTs             int64             `protobuf:"varint,3,opt,name=server_ts,json=serverTs,proto3" json:"server_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SaveWeddingBigScreenResponse) Reset()         { *m = SaveWeddingBigScreenResponse{} }
func (m *SaveWeddingBigScreenResponse) String() string { return proto.CompactTextString(m) }
func (*SaveWeddingBigScreenResponse) ProtoMessage()    {}
func (*SaveWeddingBigScreenResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{86}
}
func (m *SaveWeddingBigScreenResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveWeddingBigScreenResponse.Unmarshal(m, b)
}
func (m *SaveWeddingBigScreenResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveWeddingBigScreenResponse.Marshal(b, m, deterministic)
}
func (dst *SaveWeddingBigScreenResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveWeddingBigScreenResponse.Merge(dst, src)
}
func (m *SaveWeddingBigScreenResponse) XXX_Size() int {
	return xxx_messageInfo_SaveWeddingBigScreenResponse.Size(m)
}
func (m *SaveWeddingBigScreenResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveWeddingBigScreenResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SaveWeddingBigScreenResponse proto.InternalMessageInfo

func (m *SaveWeddingBigScreenResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SaveWeddingBigScreenResponse) GetImgList() []*BigScreenImage {
	if m != nil {
		return m.ImgList
	}
	return nil
}

func (m *SaveWeddingBigScreenResponse) GetServerTs() int64 {
	if m != nil {
		return m.ServerTs
	}
	return 0
}

// 婚礼大屏图片变更通知，包括 新增、删除、审核状态变化
type WeddingBigScreenChangeNotify struct {
	WeddingPlanId        uint32            `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	ImgList              []*BigScreenImage `protobuf:"bytes,2,rep,name=img_list,json=imgList,proto3" json:"img_list,omitempty"`
	ServerTs             int64             `protobuf:"varint,3,opt,name=server_ts,json=serverTs,proto3" json:"server_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *WeddingBigScreenChangeNotify) Reset()         { *m = WeddingBigScreenChangeNotify{} }
func (m *WeddingBigScreenChangeNotify) String() string { return proto.CompactTextString(m) }
func (*WeddingBigScreenChangeNotify) ProtoMessage()    {}
func (*WeddingBigScreenChangeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{87}
}
func (m *WeddingBigScreenChangeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingBigScreenChangeNotify.Unmarshal(m, b)
}
func (m *WeddingBigScreenChangeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingBigScreenChangeNotify.Marshal(b, m, deterministic)
}
func (dst *WeddingBigScreenChangeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingBigScreenChangeNotify.Merge(dst, src)
}
func (m *WeddingBigScreenChangeNotify) XXX_Size() int {
	return xxx_messageInfo_WeddingBigScreenChangeNotify.Size(m)
}
func (m *WeddingBigScreenChangeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingBigScreenChangeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingBigScreenChangeNotify proto.InternalMessageInfo

func (m *WeddingBigScreenChangeNotify) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *WeddingBigScreenChangeNotify) GetImgList() []*BigScreenImage {
	if m != nil {
		return m.ImgList
	}
	return nil
}

func (m *WeddingBigScreenChangeNotify) GetServerTs() int64 {
	if m != nil {
		return m.ServerTs
	}
	return 0
}

type InviteCard struct {
	InviteId             uint32              `protobuf:"varint,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	WeddingPlanId        uint32              `protobuf:"varint,3,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	ThemeId              uint32              `protobuf:"varint,4,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	Groom                *app.UserProfile    `protobuf:"bytes,5,opt,name=groom,proto3" json:"groom,omitempty"`
	Bride                *app.UserProfile    `protobuf:"bytes,6,opt,name=bride,proto3" json:"bride,omitempty"`
	WeddingDatetime      string              `protobuf:"bytes,7,opt,name=wedding_datetime,json=weddingDatetime,proto3" json:"wedding_datetime,omitempty"`
	WeddingStartTime     uint32              `protobuf:"varint,13,opt,name=wedding_start_time,json=weddingStartTime,proto3" json:"wedding_start_time,omitempty"`
	WeddingChannel       string              `protobuf:"bytes,8,opt,name=wedding_channel,json=weddingChannel,proto3" json:"wedding_channel,omitempty"`
	WeddingChannelId     uint32              `protobuf:"varint,12,opt,name=wedding_channel_id,json=weddingChannelId,proto3" json:"wedding_channel_id,omitempty"`
	GiftList             []*WeddingGuestGift `protobuf:"bytes,9,rep,name=gift_list,json=giftList,proto3" json:"gift_list,omitempty"`
	InviteCardBg         string              `protobuf:"bytes,10,opt,name=invite_card_bg,json=inviteCardBg,proto3" json:"invite_card_bg,omitempty"`
	WeddingInviteType    uint32              `protobuf:"varint,11,opt,name=wedding_invite_type,json=weddingInviteType,proto3" json:"wedding_invite_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *InviteCard) Reset()         { *m = InviteCard{} }
func (m *InviteCard) String() string { return proto.CompactTextString(m) }
func (*InviteCard) ProtoMessage()    {}
func (*InviteCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{88}
}
func (m *InviteCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteCard.Unmarshal(m, b)
}
func (m *InviteCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteCard.Marshal(b, m, deterministic)
}
func (dst *InviteCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteCard.Merge(dst, src)
}
func (m *InviteCard) XXX_Size() int {
	return xxx_messageInfo_InviteCard.Size(m)
}
func (m *InviteCard) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteCard.DiscardUnknown(m)
}

var xxx_messageInfo_InviteCard proto.InternalMessageInfo

func (m *InviteCard) GetInviteId() uint32 {
	if m != nil {
		return m.InviteId
	}
	return 0
}

func (m *InviteCard) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *InviteCard) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *InviteCard) GetGroom() *app.UserProfile {
	if m != nil {
		return m.Groom
	}
	return nil
}

func (m *InviteCard) GetBride() *app.UserProfile {
	if m != nil {
		return m.Bride
	}
	return nil
}

func (m *InviteCard) GetWeddingDatetime() string {
	if m != nil {
		return m.WeddingDatetime
	}
	return ""
}

func (m *InviteCard) GetWeddingStartTime() uint32 {
	if m != nil {
		return m.WeddingStartTime
	}
	return 0
}

func (m *InviteCard) GetWeddingChannel() string {
	if m != nil {
		return m.WeddingChannel
	}
	return ""
}

func (m *InviteCard) GetWeddingChannelId() uint32 {
	if m != nil {
		return m.WeddingChannelId
	}
	return 0
}

func (m *InviteCard) GetGiftList() []*WeddingGuestGift {
	if m != nil {
		return m.GiftList
	}
	return nil
}

func (m *InviteCard) GetInviteCardBg() string {
	if m != nil {
		return m.InviteCardBg
	}
	return ""
}

func (m *InviteCard) GetWeddingInviteType() uint32 {
	if m != nil {
		return m.WeddingInviteType
	}
	return 0
}

type WeddingGuestGift struct {
	GiftUrl              string   `protobuf:"bytes,1,opt,name=gift_url,json=giftUrl,proto3" json:"gift_url,omitempty"`
	GiftDesc             string   `protobuf:"bytes,2,opt,name=gift_desc,json=giftDesc,proto3" json:"gift_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingGuestGift) Reset()         { *m = WeddingGuestGift{} }
func (m *WeddingGuestGift) String() string { return proto.CompactTextString(m) }
func (*WeddingGuestGift) ProtoMessage()    {}
func (*WeddingGuestGift) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{89}
}
func (m *WeddingGuestGift) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingGuestGift.Unmarshal(m, b)
}
func (m *WeddingGuestGift) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingGuestGift.Marshal(b, m, deterministic)
}
func (dst *WeddingGuestGift) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingGuestGift.Merge(dst, src)
}
func (m *WeddingGuestGift) XXX_Size() int {
	return xxx_messageInfo_WeddingGuestGift.Size(m)
}
func (m *WeddingGuestGift) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingGuestGift.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingGuestGift proto.InternalMessageInfo

func (m *WeddingGuestGift) GetGiftUrl() string {
	if m != nil {
		return m.GiftUrl
	}
	return ""
}

func (m *WeddingGuestGift) GetGiftDesc() string {
	if m != nil {
		return m.GiftDesc
	}
	return ""
}

// 获取邀请信息(点击im)
type GetWeddingInviteInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	WeddingPlanId        uint32       `protobuf:"varint,2,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	WeddingInviteType    uint32       `protobuf:"varint,3,opt,name=wedding_invite_type,json=weddingInviteType,proto3" json:"wedding_invite_type,omitempty"`
	InviteId             uint32       `protobuf:"varint,4,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWeddingInviteInfoRequest) Reset()         { *m = GetWeddingInviteInfoRequest{} }
func (m *GetWeddingInviteInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingInviteInfoRequest) ProtoMessage()    {}
func (*GetWeddingInviteInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{90}
}
func (m *GetWeddingInviteInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingInviteInfoRequest.Unmarshal(m, b)
}
func (m *GetWeddingInviteInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingInviteInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingInviteInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingInviteInfoRequest.Merge(dst, src)
}
func (m *GetWeddingInviteInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingInviteInfoRequest.Size(m)
}
func (m *GetWeddingInviteInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingInviteInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingInviteInfoRequest proto.InternalMessageInfo

func (m *GetWeddingInviteInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetWeddingInviteInfoRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *GetWeddingInviteInfoRequest) GetWeddingInviteType() uint32 {
	if m != nil {
		return m.WeddingInviteType
	}
	return 0
}

func (m *GetWeddingInviteInfoRequest) GetInviteId() uint32 {
	if m != nil {
		return m.InviteId
	}
	return 0
}

type GetWeddingInviteInfoResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	InviteCard           *InviteCard   `protobuf:"bytes,2,opt,name=invite_card,json=inviteCard,proto3" json:"invite_card,omitempty"`
	InviteStatus         uint32        `protobuf:"varint,3,opt,name=invite_status,json=inviteStatus,proto3" json:"invite_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetWeddingInviteInfoResponse) Reset()         { *m = GetWeddingInviteInfoResponse{} }
func (m *GetWeddingInviteInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingInviteInfoResponse) ProtoMessage()    {}
func (*GetWeddingInviteInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{91}
}
func (m *GetWeddingInviteInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingInviteInfoResponse.Unmarshal(m, b)
}
func (m *GetWeddingInviteInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingInviteInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingInviteInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingInviteInfoResponse.Merge(dst, src)
}
func (m *GetWeddingInviteInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingInviteInfoResponse.Size(m)
}
func (m *GetWeddingInviteInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingInviteInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingInviteInfoResponse proto.InternalMessageInfo

func (m *GetWeddingInviteInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWeddingInviteInfoResponse) GetInviteCard() *InviteCard {
	if m != nil {
		return m.InviteCard
	}
	return nil
}

func (m *GetWeddingInviteInfoResponse) GetInviteStatus() uint32 {
	if m != nil {
		return m.InviteStatus
	}
	return 0
}

type HandleWeddingInviteRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	InviteId             uint32       `protobuf:"varint,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	IsAccept             bool         `protobuf:"varint,3,opt,name=is_accept,json=isAccept,proto3" json:"is_accept,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *HandleWeddingInviteRequest) Reset()         { *m = HandleWeddingInviteRequest{} }
func (m *HandleWeddingInviteRequest) String() string { return proto.CompactTextString(m) }
func (*HandleWeddingInviteRequest) ProtoMessage()    {}
func (*HandleWeddingInviteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{92}
}
func (m *HandleWeddingInviteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleWeddingInviteRequest.Unmarshal(m, b)
}
func (m *HandleWeddingInviteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleWeddingInviteRequest.Marshal(b, m, deterministic)
}
func (dst *HandleWeddingInviteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleWeddingInviteRequest.Merge(dst, src)
}
func (m *HandleWeddingInviteRequest) XXX_Size() int {
	return xxx_messageInfo_HandleWeddingInviteRequest.Size(m)
}
func (m *HandleWeddingInviteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleWeddingInviteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HandleWeddingInviteRequest proto.InternalMessageInfo

func (m *HandleWeddingInviteRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *HandleWeddingInviteRequest) GetInviteId() uint32 {
	if m != nil {
		return m.InviteId
	}
	return 0
}

func (m *HandleWeddingInviteRequest) GetIsAccept() bool {
	if m != nil {
		return m.IsAccept
	}
	return false
}

type HandleWeddingInviteResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *HandleWeddingInviteResponse) Reset()         { *m = HandleWeddingInviteResponse{} }
func (m *HandleWeddingInviteResponse) String() string { return proto.CompactTextString(m) }
func (*HandleWeddingInviteResponse) ProtoMessage()    {}
func (*HandleWeddingInviteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{93}
}
func (m *HandleWeddingInviteResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleWeddingInviteResponse.Unmarshal(m, b)
}
func (m *HandleWeddingInviteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleWeddingInviteResponse.Marshal(b, m, deterministic)
}
func (dst *HandleWeddingInviteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleWeddingInviteResponse.Merge(dst, src)
}
func (m *HandleWeddingInviteResponse) XXX_Size() int {
	return xxx_messageInfo_HandleWeddingInviteResponse.Size(m)
}
func (m *HandleWeddingInviteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleWeddingInviteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HandleWeddingInviteResponse proto.InternalMessageInfo

func (m *HandleWeddingInviteResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 购买婚礼请求
type BuyWeddingRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ThemeId              uint32       `protobuf:"varint,2,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	ReserveInfo          *ReserveInfo `protobuf:"bytes,3,opt,name=reserve_info,json=reserveInfo,proto3" json:"reserve_info,omitempty"`
	SourceMsgId          uint32       `protobuf:"varint,4,opt,name=source_msg_id,json=sourceMsgId,proto3" json:"source_msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BuyWeddingRequest) Reset()         { *m = BuyWeddingRequest{} }
func (m *BuyWeddingRequest) String() string { return proto.CompactTextString(m) }
func (*BuyWeddingRequest) ProtoMessage()    {}
func (*BuyWeddingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{94}
}
func (m *BuyWeddingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyWeddingRequest.Unmarshal(m, b)
}
func (m *BuyWeddingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyWeddingRequest.Marshal(b, m, deterministic)
}
func (dst *BuyWeddingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyWeddingRequest.Merge(dst, src)
}
func (m *BuyWeddingRequest) XXX_Size() int {
	return xxx_messageInfo_BuyWeddingRequest.Size(m)
}
func (m *BuyWeddingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyWeddingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BuyWeddingRequest proto.InternalMessageInfo

func (m *BuyWeddingRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BuyWeddingRequest) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *BuyWeddingRequest) GetReserveInfo() *ReserveInfo {
	if m != nil {
		return m.ReserveInfo
	}
	return nil
}

func (m *BuyWeddingRequest) GetSourceMsgId() uint32 {
	if m != nil {
		return m.SourceMsgId
	}
	return 0
}

type ReserveInfo struct {
	ChannelName string `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	StartTime   uint32 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime     uint32 `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ThemeId     uint32 `protobuf:"varint,9,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	ThemeName   string `protobuf:"bytes,10,opt,name=theme_name,json=themeName,proto3" json:"theme_name,omitempty"`
	// 支付透传参数
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IsHot                bool     `protobuf:"varint,5,opt,name=is_hot,json=isHot,proto3" json:"is_hot,omitempty"`
	GiftId               uint32   `protobuf:"varint,6,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	ReserveDate          uint32   `protobuf:"varint,7,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date,omitempty"`
	ReserveTime          []string `protobuf:"bytes,8,rep,name=reserve_time,json=reserveTime,proto3" json:"reserve_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReserveInfo) Reset()         { *m = ReserveInfo{} }
func (m *ReserveInfo) String() string { return proto.CompactTextString(m) }
func (*ReserveInfo) ProtoMessage()    {}
func (*ReserveInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{95}
}
func (m *ReserveInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReserveInfo.Unmarshal(m, b)
}
func (m *ReserveInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReserveInfo.Marshal(b, m, deterministic)
}
func (dst *ReserveInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReserveInfo.Merge(dst, src)
}
func (m *ReserveInfo) XXX_Size() int {
	return xxx_messageInfo_ReserveInfo.Size(m)
}
func (m *ReserveInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReserveInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReserveInfo proto.InternalMessageInfo

func (m *ReserveInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ReserveInfo) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ReserveInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ReserveInfo) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *ReserveInfo) GetThemeName() string {
	if m != nil {
		return m.ThemeName
	}
	return ""
}

func (m *ReserveInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReserveInfo) GetIsHot() bool {
	if m != nil {
		return m.IsHot
	}
	return false
}

func (m *ReserveInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *ReserveInfo) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *ReserveInfo) GetReserveTime() []string {
	if m != nil {
		return m.ReserveTime
	}
	return nil
}

type BuyWeddingResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BuyWeddingResponse) Reset()         { *m = BuyWeddingResponse{} }
func (m *BuyWeddingResponse) String() string { return proto.CompactTextString(m) }
func (*BuyWeddingResponse) ProtoMessage()    {}
func (*BuyWeddingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{96}
}
func (m *BuyWeddingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyWeddingResponse.Unmarshal(m, b)
}
func (m *BuyWeddingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyWeddingResponse.Marshal(b, m, deterministic)
}
func (dst *BuyWeddingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyWeddingResponse.Merge(dst, src)
}
func (m *BuyWeddingResponse) XXX_Size() int {
	return xxx_messageInfo_BuyWeddingResponse.Size(m)
}
func (m *BuyWeddingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyWeddingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BuyWeddingResponse proto.InternalMessageInfo

func (m *BuyWeddingResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取婚礼计划页请求
type GetWeddingSchedulePageInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWeddingSchedulePageInfoRequest) Reset()         { *m = GetWeddingSchedulePageInfoRequest{} }
func (m *GetWeddingSchedulePageInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingSchedulePageInfoRequest) ProtoMessage()    {}
func (*GetWeddingSchedulePageInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{97}
}
func (m *GetWeddingSchedulePageInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingSchedulePageInfoRequest.Unmarshal(m, b)
}
func (m *GetWeddingSchedulePageInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingSchedulePageInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingSchedulePageInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingSchedulePageInfoRequest.Merge(dst, src)
}
func (m *GetWeddingSchedulePageInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingSchedulePageInfoRequest.Size(m)
}
func (m *GetWeddingSchedulePageInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingSchedulePageInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingSchedulePageInfoRequest proto.InternalMessageInfo

func (m *GetWeddingSchedulePageInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type WeddingDressPreview struct {
	Level                 uint32                `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	GuestType             uint32                `protobuf:"varint,2,opt,name=guest_type,json=guestType,proto3" json:"guest_type,omitempty"`
	WeddingDress          *WeddingAnimationInfo `protobuf:"bytes,3,opt,name=wedding_dress,json=weddingDress,proto3" json:"wedding_dress,omitempty"`
	DressText             string                `protobuf:"bytes,4,opt,name=dress_text,json=dressText,proto3" json:"dress_text,omitempty"`
	WeddingDressSmallIcon string                `protobuf:"bytes,5,opt,name=wedding_dress_small_icon,json=weddingDressSmallIcon,proto3" json:"wedding_dress_small_icon,omitempty"`
	PreviewPageDressText  string                `protobuf:"bytes,6,opt,name=preview_page_dress_text,json=previewPageDressText,proto3" json:"preview_page_dress_text,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}              `json:"-"`
	XXX_unrecognized      []byte                `json:"-"`
	XXX_sizecache         int32                 `json:"-"`
}

func (m *WeddingDressPreview) Reset()         { *m = WeddingDressPreview{} }
func (m *WeddingDressPreview) String() string { return proto.CompactTextString(m) }
func (*WeddingDressPreview) ProtoMessage()    {}
func (*WeddingDressPreview) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{98}
}
func (m *WeddingDressPreview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingDressPreview.Unmarshal(m, b)
}
func (m *WeddingDressPreview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingDressPreview.Marshal(b, m, deterministic)
}
func (dst *WeddingDressPreview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingDressPreview.Merge(dst, src)
}
func (m *WeddingDressPreview) XXX_Size() int {
	return xxx_messageInfo_WeddingDressPreview.Size(m)
}
func (m *WeddingDressPreview) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingDressPreview.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingDressPreview proto.InternalMessageInfo

func (m *WeddingDressPreview) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WeddingDressPreview) GetGuestType() uint32 {
	if m != nil {
		return m.GuestType
	}
	return 0
}

func (m *WeddingDressPreview) GetWeddingDress() *WeddingAnimationInfo {
	if m != nil {
		return m.WeddingDress
	}
	return nil
}

func (m *WeddingDressPreview) GetDressText() string {
	if m != nil {
		return m.DressText
	}
	return ""
}

func (m *WeddingDressPreview) GetWeddingDressSmallIcon() string {
	if m != nil {
		return m.WeddingDressSmallIcon
	}
	return ""
}

func (m *WeddingDressPreview) GetPreviewPageDressText() string {
	if m != nil {
		return m.PreviewPageDressText
	}
	return ""
}

type WeddingDressPreviewList struct {
	// 初中高三个等级资源, 免费只有一个
	DressPreviewList     []*WeddingDressPreview `protobuf:"bytes,1,rep,name=dress_preview_list,json=dressPreviewList,proto3" json:"dress_preview_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *WeddingDressPreviewList) Reset()         { *m = WeddingDressPreviewList{} }
func (m *WeddingDressPreviewList) String() string { return proto.CompactTextString(m) }
func (*WeddingDressPreviewList) ProtoMessage()    {}
func (*WeddingDressPreviewList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{99}
}
func (m *WeddingDressPreviewList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingDressPreviewList.Unmarshal(m, b)
}
func (m *WeddingDressPreviewList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingDressPreviewList.Marshal(b, m, deterministic)
}
func (dst *WeddingDressPreviewList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingDressPreviewList.Merge(dst, src)
}
func (m *WeddingDressPreviewList) XXX_Size() int {
	return xxx_messageInfo_WeddingDressPreviewList.Size(m)
}
func (m *WeddingDressPreviewList) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingDressPreviewList.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingDressPreviewList proto.InternalMessageInfo

func (m *WeddingDressPreviewList) GetDressPreviewList() []*WeddingDressPreview {
	if m != nil {
		return m.DressPreviewList
	}
	return nil
}

type WeddingScenePreview struct {
	Level                uint32                `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	SceneAnimation       *WeddingAnimationInfo `protobuf:"bytes,2,opt,name=scene_animation,json=sceneAnimation,proto3" json:"scene_animation,omitempty"`
	SmallIcon            string                `protobuf:"bytes,3,opt,name=small_icon,json=smallIcon,proto3" json:"small_icon,omitempty"`
	WeddingSceneText     string                `protobuf:"bytes,4,opt,name=wedding_scene_text,json=weddingSceneText,proto3" json:"wedding_scene_text,omitempty"`          // Deprecated: Do not use.
	VirtualCharacterUrl  string                `protobuf:"bytes,5,opt,name=virtual_character_url,json=virtualCharacterUrl,proto3" json:"virtual_character_url,omitempty"` // Deprecated: Do not use.
	VirtualCharacterMd5  string                `protobuf:"bytes,6,opt,name=virtual_character_md5,json=virtualCharacterMd5,proto3" json:"virtual_character_md5,omitempty"` // Deprecated: Do not use.
	ZoomAnimation        *WeddingAnimationInfo `protobuf:"bytes,7,opt,name=zoom_animation,json=zoomAnimation,proto3" json:"zoom_animation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *WeddingScenePreview) Reset()         { *m = WeddingScenePreview{} }
func (m *WeddingScenePreview) String() string { return proto.CompactTextString(m) }
func (*WeddingScenePreview) ProtoMessage()    {}
func (*WeddingScenePreview) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{100}
}
func (m *WeddingScenePreview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingScenePreview.Unmarshal(m, b)
}
func (m *WeddingScenePreview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingScenePreview.Marshal(b, m, deterministic)
}
func (dst *WeddingScenePreview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingScenePreview.Merge(dst, src)
}
func (m *WeddingScenePreview) XXX_Size() int {
	return xxx_messageInfo_WeddingScenePreview.Size(m)
}
func (m *WeddingScenePreview) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingScenePreview.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingScenePreview proto.InternalMessageInfo

func (m *WeddingScenePreview) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WeddingScenePreview) GetSceneAnimation() *WeddingAnimationInfo {
	if m != nil {
		return m.SceneAnimation
	}
	return nil
}

func (m *WeddingScenePreview) GetSmallIcon() string {
	if m != nil {
		return m.SmallIcon
	}
	return ""
}

// Deprecated: Do not use.
func (m *WeddingScenePreview) GetWeddingSceneText() string {
	if m != nil {
		return m.WeddingSceneText
	}
	return ""
}

// Deprecated: Do not use.
func (m *WeddingScenePreview) GetVirtualCharacterUrl() string {
	if m != nil {
		return m.VirtualCharacterUrl
	}
	return ""
}

// Deprecated: Do not use.
func (m *WeddingScenePreview) GetVirtualCharacterMd5() string {
	if m != nil {
		return m.VirtualCharacterMd5
	}
	return ""
}

func (m *WeddingScenePreview) GetZoomAnimation() *WeddingAnimationInfo {
	if m != nil {
		return m.ZoomAnimation
	}
	return nil
}

type FinishWeddingAward struct {
	AwardAnimation       *WeddingAnimationInfo `protobuf:"bytes,1,opt,name=award_animation,json=awardAnimation,proto3" json:"award_animation,omitempty"`
	TopText              string                `protobuf:"bytes,2,opt,name=top_text,json=topText,proto3" json:"top_text,omitempty"`
	BottomText           string                `protobuf:"bytes,3,opt,name=bottom_text,json=bottomText,proto3" json:"bottom_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *FinishWeddingAward) Reset()         { *m = FinishWeddingAward{} }
func (m *FinishWeddingAward) String() string { return proto.CompactTextString(m) }
func (*FinishWeddingAward) ProtoMessage()    {}
func (*FinishWeddingAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{101}
}
func (m *FinishWeddingAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FinishWeddingAward.Unmarshal(m, b)
}
func (m *FinishWeddingAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FinishWeddingAward.Marshal(b, m, deterministic)
}
func (dst *FinishWeddingAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishWeddingAward.Merge(dst, src)
}
func (m *FinishWeddingAward) XXX_Size() int {
	return xxx_messageInfo_FinishWeddingAward.Size(m)
}
func (m *FinishWeddingAward) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishWeddingAward.DiscardUnknown(m)
}

var xxx_messageInfo_FinishWeddingAward proto.InternalMessageInfo

func (m *FinishWeddingAward) GetAwardAnimation() *WeddingAnimationInfo {
	if m != nil {
		return m.AwardAnimation
	}
	return nil
}

func (m *FinishWeddingAward) GetTopText() string {
	if m != nil {
		return m.TopText
	}
	return ""
}

func (m *FinishWeddingAward) GetBottomText() string {
	if m != nil {
		return m.BottomText
	}
	return ""
}

type WeddingPriceInfo struct {
	Price                uint32   `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	PriceType            uint32   `protobuf:"varint,2,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingPriceInfo) Reset()         { *m = WeddingPriceInfo{} }
func (m *WeddingPriceInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingPriceInfo) ProtoMessage()    {}
func (*WeddingPriceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{102}
}
func (m *WeddingPriceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPriceInfo.Unmarshal(m, b)
}
func (m *WeddingPriceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPriceInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingPriceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPriceInfo.Merge(dst, src)
}
func (m *WeddingPriceInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingPriceInfo.Size(m)
}
func (m *WeddingPriceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPriceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPriceInfo proto.InternalMessageInfo

func (m *WeddingPriceInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *WeddingPriceInfo) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

// 婚礼主题
type WeddingTheme struct {
	Name                      string                              `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Price                     uint32                              `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"` // Deprecated: Do not use.
	PreviewResourceList       []*WeddingScenePreview              `protobuf:"bytes,3,rep,name=preview_resource_list,json=previewResourceList,proto3" json:"preview_resource_list,omitempty"`
	ThemeId                   uint32                              `protobuf:"varint,4,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	DressPreviewMap           map[uint32]*WeddingDressPreviewList `protobuf:"bytes,5,rep,name=dress_preview_map,json=dressPreviewMap,proto3" json:"dress_preview_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MaxShowGroomsmanNum       uint32                              `protobuf:"varint,6,opt,name=max_show_groomsman_num,json=maxShowGroomsmanNum,proto3" json:"max_show_groomsman_num,omitempty"` // Deprecated: Do not use.
	MaxShowFamilyNum          uint32                              `protobuf:"varint,7,opt,name=max_show_family_num,json=maxShowFamilyNum,proto3" json:"max_show_family_num,omitempty"`          // Deprecated: Do not use.
	SelectedThemeTitleIcon    string                              `protobuf:"bytes,8,opt,name=selected_theme_title_icon,json=selectedThemeTitleIcon,proto3" json:"selected_theme_title_icon,omitempty"`
	ExamplePhoto              string                              `protobuf:"bytes,9,opt,name=example_photo,json=examplePhoto,proto3" json:"example_photo,omitempty"`
	RewardInfoDesc            string                              `protobuf:"bytes,19,opt,name=reward_info_desc,json=rewardInfoDesc,proto3" json:"reward_info_desc,omitempty"`
	RewardInfoList            []*FinishWeddingAward               `protobuf:"bytes,10,rep,name=reward_info_list,json=rewardInfoList,proto3" json:"reward_info_list,omitempty"`
	MaxBigScreenNum           uint32                              `protobuf:"varint,11,opt,name=max_big_screen_num,json=maxBigScreenNum,proto3" json:"max_big_screen_num,omitempty"`
	PriceInfo                 *WeddingPriceInfo                   `protobuf:"bytes,12,opt,name=price_info,json=priceInfo,proto3" json:"price_info,omitempty"`
	ThemeBackground           string                              `protobuf:"bytes,13,opt,name=theme_background,json=themeBackground,proto3" json:"theme_background,omitempty"`
	ThemePreviewText          string                              `protobuf:"bytes,14,opt,name=theme_preview_text,json=themePreviewText,proto3" json:"theme_preview_text,omitempty"`
	UnselectedThemeTitleIcon  string                              `protobuf:"bytes,15,opt,name=unselected_theme_title_icon,json=unselectedThemeTitleIcon,proto3" json:"unselected_theme_title_icon,omitempty"`
	MailLadyLeftBgIcon        string                              `protobuf:"bytes,16,opt,name=mail_lady_left_bg_icon,json=mailLadyLeftBgIcon,proto3" json:"mail_lady_left_bg_icon,omitempty"`
	MailLadyRightBgIcon       string                              `protobuf:"bytes,17,opt,name=mail_lady_right_bg_icon,json=mailLadyRightBgIcon,proto3" json:"mail_lady_right_bg_icon,omitempty"`
	BigScreenResource         *WeddingAnimationInfo               `protobuf:"bytes,18,opt,name=big_screen_resource,json=bigScreenResource,proto3" json:"big_screen_resource,omitempty"`
	DressPreviewNewcomersDesc string                              `protobuf:"bytes,20,opt,name=dress_preview_newcomers_desc,json=dressPreviewNewcomersDesc,proto3" json:"dress_preview_newcomers_desc,omitempty"`
	DressPreviewGuestDesc     string                              `protobuf:"bytes,21,opt,name=dress_preview_guest_desc,json=dressPreviewGuestDesc,proto3" json:"dress_preview_guest_desc,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}                            `json:"-"`
	XXX_unrecognized          []byte                              `json:"-"`
	XXX_sizecache             int32                               `json:"-"`
}

func (m *WeddingTheme) Reset()         { *m = WeddingTheme{} }
func (m *WeddingTheme) String() string { return proto.CompactTextString(m) }
func (*WeddingTheme) ProtoMessage()    {}
func (*WeddingTheme) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{103}
}
func (m *WeddingTheme) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingTheme.Unmarshal(m, b)
}
func (m *WeddingTheme) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingTheme.Marshal(b, m, deterministic)
}
func (dst *WeddingTheme) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingTheme.Merge(dst, src)
}
func (m *WeddingTheme) XXX_Size() int {
	return xxx_messageInfo_WeddingTheme.Size(m)
}
func (m *WeddingTheme) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingTheme.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingTheme proto.InternalMessageInfo

func (m *WeddingTheme) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// Deprecated: Do not use.
func (m *WeddingTheme) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *WeddingTheme) GetPreviewResourceList() []*WeddingScenePreview {
	if m != nil {
		return m.PreviewResourceList
	}
	return nil
}

func (m *WeddingTheme) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *WeddingTheme) GetDressPreviewMap() map[uint32]*WeddingDressPreviewList {
	if m != nil {
		return m.DressPreviewMap
	}
	return nil
}

// Deprecated: Do not use.
func (m *WeddingTheme) GetMaxShowGroomsmanNum() uint32 {
	if m != nil {
		return m.MaxShowGroomsmanNum
	}
	return 0
}

// Deprecated: Do not use.
func (m *WeddingTheme) GetMaxShowFamilyNum() uint32 {
	if m != nil {
		return m.MaxShowFamilyNum
	}
	return 0
}

func (m *WeddingTheme) GetSelectedThemeTitleIcon() string {
	if m != nil {
		return m.SelectedThemeTitleIcon
	}
	return ""
}

func (m *WeddingTheme) GetExamplePhoto() string {
	if m != nil {
		return m.ExamplePhoto
	}
	return ""
}

func (m *WeddingTheme) GetRewardInfoDesc() string {
	if m != nil {
		return m.RewardInfoDesc
	}
	return ""
}

func (m *WeddingTheme) GetRewardInfoList() []*FinishWeddingAward {
	if m != nil {
		return m.RewardInfoList
	}
	return nil
}

func (m *WeddingTheme) GetMaxBigScreenNum() uint32 {
	if m != nil {
		return m.MaxBigScreenNum
	}
	return 0
}

func (m *WeddingTheme) GetPriceInfo() *WeddingPriceInfo {
	if m != nil {
		return m.PriceInfo
	}
	return nil
}

func (m *WeddingTheme) GetThemeBackground() string {
	if m != nil {
		return m.ThemeBackground
	}
	return ""
}

func (m *WeddingTheme) GetThemePreviewText() string {
	if m != nil {
		return m.ThemePreviewText
	}
	return ""
}

func (m *WeddingTheme) GetUnselectedThemeTitleIcon() string {
	if m != nil {
		return m.UnselectedThemeTitleIcon
	}
	return ""
}

func (m *WeddingTheme) GetMailLadyLeftBgIcon() string {
	if m != nil {
		return m.MailLadyLeftBgIcon
	}
	return ""
}

func (m *WeddingTheme) GetMailLadyRightBgIcon() string {
	if m != nil {
		return m.MailLadyRightBgIcon
	}
	return ""
}

func (m *WeddingTheme) GetBigScreenResource() *WeddingAnimationInfo {
	if m != nil {
		return m.BigScreenResource
	}
	return nil
}

func (m *WeddingTheme) GetDressPreviewNewcomersDesc() string {
	if m != nil {
		return m.DressPreviewNewcomersDesc
	}
	return ""
}

func (m *WeddingTheme) GetDressPreviewGuestDesc() string {
	if m != nil {
		return m.DressPreviewGuestDesc
	}
	return ""
}

// 伴侣信息
type WeddingPartner struct {
	UserProfile             *app.UserProfile `protobuf:"bytes,1,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	IsAccepted              bool             `protobuf:"varint,2,opt,name=is_accepted,json=isAccepted,proto3" json:"is_accepted,omitempty"`
	InviteAutoRejectDay     uint32           `protobuf:"varint,3,opt,name=invite_auto_reject_day,json=inviteAutoRejectDay,proto3" json:"invite_auto_reject_day,omitempty"`
	EndRelationshipDeadline int64            `protobuf:"varint,4,opt,name=end_relationship_deadline,json=endRelationshipDeadline,proto3" json:"end_relationship_deadline,omitempty"`
	AutoEndRelationshipDay  uint32           `protobuf:"varint,5,opt,name=auto_end_relationship_day,json=autoEndRelationshipDay,proto3" json:"auto_end_relationship_day,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}         `json:"-"`
	XXX_unrecognized        []byte           `json:"-"`
	XXX_sizecache           int32            `json:"-"`
}

func (m *WeddingPartner) Reset()         { *m = WeddingPartner{} }
func (m *WeddingPartner) String() string { return proto.CompactTextString(m) }
func (*WeddingPartner) ProtoMessage()    {}
func (*WeddingPartner) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{104}
}
func (m *WeddingPartner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPartner.Unmarshal(m, b)
}
func (m *WeddingPartner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPartner.Marshal(b, m, deterministic)
}
func (dst *WeddingPartner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPartner.Merge(dst, src)
}
func (m *WeddingPartner) XXX_Size() int {
	return xxx_messageInfo_WeddingPartner.Size(m)
}
func (m *WeddingPartner) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPartner.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPartner proto.InternalMessageInfo

func (m *WeddingPartner) GetUserProfile() *app.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *WeddingPartner) GetIsAccepted() bool {
	if m != nil {
		return m.IsAccepted
	}
	return false
}

func (m *WeddingPartner) GetInviteAutoRejectDay() uint32 {
	if m != nil {
		return m.InviteAutoRejectDay
	}
	return 0
}

func (m *WeddingPartner) GetEndRelationshipDeadline() int64 {
	if m != nil {
		return m.EndRelationshipDeadline
	}
	return 0
}

func (m *WeddingPartner) GetAutoEndRelationshipDay() uint32 {
	if m != nil {
		return m.AutoEndRelationshipDay
	}
	return 0
}

// 动画资源
type WeddingAnimationInfo struct {
	AnimationType        uint32   `protobuf:"varint,1,opt,name=animation_type,json=animationType,proto3" json:"animation_type,omitempty"`
	AnimationResource    string   `protobuf:"bytes,2,opt,name=animation_resource,json=animationResource,proto3" json:"animation_resource,omitempty"`
	AnimationResourceMd5 string   `protobuf:"bytes,3,opt,name=animation_resource_md5,json=animationResourceMd5,proto3" json:"animation_resource_md5,omitempty"`
	AnimationPng         string   `protobuf:"bytes,4,opt,name=animation_png,json=animationPng,proto3" json:"animation_png,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingAnimationInfo) Reset()         { *m = WeddingAnimationInfo{} }
func (m *WeddingAnimationInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingAnimationInfo) ProtoMessage()    {}
func (*WeddingAnimationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{105}
}
func (m *WeddingAnimationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingAnimationInfo.Unmarshal(m, b)
}
func (m *WeddingAnimationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingAnimationInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingAnimationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingAnimationInfo.Merge(dst, src)
}
func (m *WeddingAnimationInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingAnimationInfo.Size(m)
}
func (m *WeddingAnimationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingAnimationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingAnimationInfo proto.InternalMessageInfo

func (m *WeddingAnimationInfo) GetAnimationType() uint32 {
	if m != nil {
		return m.AnimationType
	}
	return 0
}

func (m *WeddingAnimationInfo) GetAnimationResource() string {
	if m != nil {
		return m.AnimationResource
	}
	return ""
}

func (m *WeddingAnimationInfo) GetAnimationResourceMd5() string {
	if m != nil {
		return m.AnimationResourceMd5
	}
	return ""
}

func (m *WeddingAnimationInfo) GetAnimationPng() string {
	if m != nil {
		return m.AnimationPng
	}
	return ""
}

type GetWeddingSchedulePageInfoResponse struct {
	BaseResp               *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MyInfo                 *app.UserProfile       `protobuf:"bytes,2,opt,name=my_info,json=myInfo,proto3" json:"my_info,omitempty"`
	Partner                *WeddingPartner        `protobuf:"bytes,3,opt,name=partner,proto3" json:"partner,omitempty"`
	TogetherDays           uint32                 `protobuf:"varint,4,opt,name=together_days,json=togetherDays,proto3" json:"together_days,omitempty"` // Deprecated: Do not use.
	WeddingPlanInfo        *SimpleWeddingPlanInfo `protobuf:"bytes,5,opt,name=wedding_plan_info,json=weddingPlanInfo,proto3" json:"wedding_plan_info,omitempty"`
	ThemeList              []*WeddingTheme        `protobuf:"bytes,6,rep,name=theme_list,json=themeList,proto3" json:"theme_list,omitempty"` // Deprecated: Do not use.
	IsBuyer                bool                   `protobuf:"varint,7,opt,name=is_buyer,json=isBuyer,proto3" json:"is_buyer,omitempty"`
	LimitCancelWeddingDay  uint32                 `protobuf:"varint,8,opt,name=limit_cancel_wedding_day,json=limitCancelWeddingDay,proto3" json:"limit_cancel_wedding_day,omitempty"`
	ThemeTitleSelectedIcon string                 `protobuf:"bytes,9,opt,name=theme_title_selected_icon,json=themeTitleSelectedIcon,proto3" json:"theme_title_selected_icon,omitempty"`
	ThemeTitleBgIcon       string                 `protobuf:"bytes,10,opt,name=theme_title_bg_icon,json=themeTitleBgIcon,proto3" json:"theme_title_bg_icon,omitempty"`
	BlessText              string                 `protobuf:"bytes,11,opt,name=bless_text,json=blessText,proto3" json:"bless_text,omitempty"`
	PayChannelList         []*PayWeddingChannel   `protobuf:"bytes,12,rep,name=pay_channel_list,json=payChannelList,proto3" json:"pay_channel_list,omitempty"`
	ProcessDescImg         string                 `protobuf:"bytes,20,opt,name=process_desc_img,json=processDescImg,proto3" json:"process_desc_img,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}               `json:"-"`
	XXX_unrecognized       []byte                 `json:"-"`
	XXX_sizecache          int32                  `json:"-"`
}

func (m *GetWeddingSchedulePageInfoResponse) Reset()         { *m = GetWeddingSchedulePageInfoResponse{} }
func (m *GetWeddingSchedulePageInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingSchedulePageInfoResponse) ProtoMessage()    {}
func (*GetWeddingSchedulePageInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{106}
}
func (m *GetWeddingSchedulePageInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingSchedulePageInfoResponse.Unmarshal(m, b)
}
func (m *GetWeddingSchedulePageInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingSchedulePageInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingSchedulePageInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingSchedulePageInfoResponse.Merge(dst, src)
}
func (m *GetWeddingSchedulePageInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingSchedulePageInfoResponse.Size(m)
}
func (m *GetWeddingSchedulePageInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingSchedulePageInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingSchedulePageInfoResponse proto.InternalMessageInfo

func (m *GetWeddingSchedulePageInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWeddingSchedulePageInfoResponse) GetMyInfo() *app.UserProfile {
	if m != nil {
		return m.MyInfo
	}
	return nil
}

func (m *GetWeddingSchedulePageInfoResponse) GetPartner() *WeddingPartner {
	if m != nil {
		return m.Partner
	}
	return nil
}

// Deprecated: Do not use.
func (m *GetWeddingSchedulePageInfoResponse) GetTogetherDays() uint32 {
	if m != nil {
		return m.TogetherDays
	}
	return 0
}

func (m *GetWeddingSchedulePageInfoResponse) GetWeddingPlanInfo() *SimpleWeddingPlanInfo {
	if m != nil {
		return m.WeddingPlanInfo
	}
	return nil
}

// Deprecated: Do not use.
func (m *GetWeddingSchedulePageInfoResponse) GetThemeList() []*WeddingTheme {
	if m != nil {
		return m.ThemeList
	}
	return nil
}

func (m *GetWeddingSchedulePageInfoResponse) GetIsBuyer() bool {
	if m != nil {
		return m.IsBuyer
	}
	return false
}

func (m *GetWeddingSchedulePageInfoResponse) GetLimitCancelWeddingDay() uint32 {
	if m != nil {
		return m.LimitCancelWeddingDay
	}
	return 0
}

func (m *GetWeddingSchedulePageInfoResponse) GetThemeTitleSelectedIcon() string {
	if m != nil {
		return m.ThemeTitleSelectedIcon
	}
	return ""
}

func (m *GetWeddingSchedulePageInfoResponse) GetThemeTitleBgIcon() string {
	if m != nil {
		return m.ThemeTitleBgIcon
	}
	return ""
}

func (m *GetWeddingSchedulePageInfoResponse) GetBlessText() string {
	if m != nil {
		return m.BlessText
	}
	return ""
}

func (m *GetWeddingSchedulePageInfoResponse) GetPayChannelList() []*PayWeddingChannel {
	if m != nil {
		return m.PayChannelList
	}
	return nil
}

func (m *GetWeddingSchedulePageInfoResponse) GetProcessDescImg() string {
	if m != nil {
		return m.ProcessDescImg
	}
	return ""
}

type PayWeddingChannel struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string   `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelIcon          string   `protobuf:"bytes,3,opt,name=channel_icon,json=channelIcon,proto3" json:"channel_icon,omitempty"`
	IsWedding            bool     `protobuf:"varint,4,opt,name=is_wedding,json=isWedding,proto3" json:"is_wedding,omitempty"`
	ViewId               uint32   `protobuf:"varint,5,opt,name=view_id,json=viewId,proto3" json:"view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayWeddingChannel) Reset()         { *m = PayWeddingChannel{} }
func (m *PayWeddingChannel) String() string { return proto.CompactTextString(m) }
func (*PayWeddingChannel) ProtoMessage()    {}
func (*PayWeddingChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{107}
}
func (m *PayWeddingChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayWeddingChannel.Unmarshal(m, b)
}
func (m *PayWeddingChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayWeddingChannel.Marshal(b, m, deterministic)
}
func (dst *PayWeddingChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayWeddingChannel.Merge(dst, src)
}
func (m *PayWeddingChannel) XXX_Size() int {
	return xxx_messageInfo_PayWeddingChannel.Size(m)
}
func (m *PayWeddingChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_PayWeddingChannel.DiscardUnknown(m)
}

var xxx_messageInfo_PayWeddingChannel proto.InternalMessageInfo

func (m *PayWeddingChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PayWeddingChannel) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *PayWeddingChannel) GetChannelIcon() string {
	if m != nil {
		return m.ChannelIcon
	}
	return ""
}

func (m *PayWeddingChannel) GetIsWedding() bool {
	if m != nil {
		return m.IsWedding
	}
	return false
}

func (m *PayWeddingChannel) GetViewId() uint32 {
	if m != nil {
		return m.ViewId
	}
	return 0
}

// 购买婚礼推送
type WeddingPaidNotify struct {
	MyInfo               *app.UserProfile `protobuf:"bytes,1,opt,name=my_info,json=myInfo,proto3" json:"my_info,omitempty"`
	PartnerInfo          *app.UserProfile `protobuf:"bytes,2,opt,name=partner_info,json=partnerInfo,proto3" json:"partner_info,omitempty"`
	BlessText            string           `protobuf:"bytes,3,opt,name=bless_text,json=blessText,proto3" json:"bless_text,omitempty"`
	WeddingPlanId        uint32           `protobuf:"varint,4,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WeddingPaidNotify) Reset()         { *m = WeddingPaidNotify{} }
func (m *WeddingPaidNotify) String() string { return proto.CompactTextString(m) }
func (*WeddingPaidNotify) ProtoMessage()    {}
func (*WeddingPaidNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{108}
}
func (m *WeddingPaidNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPaidNotify.Unmarshal(m, b)
}
func (m *WeddingPaidNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPaidNotify.Marshal(b, m, deterministic)
}
func (dst *WeddingPaidNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPaidNotify.Merge(dst, src)
}
func (m *WeddingPaidNotify) XXX_Size() int {
	return xxx_messageInfo_WeddingPaidNotify.Size(m)
}
func (m *WeddingPaidNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPaidNotify.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPaidNotify proto.InternalMessageInfo

func (m *WeddingPaidNotify) GetMyInfo() *app.UserProfile {
	if m != nil {
		return m.MyInfo
	}
	return nil
}

func (m *WeddingPaidNotify) GetPartnerInfo() *app.UserProfile {
	if m != nil {
		return m.PartnerInfo
	}
	return nil
}

func (m *WeddingPaidNotify) GetBlessText() string {
	if m != nil {
		return m.BlessText
	}
	return ""
}

func (m *WeddingPaidNotify) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

// 取消婚礼并退款
type CancelWeddingRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	WeddingPlanId        uint32       `protobuf:"varint,2,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CancelWeddingRequest) Reset()         { *m = CancelWeddingRequest{} }
func (m *CancelWeddingRequest) String() string { return proto.CompactTextString(m) }
func (*CancelWeddingRequest) ProtoMessage()    {}
func (*CancelWeddingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{109}
}
func (m *CancelWeddingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelWeddingRequest.Unmarshal(m, b)
}
func (m *CancelWeddingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelWeddingRequest.Marshal(b, m, deterministic)
}
func (dst *CancelWeddingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelWeddingRequest.Merge(dst, src)
}
func (m *CancelWeddingRequest) XXX_Size() int {
	return xxx_messageInfo_CancelWeddingRequest.Size(m)
}
func (m *CancelWeddingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelWeddingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CancelWeddingRequest proto.InternalMessageInfo

func (m *CancelWeddingRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CancelWeddingRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type CancelWeddingResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CancelWeddingResponse) Reset()         { *m = CancelWeddingResponse{} }
func (m *CancelWeddingResponse) String() string { return proto.CompactTextString(m) }
func (*CancelWeddingResponse) ProtoMessage()    {}
func (*CancelWeddingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{110}
}
func (m *CancelWeddingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelWeddingResponse.Unmarshal(m, b)
}
func (m *CancelWeddingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelWeddingResponse.Marshal(b, m, deterministic)
}
func (dst *CancelWeddingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelWeddingResponse.Merge(dst, src)
}
func (m *CancelWeddingResponse) XXX_Size() int {
	return xxx_messageInfo_CancelWeddingResponse.Size(m)
}
func (m *CancelWeddingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelWeddingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CancelWeddingResponse proto.InternalMessageInfo

func (m *CancelWeddingResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type WeddingHallItem struct {
	WeddingPlanId uint32 `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	// Types that are valid to be assigned to WeddingView:
	//	*WeddingHallItem_GoingView
	//	*WeddingHallItem_NotStartView
	WeddingView          isWeddingHallItem_WeddingView `protobuf_oneof:"wedding_view"`
	MaleUserInfo         *app.UserProfile              `protobuf:"bytes,4,opt,name=male_user_info,json=maleUserInfo,proto3" json:"male_user_info,omitempty"`
	FemaleUserInfo       *app.UserProfile              `protobuf:"bytes,5,opt,name=female_user_info,json=femaleUserInfo,proto3" json:"female_user_info,omitempty"`
	ThemeName            string                        `protobuf:"bytes,6,opt,name=theme_name,json=themeName,proto3" json:"theme_name,omitempty"`
	BackgroundPicture    string                        `protobuf:"bytes,7,opt,name=background_picture,json=backgroundPicture,proto3" json:"background_picture,omitempty"`
	FramePicture         string                        `protobuf:"bytes,8,opt,name=frame_picture,json=framePicture,proto3" json:"frame_picture,omitempty"`
	HotLabel             string                        `protobuf:"bytes,9,opt,name=hot_label,json=hotLabel,proto3" json:"hot_label,omitempty"`
	ChargeType           uint32                        `protobuf:"varint,10,opt,name=charge_type,json=chargeType,proto3" json:"charge_type,omitempty"`
	IsIdle               bool                          `protobuf:"varint,11,opt,name=is_idle,json=isIdle,proto3" json:"is_idle,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *WeddingHallItem) Reset()         { *m = WeddingHallItem{} }
func (m *WeddingHallItem) String() string { return proto.CompactTextString(m) }
func (*WeddingHallItem) ProtoMessage()    {}
func (*WeddingHallItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{111}
}
func (m *WeddingHallItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingHallItem.Unmarshal(m, b)
}
func (m *WeddingHallItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingHallItem.Marshal(b, m, deterministic)
}
func (dst *WeddingHallItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingHallItem.Merge(dst, src)
}
func (m *WeddingHallItem) XXX_Size() int {
	return xxx_messageInfo_WeddingHallItem.Size(m)
}
func (m *WeddingHallItem) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingHallItem.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingHallItem proto.InternalMessageInfo

func (m *WeddingHallItem) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type isWeddingHallItem_WeddingView interface {
	isWeddingHallItem_WeddingView()
}

type WeddingHallItem_GoingView struct {
	GoingView *WeddingHallItem_WeddingHallItemGoingView `protobuf:"bytes,2,opt,name=going_view,json=goingView,proto3,oneof"`
}

type WeddingHallItem_NotStartView struct {
	NotStartView *WeddingHallItem_WeddingHallItemNotStartView `protobuf:"bytes,3,opt,name=not_start_view,json=notStartView,proto3,oneof"`
}

func (*WeddingHallItem_GoingView) isWeddingHallItem_WeddingView() {}

func (*WeddingHallItem_NotStartView) isWeddingHallItem_WeddingView() {}

func (m *WeddingHallItem) GetWeddingView() isWeddingHallItem_WeddingView {
	if m != nil {
		return m.WeddingView
	}
	return nil
}

func (m *WeddingHallItem) GetGoingView() *WeddingHallItem_WeddingHallItemGoingView {
	if x, ok := m.GetWeddingView().(*WeddingHallItem_GoingView); ok {
		return x.GoingView
	}
	return nil
}

func (m *WeddingHallItem) GetNotStartView() *WeddingHallItem_WeddingHallItemNotStartView {
	if x, ok := m.GetWeddingView().(*WeddingHallItem_NotStartView); ok {
		return x.NotStartView
	}
	return nil
}

func (m *WeddingHallItem) GetMaleUserInfo() *app.UserProfile {
	if m != nil {
		return m.MaleUserInfo
	}
	return nil
}

func (m *WeddingHallItem) GetFemaleUserInfo() *app.UserProfile {
	if m != nil {
		return m.FemaleUserInfo
	}
	return nil
}

func (m *WeddingHallItem) GetThemeName() string {
	if m != nil {
		return m.ThemeName
	}
	return ""
}

func (m *WeddingHallItem) GetBackgroundPicture() string {
	if m != nil {
		return m.BackgroundPicture
	}
	return ""
}

func (m *WeddingHallItem) GetFramePicture() string {
	if m != nil {
		return m.FramePicture
	}
	return ""
}

func (m *WeddingHallItem) GetHotLabel() string {
	if m != nil {
		return m.HotLabel
	}
	return ""
}

func (m *WeddingHallItem) GetChargeType() uint32 {
	if m != nil {
		return m.ChargeType
	}
	return 0
}

func (m *WeddingHallItem) GetIsIdle() bool {
	if m != nil {
		return m.IsIdle
	}
	return false
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*WeddingHallItem) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _WeddingHallItem_OneofMarshaler, _WeddingHallItem_OneofUnmarshaler, _WeddingHallItem_OneofSizer, []interface{}{
		(*WeddingHallItem_GoingView)(nil),
		(*WeddingHallItem_NotStartView)(nil),
	}
}

func _WeddingHallItem_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*WeddingHallItem)
	// wedding_view
	switch x := m.WeddingView.(type) {
	case *WeddingHallItem_GoingView:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.GoingView); err != nil {
			return err
		}
	case *WeddingHallItem_NotStartView:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.NotStartView); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("WeddingHallItem.WeddingView has unexpected type %T", x)
	}
	return nil
}

func _WeddingHallItem_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*WeddingHallItem)
	switch tag {
	case 2: // wedding_view.going_view
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(WeddingHallItem_WeddingHallItemGoingView)
		err := b.DecodeMessage(msg)
		m.WeddingView = &WeddingHallItem_GoingView{msg}
		return true, err
	case 3: // wedding_view.not_start_view
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(WeddingHallItem_WeddingHallItemNotStartView)
		err := b.DecodeMessage(msg)
		m.WeddingView = &WeddingHallItem_NotStartView{msg}
		return true, err
	default:
		return false, nil
	}
}

func _WeddingHallItem_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*WeddingHallItem)
	// wedding_view
	switch x := m.WeddingView.(type) {
	case *WeddingHallItem_GoingView:
		s := proto.Size(x.GoingView)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *WeddingHallItem_NotStartView:
		s := proto.Size(x.NotStartView)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type WeddingHallItem_WeddingHallItemGoingView struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string   `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelStageDesc     string   `protobuf:"bytes,3,opt,name=channel_stage_desc,json=channelStageDesc,proto3" json:"channel_stage_desc,omitempty"`
	ChannelHotValue      int64    `protobuf:"varint,4,opt,name=channel_hot_value,json=channelHotValue,proto3" json:"channel_hot_value,omitempty"`
	ChannelIcon          string   `protobuf:"bytes,5,opt,name=channel_icon,json=channelIcon,proto3" json:"channel_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingHallItem_WeddingHallItemGoingView) Reset() {
	*m = WeddingHallItem_WeddingHallItemGoingView{}
}
func (m *WeddingHallItem_WeddingHallItemGoingView) String() string { return proto.CompactTextString(m) }
func (*WeddingHallItem_WeddingHallItemGoingView) ProtoMessage()    {}
func (*WeddingHallItem_WeddingHallItemGoingView) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{111, 0}
}
func (m *WeddingHallItem_WeddingHallItemGoingView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingHallItem_WeddingHallItemGoingView.Unmarshal(m, b)
}
func (m *WeddingHallItem_WeddingHallItemGoingView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingHallItem_WeddingHallItemGoingView.Marshal(b, m, deterministic)
}
func (dst *WeddingHallItem_WeddingHallItemGoingView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingHallItem_WeddingHallItemGoingView.Merge(dst, src)
}
func (m *WeddingHallItem_WeddingHallItemGoingView) XXX_Size() int {
	return xxx_messageInfo_WeddingHallItem_WeddingHallItemGoingView.Size(m)
}
func (m *WeddingHallItem_WeddingHallItemGoingView) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingHallItem_WeddingHallItemGoingView.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingHallItem_WeddingHallItemGoingView proto.InternalMessageInfo

func (m *WeddingHallItem_WeddingHallItemGoingView) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *WeddingHallItem_WeddingHallItemGoingView) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *WeddingHallItem_WeddingHallItemGoingView) GetChannelStageDesc() string {
	if m != nil {
		return m.ChannelStageDesc
	}
	return ""
}

func (m *WeddingHallItem_WeddingHallItemGoingView) GetChannelHotValue() int64 {
	if m != nil {
		return m.ChannelHotValue
	}
	return 0
}

func (m *WeddingHallItem_WeddingHallItemGoingView) GetChannelIcon() string {
	if m != nil {
		return m.ChannelIcon
	}
	return ""
}

type WeddingHallItem_WeddingHallItemNotStartView struct {
	StartTime            string                 `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	SubscribeStatus      WeddingSubscribeStatus `protobuf:"varint,2,opt,name=subscribe_status,json=subscribeStatus,proto3,enum=ga.channel_wedding_logic.WeddingSubscribeStatus" json:"subscribe_status,omitempty"`
	ChannelId            uint32                 `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string                 `protobuf:"bytes,4,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	WeddingStartTime     int64                  `protobuf:"varint,5,opt,name=wedding_start_time,json=weddingStartTime,proto3" json:"wedding_start_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *WeddingHallItem_WeddingHallItemNotStartView) Reset() {
	*m = WeddingHallItem_WeddingHallItemNotStartView{}
}
func (m *WeddingHallItem_WeddingHallItemNotStartView) String() string {
	return proto.CompactTextString(m)
}
func (*WeddingHallItem_WeddingHallItemNotStartView) ProtoMessage() {}
func (*WeddingHallItem_WeddingHallItemNotStartView) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{111, 1}
}
func (m *WeddingHallItem_WeddingHallItemNotStartView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingHallItem_WeddingHallItemNotStartView.Unmarshal(m, b)
}
func (m *WeddingHallItem_WeddingHallItemNotStartView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingHallItem_WeddingHallItemNotStartView.Marshal(b, m, deterministic)
}
func (dst *WeddingHallItem_WeddingHallItemNotStartView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingHallItem_WeddingHallItemNotStartView.Merge(dst, src)
}
func (m *WeddingHallItem_WeddingHallItemNotStartView) XXX_Size() int {
	return xxx_messageInfo_WeddingHallItem_WeddingHallItemNotStartView.Size(m)
}
func (m *WeddingHallItem_WeddingHallItemNotStartView) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingHallItem_WeddingHallItemNotStartView.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingHallItem_WeddingHallItemNotStartView proto.InternalMessageInfo

func (m *WeddingHallItem_WeddingHallItemNotStartView) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *WeddingHallItem_WeddingHallItemNotStartView) GetSubscribeStatus() WeddingSubscribeStatus {
	if m != nil {
		return m.SubscribeStatus
	}
	return WeddingSubscribeStatus_WEDDING_SUBSCRIBE_STATUS_UNSPECIFIED
}

func (m *WeddingHallItem_WeddingHallItemNotStartView) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *WeddingHallItem_WeddingHallItemNotStartView) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *WeddingHallItem_WeddingHallItemNotStartView) GetWeddingStartTime() int64 {
	if m != nil {
		return m.WeddingStartTime
	}
	return 0
}

type GetWeddingHallListRequest struct {
	BaseReq              *app.BaseReq      `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChargeType           WeddingChargeType `protobuf:"varint,2,opt,name=charge_type,json=chargeType,proto3,enum=ga.channel_wedding_logic.WeddingChargeType" json:"charge_type,omitempty"`
	TimeStatus           WeddingTimeStatus `protobuf:"varint,3,opt,name=time_status,json=timeStatus,proto3,enum=ga.channel_wedding_logic.WeddingTimeStatus" json:"time_status,omitempty"`
	LoadMore             string            `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetWeddingHallListRequest) Reset()         { *m = GetWeddingHallListRequest{} }
func (m *GetWeddingHallListRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingHallListRequest) ProtoMessage()    {}
func (*GetWeddingHallListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{112}
}
func (m *GetWeddingHallListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingHallListRequest.Unmarshal(m, b)
}
func (m *GetWeddingHallListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingHallListRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingHallListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingHallListRequest.Merge(dst, src)
}
func (m *GetWeddingHallListRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingHallListRequest.Size(m)
}
func (m *GetWeddingHallListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingHallListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingHallListRequest proto.InternalMessageInfo

func (m *GetWeddingHallListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetWeddingHallListRequest) GetChargeType() WeddingChargeType {
	if m != nil {
		return m.ChargeType
	}
	return WeddingChargeType_WEDDING_CHARGE_TYPE_UNSPECIFIED
}

func (m *GetWeddingHallListRequest) GetTimeStatus() WeddingTimeStatus {
	if m != nil {
		return m.TimeStatus
	}
	return WeddingTimeStatus_WEDDING_TIME_STATUS_UNSPECIFIED
}

func (m *GetWeddingHallListRequest) GetLoadMore() string {
	if m != nil {
		return m.LoadMore
	}
	return ""
}

type GetWeddingHallListResponse struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	WeddingList          []*WeddingHallItem `protobuf:"bytes,2,rep,name=wedding_list,json=weddingList,proto3" json:"wedding_list,omitempty"`
	LoadMore             string             `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetWeddingHallListResponse) Reset()         { *m = GetWeddingHallListResponse{} }
func (m *GetWeddingHallListResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingHallListResponse) ProtoMessage()    {}
func (*GetWeddingHallListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{113}
}
func (m *GetWeddingHallListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingHallListResponse.Unmarshal(m, b)
}
func (m *GetWeddingHallListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingHallListResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingHallListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingHallListResponse.Merge(dst, src)
}
func (m *GetWeddingHallListResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingHallListResponse.Size(m)
}
func (m *GetWeddingHallListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingHallListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingHallListResponse proto.InternalMessageInfo

func (m *GetWeddingHallListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWeddingHallListResponse) GetWeddingList() []*WeddingHallItem {
	if m != nil {
		return m.WeddingList
	}
	return nil
}

func (m *GetWeddingHallListResponse) GetLoadMore() string {
	if m != nil {
		return m.LoadMore
	}
	return ""
}

type SubscribeWeddingRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	WeddingPlanId        uint32       `protobuf:"varint,2,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SubscribeWeddingRequest) Reset()         { *m = SubscribeWeddingRequest{} }
func (m *SubscribeWeddingRequest) String() string { return proto.CompactTextString(m) }
func (*SubscribeWeddingRequest) ProtoMessage()    {}
func (*SubscribeWeddingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{114}
}
func (m *SubscribeWeddingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeWeddingRequest.Unmarshal(m, b)
}
func (m *SubscribeWeddingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeWeddingRequest.Marshal(b, m, deterministic)
}
func (dst *SubscribeWeddingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeWeddingRequest.Merge(dst, src)
}
func (m *SubscribeWeddingRequest) XXX_Size() int {
	return xxx_messageInfo_SubscribeWeddingRequest.Size(m)
}
func (m *SubscribeWeddingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeWeddingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeWeddingRequest proto.InternalMessageInfo

func (m *SubscribeWeddingRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SubscribeWeddingRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type SubscribeWeddingResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SubscribeWeddingResponse) Reset()         { *m = SubscribeWeddingResponse{} }
func (m *SubscribeWeddingResponse) String() string { return proto.CompactTextString(m) }
func (*SubscribeWeddingResponse) ProtoMessage()    {}
func (*SubscribeWeddingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{115}
}
func (m *SubscribeWeddingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeWeddingResponse.Unmarshal(m, b)
}
func (m *SubscribeWeddingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeWeddingResponse.Marshal(b, m, deterministic)
}
func (dst *SubscribeWeddingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeWeddingResponse.Merge(dst, src)
}
func (m *SubscribeWeddingResponse) XXX_Size() int {
	return xxx_messageInfo_SubscribeWeddingResponse.Size(m)
}
func (m *SubscribeWeddingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeWeddingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeWeddingResponse proto.InternalMessageInfo

func (m *SubscribeWeddingResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetWeddingEntrySwitchRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWeddingEntrySwitchRequest) Reset()         { *m = GetWeddingEntrySwitchRequest{} }
func (m *GetWeddingEntrySwitchRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingEntrySwitchRequest) ProtoMessage()    {}
func (*GetWeddingEntrySwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{116}
}
func (m *GetWeddingEntrySwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingEntrySwitchRequest.Unmarshal(m, b)
}
func (m *GetWeddingEntrySwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingEntrySwitchRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingEntrySwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingEntrySwitchRequest.Merge(dst, src)
}
func (m *GetWeddingEntrySwitchRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingEntrySwitchRequest.Size(m)
}
func (m *GetWeddingEntrySwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingEntrySwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingEntrySwitchRequest proto.InternalMessageInfo

func (m *GetWeddingEntrySwitchRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetWeddingEntrySwitchRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetWeddingEntrySwitchResponse struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// 开关
	ShowWeddingTab               bool `protobuf:"varint,2,opt,name=show_wedding_tab,json=showWeddingTab,proto3" json:"show_wedding_tab,omitempty"`
	ShowWeddingHallEntry         bool `protobuf:"varint,3,opt,name=show_wedding_hall_entry,json=showWeddingHallEntry,proto3" json:"show_wedding_hall_entry,omitempty"`
	ShowWeddingHallFloatingEntry bool `protobuf:"varint,4,opt,name=show_wedding_hall_floating_entry,json=showWeddingHallFloatingEntry,proto3" json:"show_wedding_hall_floating_entry,omitempty"`
	// 仪式大厅入口
	WeddingHallEntryBackground string `protobuf:"bytes,5,opt,name=wedding_hall_entry_background,json=weddingHallEntryBackground,proto3" json:"wedding_hall_entry_background,omitempty"`
	WeddingHallEntryLottie     string `protobuf:"bytes,6,opt,name=wedding_hall_entry_lottie,json=weddingHallEntryLottie,proto3" json:"wedding_hall_entry_lottie,omitempty"`
	WeddingHallEntryLottieMd5  string `protobuf:"bytes,7,opt,name=wedding_hall_entry_lottie_md5,json=weddingHallEntryLottieMd5,proto3" json:"wedding_hall_entry_lottie_md5,omitempty"`
	// 预约入口
	WeddingReserveEntryBackground string `protobuf:"bytes,8,opt,name=wedding_reserve_entry_background,json=weddingReserveEntryBackground,proto3" json:"wedding_reserve_entry_background,omitempty"`
	WeddingReserveEntryLottie     string `protobuf:"bytes,9,opt,name=wedding_reserve_entry_lottie,json=weddingReserveEntryLottie,proto3" json:"wedding_reserve_entry_lottie,omitempty"`
	WeddingReserveEntryLottieMd5  string `protobuf:"bytes,10,opt,name=wedding_reserve_entry_lottie_md5,json=weddingReserveEntryLottieMd5,proto3" json:"wedding_reserve_entry_lottie_md5,omitempty"`
	// 仪式大厅
	WeddingHallBackground string `protobuf:"bytes,11,opt,name=wedding_hall_background,json=weddingHallBackground,proto3" json:"wedding_hall_background,omitempty"`
	// 仪式大厅内的预约入口
	WeddingHallReserveEntryBackground string   `protobuf:"bytes,12,opt,name=wedding_hall_reserve_entry_background,json=weddingHallReserveEntryBackground,proto3" json:"wedding_hall_reserve_entry_background,omitempty"`
	WeddingHallReserveEntryHint       string   `protobuf:"bytes,13,opt,name=wedding_hall_reserve_entry_hint,json=weddingHallReserveEntryHint,proto3" json:"wedding_hall_reserve_entry_hint,omitempty"`
	XXX_NoUnkeyedLiteral              struct{} `json:"-"`
	XXX_unrecognized                  []byte   `json:"-"`
	XXX_sizecache                     int32    `json:"-"`
}

func (m *GetWeddingEntrySwitchResponse) Reset()         { *m = GetWeddingEntrySwitchResponse{} }
func (m *GetWeddingEntrySwitchResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingEntrySwitchResponse) ProtoMessage()    {}
func (*GetWeddingEntrySwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{117}
}
func (m *GetWeddingEntrySwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingEntrySwitchResponse.Unmarshal(m, b)
}
func (m *GetWeddingEntrySwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingEntrySwitchResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingEntrySwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingEntrySwitchResponse.Merge(dst, src)
}
func (m *GetWeddingEntrySwitchResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingEntrySwitchResponse.Size(m)
}
func (m *GetWeddingEntrySwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingEntrySwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingEntrySwitchResponse proto.InternalMessageInfo

func (m *GetWeddingEntrySwitchResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWeddingEntrySwitchResponse) GetShowWeddingTab() bool {
	if m != nil {
		return m.ShowWeddingTab
	}
	return false
}

func (m *GetWeddingEntrySwitchResponse) GetShowWeddingHallEntry() bool {
	if m != nil {
		return m.ShowWeddingHallEntry
	}
	return false
}

func (m *GetWeddingEntrySwitchResponse) GetShowWeddingHallFloatingEntry() bool {
	if m != nil {
		return m.ShowWeddingHallFloatingEntry
	}
	return false
}

func (m *GetWeddingEntrySwitchResponse) GetWeddingHallEntryBackground() string {
	if m != nil {
		return m.WeddingHallEntryBackground
	}
	return ""
}

func (m *GetWeddingEntrySwitchResponse) GetWeddingHallEntryLottie() string {
	if m != nil {
		return m.WeddingHallEntryLottie
	}
	return ""
}

func (m *GetWeddingEntrySwitchResponse) GetWeddingHallEntryLottieMd5() string {
	if m != nil {
		return m.WeddingHallEntryLottieMd5
	}
	return ""
}

func (m *GetWeddingEntrySwitchResponse) GetWeddingReserveEntryBackground() string {
	if m != nil {
		return m.WeddingReserveEntryBackground
	}
	return ""
}

func (m *GetWeddingEntrySwitchResponse) GetWeddingReserveEntryLottie() string {
	if m != nil {
		return m.WeddingReserveEntryLottie
	}
	return ""
}

func (m *GetWeddingEntrySwitchResponse) GetWeddingReserveEntryLottieMd5() string {
	if m != nil {
		return m.WeddingReserveEntryLottieMd5
	}
	return ""
}

func (m *GetWeddingEntrySwitchResponse) GetWeddingHallBackground() string {
	if m != nil {
		return m.WeddingHallBackground
	}
	return ""
}

func (m *GetWeddingEntrySwitchResponse) GetWeddingHallReserveEntryBackground() string {
	if m != nil {
		return m.WeddingHallReserveEntryBackground
	}
	return ""
}

func (m *GetWeddingEntrySwitchResponse) GetWeddingHallReserveEntryHint() string {
	if m != nil {
		return m.WeddingHallReserveEntryHint
	}
	return ""
}

type ApplyEndWeddingRelationshipRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PartnerUid           uint32       `protobuf:"varint,2,opt,name=partner_uid,json=partnerUid,proto3" json:"partner_uid,omitempty"`
	Source               uint32       `protobuf:"varint,3,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ApplyEndWeddingRelationshipRequest) Reset()         { *m = ApplyEndWeddingRelationshipRequest{} }
func (m *ApplyEndWeddingRelationshipRequest) String() string { return proto.CompactTextString(m) }
func (*ApplyEndWeddingRelationshipRequest) ProtoMessage()    {}
func (*ApplyEndWeddingRelationshipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{118}
}
func (m *ApplyEndWeddingRelationshipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyEndWeddingRelationshipRequest.Unmarshal(m, b)
}
func (m *ApplyEndWeddingRelationshipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyEndWeddingRelationshipRequest.Marshal(b, m, deterministic)
}
func (dst *ApplyEndWeddingRelationshipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyEndWeddingRelationshipRequest.Merge(dst, src)
}
func (m *ApplyEndWeddingRelationshipRequest) XXX_Size() int {
	return xxx_messageInfo_ApplyEndWeddingRelationshipRequest.Size(m)
}
func (m *ApplyEndWeddingRelationshipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyEndWeddingRelationshipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyEndWeddingRelationshipRequest proto.InternalMessageInfo

func (m *ApplyEndWeddingRelationshipRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ApplyEndWeddingRelationshipRequest) GetPartnerUid() uint32 {
	if m != nil {
		return m.PartnerUid
	}
	return 0
}

func (m *ApplyEndWeddingRelationshipRequest) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type ApplyEndWeddingRelationshipResponse struct {
	BaseResp                *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	EndRelationshipDeadline int64         `protobuf:"varint,2,opt,name=end_relationship_deadline,json=endRelationshipDeadline,proto3" json:"end_relationship_deadline,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}      `json:"-"`
	XXX_unrecognized        []byte        `json:"-"`
	XXX_sizecache           int32         `json:"-"`
}

func (m *ApplyEndWeddingRelationshipResponse) Reset()         { *m = ApplyEndWeddingRelationshipResponse{} }
func (m *ApplyEndWeddingRelationshipResponse) String() string { return proto.CompactTextString(m) }
func (*ApplyEndWeddingRelationshipResponse) ProtoMessage()    {}
func (*ApplyEndWeddingRelationshipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{119}
}
func (m *ApplyEndWeddingRelationshipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyEndWeddingRelationshipResponse.Unmarshal(m, b)
}
func (m *ApplyEndWeddingRelationshipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyEndWeddingRelationshipResponse.Marshal(b, m, deterministic)
}
func (dst *ApplyEndWeddingRelationshipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyEndWeddingRelationshipResponse.Merge(dst, src)
}
func (m *ApplyEndWeddingRelationshipResponse) XXX_Size() int {
	return xxx_messageInfo_ApplyEndWeddingRelationshipResponse.Size(m)
}
func (m *ApplyEndWeddingRelationshipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyEndWeddingRelationshipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyEndWeddingRelationshipResponse proto.InternalMessageInfo

func (m *ApplyEndWeddingRelationshipResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ApplyEndWeddingRelationshipResponse) GetEndRelationshipDeadline() int64 {
	if m != nil {
		return m.EndRelationshipDeadline
	}
	return 0
}

type DirectEndWeddingRelationshipRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PartnerUid           uint32       `protobuf:"varint,2,opt,name=partner_uid,json=partnerUid,proto3" json:"partner_uid,omitempty"`
	Source               uint32       `protobuf:"varint,3,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DirectEndWeddingRelationshipRequest) Reset()         { *m = DirectEndWeddingRelationshipRequest{} }
func (m *DirectEndWeddingRelationshipRequest) String() string { return proto.CompactTextString(m) }
func (*DirectEndWeddingRelationshipRequest) ProtoMessage()    {}
func (*DirectEndWeddingRelationshipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{120}
}
func (m *DirectEndWeddingRelationshipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DirectEndWeddingRelationshipRequest.Unmarshal(m, b)
}
func (m *DirectEndWeddingRelationshipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DirectEndWeddingRelationshipRequest.Marshal(b, m, deterministic)
}
func (dst *DirectEndWeddingRelationshipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DirectEndWeddingRelationshipRequest.Merge(dst, src)
}
func (m *DirectEndWeddingRelationshipRequest) XXX_Size() int {
	return xxx_messageInfo_DirectEndWeddingRelationshipRequest.Size(m)
}
func (m *DirectEndWeddingRelationshipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DirectEndWeddingRelationshipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DirectEndWeddingRelationshipRequest proto.InternalMessageInfo

func (m *DirectEndWeddingRelationshipRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DirectEndWeddingRelationshipRequest) GetPartnerUid() uint32 {
	if m != nil {
		return m.PartnerUid
	}
	return 0
}

func (m *DirectEndWeddingRelationshipRequest) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type DirectEndWeddingRelationshipResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DirectEndWeddingRelationshipResponse) Reset()         { *m = DirectEndWeddingRelationshipResponse{} }
func (m *DirectEndWeddingRelationshipResponse) String() string { return proto.CompactTextString(m) }
func (*DirectEndWeddingRelationshipResponse) ProtoMessage()    {}
func (*DirectEndWeddingRelationshipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{121}
}
func (m *DirectEndWeddingRelationshipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DirectEndWeddingRelationshipResponse.Unmarshal(m, b)
}
func (m *DirectEndWeddingRelationshipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DirectEndWeddingRelationshipResponse.Marshal(b, m, deterministic)
}
func (dst *DirectEndWeddingRelationshipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DirectEndWeddingRelationshipResponse.Merge(dst, src)
}
func (m *DirectEndWeddingRelationshipResponse) XXX_Size() int {
	return xxx_messageInfo_DirectEndWeddingRelationshipResponse.Size(m)
}
func (m *DirectEndWeddingRelationshipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DirectEndWeddingRelationshipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DirectEndWeddingRelationshipResponse proto.InternalMessageInfo

func (m *DirectEndWeddingRelationshipResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type CancelEndWeddingRelationshipRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PartnerUid           uint32       `protobuf:"varint,2,opt,name=partner_uid,json=partnerUid,proto3" json:"partner_uid,omitempty"`
	Source               uint32       `protobuf:"varint,3,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CancelEndWeddingRelationshipRequest) Reset()         { *m = CancelEndWeddingRelationshipRequest{} }
func (m *CancelEndWeddingRelationshipRequest) String() string { return proto.CompactTextString(m) }
func (*CancelEndWeddingRelationshipRequest) ProtoMessage()    {}
func (*CancelEndWeddingRelationshipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{122}
}
func (m *CancelEndWeddingRelationshipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelEndWeddingRelationshipRequest.Unmarshal(m, b)
}
func (m *CancelEndWeddingRelationshipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelEndWeddingRelationshipRequest.Marshal(b, m, deterministic)
}
func (dst *CancelEndWeddingRelationshipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelEndWeddingRelationshipRequest.Merge(dst, src)
}
func (m *CancelEndWeddingRelationshipRequest) XXX_Size() int {
	return xxx_messageInfo_CancelEndWeddingRelationshipRequest.Size(m)
}
func (m *CancelEndWeddingRelationshipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelEndWeddingRelationshipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CancelEndWeddingRelationshipRequest proto.InternalMessageInfo

func (m *CancelEndWeddingRelationshipRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CancelEndWeddingRelationshipRequest) GetPartnerUid() uint32 {
	if m != nil {
		return m.PartnerUid
	}
	return 0
}

func (m *CancelEndWeddingRelationshipRequest) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type CancelEndWeddingRelationshipResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CancelEndWeddingRelationshipResponse) Reset()         { *m = CancelEndWeddingRelationshipResponse{} }
func (m *CancelEndWeddingRelationshipResponse) String() string { return proto.CompactTextString(m) }
func (*CancelEndWeddingRelationshipResponse) ProtoMessage()    {}
func (*CancelEndWeddingRelationshipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{123}
}
func (m *CancelEndWeddingRelationshipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelEndWeddingRelationshipResponse.Unmarshal(m, b)
}
func (m *CancelEndWeddingRelationshipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelEndWeddingRelationshipResponse.Marshal(b, m, deterministic)
}
func (dst *CancelEndWeddingRelationshipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelEndWeddingRelationshipResponse.Merge(dst, src)
}
func (m *CancelEndWeddingRelationshipResponse) XXX_Size() int {
	return xxx_messageInfo_CancelEndWeddingRelationshipResponse.Size(m)
}
func (m *CancelEndWeddingRelationshipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelEndWeddingRelationshipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CancelEndWeddingRelationshipResponse proto.InternalMessageInfo

func (m *CancelEndWeddingRelationshipResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取求婚列表请求
type GetProposeListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Account              string       `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetProposeListRequest) Reset()         { *m = GetProposeListRequest{} }
func (m *GetProposeListRequest) String() string { return proto.CompactTextString(m) }
func (*GetProposeListRequest) ProtoMessage()    {}
func (*GetProposeListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{124}
}
func (m *GetProposeListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProposeListRequest.Unmarshal(m, b)
}
func (m *GetProposeListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProposeListRequest.Marshal(b, m, deterministic)
}
func (dst *GetProposeListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProposeListRequest.Merge(dst, src)
}
func (m *GetProposeListRequest) XXX_Size() int {
	return xxx_messageInfo_GetProposeListRequest.Size(m)
}
func (m *GetProposeListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProposeListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetProposeListRequest proto.InternalMessageInfo

func (m *GetProposeListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetProposeListRequest) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

// 求婚对象信息
type ProposeUser struct {
	User                 *app.UserProfile `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Status               uint32           `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	FellowPoint          uint32           `protobuf:"varint,3,opt,name=fellow_point,json=fellowPoint,proto3" json:"fellow_point,omitempty"`
	ProposeId            string           `protobuf:"bytes,4,opt,name=propose_id,json=proposeId,proto3" json:"propose_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ProposeUser) Reset()         { *m = ProposeUser{} }
func (m *ProposeUser) String() string { return proto.CompactTextString(m) }
func (*ProposeUser) ProtoMessage()    {}
func (*ProposeUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{125}
}
func (m *ProposeUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProposeUser.Unmarshal(m, b)
}
func (m *ProposeUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProposeUser.Marshal(b, m, deterministic)
}
func (dst *ProposeUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProposeUser.Merge(dst, src)
}
func (m *ProposeUser) XXX_Size() int {
	return xxx_messageInfo_ProposeUser.Size(m)
}
func (m *ProposeUser) XXX_DiscardUnknown() {
	xxx_messageInfo_ProposeUser.DiscardUnknown(m)
}

var xxx_messageInfo_ProposeUser proto.InternalMessageInfo

func (m *ProposeUser) GetUser() *app.UserProfile {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *ProposeUser) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ProposeUser) GetFellowPoint() uint32 {
	if m != nil {
		return m.FellowPoint
	}
	return 0
}

func (m *ProposeUser) GetProposeId() string {
	if m != nil {
		return m.ProposeId
	}
	return ""
}

// 获取求婚列表响应
type GetProposeListResponse struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ProposeList          []*ProposeUser `protobuf:"bytes,2,rep,name=propose_list,json=proposeList,proto3" json:"propose_list,omitempty"`
	Tips                 []string       `protobuf:"bytes,4,rep,name=tips,proto3" json:"tips,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetProposeListResponse) Reset()         { *m = GetProposeListResponse{} }
func (m *GetProposeListResponse) String() string { return proto.CompactTextString(m) }
func (*GetProposeListResponse) ProtoMessage()    {}
func (*GetProposeListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{126}
}
func (m *GetProposeListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProposeListResponse.Unmarshal(m, b)
}
func (m *GetProposeListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProposeListResponse.Marshal(b, m, deterministic)
}
func (dst *GetProposeListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProposeListResponse.Merge(dst, src)
}
func (m *GetProposeListResponse) XXX_Size() int {
	return xxx_messageInfo_GetProposeListResponse.Size(m)
}
func (m *GetProposeListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProposeListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetProposeListResponse proto.InternalMessageInfo

func (m *GetProposeListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetProposeListResponse) GetProposeList() []*ProposeUser {
	if m != nil {
		return m.ProposeList
	}
	return nil
}

func (m *GetProposeListResponse) GetTips() []string {
	if m != nil {
		return m.Tips
	}
	return nil
}

type SendProposeRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Tips                 string       `protobuf:"bytes,3,opt,name=tips,proto3" json:"tips,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SendProposeRequest) Reset()         { *m = SendProposeRequest{} }
func (m *SendProposeRequest) String() string { return proto.CompactTextString(m) }
func (*SendProposeRequest) ProtoMessage()    {}
func (*SendProposeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{127}
}
func (m *SendProposeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendProposeRequest.Unmarshal(m, b)
}
func (m *SendProposeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendProposeRequest.Marshal(b, m, deterministic)
}
func (dst *SendProposeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendProposeRequest.Merge(dst, src)
}
func (m *SendProposeRequest) XXX_Size() int {
	return xxx_messageInfo_SendProposeRequest.Size(m)
}
func (m *SendProposeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendProposeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendProposeRequest proto.InternalMessageInfo

func (m *SendProposeRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendProposeRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SendProposeRequest) GetTips() string {
	if m != nil {
		return m.Tips
	}
	return ""
}

type SendProposeResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SendProposeResponse) Reset()         { *m = SendProposeResponse{} }
func (m *SendProposeResponse) String() string { return proto.CompactTextString(m) }
func (*SendProposeResponse) ProtoMessage()    {}
func (*SendProposeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{128}
}
func (m *SendProposeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendProposeResponse.Unmarshal(m, b)
}
func (m *SendProposeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendProposeResponse.Marshal(b, m, deterministic)
}
func (dst *SendProposeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendProposeResponse.Merge(dst, src)
}
func (m *SendProposeResponse) XXX_Size() int {
	return xxx_messageInfo_SendProposeResponse.Size(m)
}
func (m *SendProposeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SendProposeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SendProposeResponse proto.InternalMessageInfo

func (m *SendProposeResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 处理求婚请求
type HandleProposeRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ProposeId            string       `protobuf:"bytes,2,opt,name=propose_id,json=proposeId,proto3" json:"propose_id,omitempty"`
	IsAccept             bool         `protobuf:"varint,3,opt,name=is_accept,json=isAccept,proto3" json:"is_accept,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *HandleProposeRequest) Reset()         { *m = HandleProposeRequest{} }
func (m *HandleProposeRequest) String() string { return proto.CompactTextString(m) }
func (*HandleProposeRequest) ProtoMessage()    {}
func (*HandleProposeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{129}
}
func (m *HandleProposeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleProposeRequest.Unmarshal(m, b)
}
func (m *HandleProposeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleProposeRequest.Marshal(b, m, deterministic)
}
func (dst *HandleProposeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleProposeRequest.Merge(dst, src)
}
func (m *HandleProposeRequest) XXX_Size() int {
	return xxx_messageInfo_HandleProposeRequest.Size(m)
}
func (m *HandleProposeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleProposeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HandleProposeRequest proto.InternalMessageInfo

func (m *HandleProposeRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *HandleProposeRequest) GetProposeId() string {
	if m != nil {
		return m.ProposeId
	}
	return ""
}

func (m *HandleProposeRequest) GetIsAccept() bool {
	if m != nil {
		return m.IsAccept
	}
	return false
}

type HandleProposeResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *HandleProposeResponse) Reset()         { *m = HandleProposeResponse{} }
func (m *HandleProposeResponse) String() string { return proto.CompactTextString(m) }
func (*HandleProposeResponse) ProtoMessage()    {}
func (*HandleProposeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{130}
}
func (m *HandleProposeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleProposeResponse.Unmarshal(m, b)
}
func (m *HandleProposeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleProposeResponse.Marshal(b, m, deterministic)
}
func (dst *HandleProposeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleProposeResponse.Merge(dst, src)
}
func (m *HandleProposeResponse) XXX_Size() int {
	return xxx_messageInfo_HandleProposeResponse.Size(m)
}
func (m *HandleProposeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleProposeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HandleProposeResponse proto.InternalMessageInfo

func (m *HandleProposeResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 撤回求婚
type RevokeProposeRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RevokeProposeRequest) Reset()         { *m = RevokeProposeRequest{} }
func (m *RevokeProposeRequest) String() string { return proto.CompactTextString(m) }
func (*RevokeProposeRequest) ProtoMessage()    {}
func (*RevokeProposeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{131}
}
func (m *RevokeProposeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeProposeRequest.Unmarshal(m, b)
}
func (m *RevokeProposeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeProposeRequest.Marshal(b, m, deterministic)
}
func (dst *RevokeProposeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeProposeRequest.Merge(dst, src)
}
func (m *RevokeProposeRequest) XXX_Size() int {
	return xxx_messageInfo_RevokeProposeRequest.Size(m)
}
func (m *RevokeProposeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeProposeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeProposeRequest proto.InternalMessageInfo

func (m *RevokeProposeRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type RevokeProposeResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *RevokeProposeResponse) Reset()         { *m = RevokeProposeResponse{} }
func (m *RevokeProposeResponse) String() string { return proto.CompactTextString(m) }
func (*RevokeProposeResponse) ProtoMessage()    {}
func (*RevokeProposeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{132}
}
func (m *RevokeProposeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeProposeResponse.Unmarshal(m, b)
}
func (m *RevokeProposeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeProposeResponse.Marshal(b, m, deterministic)
}
func (dst *RevokeProposeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeProposeResponse.Merge(dst, src)
}
func (m *RevokeProposeResponse) XXX_Size() int {
	return xxx_messageInfo_RevokeProposeResponse.Size(m)
}
func (m *RevokeProposeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeProposeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeProposeResponse proto.InternalMessageInfo

func (m *RevokeProposeResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 求婚函信息
type WeddingProposeInfo struct {
	ProposeId            string           `protobuf:"bytes,1,opt,name=propose_id,json=proposeId,proto3" json:"propose_id,omitempty"`
	FromUser             *app.UserProfile `protobuf:"bytes,2,opt,name=from_user,json=fromUser,proto3" json:"from_user,omitempty"`
	TargetUser           *app.UserProfile `protobuf:"bytes,3,opt,name=target_user,json=targetUser,proto3" json:"target_user,omitempty"`
	Status               uint32           `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	CreateTime           uint32           `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	EndTime              uint32           `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ExpireDay            uint32           `protobuf:"varint,7,opt,name=expire_day,json=expireDay,proto3" json:"expire_day,omitempty"`
	Tips                 string           `protobuf:"bytes,8,opt,name=tips,proto3" json:"tips,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WeddingProposeInfo) Reset()         { *m = WeddingProposeInfo{} }
func (m *WeddingProposeInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingProposeInfo) ProtoMessage()    {}
func (*WeddingProposeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{133}
}
func (m *WeddingProposeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingProposeInfo.Unmarshal(m, b)
}
func (m *WeddingProposeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingProposeInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingProposeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingProposeInfo.Merge(dst, src)
}
func (m *WeddingProposeInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingProposeInfo.Size(m)
}
func (m *WeddingProposeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingProposeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingProposeInfo proto.InternalMessageInfo

func (m *WeddingProposeInfo) GetProposeId() string {
	if m != nil {
		return m.ProposeId
	}
	return ""
}

func (m *WeddingProposeInfo) GetFromUser() *app.UserProfile {
	if m != nil {
		return m.FromUser
	}
	return nil
}

func (m *WeddingProposeInfo) GetTargetUser() *app.UserProfile {
	if m != nil {
		return m.TargetUser
	}
	return nil
}

func (m *WeddingProposeInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *WeddingProposeInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *WeddingProposeInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *WeddingProposeInfo) GetExpireDay() uint32 {
	if m != nil {
		return m.ExpireDay
	}
	return 0
}

func (m *WeddingProposeInfo) GetTips() string {
	if m != nil {
		return m.Tips
	}
	return ""
}

type GetProposeByIdRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ProposeId            string       `protobuf:"bytes,2,opt,name=propose_id,json=proposeId,proto3" json:"propose_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetProposeByIdRequest) Reset()         { *m = GetProposeByIdRequest{} }
func (m *GetProposeByIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetProposeByIdRequest) ProtoMessage()    {}
func (*GetProposeByIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{134}
}
func (m *GetProposeByIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProposeByIdRequest.Unmarshal(m, b)
}
func (m *GetProposeByIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProposeByIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetProposeByIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProposeByIdRequest.Merge(dst, src)
}
func (m *GetProposeByIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetProposeByIdRequest.Size(m)
}
func (m *GetProposeByIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProposeByIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetProposeByIdRequest proto.InternalMessageInfo

func (m *GetProposeByIdRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetProposeByIdRequest) GetProposeId() string {
	if m != nil {
		return m.ProposeId
	}
	return ""
}

type GetProposeByIdResponse struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Propose              *WeddingProposeInfo `protobuf:"bytes,2,opt,name=propose,proto3" json:"propose,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetProposeByIdResponse) Reset()         { *m = GetProposeByIdResponse{} }
func (m *GetProposeByIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetProposeByIdResponse) ProtoMessage()    {}
func (*GetProposeByIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{135}
}
func (m *GetProposeByIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProposeByIdResponse.Unmarshal(m, b)
}
func (m *GetProposeByIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProposeByIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetProposeByIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProposeByIdResponse.Merge(dst, src)
}
func (m *GetProposeByIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetProposeByIdResponse.Size(m)
}
func (m *GetProposeByIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProposeByIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetProposeByIdResponse proto.InternalMessageInfo

func (m *GetProposeByIdResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetProposeByIdResponse) GetPropose() *WeddingProposeInfo {
	if m != nil {
		return m.Propose
	}
	return nil
}

// 我发出的求婚函
type GetSendProposeRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSendProposeRequest) Reset()         { *m = GetSendProposeRequest{} }
func (m *GetSendProposeRequest) String() string { return proto.CompactTextString(m) }
func (*GetSendProposeRequest) ProtoMessage()    {}
func (*GetSendProposeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{136}
}
func (m *GetSendProposeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSendProposeRequest.Unmarshal(m, b)
}
func (m *GetSendProposeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSendProposeRequest.Marshal(b, m, deterministic)
}
func (dst *GetSendProposeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSendProposeRequest.Merge(dst, src)
}
func (m *GetSendProposeRequest) XXX_Size() int {
	return xxx_messageInfo_GetSendProposeRequest.Size(m)
}
func (m *GetSendProposeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSendProposeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSendProposeRequest proto.InternalMessageInfo

func (m *GetSendProposeRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetSendProposeResponse struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Propose              *WeddingProposeInfo `protobuf:"bytes,2,opt,name=propose,proto3" json:"propose,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetSendProposeResponse) Reset()         { *m = GetSendProposeResponse{} }
func (m *GetSendProposeResponse) String() string { return proto.CompactTextString(m) }
func (*GetSendProposeResponse) ProtoMessage()    {}
func (*GetSendProposeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{137}
}
func (m *GetSendProposeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSendProposeResponse.Unmarshal(m, b)
}
func (m *GetSendProposeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSendProposeResponse.Marshal(b, m, deterministic)
}
func (dst *GetSendProposeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSendProposeResponse.Merge(dst, src)
}
func (m *GetSendProposeResponse) XXX_Size() int {
	return xxx_messageInfo_GetSendProposeResponse.Size(m)
}
func (m *GetSendProposeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSendProposeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSendProposeResponse proto.InternalMessageInfo

func (m *GetSendProposeResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSendProposeResponse) GetPropose() *WeddingProposeInfo {
	if m != nil {
		return m.Propose
	}
	return nil
}

// 结婚证
type WeddingCertificate struct {
	WeddingId            uint32           `protobuf:"varint,1,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	Groom                *app.UserProfile `protobuf:"bytes,2,opt,name=groom,proto3" json:"groom,omitempty"`
	Bride                *app.UserProfile `protobuf:"bytes,3,opt,name=bride,proto3" json:"bride,omitempty"`
	WeddingTime          int64            `protobuf:"varint,4,opt,name=wedding_time,json=weddingTime,proto3" json:"wedding_time,omitempty"`
	WeddingThemeId       uint32           `protobuf:"varint,5,opt,name=wedding_theme_id,json=weddingThemeId,proto3" json:"wedding_theme_id,omitempty"`
	PicUrl               string           `protobuf:"bytes,6,opt,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WeddingCertificate) Reset()         { *m = WeddingCertificate{} }
func (m *WeddingCertificate) String() string { return proto.CompactTextString(m) }
func (*WeddingCertificate) ProtoMessage()    {}
func (*WeddingCertificate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{138}
}
func (m *WeddingCertificate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingCertificate.Unmarshal(m, b)
}
func (m *WeddingCertificate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingCertificate.Marshal(b, m, deterministic)
}
func (dst *WeddingCertificate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingCertificate.Merge(dst, src)
}
func (m *WeddingCertificate) XXX_Size() int {
	return xxx_messageInfo_WeddingCertificate.Size(m)
}
func (m *WeddingCertificate) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingCertificate.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingCertificate proto.InternalMessageInfo

func (m *WeddingCertificate) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *WeddingCertificate) GetGroom() *app.UserProfile {
	if m != nil {
		return m.Groom
	}
	return nil
}

func (m *WeddingCertificate) GetBride() *app.UserProfile {
	if m != nil {
		return m.Bride
	}
	return nil
}

func (m *WeddingCertificate) GetWeddingTime() int64 {
	if m != nil {
		return m.WeddingTime
	}
	return 0
}

func (m *WeddingCertificate) GetWeddingThemeId() uint32 {
	if m != nil {
		return m.WeddingThemeId
	}
	return 0
}

func (m *WeddingCertificate) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

type WeddingScenePic struct {
	Scene                uint32   `protobuf:"varint,1,opt,name=scene,proto3" json:"scene,omitempty"`
	PicUrl               string   `protobuf:"bytes,2,opt,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
	CreateTime           int64    `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	SceneIcon            string   `protobuf:"bytes,4,opt,name=scene_icon,json=sceneIcon,proto3" json:"scene_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingScenePic) Reset()         { *m = WeddingScenePic{} }
func (m *WeddingScenePic) String() string { return proto.CompactTextString(m) }
func (*WeddingScenePic) ProtoMessage()    {}
func (*WeddingScenePic) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{139}
}
func (m *WeddingScenePic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingScenePic.Unmarshal(m, b)
}
func (m *WeddingScenePic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingScenePic.Marshal(b, m, deterministic)
}
func (dst *WeddingScenePic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingScenePic.Merge(dst, src)
}
func (m *WeddingScenePic) XXX_Size() int {
	return xxx_messageInfo_WeddingScenePic.Size(m)
}
func (m *WeddingScenePic) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingScenePic.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingScenePic proto.InternalMessageInfo

func (m *WeddingScenePic) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *WeddingScenePic) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *WeddingScenePic) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *WeddingScenePic) GetSceneIcon() string {
	if m != nil {
		return m.SceneIcon
	}
	return ""
}

// 婚礼片段信息
type WeddingClipInfo struct {
	ThemeId              uint32             `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	ThemeName            string             `protobuf:"bytes,2,opt,name=theme_name,json=themeName,proto3" json:"theme_name,omitempty"`
	WeddingId            uint32             `protobuf:"varint,3,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	ScenePicList         []*WeddingScenePic `protobuf:"bytes,4,rep,name=scene_pic_list,json=scenePicList,proto3" json:"scene_pic_list,omitempty"`
	ChannelName          string             `protobuf:"bytes,5,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *WeddingClipInfo) Reset()         { *m = WeddingClipInfo{} }
func (m *WeddingClipInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingClipInfo) ProtoMessage()    {}
func (*WeddingClipInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{140}
}
func (m *WeddingClipInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingClipInfo.Unmarshal(m, b)
}
func (m *WeddingClipInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingClipInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingClipInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingClipInfo.Merge(dst, src)
}
func (m *WeddingClipInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingClipInfo.Size(m)
}
func (m *WeddingClipInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingClipInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingClipInfo proto.InternalMessageInfo

func (m *WeddingClipInfo) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *WeddingClipInfo) GetThemeName() string {
	if m != nil {
		return m.ThemeName
	}
	return ""
}

func (m *WeddingClipInfo) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *WeddingClipInfo) GetScenePicList() []*WeddingScenePic {
	if m != nil {
		return m.ScenePicList
	}
	return nil
}

func (m *WeddingClipInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

// 获取用户婚礼沉淀信息请求
type GetUserWeddingPrecipitationRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserWeddingPrecipitationRequest) Reset()         { *m = GetUserWeddingPrecipitationRequest{} }
func (m *GetUserWeddingPrecipitationRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserWeddingPrecipitationRequest) ProtoMessage()    {}
func (*GetUserWeddingPrecipitationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{141}
}
func (m *GetUserWeddingPrecipitationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWeddingPrecipitationRequest.Unmarshal(m, b)
}
func (m *GetUserWeddingPrecipitationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWeddingPrecipitationRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserWeddingPrecipitationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWeddingPrecipitationRequest.Merge(dst, src)
}
func (m *GetUserWeddingPrecipitationRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserWeddingPrecipitationRequest.Size(m)
}
func (m *GetUserWeddingPrecipitationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWeddingPrecipitationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWeddingPrecipitationRequest proto.InternalMessageInfo

func (m *GetUserWeddingPrecipitationRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserWeddingPrecipitationRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

// 获取我的婚礼沉淀信息响应
type GetUserWeddingPrecipitationResponse struct {
	BaseResp           *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	WeddingCertificate *WeddingCertificate `protobuf:"bytes,2,opt,name=wedding_certificate,json=weddingCertificate,proto3" json:"wedding_certificate,omitempty"`
	WeddingClipList    []*WeddingClipInfo  `protobuf:"bytes,3,rep,name=wedding_clip_list,json=weddingClipList,proto3" json:"wedding_clip_list,omitempty"`
	GroupPhotoList     []*WeddingClipInfo  `protobuf:"bytes,4,rep,name=group_photo_list,json=groupPhotoList,proto3" json:"group_photo_list,omitempty"`
	// 5-8 字段仅主态返回，隐藏状态主态也返回沉淀信息
	InDivorceFreeze      bool     `protobuf:"varint,5,opt,name=in_divorce_freeze,json=inDivorceFreeze,proto3" json:"in_divorce_freeze,omitempty"`
	DivorceFreezeEndTs   int64    `protobuf:"varint,6,opt,name=divorce_freeze_end_ts,json=divorceFreezeEndTs,proto3" json:"divorce_freeze_end_ts,omitempty"`
	HideStatus           uint32   `protobuf:"varint,7,opt,name=hide_status,json=hideStatus,proto3" json:"hide_status,omitempty"`
	DivorceFreezeDay     int64    `protobuf:"varint,8,opt,name=divorce_freeze_day,json=divorceFreezeDay,proto3" json:"divorce_freeze_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserWeddingPrecipitationResponse) Reset()         { *m = GetUserWeddingPrecipitationResponse{} }
func (m *GetUserWeddingPrecipitationResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserWeddingPrecipitationResponse) ProtoMessage()    {}
func (*GetUserWeddingPrecipitationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{142}
}
func (m *GetUserWeddingPrecipitationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWeddingPrecipitationResponse.Unmarshal(m, b)
}
func (m *GetUserWeddingPrecipitationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWeddingPrecipitationResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserWeddingPrecipitationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWeddingPrecipitationResponse.Merge(dst, src)
}
func (m *GetUserWeddingPrecipitationResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserWeddingPrecipitationResponse.Size(m)
}
func (m *GetUserWeddingPrecipitationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWeddingPrecipitationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWeddingPrecipitationResponse proto.InternalMessageInfo

func (m *GetUserWeddingPrecipitationResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserWeddingPrecipitationResponse) GetWeddingCertificate() *WeddingCertificate {
	if m != nil {
		return m.WeddingCertificate
	}
	return nil
}

func (m *GetUserWeddingPrecipitationResponse) GetWeddingClipList() []*WeddingClipInfo {
	if m != nil {
		return m.WeddingClipList
	}
	return nil
}

func (m *GetUserWeddingPrecipitationResponse) GetGroupPhotoList() []*WeddingClipInfo {
	if m != nil {
		return m.GroupPhotoList
	}
	return nil
}

func (m *GetUserWeddingPrecipitationResponse) GetInDivorceFreeze() bool {
	if m != nil {
		return m.InDivorceFreeze
	}
	return false
}

func (m *GetUserWeddingPrecipitationResponse) GetDivorceFreezeEndTs() int64 {
	if m != nil {
		return m.DivorceFreezeEndTs
	}
	return 0
}

func (m *GetUserWeddingPrecipitationResponse) GetHideStatus() uint32 {
	if m != nil {
		return m.HideStatus
	}
	return 0
}

func (m *GetUserWeddingPrecipitationResponse) GetDivorceFreezeDay() int64 {
	if m != nil {
		return m.DivorceFreezeDay
	}
	return 0
}

// 上报婚礼场景片段图片
type ReportWeddingScenePicRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	WeddingId            uint32       `protobuf:"varint,3,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	Scene                uint32       `protobuf:"varint,4,opt,name=scene,proto3" json:"scene,omitempty"`
	PicUrl               string       `protobuf:"bytes,5,opt,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
	Signature            string       `protobuf:"bytes,6,opt,name=signature,proto3" json:"signature,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ReportWeddingScenePicRequest) Reset()         { *m = ReportWeddingScenePicRequest{} }
func (m *ReportWeddingScenePicRequest) String() string { return proto.CompactTextString(m) }
func (*ReportWeddingScenePicRequest) ProtoMessage()    {}
func (*ReportWeddingScenePicRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{143}
}
func (m *ReportWeddingScenePicRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportWeddingScenePicRequest.Unmarshal(m, b)
}
func (m *ReportWeddingScenePicRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportWeddingScenePicRequest.Marshal(b, m, deterministic)
}
func (dst *ReportWeddingScenePicRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportWeddingScenePicRequest.Merge(dst, src)
}
func (m *ReportWeddingScenePicRequest) XXX_Size() int {
	return xxx_messageInfo_ReportWeddingScenePicRequest.Size(m)
}
func (m *ReportWeddingScenePicRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportWeddingScenePicRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReportWeddingScenePicRequest proto.InternalMessageInfo

func (m *ReportWeddingScenePicRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportWeddingScenePicRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ReportWeddingScenePicRequest) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *ReportWeddingScenePicRequest) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *ReportWeddingScenePicRequest) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *ReportWeddingScenePicRequest) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

type ReportWeddingScenePicResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReportWeddingScenePicResponse) Reset()         { *m = ReportWeddingScenePicResponse{} }
func (m *ReportWeddingScenePicResponse) String() string { return proto.CompactTextString(m) }
func (*ReportWeddingScenePicResponse) ProtoMessage()    {}
func (*ReportWeddingScenePicResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{144}
}
func (m *ReportWeddingScenePicResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportWeddingScenePicResponse.Unmarshal(m, b)
}
func (m *ReportWeddingScenePicResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportWeddingScenePicResponse.Marshal(b, m, deterministic)
}
func (dst *ReportWeddingScenePicResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportWeddingScenePicResponse.Merge(dst, src)
}
func (m *ReportWeddingScenePicResponse) XXX_Size() int {
	return xxx_messageInfo_ReportWeddingScenePicResponse.Size(m)
}
func (m *ReportWeddingScenePicResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportWeddingScenePicResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportWeddingScenePicResponse proto.InternalMessageInfo

func (m *ReportWeddingScenePicResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 婚礼场景片段im opt
type WeddingScenePicOpt struct {
	PicList              []string `protobuf:"bytes,1,rep,name=pic_list,json=picList,proto3" json:"pic_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingScenePicOpt) Reset()         { *m = WeddingScenePicOpt{} }
func (m *WeddingScenePicOpt) String() string { return proto.CompactTextString(m) }
func (*WeddingScenePicOpt) ProtoMessage()    {}
func (*WeddingScenePicOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{145}
}
func (m *WeddingScenePicOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingScenePicOpt.Unmarshal(m, b)
}
func (m *WeddingScenePicOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingScenePicOpt.Marshal(b, m, deterministic)
}
func (dst *WeddingScenePicOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingScenePicOpt.Merge(dst, src)
}
func (m *WeddingScenePicOpt) XXX_Size() int {
	return xxx_messageInfo_WeddingScenePicOpt.Size(m)
}
func (m *WeddingScenePicOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingScenePicOpt.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingScenePicOpt proto.InternalMessageInfo

func (m *WeddingScenePicOpt) GetPicList() []string {
	if m != nil {
		return m.PicList
	}
	return nil
}

// 手动隐藏婚礼关系
type HideWeddingRelationRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	OpType               uint32       `protobuf:"varint,2,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *HideWeddingRelationRequest) Reset()         { *m = HideWeddingRelationRequest{} }
func (m *HideWeddingRelationRequest) String() string { return proto.CompactTextString(m) }
func (*HideWeddingRelationRequest) ProtoMessage()    {}
func (*HideWeddingRelationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{146}
}
func (m *HideWeddingRelationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HideWeddingRelationRequest.Unmarshal(m, b)
}
func (m *HideWeddingRelationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HideWeddingRelationRequest.Marshal(b, m, deterministic)
}
func (dst *HideWeddingRelationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HideWeddingRelationRequest.Merge(dst, src)
}
func (m *HideWeddingRelationRequest) XXX_Size() int {
	return xxx_messageInfo_HideWeddingRelationRequest.Size(m)
}
func (m *HideWeddingRelationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HideWeddingRelationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HideWeddingRelationRequest proto.InternalMessageInfo

func (m *HideWeddingRelationRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *HideWeddingRelationRequest) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

type HideWeddingRelationResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	HideStatus           uint32        `protobuf:"varint,2,opt,name=hide_status,json=hideStatus,proto3" json:"hide_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *HideWeddingRelationResponse) Reset()         { *m = HideWeddingRelationResponse{} }
func (m *HideWeddingRelationResponse) String() string { return proto.CompactTextString(m) }
func (*HideWeddingRelationResponse) ProtoMessage()    {}
func (*HideWeddingRelationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{147}
}
func (m *HideWeddingRelationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HideWeddingRelationResponse.Unmarshal(m, b)
}
func (m *HideWeddingRelationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HideWeddingRelationResponse.Marshal(b, m, deterministic)
}
func (dst *HideWeddingRelationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HideWeddingRelationResponse.Merge(dst, src)
}
func (m *HideWeddingRelationResponse) XXX_Size() int {
	return xxx_messageInfo_HideWeddingRelationResponse.Size(m)
}
func (m *HideWeddingRelationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HideWeddingRelationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HideWeddingRelationResponse proto.InternalMessageInfo

func (m *HideWeddingRelationResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *HideWeddingRelationResponse) GetHideStatus() uint32 {
	if m != nil {
		return m.HideStatus
	}
	return 0
}

type GetWeddingPreviewResourceRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Scene                uint32       `protobuf:"varint,2,opt,name=scene,proto3" json:"scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWeddingPreviewResourceRequest) Reset()         { *m = GetWeddingPreviewResourceRequest{} }
func (m *GetWeddingPreviewResourceRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingPreviewResourceRequest) ProtoMessage()    {}
func (*GetWeddingPreviewResourceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{148}
}
func (m *GetWeddingPreviewResourceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingPreviewResourceRequest.Unmarshal(m, b)
}
func (m *GetWeddingPreviewResourceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingPreviewResourceRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingPreviewResourceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingPreviewResourceRequest.Merge(dst, src)
}
func (m *GetWeddingPreviewResourceRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingPreviewResourceRequest.Size(m)
}
func (m *GetWeddingPreviewResourceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingPreviewResourceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingPreviewResourceRequest proto.InternalMessageInfo

func (m *GetWeddingPreviewResourceRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetWeddingPreviewResourceRequest) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

type GetWeddingPreviewResourceResponse struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ThemeList            []*WeddingTheme `protobuf:"bytes,2,rep,name=theme_list,json=themeList,proto3" json:"theme_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetWeddingPreviewResourceResponse) Reset()         { *m = GetWeddingPreviewResourceResponse{} }
func (m *GetWeddingPreviewResourceResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingPreviewResourceResponse) ProtoMessage()    {}
func (*GetWeddingPreviewResourceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{149}
}
func (m *GetWeddingPreviewResourceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingPreviewResourceResponse.Unmarshal(m, b)
}
func (m *GetWeddingPreviewResourceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingPreviewResourceResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingPreviewResourceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingPreviewResourceResponse.Merge(dst, src)
}
func (m *GetWeddingPreviewResourceResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingPreviewResourceResponse.Size(m)
}
func (m *GetWeddingPreviewResourceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingPreviewResourceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingPreviewResourceResponse proto.InternalMessageInfo

func (m *GetWeddingPreviewResourceResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWeddingPreviewResourceResponse) GetThemeList() []*WeddingTheme {
	if m != nil {
		return m.ThemeList
	}
	return nil
}

type GetWeddingHighLightPresentRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	WeddingId            uint32       `protobuf:"varint,3,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWeddingHighLightPresentRequest) Reset()         { *m = GetWeddingHighLightPresentRequest{} }
func (m *GetWeddingHighLightPresentRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingHighLightPresentRequest) ProtoMessage()    {}
func (*GetWeddingHighLightPresentRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{150}
}
func (m *GetWeddingHighLightPresentRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingHighLightPresentRequest.Unmarshal(m, b)
}
func (m *GetWeddingHighLightPresentRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingHighLightPresentRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingHighLightPresentRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingHighLightPresentRequest.Merge(dst, src)
}
func (m *GetWeddingHighLightPresentRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingHighLightPresentRequest.Size(m)
}
func (m *GetWeddingHighLightPresentRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingHighLightPresentRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingHighLightPresentRequest proto.InternalMessageInfo

func (m *GetWeddingHighLightPresentRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetWeddingHighLightPresentRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetWeddingHighLightPresentRequest) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

type GetWeddingHighLightPresentResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Toast                string        `protobuf:"bytes,2,opt,name=toast,proto3" json:"toast,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetWeddingHighLightPresentResponse) Reset()         { *m = GetWeddingHighLightPresentResponse{} }
func (m *GetWeddingHighLightPresentResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingHighLightPresentResponse) ProtoMessage()    {}
func (*GetWeddingHighLightPresentResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{151}
}
func (m *GetWeddingHighLightPresentResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingHighLightPresentResponse.Unmarshal(m, b)
}
func (m *GetWeddingHighLightPresentResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingHighLightPresentResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingHighLightPresentResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingHighLightPresentResponse.Merge(dst, src)
}
func (m *GetWeddingHighLightPresentResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingHighLightPresentResponse.Size(m)
}
func (m *GetWeddingHighLightPresentResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingHighLightPresentResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingHighLightPresentResponse proto.InternalMessageInfo

func (m *GetWeddingHighLightPresentResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWeddingHighLightPresentResponse) GetToast() string {
	if m != nil {
		return m.Toast
	}
	return ""
}

// 咨询婚礼预约IM消息
type ConsultWeddingReserveIMMsg struct {
	ReserveInfo          *ReserveInfo     `protobuf:"bytes,1,opt,name=reserve_info,json=reserveInfo,proto3" json:"reserve_info,omitempty"`
	Groom                *app.UserProfile `protobuf:"bytes,2,opt,name=groom,proto3" json:"groom,omitempty"`
	Bride                *app.UserProfile `protobuf:"bytes,3,opt,name=bride,proto3" json:"bride,omitempty"`
	IsArranged           bool             `protobuf:"varint,4,opt,name=is_arranged,json=isArranged,proto3" json:"is_arranged,omitempty"`
	Status               uint32           `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	MsgId                uint32           `protobuf:"varint,6,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ConsultWeddingReserveIMMsg) Reset()         { *m = ConsultWeddingReserveIMMsg{} }
func (m *ConsultWeddingReserveIMMsg) String() string { return proto.CompactTextString(m) }
func (*ConsultWeddingReserveIMMsg) ProtoMessage()    {}
func (*ConsultWeddingReserveIMMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{152}
}
func (m *ConsultWeddingReserveIMMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsultWeddingReserveIMMsg.Unmarshal(m, b)
}
func (m *ConsultWeddingReserveIMMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsultWeddingReserveIMMsg.Marshal(b, m, deterministic)
}
func (dst *ConsultWeddingReserveIMMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsultWeddingReserveIMMsg.Merge(dst, src)
}
func (m *ConsultWeddingReserveIMMsg) XXX_Size() int {
	return xxx_messageInfo_ConsultWeddingReserveIMMsg.Size(m)
}
func (m *ConsultWeddingReserveIMMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsultWeddingReserveIMMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ConsultWeddingReserveIMMsg proto.InternalMessageInfo

func (m *ConsultWeddingReserveIMMsg) GetReserveInfo() *ReserveInfo {
	if m != nil {
		return m.ReserveInfo
	}
	return nil
}

func (m *ConsultWeddingReserveIMMsg) GetGroom() *app.UserProfile {
	if m != nil {
		return m.Groom
	}
	return nil
}

func (m *ConsultWeddingReserveIMMsg) GetBride() *app.UserProfile {
	if m != nil {
		return m.Bride
	}
	return nil
}

func (m *ConsultWeddingReserveIMMsg) GetIsArranged() bool {
	if m != nil {
		return m.IsArranged
	}
	return false
}

func (m *ConsultWeddingReserveIMMsg) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ConsultWeddingReserveIMMsg) GetMsgId() uint32 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

// 客服安排婚礼预约IM消息
type ArrangeWeddingReserveIMMsg struct {
	ReserveInfo          *ReserveInfo       `protobuf:"bytes,1,opt,name=reserve_info,json=reserveInfo,proto3" json:"reserve_info,omitempty"`
	Price                uint32             `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`
	GiftList             []*WeddingGiftInfo `protobuf:"bytes,3,rep,name=gift_list,json=giftList,proto3" json:"gift_list,omitempty"`
	IsPaid               bool               `protobuf:"varint,4,opt,name=is_paid,json=isPaid,proto3" json:"is_paid,omitempty"`
	Status               uint32             `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	MsgId                uint32             `protobuf:"varint,6,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	PayValidHour         uint32             `protobuf:"varint,7,opt,name=pay_valid_hour,json=payValidHour,proto3" json:"pay_valid_hour,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ArrangeWeddingReserveIMMsg) Reset()         { *m = ArrangeWeddingReserveIMMsg{} }
func (m *ArrangeWeddingReserveIMMsg) String() string { return proto.CompactTextString(m) }
func (*ArrangeWeddingReserveIMMsg) ProtoMessage()    {}
func (*ArrangeWeddingReserveIMMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{153}
}
func (m *ArrangeWeddingReserveIMMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArrangeWeddingReserveIMMsg.Unmarshal(m, b)
}
func (m *ArrangeWeddingReserveIMMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArrangeWeddingReserveIMMsg.Marshal(b, m, deterministic)
}
func (dst *ArrangeWeddingReserveIMMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArrangeWeddingReserveIMMsg.Merge(dst, src)
}
func (m *ArrangeWeddingReserveIMMsg) XXX_Size() int {
	return xxx_messageInfo_ArrangeWeddingReserveIMMsg.Size(m)
}
func (m *ArrangeWeddingReserveIMMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ArrangeWeddingReserveIMMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ArrangeWeddingReserveIMMsg proto.InternalMessageInfo

func (m *ArrangeWeddingReserveIMMsg) GetReserveInfo() *ReserveInfo {
	if m != nil {
		return m.ReserveInfo
	}
	return nil
}

func (m *ArrangeWeddingReserveIMMsg) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ArrangeWeddingReserveIMMsg) GetGiftList() []*WeddingGiftInfo {
	if m != nil {
		return m.GiftList
	}
	return nil
}

func (m *ArrangeWeddingReserveIMMsg) GetIsPaid() bool {
	if m != nil {
		return m.IsPaid
	}
	return false
}

func (m *ArrangeWeddingReserveIMMsg) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ArrangeWeddingReserveIMMsg) GetMsgId() uint32 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *ArrangeWeddingReserveIMMsg) GetPayValidHour() uint32 {
	if m != nil {
		return m.PayValidHour
	}
	return 0
}

type WeddingGiftInfo struct {
	GiftIcon             string   `protobuf:"bytes,1,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon,omitempty"`
	GiftName             string   `protobuf:"bytes,2,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftDesc             string   `protobuf:"bytes,3,opt,name=gift_desc,json=giftDesc,proto3" json:"gift_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingGiftInfo) Reset()         { *m = WeddingGiftInfo{} }
func (m *WeddingGiftInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingGiftInfo) ProtoMessage()    {}
func (*WeddingGiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{154}
}
func (m *WeddingGiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingGiftInfo.Unmarshal(m, b)
}
func (m *WeddingGiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingGiftInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingGiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingGiftInfo.Merge(dst, src)
}
func (m *WeddingGiftInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingGiftInfo.Size(m)
}
func (m *WeddingGiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingGiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingGiftInfo proto.InternalMessageInfo

func (m *WeddingGiftInfo) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

func (m *WeddingGiftInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *WeddingGiftInfo) GetGiftDesc() string {
	if m != nil {
		return m.GiftDesc
	}
	return ""
}

type WeddingBridesmaidUpdateOpt struct {
	BridesmaidManList    []uint32 `protobuf:"varint,1,rep,packed,name=bridesmaid_man_list,json=bridesmaidManList,proto3" json:"bridesmaid_man_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingBridesmaidUpdateOpt) Reset()         { *m = WeddingBridesmaidUpdateOpt{} }
func (m *WeddingBridesmaidUpdateOpt) String() string { return proto.CompactTextString(m) }
func (*WeddingBridesmaidUpdateOpt) ProtoMessage()    {}
func (*WeddingBridesmaidUpdateOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{155}
}
func (m *WeddingBridesmaidUpdateOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingBridesmaidUpdateOpt.Unmarshal(m, b)
}
func (m *WeddingBridesmaidUpdateOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingBridesmaidUpdateOpt.Marshal(b, m, deterministic)
}
func (dst *WeddingBridesmaidUpdateOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingBridesmaidUpdateOpt.Merge(dst, src)
}
func (m *WeddingBridesmaidUpdateOpt) XXX_Size() int {
	return xxx_messageInfo_WeddingBridesmaidUpdateOpt.Size(m)
}
func (m *WeddingBridesmaidUpdateOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingBridesmaidUpdateOpt.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingBridesmaidUpdateOpt proto.InternalMessageInfo

func (m *WeddingBridesmaidUpdateOpt) GetBridesmaidManList() []uint32 {
	if m != nil {
		return m.BridesmaidManList
	}
	return nil
}

type SendWeddingReservePresentRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	WeddingId            uint32       `protobuf:"varint,3,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SendWeddingReservePresentRequest) Reset()         { *m = SendWeddingReservePresentRequest{} }
func (m *SendWeddingReservePresentRequest) String() string { return proto.CompactTextString(m) }
func (*SendWeddingReservePresentRequest) ProtoMessage()    {}
func (*SendWeddingReservePresentRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{156}
}
func (m *SendWeddingReservePresentRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendWeddingReservePresentRequest.Unmarshal(m, b)
}
func (m *SendWeddingReservePresentRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendWeddingReservePresentRequest.Marshal(b, m, deterministic)
}
func (dst *SendWeddingReservePresentRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendWeddingReservePresentRequest.Merge(dst, src)
}
func (m *SendWeddingReservePresentRequest) XXX_Size() int {
	return xxx_messageInfo_SendWeddingReservePresentRequest.Size(m)
}
func (m *SendWeddingReservePresentRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendWeddingReservePresentRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendWeddingReservePresentRequest proto.InternalMessageInfo

func (m *SendWeddingReservePresentRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendWeddingReservePresentRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SendWeddingReservePresentRequest) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

type SendWeddingReservePresentResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SendWeddingReservePresentResponse) Reset()         { *m = SendWeddingReservePresentResponse{} }
func (m *SendWeddingReservePresentResponse) String() string { return proto.CompactTextString(m) }
func (*SendWeddingReservePresentResponse) ProtoMessage()    {}
func (*SendWeddingReservePresentResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{157}
}
func (m *SendWeddingReservePresentResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendWeddingReservePresentResponse.Unmarshal(m, b)
}
func (m *SendWeddingReservePresentResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendWeddingReservePresentResponse.Marshal(b, m, deterministic)
}
func (dst *SendWeddingReservePresentResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendWeddingReservePresentResponse.Merge(dst, src)
}
func (m *SendWeddingReservePresentResponse) XXX_Size() int {
	return xxx_messageInfo_SendWeddingReservePresentResponse.Size(m)
}
func (m *SendWeddingReservePresentResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SendWeddingReservePresentResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SendWeddingReservePresentResponse proto.InternalMessageInfo

func (m *SendWeddingReservePresentResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 批量查询用户的在房状态
type GetUserInRoomStatusRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	UidList              []uint32     `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserInRoomStatusRequest) Reset()         { *m = GetUserInRoomStatusRequest{} }
func (m *GetUserInRoomStatusRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserInRoomStatusRequest) ProtoMessage()    {}
func (*GetUserInRoomStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{158}
}
func (m *GetUserInRoomStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInRoomStatusRequest.Unmarshal(m, b)
}
func (m *GetUserInRoomStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInRoomStatusRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserInRoomStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInRoomStatusRequest.Merge(dst, src)
}
func (m *GetUserInRoomStatusRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserInRoomStatusRequest.Size(m)
}
func (m *GetUserInRoomStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInRoomStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInRoomStatusRequest proto.InternalMessageInfo

func (m *GetUserInRoomStatusRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserInRoomStatusRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetUserInRoomStatusRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type UserInRoomStatus struct {
	User                 *app.UserProfile `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	InRoom               bool             `protobuf:"varint,2,opt,name=in_room,json=inRoom,proto3" json:"in_room,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserInRoomStatus) Reset()         { *m = UserInRoomStatus{} }
func (m *UserInRoomStatus) String() string { return proto.CompactTextString(m) }
func (*UserInRoomStatus) ProtoMessage()    {}
func (*UserInRoomStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{159}
}
func (m *UserInRoomStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInRoomStatus.Unmarshal(m, b)
}
func (m *UserInRoomStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInRoomStatus.Marshal(b, m, deterministic)
}
func (dst *UserInRoomStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInRoomStatus.Merge(dst, src)
}
func (m *UserInRoomStatus) XXX_Size() int {
	return xxx_messageInfo_UserInRoomStatus.Size(m)
}
func (m *UserInRoomStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInRoomStatus.DiscardUnknown(m)
}

var xxx_messageInfo_UserInRoomStatus proto.InternalMessageInfo

func (m *UserInRoomStatus) GetUser() *app.UserProfile {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *UserInRoomStatus) GetInRoom() bool {
	if m != nil {
		return m.InRoom
	}
	return false
}

type GetUserInRoomStatusResponse struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UserList             []*UserInRoomStatus `protobuf:"bytes,2,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetUserInRoomStatusResponse) Reset()         { *m = GetUserInRoomStatusResponse{} }
func (m *GetUserInRoomStatusResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserInRoomStatusResponse) ProtoMessage()    {}
func (*GetUserInRoomStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{160}
}
func (m *GetUserInRoomStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInRoomStatusResponse.Unmarshal(m, b)
}
func (m *GetUserInRoomStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInRoomStatusResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserInRoomStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInRoomStatusResponse.Merge(dst, src)
}
func (m *GetUserInRoomStatusResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserInRoomStatusResponse.Size(m)
}
func (m *GetUserInRoomStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInRoomStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInRoomStatusResponse proto.InternalMessageInfo

func (m *GetUserInRoomStatusResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserInRoomStatusResponse) GetUserList() []*UserInRoomStatus {
	if m != nil {
		return m.UserList
	}
	return nil
}

type GetGoingWeddingEntryRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Page                 uint32       `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGoingWeddingEntryRequest) Reset()         { *m = GetGoingWeddingEntryRequest{} }
func (m *GetGoingWeddingEntryRequest) String() string { return proto.CompactTextString(m) }
func (*GetGoingWeddingEntryRequest) ProtoMessage()    {}
func (*GetGoingWeddingEntryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{161}
}
func (m *GetGoingWeddingEntryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGoingWeddingEntryRequest.Unmarshal(m, b)
}
func (m *GetGoingWeddingEntryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGoingWeddingEntryRequest.Marshal(b, m, deterministic)
}
func (dst *GetGoingWeddingEntryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGoingWeddingEntryRequest.Merge(dst, src)
}
func (m *GetGoingWeddingEntryRequest) XXX_Size() int {
	return xxx_messageInfo_GetGoingWeddingEntryRequest.Size(m)
}
func (m *GetGoingWeddingEntryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGoingWeddingEntryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGoingWeddingEntryRequest proto.InternalMessageInfo

func (m *GetGoingWeddingEntryRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGoingWeddingEntryRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

type UserInfoWithChannel struct {
	UserInfo             *app.UserProfile `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	ChannelId            uint32           `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserInfoWithChannel) Reset()         { *m = UserInfoWithChannel{} }
func (m *UserInfoWithChannel) String() string { return proto.CompactTextString(m) }
func (*UserInfoWithChannel) ProtoMessage()    {}
func (*UserInfoWithChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{162}
}
func (m *UserInfoWithChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfoWithChannel.Unmarshal(m, b)
}
func (m *UserInfoWithChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfoWithChannel.Marshal(b, m, deterministic)
}
func (dst *UserInfoWithChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfoWithChannel.Merge(dst, src)
}
func (m *UserInfoWithChannel) XXX_Size() int {
	return xxx_messageInfo_UserInfoWithChannel.Size(m)
}
func (m *UserInfoWithChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfoWithChannel.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfoWithChannel proto.InternalMessageInfo

func (m *UserInfoWithChannel) GetUserInfo() *app.UserProfile {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *UserInfoWithChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetGoingWeddingEntryResponse struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Bride                *UserInfoWithChannel   `protobuf:"bytes,2,opt,name=bride,proto3" json:"bride,omitempty"`
	Groom                *UserInfoWithChannel   `protobuf:"bytes,3,opt,name=groom,proto3" json:"groom,omitempty"`
	OtherUserList        []*UserInfoWithChannel `protobuf:"bytes,4,rep,name=other_user_list,json=otherUserList,proto3" json:"other_user_list,omitempty"`
	MainTitle            string                 `protobuf:"bytes,5,opt,name=main_title,json=mainTitle,proto3" json:"main_title,omitempty"`
	SubTitle             string                 `protobuf:"bytes,6,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	NextPageNum          uint32                 `protobuf:"varint,7,opt,name=next_page_num,json=nextPageNum,proto3" json:"next_page_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetGoingWeddingEntryResponse) Reset()         { *m = GetGoingWeddingEntryResponse{} }
func (m *GetGoingWeddingEntryResponse) String() string { return proto.CompactTextString(m) }
func (*GetGoingWeddingEntryResponse) ProtoMessage()    {}
func (*GetGoingWeddingEntryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{163}
}
func (m *GetGoingWeddingEntryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGoingWeddingEntryResponse.Unmarshal(m, b)
}
func (m *GetGoingWeddingEntryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGoingWeddingEntryResponse.Marshal(b, m, deterministic)
}
func (dst *GetGoingWeddingEntryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGoingWeddingEntryResponse.Merge(dst, src)
}
func (m *GetGoingWeddingEntryResponse) XXX_Size() int {
	return xxx_messageInfo_GetGoingWeddingEntryResponse.Size(m)
}
func (m *GetGoingWeddingEntryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGoingWeddingEntryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGoingWeddingEntryResponse proto.InternalMessageInfo

func (m *GetGoingWeddingEntryResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGoingWeddingEntryResponse) GetBride() *UserInfoWithChannel {
	if m != nil {
		return m.Bride
	}
	return nil
}

func (m *GetGoingWeddingEntryResponse) GetGroom() *UserInfoWithChannel {
	if m != nil {
		return m.Groom
	}
	return nil
}

func (m *GetGoingWeddingEntryResponse) GetOtherUserList() []*UserInfoWithChannel {
	if m != nil {
		return m.OtherUserList
	}
	return nil
}

func (m *GetGoingWeddingEntryResponse) GetMainTitle() string {
	if m != nil {
		return m.MainTitle
	}
	return ""
}

func (m *GetGoingWeddingEntryResponse) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *GetGoingWeddingEntryResponse) GetNextPageNum() uint32 {
	if m != nil {
		return m.NextPageNum
	}
	return 0
}

// 提醒用户进房
type RemindUserJoinWeddingRoomRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TargetUid            uint32       `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RemindUserJoinWeddingRoomRequest) Reset()         { *m = RemindUserJoinWeddingRoomRequest{} }
func (m *RemindUserJoinWeddingRoomRequest) String() string { return proto.CompactTextString(m) }
func (*RemindUserJoinWeddingRoomRequest) ProtoMessage()    {}
func (*RemindUserJoinWeddingRoomRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{164}
}
func (m *RemindUserJoinWeddingRoomRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemindUserJoinWeddingRoomRequest.Unmarshal(m, b)
}
func (m *RemindUserJoinWeddingRoomRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemindUserJoinWeddingRoomRequest.Marshal(b, m, deterministic)
}
func (dst *RemindUserJoinWeddingRoomRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemindUserJoinWeddingRoomRequest.Merge(dst, src)
}
func (m *RemindUserJoinWeddingRoomRequest) XXX_Size() int {
	return xxx_messageInfo_RemindUserJoinWeddingRoomRequest.Size(m)
}
func (m *RemindUserJoinWeddingRoomRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RemindUserJoinWeddingRoomRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RemindUserJoinWeddingRoomRequest proto.InternalMessageInfo

func (m *RemindUserJoinWeddingRoomRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *RemindUserJoinWeddingRoomRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RemindUserJoinWeddingRoomRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type RemindUserJoinWeddingRoomResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *RemindUserJoinWeddingRoomResponse) Reset()         { *m = RemindUserJoinWeddingRoomResponse{} }
func (m *RemindUserJoinWeddingRoomResponse) String() string { return proto.CompactTextString(m) }
func (*RemindUserJoinWeddingRoomResponse) ProtoMessage()    {}
func (*RemindUserJoinWeddingRoomResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{165}
}
func (m *RemindUserJoinWeddingRoomResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemindUserJoinWeddingRoomResponse.Unmarshal(m, b)
}
func (m *RemindUserJoinWeddingRoomResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemindUserJoinWeddingRoomResponse.Marshal(b, m, deterministic)
}
func (dst *RemindUserJoinWeddingRoomResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemindUserJoinWeddingRoomResponse.Merge(dst, src)
}
func (m *RemindUserJoinWeddingRoomResponse) XXX_Size() int {
	return xxx_messageInfo_RemindUserJoinWeddingRoomResponse.Size(m)
}
func (m *RemindUserJoinWeddingRoomResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RemindUserJoinWeddingRoomResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RemindUserJoinWeddingRoomResponse proto.InternalMessageInfo

func (m *RemindUserJoinWeddingRoomResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 婚礼收送礼值
type WeddingPresentVal struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PresentVal           uint32   `protobuf:"varint,2,opt,name=present_val,json=presentVal,proto3" json:"present_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingPresentVal) Reset()         { *m = WeddingPresentVal{} }
func (m *WeddingPresentVal) String() string { return proto.CompactTextString(m) }
func (*WeddingPresentVal) ProtoMessage()    {}
func (*WeddingPresentVal) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{166}
}
func (m *WeddingPresentVal) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPresentVal.Unmarshal(m, b)
}
func (m *WeddingPresentVal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPresentVal.Marshal(b, m, deterministic)
}
func (dst *WeddingPresentVal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPresentVal.Merge(dst, src)
}
func (m *WeddingPresentVal) XXX_Size() int {
	return xxx_messageInfo_WeddingPresentVal.Size(m)
}
func (m *WeddingPresentVal) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPresentVal.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPresentVal proto.InternalMessageInfo

func (m *WeddingPresentVal) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WeddingPresentVal) GetPresentVal() uint32 {
	if m != nil {
		return m.PresentVal
	}
	return 0
}

type WeddingPresentLevel struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Lv                   uint32   `protobuf:"varint,2,opt,name=lv,proto3" json:"lv,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingPresentLevel) Reset()         { *m = WeddingPresentLevel{} }
func (m *WeddingPresentLevel) String() string { return proto.CompactTextString(m) }
func (*WeddingPresentLevel) ProtoMessage()    {}
func (*WeddingPresentLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{167}
}
func (m *WeddingPresentLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPresentLevel.Unmarshal(m, b)
}
func (m *WeddingPresentLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPresentLevel.Marshal(b, m, deterministic)
}
func (dst *WeddingPresentLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPresentLevel.Merge(dst, src)
}
func (m *WeddingPresentLevel) XXX_Size() int {
	return xxx_messageInfo_WeddingPresentLevel.Size(m)
}
func (m *WeddingPresentLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPresentLevel.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPresentLevel proto.InternalMessageInfo

func (m *WeddingPresentLevel) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WeddingPresentLevel) GetLv() uint32 {
	if m != nil {
		return m.Lv
	}
	return 0
}

// 婚礼收送礼物值信息(计数器)
type WeddingPresentCountInfo struct {
	UserPresentValList   []*WeddingPresentVal `protobuf:"bytes,1,rep,name=user_present_val_list,json=userPresentValList,proto3" json:"user_present_val_list,omitempty"`
	MvpUid               uint32               `protobuf:"varint,2,opt,name=mvp_uid,json=mvpUid,proto3" json:"mvp_uid,omitempty"`
	TopRecvPresentLv     *WeddingPresentLevel `protobuf:"bytes,3,opt,name=top_recv_present_lv,json=topRecvPresentLv,proto3" json:"top_recv_present_lv,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *WeddingPresentCountInfo) Reset()         { *m = WeddingPresentCountInfo{} }
func (m *WeddingPresentCountInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingPresentCountInfo) ProtoMessage()    {}
func (*WeddingPresentCountInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{168}
}
func (m *WeddingPresentCountInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPresentCountInfo.Unmarshal(m, b)
}
func (m *WeddingPresentCountInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPresentCountInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingPresentCountInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPresentCountInfo.Merge(dst, src)
}
func (m *WeddingPresentCountInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingPresentCountInfo.Size(m)
}
func (m *WeddingPresentCountInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPresentCountInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPresentCountInfo proto.InternalMessageInfo

func (m *WeddingPresentCountInfo) GetUserPresentValList() []*WeddingPresentVal {
	if m != nil {
		return m.UserPresentValList
	}
	return nil
}

func (m *WeddingPresentCountInfo) GetMvpUid() uint32 {
	if m != nil {
		return m.MvpUid
	}
	return 0
}

func (m *WeddingPresentCountInfo) GetTopRecvPresentLv() *WeddingPresentLevel {
	if m != nil {
		return m.TopRecvPresentLv
	}
	return nil
}

// 婚礼收礼值变化通知
type WeddingRecvPresentValChangeOpt struct {
	ChannelId            uint32               `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	WeddingId            uint32               `protobuf:"varint,2,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	ServerMs             int64                `protobuf:"varint,3,opt,name=server_ms,json=serverMs,proto3" json:"server_ms,omitempty"`
	UserPresentValList   []*WeddingPresentVal `protobuf:"bytes,4,rep,name=user_present_val_list,json=userPresentValList,proto3" json:"user_present_val_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *WeddingRecvPresentValChangeOpt) Reset()         { *m = WeddingRecvPresentValChangeOpt{} }
func (m *WeddingRecvPresentValChangeOpt) String() string { return proto.CompactTextString(m) }
func (*WeddingRecvPresentValChangeOpt) ProtoMessage()    {}
func (*WeddingRecvPresentValChangeOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{169}
}
func (m *WeddingRecvPresentValChangeOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingRecvPresentValChangeOpt.Unmarshal(m, b)
}
func (m *WeddingRecvPresentValChangeOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingRecvPresentValChangeOpt.Marshal(b, m, deterministic)
}
func (dst *WeddingRecvPresentValChangeOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingRecvPresentValChangeOpt.Merge(dst, src)
}
func (m *WeddingRecvPresentValChangeOpt) XXX_Size() int {
	return xxx_messageInfo_WeddingRecvPresentValChangeOpt.Size(m)
}
func (m *WeddingRecvPresentValChangeOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingRecvPresentValChangeOpt.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingRecvPresentValChangeOpt proto.InternalMessageInfo

func (m *WeddingRecvPresentValChangeOpt) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *WeddingRecvPresentValChangeOpt) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *WeddingRecvPresentValChangeOpt) GetServerMs() int64 {
	if m != nil {
		return m.ServerMs
	}
	return 0
}

func (m *WeddingRecvPresentValChangeOpt) GetUserPresentValList() []*WeddingPresentVal {
	if m != nil {
		return m.UserPresentValList
	}
	return nil
}

// 婚礼收送礼物值前N名变化通知
type WeddingPresentValTopChangeOpt struct {
	ChannelId            uint32               `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	WeddingId            uint32               `protobuf:"varint,2,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	ServerMs             int64                `protobuf:"varint,3,opt,name=server_ms,json=serverMs,proto3" json:"server_ms,omitempty"`
	TopRecvPresentLv     *WeddingPresentLevel `protobuf:"bytes,4,opt,name=top_recv_present_lv,json=topRecvPresentLv,proto3" json:"top_recv_present_lv,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *WeddingPresentValTopChangeOpt) Reset()         { *m = WeddingPresentValTopChangeOpt{} }
func (m *WeddingPresentValTopChangeOpt) String() string { return proto.CompactTextString(m) }
func (*WeddingPresentValTopChangeOpt) ProtoMessage()    {}
func (*WeddingPresentValTopChangeOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{170}
}
func (m *WeddingPresentValTopChangeOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPresentValTopChangeOpt.Unmarshal(m, b)
}
func (m *WeddingPresentValTopChangeOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPresentValTopChangeOpt.Marshal(b, m, deterministic)
}
func (dst *WeddingPresentValTopChangeOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPresentValTopChangeOpt.Merge(dst, src)
}
func (m *WeddingPresentValTopChangeOpt) XXX_Size() int {
	return xxx_messageInfo_WeddingPresentValTopChangeOpt.Size(m)
}
func (m *WeddingPresentValTopChangeOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPresentValTopChangeOpt.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPresentValTopChangeOpt proto.InternalMessageInfo

func (m *WeddingPresentValTopChangeOpt) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *WeddingPresentValTopChangeOpt) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *WeddingPresentValTopChangeOpt) GetServerMs() int64 {
	if m != nil {
		return m.ServerMs
	}
	return 0
}

func (m *WeddingPresentValTopChangeOpt) GetTopRecvPresentLv() *WeddingPresentLevel {
	if m != nil {
		return m.TopRecvPresentLv
	}
	return nil
}

// 婚礼收送礼物值mvp变化通知
type WeddingPresentValMvpChangeOpt struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	WeddingId            uint32   `protobuf:"varint,2,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	ServerMs             int64    `protobuf:"varint,3,opt,name=server_ms,json=serverMs,proto3" json:"server_ms,omitempty"`
	MvpUid               uint32   `protobuf:"varint,4,opt,name=mvp_uid,json=mvpUid,proto3" json:"mvp_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingPresentValMvpChangeOpt) Reset()         { *m = WeddingPresentValMvpChangeOpt{} }
func (m *WeddingPresentValMvpChangeOpt) String() string { return proto.CompactTextString(m) }
func (*WeddingPresentValMvpChangeOpt) ProtoMessage()    {}
func (*WeddingPresentValMvpChangeOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{171}
}
func (m *WeddingPresentValMvpChangeOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPresentValMvpChangeOpt.Unmarshal(m, b)
}
func (m *WeddingPresentValMvpChangeOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPresentValMvpChangeOpt.Marshal(b, m, deterministic)
}
func (dst *WeddingPresentValMvpChangeOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPresentValMvpChangeOpt.Merge(dst, src)
}
func (m *WeddingPresentValMvpChangeOpt) XXX_Size() int {
	return xxx_messageInfo_WeddingPresentValMvpChangeOpt.Size(m)
}
func (m *WeddingPresentValMvpChangeOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPresentValMvpChangeOpt.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPresentValMvpChangeOpt proto.InternalMessageInfo

func (m *WeddingPresentValMvpChangeOpt) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *WeddingPresentValMvpChangeOpt) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *WeddingPresentValMvpChangeOpt) GetServerMs() int64 {
	if m != nil {
		return m.ServerMs
	}
	return 0
}

func (m *WeddingPresentValMvpChangeOpt) GetMvpUid() uint32 {
	if m != nil {
		return m.MvpUid
	}
	return 0
}

// 婚礼结束mvp用户结算通知
type WeddingMvpUserSettlementNotify struct {
	ChannelId            uint32           `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	WeddingId            uint32           `protobuf:"varint,2,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	ServerMs             int64            `protobuf:"varint,3,opt,name=server_ms,json=serverMs,proto3" json:"server_ms,omitempty"`
	MvpUser              *app.UserProfile `protobuf:"bytes,4,opt,name=mvp_user,json=mvpUser,proto3" json:"mvp_user,omitempty"`
	ResourceUrl          string           `protobuf:"bytes,5,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	ResourceMd5          string           `protobuf:"bytes,6,opt,name=resource_md5,json=resourceMd5,proto3" json:"resource_md5,omitempty"`
	UserInuseItemList    []uint32         `protobuf:"varint,7,rep,packed,name=user_inuse_item_list,json=userInuseItemList,proto3" json:"user_inuse_item_list,omitempty"`
	MvpPoseId            uint32           `protobuf:"varint,8,opt,name=mvp_pose_id,json=mvpPoseId,proto3" json:"mvp_pose_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WeddingMvpUserSettlementNotify) Reset()         { *m = WeddingMvpUserSettlementNotify{} }
func (m *WeddingMvpUserSettlementNotify) String() string { return proto.CompactTextString(m) }
func (*WeddingMvpUserSettlementNotify) ProtoMessage()    {}
func (*WeddingMvpUserSettlementNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{172}
}
func (m *WeddingMvpUserSettlementNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingMvpUserSettlementNotify.Unmarshal(m, b)
}
func (m *WeddingMvpUserSettlementNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingMvpUserSettlementNotify.Marshal(b, m, deterministic)
}
func (dst *WeddingMvpUserSettlementNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingMvpUserSettlementNotify.Merge(dst, src)
}
func (m *WeddingMvpUserSettlementNotify) XXX_Size() int {
	return xxx_messageInfo_WeddingMvpUserSettlementNotify.Size(m)
}
func (m *WeddingMvpUserSettlementNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingMvpUserSettlementNotify.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingMvpUserSettlementNotify proto.InternalMessageInfo

func (m *WeddingMvpUserSettlementNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *WeddingMvpUserSettlementNotify) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *WeddingMvpUserSettlementNotify) GetServerMs() int64 {
	if m != nil {
		return m.ServerMs
	}
	return 0
}

func (m *WeddingMvpUserSettlementNotify) GetMvpUser() *app.UserProfile {
	if m != nil {
		return m.MvpUser
	}
	return nil
}

func (m *WeddingMvpUserSettlementNotify) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *WeddingMvpUserSettlementNotify) GetResourceMd5() string {
	if m != nil {
		return m.ResourceMd5
	}
	return ""
}

func (m *WeddingMvpUserSettlementNotify) GetUserInuseItemList() []uint32 {
	if m != nil {
		return m.UserInuseItemList
	}
	return nil
}

func (m *WeddingMvpUserSettlementNotify) GetMvpPoseId() uint32 {
	if m != nil {
		return m.MvpPoseId
	}
	return 0
}

// 获取婚礼礼物
type GetWeddingPresentRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWeddingPresentRequest) Reset()         { *m = GetWeddingPresentRequest{} }
func (m *GetWeddingPresentRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingPresentRequest) ProtoMessage()    {}
func (*GetWeddingPresentRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{173}
}
func (m *GetWeddingPresentRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingPresentRequest.Unmarshal(m, b)
}
func (m *GetWeddingPresentRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingPresentRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingPresentRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingPresentRequest.Merge(dst, src)
}
func (m *GetWeddingPresentRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingPresentRequest.Size(m)
}
func (m *GetWeddingPresentRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingPresentRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingPresentRequest proto.InternalMessageInfo

func (m *GetWeddingPresentRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetWeddingPresentResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GiftIdList           []uint32      `protobuf:"varint,2,rep,packed,name=gift_id_list,json=giftIdList,proto3" json:"gift_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetWeddingPresentResponse) Reset()         { *m = GetWeddingPresentResponse{} }
func (m *GetWeddingPresentResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingPresentResponse) ProtoMessage()    {}
func (*GetWeddingPresentResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{174}
}
func (m *GetWeddingPresentResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingPresentResponse.Unmarshal(m, b)
}
func (m *GetWeddingPresentResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingPresentResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingPresentResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingPresentResponse.Merge(dst, src)
}
func (m *GetWeddingPresentResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingPresentResponse.Size(m)
}
func (m *GetWeddingPresentResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingPresentResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingPresentResponse proto.InternalMessageInfo

func (m *GetWeddingPresentResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWeddingPresentResponse) GetGiftIdList() []uint32 {
	if m != nil {
		return m.GiftIdList
	}
	return nil
}

// 获取婚礼前进度面板
type GetWeddingPreProgressInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWeddingPreProgressInfoRequest) Reset()         { *m = GetWeddingPreProgressInfoRequest{} }
func (m *GetWeddingPreProgressInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingPreProgressInfoRequest) ProtoMessage()    {}
func (*GetWeddingPreProgressInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{175}
}
func (m *GetWeddingPreProgressInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingPreProgressInfoRequest.Unmarshal(m, b)
}
func (m *GetWeddingPreProgressInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingPreProgressInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingPreProgressInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingPreProgressInfoRequest.Merge(dst, src)
}
func (m *GetWeddingPreProgressInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingPreProgressInfoRequest.Size(m)
}
func (m *GetWeddingPreProgressInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingPreProgressInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingPreProgressInfoRequest proto.InternalMessageInfo

func (m *GetWeddingPreProgressInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetWeddingPreProgressInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetWeddingPreProgressInfoResponse struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PreProgressInfo      *WeddingPreProgressInfo `protobuf:"bytes,2,opt,name=pre_progress_info,json=preProgressInfo,proto3" json:"pre_progress_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetWeddingPreProgressInfoResponse) Reset()         { *m = GetWeddingPreProgressInfoResponse{} }
func (m *GetWeddingPreProgressInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingPreProgressInfoResponse) ProtoMessage()    {}
func (*GetWeddingPreProgressInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{176}
}
func (m *GetWeddingPreProgressInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingPreProgressInfoResponse.Unmarshal(m, b)
}
func (m *GetWeddingPreProgressInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingPreProgressInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingPreProgressInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingPreProgressInfoResponse.Merge(dst, src)
}
func (m *GetWeddingPreProgressInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingPreProgressInfoResponse.Size(m)
}
func (m *GetWeddingPreProgressInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingPreProgressInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingPreProgressInfoResponse proto.InternalMessageInfo

func (m *GetWeddingPreProgressInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWeddingPreProgressInfoResponse) GetPreProgressInfo() *WeddingPreProgressInfo {
	if m != nil {
		return m.PreProgressInfo
	}
	return nil
}

type WeddingPreProgressStageCfg struct {
	Stage                uint32                               `protobuf:"varint,1,opt,name=stage,proto3" json:"stage,omitempty"`
	StageName            string                               `protobuf:"bytes,2,opt,name=stage_name,json=stageName,proto3" json:"stage_name,omitempty"`
	DefaultDesc          *WeddingPrePreProgressStageDescGroup `protobuf:"bytes,3,opt,name=default_desc,json=defaultDesc,proto3" json:"default_desc,omitempty"`
	DoneDesc             *WeddingPrePreProgressStageDescGroup `protobuf:"bytes,4,opt,name=done_desc,json=doneDesc,proto3" json:"done_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *WeddingPreProgressStageCfg) Reset()         { *m = WeddingPreProgressStageCfg{} }
func (m *WeddingPreProgressStageCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingPreProgressStageCfg) ProtoMessage()    {}
func (*WeddingPreProgressStageCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{177}
}
func (m *WeddingPreProgressStageCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPreProgressStageCfg.Unmarshal(m, b)
}
func (m *WeddingPreProgressStageCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPreProgressStageCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingPreProgressStageCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPreProgressStageCfg.Merge(dst, src)
}
func (m *WeddingPreProgressStageCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingPreProgressStageCfg.Size(m)
}
func (m *WeddingPreProgressStageCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPreProgressStageCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPreProgressStageCfg proto.InternalMessageInfo

func (m *WeddingPreProgressStageCfg) GetStage() uint32 {
	if m != nil {
		return m.Stage
	}
	return 0
}

func (m *WeddingPreProgressStageCfg) GetStageName() string {
	if m != nil {
		return m.StageName
	}
	return ""
}

func (m *WeddingPreProgressStageCfg) GetDefaultDesc() *WeddingPrePreProgressStageDescGroup {
	if m != nil {
		return m.DefaultDesc
	}
	return nil
}

func (m *WeddingPreProgressStageCfg) GetDoneDesc() *WeddingPrePreProgressStageDescGroup {
	if m != nil {
		return m.DoneDesc
	}
	return nil
}

type WeddingPrePreProgressStageDescGroup struct {
	HostDesc             *WeddingPreProgressStageDesc `protobuf:"bytes,3,opt,name=host_desc,json=hostDesc,proto3" json:"host_desc,omitempty"`
	NewcomersDesc        *WeddingPreProgressStageDesc `protobuf:"bytes,4,opt,name=newcomers_desc,json=newcomersDesc,proto3" json:"newcomers_desc,omitempty"`
	AudienceDesc         *WeddingPreProgressStageDesc `protobuf:"bytes,5,opt,name=audience_desc,json=audienceDesc,proto3" json:"audience_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *WeddingPrePreProgressStageDescGroup) Reset()         { *m = WeddingPrePreProgressStageDescGroup{} }
func (m *WeddingPrePreProgressStageDescGroup) String() string { return proto.CompactTextString(m) }
func (*WeddingPrePreProgressStageDescGroup) ProtoMessage()    {}
func (*WeddingPrePreProgressStageDescGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{178}
}
func (m *WeddingPrePreProgressStageDescGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPrePreProgressStageDescGroup.Unmarshal(m, b)
}
func (m *WeddingPrePreProgressStageDescGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPrePreProgressStageDescGroup.Marshal(b, m, deterministic)
}
func (dst *WeddingPrePreProgressStageDescGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPrePreProgressStageDescGroup.Merge(dst, src)
}
func (m *WeddingPrePreProgressStageDescGroup) XXX_Size() int {
	return xxx_messageInfo_WeddingPrePreProgressStageDescGroup.Size(m)
}
func (m *WeddingPrePreProgressStageDescGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPrePreProgressStageDescGroup.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPrePreProgressStageDescGroup proto.InternalMessageInfo

func (m *WeddingPrePreProgressStageDescGroup) GetHostDesc() *WeddingPreProgressStageDesc {
	if m != nil {
		return m.HostDesc
	}
	return nil
}

func (m *WeddingPrePreProgressStageDescGroup) GetNewcomersDesc() *WeddingPreProgressStageDesc {
	if m != nil {
		return m.NewcomersDesc
	}
	return nil
}

func (m *WeddingPrePreProgressStageDescGroup) GetAudienceDesc() *WeddingPreProgressStageDesc {
	if m != nil {
		return m.AudienceDesc
	}
	return nil
}

type WeddingPreProgressStageDesc struct {
	DescTitle            string   `protobuf:"bytes,1,opt,name=desc_title,json=descTitle,proto3" json:"desc_title,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingPreProgressStageDesc) Reset()         { *m = WeddingPreProgressStageDesc{} }
func (m *WeddingPreProgressStageDesc) String() string { return proto.CompactTextString(m) }
func (*WeddingPreProgressStageDesc) ProtoMessage()    {}
func (*WeddingPreProgressStageDesc) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{179}
}
func (m *WeddingPreProgressStageDesc) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPreProgressStageDesc.Unmarshal(m, b)
}
func (m *WeddingPreProgressStageDesc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPreProgressStageDesc.Marshal(b, m, deterministic)
}
func (dst *WeddingPreProgressStageDesc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPreProgressStageDesc.Merge(dst, src)
}
func (m *WeddingPreProgressStageDesc) XXX_Size() int {
	return xxx_messageInfo_WeddingPreProgressStageDesc.Size(m)
}
func (m *WeddingPreProgressStageDesc) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPreProgressStageDesc.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPreProgressStageDesc proto.InternalMessageInfo

func (m *WeddingPreProgressStageDesc) GetDescTitle() string {
	if m != nil {
		return m.DescTitle
	}
	return ""
}

func (m *WeddingPreProgressStageDesc) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type WeddingNewcomer struct {
	User                 *app.UserProfile `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Value                uint32           `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WeddingNewcomer) Reset()         { *m = WeddingNewcomer{} }
func (m *WeddingNewcomer) String() string { return proto.CompactTextString(m) }
func (*WeddingNewcomer) ProtoMessage()    {}
func (*WeddingNewcomer) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{180}
}
func (m *WeddingNewcomer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingNewcomer.Unmarshal(m, b)
}
func (m *WeddingNewcomer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingNewcomer.Marshal(b, m, deterministic)
}
func (dst *WeddingNewcomer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingNewcomer.Merge(dst, src)
}
func (m *WeddingNewcomer) XXX_Size() int {
	return xxx_messageInfo_WeddingNewcomer.Size(m)
}
func (m *WeddingNewcomer) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingNewcomer.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingNewcomer proto.InternalMessageInfo

func (m *WeddingNewcomer) GetUser() *app.UserProfile {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *WeddingNewcomer) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

type WeddingPreProgressInfo struct {
	Stage                uint32                        `protobuf:"varint,1,opt,name=stage,proto3" json:"stage,omitempty"`
	WeddingPlanId        uint32                        `protobuf:"varint,2,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	NewcomerList         []*WeddingNewcomer            `protobuf:"bytes,3,rep,name=newcomer_list,json=newcomerList,proto3" json:"newcomer_list,omitempty"`
	PanelResourceUrl     string                        `protobuf:"bytes,4,opt,name=panel_resource_url,json=panelResourceUrl,proto3" json:"panel_resource_url,omitempty"`
	PanelResourceMd5     string                        `protobuf:"bytes,5,opt,name=panel_resource_md5,json=panelResourceMd5,proto3" json:"panel_resource_md5,omitempty"`
	StageCfgList         []*WeddingPreProgressStageCfg `protobuf:"bytes,6,rep,name=stage_cfg_list,json=stageCfgList,proto3" json:"stage_cfg_list,omitempty"`
	PanelResourceUrlPc   string                        `protobuf:"bytes,7,opt,name=panel_resource_url_pc,json=panelResourceUrlPc,proto3" json:"panel_resource_url_pc,omitempty"`
	PanelResourceMd5Pc   string                        `protobuf:"bytes,8,opt,name=panel_resource_md5_pc,json=panelResourceMd5Pc,proto3" json:"panel_resource_md5_pc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *WeddingPreProgressInfo) Reset()         { *m = WeddingPreProgressInfo{} }
func (m *WeddingPreProgressInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingPreProgressInfo) ProtoMessage()    {}
func (*WeddingPreProgressInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{181}
}
func (m *WeddingPreProgressInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPreProgressInfo.Unmarshal(m, b)
}
func (m *WeddingPreProgressInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPreProgressInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingPreProgressInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPreProgressInfo.Merge(dst, src)
}
func (m *WeddingPreProgressInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingPreProgressInfo.Size(m)
}
func (m *WeddingPreProgressInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPreProgressInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPreProgressInfo proto.InternalMessageInfo

func (m *WeddingPreProgressInfo) GetStage() uint32 {
	if m != nil {
		return m.Stage
	}
	return 0
}

func (m *WeddingPreProgressInfo) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *WeddingPreProgressInfo) GetNewcomerList() []*WeddingNewcomer {
	if m != nil {
		return m.NewcomerList
	}
	return nil
}

func (m *WeddingPreProgressInfo) GetPanelResourceUrl() string {
	if m != nil {
		return m.PanelResourceUrl
	}
	return ""
}

func (m *WeddingPreProgressInfo) GetPanelResourceMd5() string {
	if m != nil {
		return m.PanelResourceMd5
	}
	return ""
}

func (m *WeddingPreProgressInfo) GetStageCfgList() []*WeddingPreProgressStageCfg {
	if m != nil {
		return m.StageCfgList
	}
	return nil
}

func (m *WeddingPreProgressInfo) GetPanelResourceUrlPc() string {
	if m != nil {
		return m.PanelResourceUrlPc
	}
	return ""
}

func (m *WeddingPreProgressInfo) GetPanelResourceMd5Pc() string {
	if m != nil {
		return m.PanelResourceMd5Pc
	}
	return ""
}

// 婚礼准备信息变更推送 WEDDING_PRE_PROGRESS_UPDATE_PUSH = 473
type WeddingPreProgressInfoChangeOpt struct {
	PreProgressInfo      *WeddingPreProgressInfo `protobuf:"bytes,1,opt,name=pre_progress_info,json=preProgressInfo,proto3" json:"pre_progress_info,omitempty"`
	ServerTs             uint32                  `protobuf:"varint,2,opt,name=server_ts,json=serverTs,proto3" json:"server_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *WeddingPreProgressInfoChangeOpt) Reset()         { *m = WeddingPreProgressInfoChangeOpt{} }
func (m *WeddingPreProgressInfoChangeOpt) String() string { return proto.CompactTextString(m) }
func (*WeddingPreProgressInfoChangeOpt) ProtoMessage()    {}
func (*WeddingPreProgressInfoChangeOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{182}
}
func (m *WeddingPreProgressInfoChangeOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPreProgressInfoChangeOpt.Unmarshal(m, b)
}
func (m *WeddingPreProgressInfoChangeOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPreProgressInfoChangeOpt.Marshal(b, m, deterministic)
}
func (dst *WeddingPreProgressInfoChangeOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPreProgressInfoChangeOpt.Merge(dst, src)
}
func (m *WeddingPreProgressInfoChangeOpt) XXX_Size() int {
	return xxx_messageInfo_WeddingPreProgressInfoChangeOpt.Size(m)
}
func (m *WeddingPreProgressInfoChangeOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPreProgressInfoChangeOpt.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPreProgressInfoChangeOpt proto.InternalMessageInfo

func (m *WeddingPreProgressInfoChangeOpt) GetPreProgressInfo() *WeddingPreProgressInfo {
	if m != nil {
		return m.PreProgressInfo
	}
	return nil
}

func (m *WeddingPreProgressInfoChangeOpt) GetServerTs() uint32 {
	if m != nil {
		return m.ServerTs
	}
	return 0
}

type StartWeddingRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	WeddingPlanId        uint32       `protobuf:"varint,3,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StartWeddingRequest) Reset()         { *m = StartWeddingRequest{} }
func (m *StartWeddingRequest) String() string { return proto.CompactTextString(m) }
func (*StartWeddingRequest) ProtoMessage()    {}
func (*StartWeddingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{183}
}
func (m *StartWeddingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartWeddingRequest.Unmarshal(m, b)
}
func (m *StartWeddingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartWeddingRequest.Marshal(b, m, deterministic)
}
func (dst *StartWeddingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartWeddingRequest.Merge(dst, src)
}
func (m *StartWeddingRequest) XXX_Size() int {
	return xxx_messageInfo_StartWeddingRequest.Size(m)
}
func (m *StartWeddingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StartWeddingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StartWeddingRequest proto.InternalMessageInfo

func (m *StartWeddingRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *StartWeddingRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StartWeddingRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type StartWeddingResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *StartWeddingResponse) Reset()         { *m = StartWeddingResponse{} }
func (m *StartWeddingResponse) String() string { return proto.CompactTextString(m) }
func (*StartWeddingResponse) ProtoMessage()    {}
func (*StartWeddingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{184}
}
func (m *StartWeddingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartWeddingResponse.Unmarshal(m, b)
}
func (m *StartWeddingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartWeddingResponse.Marshal(b, m, deterministic)
}
func (dst *StartWeddingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartWeddingResponse.Merge(dst, src)
}
func (m *StartWeddingResponse) XXX_Size() int {
	return xxx_messageInfo_StartWeddingResponse.Size(m)
}
func (m *StartWeddingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StartWeddingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StartWeddingResponse proto.InternalMessageInfo

func (m *StartWeddingResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 取消婚礼
type CancelPreparedWeddingRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	WeddingPlanId        uint32       `protobuf:"varint,2,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CancelPreparedWeddingRequest) Reset()         { *m = CancelPreparedWeddingRequest{} }
func (m *CancelPreparedWeddingRequest) String() string { return proto.CompactTextString(m) }
func (*CancelPreparedWeddingRequest) ProtoMessage()    {}
func (*CancelPreparedWeddingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{185}
}
func (m *CancelPreparedWeddingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelPreparedWeddingRequest.Unmarshal(m, b)
}
func (m *CancelPreparedWeddingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelPreparedWeddingRequest.Marshal(b, m, deterministic)
}
func (dst *CancelPreparedWeddingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelPreparedWeddingRequest.Merge(dst, src)
}
func (m *CancelPreparedWeddingRequest) XXX_Size() int {
	return xxx_messageInfo_CancelPreparedWeddingRequest.Size(m)
}
func (m *CancelPreparedWeddingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelPreparedWeddingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CancelPreparedWeddingRequest proto.InternalMessageInfo

func (m *CancelPreparedWeddingRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CancelPreparedWeddingRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *CancelPreparedWeddingRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CancelPreparedWeddingResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CancelPreparedWeddingResponse) Reset()         { *m = CancelPreparedWeddingResponse{} }
func (m *CancelPreparedWeddingResponse) String() string { return proto.CompactTextString(m) }
func (*CancelPreparedWeddingResponse) ProtoMessage()    {}
func (*CancelPreparedWeddingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{186}
}
func (m *CancelPreparedWeddingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelPreparedWeddingResponse.Unmarshal(m, b)
}
func (m *CancelPreparedWeddingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelPreparedWeddingResponse.Marshal(b, m, deterministic)
}
func (dst *CancelPreparedWeddingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelPreparedWeddingResponse.Merge(dst, src)
}
func (m *CancelPreparedWeddingResponse) XXX_Size() int {
	return xxx_messageInfo_CancelPreparedWeddingResponse.Size(m)
}
func (m *CancelPreparedWeddingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelPreparedWeddingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CancelPreparedWeddingResponse proto.InternalMessageInfo

func (m *CancelPreparedWeddingResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取婚礼准备信息
type GetWeddingPrepareInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	WeddingPlanId        uint32       `protobuf:"varint,2,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWeddingPrepareInfoRequest) Reset()         { *m = GetWeddingPrepareInfoRequest{} }
func (m *GetWeddingPrepareInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingPrepareInfoRequest) ProtoMessage()    {}
func (*GetWeddingPrepareInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{187}
}
func (m *GetWeddingPrepareInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingPrepareInfoRequest.Unmarshal(m, b)
}
func (m *GetWeddingPrepareInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingPrepareInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingPrepareInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingPrepareInfoRequest.Merge(dst, src)
}
func (m *GetWeddingPrepareInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingPrepareInfoRequest.Size(m)
}
func (m *GetWeddingPrepareInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingPrepareInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingPrepareInfoRequest proto.InternalMessageInfo

func (m *GetWeddingPrepareInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetWeddingPrepareInfoRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type GetWeddingPrepareInfoResponse struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PlanInfo             *SimpleWeddingPlanInfo `protobuf:"bytes,2,opt,name=plan_info,json=planInfo,proto3" json:"plan_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetWeddingPrepareInfoResponse) Reset()         { *m = GetWeddingPrepareInfoResponse{} }
func (m *GetWeddingPrepareInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingPrepareInfoResponse) ProtoMessage()    {}
func (*GetWeddingPrepareInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_logic_c0fde01e342fd719, []int{188}
}
func (m *GetWeddingPrepareInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingPrepareInfoResponse.Unmarshal(m, b)
}
func (m *GetWeddingPrepareInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingPrepareInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingPrepareInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingPrepareInfoResponse.Merge(dst, src)
}
func (m *GetWeddingPrepareInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingPrepareInfoResponse.Size(m)
}
func (m *GetWeddingPrepareInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingPrepareInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingPrepareInfoResponse proto.InternalMessageInfo

func (m *GetWeddingPrepareInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWeddingPrepareInfoResponse) GetPlanInfo() *SimpleWeddingPlanInfo {
	if m != nil {
		return m.PlanInfo
	}
	return nil
}

func init() {
	proto.RegisterType((*WeddingStageCfg)(nil), "ga.channel_wedding_logic.WeddingStageCfg")
	proto.RegisterType((*WeddingStageInfo)(nil), "ga.channel_wedding_logic.WeddingStageInfo")
	proto.RegisterType((*WeddingLevelClothes)(nil), "ga.channel_wedding_logic.WeddingLevelClothes")
	proto.RegisterType((*WeddingSceneBoneCfg)(nil), "ga.channel_wedding_logic.WeddingSceneBoneCfg")
	proto.RegisterType((*WeddingSceneCfg)(nil), "ga.channel_wedding_logic.WeddingSceneCfg")
	proto.RegisterType((*HappinessLevelInfo)(nil), "ga.channel_wedding_logic.HappinessLevelInfo")
	proto.RegisterType((*HappinessConfigInfo)(nil), "ga.channel_wedding_logic.HappinessConfigInfo")
	proto.RegisterType((*WeddingLevelBackgroundCfg)(nil), "ga.channel_wedding_logic.WeddingLevelBackgroundCfg")
	proto.RegisterType((*WeddingResource)(nil), "ga.channel_wedding_logic.WeddingResource")
	proto.RegisterType((*WeddingRoomThemeCfg)(nil), "ga.channel_wedding_logic.WeddingRoomThemeCfg")
	proto.RegisterType((*ChairGameResourceCfg)(nil), "ga.channel_wedding_logic.ChairGameResourceCfg")
	proto.RegisterType((*WeddingCpMemInfo)(nil), "ga.channel_wedding_logic.WeddingCpMemInfo")
	proto.RegisterType((*WeddingMemorialVideo)(nil), "ga.channel_wedding_logic.WeddingMemorialVideo")
	proto.RegisterType((*WeddingBoneCfg)(nil), "ga.channel_wedding_logic.WeddingBoneCfg")
	proto.RegisterType((*WeddingInfo)(nil), "ga.channel_wedding_logic.WeddingInfo")
	proto.RegisterType((*WeddingClothesInfo)(nil), "ga.channel_wedding_logic.WeddingClothesInfo")
	proto.RegisterType((*BatchGetUserWeddingClothesRequest)(nil), "ga.channel_wedding_logic.BatchGetUserWeddingClothesRequest")
	proto.RegisterType((*BatchGetUserWeddingClothesResponse)(nil), "ga.channel_wedding_logic.BatchGetUserWeddingClothesResponse")
	proto.RegisterType((*WeddingClothesChangeOpt)(nil), "ga.channel_wedding_logic.WeddingClothesChangeOpt")
	proto.RegisterType((*WeddingStageChangeOpt)(nil), "ga.channel_wedding_logic.WeddingStageChangeOpt")
	proto.RegisterType((*WeddingLevelChangeOpt)(nil), "ga.channel_wedding_logic.WeddingLevelChangeOpt")
	proto.RegisterType((*WeddingSceneNotifyOpt)(nil), "ga.channel_wedding_logic.WeddingSceneNotifyOpt")
	proto.RegisterType((*WeddingSceneGroupPhotoOpt)(nil), "ga.channel_wedding_logic.WeddingSceneGroupPhotoOpt")
	proto.RegisterType((*WeddingSceneHighLightOpt)(nil), "ga.channel_wedding_logic.WeddingSceneHighLightOpt")
	proto.RegisterType((*WeddingReservePresent)(nil), "ga.channel_wedding_logic.WeddingReservePresent")
	proto.RegisterType((*WeddingHappinessChangeOpt)(nil), "ga.channel_wedding_logic.WeddingHappinessChangeOpt")
	proto.RegisterType((*GetChannelWeddingInfoRequest)(nil), "ga.channel_wedding_logic.GetChannelWeddingInfoRequest")
	proto.RegisterType((*GetChannelWeddingInfoResponse)(nil), "ga.channel_wedding_logic.GetChannelWeddingInfoResponse")
	proto.RegisterType((*SwitchWeddingStageRequest)(nil), "ga.channel_wedding_logic.SwitchWeddingStageRequest")
	proto.RegisterType((*SwitchWeddingStageResponse)(nil), "ga.channel_wedding_logic.SwitchWeddingStageResponse")
	proto.RegisterType((*TakeWeddingGroupPhotoRequest)(nil), "ga.channel_wedding_logic.TakeWeddingGroupPhotoRequest")
	proto.RegisterType((*TakeWeddingGroupPhotoResponse)(nil), "ga.channel_wedding_logic.TakeWeddingGroupPhotoResponse")
	proto.RegisterType((*GetUserWeddingPoseRequest)(nil), "ga.channel_wedding_logic.GetUserWeddingPoseRequest")
	proto.RegisterType((*GetUserWeddingPoseResponse)(nil), "ga.channel_wedding_logic.GetUserWeddingPoseResponse")
	proto.RegisterType((*SetUserInuseWeddingPoseRequest)(nil), "ga.channel_wedding_logic.SetUserInuseWeddingPoseRequest")
	proto.RegisterType((*SetUserInuseWeddingPoseResponse)(nil), "ga.channel_wedding_logic.SetUserInuseWeddingPoseResponse")
	proto.RegisterType((*BatchGetUserInuseWeddingPoseRequest)(nil), "ga.channel_wedding_logic.BatchGetUserInuseWeddingPoseRequest")
	proto.RegisterType((*SetUserWeddingOrientationRequest)(nil), "ga.channel_wedding_logic.SetUserWeddingOrientationRequest")
	proto.RegisterType((*SetUserWeddingOrientationResponse)(nil), "ga.channel_wedding_logic.SetUserWeddingOrientationResponse")
	proto.RegisterType((*UserWeddingPose)(nil), "ga.channel_wedding_logic.UserWeddingPose")
	proto.RegisterType((*BatchGetUserInuseWeddingPoseResponse)(nil), "ga.channel_wedding_logic.BatchGetUserInuseWeddingPoseResponse")
	proto.RegisterType((*UserWeddingPoseChangeOpt)(nil), "ga.channel_wedding_logic.UserWeddingPoseChangeOpt")
	proto.RegisterType((*GetWeddingGroupPhotoSeatMapRequest)(nil), "ga.channel_wedding_logic.GetWeddingGroupPhotoSeatMapRequest")
	proto.RegisterType((*WeddingGroupPhotoSeat)(nil), "ga.channel_wedding_logic.WeddingGroupPhotoSeat")
	proto.RegisterType((*GetWeddingGroupPhotoSeatMapResponse)(nil), "ga.channel_wedding_logic.GetWeddingGroupPhotoSeatMapResponse")
	proto.RegisterType((*SetUserWeddingGroupPhotoSeatRequest)(nil), "ga.channel_wedding_logic.SetUserWeddingGroupPhotoSeatRequest")
	proto.RegisterType((*SetUserWeddingGroupPhotoSeatResponse)(nil), "ga.channel_wedding_logic.SetUserWeddingGroupPhotoSeatResponse")
	proto.RegisterType((*WeddingGroupPhotoSeatChangeOpt)(nil), "ga.channel_wedding_logic.WeddingGroupPhotoSeatChangeOpt")
	proto.RegisterType((*WeddingGuestEnterRoomOpt)(nil), "ga.channel_wedding_logic.WeddingGuestEnterRoomOpt")
	proto.RegisterType((*WeddingRankEntry)(nil), "ga.channel_wedding_logic.WeddingRankEntry")
	proto.RegisterType((*GetWeddingRankEntryRequest)(nil), "ga.channel_wedding_logic.GetWeddingRankEntryRequest")
	proto.RegisterType((*GetWeddingRankEntryResponse)(nil), "ga.channel_wedding_logic.GetWeddingRankEntryResponse")
	proto.RegisterType((*GetWeddingThemeCfgListRequest)(nil), "ga.channel_wedding_logic.GetWeddingThemeCfgListRequest")
	proto.RegisterType((*GetWeddingThemeCfgListResponse)(nil), "ga.channel_wedding_logic.GetWeddingThemeCfgListResponse")
	proto.RegisterType((*ApplyToJoinChairGameRequest)(nil), "ga.channel_wedding_logic.ApplyToJoinChairGameRequest")
	proto.RegisterType((*ApplyToJoinChairGameResponse)(nil), "ga.channel_wedding_logic.ApplyToJoinChairGameResponse")
	proto.RegisterType((*ChairGameUserInfo)(nil), "ga.channel_wedding_logic.ChairGameUserInfo")
	proto.RegisterType((*ChairGamePublicText)(nil), "ga.channel_wedding_logic.ChairGamePublicText")
	proto.RegisterType((*ChairGameApplyMsgOpt)(nil), "ga.channel_wedding_logic.ChairGameApplyMsgOpt")
	proto.RegisterType((*ChairGamePlayerOpt)(nil), "ga.channel_wedding_logic.ChairGamePlayerOpt")
	proto.RegisterType((*GetChairGameApplyListRequest)(nil), "ga.channel_wedding_logic.GetChairGameApplyListRequest")
	proto.RegisterType((*GetChairGameApplyListResponse)(nil), "ga.channel_wedding_logic.GetChairGameApplyListResponse")
	proto.RegisterType((*ChairGameRewardSetting)(nil), "ga.channel_wedding_logic.ChairGameRewardSetting")
	proto.RegisterType((*ChairGameRewardInfo)(nil), "ga.channel_wedding_logic.ChairGameRewardInfo")
	proto.RegisterType((*SetChairGameRewardRequest)(nil), "ga.channel_wedding_logic.SetChairGameRewardRequest")
	proto.RegisterType((*SetChairGameRewardResponse)(nil), "ga.channel_wedding_logic.SetChairGameRewardResponse")
	proto.RegisterType((*GetChairGameRewardSettingRequest)(nil), "ga.channel_wedding_logic.GetChairGameRewardSettingRequest")
	proto.RegisterType((*GetChairGameRewardSettingResponse)(nil), "ga.channel_wedding_logic.GetChairGameRewardSettingResponse")
	proto.RegisterType((*ChairGameProgress)(nil), "ga.channel_wedding_logic.ChairGameProgress")
	proto.RegisterType((*ChairGameInfo)(nil), "ga.channel_wedding_logic.ChairGameInfo")
	proto.RegisterType((*ChairGamePlayerInfo)(nil), "ga.channel_wedding_logic.ChairGamePlayerInfo")
	proto.RegisterType((*GetChairGameInfoRequest)(nil), "ga.channel_wedding_logic.GetChairGameInfoRequest")
	proto.RegisterType((*GetChairGameInfoResponse)(nil), "ga.channel_wedding_logic.GetChairGameInfoResponse")
	proto.RegisterType((*GrabChairRequest)(nil), "ga.channel_wedding_logic.GrabChairRequest")
	proto.RegisterType((*GrabChairResponse)(nil), "ga.channel_wedding_logic.GrabChairResponse")
	proto.RegisterType((*StartChairGameRequest)(nil), "ga.channel_wedding_logic.StartChairGameRequest")
	proto.RegisterType((*StartChairGameResponse)(nil), "ga.channel_wedding_logic.StartChairGameResponse")
	proto.RegisterType((*SetChairGameToNextRoundRequest)(nil), "ga.channel_wedding_logic.SetChairGameToNextRoundRequest")
	proto.RegisterType((*SetChairGameToNextRoundResponse)(nil), "ga.channel_wedding_logic.SetChairGameToNextRoundResponse")
	proto.RegisterType((*StartGrabChairRequest)(nil), "ga.channel_wedding_logic.StartGrabChairRequest")
	proto.RegisterType((*StartGrabChairResponse)(nil), "ga.channel_wedding_logic.StartGrabChairResponse")
	proto.RegisterType((*SimpleWeddingPlanInfo)(nil), "ga.channel_wedding_logic.SimpleWeddingPlanInfo")
	proto.RegisterType((*BigScreenImage)(nil), "ga.channel_wedding_logic.BigScreenImage")
	proto.RegisterType((*GetWeddingBigScreenRequest)(nil), "ga.channel_wedding_logic.GetWeddingBigScreenRequest")
	proto.RegisterType((*GetWeddingBigScreenResponse)(nil), "ga.channel_wedding_logic.GetWeddingBigScreenResponse")
	proto.RegisterType((*SaveWeddingBigScreenRequest)(nil), "ga.channel_wedding_logic.SaveWeddingBigScreenRequest")
	proto.RegisterType((*SaveWeddingBigScreenResponse)(nil), "ga.channel_wedding_logic.SaveWeddingBigScreenResponse")
	proto.RegisterType((*WeddingBigScreenChangeNotify)(nil), "ga.channel_wedding_logic.WeddingBigScreenChangeNotify")
	proto.RegisterType((*InviteCard)(nil), "ga.channel_wedding_logic.InviteCard")
	proto.RegisterType((*WeddingGuestGift)(nil), "ga.channel_wedding_logic.WeddingGuestGift")
	proto.RegisterType((*GetWeddingInviteInfoRequest)(nil), "ga.channel_wedding_logic.GetWeddingInviteInfoRequest")
	proto.RegisterType((*GetWeddingInviteInfoResponse)(nil), "ga.channel_wedding_logic.GetWeddingInviteInfoResponse")
	proto.RegisterType((*HandleWeddingInviteRequest)(nil), "ga.channel_wedding_logic.HandleWeddingInviteRequest")
	proto.RegisterType((*HandleWeddingInviteResponse)(nil), "ga.channel_wedding_logic.HandleWeddingInviteResponse")
	proto.RegisterType((*BuyWeddingRequest)(nil), "ga.channel_wedding_logic.BuyWeddingRequest")
	proto.RegisterType((*ReserveInfo)(nil), "ga.channel_wedding_logic.ReserveInfo")
	proto.RegisterType((*BuyWeddingResponse)(nil), "ga.channel_wedding_logic.BuyWeddingResponse")
	proto.RegisterType((*GetWeddingSchedulePageInfoRequest)(nil), "ga.channel_wedding_logic.GetWeddingSchedulePageInfoRequest")
	proto.RegisterType((*WeddingDressPreview)(nil), "ga.channel_wedding_logic.WeddingDressPreview")
	proto.RegisterType((*WeddingDressPreviewList)(nil), "ga.channel_wedding_logic.WeddingDressPreviewList")
	proto.RegisterType((*WeddingScenePreview)(nil), "ga.channel_wedding_logic.WeddingScenePreview")
	proto.RegisterType((*FinishWeddingAward)(nil), "ga.channel_wedding_logic.FinishWeddingAward")
	proto.RegisterType((*WeddingPriceInfo)(nil), "ga.channel_wedding_logic.WeddingPriceInfo")
	proto.RegisterType((*WeddingTheme)(nil), "ga.channel_wedding_logic.WeddingTheme")
	proto.RegisterMapType((map[uint32]*WeddingDressPreviewList)(nil), "ga.channel_wedding_logic.WeddingTheme.DressPreviewMapEntry")
	proto.RegisterType((*WeddingPartner)(nil), "ga.channel_wedding_logic.WeddingPartner")
	proto.RegisterType((*WeddingAnimationInfo)(nil), "ga.channel_wedding_logic.WeddingAnimationInfo")
	proto.RegisterType((*GetWeddingSchedulePageInfoResponse)(nil), "ga.channel_wedding_logic.GetWeddingSchedulePageInfoResponse")
	proto.RegisterType((*PayWeddingChannel)(nil), "ga.channel_wedding_logic.PayWeddingChannel")
	proto.RegisterType((*WeddingPaidNotify)(nil), "ga.channel_wedding_logic.WeddingPaidNotify")
	proto.RegisterType((*CancelWeddingRequest)(nil), "ga.channel_wedding_logic.CancelWeddingRequest")
	proto.RegisterType((*CancelWeddingResponse)(nil), "ga.channel_wedding_logic.CancelWeddingResponse")
	proto.RegisterType((*WeddingHallItem)(nil), "ga.channel_wedding_logic.WeddingHallItem")
	proto.RegisterType((*WeddingHallItem_WeddingHallItemGoingView)(nil), "ga.channel_wedding_logic.WeddingHallItem.WeddingHallItemGoingView")
	proto.RegisterType((*WeddingHallItem_WeddingHallItemNotStartView)(nil), "ga.channel_wedding_logic.WeddingHallItem.WeddingHallItemNotStartView")
	proto.RegisterType((*GetWeddingHallListRequest)(nil), "ga.channel_wedding_logic.GetWeddingHallListRequest")
	proto.RegisterType((*GetWeddingHallListResponse)(nil), "ga.channel_wedding_logic.GetWeddingHallListResponse")
	proto.RegisterType((*SubscribeWeddingRequest)(nil), "ga.channel_wedding_logic.SubscribeWeddingRequest")
	proto.RegisterType((*SubscribeWeddingResponse)(nil), "ga.channel_wedding_logic.SubscribeWeddingResponse")
	proto.RegisterType((*GetWeddingEntrySwitchRequest)(nil), "ga.channel_wedding_logic.GetWeddingEntrySwitchRequest")
	proto.RegisterType((*GetWeddingEntrySwitchResponse)(nil), "ga.channel_wedding_logic.GetWeddingEntrySwitchResponse")
	proto.RegisterType((*ApplyEndWeddingRelationshipRequest)(nil), "ga.channel_wedding_logic.ApplyEndWeddingRelationshipRequest")
	proto.RegisterType((*ApplyEndWeddingRelationshipResponse)(nil), "ga.channel_wedding_logic.ApplyEndWeddingRelationshipResponse")
	proto.RegisterType((*DirectEndWeddingRelationshipRequest)(nil), "ga.channel_wedding_logic.DirectEndWeddingRelationshipRequest")
	proto.RegisterType((*DirectEndWeddingRelationshipResponse)(nil), "ga.channel_wedding_logic.DirectEndWeddingRelationshipResponse")
	proto.RegisterType((*CancelEndWeddingRelationshipRequest)(nil), "ga.channel_wedding_logic.CancelEndWeddingRelationshipRequest")
	proto.RegisterType((*CancelEndWeddingRelationshipResponse)(nil), "ga.channel_wedding_logic.CancelEndWeddingRelationshipResponse")
	proto.RegisterType((*GetProposeListRequest)(nil), "ga.channel_wedding_logic.GetProposeListRequest")
	proto.RegisterType((*ProposeUser)(nil), "ga.channel_wedding_logic.ProposeUser")
	proto.RegisterType((*GetProposeListResponse)(nil), "ga.channel_wedding_logic.GetProposeListResponse")
	proto.RegisterType((*SendProposeRequest)(nil), "ga.channel_wedding_logic.SendProposeRequest")
	proto.RegisterType((*SendProposeResponse)(nil), "ga.channel_wedding_logic.SendProposeResponse")
	proto.RegisterType((*HandleProposeRequest)(nil), "ga.channel_wedding_logic.HandleProposeRequest")
	proto.RegisterType((*HandleProposeResponse)(nil), "ga.channel_wedding_logic.HandleProposeResponse")
	proto.RegisterType((*RevokeProposeRequest)(nil), "ga.channel_wedding_logic.RevokeProposeRequest")
	proto.RegisterType((*RevokeProposeResponse)(nil), "ga.channel_wedding_logic.RevokeProposeResponse")
	proto.RegisterType((*WeddingProposeInfo)(nil), "ga.channel_wedding_logic.WeddingProposeInfo")
	proto.RegisterType((*GetProposeByIdRequest)(nil), "ga.channel_wedding_logic.GetProposeByIdRequest")
	proto.RegisterType((*GetProposeByIdResponse)(nil), "ga.channel_wedding_logic.GetProposeByIdResponse")
	proto.RegisterType((*GetSendProposeRequest)(nil), "ga.channel_wedding_logic.GetSendProposeRequest")
	proto.RegisterType((*GetSendProposeResponse)(nil), "ga.channel_wedding_logic.GetSendProposeResponse")
	proto.RegisterType((*WeddingCertificate)(nil), "ga.channel_wedding_logic.WeddingCertificate")
	proto.RegisterType((*WeddingScenePic)(nil), "ga.channel_wedding_logic.WeddingScenePic")
	proto.RegisterType((*WeddingClipInfo)(nil), "ga.channel_wedding_logic.WeddingClipInfo")
	proto.RegisterType((*GetUserWeddingPrecipitationRequest)(nil), "ga.channel_wedding_logic.GetUserWeddingPrecipitationRequest")
	proto.RegisterType((*GetUserWeddingPrecipitationResponse)(nil), "ga.channel_wedding_logic.GetUserWeddingPrecipitationResponse")
	proto.RegisterType((*ReportWeddingScenePicRequest)(nil), "ga.channel_wedding_logic.ReportWeddingScenePicRequest")
	proto.RegisterType((*ReportWeddingScenePicResponse)(nil), "ga.channel_wedding_logic.ReportWeddingScenePicResponse")
	proto.RegisterType((*WeddingScenePicOpt)(nil), "ga.channel_wedding_logic.WeddingScenePicOpt")
	proto.RegisterType((*HideWeddingRelationRequest)(nil), "ga.channel_wedding_logic.HideWeddingRelationRequest")
	proto.RegisterType((*HideWeddingRelationResponse)(nil), "ga.channel_wedding_logic.HideWeddingRelationResponse")
	proto.RegisterType((*GetWeddingPreviewResourceRequest)(nil), "ga.channel_wedding_logic.GetWeddingPreviewResourceRequest")
	proto.RegisterType((*GetWeddingPreviewResourceResponse)(nil), "ga.channel_wedding_logic.GetWeddingPreviewResourceResponse")
	proto.RegisterType((*GetWeddingHighLightPresentRequest)(nil), "ga.channel_wedding_logic.GetWeddingHighLightPresentRequest")
	proto.RegisterType((*GetWeddingHighLightPresentResponse)(nil), "ga.channel_wedding_logic.GetWeddingHighLightPresentResponse")
	proto.RegisterType((*ConsultWeddingReserveIMMsg)(nil), "ga.channel_wedding_logic.ConsultWeddingReserveIMMsg")
	proto.RegisterType((*ArrangeWeddingReserveIMMsg)(nil), "ga.channel_wedding_logic.ArrangeWeddingReserveIMMsg")
	proto.RegisterType((*WeddingGiftInfo)(nil), "ga.channel_wedding_logic.WeddingGiftInfo")
	proto.RegisterType((*WeddingBridesmaidUpdateOpt)(nil), "ga.channel_wedding_logic.WeddingBridesmaidUpdateOpt")
	proto.RegisterType((*SendWeddingReservePresentRequest)(nil), "ga.channel_wedding_logic.SendWeddingReservePresentRequest")
	proto.RegisterType((*SendWeddingReservePresentResponse)(nil), "ga.channel_wedding_logic.SendWeddingReservePresentResponse")
	proto.RegisterType((*GetUserInRoomStatusRequest)(nil), "ga.channel_wedding_logic.GetUserInRoomStatusRequest")
	proto.RegisterType((*UserInRoomStatus)(nil), "ga.channel_wedding_logic.UserInRoomStatus")
	proto.RegisterType((*GetUserInRoomStatusResponse)(nil), "ga.channel_wedding_logic.GetUserInRoomStatusResponse")
	proto.RegisterType((*GetGoingWeddingEntryRequest)(nil), "ga.channel_wedding_logic.GetGoingWeddingEntryRequest")
	proto.RegisterType((*UserInfoWithChannel)(nil), "ga.channel_wedding_logic.UserInfoWithChannel")
	proto.RegisterType((*GetGoingWeddingEntryResponse)(nil), "ga.channel_wedding_logic.GetGoingWeddingEntryResponse")
	proto.RegisterType((*RemindUserJoinWeddingRoomRequest)(nil), "ga.channel_wedding_logic.RemindUserJoinWeddingRoomRequest")
	proto.RegisterType((*RemindUserJoinWeddingRoomResponse)(nil), "ga.channel_wedding_logic.RemindUserJoinWeddingRoomResponse")
	proto.RegisterType((*WeddingPresentVal)(nil), "ga.channel_wedding_logic.WeddingPresentVal")
	proto.RegisterType((*WeddingPresentLevel)(nil), "ga.channel_wedding_logic.WeddingPresentLevel")
	proto.RegisterType((*WeddingPresentCountInfo)(nil), "ga.channel_wedding_logic.WeddingPresentCountInfo")
	proto.RegisterType((*WeddingRecvPresentValChangeOpt)(nil), "ga.channel_wedding_logic.WeddingRecvPresentValChangeOpt")
	proto.RegisterType((*WeddingPresentValTopChangeOpt)(nil), "ga.channel_wedding_logic.WeddingPresentValTopChangeOpt")
	proto.RegisterType((*WeddingPresentValMvpChangeOpt)(nil), "ga.channel_wedding_logic.WeddingPresentValMvpChangeOpt")
	proto.RegisterType((*WeddingMvpUserSettlementNotify)(nil), "ga.channel_wedding_logic.WeddingMvpUserSettlementNotify")
	proto.RegisterType((*GetWeddingPresentRequest)(nil), "ga.channel_wedding_logic.GetWeddingPresentRequest")
	proto.RegisterType((*GetWeddingPresentResponse)(nil), "ga.channel_wedding_logic.GetWeddingPresentResponse")
	proto.RegisterType((*GetWeddingPreProgressInfoRequest)(nil), "ga.channel_wedding_logic.GetWeddingPreProgressInfoRequest")
	proto.RegisterType((*GetWeddingPreProgressInfoResponse)(nil), "ga.channel_wedding_logic.GetWeddingPreProgressInfoResponse")
	proto.RegisterType((*WeddingPreProgressStageCfg)(nil), "ga.channel_wedding_logic.WeddingPreProgressStageCfg")
	proto.RegisterType((*WeddingPrePreProgressStageDescGroup)(nil), "ga.channel_wedding_logic.WeddingPrePreProgressStageDescGroup")
	proto.RegisterType((*WeddingPreProgressStageDesc)(nil), "ga.channel_wedding_logic.WeddingPreProgressStageDesc")
	proto.RegisterType((*WeddingNewcomer)(nil), "ga.channel_wedding_logic.WeddingNewcomer")
	proto.RegisterType((*WeddingPreProgressInfo)(nil), "ga.channel_wedding_logic.WeddingPreProgressInfo")
	proto.RegisterType((*WeddingPreProgressInfoChangeOpt)(nil), "ga.channel_wedding_logic.WeddingPreProgressInfoChangeOpt")
	proto.RegisterType((*StartWeddingRequest)(nil), "ga.channel_wedding_logic.StartWeddingRequest")
	proto.RegisterType((*StartWeddingResponse)(nil), "ga.channel_wedding_logic.StartWeddingResponse")
	proto.RegisterType((*CancelPreparedWeddingRequest)(nil), "ga.channel_wedding_logic.CancelPreparedWeddingRequest")
	proto.RegisterType((*CancelPreparedWeddingResponse)(nil), "ga.channel_wedding_logic.CancelPreparedWeddingResponse")
	proto.RegisterType((*GetWeddingPrepareInfoRequest)(nil), "ga.channel_wedding_logic.GetWeddingPrepareInfoRequest")
	proto.RegisterType((*GetWeddingPrepareInfoResponse)(nil), "ga.channel_wedding_logic.GetWeddingPrepareInfoResponse")
	proto.RegisterEnum("ga.channel_wedding_logic.WeddingStage", WeddingStage_name, WeddingStage_value)
	proto.RegisterEnum("ga.channel_wedding_logic.GroupPhotoSubStage", GroupPhotoSubStage_name, GroupPhotoSubStage_value)
	proto.RegisterEnum("ga.channel_wedding_logic.WeddingScene", WeddingScene_name, WeddingScene_value)
	proto.RegisterEnum("ga.channel_wedding_logic.WeddingGuestType", WeddingGuestType_name, WeddingGuestType_value)
	proto.RegisterEnum("ga.channel_wedding_logic.ChairRoundStatus", ChairRoundStatus_name, ChairRoundStatus_value)
	proto.RegisterEnum("ga.channel_wedding_logic.WeddingInviteType", WeddingInviteType_name, WeddingInviteType_value)
	proto.RegisterEnum("ga.channel_wedding_logic.WeddingInviteStatus", WeddingInviteStatus_name, WeddingInviteStatus_value)
	proto.RegisterEnum("ga.channel_wedding_logic.WeddingPriceType", WeddingPriceType_name, WeddingPriceType_value)
	proto.RegisterEnum("ga.channel_wedding_logic.WeddingAnimationType", WeddingAnimationType_name, WeddingAnimationType_value)
	proto.RegisterEnum("ga.channel_wedding_logic.WeddingLevelType", WeddingLevelType_name, WeddingLevelType_value)
	proto.RegisterEnum("ga.channel_wedding_logic.WeddingChargeType", WeddingChargeType_name, WeddingChargeType_value)
	proto.RegisterEnum("ga.channel_wedding_logic.WeddingTimeStatus", WeddingTimeStatus_name, WeddingTimeStatus_value)
	proto.RegisterEnum("ga.channel_wedding_logic.WeddingSubscribeStatus", WeddingSubscribeStatus_name, WeddingSubscribeStatus_value)
	proto.RegisterEnum("ga.channel_wedding_logic.EndRelationShipSource", EndRelationShipSource_name, EndRelationShipSource_value)
	proto.RegisterEnum("ga.channel_wedding_logic.ProposeStatus", ProposeStatus_name, ProposeStatus_value)
	proto.RegisterEnum("ga.channel_wedding_logic.HideOpType", HideOpType_name, HideOpType_value)
	proto.RegisterEnum("ga.channel_wedding_logic.WeddingPreviewResourceScene", WeddingPreviewResourceScene_name, WeddingPreviewResourceScene_value)
	proto.RegisterEnum("ga.channel_wedding_logic.WeddingReserveIMStatus", WeddingReserveIMStatus_name, WeddingReserveIMStatus_value)
	proto.RegisterEnum("ga.channel_wedding_logic.WeddingPreProgressStage", WeddingPreProgressStage_name, WeddingPreProgressStage_value)
	proto.RegisterEnum("ga.channel_wedding_logic.SaveWeddingBigScreenRequest_BigScreenOperation", SaveWeddingBigScreenRequest_BigScreenOperation_name, SaveWeddingBigScreenRequest_BigScreenOperation_value)
}

func init() {
	proto.RegisterFile("channel_wedding_logic/channel_wedding_logic.proto", fileDescriptor_channel_wedding_logic_c0fde01e342fd719)
}

var fileDescriptor_channel_wedding_logic_c0fde01e342fd719 = []byte{
	// 9709 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x7d, 0x6d, 0x6c, 0x24, 0x49,
	0x96, 0xd0, 0x65, 0x95, 0x3f, 0x5f, 0xb9, 0xec, 0x72, 0xda, 0xee, 0x76, 0x7f, 0x4d, 0x77, 0x67,
	0xf7, 0xcc, 0x74, 0x7b, 0x66, 0xba, 0x67, 0x7b, 0xa7, 0x77, 0x6f, 0xef, 0x6b, 0xd7, 0x2e, 0x57,
	0xdb, 0x35, 0xf8, 0xa3, 0x36, 0xcb, 0xee, 0x9e, 0xfd, 0xb8, 0xc9, 0x4d, 0x57, 0x86, 0xcb, 0x79,
	0x53, 0x95, 0x99, 0x93, 0x99, 0xe5, 0xee, 0x5a, 0xd0, 0xdd, 0x21, 0x2d, 0xe8, 0x96, 0x3b, 0x38,
	0xed, 0x9d, 0x90, 0x16, 0xb4, 0x12, 0x02, 0x21, 0x74, 0xc7, 0xb2, 0x08, 0x2d, 0xe2, 0xe3, 0x00,
	0x9d, 0x04, 0x5a, 0x38, 0x40, 0x02, 0x84, 0x60, 0x25, 0x10, 0x12, 0x3f, 0xe1, 0x0f, 0x08, 0x04,
	0xfc, 0x42, 0xfc, 0x40, 0xf1, 0xe2, 0x23, 0x23, 0xb3, 0xb2, 0xec, 0x2a, 0x77, 0xf7, 0xde, 0xfd,
	0xab, 0x8c, 0x78, 0x11, 0xf1, 0xe2, 0xc5, 0x7b, 0x2f, 0xde, 0x8b, 0x78, 0xf1, 0x0a, 0x3e, 0xd3,
	0x3a, 0xb1, 0x3d, 0x8f, 0x74, 0xac, 0xe7, 0xc4, 0x71, 0x5c, 0xaf, 0x6d, 0x75, 0xfc, 0xb6, 0xdb,
	0x7a, 0x98, 0x5b, 0xfa, 0x20, 0x08, 0xfd, 0xd8, 0xd7, 0x57, 0xdb, 0xf6, 0x83, 0xdc, 0xfa, 0xab,
	0xe5, 0xb6, 0x6d, 0x1d, 0xd9, 0x11, 0x61, 0x80, 0x46, 0x0b, 0x16, 0x9e, 0xb1, 0xfa, 0x66, 0x6c,
	0xb7, 0x49, 0xf5, 0xb8, 0xad, 0x2f, 0xc3, 0x64, 0x44, 0x7f, 0xaf, 0x6a, 0xb7, 0xb4, 0x7b, 0x65,
	0x93, 0x7d, 0xe8, 0x37, 0x00, 0xf0, 0x87, 0xe5, 0xd9, 0x5d, 0xb2, 0x5a, 0xb8, 0xa5, 0xdd, 0x9b,
	0x35, 0x67, 0xb1, 0x64, 0xcf, 0xee, 0x12, 0xfd, 0x1a, 0xcc, 0x46, 0xbd, 0x23, 0x8b, 0x35, 0x2c,
	0x62, 0xc3, 0x99, 0xa8, 0x77, 0x84, 0x9d, 0x1a, 0xff, 0x43, 0x83, 0x8a, 0x3a, 0x4a, 0xdd, 0x3b,
	0xf6, 0xf5, 0x7d, 0x98, 0x67, 0x1d, 0xb6, 0x8e, 0xdb, 0x56, 0xc7, 0x8d, 0xe2, 0x55, 0xed, 0x56,
	0xf1, 0x5e, 0xe9, 0xd1, 0xfd, 0x07, 0xc3, 0x70, 0x7f, 0x90, 0xc1, 0xd4, 0x9c, 0x8b, 0xf8, 0xaf,
	0x1d, 0x37, 0x8a, 0x29, 0x86, 0xad, 0x5e, 0x18, 0x72, 0x1c, 0x0a, 0x88, 0xc3, 0x2c, 0x2d, 0x41,
	0xf8, 0x33, 0x31, 0xd4, 0xef, 0x41, 0x85, 0x21, 0x73, 0x44, 0xda, 0xae, 0x67, 0xc5, 0x6e, 0x97,
	0xac, 0x4e, 0xdc, 0xd2, 0xee, 0x15, 0x4d, 0x86, 0xe4, 0x06, 0x2d, 0x3e, 0x70, 0xbb, 0x44, 0xbf,
	0x2b, 0xd0, 0x26, 0x9e, 0xc3, 0xe0, 0x26, 0x11, 0x8e, 0xe1, 0x52, 0xf3, 0x1c, 0x0a, 0x65, 0xfc,
	0x5b, 0x0d, 0x96, 0x38, 0xb6, 0x3b, 0xe4, 0x94, 0x74, 0xaa, 0x1d, 0x3f, 0x3e, 0x21, 0x11, 0xa5,
	0x6d, 0x87, 0x7e, 0x0b, 0xda, 0xe2, 0x87, 0x7e, 0x07, 0xca, 0xed, 0xd0, 0xf7, 0xbb, 0x56, 0x8b,
	0x81, 0xad, 0x16, 0x6e, 0x15, 0xef, 0x95, 0xcd, 0x39, 0x2c, 0x14, 0x4d, 0xef, 0x40, 0xf9, 0x28,
	0x74, 0x1d, 0x22, 0x81, 0x8a, 0x0c, 0x08, 0x0b, 0x05, 0xd0, 0x3b, 0xb0, 0x88, 0x8d, 0xa2, 0xae,
	0xed, 0x49, 0xc0, 0x09, 0x04, 0xac, 0xc8, 0x0a, 0x01, 0xfc, 0x1e, 0xe8, 0xd8, 0x38, 0xea, 0xda,
	0xae, 0x23, 0xa1, 0x27, 0x11, 0x7a, 0x31, 0xa9, 0xe1, 0xe0, 0xc6, 0xf7, 0x0b, 0x72, 0x4e, 0xcd,
	0x16, 0xf1, 0xc8, 0x86, 0xef, 0x09, 0x7e, 0xc9, 0x99, 0x13, 0x25, 0x37, 0xf9, 0xd4, 0x72, 0x3d,
	0x87, 0xbc, 0xe0, 0x8b, 0x31, 0x13, 0x91, 0x4f, 0xeb, 0xf4, 0x5b, 0x7f, 0x13, 0xe6, 0x6d, 0xcf,
	0xed, 0xda, 0xb1, 0xeb, 0x7b, 0x8c, 0xa1, 0x8a, 0xc8, 0x50, 0x65, 0x59, 0x8a, 0x4c, 0x75, 0x19,
	0xa6, 0x8f, 0x7c, 0x8f, 0x58, 0xae, 0x83, 0x8b, 0x51, 0x36, 0xa7, 0xe8, 0x67, 0xdd, 0xd1, 0x6f,
	0xc1, 0x1c, 0xe5, 0x61, 0x4b, 0xd4, 0x4e, 0x62, 0x2d, 0xd0, 0xb2, 0x0d, 0x06, 0xf1, 0x26, 0x2c,
	0xb4, 0x02, 0xcb, 0x8d, 0x49, 0xd7, 0x72, 0x1d, 0xc6, 0x5e, 0x53, 0x8c, 0x5e, 0xad, 0xa0, 0x1e,
	0x93, 0x6e, 0xdd, 0x41, 0x9e, 0xb9, 0x0f, 0x8b, 0x5d, 0xbb, 0x43, 0xd2, 0x80, 0xd3, 0x08, 0x38,
	0x4f, 0x2b, 0x14, 0xd0, 0xf7, 0x60, 0xe9, 0x98, 0x0c, 0x02, 0xcf, 0x30, 0xe2, 0xb2, 0xaa, 0x04,
	0xdc, 0xf8, 0x37, 0x5a, 0x22, 0x59, 0x94, 0x5a, 0x42, 0xb2, 0xe8, 0x6f, 0x29, 0x59, 0xf4, 0x83,
	0x12, 0x03, 0x7f, 0x58, 0x21, 0x89, 0xfc, 0x5e, 0xd8, 0x12, 0xd2, 0x55, 0xc6, 0x52, 0x93, 0x17,
	0xea, 0xef, 0x82, 0x9e, 0x06, 0xb3, 0xba, 0xce, 0x63, 0x4e, 0xb7, 0x4a, 0x0a, 0x74, 0xd7, 0x79,
	0xac, 0x7f, 0x19, 0xca, 0x48, 0x1c, 0x29, 0x5c, 0x13, 0x28, 0x5c, 0xef, 0x9d, 0x2f, 0x5c, 0xca,
	0xd2, 0x9a, 0xa5, 0x23, 0xf6, 0x03, 0x67, 0xf4, 0x2b, 0xa0, 0x6f, 0xdb, 0x41, 0xe0, 0x7a, 0x24,
	0x8a, 0x90, 0xa9, 0x51, 0x8c, 0xf3, 0x57, 0xff, 0x26, 0x94, 0xf0, 0x87, 0x75, 0x6a, 0x77, 0x7a,
	0x42, 0x18, 0x01, 0x8b, 0x9e, 0xd2, 0x12, 0x5d, 0x87, 0x09, 0xb7, 0xe5, 0x7b, 0x1c, 0x7f, 0xfc,
	0xad, 0xbf, 0x01, 0x70, 0x64, 0xb7, 0x3e, 0x69, 0x87, 0x7e, 0xcf, 0x63, 0x2b, 0x3e, 0x6b, 0x2a,
	0x25, 0xc6, 0xaf, 0x6a, 0xb0, 0x24, 0x31, 0xa8, 0xfa, 0xde, 0xb1, 0xdb, 0x46, 0x14, 0x36, 0x61,
	0xaa, 0x85, 0x5f, 0x5c, 0x83, 0xbc, 0x3b, 0x7c, 0x92, 0x83, 0x13, 0x30, 0x79, 0x5b, 0x2a, 0x5f,
	0xbd, 0xa0, 0x1d, 0xda, 0x0e, 0xb1, 0x4e, 0x5d, 0x87, 0xf8, 0x7c, 0x15, 0xe6, 0x78, 0xe1, 0x53,
	0x5a, 0x66, 0x7c, 0xbb, 0x00, 0x57, 0x54, 0xb9, 0xde, 0x90, 0xd8, 0x0d, 0x97, 0x04, 0x2a, 0x66,
	0x12, 0xcc, 0x0a, 0xdc, 0x56, 0xdc, 0x0b, 0xc5, 0x1a, 0x2f, 0x26, 0x35, 0x0d, 0x56, 0x41, 0xd7,
	0x59, 0x01, 0xef, 0x06, 0x1f, 0x58, 0xbd, 0xb0, 0x23, 0xd6, 0x39, 0xa9, 0xd9, 0x0d, 0x3e, 0x38,
	0x0c, 0x3b, 0xfa, 0xcf, 0xc1, 0xd5, 0x28, 0x20, 0x2d, 0xd7, 0xee, 0x58, 0x39, 0x83, 0x30, 0x1a,
	0xae, 0x72, 0x88, 0x8d, 0x81, 0xb1, 0x7e, 0x36, 0xb7, 0xb5, 0x18, 0x73, 0x12, 0x5b, 0x5f, 0x1e,
	0x68, 0xcd, 0x86, 0x36, 0x7e, 0x3f, 0xe1, 0x70, 0xc9, 0xa4, 0xb7, 0x61, 0x4e, 0xb2, 0x27, 0xed,
	0x42, 0xc3, 0x2e, 0x4a, 0xa2, 0x8c, 0x62, 0xac, 0x82, 0x50, 0x0e, 0x2e, 0xa4, 0x41, 0x28, 0xf3,
	0x5e, 0x07, 0x68, 0x05, 0x52, 0xb8, 0xb9, 0xae, 0x6e, 0x05, 0x1b, 0x52, 0xf8, 0x53, 0x12, 0xc8,
	0xd4, 0x1b, 0xb8, 0x89, 0xa8, 0xbe, 0x09, 0x0b, 0xa8, 0x1e, 0x94, 0x4e, 0x98, 0x86, 0x40, 0xad,
	0x51, 0xe5, 0x1d, 0x19, 0xdf, 0x9d, 0x92, 0x0a, 0xcd, 0xf4, 0xfd, 0xee, 0xc1, 0x09, 0xe9, 0xa2,
	0x98, 0x5e, 0x81, 0x99, 0x98, 0xfe, 0xa6, 0xed, 0xd8, 0x4a, 0x4e, 0xe3, 0x37, 0xaa, 0x95, 0x79,
	0x56, 0x95, 0x95, 0x55, 0x2c, 0x55, 0x65, 0x35, 0x0d, 0xa6, 0xca, 0x6a, 0x0a, 0x94, 0x4e, 0x77,
	0x5f, 0x28, 0x80, 0x8c, 0xb0, 0xde, 0x1f, 0x4d, 0x58, 0xd9, 0x4e, 0xc8, 0x7f, 0xe1, 0xfc, 0x1f,
	0xc1, 0x52, 0xeb, 0xc4, 0x76, 0x43, 0xab, 0x6d, 0xab, 0xa8, 0xe2, 0x7a, 0x6e, 0x14, 0x56, 0x35,
	0x73, 0x11, 0xab, 0xb7, 0x6c, 0x05, 0xe5, 0x2f, 0xc0, 0xe5, 0x9c, 0x36, 0x88, 0xf7, 0x94, 0x6c,
	0xb7, 0x3c, 0xd0, 0x8e, 0xe2, 0xff, 0x35, 0xd0, 0x99, 0xb0, 0xf3, 0x2d, 0x24, 0xd1, 0xa2, 0xa3,
	0x28, 0x1c, 0x75, 0x7f, 0x34, 0x2b, 0x1d, 0xe5, 0x0b, 0xe7, 0xd2, 0x86, 0x15, 0xd6, 0xb9, 0xc2,
	0xa0, 0x52, 0xf1, 0x96, 0x1e, 0x7d, 0x76, 0xb4, 0xfe, 0x53, 0x72, 0x6a, 0x2e, 0x75, 0xd2, 0x65,
	0x38, 0x50, 0x0b, 0x56, 0x45, 0xfb, 0x20, 0x24, 0xa7, 0x2e, 0x79, 0x9e, 0x50, 0x6e, 0xf6, 0x96,
	0x36, 0xd2, 0x7a, 0x08, 0xaa, 0x98, 0x97, 0x78, 0x75, 0x83, 0xf5, 0x24, 0xa9, 0xfc, 0x00, 0x18,
	0xe9, 0x13, 0x02, 0xbb, 0xce, 0x2a, 0x50, 0x1e, 0x43, 0xfa, 0x2e, 0x60, 0xa5, 0x80, 0xae, 0x3b,
	0xba, 0x01, 0x65, 0x37, 0xb2, 0x8e, 0x43, 0x42, 0x2c, 0x64, 0x9b, 0xd5, 0xd2, 0x2d, 0xed, 0xde,
	0x8c, 0x59, 0x72, 0xa3, 0x27, 0x21, 0x21, 0xc8, 0xb1, 0xba, 0x09, 0x65, 0xd9, 0x27, 0x65, 0xa1,
	0xd5, 0x39, 0xc4, 0xf6, 0xc1, 0x70, 0x6c, 0xab, 0xd9, 0x55, 0x44, 0x5d, 0x2f, 0xc6, 0xae, 0x1e,
	0xb7, 0x8d, 0xdf, 0x2b, 0xc0, 0x72, 0x1e, 0x14, 0xdd, 0xd6, 0xd9, 0x60, 0x81, 0xdb, 0xe2, 0xd2,
	0x3d, 0x83, 0x05, 0x0d, 0xb7, 0xa5, 0x7f, 0x16, 0x2e, 0x45, 0x6e, 0x1c, 0x23, 0x09, 0xfd, 0x88,
	0x58, 0x62, 0xbf, 0x74, 0xf8, 0x06, 0xb0, 0xc4, 0x6b, 0x1b, 0x7e, 0x44, 0x9e, 0xb0, 0x0d, 0xd3,
	0xd1, 0x1f, 0xc2, 0x72, 0xaa, 0x91, 0x68, 0xc2, 0xc4, 0x7e, 0x51, 0x69, 0xb2, 0xcb, 0x1a, 0xac,
	0xc1, 0x62, 0x14, 0xdb, 0x9e, 0x73, 0xd4, 0x57, 0x06, 0x60, 0xf6, 0xc1, 0x02, 0xaf, 0x90, 0x9d,
	0xbf, 0x05, 0xa2, 0x48, 0xf6, 0xcb, 0x34, 0x41, 0x99, 0x17, 0xef, 0x4a, 0xb8, 0x63, 0xdb, 0xed,
	0x24, 0x1d, 0x46, 0xdc, 0x5c, 0x28, 0xd3, 0x62, 0xd1, 0x5d, 0x44, 0xd7, 0x03, 0xe1, 0x24, 0x14,
	0xb3, 0x15, 0x4a, 0xb4, 0x90, 0x75, 0x15, 0x19, 0x4f, 0xa4, 0xb1, 0x5b, 0x0d, 0x76, 0x49, 0x17,
	0xb7, 0xa8, 0x47, 0x30, 0xd7, 0x8b, 0x48, 0x68, 0x05, 0xa1, 0x7f, 0xec, 0x76, 0x98, 0x01, 0x50,
	0x7a, 0xb4, 0x40, 0x97, 0xe8, 0x30, 0x22, 0x61, 0x83, 0x15, 0x9b, 0xa5, 0x5e, 0xf2, 0x61, 0xfc,
	0x35, 0x0d, 0x96, 0x79, 0x47, 0xbb, 0xa4, 0xeb, 0x87, 0xae, 0xdd, 0xc1, 0x4d, 0xe8, 0x15, 0x29,
	0xd9, 0x3b, 0x50, 0x96, 0x20, 0xbf, 0x14, 0xc9, 0xad, 0x58, 0xb6, 0xfb, 0x30, 0xf2, 0x3d, 0xdc,
	0x14, 0x11, 0x6f, 0xb6, 0x61, 0x30, 0x5b, 0x92, 0x6e, 0x8a, 0x14, 0x4f, 0x5e, 0x66, 0xfc, 0x50,
	0x83, 0x79, 0x8e, 0xa8, 0xb0, 0x09, 0x6f, 0xc1, 0x1c, 0x92, 0x48, 0xa8, 0x5f, 0xa6, 0x46, 0x81,
	0x96, 0x71, 0x2d, 0x7e, 0x17, 0xe6, 0x39, 0xb1, 0x05, 0x0c, 0xe3, 0x91, 0x39, 0x56, 0xca, 0xa1,
	0xee, 0xc3, 0x22, 0x6a, 0xf2, 0x14, 0x20, 0xe3, 0x8c, 0x79, 0x5a, 0xb1, 0x9b, 0x80, 0xbe, 0x07,
	0x4b, 0x08, 0x9a, 0xe9, 0x95, 0x31, 0x46, 0x85, 0x56, 0x3d, 0x51, 0x7a, 0x36, 0x7e, 0x67, 0x0a,
	0x4a, 0x1c, 0x69, 0x5c, 0xa1, 0x1b, 0x00, 0x42, 0x48, 0x38, 0xbe, 0x45, 0x73, 0x96, 0x97, 0xd4,
	0x1d, 0xbd, 0x2e, 0xdc, 0x1f, 0xd7, 0x3b, 0x66, 0xa6, 0x41, 0xe9, 0xd1, 0xda, 0x68, 0x9e, 0x0a,
	0x5a, 0x19, 0xcc, 0x55, 0xc2, 0x91, 0x3e, 0x84, 0x59, 0xb6, 0x39, 0x50, 0x59, 0x2d, 0x62, 0x4f,
	0xe7, 0x6b, 0x49, 0x75, 0x83, 0x32, 0xd9, 0xf6, 0x44, 0xe9, 0x2c, 0x7c, 0x1e, 0x66, 0x76, 0x4c,
	0x24, 0x3e, 0x0f, 0x6a, 0x3d, 0xfd, 0x4b, 0x30, 0x89, 0x76, 0x3c, 0x32, 0xfd, 0x28, 0x08, 0x4b,
	0x8e, 0x35, 0x59, 0x43, 0xda, 0x03, 0xfa, 0x0d, 0xb8, 0x09, 0x8c, 0xd9, 0x03, 0x36, 0xe4, 0x8e,
	0x63, 0x18, 0x33, 0x67, 0x69, 0x9a, 0x11, 0x16, 0x4b, 0xd0, 0x9f, 0xba, 0x02, 0x33, 0xd2, 0x93,
	0x9a, 0xc1, 0xca, 0x69, 0xc2, 0x9c, 0x28, 0xdd, 0x01, 0xa1, 0x46, 0xad, 0x2e, 0x17, 0x00, 0x6e,
	0x9a, 0xcd, 0x9e, 0xa7, 0xe1, 0xf2, 0xe4, 0xc6, 0x5c, 0x7e, 0x9e, 0x27, 0x4d, 0xf7, 0xa0, 0xa2,
	0x6c, 0x7c, 0xc4, 0x8b, 0xc3, 0x3e, 0x6a, 0xe4, 0x19, 0x73, 0x5e, 0xee, 0x76, 0x35, 0x5a, 0xaa,
	0x3f, 0x80, 0x25, 0xc5, 0x5f, 0xa2, 0x1e, 0x16, 0x6e, 0x44, 0xa5, 0xac, 0xc3, 0xb4, 0x6b, 0x7b,
	0xb8, 0xa3, 0x7c, 0x04, 0x95, 0x13, 0x61, 0x6f, 0x5a, 0xdc, 0x42, 0x9d, 0x3b, 0x6f, 0xbd, 0x73,
	0x0c, 0x5c, 0x73, 0xe1, 0x24, 0x5d, 0xa8, 0xbf, 0x0d, 0x49, 0x11, 0x37, 0xb1, 0xcb, 0x4c, 0x28,
	0x64, 0x31, 0x33, 0xb3, 0xab, 0x30, 0x23, 0xdc, 0x80, 0xd5, 0x79, 0x1c, 0xfa, 0xde, 0xb9, 0x44,
	0x13, 0xc6, 0xff, 0x34, 0x37, 0xfe, 0x8d, 0x00, 0x74, 0xb1, 0xb8, 0x6c, 0x63, 0x46, 0x36, 0xae,
	0x40, 0xb1, 0x27, 0x25, 0x9b, 0xfe, 0xa4, 0x4a, 0x54, 0x58, 0x00, 0xc2, 0x36, 0x63, 0x8e, 0x6c,
	0x99, 0x17, 0x73, 0xf3, 0xec, 0x16, 0x94, 0xfc, 0xd0, 0x25, 0x5e, 0x8c, 0x9e, 0x1e, 0x17, 0x67,
	0xb5, 0xc8, 0x78, 0x01, 0xb7, 0x37, 0xec, 0xb8, 0x75, 0xb2, 0x45, 0x62, 0xaa, 0x1e, 0xd3, 0xa3,
	0x9b, 0xe4, 0xd3, 0x1e, 0x89, 0x62, 0xfd, 0x2d, 0x98, 0x41, 0x81, 0x0f, 0xc9, 0xa7, 0x5c, 0x9f,
	0x96, 0xe8, 0xdc, 0x36, 0xec, 0x88, 0x98, 0xe4, 0x53, 0x73, 0xfa, 0x88, 0xfd, 0xa0, 0x88, 0xb6,
	0xa4, 0x7a, 0xa1, 0x3f, 0x29, 0xcf, 0xf5, 0x04, 0x86, 0xcc, 0x8b, 0x9e, 0xee, 0xb9, 0xcc, 0x6d,
	0xfb, 0x5d, 0x0d, 0x8c, 0xb3, 0x86, 0x8e, 0x02, 0xdf, 0x8b, 0x88, 0x7e, 0x1f, 0x66, 0xf9, 0xd8,
	0x51, 0xc0, 0x07, 0x9f, 0x4b, 0x06, 0x8f, 0x02, 0x73, 0xe6, 0x88, 0xff, 0xd2, 0x3f, 0x82, 0x45,
	0x49, 0x15, 0xef, 0xd8, 0x4f, 0xe8, 0x72, 0xa6, 0xa3, 0x32, 0x48, 0x70, 0x53, 0x10, 0x97, 0x7e,
	0x20, 0xae, 0xdf, 0xd7, 0xe0, 0x72, 0x1a, 0xae, 0x7a, 0x62, 0x7b, 0x6d, 0xb2, 0x1f, 0xc4, 0x62,
	0xd2, 0x5a, 0x32, 0xe9, 0xbb, 0x30, 0x1f, 0x91, 0xf0, 0x94, 0x84, 0x28, 0x6b, 0x56, 0x37, 0x42,
	0x8a, 0x14, 0xcd, 0x39, 0x56, 0x4a, 0x25, 0x6e, 0x37, 0xca, 0xc7, 0xb6, 0xf8, 0x2a, 0xb0, 0xfd,
	0xdf, 0x1a, 0xac, 0xa4, 0x0e, 0x70, 0x5e, 0x1a, 0xd7, 0x6d, 0x98, 0x93, 0x2a, 0x9b, 0x6a, 0x65,
	0xa6, 0x4b, 0xdf, 0x3c, 0x17, 0x4d, 0xc4, 0xaf, 0xf4, 0x5c, 0x51, 0xfe, 0xb9, 0xb3, 0x9e, 0x78,
	0x15, 0xb3, 0xfe, 0xe7, 0xc9, 0xac, 0x99, 0xa1, 0xfb, 0xd2, 0xb3, 0x4e, 0xab, 0xfc, 0x62, 0x56,
	0xe5, 0xbf, 0xbe, 0xa9, 0xfc, 0xab, 0x42, 0xb2, 0x80, 0xd4, 0xdb, 0xd8, 0xf3, 0x63, 0xf7, 0xb8,
	0xff, 0x32, 0x53, 0x79, 0x02, 0xb3, 0xd2, 0xf1, 0xe1, 0xab, 0x37, 0x86, 0xcf, 0x33, 0x23, 0x7c,
	0x9e, 0xf3, 0x76, 0xc1, 0xcb, 0x30, 0x4d, 0x5e, 0xc4, 0x96, 0x1f, 0xc4, 0xdc, 0xa5, 0x9d, 0x22,
	0x2f, 0x62, 0x8a, 0xb7, 0x01, 0x65, 0x5e, 0x61, 0x1d, 0xf5, 0x63, 0x12, 0xe1, 0x26, 0x37, 0x67,
	0x96, 0x58, 0xf5, 0x06, 0x2d, 0xd2, 0x3f, 0x82, 0x85, 0x90, 0x20, 0xd6, 0xd4, 0x2d, 0x88, 0x88,
	0x17, 0xe3, 0x1e, 0x56, 0x7a, 0xf4, 0x70, 0x14, 0x6f, 0x80, 0xb6, 0x6b, 0xb0, 0x66, 0xe6, 0x7c,
	0x98, 0xfa, 0x36, 0x7e, 0x5a, 0x1e, 0x25, 0xe0, 0x94, 0xb6, 0x42, 0xbf, 0x17, 0x34, 0x4e, 0xfc,
	0xd8, 0xa7, 0xa8, 0x5d, 0x83, 0xd9, 0x80, 0xfe, 0x56, 0x0c, 0xbc, 0x19, 0x2c, 0xa0, 0x9e, 0xf7,
	0x53, 0x58, 0x55, 0x5b, 0x6e, 0xbb, 0xed, 0x93, 0x1d, 0xb7, 0x7d, 0x82, 0x73, 0xba, 0x01, 0xc0,
	0xf1, 0x4c, 0xec, 0xae, 0x59, 0x5e, 0x52, 0x77, 0x68, 0xbf, 0x8e, 0xdd, 0xb7, 0x5a, 0x7e, 0xcf,
	0x8b, 0xc5, 0xb1, 0x9c, 0x63, 0xf7, 0xab, 0xf4, 0xdb, 0x68, 0xca, 0x05, 0x4e, 0xa3, 0x3e, 0x42,
	0xa7, 0x47, 0xbd, 0x3e, 0x09, 0xad, 0x9e, 0xd4, 0xb3, 0x33, 0x58, 0x70, 0xe8, 0x3a, 0xc6, 0x0f,
	0x34, 0x39, 0xcf, 0x64, 0x6f, 0x7b, 0x69, 0x29, 0x78, 0x1f, 0x96, 0x71, 0xc9, 0xb3, 0xdb, 0x20,
	0x93, 0x07, 0x9d, 0xd6, 0x6d, 0xa7, 0xb7, 0xc2, 0xb7, 0x60, 0xc1, 0xa3, 0xab, 0xcd, 0xbc, 0xc9,
	0xd8, 0x0d, 0x22, 0xce, 0x29, 0x65, 0x5a, 0x8c, 0x9c, 0x72, 0xe0, 0x06, 0x91, 0xf1, 0x11, 0x5c,
	0xdf, 0x22, 0x71, 0x95, 0x2d, 0xac, 0xaa, 0x31, 0x5e, 0x76, 0xdb, 0x31, 0x7e, 0x5c, 0x80, 0x1b,
	0x43, 0xba, 0x1e, 0x7f, 0x5b, 0xc9, 0x2a, 0xbf, 0xc2, 0x85, 0x95, 0xdf, 0x03, 0x58, 0x7a, 0x4e,
	0x3a, 0x2d, 0xbf, 0x4b, 0xac, 0x98, 0x12, 0x28, 0x08, 0xc9, 0xb1, 0xfb, 0x82, 0xbb, 0x03, 0x8b,
	0xbc, 0xea, 0x80, 0xbc, 0x88, 0x1b, 0x58, 0x31, 0x00, 0x1f, 0xf5, 0x8e, 0x29, 0xfc, 0xc4, 0x00,
	0x7c, 0x13, 0x2b, 0x74, 0x0b, 0x74, 0xc1, 0x3c, 0xc8, 0x76, 0x0c, 0x5f, 0x66, 0x91, 0x7e, 0xe6,
	0x5c, 0x7c, 0x39, 0x0b, 0x22, 0x83, 0x22, 0xee, 0x95, 0x20, 0x53, 0x62, 0xfc, 0x9a, 0x06, 0x57,
	0x9a, 0xcf, 0xdd, 0xb8, 0x75, 0xa2, 0xee, 0x2f, 0x2f, 0x6f, 0x26, 0xc8, 0x8b, 0x90, 0xa2, 0x7a,
	0x11, 0x92, 0xba, 0x47, 0x98, 0xc8, 0xdc, 0x74, 0x6c, 0xc1, 0xd5, 0x3c, 0x4c, 0xc6, 0x5e, 0x5e,
	0xe3, 0xb7, 0x35, 0xb8, 0x7e, 0x60, 0x7f, 0x42, 0x78, 0x3f, 0x89, 0x72, 0x78, 0xf9, 0x69, 0x49,
	0xd5, 0x12, 0xf6, 0xc4, 0xb9, 0x22, 0x53, 0x2d, 0x66, 0xaf, 0xa3, 0x5f, 0x87, 0xd9, 0xc8, 0x6d,
	0x7b, 0xb6, 0x72, 0x7c, 0x98, 0x14, 0x18, 0x1f, 0xc2, 0x8d, 0x21, 0x48, 0x8d, 0x3f, 0xc3, 0x43,
	0xb8, 0x92, 0xb6, 0xb1, 0xa8, 0x8b, 0xff, 0xf2, 0x42, 0xf6, 0x03, 0x0d, 0xae, 0xe6, 0xf5, 0x3b,
	0xbe, 0x84, 0xdd, 0x82, 0x39, 0x3c, 0x90, 0x48, 0xdb, 0xb2, 0x40, 0xcb, 0xa4, 0x21, 0x3b, 0x87,
	0x4a, 0x88, 0x83, 0x71, 0x3e, 0xc1, 0xbd, 0xa8, 0x81, 0x50, 0x59, 0x53, 0x77, 0x62, 0xd0, 0xd4,
	0x8d, 0xe0, 0x8d, 0x26, 0x43, 0xb7, 0xee, 0xf5, 0x22, 0xf2, 0x2a, 0x69, 0x41, 0x37, 0xbe, 0x34,
	0x6a, 0x53, 0x0c, 0x79, 0x63, 0x07, 0x6e, 0x0e, 0x1d, 0x74, 0xfc, 0x95, 0xfc, 0x26, 0xdc, 0x51,
	0x4d, 0xe6, 0x57, 0x3f, 0x8f, 0x33, 0xec, 0xf5, 0x5f, 0x86, 0x5b, 0xcd, 0xd4, 0x6a, 0xef, 0x27,
	0xb4, 0x7d, 0xf9, 0x81, 0xcf, 0xf7, 0x54, 0xf6, 0xe0, 0xf6, 0x19, 0xe3, 0x8f, 0x4f, 0xcb, 0x9f,
	0x83, 0x85, 0x0c, 0xeb, 0xe6, 0x38, 0x5a, 0xca, 0xba, 0x16, 0x52, 0xeb, 0xfa, 0x17, 0x34, 0xb8,
	0x7b, 0xf6, 0x52, 0x8c, 0x2f, 0x06, 0x4f, 0x60, 0x96, 0x1d, 0x01, 0xf9, 0x11, 0xe1, 0x7e, 0xcb,
	0x19, 0x46, 0x5a, 0x56, 0xee, 0x66, 0xf0, 0xa4, 0xc8, 0x8f, 0x88, 0xf1, 0x0c, 0x56, 0x33, 0x95,
	0x67, 0x59, 0x01, 0x7c, 0xd2, 0x85, 0xdc, 0x49, 0xa7, 0x99, 0xf9, 0x63, 0x30, 0xb6, 0x48, 0x3c,
	0xa0, 0x93, 0x9a, 0xc4, 0x8e, 0x77, 0xed, 0xe0, 0xe5, 0x35, 0xca, 0x03, 0x69, 0x15, 0xa5, 0x3b,
	0xd7, 0x57, 0x60, 0xaa, 0xeb, 0xb6, 0x12, 0x8b, 0x68, 0xb2, 0xeb, 0xb6, 0xea, 0x8e, 0xf1, 0x63,
	0x0d, 0xee, 0x9c, 0x89, 0xd0, 0xf8, 0x6b, 0xb0, 0x03, 0xb3, 0x11, 0xb1, 0x63, 0xd5, 0x77, 0x3c,
	0xdf, 0xfc, 0x4c, 0x8f, 0x6c, 0xce, 0xd0, 0x1e, 0x50, 0x6d, 0x3d, 0x86, 0xcb, 0x48, 0x49, 0x3c,
	0x92, 0x08, 0xbb, 0xc4, 0xb1, 0x32, 0xd2, 0xb5, 0x4c, 0xab, 0xab, 0xa2, 0xf6, 0x90, 0x8b, 0xda,
	0x0f, 0x34, 0xb8, 0x93, 0xe6, 0xf5, 0xcc, 0x08, 0x2f, 0x2d, 0x6e, 0xa9, 0x69, 0x16, 0x5f, 0x72,
	0x9a, 0xc6, 0x97, 0xe1, 0xee, 0xd9, 0xe8, 0x8e, 0x2f, 0x9d, 0x7f, 0x4f, 0x83, 0x37, 0x72, 0x3b,
	0x3b, 0x8b, 0x95, 0xff, 0x48, 0x2c, 0xde, 0xff, 0xd2, 0xa4, 0xcf, 0xb0, 0x45, 0x57, 0xa9, 0xe6,
	0xc5, 0x24, 0x34, 0x7d, 0xbf, 0x9b, 0x8f, 0xf3, 0x1d, 0x98, 0xa0, 0x82, 0xcb, 0xad, 0xca, 0x81,
	0x73, 0x6a, 0xac, 0xcc, 0xb1, 0xd4, 0x8b, 0x39, 0x96, 0xfa, 0xbb, 0xa0, 0x07, 0xbd, 0xa3, 0x8e,
	0xdb, 0x4a, 0x59, 0x97, 0xcc, 0xb4, 0xa8, 0xb0, 0x1a, 0xc5, 0xb8, 0xcc, 0x40, 0x73, 0xdb, 0x72,
	0x32, 0x0b, 0xcd, 0x4d, 0xcb, 0x1b, 0x00, 0x6d, 0x3a, 0x1b, 0x2b, 0xee, 0x07, 0x04, 0xbd, 0xb7,
	0xb2, 0x39, 0x8b, 0x25, 0x07, 0xfd, 0x80, 0x18, 0xbf, 0x95, 0xc4, 0x9d, 0x98, 0xb6, 0xf7, 0x09,
	0x3b, 0xc5, 0xbb, 0x01, 0x10, 0x9d, 0xf8, 0xcf, 0xf9, 0x49, 0x9f, 0x86, 0x27, 0x7d, 0xb3, 0xb4,
	0x84, 0x55, 0xcb, 0x30, 0x0b, 0xbb, 0x95, 0x38, 0x49, 0xb3, 0x3c, 0xcc, 0x62, 0x9d, 0x95, 0x25,
	0x01, 0x1b, 0x02, 0x88, 0x9f, 0x9d, 0x63, 0xa1, 0x00, 0x5a, 0x81, 0xa9, 0x93, 0xc7, 0xe8, 0xbf,
	0xb1, 0xc9, 0x4e, 0x9e, 0x3c, 0xa6, 0xce, 0x5b, 0x0b, 0xed, 0x93, 0x2c, 0x5a, 0xe3, 0x0a, 0x0f,
	0x75, 0x79, 0x39, 0xff, 0x48, 0x19, 0x9a, 0xe5, 0x25, 0x75, 0xc7, 0xf8, 0x2b, 0x1a, 0x5c, 0xcb,
	0x1d, 0xe5, 0x22, 0xe7, 0x57, 0xba, 0xe0, 0xd0, 0xd0, 0xf6, 0x3e, 0xe1, 0x74, 0x1b, 0xf5, 0x04,
	0x3c, 0x19, 0xba, 0xf2, 0x3c, 0x53, 0x62, 0x6c, 0xa1, 0x3b, 0xc4, 0x01, 0xc5, 0xe1, 0x36, 0xe5,
	0xd6, 0x31, 0x89, 0x61, 0xfc, 0x6b, 0x0d, 0xde, 0x18, 0xd6, 0xd3, 0xf8, 0x13, 0x6e, 0x8a, 0x3b,
	0x5e, 0x79, 0x1d, 0x5b, 0x18, 0xf1, 0x2a, 0x33, 0x75, 0x48, 0x3f, 0x17, 0x2b, 0x78, 0x50, 0xbe,
	0x76, 0xfc, 0xe7, 0x5e, 0xc7, 0xb7, 0x1d, 0xcb, 0x21, 0x1d, 0xbb, 0x6f, 0x45, 0xa4, 0xc5, 0x37,
	0xb2, 0x8a, 0xa8, 0xd9, 0xa4, 0x15, 0x4d, 0xd2, 0x32, 0xfe, 0xa4, 0x06, 0xd7, 0xd6, 0x83, 0xa0,
	0xd3, 0x3f, 0xf0, 0x3f, 0xf4, 0x5d, 0x4f, 0xb9, 0x89, 0x7b, 0x95, 0x5c, 0x42, 0x3d, 0x01, 0x37,
	0xb2, 0x5a, 0xb6, 0xd7, 0xe2, 0x27, 0x49, 0x33, 0xe6, 0x8c, 0x1b, 0x55, 0xf1, 0xdb, 0xa8, 0xc3,
	0xf5, 0x7c, 0x14, 0x2e, 0xe4, 0xcc, 0x2c, 0xca, 0x0e, 0x98, 0x5d, 0x72, 0xec, 0xeb, 0xef, 0x72,
	0xc3, 0x02, 0xdd, 0xc1, 0x21, 0x17, 0x62, 0x68, 0x3e, 0x20, 0x34, 0xbb, 0x09, 0x75, 0x3d, 0x8b,
	0xa3, 0x8f, 0xb3, 0xc1, 0x9b, 0xd0, 0xba, 0xc7, 0x3d, 0x6a, 0x4a, 0x64, 0xb1, 0x2e, 0x8a, 0x5a,
	0xe0, 0x44, 0x7e, 0xae, 0x68, 0x3f, 0xd4, 0x0e, 0x0e, 0x2c, 0x49, 0xa4, 0x1a, 0x52, 0xb3, 0xe4,
	0x98, 0x5b, 0x57, 0x61, 0xc6, 0x73, 0x5b, 0x9f, 0x28, 0x81, 0x6f, 0xf2, 0x5b, 0xbf, 0x09, 0x25,
	0xd7, 0x3b, 0x75, 0x63, 0xe6, 0x0b, 0x73, 0x3d, 0x00, 0xac, 0x88, 0x76, 0x67, 0xfc, 0x39, 0x4d,
	0xb9, 0x49, 0x45, 0x82, 0xee, 0x46, 0x6d, 0xaa, 0x74, 0xf7, 0xa0, 0xa4, 0x68, 0x3a, 0x4e, 0x80,
	0xf7, 0x46, 0xb8, 0xb4, 0x4d, 0x70, 0x35, 0x21, 0xd1, 0x88, 0xfa, 0x5b, 0xb0, 0x10, 0xfb, 0xb1,
	0xdd, 0xb1, 0x6c, 0x3a, 0x88, 0xe5, 0xf5, 0xba, 0x7c, 0xc1, 0xcb, 0x58, 0x8c, 0x43, 0xef, 0xf5,
	0xba, 0xc6, 0x43, 0xd0, 0x93, 0xae, 0x3a, 0x76, 0x9f, 0x84, 0x14, 0x1b, 0xd5, 0xc4, 0xd6, 0xd2,
	0x26, 0x36, 0x11, 0x07, 0x22, 0xca, 0x1c, 0x2e, 0x20, 0xa5, 0xe7, 0xa9, 0xac, 0x3f, 0xd0, 0xc4,
	0xe9, 0xc8, 0xc0, 0x38, 0x17, 0x91, 0xe1, 0x05, 0x46, 0x06, 0xe4, 0x30, 0x45, 0x88, 0xdf, 0x19,
	0x81, 0xc0, 0x82, 0x43, 0xcd, 0x32, 0xf6, 0x41, 0x3f, 0x51, 0x86, 0x73, 0x28, 0x5c, 0xcc, 0xa3,
	0xf0, 0xb7, 0x34, 0xb8, 0xa4, 0xc8, 0xcb, 0x73, 0x3b, 0x74, 0x9a, 0x04, 0x6f, 0xb1, 0xa9, 0xc4,
	0xb5, 0xdd, 0x63, 0xce, 0x98, 0x8c, 0xce, 0x33, 0xb4, 0x80, 0x32, 0x24, 0x46, 0x78, 0xf5, 0x82,
	0xc0, 0x0f, 0x63, 0xab, 0x6b, 0xb7, 0xdd, 0x96, 0x45, 0x6b, 0x38, 0x9f, 0x57, 0x78, 0xcd, 0x2e,
	0xad, 0xd8, 0x72, 0x8f, 0x63, 0xca, 0x79, 0x41, 0xe8, 0xb6, 0x88, 0xd5, 0x71, 0xbb, 0x6e, 0x2c,
	0x7c, 0x4f, 0x2c, 0xda, 0xa1, 0x25, 0xc6, 0x6f, 0x68, 0x0a, 0x83, 0x33, 0x34, 0x50, 0x92, 0x44,
	0xe8, 0x95, 0xa6, 0x84, 0x5e, 0xe9, 0x30, 0xa1, 0xb0, 0x37, 0xfe, 0xd6, 0x97, 0x61, 0x52, 0x3d,
	0x53, 0x63, 0x1f, 0x74, 0xd8, 0x10, 0xfb, 0xb2, 0x7a, 0x9e, 0x1b, 0x8b, 0x28, 0x2d, 0x56, 0x74,
	0xe8, 0xb9, 0xb1, 0x7e, 0x09, 0xa6, 0xec, 0x2e, 0x6e, 0x8a, 0xec, 0xa6, 0x9d, 0x7f, 0x19, 0xff,
	0x5d, 0x83, 0x2b, 0x4d, 0x65, 0x7d, 0x19, 0x46, 0xaf, 0x58, 0xa3, 0x19, 0x50, 0xb6, 0x11, 0x39,
	0xa4, 0xb2, 0x74, 0x15, 0x4a, 0x58, 0x48, 0xc9, 0xc6, 0xb4, 0x5e, 0xb2, 0x06, 0xfc, 0x00, 0x47,
	0xae, 0xc1, 0x5d, 0x98, 0x57, 0x3a, 0xa0, 0x4b, 0xcc, 0x23, 0x87, 0x64, 0x0f, 0x7b, 0xbd, 0x2e,
	0x57, 0x46, 0xca, 0x22, 0x4d, 0x09, 0x65, 0x24, 0xd7, 0x07, 0x8f, 0x82, 0x72, 0xa6, 0x3b, 0xbe,
	0xf6, 0x74, 0xe1, 0xd6, 0xd6, 0x40, 0x47, 0x9c, 0xa1, 0x5e, 0xb1, 0x0c, 0xfe, 0x56, 0x01, 0x6e,
	0x9f, 0x31, 0xd6, 0xf8, 0x72, 0xf8, 0x65, 0xc9, 0x2d, 0xd4, 0x5e, 0xe5, 0x56, 0xc3, 0xfb, 0x23,
	0x45, 0xa6, 0xa8, 0x23, 0x73, 0xfe, 0xa2, 0x56, 0x2d, 0xd5, 0x9b, 0xbc, 0x4b, 0xc5, 0x4d, 0x78,
	0x6f, 0xe4, 0x2e, 0x51, 0xb0, 0x79, 0x7f, 0x28, 0xd5, 0x37, 0xa1, 0x84, 0xf3, 0xf2, 0xd9, 0xf1,
	0x35, 0x63, 0x08, 0xe0, 0x45, 0x87, 0xae, 0x63, 0xfc, 0x93, 0xa2, 0xb2, 0x7b, 0x35, 0x42, 0xbf,
	0x1d, 0x92, 0x28, 0xa2, 0xee, 0x28, 0x5e, 0x18, 0xcb, 0xad, 0x62, 0x8a, 0x7e, 0x32, 0xf6, 0x6a,
	0xf5, 0x42, 0x8b, 0x05, 0x31, 0xf2, 0xc3, 0xf0, 0x56, 0x2f, 0x34, 0xe9, 0x77, 0x12, 0x3e, 0x93,
	0x28, 0x0f, 0x16, 0x3e, 0x43, 0xb9, 0xea, 0x36, 0xcc, 0xb1, 0xf8, 0xa6, 0x28, 0xb6, 0xe3, 0x9e,
	0x38, 0x9e, 0x2e, 0x61, 0x59, 0x13, 0x8b, 0xd0, 0xe4, 0xa6, 0xc6, 0x2b, 0x83, 0x8b, 0xdd, 0x00,
	0xd9, 0x73, 0xc6, 0x9c, 0xa3, 0xa5, 0x38, 0xc4, 0x81, 0x1b, 0xe8, 0x6b, 0xb0, 0xc8, 0xe3, 0x00,
	0xed, 0x0e, 0x3f, 0x96, 0x17, 0xf1, 0x2c, 0x0b, 0x2c, 0xfe, 0x0f, 0xcb, 0x0f, 0x5d, 0x27, 0x4a,
	0x60, 0x9f, 0xbb, 0x9e, 0x27, 0x60, 0xa7, 0x15, 0xd8, 0x67, 0x58, 0x8e, 0xb0, 0x0f, 0x61, 0x19,
	0x8f, 0xd0, 0x59, 0x83, 0x64, 0x22, 0x33, 0x2c, 0x54, 0x87, 0xd6, 0x21, 0x0e, 0x55, 0x31, 0xa3,
	0x41, 0x0f, 0x61, 0x36, 0xc7, 0x43, 0x78, 0x0c, 0x97, 0x4f, 0xfc, 0x28, 0xb6, 0x58, 0x98, 0xc0,
	0x51, 0x2f, 0xb6, 0x9c, 0x5e, 0xc8, 0x4e, 0x5c, 0x00, 0xc1, 0x97, 0x69, 0x75, 0x93, 0xd6, 0x6e,
	0xf4, 0xe2, 0x4d, 0x5e, 0xa7, 0xbf, 0x03, 0x3a, 0x36, 0x3b, 0xea, 0xc5, 0xb1, 0xef, 0xb1, 0x78,
	0xec, 0x08, 0x03, 0xa4, 0x8a, 0xe6, 0x02, 0xad, 0xd9, 0xc0, 0x8a, 0x9a, 0xe7, 0x1c, 0x44, 0xc6,
	0xff, 0x2d, 0x40, 0x59, 0x2e, 0x22, 0xaa, 0xc1, 0xa1, 0x0b, 0xf8, 0x10, 0x96, 0x91, 0xc6, 0x58,
	0xcb, 0xe2, 0xc1, 0x6d, 0xcf, 0xed, 0x72, 0x45, 0xbc, 0x48, 0xeb, 0x68, 0x27, 0x18, 0x12, 0xbe,
	0xee, 0xb9, 0x5d, 0xbd, 0x01, 0x65, 0x84, 0x0d, 0x38, 0x6f, 0xf0, 0xab, 0xac, 0x51, 0xb6, 0x1a,
	0xc1, 0x4e, 0xe6, 0x5c, 0x5b, 0x65, 0xae, 0x0c, 0x8f, 0x4f, 0xbc, 0x2c, 0x8f, 0x7f, 0x00, 0xa5,
	0x00, 0xb7, 0x7a, 0xd6, 0xdf, 0x24, 0xf6, 0x97, 0x35, 0xb6, 0x30, 0x02, 0x0d, 0x18, 0x1c, 0xb6,
	0xda, 0x82, 0x69, 0xf6, 0xc5, 0x98, 0x67, 0x44, 0xeb, 0x04, 0x5b, 0x20, 0x06, 0xa2, 0xb5, 0xf1,
	0x55, 0xd5, 0xd2, 0x92, 0xf5, 0x63, 0x1a, 0x80, 0xc9, 0x69, 0x4b, 0x41, 0x3d, 0x6d, 0xf9, 0x06,
	0x5c, 0x56, 0x35, 0xd6, 0x45, 0x6e, 0x6a, 0xce, 0x51, 0x8a, 0xbf, 0xae, 0xc1, 0xea, 0xe0, 0x10,
	0xe3, 0xeb, 0xc2, 0x4d, 0x98, 0x65, 0x0c, 0x97, 0x5c, 0xd7, 0xbc, 0x3d, 0x02, 0x41, 0x71, 0xb8,
	0x99, 0x36, 0xff, 0x65, 0x84, 0x50, 0xd9, 0x0a, 0xed, 0xa3, 0x2a, 0x0b, 0xd6, 0x7b, 0xa5, 0x9b,
	0xa7, 0x22, 0x11, 0x45, 0x55, 0x22, 0x8c, 0x5f, 0x80, 0x45, 0x65, 0xcc, 0xf1, 0x77, 0xb0, 0x5f,
	0x81, 0x15, 0x94, 0xde, 0xd7, 0xe5, 0xc7, 0xdc, 0x94, 0xec, 0x8d, 0xda, 0xab, 0xc8, 0x0f, 0xea,
	0xb1, 0x88, 0x2a, 0x2e, 0xa3, 0x0a, 0x97, 0xb2, 0x08, 0x8c, 0x3f, 0x8b, 0x36, 0x9e, 0xd4, 0xcb,
	0x2e, 0x0e, 0xfc, 0x3d, 0xa1, 0xef, 0x5e, 0x31, 0xc3, 0xb1, 0xd3, 0xf9, 0xfc, 0x81, 0xc6, 0x47,
	0xfb, 0x63, 0x4e, 0xfc, 0xd7, 0xc4, 0x35, 0x92, 0xb6, 0x2f, 0xc5, 0x21, 0xff, 0xa2, 0x00, 0x2b,
	0x4d, 0xb7, 0x1b, 0x74, 0xe4, 0x69, 0x75, 0xc7, 0xf6, 0x50, 0xec, 0xdf, 0x82, 0x05, 0x19, 0x96,
	0xdb, 0xb1, 0xbd, 0x44, 0x5d, 0x97, 0x9f, 0x2b, 0x90, 0x4e, 0x2a, 0x68, 0xbb, 0x90, 0x0e, 0xda,
	0x5e, 0x87, 0x1b, 0x51, 0xeb, 0x84, 0x38, 0xbd, 0x0e, 0x91, 0x42, 0x26, 0x76, 0x18, 0x2b, 0x8a,
	0x43, 0xee, 0xb5, 0x5d, 0x15, 0x40, 0x1c, 0x05, 0xb1, 0xd1, 0x34, 0xe3, 0x50, 0xff, 0x1c, 0xcc,
	0x27, 0xef, 0x6a, 0x14, 0x9d, 0x3c, 0xa0, 0xaf, 0xca, 0x12, 0x0c, 0x55, 0xe8, 0xfb, 0x50, 0x3a,
	0xb6, 0xbb, 0x6e, 0xa7, 0x7f, 0x96, 0xe2, 0x35, 0x81, 0xc1, 0x60, 0x8b, 0x2f, 0xc1, 0x0d, 0xbb,
	0x13, 0x12, 0xdb, 0xe9, 0x5b, 0xbd, 0x00, 0x8f, 0x0b, 0x8e, 0xdc, 0xb6, 0x15, 0xb5, 0x42, 0x42,
	0x3c, 0xdc, 0x6c, 0xd9, 0x29, 0xd7, 0x15, 0x0e, 0x74, 0x88, 0x30, 0x1b, 0x6e, 0xbb, 0x89, 0x10,
	0xd4, 0xfd, 0xe8, 0xc1, 0xbc, 0xfc, 0xae, 0x77, 0xed, 0x36, 0xbe, 0xa3, 0x71, 0xbb, 0x6d, 0x25,
	0x94, 0x60, 0xca, 0xed, 0xb6, 0x0f, 0xc3, 0x0e, 0x25, 0xae, 0x1b, 0x59, 0x3d, 0xcf, 0x21, 0xa1,
	0xc5, 0x22, 0x95, 0xf9, 0x2e, 0x57, 0x76, 0xa3, 0x43, 0x5a, 0x6a, 0x62, 0x21, 0xb5, 0x77, 0x05,
	0x32, 0x7d, 0xb4, 0x92, 0xb8, 0x59, 0xcd, 0x0a, 0x37, 0xfa, 0xd4, 0x4c, 0xea, 0xa8, 0xe7, 0x5a,
	0x12, 0x81, 0x71, 0x99, 0x2d, 0x67, 0xb9, 0x0b, 0x39, 0xcb, 0x6d, 0xfc, 0xf5, 0xd4, 0x01, 0x97,
	0x32, 0xdc, 0xf8, 0x7a, 0xb9, 0x0a, 0x33, 0x94, 0x3a, 0x8a, 0x93, 0x78, 0x46, 0x8c, 0x5c, 0x9a,
	0xb2, 0x26, 0xa5, 0x2b, 0x2e, 0x1b, 0x3e, 0x77, 0x62, 0x96, 0x8e, 0x38, 0x06, 0x9d, 0xe1, 0x46,
	0x4e, 0x64, 0xfc, 0x97, 0x02, 0x5c, 0x6b, 0xda, 0xa7, 0xe4, 0x27, 0x44, 0x1c, 0x75, 0xbd, 0x8b,
	0xa9, 0xf5, 0x3e, 0x86, 0x59, 0x3f, 0x20, 0x61, 0x72, 0x19, 0x39, 0xff, 0x68, 0x7b, 0xf8, 0x5c,
	0xcf, 0x40, 0x39, 0xa1, 0xc3, 0xbe, 0xe8, 0xcf, 0x4c, 0xba, 0x36, 0x4e, 0x41, 0x1f, 0x04, 0xd0,
	0xef, 0xc2, 0xad, 0x8d, 0xfa, 0x96, 0xd5, 0xac, 0x9a, 0xb5, 0xda, 0x9e, 0xb5, 0xdf, 0xa8, 0x99,
	0xeb, 0x07, 0xf5, 0xfd, 0x3d, 0xeb, 0x70, 0xaf, 0xd9, 0xa8, 0x55, 0xeb, 0x4f, 0xea, 0xb5, 0xcd,
	0xca, 0x4f, 0xe9, 0xd7, 0x61, 0x35, 0x17, 0x6a, 0x7d, 0x73, 0xb3, 0xa2, 0x0d, 0xad, 0xdd, 0xac,
	0xed, 0x54, 0x0a, 0xc6, 0xf7, 0x35, 0xb8, 0x9e, 0x8f, 0xf5, 0x1f, 0x45, 0xb6, 0xf8, 0x1d, 0x0d,
	0xae, 0x67, 0x31, 0x65, 0x17, 0x09, 0x2c, 0xb4, 0x6a, 0x64, 0xdd, 0xf7, 0xfa, 0x51, 0xfd, 0xf6,
	0x04, 0x40, 0x1d, 0x0f, 0xb5, 0xaa, 0x76, 0xc8, 0x0e, 0x0e, 0xd9, 0xa9, 0x57, 0x12, 0xf0, 0xc3,
	0x0a, 0x58, 0x2c, 0x7d, 0x16, 0xeb, 0xe2, 0x79, 0x1a, 0x7b, 0x22, 0xfb, 0xcc, 0x86, 0x47, 0x1d,
	0x4f, 0xe6, 0x5b, 0x85, 0x3c, 0xb4, 0xf8, 0x4d, 0x11, 0xde, 0x3c, 0x35, 0x04, 0x8c, 0xc5, 0x30,
	0xdf, 0x87, 0x8a, 0x54, 0xfb, 0x76, 0x4c, 0x64, 0x1c, 0xf2, 0xac, 0x29, 0x10, 0xdd, 0xe4, 0xc5,
	0xea, 0x09, 0xa2, 0x12, 0xb4, 0x5c, 0x4e, 0x9d, 0x20, 0x36, 0x65, 0xec, 0xf2, 0xdb, 0xc9, 0x4c,
	0xc5, 0xa9, 0xe4, 0x0c, 0xf6, 0x3b, 0xcf, 0x8b, 0x73, 0x0e, 0x26, 0x95, 0xad, 0x74, 0x2e, 0xd5,
	0x6d, 0x55, 0x9a, 0x33, 0x5b, 0xfc, 0x80, 0x02, 0xd7, 0x73, 0x16, 0xd7, 0xf3, 0xfc, 0x83, 0x76,
	0x3c, 0xd7, 0xdc, 0x72, 0x8f, 0x63, 0x76, 0x98, 0x81, 0x4b, 0x7a, 0x17, 0xe6, 0xf9, 0x32, 0xb5,
	0xa8, 0x2f, 0x71, 0xd4, 0x46, 0x7f, 0x6a, 0xd6, 0x9c, 0x73, 0xe5, 0x52, 0x6e, 0xb4, 0x59, 0x3c,
	0x8f, 0x88, 0x24, 0x62, 0x47, 0x99, 0xfd, 0x80, 0xbd, 0x34, 0x29, 0x9b, 0x8b, 0x32, 0x52, 0x08,
	0x4f, 0x34, 0xfb, 0x01, 0x31, 0x3e, 0x94, 0x97, 0x2a, 0x72, 0x4c, 0xba, 0x96, 0x88, 0x72, 0xb2,
	0xc5, 0x4c, 0xd3, 0x6f, 0xaa, 0x73, 0xc4, 0x71, 0x8b, 0x43, 0xa2, 0x96, 0x38, 0x3e, 0xa5, 0x05,
	0x9b, 0x24, 0x6a, 0x19, 0xff, 0x20, 0xa5, 0xc6, 0xd9, 0x20, 0x17, 0x31, 0xe1, 0x47, 0xd5, 0x8c,
	0x43, 0xe6, 0x5a, 0x1c, 0x32, 0xd7, 0x34, 0xa3, 0x4f, 0xa4, 0x19, 0xdd, 0xf8, 0x3b, 0x1a, 0x9e,
	0x8c, 0xe6, 0x20, 0x3f, 0xbe, 0xb6, 0xa9, 0xc9, 0x73, 0x64, 0xba, 0x54, 0xdc, 0x3d, 0xb8, 0x3b,
	0x7c, 0xd5, 0x13, 0x61, 0x14, 0xa7, 0xcd, 0x28, 0x98, 0x77, 0xa0, 0xcc, 0xbb, 0xe1, 0x67, 0x08,
	0x6c, 0x66, 0x7c, 0xc1, 0xd9, 0x21, 0x82, 0xf1, 0xcb, 0x70, 0x75, 0xdb, 0xf6, 0x1c, 0x69, 0xe8,
	0xb0, 0xbe, 0xc6, 0x25, 0xf9, 0x99, 0x3a, 0x80, 0xdd, 0x2c, 0xd8, 0xad, 0x16, 0x09, 0xe2, 0xe4,
	0x66, 0x61, 0x1d, 0xbf, 0x8d, 0x6d, 0xb8, 0x96, 0x3b, 0xfe, 0xf8, 0x66, 0xe3, 0x8f, 0x34, 0x58,
	0xdc, 0xe8, 0xf5, 0x65, 0xd0, 0xe2, 0x78, 0x33, 0x38, 0xc3, 0x64, 0xdc, 0xc6, 0xf7, 0x33, 0x18,
	0xf5, 0x39, 0x5a, 0x68, 0x31, 0x0f, 0x98, 0x64, 0xd1, 0x75, 0x61, 0xf2, 0x41, 0x4d, 0x27, 0xf1,
	0x0e, 0x27, 0x6a, 0x27, 0x5c, 0x54, 0xe2, 0x0f, 0x71, 0xa2, 0x76, 0xdd, 0x31, 0x7e, 0xaf, 0x00,
	0x25, 0xa5, 0x03, 0xfd, 0x36, 0xcc, 0x89, 0x51, 0x94, 0x53, 0xd9, 0x12, 0x2f, 0xc3, 0xa7, 0xd1,
	0xe9, 0x57, 0x15, 0x3c, 0x0a, 0x38, 0xff, 0x55, 0x05, 0xd7, 0xad, 0xe2, 0x55, 0x85, 0x3a, 0xeb,
	0xd9, 0xf4, 0xac, 0x6f, 0x00, 0xb0, 0x2a, 0x1c, 0x95, 0xe9, 0x0a, 0xf6, 0x56, 0x45, 0x8c, 0xa9,
	0x68, 0x2f, 0x2d, 0xeb, 0x85, 0xad, 0xc0, 0x94, 0x1b, 0x59, 0x27, 0x7e, 0xcc, 0xcf, 0xa4, 0x26,
	0xdd, 0x68, 0xdb, 0x8f, 0xd1, 0xab, 0xe4, 0x87, 0xb1, 0x53, 0xdc, 0xab, 0x64, 0xe7, 0xb0, 0xb7,
	0x13, 0x1a, 0x53, 0xb5, 0x8c, 0x2a, 0xb9, 0x2c, 0x89, 0x47, 0x55, 0xb2, 0x0a, 0xc2, 0x1f, 0x88,
	0x14, 0xf9, 0x33, 0x26, 0x5a, 0x86, 0x2f, 0xed, 0xbf, 0x08, 0xba, 0xca, 0x01, 0xe3, 0xf3, 0xd0,
	0x1f, 0xc3, 0x23, 0x4f, 0x19, 0x4f, 0xcb, 0x5c, 0x80, 0x86, 0x78, 0xb7, 0x33, 0xe6, 0x4d, 0xe4,
	0xdf, 0x4c, 0xde, 0xc8, 0x6f, 0x86, 0x24, 0x8a, 0xf8, 0xfb, 0xbf, 0x21, 0x2f, 0x83, 0xd3, 0xd7,
	0xd7, 0x85, 0xcc, 0xf5, 0xb5, 0xde, 0x84, 0xb2, 0xdc, 0xb7, 0x94, 0x73, 0xa5, 0xf3, 0x9f, 0xbd,
	0xac, 0x8b, 0x57, 0xf4, 0x38, 0x05, 0x11, 0x27, 0x8a, 0x08, 0xd1, 0x31, 0xb1, 0x33, 0x76, 0xeb,
	0xc4, 0x23, 0xfc, 0xb0, 0x04, 0x6f, 0x91, 0x3e, 0x9f, 0xbc, 0x82, 0x64, 0x60, 0x51, 0xd7, 0xee,
	0x74, 0x2c, 0xbc, 0x30, 0x60, 0xb7, 0xf0, 0x2b, 0x6a, 0x77, 0x4d, 0x5a, 0x5b, 0x6f, 0xf9, 0x1e,
	0xc6, 0x25, 0xf0, 0x67, 0x93, 0x81, 0xdd, 0x26, 0x96, 0x32, 0x08, 0xbe, 0x1f, 0x35, 0x97, 0x79,
	0x35, 0x25, 0xed, 0xa6, 0x18, 0xcf, 0x38, 0x95, 0x4f, 0x18, 0x54, 0x7a, 0xe1, 0xee, 0xf5, 0x35,
	0xd0, 0x59, 0x27, 0xa2, 0x5f, 0x25, 0x49, 0xc4, 0xf9, 0x77, 0xb1, 0x6a, 0x77, 0x66, 0xc5, 0xc9,
	0x74, 0x6e, 0x7c, 0xaf, 0x98, 0x4e, 0x66, 0x70, 0xf6, 0x42, 0x3d, 0x83, 0x05, 0x16, 0xa8, 0x2e,
	0xf3, 0x13, 0x70, 0x0d, 0x3d, 0xee, 0x5a, 0xb0, 0x87, 0xbe, 0xb2, 0x0c, 0xc5, 0x38, 0x21, 0x70,
	0x91, 0xc7, 0x5b, 0x4a, 0xa2, 0xbe, 0xaf, 0x98, 0x23, 0x38, 0x7e, 0xb2, 0x68, 0x78, 0x5a, 0x27,
	0x4d, 0x12, 0x5a, 0x89, 0xeb, 0xf7, 0x39, 0x58, 0x39, 0x75, 0xc3, 0xb8, 0x67, 0x77, 0xa8, 0xa5,
	0x11, 0xda, 0xad, 0x98, 0x84, 0xc9, 0x63, 0x6e, 0x6c, 0xb4, 0xc4, 0x01, 0xaa, 0xa2, 0x9e, 0xee,
	0xd2, 0xb9, 0xed, 0xd2, 0x8f, 0x7f, 0x07, 0xda, 0xed, 0x3a, 0x8f, 0xf5, 0x43, 0x98, 0xff, 0x26,
	0x06, 0x42, 0x48, 0xc2, 0x4c, 0x5f, 0x88, 0x30, 0x65, 0xda, 0x8b, 0x2c, 0x32, 0x7e, 0x57, 0x03,
	0xfd, 0x89, 0xeb, 0xb9, 0x91, 0x08, 0xa4, 0x5d, 0x7f, 0x4e, 0xb7, 0xb7, 0x67, 0xb0, 0xc0, 0x6e,
	0x67, 0x92, 0xe1, 0xb4, 0x8b, 0xad, 0x03, 0x76, 0x93, 0xac, 0x03, 0x55, 0x8a, 0x7e, 0xc0, 0xc8,
	0xcb, 0xb4, 0xed, 0x74, 0xec, 0x07, 0x48, 0xd1, 0x9b, 0x50, 0x3a, 0xf2, 0xe3, 0xd8, 0xef, 0xa6,
	0x6e, 0x78, 0x59, 0x11, 0xb2, 0xf0, 0x96, 0xb4, 0x87, 0x1a, 0xa1, 0xdb, 0x22, 0x22, 0x2b, 0x02,
	0xde, 0xc4, 0x09, 0x36, 0xc2, 0x0f, 0x16, 0x46, 0xef, 0xb6, 0x48, 0x4a, 0xde, 0xb1, 0x04, 0x0d,
	0xab, 0x7f, 0x04, 0x30, 0xa7, 0xc6, 0x30, 0xc8, 0x5b, 0x39, 0x4d, 0xb9, 0x95, 0x5b, 0x15, 0x3d,
	0x17, 0xe4, 0xab, 0x61, 0xde, 0xbb, 0x0d, 0x2b, 0xd9, 0x87, 0xcb, 0x23, 0x5e, 0x91, 0xe4, 0x08,
	0x82, 0xb9, 0x14, 0xa4, 0x9f, 0x2e, 0xa3, 0x48, 0x9e, 0x61, 0xb2, 0xb7, 0x61, 0x31, 0x2d, 0xad,
	0x5d, 0x3b, 0xe0, 0xe7, 0x1d, 0x3f, 0x7b, 0xee, 0xc8, 0x38, 0xdd, 0x07, 0xaa, 0xc8, 0xee, 0xda,
	0x01, 0x0b, 0x1b, 0x59, 0x70, 0xd2, 0xa5, 0xfa, 0xe7, 0xe1, 0x52, 0xd7, 0x7e, 0x61, 0xb1, 0x23,
	0x7a, 0x79, 0x26, 0x23, 0x4f, 0x46, 0x18, 0xab, 0x76, 0xed, 0x17, 0xcd, 0x13, 0xff, 0xf9, 0x96,
	0xa8, 0xdf, 0xeb, 0x75, 0xf5, 0xcf, 0xc0, 0x92, 0x6c, 0xc8, 0x0f, 0x65, 0x68, 0xab, 0x69, 0xd9,
	0xaa, 0xc2, 0x5b, 0x3d, 0xc1, 0x4a, 0xda, 0xe4, 0x0b, 0x70, 0x25, 0x22, 0x1d, 0xd2, 0x8a, 0x89,
	0xc3, 0xde, 0x5f, 0x5b, 0xb1, 0x1b, 0x77, 0x08, 0x93, 0x56, 0x66, 0xea, 0x5f, 0x12, 0x00, 0x38,
	0x87, 0x03, 0x5a, 0x8d, 0xa2, 0x7b, 0x07, 0xca, 0xe4, 0x85, 0xdd, 0x0d, 0x3a, 0xc4, 0xc2, 0xe0,
	0x6a, 0xdc, 0x6b, 0x67, 0xcd, 0x39, 0x5e, 0x88, 0xa1, 0x5d, 0xfa, 0x3d, 0xa8, 0xf0, 0x73, 0x7e,
	0x7c, 0xab, 0x83, 0x26, 0xf2, 0x12, 0xf3, 0x20, 0x42, 0x79, 0x92, 0x4f, 0x0d, 0x65, 0xfd, 0x69,
	0x1a, 0x12, 0xd7, 0x15, 0xce, 0x7b, 0xd5, 0x33, 0x28, 0x41, 0x6a, 0xbf, 0xb8, 0xa2, 0xef, 0x80,
	0x4e, 0x89, 0x92, 0x39, 0x63, 0x62, 0xb6, 0xff, 0x42, 0xd7, 0x7e, 0xa1, 0x9e, 0x2c, 0xe9, 0x75,
	0xc1, 0xbf, 0x68, 0x13, 0xcd, 0x8d, 0x18, 0x02, 0x24, 0xa5, 0x82, 0xf3, 0x3a, 0x0a, 0xc8, 0x7d,
	0x60, 0x79, 0x10, 0x94, 0x67, 0xfd, 0xe8, 0x66, 0xcd, 0x9a, 0x0b, 0x58, 0x9e, 0x3c, 0xce, 0x4f,
	0x92, 0x29, 0x08, 0xce, 0x42, 0x39, 0x9c, 0x57, 0x92, 0x29, 0x70, 0xee, 0x40, 0x71, 0xfd, 0x79,
	0xb8, 0xd6, 0xf3, 0x86, 0x2f, 0xda, 0x02, 0xcb, 0x88, 0x91, 0x80, 0x64, 0x96, 0xed, 0x11, 0xe5,
	0x2e, 0xb7, 0x63, 0x75, 0x6c, 0xa7, 0x6f, 0x75, 0xc8, 0x71, 0x6c, 0x1d, 0xb5, 0x59, 0xcb, 0x0a,
	0xb6, 0xd4, 0x69, 0xed, 0x8e, 0xed, 0xf4, 0x77, 0xc8, 0x71, 0xbc, 0xd1, 0xc6, 0x36, 0x1f, 0xc0,
	0xe5, 0xa4, 0x4d, 0xe8, 0xb6, 0x4f, 0x92, 0x46, 0x8b, 0xd8, 0x68, 0x49, 0x34, 0x32, 0x69, 0x25,
	0x6f, 0xf5, 0x31, 0x2c, 0x29, 0x54, 0x97, 0xa9, 0x06, 0xf4, 0x0b, 0xe9, 0xb3, 0xc5, 0x23, 0xe5,
	0xa8, 0x83, 0xa5, 0x1a, 0xf8, 0x22, 0x5c, 0x4f, 0x0b, 0xa4, 0x47, 0x9e, 0xb7, 0xfc, 0x2e, 0x09,
	0x23, 0xc6, 0x67, 0xcb, 0x88, 0xda, 0x15, 0x55, 0xbc, 0xf6, 0x04, 0x04, 0xb2, 0xdc, 0xe7, 0x61,
	0x35, 0xdd, 0x01, 0xb3, 0x55, 0xb0, 0xf1, 0x0a, 0x33, 0x05, 0xd4, 0xc6, 0xe8, 0x0c, 0xd2, 0x86,
	0x57, 0x7b, 0xb0, 0x9c, 0x27, 0xca, 0x7a, 0x05, 0x8a, 0x9f, 0x90, 0xbe, 0x88, 0xac, 0xf9, 0x84,
	0xf4, 0xf5, 0x2d, 0x11, 0x62, 0x50, 0x18, 0xf1, 0x35, 0x48, 0xd6, 0x48, 0xe0, 0x51, 0x09, 0x3f,
	0x53, 0xf8, 0x69, 0xcd, 0xf8, 0x4e, 0x41, 0x3e, 0x43, 0x6f, 0xd8, 0x61, 0xec, 0x91, 0xf0, 0x22,
	0xcf, 0xee, 0x31, 0xa2, 0x47, 0xb8, 0x2e, 0xc4, 0xe1, 0xe7, 0xa1, 0x20, 0x9c, 0x17, 0xe2, 0xe8,
	0x9f, 0x85, 0x4b, 0xdc, 0xf1, 0xb1, 0x7b, 0xb1, 0x6f, 0x85, 0xe4, 0x97, 0x48, 0x2b, 0xb6, 0x1c,
	0xbb, 0xcf, 0xcd, 0xf0, 0x25, 0x56, 0xbb, 0xde, 0x8b, 0x7d, 0x13, 0xeb, 0x36, 0xed, 0xbe, 0xfe,
	0x33, 0x70, 0x85, 0x1a, 0xe4, 0x21, 0xe9, 0xe0, 0xa2, 0x45, 0x27, 0x6e, 0x60, 0x39, 0xc4, 0x76,
	0x3a, 0xae, 0x27, 0x32, 0x4d, 0x5d, 0x26, 0x9e, 0x63, 0x2a, 0xf5, 0x9b, 0xbc, 0x9a, 0x6a, 0x21,
	0x1c, 0x69, 0xb0, 0x03, 0xbb, 0xcf, 0xc3, 0x13, 0x2e, 0x51, 0x80, 0x5a, 0xa6, 0xbd, 0xdd, 0x37,
	0xfe, 0x20, 0xc9, 0x21, 0x90, 0x62, 0x98, 0x74, 0x06, 0x26, 0x1e, 0x8d, 0x82, 0xfe, 0xb2, 0x2c,
	0x45, 0x13, 0xf4, 0x3d, 0xd0, 0x13, 0xb0, 0x4c, 0xce, 0x93, 0x45, 0x59, 0x23, 0x79, 0xee, 0x03,
	0xb8, 0x34, 0x08, 0xae, 0xe4, 0x3e, 0x59, 0x1e, 0x68, 0xc2, 0x33, 0x11, 0x24, 0xad, 0x02, 0xaf,
	0xcd, 0xad, 0xd2, 0x39, 0x59, 0xd8, 0xf0, 0xda, 0xc6, 0x7f, 0x9b, 0x54, 0xc3, 0xbc, 0x07, 0xed,
	0xf4, 0xf1, 0x5d, 0xee, 0x7b, 0x30, 0xdd, 0xed, 0xab, 0xb7, 0x71, 0x03, 0x7c, 0x31, 0xd5, 0xed,
	0x23, 0xb1, 0x36, 0x60, 0x3a, 0x60, 0x1c, 0xc5, 0x4d, 0xf0, 0xf3, 0x1f, 0x51, 0x73, 0x0e, 0x34,
	0x45, 0x43, 0xfd, 0x6d, 0x28, 0xc7, 0x7e, 0x9b, 0xc4, 0x27, 0x24, 0xa4, 0xeb, 0xc6, 0x6f, 0xf7,
	0x71, 0xdf, 0x99, 0x13, 0x15, 0x9b, 0x76, 0x3f, 0xd2, 0xbf, 0x06, 0x8b, 0xe9, 0xa3, 0x8c, 0xe4,
	0xb5, 0xd4, 0x19, 0x61, 0xc3, 0xb9, 0x97, 0x27, 0xf2, 0x7c, 0x4b, 0xde, 0xa6, 0xd4, 0x85, 0x87,
	0x27, 0x33, 0x62, 0x95, 0x1e, 0xbd, 0x35, 0xda, 0xf6, 0x8c, 0xa8, 0x32, 0x6f, 0x50, 0xd8, 0x02,
	0x6e, 0x64, 0xe1, 0x33, 0x3f, 0xdc, 0x43, 0x67, 0xcc, 0x69, 0x37, 0xda, 0xa0, 0x9f, 0x54, 0x73,
	0x60, 0x50, 0x12, 0x0f, 0x2d, 0x4c, 0x2e, 0x5d, 0xec, 0x3e, 0x8f, 0x15, 0x58, 0xc1, 0x7a, 0x16,
	0x69, 0x28, 0x44, 0xdb, 0xee, 0x53, 0x4e, 0x57, 0x35, 0xb6, 0x54, 0xe3, 0xa8, 0x4b, 0xd9, 0x06,
	0x7a, 0x29, 0x96, 0x0a, 0xbb, 0xc9, 0xab, 0x51, 0x9d, 0xbe, 0x07, 0x4b, 0x6a, 0x53, 0xa1, 0x80,
	0x41, 0xd9, 0x26, 0xb0, 0x11, 0xd7, 0xbe, 0x37, 0x00, 0x8e, 0x3a, 0xd2, 0x43, 0x29, 0x31, 0xc3,
	0x1b, 0x4b, 0x70, 0x17, 0x39, 0x84, 0x4a, 0x60, 0xf7, 0xe5, 0x61, 0x1d, 0x52, 0x6b, 0xee, 0xbc,
	0x00, 0xb2, 0x86, 0x2d, 0xfc, 0x50, 0x7e, 0x96, 0x67, 0xce, 0x07, 0x76, 0x9f, 0xff, 0x46, 0x9a,
	0xdd, 0x83, 0x4a, 0x10, 0xfa, 0x2d, 0x3a, 0x2e, 0x55, 0xa3, 0x96, 0xdb, 0x6d, 0x73, 0x3d, 0x3c,
	0xcf, 0xcb, 0xa9, 0x02, 0xad, 0x77, 0xdb, 0xc6, 0xdf, 0xd2, 0x60, 0x71, 0xa0, 0xbf, 0xf3, 0x3c,
	0xf0, 0x11, 0xce, 0x0d, 0x14, 0x10, 0xc5, 0xe5, 0x10, 0x20, 0x82, 0x34, 0x6e, 0x24, 0xa6, 0x86,
	0x6c, 0x3a, 0x63, 0xce, 0xba, 0x11, 0x47, 0x85, 0xfa, 0xf3, 0xb8, 0x1b, 0xc8, 0x54, 0x2a, 0x53,
	0xf4, 0xb3, 0xee, 0x18, 0x7f, 0x5f, 0x83, 0x45, 0xc9, 0xfd, 0xae, 0xc3, 0xcf, 0xb0, 0x15, 0x29,
	0xd3, 0xce, 0x96, 0xb2, 0x47, 0x30, 0xc7, 0x85, 0xe5, 0x4c, 0xa1, 0x2c, 0x71, 0x20, 0x91, 0xb5,
	0x43, 0x59, 0xc6, 0x62, 0x76, 0x19, 0x73, 0x8e, 0x05, 0x27, 0xf2, 0x6e, 0x93, 0x8e, 0x61, 0x39,
	0xc5, 0x8b, 0xaf, 0xeb, 0xd6, 0x6a, 0x03, 0x56, 0x32, 0xe3, 0x8c, 0x7f, 0x5e, 0xf1, 0x8f, 0x67,
	0x64, 0xda, 0xad, 0x6d, 0xea, 0x27, 0xc6, 0xa4, 0x3b, 0xf2, 0x45, 0x41, 0x0b, 0xa0, 0xed, 0x53,
	0x28, 0x79, 0xd5, 0x57, 0x7a, 0xb4, 0x71, 0xae, 0xf8, 0x8b, 0x61, 0xb2, 0xdf, 0x5b, 0xb4, 0xab,
	0xa7, 0x2e, 0x79, 0xbe, 0xfd, 0x53, 0xe6, 0x6c, 0x5b, 0x7c, 0xe8, 0x5d, 0x98, 0xf7, 0x7c, 0x11,
	0xcd, 0x83, 0x03, 0x31, 0xa5, 0x59, 0xbb, 0xf0, 0x40, 0x7b, 0x3e, 0x8b, 0xfe, 0xe1, 0x63, 0xcd,
	0x79, 0xca, 0xb7, 0xfe, 0x18, 0x30, 0x53, 0x9f, 0x95, 0x84, 0x92, 0x4c, 0xe4, 0x33, 0x0e, 0x66,
	0xa4, 0x91, 0xd1, 0xc7, 0x5f, 0x00, 0x9e, 0xb3, 0x4f, 0x69, 0x38, 0xe4, 0xb6, 0x81, 0x27, 0xaa,
	0x91, 0x4d, 0xd3, 0xc7, 0x64, 0x53, 0xd9, 0x63, 0xb2, 0xfc, 0x7c, 0x6f, 0xd3, 0xc3, 0xf2, 0xbd,
	0xdd, 0x81, 0xf2, 0x71, 0x88, 0xe1, 0x43, 0x1c, 0x92, 0xf9, 0x15, 0x73, 0x58, 0x28, 0x80, 0xae,
	0xc1, 0xec, 0x89, 0x1f, 0x5b, 0x1d, 0xfb, 0x88, 0x74, 0xb8, 0x22, 0x9c, 0x39, 0xf1, 0xe3, 0x1d,
	0xfa, 0x4d, 0x2d, 0x16, 0xea, 0xb3, 0xb7, 0xb9, 0x5f, 0x09, 0xfc, 0x15, 0x22, 0x16, 0xe1, 0x2e,
	0x7e, 0x19, 0xa6, 0xdd, 0xc8, 0x72, 0x9d, 0x8e, 0xc8, 0x1f, 0x35, 0xe5, 0x46, 0x75, 0xa7, 0x43,
	0xae, 0xfe, 0xc7, 0xe4, 0x55, 0xc8, 0xc0, 0xa2, 0xbe, 0x02, 0x65, 0xf3, 0x2e, 0xe8, 0x02, 0x84,
	0x25, 0xcf, 0x41, 0xdb, 0x91, 0xa7, 0x41, 0xe3, 0x35, 0xf8, 0x42, 0x16, 0xed, 0xcd, 0x35, 0xcc,
	0x8d, 0x85, 0xd0, 0x74, 0xae, 0xcc, 0x30, 0x64, 0xa6, 0xd1, 0x02, 0xaf, 0xd8, 0xf6, 0x63, 0xf6,
	0x98, 0x3b, 0xab, 0xc6, 0x26, 0x07, 0xd4, 0xd8, 0xd5, 0x6f, 0x17, 0xe0, 0xda, 0x19, 0x7c, 0x94,
	0x39, 0x41, 0xd5, 0x64, 0x42, 0x53, 0x7e, 0x82, 0xfa, 0x35, 0xa8, 0x44, 0xbd, 0xa3, 0xa8, 0x15,
	0xba, 0x47, 0xf2, 0x30, 0xbd, 0x80, 0x37, 0xa6, 0xef, 0x9f, 0xef, 0x48, 0x8b, 0x86, 0xec, 0xc0,
	0xdd, 0x5c, 0x88, 0xd2, 0x05, 0x19, 0xd2, 0x16, 0xcf, 0x23, 0xed, 0x44, 0x2e, 0x69, 0x73, 0x2e,
	0xaa, 0x58, 0x2a, 0xd2, 0x81, 0x8b, 0xaa, 0x8d, 0xf9, 0xe4, 0xb1, 0x38, 0x15, 0x48, 0xe3, 0xff,
	0x69, 0xf8, 0xf8, 0x56, 0x21, 0xcf, 0x45, 0x02, 0xba, 0x77, 0xd2, 0x7c, 0xc7, 0xa8, 0xf3, 0xce,
	0xf9, 0x49, 0x26, 0x24, 0x63, 0xa6, 0x98, 0x74, 0x07, 0x4a, 0x18, 0x24, 0xa8, 0x5c, 0x5c, 0x8c,
	0xd2, 0x1b, 0x9d, 0x1f, 0x27, 0x33, 0xc4, 0xf2, 0x37, 0x15, 0x18, 0x8c, 0x57, 0xe8, 0xfa, 0xf2,
	0x1d, 0xf3, 0x0c, 0x2d, 0xd8, 0xf5, 0x43, 0x62, 0xfc, 0x50, 0x53, 0x63, 0x15, 0x92, 0xe9, 0x5f,
	0xe4, 0x61, 0x9e, 0x24, 0xac, 0x72, 0xfb, 0x7a, 0x7f, 0x64, 0x4d, 0x27, 0x5f, 0xe2, 0x8b, 0x2b,
	0xd8, 0x04, 0xe9, 0x62, 0x06, 0x69, 0x17, 0x2e, 0x4b, 0xbe, 0x7a, 0xcd, 0xdb, 0x54, 0x0d, 0x56,
	0x07, 0x87, 0x1a, 0x7f, 0xa7, 0x22, 0xea, 0xf5, 0x18, 0xba, 0x80, 0xec, 0x6d, 0xfc, 0x05, 0x02,
	0x90, 0x62, 0xca, 0x26, 0xb1, 0x92, 0x5f, 0x62, 0x96, 0x95, 0x1c, 0xba, 0x8e, 0xf1, 0xe3, 0x29,
	0xf5, 0x1d, 0x51, 0x6a, 0x9c, 0x8b, 0x38, 0x05, 0x15, 0x3c, 0x20, 0x12, 0x74, 0x8a, 0xed, 0x23,
	0xee, 0x02, 0x62, 0xe0, 0xad, 0x60, 0x3a, 0xfb, 0x48, 0x7f, 0x0c, 0x97, 0x53, 0x90, 0x27, 0x76,
	0xa7, 0xc3, 0x1f, 0x47, 0xb1, 0x0b, 0xaf, 0x65, 0xa5, 0x01, 0x5d, 0x6f, 0xe6, 0x04, 0x3f, 0x81,
	0x5b, 0x83, 0xcd, 0x8e, 0x3b, 0xbe, 0x8d, 0x19, 0xf0, 0x58, 0x7b, 0x66, 0x73, 0x5d, 0xcf, 0xb4,
	0x7f, 0xc2, 0x81, 0x58, 0x3f, 0xeb, 0x70, 0x63, 0x70, 0x64, 0xf5, 0x34, 0x85, 0xa9, 0xc4, 0xab,
	0xcf, 0x33, 0x08, 0x28, 0x07, 0x2b, 0x5f, 0x80, 0x2b, 0x39, 0x5d, 0x74, 0xfc, 0x38, 0x76, 0xc5,
	0xb6, 0x76, 0x29, 0xdb, 0x7c, 0x07, 0x6b, 0xf5, 0x2f, 0xe5, 0x8e, 0xce, 0x9a, 0xa2, 0xbf, 0xc7,
	0xb6, 0xbb, 0x2b, 0xf9, 0xcd, 0xa9, 0xd3, 0xb7, 0x05, 0xb7, 0xe4, 0xb3, 0x32, 0x7e, 0xc5, 0x33,
	0x30, 0x05, 0xb6, 0x13, 0x8a, 0x91, 0xf8, 0x0d, 0x59, 0x76, 0x16, 0x5f, 0x84, 0xeb, 0xf9, 0x1d,
	0xf1, 0x89, 0xcc, 0xa6, 0x30, 0x51, 0x3b, 0xe1, 0x73, 0x79, 0x32, 0x0c, 0x13, 0x65, 0x3a, 0xcc,
	0x8d, 0xb8, 0x3e, 0xb4, 0x13, 0x3a, 0xa3, 0xcf, 0xc1, 0xe5, 0x14, 0x4d, 0x94, 0x89, 0x94, 0x52,
	0x37, 0x27, 0x94, 0x1a, 0xca, 0x04, 0x1a, 0xf0, 0x66, 0xaa, 0xdd, 0x50, 0x72, 0xcc, 0x61, 0x2f,
	0xb7, 0x95, 0x5e, 0x86, 0x90, 0x64, 0x13, 0x6e, 0x9e, 0xd1, 0xe3, 0x89, 0xeb, 0xc5, 0xfc, 0xac,
	0xed, 0xda, 0x90, 0xbe, 0xb6, 0x5d, 0x2f, 0x36, 0xfe, 0x94, 0x06, 0x06, 0xbe, 0x69, 0xa9, 0x79,
	0x8e, 0xd4, 0x02, 0xc9, 0xf1, 0xc2, 0xb8, 0x52, 0x7c, 0x13, 0x84, 0xe5, 0xae, 0x88, 0x31, 0xf0,
	0xa2, 0x43, 0xd7, 0xd1, 0x2f, 0xc1, 0x14, 0x3f, 0x5f, 0xe0, 0xd1, 0xa7, 0xec, 0xcb, 0xf8, 0x0d,
	0x0d, 0xee, 0x9c, 0x89, 0xc7, 0xf8, 0x52, 0x7e, 0xe6, 0x69, 0x4c, 0xe1, 0xcc, 0xd3, 0x18, 0xe3,
	0x4f, 0x6b, 0x70, 0x67, 0xd3, 0x0d, 0x49, 0x2b, 0xfe, 0x43, 0xa6, 0xcb, 0x97, 0xe1, 0xee, 0xd9,
	0x78, 0x8c, 0xaf, 0xb1, 0xe9, 0xdc, 0x98, 0x83, 0xf2, 0x87, 0x3f, 0xb7, 0xb3, 0xf1, 0x18, 0x7f,
	0x6e, 0x5f, 0x81, 0x95, 0x2d, 0x12, 0x37, 0x42, 0x3f, 0xf0, 0x23, 0x72, 0x11, 0x73, 0x67, 0x15,
	0xa6, 0xd3, 0x6f, 0x82, 0xc5, 0x27, 0xe5, 0xd0, 0x12, 0xef, 0x98, 0x3a, 0x09, 0xf2, 0xf5, 0xb4,
	0x76, 0xd6, 0xeb, 0x69, 0x3a, 0xf5, 0xc4, 0xac, 0xa4, 0x53, 0x67, 0x96, 0xcb, 0x6d, 0x98, 0x3b,
	0x26, 0x9d, 0x8e, 0xff, 0xdc, 0x0a, 0x7c, 0xd7, 0x13, 0x0f, 0xbb, 0x4a, 0xac, 0xac, 0x41, 0x8b,
	0xd8, 0x3d, 0x92, 0x2f, 0xb2, 0x21, 0xf0, 0x3b, 0x5c, 0x5e, 0x52, 0x77, 0x8c, 0xbf, 0xac, 0xc1,
	0xa5, 0xec, 0x54, 0x2f, 0x94, 0x60, 0x48, 0x0c, 0xa2, 0x98, 0x36, 0x67, 0x84, 0x40, 0x28, 0x14,
	0x30, 0x4b, 0x41, 0x32, 0xb8, 0xae, 0xc3, 0x04, 0x4f, 0xb7, 0x54, 0xbc, 0x37, 0x6b, 0xe2, 0x6f,
	0xc3, 0x07, 0xbd, 0x49, 0x3c, 0x87, 0xb7, 0x79, 0xb5, 0x16, 0x81, 0x1c, 0x90, 0x27, 0x17, 0xc7,
	0x01, 0xbf, 0x04, 0x4b, 0xa9, 0x01, 0x2f, 0x92, 0xe6, 0x64, 0x99, 0x85, 0xad, 0x5c, 0x1c, 0x69,
	0x65, 0xd5, 0x0a, 0x99, 0x55, 0x3b, 0x3b, 0x64, 0x66, 0x03, 0x56, 0x32, 0x63, 0x8f, 0x8f, 0xff,
	0x2f, 0xc0, 0xb2, 0x49, 0x4e, 0xfd, 0x4f, 0x2e, 0x88, 0x3f, 0xc5, 0x21, 0xd3, 0x7e, 0x7c, 0x1c,
	0xfe, 0x62, 0x41, 0xe6, 0x92, 0xe4, 0xbd, 0x08, 0x8f, 0x5a, 0x21, 0x8d, 0x96, 0x25, 0xcd, 0xbb,
	0x30, 0x7b, 0x1c, 0xfa, 0x5d, 0xeb, 0xac, 0x94, 0x04, 0x33, 0x14, 0x02, 0xa5, 0xef, 0x7d, 0x28,
	0x09, 0xe6, 0x88, 0xe4, 0x89, 0xed, 0x60, 0xcc, 0x35, 0x67, 0x97, 0xb4, 0x28, 0x4e, 0xa4, 0x44,
	0x91, 0x3a, 0xd6, 0x21, 0xb1, 0x63, 0x92, 0x78, 0x57, 0xd4, 0xb1, 0xc6, 0xa2, 0x81, 0x30, 0x9b,
	0xa9, 0x74, 0x98, 0xcd, 0x0d, 0x00, 0xf2, 0x22, 0x70, 0x43, 0x82, 0xa7, 0x9e, 0x2c, 0xb6, 0x65,
	0x96, 0x95, 0x6c, 0xda, 0x7d, 0xc9, 0xa2, 0x33, 0x0a, 0x8b, 0x7e, 0xac, 0x6a, 0xa8, 0x8d, 0x7e,
	0xdd, 0x79, 0xb5, 0x1c, 0x66, 0xfc, 0x7a, 0x4a, 0x2f, 0xb0, 0x01, 0x2e, 0x92, 0x0f, 0x66, 0x9a,
	0x77, 0xc9, 0x97, 0xe2, 0xdd, 0x11, 0x6e, 0x00, 0xe5, 0x52, 0x9b, 0xa2, 0xb1, 0xf1, 0x45, 0x9c,
	0xed, 0xc5, 0x95, 0x80, 0x98, 0xce, 0xcb, 0x49, 0xf5, 0x2b, 0x9b, 0xce, 0x7f, 0xd5, 0x92, 0x2c,
	0xa9, 0x24, 0x8c, 0xdd, 0x63, 0xb7, 0x65, 0xc7, 0x24, 0x27, 0xad, 0x70, 0x59, 0x4d, 0x2b, 0x2c,
	0x03, 0x5d, 0x0b, 0xa3, 0x05, 0xba, 0x16, 0xcf, 0x0c, 0x74, 0xbd, 0x9d, 0x78, 0xa3, 0xca, 0x3f,
	0x98, 0x08, 0x17, 0x13, 0xd9, 0xf2, 0x5e, 0x12, 0x0b, 0x2b, 0x6f, 0xf2, 0x19, 0x5f, 0x8b, 0x98,
	0xd5, 0x03, 0x7e, 0xa1, 0x7f, 0x19, 0xa6, 0x03, 0xb7, 0x85, 0xb1, 0x23, 0xcc, 0x17, 0x98, 0x0a,
	0xdc, 0xd6, 0x61, 0xd8, 0x31, 0x7e, 0x35, 0xf3, 0xcf, 0x16, 0x0d, 0xb7, 0x35, 0xe4, 0x9f, 0x2d,
	0x94, 0x2e, 0x0a, 0x6a, 0x17, 0x59, 0xc1, 0x62, 0xd1, 0xc6, 0xaa, 0x60, 0xdd, 0x00, 0x60, 0x01,
	0x2f, 0x78, 0xba, 0x23, 0xf2, 0x90, 0xd1, 0x92, 0x7a, 0xcb, 0xf7, 0x8c, 0xff, 0x9c, 0xa0, 0x50,
	0xed, 0xb8, 0x01, 0xea, 0x90, 0x33, 0xb2, 0xf6, 0xa7, 0x0f, 0xec, 0x0a, 0x39, 0x71, 0x6d, 0xca,
	0x1a, 0x15, 0xb3, 0x6b, 0x24, 0xd3, 0xf3, 0xd3, 0xb9, 0x8c, 0x9f, 0x9e, 0xbf, 0xe1, 0xb6, 0x78,
	0x7a, 0xfe, 0x86, 0xdb, 0xc2, 0xfd, 0x30, 0x7b, 0xbc, 0x33, 0x39, 0x70, 0xbc, 0x63, 0x7c, 0x82,
	0x97, 0x5d, 0x6a, 0xbe, 0xa4, 0x90, 0xb4, 0xdc, 0xc0, 0xbd, 0x58, 0x62, 0xab, 0x73, 0x1c, 0xe8,
	0x6f, 0x4f, 0x60, 0xc2, 0xa2, 0xe1, 0xa3, 0x8d, 0x2f, 0x55, 0xbf, 0x98, 0xc4, 0xd9, 0xb6, 0x12,
	0x69, 0x18, 0x59, 0xc2, 0x14, 0x09, 0x32, 0xc5, 0x39, 0x97, 0x2a, 0x55, 0x87, 0xc9, 0x1d, 0x59,
	0xab, 0xe3, 0x06, 0x6a, 0x98, 0xcb, 0xfd, 0x11, 0x92, 0x9c, 0x32, 0x8e, 0x91, 0xb7, 0x63, 0xb4,
	0x00, 0x17, 0xa6, 0x09, 0x15, 0xea, 0x41, 0x05, 0x2c, 0x62, 0x63, 0xbc, 0xb5, 0x96, 0xbd, 0xce,
	0xb7, 0x65, 0xea, 0x1e, 0xec, 0x74, 0x0d, 0x16, 0x5d, 0xcf, 0x72, 0xdc, 0x53, 0x3f, 0x6c, 0x11,
	0x4c, 0xe5, 0xff, 0x4d, 0xc2, 0x23, 0x24, 0x17, 0x5c, 0x6f, 0x93, 0x95, 0x3f, 0xc1, 0x62, 0xfd,
	0x33, 0xb0, 0x92, 0x06, 0x14, 0xaf, 0x5a, 0xa7, 0x50, 0x42, 0x74, 0x47, 0x85, 0xc6, 0x87, 0xad,
	0x54, 0x94, 0x4e, 0x5c, 0x47, 0x1e, 0x9b, 0xb1, 0x8d, 0x06, 0x68, 0x11, 0x3f, 0x09, 0x7b, 0x17,
	0xf4, 0x4c, 0x9f, 0xe2, 0x1a, 0xae, 0x68, 0x56, 0x52, 0x1d, 0x6e, 0xda, 0x7d, 0xe3, 0x47, 0x1a,
	0x5c, 0x37, 0x49, 0xe0, 0x87, 0x71, 0x96, 0x87, 0x5f, 0x3a, 0xbb, 0xd3, 0x39, 0x62, 0x26, 0x55,
	0xc8, 0xc4, 0x10, 0x15, 0x32, 0x99, 0x52, 0x21, 0xa9, 0x44, 0x85, 0x53, 0x39, 0x89, 0x0a, 0x87,
	0xcc, 0x62, 0x7c, 0x9b, 0xe5, 0xa1, 0x54, 0xec, 0xa2, 0x17, 0x9e, 0x30, 0x43, 0xea, 0x03, 0x0d,
	0x0d, 0x5b, 0x8a, 0x28, 0xc6, 0x16, 0xfe, 0x22, 0x5c, 0xdd, 0x76, 0x1d, 0x92, 0xf1, 0x5b, 0xc6,
	0x25, 0xe0, 0x65, 0x98, 0xf6, 0x03, 0x35, 0x52, 0x6c, 0xca, 0x0f, 0x30, 0x4c, 0xcc, 0x85, 0x6b,
	0xb9, 0xdd, 0x8f, 0x2f, 0xa5, 0x19, 0xde, 0x29, 0x64, 0x79, 0xc7, 0xf8, 0x06, 0xa6, 0x1e, 0x78,
	0x96, 0xfb, 0x5f, 0x16, 0xe3, 0xce, 0x47, 0xae, 0x6f, 0x41, 0x59, 0x5f, 0xe3, 0xcf, 0x6b, 0x6a,
	0xf8, 0xed, 0xc0, 0x10, 0x17, 0x09, 0xa4, 0x57, 0x6f, 0xb8, 0x0b, 0xe3, 0xdc, 0x70, 0x2b, 0xb7,
	0xdb, 0xc6, 0x9f, 0x50, 0xd1, 0x92, 0x09, 0x76, 0x45, 0x2a, 0xdf, 0xd7, 0x2c, 0x0b, 0x06, 0x51,
	0x63, 0x1d, 0x06, 0x47, 0x1f, 0x9f, 0x2a, 0xcb, 0x30, 0x19, 0xfb, 0x76, 0x24, 0x3c, 0x57, 0xf6,
	0x61, 0x7c, 0xab, 0x00, 0x57, 0xab, 0xbe, 0x17, 0xf5, 0x3a, 0x71, 0x3a, 0xef, 0x6f, 0x7d, 0x77,
	0x37, 0x6a, 0x0f, 0x04, 0xc1, 0x6b, 0x17, 0x0e, 0x82, 0x7f, 0xb5, 0x66, 0x0e, 0x8f, 0xd0, 0x09,
	0x43, 0xdb, 0x6b, 0x13, 0x87, 0x9f, 0x96, 0x82, 0x1b, 0xad, 0xf3, 0x12, 0xc5, 0x9e, 0x9f, 0x4c,
	0xd9, 0xf3, 0x2b, 0x30, 0xc5, 0x83, 0xf0, 0xa7, 0xf8, 0x13, 0x72, 0x0c, 0xbf, 0xff, 0x61, 0x01,
	0xae, 0xf2, 0xb6, 0xaf, 0x97, 0x0c, 0xcb, 0xa9, 0xd8, 0x4d, 0x11, 0xb7, 0xf9, 0x44, 0x7d, 0xee,
	0x33, 0xea, 0x26, 0x86, 0xb9, 0x4c, 0xd8, 0xcb, 0x70, 0xf1, 0xda, 0x87, 0xdd, 0xf2, 0x05, 0xb6,
	0x2b, 0x48, 0x32, 0xe5, 0x46, 0x0d, 0xdb, 0x1d, 0x97, 0x1c, 0xfa, 0x5d, 0x98, 0x0f, 0xec, 0xbe,
	0x75, 0x6a, 0x77, 0x5c, 0xc7, 0x3a, 0xf1, 0x7b, 0x21, 0xdf, 0x54, 0xe6, 0x02, 0xbb, 0xff, 0x94,
	0x16, 0x6e, 0xfb, 0xbd, 0xd0, 0x38, 0x91, 0x16, 0x98, 0x40, 0x45, 0xbe, 0xf4, 0x51, 0xb2, 0xcb,
	0x20, 0x76, 0x18, 0x55, 0x20, 0x2a, 0xd5, 0x2c, 0x4a, 0xb4, 0x40, 0xfc, 0x7b, 0x60, 0xf2, 0x46,
	0xa8, 0x98, 0x79, 0x23, 0xb4, 0x03, 0x57, 0xc5, 0x2b, 0x39, 0xf9, 0x17, 0x0b, 0x87, 0x81, 0x63,
	0xc7, 0x98, 0x6f, 0x6f, 0xc8, 0x9f, 0x32, 0x68, 0x43, 0xfe, 0x94, 0xc1, 0xf8, 0xe3, 0x70, 0x8b,
	0x7a, 0x0c, 0xf9, 0x29, 0xba, 0x5f, 0xb7, 0x5c, 0x63, 0xb6, 0xd0, 0xa1, 0x83, 0x8f, 0xbf, 0x35,
	0x7d, 0x2a, 0x73, 0xdd, 0xd6, 0x3d, 0xd3, 0xf7, 0xbb, 0xfc, 0x22, 0xec, 0x75, 0x26, 0x5c, 0x6d,
	0x40, 0x25, 0x3b, 0xde, 0x68, 0xe7, 0x5d, 0x94, 0x3d, 0x3d, 0x4b, 0x6a, 0x01, 0xca, 0x9e, 0xd8,
	0x87, 0xf1, 0x1d, 0xf6, 0x06, 0x6c, 0x70, 0x16, 0xe3, 0xab, 0xb9, 0x2d, 0x9e, 0x51, 0x42, 0xd1,
	0xfd, 0x6b, 0x67, 0xe7, 0x2a, 0x4d, 0x8d, 0x88, 0xc9, 0x26, 0x70, 0x96, 0x5f, 0x41, 0x94, 0xf0,
	0x2e, 0x5c, 0xbd, 0x57, 0x1a, 0x97, 0xb2, 0x3a, 0x4c, 0x04, 0xc9, 0x9f, 0x51, 0xe2, 0x6f, 0xe3,
	0x08, 0x96, 0x44, 0x24, 0xc1, 0x33, 0x37, 0x3e, 0x49, 0x9e, 0x08, 0x8e, 0x93, 0x0c, 0xe3, 0x9c,
	0x37, 0xf9, 0x7f, 0xa6, 0x88, 0x57, 0x6f, 0x39, 0xf8, 0x5f, 0xe4, 0x1d, 0x2c, 0x57, 0xca, 0x85,
	0xf3, 0x32, 0x94, 0xe5, 0x4c, 0x4b, 0xa8, 0xec, 0xaa, 0xd8, 0x00, 0x8a, 0x17, 0xea, 0x84, 0x6d,
	0x0f, 0x87, 0xb0, 0xe0, 0x63, 0xfc, 0x5c, 0xb2, 0xc6, 0xe7, 0x66, 0x46, 0xc9, 0xeb, 0xae, 0x8c,
	0xbd, 0xc8, 0xb4, 0x5e, 0x37, 0x00, 0xba, 0x36, 0xfe, 0xe9, 0x67, 0xdc, 0x11, 0xce, 0xd8, 0x2c,
	0x2d, 0xc1, 0x78, 0x31, 0x91, 0xef, 0x9b, 0xd5, 0x32, 0x43, 0x73, 0x26, 0xea, 0x1d, 0xb1, 0x4a,
	0x03, 0x30, 0x7b, 0x3c, 0x7b, 0xf2, 0x22, 0xa3, 0xc9, 0xcd, 0x12, 0x2d, 0x6c, 0xd8, 0x6d, 0xb2,
	0xd7, 0xeb, 0x1a, 0xbf, 0xa6, 0xc1, 0x2d, 0x93, 0x74, 0x5d, 0xcf, 0xa1, 0x43, 0x7e, 0xe8, 0xbb,
	0x9e, 0x92, 0x2e, 0xf0, 0x15, 0x67, 0xc2, 0x48, 0x7b, 0x7a, 0xc5, 0xac, 0xa7, 0xb7, 0x07, 0xb7,
	0xcf, 0xc0, 0x64, 0x7c, 0xfd, 0xf3, 0x24, 0x89, 0xf8, 0x62, 0x4a, 0xec, 0xa9, 0xdd, 0xc9, 0x49,
	0xa0, 0x87, 0xa9, 0xca, 0x58, 0x06, 0xf8, 0x53, 0xbb, 0x23, 0xaf, 0x01, 0x64, 0x13, 0xe3, 0xf3,
	0xf2, 0x31, 0x0e, 0xef, 0x87, 0xfd, 0x71, 0xc3, 0x60, 0x4f, 0xf3, 0x50, 0xe8, 0x9c, 0xf2, 0x0e,
	0x0a, 0x9d, 0x53, 0xe3, 0x7f, 0x26, 0x7f, 0x81, 0x92, 0x4d, 0x14, 0xaf, 0x7f, 0x0c, 0x2b, 0x3c,
	0xf8, 0x57, 0x0e, 0xad, 0x3e, 0x21, 0x7a, 0x67, 0xd4, 0xd4, 0xf3, 0x4f, 0xed, 0x8e, 0xa9, 0xb3,
	0x08, 0x61, 0xf1, 0x2d, 0xf6, 0xdb, 0xee, 0x69, 0xa0, 0xb8, 0xd4, 0x53, 0xdd, 0xd3, 0xe0, 0xd0,
	0x75, 0xf4, 0xaf, 0xc3, 0x52, 0xec, 0x07, 0x56, 0x48, 0x5a, 0xa7, 0x72, 0xf0, 0xce, 0xe9, 0xc8,
	0x7f, 0xf5, 0xa4, 0x92, 0xc0, 0xac, 0xc4, 0x7e, 0x60, 0x92, 0xd6, 0xa9, 0x28, 0x3c, 0x35, 0xfe,
	0x53, 0x92, 0x83, 0x56, 0xa9, 0x78, 0x6a, 0x2b, 0x7f, 0x2d, 0x72, 0x4e, 0xe4, 0x4e, 0x7a, 0x93,
	0x2a, 0x64, 0x1d, 0xb1, 0xe4, 0x21, 0x78, 0x37, 0xf3, 0x10, 0x7c, 0x37, 0x1a, 0x4e, 0xd4, 0x89,
	0x57, 0x42, 0x54, 0xe3, 0x3f, 0x68, 0x70, 0x63, 0x00, 0xf2, 0xc0, 0x0f, 0x7e, 0x22, 0x93, 0x1b,
	0xb2, 0x70, 0x13, 0xaf, 0x66, 0xe1, 0x7e, 0x3b, 0x6f, 0x6a, 0xbb, 0xa7, 0x3f, 0x99, 0xa9, 0x29,
	0xcc, 0x3a, 0xa1, 0x32, 0xab, 0xf1, 0xfb, 0x05, 0xc9, 0x4e, 0xbb, 0xa7, 0x01, 0xd5, 0x0b, 0x4d,
	0x12, 0xc7, 0x1d, 0xd2, 0x25, 0x5e, 0xcc, 0x43, 0x38, 0x5f, 0x23, 0x5a, 0x6b, 0x30, 0x83, 0x68,
	0x51, 0xeb, 0x61, 0x48, 0xd8, 0x1e, 0xc5, 0x1b, 0x4f, 0xe9, 0xb3, 0x7f, 0x7b, 0x37, 0x79, 0xfe,
	0xdf, 0xde, 0x4d, 0x0d, 0xfe, 0xed, 0xdd, 0x43, 0x58, 0xe6, 0xfb, 0x6c, 0x2f, 0xe2, 0x7f, 0xe5,
	0xab, 0xfc, 0xe9, 0xef, 0x62, 0x4f, 0x64, 0x4d, 0xaf, 0xc7, 0xa4, 0x8b, 0x62, 0xfe, 0x06, 0x94,
	0x28, 0x8a, 0xe2, 0x54, 0x9d, 0xc5, 0x2f, 0xcf, 0x76, 0x4f, 0x03, 0x96, 0xe2, 0xdf, 0xd8, 0xc0,
	0xec, 0x50, 0xe9, 0x85, 0x1d, 0xf7, 0x28, 0xfb, 0x44, 0x0d, 0xc7, 0x7a, 0x09, 0x37, 0xef, 0x16,
	0xcc, 0xf1, 0xb7, 0xb6, 0xa9, 0xbf, 0x2c, 0x60, 0x0f, 0x6e, 0x51, 0xbe, 0xdc, 0x8c, 0x47, 0x2f,
	0x52, 0x8e, 0xbd, 0x86, 0xbc, 0x59, 0x7f, 0x23, 0xeb, 0xda, 0xa7, 0xc7, 0x1a, 0x7f, 0x76, 0x5f,
	0x87, 0xc5, 0x20, 0x4c, 0xd2, 0xac, 0xa9, 0x51, 0xc2, 0xef, 0x8f, 0x22, 0x9c, 0xa9, 0xf1, 0x17,
	0x82, 0x74, 0x81, 0xf1, 0x9b, 0x05, 0xe9, 0x67, 0x28, 0xb0, 0x2f, 0xf7, 0xaf, 0xe8, 0xdf, 0x80,
	0x39, 0x87, 0x1c, 0xdb, 0xbd, 0x8e, 0xe2, 0xda, 0x94, 0x1e, 0xfd, 0xfc, 0x68, 0xc8, 0xa6, 0x71,
	0xa0, 0xae, 0x10, 0x26, 0x05, 0x37, 0x4b, 0xbc, 0x4b, 0x0c, 0x9a, 0xfc, 0x2a, 0xcc, 0x3a, 0xbe,
	0xc7, 0x23, 0x2b, 0x27, 0x5e, 0x45, 0xf7, 0x33, 0xb4, 0x3f, 0x74, 0xbc, 0xfe, 0x76, 0x01, 0xee,
	0x8c, 0xd0, 0x42, 0x37, 0x61, 0x16, 0x13, 0xf1, 0x29, 0x53, 0x7c, 0x3c, 0xce, 0x7a, 0xc8, 0xee,
	0xcc, 0x19, 0xda, 0x0f, 0xce, 0xeb, 0xeb, 0x30, 0x9f, 0x79, 0xaf, 0x34, 0xf1, 0x32, 0x1d, 0x97,
	0xbd, 0xd4, 0xd3, 0xa6, 0xaf, 0x42, 0xd9, 0xee, 0x39, 0x2e, 0xf1, 0x5a, 0x9c, 0x72, 0x93, 0x2f,
	0xd3, 0xf9, 0x9c, 0xe8, 0x0b, 0xa9, 0xd6, 0x90, 0x61, 0xa7, 0x79, 0xc0, 0xf8, 0xfe, 0x9a, 0x44,
	0x2d, 0x6e, 0x4f, 0xf2, 0xab, 0x4e, 0x5a, 0xc2, 0x0c, 0x4a, 0x1d, 0x26, 0x94, 0x44, 0x19, 0xf8,
	0xdb, 0xd8, 0x91, 0xae, 0xb6, 0x78, 0xa0, 0x35, 0x9a, 0xc7, 0xb5, 0xac, 0xbe, 0xae, 0x12, 0x09,
	0x5c, 0x8d, 0x1f, 0x15, 0xe1, 0x52, 0xbe, 0x4c, 0x0c, 0xe1, 0xf1, 0x51, 0x73, 0x6b, 0xec, 0x81,
	0xa4, 0xf2, 0x78, 0x67, 0x19, 0x62, 0x56, 0xe6, 0x9c, 0x68, 0x2f, 0x52, 0x66, 0x07, 0x36, 0x6d,
	0x94, 0xd2, 0xfa, 0x22, 0x71, 0x3c, 0xad, 0x31, 0x15, 0xd5, 0x3f, 0x08, 0x4d, 0x37, 0x80, 0xc9,
	0x1c, 0x68, 0xba, 0x0b, 0x7c, 0x55, 0xfc, 0x8b, 0xbf, 0xcc, 0xf1, 0xcd, 0xde, 0xc2, 0x7c, 0x30,
	0x36, 0x07, 0xb0, 0x7f, 0x5f, 0xe6, 0xbf, 0x10, 0xef, 0xcf, 0xc0, 0xca, 0x20, 0xde, 0x56, 0xd0,
	0xe2, 0x31, 0x71, 0x7a, 0x16, 0xf5, 0x46, 0x2b, 0xa7, 0x49, 0xd7, 0x79, 0x4c, 0x9b, 0xcc, 0xe4,
	0x34, 0xd9, 0x75, 0x1e, 0x37, 0x5a, 0xc6, 0xf7, 0x34, 0xb8, 0x99, 0xbf, 0x8c, 0x89, 0x3d, 0x91,
	0xab, 0x30, 0xb5, 0x57, 0xa4, 0x30, 0xd3, 0x09, 0x83, 0xe4, 0x3f, 0xfc, 0xf3, 0x84, 0x41, 0xdf,
	0xd2, 0x60, 0x09, 0xe3, 0x8f, 0x2f, 0x18, 0xaa, 0x7a, 0x8e, 0x9f, 0x33, 0x62, 0x8e, 0x21, 0x63,
	0x1d, 0x96, 0xd3, 0x58, 0x8c, 0xef, 0xe3, 0xfc, 0x59, 0x0d, 0xae, 0xb3, 0x58, 0xa4, 0x46, 0x48,
	0x02, 0x3b, 0x24, 0xce, 0xeb, 0x8d, 0xbe, 0x3d, 0x27, 0x38, 0xdc, 0xf8, 0x10, 0x6e, 0x0c, 0x41,
	0x67, 0xfc, 0xb9, 0x79, 0x6a, 0x84, 0x2e, 0xef, 0xef, 0x35, 0xa6, 0xdf, 0x31, 0xbe, 0xab, 0xa9,
	0xa1, 0xba, 0xa9, 0x01, 0x2f, 0xf4, 0xa7, 0x28, 0xc9, 0x03, 0xb9, 0xc2, 0xc5, 0x1e, 0xc8, 0xcd,
	0x04, 0xfc, 0xd7, 0xda, 0xff, 0xd1, 0xe4, 0xe3, 0xfb, 0x26, 0xdf, 0xda, 0xaf, 0x3c, 0xab, 0x6d,
	0x6e, 0xd6, 0xf7, 0xb6, 0xac, 0xe6, 0xc1, 0xfa, 0x56, 0x2d, 0x93, 0xa6, 0xec, 0x26, 0x5c, 0x4b,
	0x57, 0x3f, 0xab, 0xed, 0x54, 0xf7, 0x77, 0x6b, 0xd6, 0xd6, 0x61, 0xad, 0x79, 0x50, 0xd1, 0xf4,
	0x3b, 0x70, 0x33, 0x0d, 0xb0, 0x61, 0xd6, 0x37, 0x6b, 0xd6, 0x96, 0xb9, 0xbf, 0xbf, 0x6b, 0xd5,
	0xf6, 0x0e, 0x6a, 0x66, 0xa5, 0xa0, 0x1b, 0xf0, 0x46, 0x1a, 0x68, 0x67, 0xff, 0x69, 0xcd, 0xda,
	0xac, 0x55, 0x77, 0xd6, 0x59, 0x62, 0xb3, 0x4a, 0x71, 0x70, 0xa4, 0xda, 0x47, 0xd5, 0xed, 0xf5,
	0xbd, 0xad, 0x9a, 0x65, 0xd6, 0xf7, 0xb6, 0x2a, 0x13, 0xfa, 0x35, 0xb8, 0x9c, 0x06, 0xd8, 0xae,
	0x6f, 0x6d, 0xef, 0xd4, 0xb7, 0xb6, 0x0f, 0x2a, 0x93, 0x83, 0xd3, 0xd8, 0x32, 0xf7, 0x0f, 0x1b,
	0x56, 0x63, 0x7b, 0xff, 0x60, 0xbf, 0x32, 0xb5, 0x66, 0x83, 0xae, 0xfc, 0xd5, 0x08, 0xff, 0x1b,
	0x33, 0xfd, 0x4d, 0xb8, 0xad, 0x80, 0x59, 0xcd, 0xc3, 0x8d, 0x5c, 0x1a, 0xdc, 0x85, 0x5b, 0xf9,
	0x60, 0xf8, 0xbd, 0x65, 0xae, 0x37, 0xb6, 0x2b, 0xda, 0xda, 0xbf, 0x53, 0x28, 0x8b, 0x77, 0x7a,
	0x2a, 0x4a, 0xd5, 0xda, 0x5e, 0xb6, 0x57, 0x95, 0x70, 0x58, 0x3d, 0x48, 0x38, 0x2d, 0x45, 0x14,
	0x04, 0x4a, 0x13, 0xa5, 0x90, 0x22, 0x0a, 0x02, 0x24, 0x44, 0x29, 0x0e, 0x62, 0xa0, 0x12, 0x65,
	0x22, 0xb5, 0x2a, 0x58, 0x3d, 0xb0, 0x2a, 0x93, 0x6b, 0xff, 0x5e, 0x4b, 0xa7, 0xc1, 0xc2, 0x37,
	0x0c, 0x4a, 0x43, 0x64, 0x03, 0xeb, 0xe0, 0x2b, 0x8d, 0xda, 0x60, 0x7e, 0xbb, 0x1c, 0x18, 0x9c,
	0x23, 0xcb, 0x6f, 0x97, 0x53, 0x8b, 0x73, 0xaf, 0x14, 0x54, 0xbc, 0xb3, 0x6d, 0x9b, 0x95, 0xa2,
	0x7e, 0x1b, 0x6e, 0x0c, 0xad, 0xde, 0x5d, 0xaf, 0x6f, 0x56, 0x26, 0xf4, 0x37, 0xe0, 0x6a, 0x0e,
	0xc8, 0x13, 0xb3, 0x5e, 0xdb, 0xdb, 0x6c, 0x56, 0x26, 0xd7, 0xfe, 0xa9, 0x06, 0x15, 0x96, 0xc5,
	0x53, 0xc9, 0x3a, 0x6d, 0xc0, 0x1b, 0xd5, 0xed, 0xf5, 0xba, 0x69, 0x99, 0xfb, 0x87, 0x7b, 0x9b,
	0x74, 0x8d, 0x0f, 0x0e, 0x9b, 0x99, 0x69, 0xe5, 0xc3, 0xb0, 0x8f, 0x8d, 0xda, 0x56, 0x7d, 0xaf,
	0xa2, 0x51, 0xb6, 0x1a, 0x0a, 0xb3, 0x65, 0xae, 0x6f, 0x6c, 0xb0, 0xa5, 0xbb, 0x05, 0xd7, 0x87,
	0x82, 0xd5, 0xf6, 0x36, 0x2b, 0xc5, 0x21, 0x10, 0x5b, 0xeb, 0xbb, 0x35, 0x6b, 0xff, 0x69, 0xcd,
	0xac, 0x4c, 0xac, 0xfd, 0xa5, 0xe4, 0x31, 0xa2, 0x92, 0xce, 0x4b, 0x61, 0xad, 0xfa, 0xde, 0xd3,
	0xfa, 0x41, 0x2d, 0x6f, 0x81, 0x14, 0x2a, 0xaa, 0x40, 0xb8, 0x06, 0xcd, 0xdd, 0x75, 0x3a, 0x11,
	0x65, 0x9d, 0x55, 0x10, 0x85, 0xd2, 0x05, 0x95, 0xd2, 0x2a, 0x0c, 0x23, 0x75, 0xa5, 0xb8, 0xf6,
	0x2f, 0x35, 0x79, 0xe8, 0x55, 0x57, 0xb2, 0x73, 0x51, 0x22, 0x65, 0xda, 0xe5, 0xd2, 0x7b, 0x10,
	0x4b, 0x0e, 0xf6, 0x6c, 0xbd, 0x7e, 0x40, 0xe9, 0x98, 0x87, 0x25, 0x07, 0x59, 0xaf, 0x56, 0x6b,
	0x8d, 0x83, 0x1a, 0xc5, 0x72, 0x68, 0x37, 0x66, 0xed, 0xc9, 0x61, 0xb3, 0x46, 0x89, 0x3d, 0xb4,
	0x9b, 0xea, 0xfa, 0x5e, 0xb5, 0xb6, 0x53, 0xdb, 0xac, 0x4c, 0xac, 0xf5, 0xd3, 0x39, 0x50, 0xb2,
	0xc2, 0xd0, 0x30, 0xeb, 0xd5, 0x5c, 0x5a, 0xe7, 0xc3, 0x98, 0xb5, 0x4d, 0x6b, 0xb3, 0xbe, 0xbe,
	0xbb, 0xbf, 0xb7, 0x59, 0xd1, 0x54, 0xa6, 0x57, 0x60, 0x0e, 0xac, 0x8d, 0xda, 0xfa, 0x5e, 0xa5,
	0xb0, 0xf6, 0x57, 0x73, 0x9e, 0xb8, 0xe3, 0xf8, 0x6f, 0x81, 0x21, 0xda, 0xad, 0xef, 0xd5, 0x77,
	0x59, 0x9e, 0xc8, 0xb3, 0xd7, 0x3b, 0x03, 0x47, 0xe7, 0x59, 0xaf, 0x56, 0xb4, 0x33, 0x40, 0x76,
	0xf6, 0x0f, 0x0e, 0xea, 0xb5, 0xf4, 0x72, 0x67, 0x40, 0x9e, 0xae, 0x37, 0x2a, 0xc5, 0xb5, 0xef,
	0x25, 0xfa, 0x82, 0xfd, 0xd7, 0x68, 0x86, 0x44, 0x3b, 0xb5, 0xa7, 0xb5, 0x9d, 0x3c, 0xf4, 0x94,
	0x8e, 0x15, 0x98, 0x86, 0x59, 0xdf, 0x5d, 0x37, 0xbf, 0x92, 0xde, 0x67, 0x94, 0xfa, 0x3a, 0xd5,
	0x93, 0xbb, 0xb5, 0xcd, 0xfa, 0xfa, 0x01, 0xc5, 0x4e, 0x51, 0x97, 0x0a, 0xd0, 0xfa, 0xe6, 0x53,
	0xba, 0x84, 0x94, 0x1b, 0xbf, 0x93, 0xc8, 0x4b, 0xf2, 0x3e, 0x4b, 0xed, 0xbb, 0xba, 0xbd, 0x6e,
	0x6e, 0xd5, 0xce, 0x51, 0x68, 0x2a, 0x50, 0x83, 0x8a, 0x81, 0x36, 0xac, 0xf6, 0x89, 0x59, 0xab,
	0xa5, 0xb5, 0xb4, 0x5a, 0xbb, 0x5b, 0xff, 0xa8, 0x52, 0x5c, 0x7b, 0x2e, 0x51, 0x4a, 0x1e, 0x79,
	0xa9, 0x28, 0x1d, 0xd4, 0x77, 0x87, 0x08, 0x87, 0xc2, 0x32, 0x2a, 0xd0, 0xd6, 0x3e, 0x13, 0x0c,
	0x85, 0xa4, 0x6a, 0x75, 0x75, 0x7f, 0x17, 0x15, 0xd0, 0xda, 0x77, 0x35, 0xe9, 0x22, 0x65, 0x9e,
	0xf2, 0xe9, 0xf7, 0xe0, 0xae, 0xdc, 0x1a, 0x0e, 0x37, 0x9a, 0x55, 0xb3, 0xbe, 0x31, 0x04, 0x87,
	0xb7, 0xe1, 0xce, 0x50, 0x48, 0x59, 0x40, 0x29, 0xf4, 0x0e, 0xbc, 0x3d, 0x14, 0x70, 0x6f, 0xff,
	0x40, 0x05, 0x2e, 0x50, 0x36, 0x5a, 0x51, 0xf2, 0x3c, 0x34, 0x4f, 0xdc, 0xa0, 0xc9, 0x72, 0x2f,
	0xdc, 0x83, 0xbb, 0xb5, 0xbd, 0x4d, 0xcb, 0xac, 0xed, 0x30, 0xde, 0x6b, 0x6e, 0xd7, 0x1b, 0x56,
	0x73, 0xff, 0xd0, 0xac, 0x66, 0x17, 0xec, 0x3e, 0xbc, 0x39, 0x14, 0x52, 0x4a, 0xda, 0xfa, 0x16,
	0xdd, 0x8e, 0x1e, 0xc0, 0xda, 0x50, 0xd0, 0x46, 0xcd, 0x6c, 0xee, 0xef, 0xad, 0xef, 0x58, 0xdb,
	0xfb, 0xbb, 0x35, 0x84, 0x2f, 0xac, 0xfd, 0x43, 0x0d, 0xca, 0x3c, 0x3c, 0x92, 0x13, 0xec, 0x0d,
	0xb8, 0xda, 0x30, 0xf7, 0x1b, 0xfb, 0xcd, 0x21, 0x64, 0xba, 0x0a, 0x97, 0x32, 0xf5, 0x4c, 0xc9,
	0x50, 0xca, 0x0c, 0xd6, 0x35, 0x0f, 0xab, 0xd5, 0x5a, 0xb3, 0x59, 0x29, 0xe8, 0x57, 0x60, 0x25,
	0x53, 0xf7, 0x64, 0xbd, 0xbe, 0x83, 0x0a, 0xeb, 0x1a, 0x5c, 0xce, 0x54, 0x25, 0x9a, 0x2a, 0xa7,
	0x4f, 0xca, 0x02, 0xfb, 0x87, 0x07, 0x95, 0xc9, 0xb5, 0x8f, 0x00, 0xb6, 0x5d, 0x87, 0xec, 0x63,
	0x9c, 0x0d, 0xe5, 0xdc, 0x6d, 0x6a, 0x78, 0xec, 0x37, 0xf2, 0xb8, 0x7e, 0x05, 0x16, 0x53, 0xb5,
	0xdb, 0x6c, 0xff, 0xce, 0x16, 0x37, 0xb7, 0xf7, 0x9f, 0x55, 0x0a, 0x6b, 0x7f, 0x57, 0x53, 0x4f,
	0x05, 0xd4, 0x18, 0x17, 0x66, 0x12, 0x3d, 0x80, 0xb5, 0x44, 0xc7, 0xd5, 0x9e, 0xd6, 0x6b, 0xcf,
	0x2c, 0xb3, 0xc6, 0xe9, 0x9c, 0x67, 0x23, 0x3d, 0x84, 0x77, 0xce, 0x81, 0x37, 0x6b, 0xcd, 0x9a,
	0xf9, 0xb4, 0x26, 0x16, 0xf2, 0x73, 0xf0, 0xe8, 0x9c, 0x06, 0xd4, 0x7c, 0xda, 0xab, 0xed, 0xd0,
	0x6a, 0xc1, 0x05, 0x95, 0xc2, 0xda, 0x6f, 0x26, 0xa2, 0x20, 0x83, 0x22, 0xf8, 0xca, 0xde, 0x87,
	0x37, 0x45, 0x97, 0x62, 0xb0, 0xfa, 0x6e, 0xfe, 0x22, 0x2b, 0x42, 0x3b, 0x08, 0xfa, 0x74, 0x7d,
	0x07, 0x35, 0x85, 0xb2, 0xf1, 0x0d, 0x02, 0xd5, 0x3e, 0x6a, 0xd4, 0x4d, 0x94, 0x80, 0x7f, 0x96,
	0xba, 0xf2, 0x49, 0xf9, 0xe2, 0xfa, 0x1a, 0xbc, 0xa5, 0xcc, 0xd2, 0x6a, 0x98, 0xfb, 0x5b, 0x66,
	0xad, 0xd9, 0xcc, 0x35, 0x5e, 0x95, 0xe1, 0x72, 0x60, 0xf7, 0xf7, 0xac, 0x5d, 0x54, 0xfd, 0x8a,
	0xc0, 0xe7, 0x80, 0x35, 0xa9, 0x74, 0x6c, 0xd5, 0x9f, 0x1c, 0x54, 0x0a, 0x99, 0x35, 0xcc, 0x42,
	0x2a, 0x55, 0x8d, 0x75, 0xb3, 0x56, 0x29, 0x6e, 0xec, 0xc1, 0x6a, 0xcb, 0xef, 0x3e, 0xe8, 0xbb,
	0x7d, 0xbf, 0x47, 0xfd, 0x96, 0xae, 0xef, 0x90, 0xce, 0x83, 0x20, 0xf4, 0x63, 0xff, 0xab, 0x8f,
	0xda, 0x7e, 0xc7, 0xf6, 0xda, 0x0f, 0x1e, 0x3f, 0x8a, 0xe3, 0x07, 0x2d, 0xbf, 0xfb, 0x10, 0x8b,
	0x5b, 0x7e, 0xe7, 0xa1, 0x1d, 0x04, 0x0f, 0x73, 0x3d, 0x9c, 0xa3, 0x29, 0x84, 0xf9, 0xec, 0xff,
	0x0f, 0x00, 0x00, 0xff, 0xff, 0x8f, 0xee, 0x9d, 0xec, 0x2f, 0x92, 0x00, 0x00,
}
