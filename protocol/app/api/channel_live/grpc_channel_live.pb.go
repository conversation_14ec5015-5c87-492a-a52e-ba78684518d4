// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/channel_live/grpc_channel_live.proto

package channel_live // import "golang.52tt.com/protocol/app/api/channel_live"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import channel_live_logic "golang.52tt.com/protocol/app/channel-live-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelLiveLogicClient is the client API for ChannelLiveLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelLiveLogicClient interface {
	GetLiveChannelInfo(ctx context.Context, in *channel_live_logic.GetLiveChannelInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetLiveChannelInfoResp, error)
	ChannelLiveHeartbeat(ctx context.Context, in *channel_live_logic.ChannelLiveHeartbeatReq, opts ...grpc.CallOption) (*channel_live_logic.ChannelLiveHeartbeatResp, error)
	SetChannelLiveStatus(ctx context.Context, in *channel_live_logic.SetChannelLiveStatusReq, opts ...grpc.CallOption) (*channel_live_logic.SetChannelLiveStatusResp, error)
	GetChannelLiveStatus(ctx context.Context, in *channel_live_logic.GetChannelLiveStatusReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveStatusResp, error)
	ApplyPk(ctx context.Context, in *channel_live_logic.ApplyPkReq, opts ...grpc.CallOption) (*channel_live_logic.ApplyPkResp, error)
	HandlerApply(ctx context.Context, in *channel_live_logic.HandlerApplyReq, opts ...grpc.CallOption) (*channel_live_logic.HandlerApplyResp, error)
	BatchGetChannelLiveStatusByAccount(ctx context.Context, in *channel_live_logic.BatchGetChannelLiveStatusByAccountReq, opts ...grpc.CallOption) (*channel_live_logic.BatchGetChannelLiveStatusByAccountResp, error)
	SetPkStatus(ctx context.Context, in *channel_live_logic.SetPkStatusReq, opts ...grpc.CallOption) (*channel_live_logic.SetPkStatusResp, error)
	CancelPKApply(ctx context.Context, in *channel_live_logic.CancelPKApplyReq, opts ...grpc.CallOption) (*channel_live_logic.CancelPKApplyResp, error)
	GetChannelLivePKRecord(ctx context.Context, in *channel_live_logic.GetChannelLivePKRecordReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLivePKRecordResp, error)
	GetChannelLivePkRankUser(ctx context.Context, in *channel_live_logic.GetChannelLivePkRankUserReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLivePkRankUserResp, error)
	GetChannelLiveRankUser(ctx context.Context, in *channel_live_logic.GetChannelLiveRankUserReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveRankUserResp, error)
	GetChannelLiveWatchTimeRankUser(ctx context.Context, in *channel_live_logic.GetChannelLiveWatchTimeRankUserReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveWatchTimeRankUserResp, error)
	GetChannelLiveData(ctx context.Context, in *channel_live_logic.GetChannelLiveDataReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveDataResp, error)
	GetFansRankList(ctx context.Context, in *channel_live_logic.GetFansRankListReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansRankListResp, error)
	GetFansInfo(ctx context.Context, in *channel_live_logic.GetFansInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansInfoResp, error)
	GetAnchorFansInfo(ctx context.Context, in *channel_live_logic.GetAnchorFansInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetAnchorFansInfoResp, error)
	SearchAnchor(ctx context.Context, in *channel_live_logic.SearchAnchorReq, opts ...grpc.CallOption) (*channel_live_logic.SearchAnchorResp, error)
	ReportClientIDChange(ctx context.Context, in *channel_live_logic.ReportClientIDChangeReq, opts ...grpc.CallOption) (*channel_live_logic.ReportClientIDChangeResp, error)
	GetApplyList(ctx context.Context, in *channel_live_logic.GetApplyListReq, opts ...grpc.CallOption) (*channel_live_logic.GetApplyListResp, error)
	GetPkInfo(ctx context.Context, in *channel_live_logic.GetPkInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetPkInfoResp, error)
	GetMyToolList(ctx context.Context, in *channel_live_logic.GetMyToolListReq, opts ...grpc.CallOption) (*channel_live_logic.GetMyToolListResp, error)
	GetItemConfig(ctx context.Context, in *channel_live_logic.GetItemConfigReq, opts ...grpc.CallOption) (*channel_live_logic.GetItemConfigResp, error)
	SetChannelLiveOpponentMicFlag(ctx context.Context, in *channel_live_logic.SetChannelLiveOpponentMicFlagReq, opts ...grpc.CallOption) (*channel_live_logic.SetChannelLiveOpponentMicFlagResp, error)
	StartPkMatch(ctx context.Context, in *channel_live_logic.StartPkMatchReq, opts ...grpc.CallOption) (*channel_live_logic.StartPkMatchResp, error)
	CancelPkMatch(ctx context.Context, in *channel_live_logic.CancelPkMatchReq, opts ...grpc.CallOption) (*channel_live_logic.CancelPkMatchResp, error)
	GetPKMatchInfo(ctx context.Context, in *channel_live_logic.GetPKMatchInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetPKMatchInfoResp, error)
	GetUserMissionList(ctx context.Context, in *channel_live_logic.GetUserMissionListReq, opts ...grpc.CallOption) (*channel_live_logic.GetUserMissionListResp, error)
	GetFansMissionList(ctx context.Context, in *channel_live_logic.GetFansMissionListReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansMissionListResp, error)
	GetActorMissionList(ctx context.Context, in *channel_live_logic.GetActorMissionListReq, opts ...grpc.CallOption) (*channel_live_logic.GetActorMissionListResp, error)
	HandleUserMissionAtInterval(ctx context.Context, in *channel_live_logic.HandleUserMissionAtIntervalReq, opts ...grpc.CallOption) (*channel_live_logic.HandleUserMissionAtIntervalResp, error)
	HandleShareLiveChannelMission(ctx context.Context, in *channel_live_logic.HandleShareLiveChannelMissionReq, opts ...grpc.CallOption) (*channel_live_logic.HandleShareLiveChannelMissionResp, error)
	GetProcessActorMissionDesc(ctx context.Context, in *channel_live_logic.GetProcessActorMissionDescReq, opts ...grpc.CallOption) (*channel_live_logic.GetProcessActorMissionDescResp, error)
	HandleFansMissionAtInterval(ctx context.Context, in *channel_live_logic.HandleFansMissionAtIntervalReq, opts ...grpc.CallOption) (*channel_live_logic.HandleFansMissionAtIntervalResp, error)
	GetAnchorHonorNameplate(ctx context.Context, in *channel_live_logic.GetAnchorHonorNameplateReq, opts ...grpc.CallOption) (*channel_live_logic.GetAnchorHonorNameplateResp, error)
	GetRankingList(ctx context.Context, in *channel_live_logic.GetRankingListReq, opts ...grpc.CallOption) (*channel_live_logic.GetRankingListResp, error)
	GetFansAddedGroupList(ctx context.Context, in *channel_live_logic.GetFansAddedGroupListReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansAddedGroupListResp, error)
	SetFansGroupName(ctx context.Context, in *channel_live_logic.SetFansGroupNameReq, opts ...grpc.CallOption) (*channel_live_logic.SetFansGroupNameResp, error)
	CheckSetGroupNamePermit(ctx context.Context, in *channel_live_logic.CheckSetGroupNamePermitReq, opts ...grpc.CallOption) (*channel_live_logic.CheckSetGroupNamePermitResp, error)
	CheckUserIsFans(ctx context.Context, in *channel_live_logic.CheckUserIsFansReq, opts ...grpc.CallOption) (*channel_live_logic.CheckUserIsFansResp, error)
	ChannelLiveReport(ctx context.Context, in *channel_live_logic.ChannelLiveReportReq, opts ...grpc.CallOption) (*channel_live_logic.ChannelLiveReportResp, error)
	AcceptAppointPk(ctx context.Context, in *channel_live_logic.AcceptAppointPkReq, opts ...grpc.CallOption) (*channel_live_logic.AcceptAppointPkResp, error)
	ConfirmAppointPkPush(ctx context.Context, in *channel_live_logic.ConfirmAppointPkPushReq, opts ...grpc.CallOption) (*channel_live_logic.ConfirmAppointPkPushResp, error)
	GetAppointPkInfo(ctx context.Context, in *channel_live_logic.GetAppointPkInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetAppointPkInfoResp, error)
	GetAnchorValidPlateList(ctx context.Context, in *channel_live_logic.GetAnchorValidPlateListReq, opts ...grpc.CallOption) (*channel_live_logic.GetAnchorValidPlateListResp, error)
	WearAnchorPlate(ctx context.Context, in *channel_live_logic.WearAnchorPlateReq, opts ...grpc.CallOption) (*channel_live_logic.WearAnchorPlateResp, error)
	GetChannelLiveMultiPkPermission(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkPermissionRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkPermissionResponse, error)
	SerarchPkAnchor(ctx context.Context, in *channel_live_logic.SerarchMultiPkAnchorRequest, opts ...grpc.CallOption) (*channel_live_logic.SerarchMultiPkAnchorResponse, error)
	ApplyChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.ApplyChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.ApplyChannelLiveMultiPkResponse, error)
	MatchMultiPk(ctx context.Context, in *channel_live_logic.MatchMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.MatchMultiPkResponse, error)
	CancelMatchMultiPk(ctx context.Context, in *channel_live_logic.CancelMatchMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.CancelMatchMultiPkResponse, error)
	AcceptChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.AcceptChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.AcceptChannelLiveMultiPkResponse, error)
	StartChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.StartChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.StartChannelLiveMultiPkResponse, error)
	GetChannelLiveMultiPkRank(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkRankRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkRankResponse, error)
	GetChannelLiveMultiPkKnightList(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkKnightListRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkKnightListResponse, error)
	GetChannelLiveMultiPkRecordList(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkRecordListRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkRecordListResponse, error)
	CancelChannelLiveMultiPkTeam(ctx context.Context, in *channel_live_logic.CancelChannelLiveMultiPkTeamRequest, opts ...grpc.CallOption) (*channel_live_logic.CancelChannelLiveMultiPkTeamResponse, error)
	StopChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.StopChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.StopChannelLiveMultiPkResponse, error)
	DisinviteChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.DisinviteChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.DisinviteChannelLiveMultiPkResponse, error)
	GetChannelLiveMultiPkTeamInfo(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkTeamInfoRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkTeamInfoResponse, error)
	InitChannelLiveMultiPkTeam(ctx context.Context, in *channel_live_logic.InitChannelLiveMultiPkTeamRequest, opts ...grpc.CallOption) (*channel_live_logic.InitChannelLiveMultiPkTeamResponse, error)
	LeaveFansGroup(ctx context.Context, in *channel_live_logic.LeaveFansGroupRequest, opts ...grpc.CallOption) (*channel_live_logic.LeaveFansGroupResponse, error)
	GetVirtualLiveChannelSecret(ctx context.Context, in *channel_live_logic.GetVirtualLiveChannelSecretRequest, opts ...grpc.CallOption) (*channel_live_logic.GetVirtualLiveChannelSecretResponse, error)
	GetUserFansGiftPri(ctx context.Context, in *channel_live_logic.GetUserFansGiftPriRequest, opts ...grpc.CallOption) (*channel_live_logic.GetUserFansGiftPriResponse, error)
	ReportLiveShowScore(ctx context.Context, in *channel_live_logic.ReportLiveShowScoreRequest, opts ...grpc.CallOption) (*channel_live_logic.ReportLiveShowScoreResponse, error)
	// 该接口用于获取直播节目入口信息
	GetLiveShowEntryInfo(ctx context.Context, in *channel_live_logic.GetLiveShowEntryInfoRequest, opts ...grpc.CallOption) (*channel_live_logic.GetLiveShowEntryInfoResponse, error)
	// 获取直播间随机标题
	GetChannelLiveRandomTitle(ctx context.Context, in *channel_live_logic.GetChannelLiveRandomTitleRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveRandomTitleResponse, error)
}

type channelLiveLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelLiveLogicClient(cc *grpc.ClientConn) ChannelLiveLogicClient {
	return &channelLiveLogicClient{cc}
}

func (c *channelLiveLogicClient) GetLiveChannelInfo(ctx context.Context, in *channel_live_logic.GetLiveChannelInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetLiveChannelInfoResp, error) {
	out := new(channel_live_logic.GetLiveChannelInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetLiveChannelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) ChannelLiveHeartbeat(ctx context.Context, in *channel_live_logic.ChannelLiveHeartbeatReq, opts ...grpc.CallOption) (*channel_live_logic.ChannelLiveHeartbeatResp, error) {
	out := new(channel_live_logic.ChannelLiveHeartbeatResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/ChannelLiveHeartbeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) SetChannelLiveStatus(ctx context.Context, in *channel_live_logic.SetChannelLiveStatusReq, opts ...grpc.CallOption) (*channel_live_logic.SetChannelLiveStatusResp, error) {
	out := new(channel_live_logic.SetChannelLiveStatusResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/SetChannelLiveStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveStatus(ctx context.Context, in *channel_live_logic.GetChannelLiveStatusReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveStatusResp, error) {
	out := new(channel_live_logic.GetChannelLiveStatusResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) ApplyPk(ctx context.Context, in *channel_live_logic.ApplyPkReq, opts ...grpc.CallOption) (*channel_live_logic.ApplyPkResp, error) {
	out := new(channel_live_logic.ApplyPkResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/ApplyPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) HandlerApply(ctx context.Context, in *channel_live_logic.HandlerApplyReq, opts ...grpc.CallOption) (*channel_live_logic.HandlerApplyResp, error) {
	out := new(channel_live_logic.HandlerApplyResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/HandlerApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) BatchGetChannelLiveStatusByAccount(ctx context.Context, in *channel_live_logic.BatchGetChannelLiveStatusByAccountReq, opts ...grpc.CallOption) (*channel_live_logic.BatchGetChannelLiveStatusByAccountResp, error) {
	out := new(channel_live_logic.BatchGetChannelLiveStatusByAccountResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/BatchGetChannelLiveStatusByAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) SetPkStatus(ctx context.Context, in *channel_live_logic.SetPkStatusReq, opts ...grpc.CallOption) (*channel_live_logic.SetPkStatusResp, error) {
	out := new(channel_live_logic.SetPkStatusResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/SetPkStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) CancelPKApply(ctx context.Context, in *channel_live_logic.CancelPKApplyReq, opts ...grpc.CallOption) (*channel_live_logic.CancelPKApplyResp, error) {
	out := new(channel_live_logic.CancelPKApplyResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/CancelPKApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLivePKRecord(ctx context.Context, in *channel_live_logic.GetChannelLivePKRecordReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLivePKRecordResp, error) {
	out := new(channel_live_logic.GetChannelLivePKRecordResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetChannelLivePKRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLivePkRankUser(ctx context.Context, in *channel_live_logic.GetChannelLivePkRankUserReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLivePkRankUserResp, error) {
	out := new(channel_live_logic.GetChannelLivePkRankUserResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetChannelLivePkRankUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveRankUser(ctx context.Context, in *channel_live_logic.GetChannelLiveRankUserReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveRankUserResp, error) {
	out := new(channel_live_logic.GetChannelLiveRankUserResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveRankUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveWatchTimeRankUser(ctx context.Context, in *channel_live_logic.GetChannelLiveWatchTimeRankUserReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveWatchTimeRankUserResp, error) {
	out := new(channel_live_logic.GetChannelLiveWatchTimeRankUserResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveWatchTimeRankUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveData(ctx context.Context, in *channel_live_logic.GetChannelLiveDataReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveDataResp, error) {
	out := new(channel_live_logic.GetChannelLiveDataResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetFansRankList(ctx context.Context, in *channel_live_logic.GetFansRankListReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansRankListResp, error) {
	out := new(channel_live_logic.GetFansRankListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetFansRankList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetFansInfo(ctx context.Context, in *channel_live_logic.GetFansInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansInfoResp, error) {
	out := new(channel_live_logic.GetFansInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetFansInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetAnchorFansInfo(ctx context.Context, in *channel_live_logic.GetAnchorFansInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetAnchorFansInfoResp, error) {
	out := new(channel_live_logic.GetAnchorFansInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetAnchorFansInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) SearchAnchor(ctx context.Context, in *channel_live_logic.SearchAnchorReq, opts ...grpc.CallOption) (*channel_live_logic.SearchAnchorResp, error) {
	out := new(channel_live_logic.SearchAnchorResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/SearchAnchor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) ReportClientIDChange(ctx context.Context, in *channel_live_logic.ReportClientIDChangeReq, opts ...grpc.CallOption) (*channel_live_logic.ReportClientIDChangeResp, error) {
	out := new(channel_live_logic.ReportClientIDChangeResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/ReportClientIDChange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetApplyList(ctx context.Context, in *channel_live_logic.GetApplyListReq, opts ...grpc.CallOption) (*channel_live_logic.GetApplyListResp, error) {
	out := new(channel_live_logic.GetApplyListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetApplyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetPkInfo(ctx context.Context, in *channel_live_logic.GetPkInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetPkInfoResp, error) {
	out := new(channel_live_logic.GetPkInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetPkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetMyToolList(ctx context.Context, in *channel_live_logic.GetMyToolListReq, opts ...grpc.CallOption) (*channel_live_logic.GetMyToolListResp, error) {
	out := new(channel_live_logic.GetMyToolListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetMyToolList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetItemConfig(ctx context.Context, in *channel_live_logic.GetItemConfigReq, opts ...grpc.CallOption) (*channel_live_logic.GetItemConfigResp, error) {
	out := new(channel_live_logic.GetItemConfigResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetItemConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) SetChannelLiveOpponentMicFlag(ctx context.Context, in *channel_live_logic.SetChannelLiveOpponentMicFlagReq, opts ...grpc.CallOption) (*channel_live_logic.SetChannelLiveOpponentMicFlagResp, error) {
	out := new(channel_live_logic.SetChannelLiveOpponentMicFlagResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/SetChannelLiveOpponentMicFlag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) StartPkMatch(ctx context.Context, in *channel_live_logic.StartPkMatchReq, opts ...grpc.CallOption) (*channel_live_logic.StartPkMatchResp, error) {
	out := new(channel_live_logic.StartPkMatchResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/StartPkMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) CancelPkMatch(ctx context.Context, in *channel_live_logic.CancelPkMatchReq, opts ...grpc.CallOption) (*channel_live_logic.CancelPkMatchResp, error) {
	out := new(channel_live_logic.CancelPkMatchResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/CancelPkMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetPKMatchInfo(ctx context.Context, in *channel_live_logic.GetPKMatchInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetPKMatchInfoResp, error) {
	out := new(channel_live_logic.GetPKMatchInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetPKMatchInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetUserMissionList(ctx context.Context, in *channel_live_logic.GetUserMissionListReq, opts ...grpc.CallOption) (*channel_live_logic.GetUserMissionListResp, error) {
	out := new(channel_live_logic.GetUserMissionListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetUserMissionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetFansMissionList(ctx context.Context, in *channel_live_logic.GetFansMissionListReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansMissionListResp, error) {
	out := new(channel_live_logic.GetFansMissionListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetFansMissionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetActorMissionList(ctx context.Context, in *channel_live_logic.GetActorMissionListReq, opts ...grpc.CallOption) (*channel_live_logic.GetActorMissionListResp, error) {
	out := new(channel_live_logic.GetActorMissionListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetActorMissionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) HandleUserMissionAtInterval(ctx context.Context, in *channel_live_logic.HandleUserMissionAtIntervalReq, opts ...grpc.CallOption) (*channel_live_logic.HandleUserMissionAtIntervalResp, error) {
	out := new(channel_live_logic.HandleUserMissionAtIntervalResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/HandleUserMissionAtInterval", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) HandleShareLiveChannelMission(ctx context.Context, in *channel_live_logic.HandleShareLiveChannelMissionReq, opts ...grpc.CallOption) (*channel_live_logic.HandleShareLiveChannelMissionResp, error) {
	out := new(channel_live_logic.HandleShareLiveChannelMissionResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/HandleShareLiveChannelMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetProcessActorMissionDesc(ctx context.Context, in *channel_live_logic.GetProcessActorMissionDescReq, opts ...grpc.CallOption) (*channel_live_logic.GetProcessActorMissionDescResp, error) {
	out := new(channel_live_logic.GetProcessActorMissionDescResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetProcessActorMissionDesc", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) HandleFansMissionAtInterval(ctx context.Context, in *channel_live_logic.HandleFansMissionAtIntervalReq, opts ...grpc.CallOption) (*channel_live_logic.HandleFansMissionAtIntervalResp, error) {
	out := new(channel_live_logic.HandleFansMissionAtIntervalResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/HandleFansMissionAtInterval", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetAnchorHonorNameplate(ctx context.Context, in *channel_live_logic.GetAnchorHonorNameplateReq, opts ...grpc.CallOption) (*channel_live_logic.GetAnchorHonorNameplateResp, error) {
	out := new(channel_live_logic.GetAnchorHonorNameplateResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetAnchorHonorNameplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetRankingList(ctx context.Context, in *channel_live_logic.GetRankingListReq, opts ...grpc.CallOption) (*channel_live_logic.GetRankingListResp, error) {
	out := new(channel_live_logic.GetRankingListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetRankingList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetFansAddedGroupList(ctx context.Context, in *channel_live_logic.GetFansAddedGroupListReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansAddedGroupListResp, error) {
	out := new(channel_live_logic.GetFansAddedGroupListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetFansAddedGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) SetFansGroupName(ctx context.Context, in *channel_live_logic.SetFansGroupNameReq, opts ...grpc.CallOption) (*channel_live_logic.SetFansGroupNameResp, error) {
	out := new(channel_live_logic.SetFansGroupNameResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/SetFansGroupName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) CheckSetGroupNamePermit(ctx context.Context, in *channel_live_logic.CheckSetGroupNamePermitReq, opts ...grpc.CallOption) (*channel_live_logic.CheckSetGroupNamePermitResp, error) {
	out := new(channel_live_logic.CheckSetGroupNamePermitResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/CheckSetGroupNamePermit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) CheckUserIsFans(ctx context.Context, in *channel_live_logic.CheckUserIsFansReq, opts ...grpc.CallOption) (*channel_live_logic.CheckUserIsFansResp, error) {
	out := new(channel_live_logic.CheckUserIsFansResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/CheckUserIsFans", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) ChannelLiveReport(ctx context.Context, in *channel_live_logic.ChannelLiveReportReq, opts ...grpc.CallOption) (*channel_live_logic.ChannelLiveReportResp, error) {
	out := new(channel_live_logic.ChannelLiveReportResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/ChannelLiveReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) AcceptAppointPk(ctx context.Context, in *channel_live_logic.AcceptAppointPkReq, opts ...grpc.CallOption) (*channel_live_logic.AcceptAppointPkResp, error) {
	out := new(channel_live_logic.AcceptAppointPkResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/AcceptAppointPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) ConfirmAppointPkPush(ctx context.Context, in *channel_live_logic.ConfirmAppointPkPushReq, opts ...grpc.CallOption) (*channel_live_logic.ConfirmAppointPkPushResp, error) {
	out := new(channel_live_logic.ConfirmAppointPkPushResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/ConfirmAppointPkPush", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetAppointPkInfo(ctx context.Context, in *channel_live_logic.GetAppointPkInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetAppointPkInfoResp, error) {
	out := new(channel_live_logic.GetAppointPkInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetAppointPkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetAnchorValidPlateList(ctx context.Context, in *channel_live_logic.GetAnchorValidPlateListReq, opts ...grpc.CallOption) (*channel_live_logic.GetAnchorValidPlateListResp, error) {
	out := new(channel_live_logic.GetAnchorValidPlateListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetAnchorValidPlateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) WearAnchorPlate(ctx context.Context, in *channel_live_logic.WearAnchorPlateReq, opts ...grpc.CallOption) (*channel_live_logic.WearAnchorPlateResp, error) {
	out := new(channel_live_logic.WearAnchorPlateResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/WearAnchorPlate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveMultiPkPermission(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkPermissionRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkPermissionResponse, error) {
	out := new(channel_live_logic.GetChannelLiveMultiPkPermissionResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveMultiPkPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) SerarchPkAnchor(ctx context.Context, in *channel_live_logic.SerarchMultiPkAnchorRequest, opts ...grpc.CallOption) (*channel_live_logic.SerarchMultiPkAnchorResponse, error) {
	out := new(channel_live_logic.SerarchMultiPkAnchorResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/SerarchPkAnchor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) ApplyChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.ApplyChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.ApplyChannelLiveMultiPkResponse, error) {
	out := new(channel_live_logic.ApplyChannelLiveMultiPkResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/ApplyChannelLiveMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) MatchMultiPk(ctx context.Context, in *channel_live_logic.MatchMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.MatchMultiPkResponse, error) {
	out := new(channel_live_logic.MatchMultiPkResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/MatchMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) CancelMatchMultiPk(ctx context.Context, in *channel_live_logic.CancelMatchMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.CancelMatchMultiPkResponse, error) {
	out := new(channel_live_logic.CancelMatchMultiPkResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/CancelMatchMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) AcceptChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.AcceptChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.AcceptChannelLiveMultiPkResponse, error) {
	out := new(channel_live_logic.AcceptChannelLiveMultiPkResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/AcceptChannelLiveMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) StartChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.StartChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.StartChannelLiveMultiPkResponse, error) {
	out := new(channel_live_logic.StartChannelLiveMultiPkResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/StartChannelLiveMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveMultiPkRank(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkRankRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkRankResponse, error) {
	out := new(channel_live_logic.GetChannelLiveMultiPkRankResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveMultiPkRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveMultiPkKnightList(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkKnightListRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkKnightListResponse, error) {
	out := new(channel_live_logic.GetChannelLiveMultiPkKnightListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveMultiPkKnightList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveMultiPkRecordList(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkRecordListRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkRecordListResponse, error) {
	out := new(channel_live_logic.GetChannelLiveMultiPkRecordListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveMultiPkRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) CancelChannelLiveMultiPkTeam(ctx context.Context, in *channel_live_logic.CancelChannelLiveMultiPkTeamRequest, opts ...grpc.CallOption) (*channel_live_logic.CancelChannelLiveMultiPkTeamResponse, error) {
	out := new(channel_live_logic.CancelChannelLiveMultiPkTeamResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/CancelChannelLiveMultiPkTeam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) StopChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.StopChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.StopChannelLiveMultiPkResponse, error) {
	out := new(channel_live_logic.StopChannelLiveMultiPkResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/StopChannelLiveMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) DisinviteChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.DisinviteChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.DisinviteChannelLiveMultiPkResponse, error) {
	out := new(channel_live_logic.DisinviteChannelLiveMultiPkResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/DisinviteChannelLiveMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveMultiPkTeamInfo(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkTeamInfoRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkTeamInfoResponse, error) {
	out := new(channel_live_logic.GetChannelLiveMultiPkTeamInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveMultiPkTeamInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) InitChannelLiveMultiPkTeam(ctx context.Context, in *channel_live_logic.InitChannelLiveMultiPkTeamRequest, opts ...grpc.CallOption) (*channel_live_logic.InitChannelLiveMultiPkTeamResponse, error) {
	out := new(channel_live_logic.InitChannelLiveMultiPkTeamResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/InitChannelLiveMultiPkTeam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) LeaveFansGroup(ctx context.Context, in *channel_live_logic.LeaveFansGroupRequest, opts ...grpc.CallOption) (*channel_live_logic.LeaveFansGroupResponse, error) {
	out := new(channel_live_logic.LeaveFansGroupResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/LeaveFansGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetVirtualLiveChannelSecret(ctx context.Context, in *channel_live_logic.GetVirtualLiveChannelSecretRequest, opts ...grpc.CallOption) (*channel_live_logic.GetVirtualLiveChannelSecretResponse, error) {
	out := new(channel_live_logic.GetVirtualLiveChannelSecretResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetVirtualLiveChannelSecret", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetUserFansGiftPri(ctx context.Context, in *channel_live_logic.GetUserFansGiftPriRequest, opts ...grpc.CallOption) (*channel_live_logic.GetUserFansGiftPriResponse, error) {
	out := new(channel_live_logic.GetUserFansGiftPriResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetUserFansGiftPri", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) ReportLiveShowScore(ctx context.Context, in *channel_live_logic.ReportLiveShowScoreRequest, opts ...grpc.CallOption) (*channel_live_logic.ReportLiveShowScoreResponse, error) {
	out := new(channel_live_logic.ReportLiveShowScoreResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/ReportLiveShowScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetLiveShowEntryInfo(ctx context.Context, in *channel_live_logic.GetLiveShowEntryInfoRequest, opts ...grpc.CallOption) (*channel_live_logic.GetLiveShowEntryInfoResponse, error) {
	out := new(channel_live_logic.GetLiveShowEntryInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetLiveShowEntryInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveRandomTitle(ctx context.Context, in *channel_live_logic.GetChannelLiveRandomTitleRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveRandomTitleResponse, error) {
	out := new(channel_live_logic.GetChannelLiveRandomTitleResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveRandomTitle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelLiveLogicServer is the server API for ChannelLiveLogic service.
type ChannelLiveLogicServer interface {
	GetLiveChannelInfo(context.Context, *channel_live_logic.GetLiveChannelInfoReq) (*channel_live_logic.GetLiveChannelInfoResp, error)
	ChannelLiveHeartbeat(context.Context, *channel_live_logic.ChannelLiveHeartbeatReq) (*channel_live_logic.ChannelLiveHeartbeatResp, error)
	SetChannelLiveStatus(context.Context, *channel_live_logic.SetChannelLiveStatusReq) (*channel_live_logic.SetChannelLiveStatusResp, error)
	GetChannelLiveStatus(context.Context, *channel_live_logic.GetChannelLiveStatusReq) (*channel_live_logic.GetChannelLiveStatusResp, error)
	ApplyPk(context.Context, *channel_live_logic.ApplyPkReq) (*channel_live_logic.ApplyPkResp, error)
	HandlerApply(context.Context, *channel_live_logic.HandlerApplyReq) (*channel_live_logic.HandlerApplyResp, error)
	BatchGetChannelLiveStatusByAccount(context.Context, *channel_live_logic.BatchGetChannelLiveStatusByAccountReq) (*channel_live_logic.BatchGetChannelLiveStatusByAccountResp, error)
	SetPkStatus(context.Context, *channel_live_logic.SetPkStatusReq) (*channel_live_logic.SetPkStatusResp, error)
	CancelPKApply(context.Context, *channel_live_logic.CancelPKApplyReq) (*channel_live_logic.CancelPKApplyResp, error)
	GetChannelLivePKRecord(context.Context, *channel_live_logic.GetChannelLivePKRecordReq) (*channel_live_logic.GetChannelLivePKRecordResp, error)
	GetChannelLivePkRankUser(context.Context, *channel_live_logic.GetChannelLivePkRankUserReq) (*channel_live_logic.GetChannelLivePkRankUserResp, error)
	GetChannelLiveRankUser(context.Context, *channel_live_logic.GetChannelLiveRankUserReq) (*channel_live_logic.GetChannelLiveRankUserResp, error)
	GetChannelLiveWatchTimeRankUser(context.Context, *channel_live_logic.GetChannelLiveWatchTimeRankUserReq) (*channel_live_logic.GetChannelLiveWatchTimeRankUserResp, error)
	GetChannelLiveData(context.Context, *channel_live_logic.GetChannelLiveDataReq) (*channel_live_logic.GetChannelLiveDataResp, error)
	GetFansRankList(context.Context, *channel_live_logic.GetFansRankListReq) (*channel_live_logic.GetFansRankListResp, error)
	GetFansInfo(context.Context, *channel_live_logic.GetFansInfoReq) (*channel_live_logic.GetFansInfoResp, error)
	GetAnchorFansInfo(context.Context, *channel_live_logic.GetAnchorFansInfoReq) (*channel_live_logic.GetAnchorFansInfoResp, error)
	SearchAnchor(context.Context, *channel_live_logic.SearchAnchorReq) (*channel_live_logic.SearchAnchorResp, error)
	ReportClientIDChange(context.Context, *channel_live_logic.ReportClientIDChangeReq) (*channel_live_logic.ReportClientIDChangeResp, error)
	GetApplyList(context.Context, *channel_live_logic.GetApplyListReq) (*channel_live_logic.GetApplyListResp, error)
	GetPkInfo(context.Context, *channel_live_logic.GetPkInfoReq) (*channel_live_logic.GetPkInfoResp, error)
	GetMyToolList(context.Context, *channel_live_logic.GetMyToolListReq) (*channel_live_logic.GetMyToolListResp, error)
	GetItemConfig(context.Context, *channel_live_logic.GetItemConfigReq) (*channel_live_logic.GetItemConfigResp, error)
	SetChannelLiveOpponentMicFlag(context.Context, *channel_live_logic.SetChannelLiveOpponentMicFlagReq) (*channel_live_logic.SetChannelLiveOpponentMicFlagResp, error)
	StartPkMatch(context.Context, *channel_live_logic.StartPkMatchReq) (*channel_live_logic.StartPkMatchResp, error)
	CancelPkMatch(context.Context, *channel_live_logic.CancelPkMatchReq) (*channel_live_logic.CancelPkMatchResp, error)
	GetPKMatchInfo(context.Context, *channel_live_logic.GetPKMatchInfoReq) (*channel_live_logic.GetPKMatchInfoResp, error)
	GetUserMissionList(context.Context, *channel_live_logic.GetUserMissionListReq) (*channel_live_logic.GetUserMissionListResp, error)
	GetFansMissionList(context.Context, *channel_live_logic.GetFansMissionListReq) (*channel_live_logic.GetFansMissionListResp, error)
	GetActorMissionList(context.Context, *channel_live_logic.GetActorMissionListReq) (*channel_live_logic.GetActorMissionListResp, error)
	HandleUserMissionAtInterval(context.Context, *channel_live_logic.HandleUserMissionAtIntervalReq) (*channel_live_logic.HandleUserMissionAtIntervalResp, error)
	HandleShareLiveChannelMission(context.Context, *channel_live_logic.HandleShareLiveChannelMissionReq) (*channel_live_logic.HandleShareLiveChannelMissionResp, error)
	GetProcessActorMissionDesc(context.Context, *channel_live_logic.GetProcessActorMissionDescReq) (*channel_live_logic.GetProcessActorMissionDescResp, error)
	HandleFansMissionAtInterval(context.Context, *channel_live_logic.HandleFansMissionAtIntervalReq) (*channel_live_logic.HandleFansMissionAtIntervalResp, error)
	GetAnchorHonorNameplate(context.Context, *channel_live_logic.GetAnchorHonorNameplateReq) (*channel_live_logic.GetAnchorHonorNameplateResp, error)
	GetRankingList(context.Context, *channel_live_logic.GetRankingListReq) (*channel_live_logic.GetRankingListResp, error)
	GetFansAddedGroupList(context.Context, *channel_live_logic.GetFansAddedGroupListReq) (*channel_live_logic.GetFansAddedGroupListResp, error)
	SetFansGroupName(context.Context, *channel_live_logic.SetFansGroupNameReq) (*channel_live_logic.SetFansGroupNameResp, error)
	CheckSetGroupNamePermit(context.Context, *channel_live_logic.CheckSetGroupNamePermitReq) (*channel_live_logic.CheckSetGroupNamePermitResp, error)
	CheckUserIsFans(context.Context, *channel_live_logic.CheckUserIsFansReq) (*channel_live_logic.CheckUserIsFansResp, error)
	ChannelLiveReport(context.Context, *channel_live_logic.ChannelLiveReportReq) (*channel_live_logic.ChannelLiveReportResp, error)
	AcceptAppointPk(context.Context, *channel_live_logic.AcceptAppointPkReq) (*channel_live_logic.AcceptAppointPkResp, error)
	ConfirmAppointPkPush(context.Context, *channel_live_logic.ConfirmAppointPkPushReq) (*channel_live_logic.ConfirmAppointPkPushResp, error)
	GetAppointPkInfo(context.Context, *channel_live_logic.GetAppointPkInfoReq) (*channel_live_logic.GetAppointPkInfoResp, error)
	GetAnchorValidPlateList(context.Context, *channel_live_logic.GetAnchorValidPlateListReq) (*channel_live_logic.GetAnchorValidPlateListResp, error)
	WearAnchorPlate(context.Context, *channel_live_logic.WearAnchorPlateReq) (*channel_live_logic.WearAnchorPlateResp, error)
	GetChannelLiveMultiPkPermission(context.Context, *channel_live_logic.GetChannelLiveMultiPkPermissionRequest) (*channel_live_logic.GetChannelLiveMultiPkPermissionResponse, error)
	SerarchPkAnchor(context.Context, *channel_live_logic.SerarchMultiPkAnchorRequest) (*channel_live_logic.SerarchMultiPkAnchorResponse, error)
	ApplyChannelLiveMultiPk(context.Context, *channel_live_logic.ApplyChannelLiveMultiPkRequest) (*channel_live_logic.ApplyChannelLiveMultiPkResponse, error)
	MatchMultiPk(context.Context, *channel_live_logic.MatchMultiPkRequest) (*channel_live_logic.MatchMultiPkResponse, error)
	CancelMatchMultiPk(context.Context, *channel_live_logic.CancelMatchMultiPkRequest) (*channel_live_logic.CancelMatchMultiPkResponse, error)
	AcceptChannelLiveMultiPk(context.Context, *channel_live_logic.AcceptChannelLiveMultiPkRequest) (*channel_live_logic.AcceptChannelLiveMultiPkResponse, error)
	StartChannelLiveMultiPk(context.Context, *channel_live_logic.StartChannelLiveMultiPkRequest) (*channel_live_logic.StartChannelLiveMultiPkResponse, error)
	GetChannelLiveMultiPkRank(context.Context, *channel_live_logic.GetChannelLiveMultiPkRankRequest) (*channel_live_logic.GetChannelLiveMultiPkRankResponse, error)
	GetChannelLiveMultiPkKnightList(context.Context, *channel_live_logic.GetChannelLiveMultiPkKnightListRequest) (*channel_live_logic.GetChannelLiveMultiPkKnightListResponse, error)
	GetChannelLiveMultiPkRecordList(context.Context, *channel_live_logic.GetChannelLiveMultiPkRecordListRequest) (*channel_live_logic.GetChannelLiveMultiPkRecordListResponse, error)
	CancelChannelLiveMultiPkTeam(context.Context, *channel_live_logic.CancelChannelLiveMultiPkTeamRequest) (*channel_live_logic.CancelChannelLiveMultiPkTeamResponse, error)
	StopChannelLiveMultiPk(context.Context, *channel_live_logic.StopChannelLiveMultiPkRequest) (*channel_live_logic.StopChannelLiveMultiPkResponse, error)
	DisinviteChannelLiveMultiPk(context.Context, *channel_live_logic.DisinviteChannelLiveMultiPkRequest) (*channel_live_logic.DisinviteChannelLiveMultiPkResponse, error)
	GetChannelLiveMultiPkTeamInfo(context.Context, *channel_live_logic.GetChannelLiveMultiPkTeamInfoRequest) (*channel_live_logic.GetChannelLiveMultiPkTeamInfoResponse, error)
	InitChannelLiveMultiPkTeam(context.Context, *channel_live_logic.InitChannelLiveMultiPkTeamRequest) (*channel_live_logic.InitChannelLiveMultiPkTeamResponse, error)
	LeaveFansGroup(context.Context, *channel_live_logic.LeaveFansGroupRequest) (*channel_live_logic.LeaveFansGroupResponse, error)
	GetVirtualLiveChannelSecret(context.Context, *channel_live_logic.GetVirtualLiveChannelSecretRequest) (*channel_live_logic.GetVirtualLiveChannelSecretResponse, error)
	GetUserFansGiftPri(context.Context, *channel_live_logic.GetUserFansGiftPriRequest) (*channel_live_logic.GetUserFansGiftPriResponse, error)
	ReportLiveShowScore(context.Context, *channel_live_logic.ReportLiveShowScoreRequest) (*channel_live_logic.ReportLiveShowScoreResponse, error)
	// 该接口用于获取直播节目入口信息
	GetLiveShowEntryInfo(context.Context, *channel_live_logic.GetLiveShowEntryInfoRequest) (*channel_live_logic.GetLiveShowEntryInfoResponse, error)
	// 获取直播间随机标题
	GetChannelLiveRandomTitle(context.Context, *channel_live_logic.GetChannelLiveRandomTitleRequest) (*channel_live_logic.GetChannelLiveRandomTitleResponse, error)
}

func RegisterChannelLiveLogicServer(s *grpc.Server, srv ChannelLiveLogicServer) {
	s.RegisterService(&_ChannelLiveLogic_serviceDesc, srv)
}

func _ChannelLiveLogic_GetLiveChannelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetLiveChannelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetLiveChannelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetLiveChannelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetLiveChannelInfo(ctx, req.(*channel_live_logic.GetLiveChannelInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_ChannelLiveHeartbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.ChannelLiveHeartbeatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).ChannelLiveHeartbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/ChannelLiveHeartbeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).ChannelLiveHeartbeat(ctx, req.(*channel_live_logic.ChannelLiveHeartbeatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_SetChannelLiveStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.SetChannelLiveStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).SetChannelLiveStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/SetChannelLiveStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).SetChannelLiveStatus(ctx, req.(*channel_live_logic.SetChannelLiveStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveStatus(ctx, req.(*channel_live_logic.GetChannelLiveStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_ApplyPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.ApplyPkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).ApplyPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/ApplyPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).ApplyPk(ctx, req.(*channel_live_logic.ApplyPkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_HandlerApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.HandlerApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).HandlerApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/HandlerApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).HandlerApply(ctx, req.(*channel_live_logic.HandlerApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_BatchGetChannelLiveStatusByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.BatchGetChannelLiveStatusByAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).BatchGetChannelLiveStatusByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/BatchGetChannelLiveStatusByAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).BatchGetChannelLiveStatusByAccount(ctx, req.(*channel_live_logic.BatchGetChannelLiveStatusByAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_SetPkStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.SetPkStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).SetPkStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/SetPkStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).SetPkStatus(ctx, req.(*channel_live_logic.SetPkStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_CancelPKApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.CancelPKApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).CancelPKApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/CancelPKApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).CancelPKApply(ctx, req.(*channel_live_logic.CancelPKApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLivePKRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLivePKRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLivePKRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetChannelLivePKRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLivePKRecord(ctx, req.(*channel_live_logic.GetChannelLivePKRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLivePkRankUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLivePkRankUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLivePkRankUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetChannelLivePkRankUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLivePkRankUser(ctx, req.(*channel_live_logic.GetChannelLivePkRankUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveRankUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveRankUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveRankUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveRankUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveRankUser(ctx, req.(*channel_live_logic.GetChannelLiveRankUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveWatchTimeRankUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveWatchTimeRankUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveWatchTimeRankUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveWatchTimeRankUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveWatchTimeRankUser(ctx, req.(*channel_live_logic.GetChannelLiveWatchTimeRankUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveData(ctx, req.(*channel_live_logic.GetChannelLiveDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetFansRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetFansRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetFansRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetFansRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetFansRankList(ctx, req.(*channel_live_logic.GetFansRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetFansInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetFansInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetFansInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetFansInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetFansInfo(ctx, req.(*channel_live_logic.GetFansInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetAnchorFansInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetAnchorFansInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetAnchorFansInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetAnchorFansInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetAnchorFansInfo(ctx, req.(*channel_live_logic.GetAnchorFansInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_SearchAnchor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.SearchAnchorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).SearchAnchor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/SearchAnchor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).SearchAnchor(ctx, req.(*channel_live_logic.SearchAnchorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_ReportClientIDChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.ReportClientIDChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).ReportClientIDChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/ReportClientIDChange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).ReportClientIDChange(ctx, req.(*channel_live_logic.ReportClientIDChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetApplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetApplyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetApplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetApplyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetApplyList(ctx, req.(*channel_live_logic.GetApplyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetPkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetPkInfo(ctx, req.(*channel_live_logic.GetPkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetMyToolList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetMyToolListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetMyToolList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetMyToolList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetMyToolList(ctx, req.(*channel_live_logic.GetMyToolListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetItemConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetItemConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetItemConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetItemConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetItemConfig(ctx, req.(*channel_live_logic.GetItemConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_SetChannelLiveOpponentMicFlag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.SetChannelLiveOpponentMicFlagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).SetChannelLiveOpponentMicFlag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/SetChannelLiveOpponentMicFlag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).SetChannelLiveOpponentMicFlag(ctx, req.(*channel_live_logic.SetChannelLiveOpponentMicFlagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_StartPkMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.StartPkMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).StartPkMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/StartPkMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).StartPkMatch(ctx, req.(*channel_live_logic.StartPkMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_CancelPkMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.CancelPkMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).CancelPkMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/CancelPkMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).CancelPkMatch(ctx, req.(*channel_live_logic.CancelPkMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetPKMatchInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetPKMatchInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetPKMatchInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetPKMatchInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetPKMatchInfo(ctx, req.(*channel_live_logic.GetPKMatchInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetUserMissionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetUserMissionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetUserMissionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetUserMissionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetUserMissionList(ctx, req.(*channel_live_logic.GetUserMissionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetFansMissionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetFansMissionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetFansMissionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetFansMissionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetFansMissionList(ctx, req.(*channel_live_logic.GetFansMissionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetActorMissionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetActorMissionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetActorMissionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetActorMissionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetActorMissionList(ctx, req.(*channel_live_logic.GetActorMissionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_HandleUserMissionAtInterval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.HandleUserMissionAtIntervalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).HandleUserMissionAtInterval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/HandleUserMissionAtInterval",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).HandleUserMissionAtInterval(ctx, req.(*channel_live_logic.HandleUserMissionAtIntervalReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_HandleShareLiveChannelMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.HandleShareLiveChannelMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).HandleShareLiveChannelMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/HandleShareLiveChannelMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).HandleShareLiveChannelMission(ctx, req.(*channel_live_logic.HandleShareLiveChannelMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetProcessActorMissionDesc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetProcessActorMissionDescReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetProcessActorMissionDesc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetProcessActorMissionDesc",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetProcessActorMissionDesc(ctx, req.(*channel_live_logic.GetProcessActorMissionDescReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_HandleFansMissionAtInterval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.HandleFansMissionAtIntervalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).HandleFansMissionAtInterval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/HandleFansMissionAtInterval",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).HandleFansMissionAtInterval(ctx, req.(*channel_live_logic.HandleFansMissionAtIntervalReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetAnchorHonorNameplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetAnchorHonorNameplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetAnchorHonorNameplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetAnchorHonorNameplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetAnchorHonorNameplate(ctx, req.(*channel_live_logic.GetAnchorHonorNameplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetRankingList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetRankingListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetRankingList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetRankingList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetRankingList(ctx, req.(*channel_live_logic.GetRankingListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetFansAddedGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetFansAddedGroupListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetFansAddedGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetFansAddedGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetFansAddedGroupList(ctx, req.(*channel_live_logic.GetFansAddedGroupListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_SetFansGroupName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.SetFansGroupNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).SetFansGroupName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/SetFansGroupName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).SetFansGroupName(ctx, req.(*channel_live_logic.SetFansGroupNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_CheckSetGroupNamePermit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.CheckSetGroupNamePermitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).CheckSetGroupNamePermit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/CheckSetGroupNamePermit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).CheckSetGroupNamePermit(ctx, req.(*channel_live_logic.CheckSetGroupNamePermitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_CheckUserIsFans_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.CheckUserIsFansReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).CheckUserIsFans(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/CheckUserIsFans",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).CheckUserIsFans(ctx, req.(*channel_live_logic.CheckUserIsFansReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_ChannelLiveReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.ChannelLiveReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).ChannelLiveReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/ChannelLiveReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).ChannelLiveReport(ctx, req.(*channel_live_logic.ChannelLiveReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_AcceptAppointPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.AcceptAppointPkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).AcceptAppointPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/AcceptAppointPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).AcceptAppointPk(ctx, req.(*channel_live_logic.AcceptAppointPkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_ConfirmAppointPkPush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.ConfirmAppointPkPushReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).ConfirmAppointPkPush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/ConfirmAppointPkPush",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).ConfirmAppointPkPush(ctx, req.(*channel_live_logic.ConfirmAppointPkPushReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetAppointPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetAppointPkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetAppointPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetAppointPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetAppointPkInfo(ctx, req.(*channel_live_logic.GetAppointPkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetAnchorValidPlateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetAnchorValidPlateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetAnchorValidPlateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetAnchorValidPlateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetAnchorValidPlateList(ctx, req.(*channel_live_logic.GetAnchorValidPlateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_WearAnchorPlate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.WearAnchorPlateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).WearAnchorPlate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/WearAnchorPlate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).WearAnchorPlate(ctx, req.(*channel_live_logic.WearAnchorPlateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveMultiPkPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveMultiPkPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveMultiPkPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkPermission(ctx, req.(*channel_live_logic.GetChannelLiveMultiPkPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_SerarchPkAnchor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.SerarchMultiPkAnchorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).SerarchPkAnchor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/SerarchPkAnchor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).SerarchPkAnchor(ctx, req.(*channel_live_logic.SerarchMultiPkAnchorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_ApplyChannelLiveMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.ApplyChannelLiveMultiPkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).ApplyChannelLiveMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/ApplyChannelLiveMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).ApplyChannelLiveMultiPk(ctx, req.(*channel_live_logic.ApplyChannelLiveMultiPkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_MatchMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.MatchMultiPkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).MatchMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/MatchMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).MatchMultiPk(ctx, req.(*channel_live_logic.MatchMultiPkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_CancelMatchMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.CancelMatchMultiPkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).CancelMatchMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/CancelMatchMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).CancelMatchMultiPk(ctx, req.(*channel_live_logic.CancelMatchMultiPkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_AcceptChannelLiveMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.AcceptChannelLiveMultiPkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).AcceptChannelLiveMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/AcceptChannelLiveMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).AcceptChannelLiveMultiPk(ctx, req.(*channel_live_logic.AcceptChannelLiveMultiPkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_StartChannelLiveMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.StartChannelLiveMultiPkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).StartChannelLiveMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/StartChannelLiveMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).StartChannelLiveMultiPk(ctx, req.(*channel_live_logic.StartChannelLiveMultiPkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveMultiPkRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveMultiPkRankRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveMultiPkRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkRank(ctx, req.(*channel_live_logic.GetChannelLiveMultiPkRankRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveMultiPkKnightList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveMultiPkKnightListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkKnightList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveMultiPkKnightList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkKnightList(ctx, req.(*channel_live_logic.GetChannelLiveMultiPkKnightListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveMultiPkRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveMultiPkRecordListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveMultiPkRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkRecordList(ctx, req.(*channel_live_logic.GetChannelLiveMultiPkRecordListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_CancelChannelLiveMultiPkTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.CancelChannelLiveMultiPkTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).CancelChannelLiveMultiPkTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/CancelChannelLiveMultiPkTeam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).CancelChannelLiveMultiPkTeam(ctx, req.(*channel_live_logic.CancelChannelLiveMultiPkTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_StopChannelLiveMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.StopChannelLiveMultiPkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).StopChannelLiveMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/StopChannelLiveMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).StopChannelLiveMultiPk(ctx, req.(*channel_live_logic.StopChannelLiveMultiPkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_DisinviteChannelLiveMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.DisinviteChannelLiveMultiPkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).DisinviteChannelLiveMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/DisinviteChannelLiveMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).DisinviteChannelLiveMultiPk(ctx, req.(*channel_live_logic.DisinviteChannelLiveMultiPkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveMultiPkTeamInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveMultiPkTeamInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkTeamInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveMultiPkTeamInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkTeamInfo(ctx, req.(*channel_live_logic.GetChannelLiveMultiPkTeamInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_InitChannelLiveMultiPkTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.InitChannelLiveMultiPkTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).InitChannelLiveMultiPkTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/InitChannelLiveMultiPkTeam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).InitChannelLiveMultiPkTeam(ctx, req.(*channel_live_logic.InitChannelLiveMultiPkTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_LeaveFansGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.LeaveFansGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).LeaveFansGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/LeaveFansGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).LeaveFansGroup(ctx, req.(*channel_live_logic.LeaveFansGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetVirtualLiveChannelSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetVirtualLiveChannelSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetVirtualLiveChannelSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetVirtualLiveChannelSecret",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetVirtualLiveChannelSecret(ctx, req.(*channel_live_logic.GetVirtualLiveChannelSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetUserFansGiftPri_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetUserFansGiftPriRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetUserFansGiftPri(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetUserFansGiftPri",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetUserFansGiftPri(ctx, req.(*channel_live_logic.GetUserFansGiftPriRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_ReportLiveShowScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.ReportLiveShowScoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).ReportLiveShowScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/ReportLiveShowScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).ReportLiveShowScore(ctx, req.(*channel_live_logic.ReportLiveShowScoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetLiveShowEntryInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetLiveShowEntryInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetLiveShowEntryInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetLiveShowEntryInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetLiveShowEntryInfo(ctx, req.(*channel_live_logic.GetLiveShowEntryInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveRandomTitle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveRandomTitleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveRandomTitle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_live.ChannelLiveLogic/GetChannelLiveRandomTitle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveRandomTitle(ctx, req.(*channel_live_logic.GetChannelLiveRandomTitleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelLiveLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.channel_live.ChannelLiveLogic",
	HandlerType: (*ChannelLiveLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLiveChannelInfo",
			Handler:    _ChannelLiveLogic_GetLiveChannelInfo_Handler,
		},
		{
			MethodName: "ChannelLiveHeartbeat",
			Handler:    _ChannelLiveLogic_ChannelLiveHeartbeat_Handler,
		},
		{
			MethodName: "SetChannelLiveStatus",
			Handler:    _ChannelLiveLogic_SetChannelLiveStatus_Handler,
		},
		{
			MethodName: "GetChannelLiveStatus",
			Handler:    _ChannelLiveLogic_GetChannelLiveStatus_Handler,
		},
		{
			MethodName: "ApplyPk",
			Handler:    _ChannelLiveLogic_ApplyPk_Handler,
		},
		{
			MethodName: "HandlerApply",
			Handler:    _ChannelLiveLogic_HandlerApply_Handler,
		},
		{
			MethodName: "BatchGetChannelLiveStatusByAccount",
			Handler:    _ChannelLiveLogic_BatchGetChannelLiveStatusByAccount_Handler,
		},
		{
			MethodName: "SetPkStatus",
			Handler:    _ChannelLiveLogic_SetPkStatus_Handler,
		},
		{
			MethodName: "CancelPKApply",
			Handler:    _ChannelLiveLogic_CancelPKApply_Handler,
		},
		{
			MethodName: "GetChannelLivePKRecord",
			Handler:    _ChannelLiveLogic_GetChannelLivePKRecord_Handler,
		},
		{
			MethodName: "GetChannelLivePkRankUser",
			Handler:    _ChannelLiveLogic_GetChannelLivePkRankUser_Handler,
		},
		{
			MethodName: "GetChannelLiveRankUser",
			Handler:    _ChannelLiveLogic_GetChannelLiveRankUser_Handler,
		},
		{
			MethodName: "GetChannelLiveWatchTimeRankUser",
			Handler:    _ChannelLiveLogic_GetChannelLiveWatchTimeRankUser_Handler,
		},
		{
			MethodName: "GetChannelLiveData",
			Handler:    _ChannelLiveLogic_GetChannelLiveData_Handler,
		},
		{
			MethodName: "GetFansRankList",
			Handler:    _ChannelLiveLogic_GetFansRankList_Handler,
		},
		{
			MethodName: "GetFansInfo",
			Handler:    _ChannelLiveLogic_GetFansInfo_Handler,
		},
		{
			MethodName: "GetAnchorFansInfo",
			Handler:    _ChannelLiveLogic_GetAnchorFansInfo_Handler,
		},
		{
			MethodName: "SearchAnchor",
			Handler:    _ChannelLiveLogic_SearchAnchor_Handler,
		},
		{
			MethodName: "ReportClientIDChange",
			Handler:    _ChannelLiveLogic_ReportClientIDChange_Handler,
		},
		{
			MethodName: "GetApplyList",
			Handler:    _ChannelLiveLogic_GetApplyList_Handler,
		},
		{
			MethodName: "GetPkInfo",
			Handler:    _ChannelLiveLogic_GetPkInfo_Handler,
		},
		{
			MethodName: "GetMyToolList",
			Handler:    _ChannelLiveLogic_GetMyToolList_Handler,
		},
		{
			MethodName: "GetItemConfig",
			Handler:    _ChannelLiveLogic_GetItemConfig_Handler,
		},
		{
			MethodName: "SetChannelLiveOpponentMicFlag",
			Handler:    _ChannelLiveLogic_SetChannelLiveOpponentMicFlag_Handler,
		},
		{
			MethodName: "StartPkMatch",
			Handler:    _ChannelLiveLogic_StartPkMatch_Handler,
		},
		{
			MethodName: "CancelPkMatch",
			Handler:    _ChannelLiveLogic_CancelPkMatch_Handler,
		},
		{
			MethodName: "GetPKMatchInfo",
			Handler:    _ChannelLiveLogic_GetPKMatchInfo_Handler,
		},
		{
			MethodName: "GetUserMissionList",
			Handler:    _ChannelLiveLogic_GetUserMissionList_Handler,
		},
		{
			MethodName: "GetFansMissionList",
			Handler:    _ChannelLiveLogic_GetFansMissionList_Handler,
		},
		{
			MethodName: "GetActorMissionList",
			Handler:    _ChannelLiveLogic_GetActorMissionList_Handler,
		},
		{
			MethodName: "HandleUserMissionAtInterval",
			Handler:    _ChannelLiveLogic_HandleUserMissionAtInterval_Handler,
		},
		{
			MethodName: "HandleShareLiveChannelMission",
			Handler:    _ChannelLiveLogic_HandleShareLiveChannelMission_Handler,
		},
		{
			MethodName: "GetProcessActorMissionDesc",
			Handler:    _ChannelLiveLogic_GetProcessActorMissionDesc_Handler,
		},
		{
			MethodName: "HandleFansMissionAtInterval",
			Handler:    _ChannelLiveLogic_HandleFansMissionAtInterval_Handler,
		},
		{
			MethodName: "GetAnchorHonorNameplate",
			Handler:    _ChannelLiveLogic_GetAnchorHonorNameplate_Handler,
		},
		{
			MethodName: "GetRankingList",
			Handler:    _ChannelLiveLogic_GetRankingList_Handler,
		},
		{
			MethodName: "GetFansAddedGroupList",
			Handler:    _ChannelLiveLogic_GetFansAddedGroupList_Handler,
		},
		{
			MethodName: "SetFansGroupName",
			Handler:    _ChannelLiveLogic_SetFansGroupName_Handler,
		},
		{
			MethodName: "CheckSetGroupNamePermit",
			Handler:    _ChannelLiveLogic_CheckSetGroupNamePermit_Handler,
		},
		{
			MethodName: "CheckUserIsFans",
			Handler:    _ChannelLiveLogic_CheckUserIsFans_Handler,
		},
		{
			MethodName: "ChannelLiveReport",
			Handler:    _ChannelLiveLogic_ChannelLiveReport_Handler,
		},
		{
			MethodName: "AcceptAppointPk",
			Handler:    _ChannelLiveLogic_AcceptAppointPk_Handler,
		},
		{
			MethodName: "ConfirmAppointPkPush",
			Handler:    _ChannelLiveLogic_ConfirmAppointPkPush_Handler,
		},
		{
			MethodName: "GetAppointPkInfo",
			Handler:    _ChannelLiveLogic_GetAppointPkInfo_Handler,
		},
		{
			MethodName: "GetAnchorValidPlateList",
			Handler:    _ChannelLiveLogic_GetAnchorValidPlateList_Handler,
		},
		{
			MethodName: "WearAnchorPlate",
			Handler:    _ChannelLiveLogic_WearAnchorPlate_Handler,
		},
		{
			MethodName: "GetChannelLiveMultiPkPermission",
			Handler:    _ChannelLiveLogic_GetChannelLiveMultiPkPermission_Handler,
		},
		{
			MethodName: "SerarchPkAnchor",
			Handler:    _ChannelLiveLogic_SerarchPkAnchor_Handler,
		},
		{
			MethodName: "ApplyChannelLiveMultiPk",
			Handler:    _ChannelLiveLogic_ApplyChannelLiveMultiPk_Handler,
		},
		{
			MethodName: "MatchMultiPk",
			Handler:    _ChannelLiveLogic_MatchMultiPk_Handler,
		},
		{
			MethodName: "CancelMatchMultiPk",
			Handler:    _ChannelLiveLogic_CancelMatchMultiPk_Handler,
		},
		{
			MethodName: "AcceptChannelLiveMultiPk",
			Handler:    _ChannelLiveLogic_AcceptChannelLiveMultiPk_Handler,
		},
		{
			MethodName: "StartChannelLiveMultiPk",
			Handler:    _ChannelLiveLogic_StartChannelLiveMultiPk_Handler,
		},
		{
			MethodName: "GetChannelLiveMultiPkRank",
			Handler:    _ChannelLiveLogic_GetChannelLiveMultiPkRank_Handler,
		},
		{
			MethodName: "GetChannelLiveMultiPkKnightList",
			Handler:    _ChannelLiveLogic_GetChannelLiveMultiPkKnightList_Handler,
		},
		{
			MethodName: "GetChannelLiveMultiPkRecordList",
			Handler:    _ChannelLiveLogic_GetChannelLiveMultiPkRecordList_Handler,
		},
		{
			MethodName: "CancelChannelLiveMultiPkTeam",
			Handler:    _ChannelLiveLogic_CancelChannelLiveMultiPkTeam_Handler,
		},
		{
			MethodName: "StopChannelLiveMultiPk",
			Handler:    _ChannelLiveLogic_StopChannelLiveMultiPk_Handler,
		},
		{
			MethodName: "DisinviteChannelLiveMultiPk",
			Handler:    _ChannelLiveLogic_DisinviteChannelLiveMultiPk_Handler,
		},
		{
			MethodName: "GetChannelLiveMultiPkTeamInfo",
			Handler:    _ChannelLiveLogic_GetChannelLiveMultiPkTeamInfo_Handler,
		},
		{
			MethodName: "InitChannelLiveMultiPkTeam",
			Handler:    _ChannelLiveLogic_InitChannelLiveMultiPkTeam_Handler,
		},
		{
			MethodName: "LeaveFansGroup",
			Handler:    _ChannelLiveLogic_LeaveFansGroup_Handler,
		},
		{
			MethodName: "GetVirtualLiveChannelSecret",
			Handler:    _ChannelLiveLogic_GetVirtualLiveChannelSecret_Handler,
		},
		{
			MethodName: "GetUserFansGiftPri",
			Handler:    _ChannelLiveLogic_GetUserFansGiftPri_Handler,
		},
		{
			MethodName: "ReportLiveShowScore",
			Handler:    _ChannelLiveLogic_ReportLiveShowScore_Handler,
		},
		{
			MethodName: "GetLiveShowEntryInfo",
			Handler:    _ChannelLiveLogic_GetLiveShowEntryInfo_Handler,
		},
		{
			MethodName: "GetChannelLiveRandomTitle",
			Handler:    _ChannelLiveLogic_GetChannelLiveRandomTitle_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/channel_live/grpc_channel_live.proto",
}

func init() {
	proto.RegisterFile("api/channel_live/grpc_channel_live.proto", fileDescriptor_grpc_channel_live_92d3c71037e41d8c)
}

var fileDescriptor_grpc_channel_live_92d3c71037e41d8c = []byte{
	// 1916 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x9a, 0xf9, 0x6f, 0xd4, 0xcc,
	0x1d, 0xc6, 0x65, 0x90, 0x4a, 0x3b, 0xa5, 0x40, 0x87, 0x16, 0x68, 0xd6, 0x68, 0x53, 0x5a, 0x28,
	0x57, 0x76, 0x49, 0x20, 0x1c, 0x01, 0x2a, 0x85, 0xa4, 0x2c, 0x51, 0x92, 0x76, 0x95, 0x4d, 0x41,
	0xe5, 0x97, 0x68, 0x70, 0x86, 0x5d, 0x6b, 0xbd, 0xb6, 0x63, 0xcf, 0x86, 0xa6, 0x52, 0xa5, 0x34,
	0x14, 0x0a, 0x14, 0xd1, 0x92, 0x42, 0x2b, 0x81, 0x2a, 0x15, 0xe8, 0x45, 0x0f, 0xa0, 0x77, 0xff,
	0xa9, 0x1e, 0xef, 0xfd, 0xbe, 0xd2, 0x7b, 0x69, 0xc6, 0xeb, 0x59, 0xcf, 0xda, 0x33, 0xb6, 0xf7,
	0xfd, 0x2d, 0x89, 0x3f, 0xcf, 0x3c, 0x8f, 0x3d, 0x33, 0xdf, 0x99, 0x71, 0x0c, 0xf6, 0x23, 0xd7,
	0x2c, 0x1b, 0x0d, 0x64, 0xdb, 0xd8, 0x5a, 0xb0, 0xcc, 0x65, 0x5c, 0xae, 0x7b, 0xae, 0xb1, 0x10,
	0xfd, 0x4b, 0xc9, 0xf5, 0x1c, 0xe2, 0xc0, 0xed, 0x75, 0x54, 0x42, 0xae, 0x59, 0x8a, 0x5e, 0x1a,
	0x38, 0x1c, 0xfd, 0x6d, 0xc1, 0x72, 0xea, 0xa6, 0x11, 0xb6, 0x36, 0x44, 0xff, 0x34, 0xc4, 0xfe,
	0xb4, 0x10, 0x34, 0x31, 0xb0, 0x9b, 0x9a, 0xe1, 0xef, 0x12, 0x6c, 0xfb, 0xa6, 0x63, 0x77, 0x7f,
	0x0a, 0x2e, 0x8f, 0xac, 0x9f, 0x07, 0xdb, 0x26, 0x02, 0xf1, 0x8c, 0xb9, 0x8c, 0x67, 0xa8, 0x14,
	0x7e, 0x0f, 0xc0, 0x0a, 0x26, 0xf4, 0xf7, 0xce, 0xa5, 0x29, 0xfb, 0xaa, 0x03, 0x0f, 0x97, 0xea,
	0xa8, 0x14, 0xf7, 0x2e, 0xc5, 0xd1, 0x39, 0xbc, 0x34, 0x30, 0x94, 0x83, 0xf6, 0xdd, 0x3d, 0x9b,
	0xd6, 0x56, 0x07, 0x37, 0x7e, 0xfa, 0xfd, 0x02, 0x5c, 0xd5, 0xc0, 0x17, 0x22, 0x81, 0x2e, 0x60,
	0xe4, 0x91, 0x2b, 0x18, 0x11, 0x58, 0x92, 0x34, 0x98, 0x04, 0xd3, 0x00, 0xe5, 0x5c, 0x3c, 0x8f,
	0xf0, 0x41, 0x10, 0xa1, 0x86, 0x49, 0x04, 0xac, 0x11, 0x44, 0xda, 0xbe, 0x34, 0x42, 0x12, 0xac,
	0x8a, 0x90, 0xcc, 0xf3, 0x08, 0x1f, 0x06, 0x11, 0x2a, 0x79, 0x22, 0x54, 0x72, 0x46, 0xa8, 0xa8,
	0x23, 0x7c, 0x54, 0x80, 0xdf, 0x01, 0x9b, 0xc6, 0x5d, 0xd7, 0x5a, 0xa9, 0x36, 0xe1, 0x97, 0x25,
	0x8d, 0x74, 0xae, 0x53, 0x9f, 0x3d, 0x69, 0x08, 0x6f, 0x7a, 0x55, 0x87, 0x0d, 0xb0, 0xf9, 0x02,
	0xb2, 0x17, 0x2d, 0xec, 0xb1, 0xcb, 0x70, 0x9f, 0x44, 0x1c, 0x85, 0xa8, 0xc9, 0xd7, 0x32, 0x71,
	0xdc, 0xe9, 0x07, 0x3a, 0xfc, 0x83, 0x06, 0xf6, 0x9c, 0x43, 0xc4, 0x68, 0x24, 0xdd, 0xef, 0xb9,
	0x95, 0x71, 0xc3, 0x70, 0xda, 0x36, 0x81, 0x67, 0x24, 0x0d, 0xa7, 0x4b, 0x69, 0xac, 0xb3, 0x9f,
	0x40, 0xcd, 0xc3, 0xae, 0xe9, 0x10, 0x83, 0xcf, 0xd6, 0x30, 0xa9, 0x36, 0x3b, 0x5d, 0xbd, 0x57,
	0x3e, 0x7a, 0x42, 0x86, 0xba, 0xef, 0xcb, 0x82, 0x71, 0x9b, 0xeb, 0x3a, 0x6c, 0x82, 0xcf, 0x4d,
	0x20, 0xdb, 0xc0, 0x56, 0x75, 0x3a, 0x78, 0xfc, 0xb2, 0xc7, 0x2a, 0x50, 0xd4, 0x6a, 0x7f, 0x36,
	0x90, 0x9b, 0xfd, 0x50, 0x87, 0x37, 0x35, 0xb0, 0x43, 0x7c, 0x04, 0xd5, 0xe9, 0x39, 0x6c, 0x38,
	0xde, 0x22, 0x3c, 0x92, 0x69, 0x68, 0x86, 0x38, 0xf5, 0x1f, 0xce, 0xa9, 0xe0, 0x41, 0x6e, 0xe8,
	0xf0, 0xae, 0x06, 0x76, 0xf5, 0x70, 0xcd, 0x39, 0x64, 0x37, 0xbf, 0xed, 0x63, 0x0f, 0x8e, 0x64,
	0x6b, 0x98, 0x0b, 0x68, 0x98, 0xa3, 0xb9, 0x35, 0x3c, 0xce, 0xcd, 0xa4, 0xe7, 0xc2, 0xc3, 0x64,
	0x7b, 0x2e, 0xd1, 0x28, 0xc3, 0x39, 0x15, 0x3c, 0xc8, 0x8f, 0x74, 0xf8, 0x44, 0x03, 0x45, 0x91,
	0xbb, 0x44, 0x87, 0xed, 0xbc, 0xd9, 0xea, 0x26, 0x3a, 0x95, 0xa9, 0xfd, 0x98, 0x8e, 0x46, 0x1b,
	0xeb, 0x57, 0xca, 0x33, 0xde, 0xd2, 0x3b, 0xeb, 0x51, 0x84, 0x9f, 0x44, 0x04, 0xa9, 0xd6, 0xa3,
	0x1e, 0x34, 0x65, 0x3d, 0x8a, 0xd1, 0xdc, 0xfb, 0xb6, 0x0e, 0x97, 0xc0, 0xd6, 0x0a, 0x26, 0xe7,
	0x91, 0xed, 0xd3, 0x6c, 0x33, 0xa6, 0x4f, 0xe0, 0x01, 0x79, 0x53, 0x51, 0x8e, 0xba, 0x1e, 0xcc,
	0x8a, 0x72, 0xcb, 0x3b, 0xac, 0x0e, 0x74, 0xae, 0xb3, 0x75, 0x77, 0xaf, 0xba, 0x8d, 0x70, 0xc1,
	0xdd, 0x97, 0x05, 0xe3, 0x36, 0x3f, 0xd6, 0xe1, 0x35, 0xf0, 0xf9, 0x0a, 0x26, 0xe3, 0xb6, 0xd1,
	0x70, 0x3c, 0x6e, 0x76, 0x48, 0xde, 0x8a, 0x48, 0x52, 0xcb, 0xc3, 0xd9, 0x61, 0x6e, 0x7c, 0x97,
	0x95, 0xff, 0x1a, 0x46, 0x9e, 0xd1, 0x08, 0x20, 0x28, 0xaf, 0x60, 0x5d, 0x48, 0x55, 0xfe, 0x45,
	0x8e, 0x3b, 0xdd, 0xd3, 0xd9, 0x32, 0x3a, 0x87, 0x5d, 0xc7, 0x23, 0x13, 0x96, 0x89, 0x6d, 0x32,
	0x35, 0x49, 0xfb, 0xba, 0x8e, 0xa5, 0xcb, 0x68, 0x12, 0xac, 0x5a, 0x46, 0x93, 0x79, 0x1e, 0xe1,
	0x27, 0xec, 0x66, 0xe9, 0xe3, 0xa0, 0x95, 0x91, 0x0d, 0x1e, 0x45, 0x37, 0x71, 0x48, 0x75, 0xb3,
	0x22, 0xc7, 0x9d, 0x5e, 0x2f, 0xc0, 0x05, 0xf0, 0x99, 0x0a, 0xad, 0xf9, 0xac, 0x1f, 0xbf, 0x22,
	0x97, 0x07, 0x04, 0xf5, 0xf8, 0x6a, 0x3a, 0xc4, 0x0d, 0xde, 0x28, 0xd0, 0x85, 0xa3, 0x82, 0xc9,
	0xec, 0xca, 0xbc, 0xe3, 0x58, 0xec, 0x5e, 0x14, 0x19, 0xbb, 0x94, 0x6a, 0xe1, 0xe8, 0x01, 0xb9,
	0xd9, 0x9b, 0xa1, 0xd9, 0x14, 0xc1, 0xad, 0x09, 0xc7, 0xbe, 0x6a, 0xd6, 0x55, 0x66, 0x5d, 0x2a,
	0xc5, 0x2c, 0x0a, 0x72, 0xb3, 0xb7, 0x0a, 0xf0, 0xb1, 0x06, 0x76, 0x8b, 0x9b, 0xb2, 0x6f, 0xb9,
	0xae, 0x63, 0x63, 0x9b, 0xcc, 0x9a, 0xc6, 0x79, 0x0b, 0xd5, 0xe1, 0x89, 0x4c, 0x5b, 0xb9, 0x1e,
	0x15, 0x4d, 0x73, 0xb2, 0x3f, 0x21, 0x4f, 0xf7, 0x76, 0x81, 0xcd, 0x17, 0x82, 0x3c, 0x52, 0x6d,
	0xce, 0xd2, 0x3a, 0x29, 0x9f, 0x2f, 0x11, 0x48, 0x39, 0x5f, 0x04, 0x8e, 0x3b, 0xbd, 0x53, 0x88,
	0x6c, 0x0d, 0x3a, 0x56, 0x29, 0x5b, 0x83, 0xae, 0xd7, 0xfe, 0x6c, 0x20, 0x37, 0x7b, 0xb7, 0x00,
	0x6d, 0xb0, 0x85, 0x0e, 0xb4, 0x69, 0x76, 0x89, 0x0d, 0x5a, 0x45, 0xcf, 0x45, 0x30, 0x6a, 0x77,
	0x20, 0x23, 0xc9, 0xfd, 0xde, 0x2b, 0x74, 0x56, 0x11, 0xba, 0xba, 0xcc, 0x9a, 0x3e, 0x3d, 0x02,
	0xb1, 0x31, 0xac, 0xa8, 0x61, 0x3d, 0x68, 0xca, 0x2a, 0x12, 0xa3, 0xb9, 0xf7, 0x4f, 0xc3, 0x15,
	0x8c, 0x96, 0xc3, 0x8c, 0xde, 0x3d, 0x68, 0x8a, 0x77, 0x8c, 0xe6, 0xde, 0xf7, 0x75, 0xf8, 0x7d,
	0xb0, 0x9d, 0x16, 0x0d, 0x83, 0x38, 0xc2, 0x8d, 0x2b, 0x9a, 0xeb, 0x65, 0xa9, 0x7b, 0x29, 0x0f,
	0xce, 0xed, 0xd7, 0x75, 0xf8, 0x50, 0x03, 0x85, 0x60, 0x83, 0x1e, 0x79, 0x42, 0xe3, 0x64, 0xca,
	0x26, 0xd8, 0x5b, 0x46, 0x16, 0x1c, 0x55, 0x6e, 0xea, 0x13, 0x35, 0x34, 0xcf, 0xf1, 0x7e, 0x64,
	0x3c, 0xd7, 0xcf, 0x74, 0x36, 0xe7, 0x03, 0xb8, 0xd6, 0x40, 0x1e, 0x8e, 0x1c, 0x4a, 0x3b, 0x42,
	0xe9, 0x9c, 0x57, 0xaa, 0x54, 0x73, 0x3e, 0x45, 0xc8, 0xd3, 0x3d, 0xd0, 0xe1, 0xba, 0x06, 0x06,
	0xe8, 0x60, 0xf6, 0x1c, 0x03, 0xfb, 0x7e, 0xf4, 0x09, 0x4f, 0x62, 0xdf, 0x80, 0xc7, 0x14, 0xe3,
	0x3f, 0x59, 0x42, 0x73, 0x8d, 0xf6, 0xa1, 0xe2, 0xa1, 0x1e, 0x46, 0xbb, 0x32, 0x32, 0xe0, 0x32,
	0x77, 0x65, 0xa2, 0x26, 0xbd, 0x2b, 0x25, 0x32, 0x9e, 0xeb, 0xe7, 0x3a, 0xbc, 0xad, 0x81, 0x9d,
	0x7c, 0xcf, 0x71, 0xc1, 0xb1, 0x1d, 0xef, 0x9b, 0xa8, 0x85, 0x5d, 0x0b, 0x11, 0x0c, 0x87, 0xd3,
	0xf6, 0x28, 0x22, 0x4f, 0xf3, 0x8c, 0xe4, 0x95, 0xf0, 0x2c, 0x8f, 0xf4, 0x4e, 0x55, 0xa3, 0x1b,
	0x3b, 0xd3, 0xae, 0xb3, 0x89, 0xa6, 0xa8, 0x6a, 0x11, 0x2c, 0xa5, 0xaa, 0x09, 0x24, 0xf7, 0x7b,
	0xac, 0xc3, 0xeb, 0x1a, 0xf8, 0x62, 0xa7, 0x02, 0x8c, 0x2f, 0x2e, 0xe2, 0xc5, 0x8a, 0xe7, 0xb4,
	0x5d, 0xe6, 0x5b, 0x56, 0xd7, 0x0b, 0x91, 0xa6, 0xf6, 0x47, 0xf2, 0x09, 0x78, 0x8a, 0x17, 0x3a,
	0x24, 0x60, 0x5b, 0x2d, 0xa0, 0x18, 0x40, 0x9f, 0x0d, 0x3c, 0x28, 0x5f, 0xf9, 0x04, 0x90, 0x5a,
	0x1f, 0xca, 0xcc, 0x72, 0xd7, 0x97, 0x41, 0xbf, 0x4f, 0x34, 0xb0, 0xd1, 0xac, 0x61, 0xc2, 0x91,
	0x2a, 0xf6, 0x5a, 0x26, 0x91, 0xf6, 0xbb, 0x84, 0x57, 0xf5, 0xbb, 0x54, 0xc2, 0xb3, 0xbc, 0x62,
	0xe7, 0x04, 0xc6, 0xd1, 0xca, 0x33, 0xe5, 0xd3, 0xd0, 0xd2, 0x73, 0x42, 0x0f, 0xa7, 0x3a, 0x27,
	0xc4, 0x50, 0x6e, 0xf9, 0x17, 0xb6, 0x81, 0x8f, 0x1e, 0xef, 0xd8, 0x56, 0x54, 0xba, 0x81, 0x8f,
	0x91, 0xaa, 0x0d, 0x7c, 0x02, 0xcc, 0x8d, 0xff, 0xca, 0xee, 0x75, 0xdc, 0x30, 0xb0, 0x4b, 0x77,
	0xa2, 0x8e, 0x69, 0x93, 0x6a, 0x53, 0x7a, 0xaf, 0x3d, 0x9c, 0xea, 0x5e, 0x63, 0x28, 0xb7, 0xfc,
	0x5f, 0xe7, 0xb5, 0x20, 0xdd, 0xb9, 0x79, 0x2d, 0x4e, 0x54, 0xdb, 0x7e, 0x43, 0xfe, 0x5a, 0x30,
	0x01, 0x56, 0xbe, 0x16, 0x4c, 0xe4, 0x79, 0x84, 0xff, 0x17, 0xe8, 0x18, 0x0f, 0x36, 0xdf, 0x01,
	0xc0, 0x76, 0x2c, 0x07, 0x95, 0xbb, 0xf4, 0x2e, 0xa8, 0x1a, 0xe3, 0x71, 0x96, 0xbb, 0xbe, 0x56,
	0x10, 0x6b, 0xdb, 0x45, 0x64, 0x99, 0x8b, 0x55, 0x5a, 0x74, 0xd8, 0x0c, 0x4f, 0xad, 0x6d, 0x22,
	0x9f, 0xa9, 0xb6, 0xf5, 0x4a, 0x78, 0x96, 0xff, 0x14, 0x68, 0xbf, 0x5f, 0xc2, 0xc8, 0x0b, 0x40,
	0xc6, 0x48, 0xfb, 0xbd, 0x87, 0x53, 0xf5, 0x7b, 0x0c, 0xe5, 0x96, 0xff, 0x2d, 0xc0, 0xe7, 0xb1,
	0xd7, 0x13, 0xb3, 0x6d, 0x8b, 0x98, 0xd5, 0x26, 0x9b, 0x83, 0xc1, 0x3a, 0x7d, 0x36, 0xd3, 0xd1,
	0x3e, 0xa6, 0x9b, 0xc3, 0x4b, 0x6d, 0xec, 0x93, 0x81, 0xaf, 0xf7, 0x2b, 0xf7, 0x5d, 0xc7, 0xf6,
	0x71, 0x27, 0xeb, 0x93, 0x22, 0x2d, 0xc5, 0x5b, 0x6b, 0xd8, 0xa3, 0x87, 0xd1, 0x6a, 0xb3, 0x73,
	0xb6, 0x1d, 0x91, 0x16, 0x36, 0xc6, 0x75, 0x5a, 0xe5, 0x67, 0x5c, 0x16, 0xe8, 0x68, 0x2e, 0x8d,
	0x90, 0xe2, 0x69, 0x11, 0xde, 0xd7, 0xc0, 0x4e, 0x76, 0x42, 0x8c, 0x87, 0x97, 0x2e, 0xd0, 0x12,
	0x3e, 0x0c, 0x74, 0x3c, 0xaf, 0x4c, 0xc8, 0xf4, 0xac, 0x08, 0x5d, 0xb0, 0x99, 0x6d, 0xca, 0xc3,
	0x1c, 0xb2, 0xa1, 0x10, 0x85, 0x42, 0xf3, 0x43, 0x99, 0x58, 0xc1, 0xf1, 0xd7, 0x45, 0xb8, 0xa6,
	0x01, 0x18, 0x9c, 0x3d, 0x04, 0xe3, 0x23, 0xca, 0x63, 0x4a, 0x92, 0xfd, 0x70, 0x0e, 0x85, 0x10,
	0xe2, 0x37, 0x45, 0xf8, 0x40, 0x03, 0xbb, 0x82, 0xaa, 0x96, 0xd0, 0x17, 0xc7, 0x95, 0x65, 0x50,
	0xde, 0x19, 0x27, 0x72, 0xeb, 0x84, 0x58, 0xbf, 0x0d, 0x46, 0x08, 0x3b, 0x03, 0xe6, 0x18, 0x21,
	0x12, 0x3e, 0x6d, 0x84, 0x48, 0x65, 0x42, 0xa6, 0xdf, 0x15, 0xe1, 0x2f, 0x34, 0xf0, 0xa5, 0xc4,
	0x09, 0x47, 0x37, 0x3e, 0xd2, 0x9d, 0xb8, 0x54, 0x11, 0xe6, 0x3a, 0x99, 0x5f, 0x28, 0x24, 0xfb,
	0x7d, 0x51, 0x5e, 0x81, 0xa6, 0x6d, 0xb3, 0xde, 0x20, 0xac, 0x10, 0xe7, 0xaa, 0x40, 0x5d, 0x5d,
	0x5f, 0x15, 0x28, 0x2a, 0x17, 0xb2, 0x3e, 0x57, 0x64, 0x0d, 0xde, 0x88, 0xe7, 0xcf, 0xda, 0xd5,
	0xf5, 0x95, 0x35, 0x2a, 0x17, 0xb2, 0xfe, 0xb1, 0x08, 0x7f, 0xa5, 0x01, 0x3d, 0x98, 0x44, 0x71,
	0xdd, 0x3c, 0x46, 0x2d, 0x38, 0xa6, 0x9c, 0x79, 0xc9, 0xa2, 0x30, 0xe5, 0xe9, 0xbe, 0xb4, 0x42,
	0xc4, 0x3f, 0x15, 0xe1, 0x3d, 0x0d, 0xec, 0xa8, 0x11, 0xc7, 0x4d, 0x98, 0x27, 0xc7, 0xa4, 0x03,
	0x3e, 0x09, 0x0f, 0x63, 0x8d, 0xe6, 0x54, 0x09, 0x81, 0xfe, 0x5c, 0x84, 0xbf, 0xd4, 0x40, 0x61,
	0xd2, 0xf4, 0x4d, 0x7b, 0xd9, 0x24, 0x38, 0x21, 0x95, 0xec, 0x45, 0xbd, 0x42, 0x13, 0x46, 0x1b,
	0xeb, 0x47, 0x2a, 0xe4, 0x7b, 0x51, 0x84, 0xcf, 0x34, 0xb0, 0x3b, 0x71, 0x20, 0xd0, 0xe7, 0xcb,
	0x36, 0x4c, 0xa7, 0xf3, 0x0c, 0x9f, 0x50, 0x15, 0x66, 0x3c, 0xd3, 0x9f, 0x58, 0x48, 0xf9, 0xb2,
	0x08, 0x1f, 0x69, 0x60, 0x60, 0xca, 0x36, 0x25, 0x1a, 0x28, 0xab, 0x19, 0x72, 0x49, 0x98, 0xef,
	0x54, 0x1f, 0x4a, 0x21, 0xdc, 0xab, 0x22, 0x5c, 0x06, 0x5b, 0x66, 0x30, 0x5a, 0xc6, 0xfc, 0xd8,
	0x23, 0x7d, 0x4b, 0x24, 0x62, 0x61, 0x86, 0xa1, 0x8c, 0xb4, 0xe0, 0xfb, 0x37, 0x9d, 0x0d, 0xad,
	0x0a, 0x26, 0x17, 0x4d, 0x8f, 0xb4, 0x91, 0x15, 0x79, 0x3d, 0x51, 0xc3, 0x86, 0x87, 0x89, 0xea,
	0x7f, 0x40, 0x32, 0x4d, 0xda, 0xd0, 0x52, 0x4a, 0x85, 0x7c, 0x7f, 0xd7, 0xd9, 0x82, 0xde, 0x79,
	0xcb, 0xc6, 0xee, 0xc2, 0xbc, 0x4a, 0xaa, 0x9e, 0xa9, 0xfa, 0x67, 0x59, 0x0f, 0x9a, 0xb6, 0xa0,
	0x27, 0x29, 0x84, 0x10, 0xff, 0xd0, 0xe1, 0x0d, 0x0d, 0x6c, 0x0f, 0x0e, 0x44, 0xec, 0x9f, 0xb9,
	0x0d, 0xe7, 0x5a, 0xcd, 0x70, 0x3c, 0xf9, 0x4b, 0x86, 0x04, 0x36, 0x8c, 0x31, 0x92, 0x47, 0x22,
	0xe4, 0xf8, 0xa7, 0x0e, 0x6f, 0x05, 0x9f, 0x07, 0x84, 0xd4, 0x37, 0x6c, 0xe2, 0xad, 0xb0, 0xe9,
	0x35, 0xa2, 0xfe, 0xea, 0x42, 0x80, 0xd3, 0xb6, 0x9b, 0xc9, 0x1a, 0x21, 0xca, 0xbf, 0xf4, 0x84,
	0x85, 0x7b, 0x0e, 0xd9, 0x8b, 0x4e, 0x6b, 0xde, 0x24, 0x16, 0xce, 0xb8, 0x70, 0x47, 0x14, 0xf9,
	0x16, 0x6e, 0x41, 0x28, 0x24, 0xfb, 0xb7, 0x3e, 0x70, 0x72, 0x6d, 0x75, 0x10, 0xc6, 0x3f, 0x8c,
	0xb9, 0xb3, 0x3a, 0xb8, 0xa1, 0xee, 0xac, 0xaf, 0x0e, 0xee, 0x2a, 0xc7, 0x0e, 0xbb, 0xec, 0xf3,
	0x97, 0xf2, 0xb9, 0xcb, 0x60, 0x87, 0xe1, 0xb4, 0x4a, 0x4b, 0xed, 0x6b, 0xc8, 0x2e, 0x11, 0x12,
	0x7c, 0x2a, 0x53, 0x42, 0xae, 0x79, 0x79, 0xac, 0xee, 0x58, 0xc8, 0xae, 0x97, 0x46, 0x47, 0x08,
	0x29, 0x19, 0x4e, 0xab, 0xcc, 0x2e, 0x19, 0x8e, 0x55, 0x46, 0xae, 0x5b, 0xee, 0xfd, 0xae, 0xe7,
	0x74, 0xf4, 0x97, 0xa7, 0x1b, 0x36, 0xce, 0x55, 0x27, 0xae, 0x7c, 0x8a, 0x29, 0x8e, 0x7e, 0x1c,
	0x00, 0x00, 0xff, 0xff, 0xe5, 0xab, 0x25, 0x49, 0x05, 0x24, 0x00, 0x00,
}
