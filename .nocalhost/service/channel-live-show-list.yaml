- name: channel-live-show-list # 服务deploy名称
  serviceType: deployment
  containers:
    - name: service
      dev:
        ${_INCLUDE_:- ../include/service_dev_config_base.yaml | nindent 8}
        command:
          run: # run参数
            - go run ./services/channel-live-show-list/main.go
            - --server.configFile=/config/service-config.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
          debug: # debug参数
            - dlv # debug参数
            - --headless
            - --log
            - --listen :9999
            - --api-version 2
            - --accept-multiclient
            - debug
            - ./services/channel-live-show-list/main.go
            - --server.configFile=/config/service-config.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
        hotReload: false
        sync:
          mode: "pattern"
          type: "send"
          filePattern:
            ${_INCLUDE_:- ../include/common_deps.yaml | nindent 12}
            - "services/channel-live-show-list" # 服务代码目录
            - "services/notify"
            - "services/tt-rev/common/goroutineex"
          ignoreFilePattern: [ ]
        portForward: []