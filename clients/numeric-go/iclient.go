// Code generated by quicksilver-cli. DO NOT EDIT.
package numeric_go

import (
	context "context"
	"golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/numeric-go"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AddUserNumeric(ctx context.Context, req *pb.AddUserNumericReq) (*pb.AddUserNumericResp, protocol.ServerError)
	AddUserNumericV2(ctx context.Context, req *pb.AddUserNumericV2Req) (*pb.AddUserNumericV2Resp, protocol.ServerError)
	BatchGetPersonalNumeric(ctx context.Context, uidList []uint32) (*pb.BatchGetPersonalNumericResp, protocol.ServerError)
	BatchGetUserNumericLock(ctx context.Context, req *pb.BatchGetUserNumericLockReq) (*pb.BatchGetUserNumericLockResp, protocol.ServerError)
	BatchRecordSendGiftEvent(ctx context.Context, req *pb.BatchRecordSendGiftEventReq) (*pb.BatchRecordSendGiftEventResp, protocol.ServerError)
	GetPersonalNumericV2(ctx context.Context, uid uint32) (*pb.GetPersonalNumericV2Resp, protocol.ServerError)
	GetUserNumericLock(ctx context.Context, uid uint32) (*pb.GetUserNumericLockResp, protocol.ServerError)
	GetUserRichSwitch(ctx context.Context, uid uint32) (*pb.GetUserRichSwitchResp, protocol.ServerError)
	RecordSendGiftEvent(ctx context.Context, req *pb.RecordSendGiftEventReq) (*pb.RecordSendGiftEventResp, protocol.ServerError)
	SetUserNumericLock(ctx context.Context, req *pb.SetUserNumericLockReq) (*pb.SetUserNumericLockResp, protocol.ServerError)
	SetUserRichSwitch(ctx context.Context, uid uint32, enable bool) (*pb.SetUserRichSwitchResp, protocol.ServerError)
	UseRichCard(ctx context.Context, req *pb.UseRichCardReq) (*pb.UseRichCardResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
