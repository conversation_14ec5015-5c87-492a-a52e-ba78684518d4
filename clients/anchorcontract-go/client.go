package anchorcontract_go

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"google.golang.org/grpc"

	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	PB "golang.52tt.com/protocol/services/anchorcontract-go"
)

const (
	serviceName = "anchorcontract-go"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return PB.NewAnchorContractGoClient(cc)
			}, dopts...,
		),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() PB.AnchorContractGoClient { return c.Stub().(PB.AnchorContractGoClient) }

func (c *Client) GetUserContract(ctx context.Context, uin, targetUid uint32) (*PB.GetUserContractResp, protocol.ServerError) {

	resp, err := c.typedStub().GetUserContract(ctx, &PB.GetUserContractReq{Uid: targetUid})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserContractCacheInfo(ctx context.Context, uin, targetUid uint32) (*PB.ContractCacheInfo, protocol.ServerError) {

	resp, err := c.typedStub().GetUserContractCacheInfo(ctx, &PB.GetUserContractCacheInfoReq{Uid: targetUid})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserApplySignRecord(ctx context.Context, uid uint32, in *PB.GetUserApplySignRecordReq) (*PB.GetUserApplySignRecordResp, protocol.ServerError) {

	resp, err := c.typedStub().GetUserApplySignRecord(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetUserApplySignRecord(ctx context.Context, uid uint32, in *PB.BatchGetUserApplySignRecordReq) (*PB.BatchGetUserApplySignRecordResp, protocol.ServerError) {

	resp, err := c.typedStub().BatchGetUserApplySignRecord(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGuildApplySignRecord(ctx context.Context, uid uint32, in *PB.GetGuildApplySignRecordReq) (*PB.GetGuildApplySignRecordResp, protocol.ServerError) {

	resp, err := c.typedStub().GetGuildApplySignRecord(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAllApplySignRecord(ctx context.Context, uid uint32, in *PB.GetAllApplySignRecordReq) (*PB.GetAllApplySignRecordResp, protocol.ServerError) {

	resp, err := c.typedStub().GetAllApplySignRecord(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGuildApplySignRecordCnt(ctx context.Context, uid uint32, in *PB.GetGuildApplySignRecordCntReq) (*PB.GetGuildApplySignRecordCntResp, protocol.ServerError) {

	resp, err := c.typedStub().GetGuildApplySignRecordCnt(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) OfficialHandleApplySign(ctx context.Context, uid uint32, in *PB.OfficialHandleApplySignReq) (*PB.OfficialHandleApplySignResp, protocol.ServerError) {

	resp, err := c.typedStub().OfficialHandleApplySign(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetAnchorIdentity(ctx context.Context, uid uint32, in *PB.BatchGetAnchorIdentityReq) (*PB.BatchGetAnchorIdentityResp, protocol.ServerError) {

	resp, err := c.typedStub().BatchGetAnchorIdentity(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGuildAnchorIdentity(ctx context.Context, uid uint32, in *PB.GetGuildAnchorIdentityReq) (*PB.GetGuildAnchorIdentityResp, protocol.ServerError) {

	resp, err := c.typedStub().GetGuildAnchorIdentity(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetUserContract(ctx context.Context, uid uint32, in *PB.BatchGetUserContractReq) (*PB.BatchGetUserContractResp, protocol.ServerError) {

	resp, err := c.typedStub().BatchGetUserContract(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ReclaimAnchorIdentity(ctx context.Context, uid uint32, in *PB.ReclaimAnchorIdentityReq) (*PB.ReclaimAnchorIdentityResp, protocol.ServerError) {

	resp, err := c.typedStub().ReclaimAnchorIdentity(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserAnchorIdentityLog(ctx context.Context, uid uint32, in *PB.GetUserAnchorIdentityLogReq) (*PB.GetUserAnchorIdentityLogResp, protocol.ServerError) {

	resp, err := c.typedStub().GetUserAnchorIdentityLog(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetUserAnchorIdentityLog(ctx context.Context, uid uint32, in *PB.BatchGetUserAnchorIdentityLogReq) (*PB.BatchGetUserAnchorIdentityLogResp, protocol.ServerError) {

	resp, err := c.typedStub().BatchGetUserAnchorIdentityLog(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGuildAnchorIdentityLog(ctx context.Context, uid uint32, in *PB.GetGuildAnchorIdentityLogReq) (*PB.GetGuildAnchorIdentityLogResp, protocol.ServerError) {

	resp, err := c.typedStub().GetGuildAnchorIdentityLog(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetApplyBlacklist(ctx context.Context, uid uint32, in *PB.BatchGetApplyBlacklistReq) (*PB.BatchGetApplyBlacklistResp, protocol.ServerError) {

	resp, err := c.typedStub().BatchGetApplyBlacklist(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) HandleApplyBlackInfo(ctx context.Context, uid uint32, in *PB.HandleApplyBlackInfoReq) (*PB.HandleApplyBlackInfoResp, protocol.ServerError) {

	resp, err := c.typedStub().HandleApplyBlackInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetUserLiveAnchorExamine(ctx context.Context, uid uint32, in *PB.BatchGetUserLiveAnchorExamineReq) (*PB.BatchGetUserLiveAnchorExamineResp, protocol.ServerError) {

	resp, err := c.typedStub().BatchGetUserLiveAnchorExamine(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGuildLiveAnchorExamine(ctx context.Context, uid uint32, in *PB.GetGuildLiveAnchorExamineReq) (*PB.GetGuildLiveAnchorExamineResp, protocol.ServerError) {

	resp, err := c.typedStub().GetGuildLiveAnchorExamine(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAllLiveAnchorExamine(ctx context.Context, uid uint32, in *PB.GetAllLiveAnchorExamineReq) (*PB.GetAllLiveAnchorExamineResp, protocol.ServerError) {

	resp, err := c.typedStub().GetAllLiveAnchorExamine(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateLiveAnchorExamineStatus(ctx context.Context, uid uint32, in *PB.UpdateLiveAnchorExamineStatusReq) (*PB.UpdateLiveAnchorExamineStatusResp, protocol.ServerError) {

	resp, err := c.typedStub().UpdateLiveAnchorExamineStatus(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateLiveAnchorExamineTime(ctx context.Context, uid uint32, in *PB.UpdateLiveAnchorExamineTimeReq) (*PB.UpdateLiveAnchorExamineTimeResp, protocol.ServerError) {

	resp, err := c.typedStub().UpdateLiveAnchorExamineTime(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAllApplyBlacklist(ctx context.Context, uid uint32, in *PB.GetAllApplyBlacklistReq) (*PB.GetAllApplyBlacklistResp, protocol.ServerError) {

	resp, err := c.typedStub().GetAllApplyBlacklist(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGuildContractByIdentity(ctx context.Context, in *PB.GetGuildContractByIdentityReq) (*PB.GetGuildContractByIdentityResp, protocol.ServerError) {

	resp, err := c.typedStub().GetGuildContractByIdentity(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGuildContract(ctx context.Context, guildId, page, pageSize uint32) (*PB.GetGuildContractResp, protocol.ServerError) {

	resp, err := c.typedStub().GetGuildContract(ctx, &PB.GetGuildContractReq{
		GuildId:  guildId,
		Page:     page,
		PageSize: pageSize,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGuildContractSum(ctx context.Context, guildId uint32) (*PB.GetGuildContractSumResp, protocol.ServerError) {

	resp, err := c.typedStub().GetGuildContractSum(ctx, &PB.GetGuildContractSumReq{
		GuildId: guildId,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateSignedAnchorAgentId(ctx context.Context, guildId, agentUid, identityType uint32, anchorList []uint32) (*PB.UpdateSignedAnchorAgentIdResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateSignedAnchorAgentId(ctx, &PB.UpdateSignedAnchorAgentIdReq{
		GuildId:      guildId,
		AgentUid:     agentUid,
		IdentityType: identityType,
		AnchorList:   anchorList,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserExamineCert(ctx context.Context, uid uint32) (*PB.GetUserExamineCertResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserExamineCert(ctx, &PB.GetUserExamineCertReq{Uid: uid})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRadioLiveAnchorExamine(ctx context.Context, uid uint32) (uint32, error) {
	resp, err := c.typedStub().GetRadioLiveAnchorExamine(ctx, &PB.GetRadioLiveAnchorExamineReq{Uid: uid})
	return resp.GetTtl(), err
}

func (c *Client) UpdateRadioLiveAnchorExamine(ctx context.Context, uid uint32) (*PB.UpdateRadioLiveAnchorExamineResp, error) {
	resp, err := c.typedStub().UpdateRadioLiveAnchorExamine(ctx, &PB.UpdateRadioLiveAnchorExamineReq{Uid: uid})
	return resp, err
}

func (c *Client) BatchGetUserExamineCert(ctx context.Context, uids []uint32) (*PB.BatchGetUserExamineCertResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetUserExamineCert(ctx, &PB.BatchGetUserExamineCertReq{UidList: uids})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGuildApplySignRecordList(ctx context.Context, in *PB.GetGuildApplySignRecordListReq) (*PB.GetGuildApplySignRecordListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGuildApplySignRecordList(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGuildCancelSignRecordList(ctx context.Context, in *PB.GetGuildCancelSignRecordListReq) (*PB.GetGuildCancelSignRecordListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGuildCancelSignRecordList(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) PresidentHandleApplySign(ctx context.Context, in *PB.PresidentHandleApplySignReq) (*PB.PresidentHandleApplySignResp, protocol.ServerError) {
	resp, err := c.typedStub().PresidentHandleApplySign(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) HandlerCancelContractApply(ctx context.Context, in *PB.HandlerCancelContractApplyReq) (*PB.HandlerCancelContractApplyResp, protocol.ServerError) {
	resp, err := c.typedStub().HandlerCancelContractApply(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGuildAnchorExtInfoList(ctx context.Context, in *PB.GetGuildAnchorExtInfoListReq) (*PB.GetGuildAnchorExtInfoListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGuildAnchorExtInfoList(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) HandleFocusAnchor(ctx context.Context, in *PB.HandleFocusAnchorReq) (*PB.HandleFocusAnchorResp, protocol.ServerError) {
	resp, err := c.typedStub().HandleFocusAnchor(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateRemark(ctx context.Context, in *PB.UpdateRemarkReq) (*PB.UpdateRemarkResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateRemark(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GuildExtensionContract(ctx context.Context, in *PB.GuildExtensionContractReq) (*PB.GuildExtensionContractResp, protocol.ServerError) {
	resp, err := c.typedStub().GuildExtensionContract(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGuildExtensionContract(ctx context.Context, in *PB.BatchGuildExtensionContractReq) (*PB.BatchGuildExtensionContractResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGuildExtensionContract(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CancelContractByUid(ctx context.Context, in *PB.CancelContractByUidReq) (*PB.CancelContractByUidResp, protocol.ServerError) {
	resp, err := c.typedStub().CancelContractByUid(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetMultiPlayerCenterEntry(ctx context.Context, uid uint32) (string, protocol.ServerError) {
	resp, err := c.typedStub().GetMultiPlayerCenterEntry(ctx, &PB.GetMultiPlayerCenterEntryReq{Uid: uid})
	if err != nil {
		return "", protocol.ToServerError(err)
	}
	return resp.GetJumpUrl(), nil
}

func (c *Client) BatchGetUserContractCacheInfo(ctx context.Context, in *PB.BatchGetUserContractCacheInfoReq) (*PB.BatchGetUserContractCacheInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetUserContractCacheInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetLiveAnchorCert(ctx context.Context, uids []uint32) (map[uint32]*PB.BatchGetLiveAnchorCertResp_LiveAnchorCertInfo, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetLiveAnchorCert(ctx, &PB.BatchGetLiveAnchorCertReq{Uids: uids})
	return resp.GetUid2LiveAnchorCert(), protocol.ToServerError(err)
}

func (c *Client) GetAnchorCertListByItemId(ctx context.Context, in *PB.GetAnchorCertListByItemIdReq) (*PB.GetAnchorCertListByItemIdResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAnchorCertListByItemId(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAnchorCertTaskInfo(ctx context.Context, uid uint32) (*PB.GetAnchorCertTaskInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAnchorCertTaskInfo(ctx, &PB.GetAnchorCertTaskInfoReq{Uid: uid})
	return resp, protocol.ToServerError(err)
}

func (c *Client) ReclaimGuildAllAnchorIdentity(ctx context.Context, in *PB.ReclaimGuildAllAnchorIdentityReq) protocol.ServerError {
	_, err := c.typedStub().ReclaimGuildAllAnchorIdentity(ctx, in)
	return protocol.ToServerError(err)
}

// 获取所有合约及身份信息
func (c *Client) GetContract(ctx context.Context, in *PB.GetContractReq) (*PB.GetContractResp, protocol.ServerError) {
	resp, err := c.typedStub().GetContract(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ApplySignEsport(ctx context.Context, in *PB.ApplySignEsportReq) (*PB.ApplySignEsportResp, protocol.ServerError) {
	resp, err := c.typedStub().ApplySignEsport(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) OfficialHandleApplySignEsport(ctx context.Context, in *PB.OfficialHandleApplySignEsportReq) (*PB.OfficialHandleApplySignEsportResp, protocol.ServerError) {
	resp, err := c.typedStub().OfficialHandleApplySignEsport(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetContractInfo(ctx context.Context, in *PB.BatchGetContractInfoReq) (*PB.BatchGetContractInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetContractInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetIdentityChangeHistory(ctx context.Context, in *PB.GetIdentityChangeHistoryReq) (*PB.GetIdentityChangeHistoryResp, protocol.ServerError) {
	resp, err := c.typedStub().GetIdentityChangeHistory(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAnchorAgentUid(ctx context.Context, uids []uint32) (*PB.GetAnchorAgentUidResp, error) {
	resp, err := c.typedStub().GetAnchorAgentUid(ctx, &PB.GetAnchorAgentUidReq{Uids: uids})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSignEsportAuditToken(ctx context.Context, applyId uint32) (string, error) {
	resp, err := c.typedStub().GetSignEsportAuditToken(ctx, &PB.GetSignEsportAuditTokenReq{ApplyId: applyId})
	return resp.GetAuditToken(), protocol.ToServerError(err)
}

func (c *Client) CheckCanApplySign(ctx context.Context, req *PB.CheckCanApplySignReq) protocol.ServerError {
	_, err := c.typedStub().CheckCanApplySign(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) ApplySignDoyen(ctx context.Context, req *PB.ApplySignDoyenReq) protocol.ServerError {
	_, err := c.typedStub().ApplySignDoyen(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) CheckIsSignWhiteUid(ctx context.Context, req *PB.CheckIsSignWhiteUidReq) (*PB.CheckIsSignWhiteUidResp, protocol.ServerError) {
	resp, err := c.typedStub().CheckIsSignWhiteUid(ctx, req)

	return resp, protocol.ToServerError(err)
}

func (c *Client) CheckUserGreatLiveAnchor(ctx context.Context, req *PB.CheckUserGreatLiveAnchorReq) (*PB.CheckUserGreatLiveAnchorResp, protocol.ServerError) {
	resp, err := c.typedStub().CheckUserGreatLiveAnchor(ctx, req)

	return resp, protocol.ToServerError(err)
}

func (c *Client) CheckIfGreatLiveAnchor(ctx context.Context, req *PB.CheckIfGreatLiveAnchorReq) (*PB.CheckIfGreatLiveAnchorResp, protocol.ServerError) {
	resp, err := c.typedStub().CheckIfGreatLiveAnchor(ctx, req)

	return resp, protocol.ToServerError(err)
}

func (c *Client) AddSignWhiteUid(ctx context.Context, req *PB.AddSignWhiteUidReq) protocol.ServerError {
	_, err := c.typedStub().AddSignWhiteUid(ctx, req)

	return protocol.ToServerError(err)
}

func (c *Client) DelSignWhiteUid(ctx context.Context, req *PB.DelSignWhiteUidReq) protocol.ServerError {
	_, err := c.typedStub().DelSignWhiteUid(ctx, req)

	return protocol.ToServerError(err)
}

func (c *Client) GetRecommendTopGuildList(ctx context.Context, req *PB.GetRecommendTopGuildListReq) (*PB.GetRecommendTopGuildListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRecommendTopGuildList(ctx, req)

	return resp, protocol.ToServerError(err)
}

func (c *Client) GetContractWorkerConfigs(ctx context.Context, req *PB.GetContractWorkerConfigsReq) (*PB.GetContractWorkerConfigsResp, protocol.ServerError) {
	resp, err := c.typedStub().GetContractWorkerConfigs(ctx, req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) CheckCanApplyCancelContractV2(ctx context.Context, req *PB.CheckCanApplyCancelContractV2Req) (*PB.CheckCanApplyCancelContractV2Resp, protocol.ServerError) {
	resp, err := c.typedStub().CheckCanApplyCancelContractV2(ctx, req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetCancelContractApplyList(ctx context.Context, req *PB.GetCancelContractApplyListReq) (*PB.GetCancelContractApplyListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetCancelContractApplyList(ctx, req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) ApplySignContract(ctx context.Context, req *PB.ApplySignContractReq) protocol.ServerError {
	_, err := c.typedStub().ApplySignContract(ctx, req)
	return protocol.ToServerError(err)
}
func (c *Client) ApplyCancelContractNew(ctx context.Context, req *PB.ApplyCancelContractNewReq) protocol.ServerError {
	_, err := c.typedStub().ApplyCancelContractNew(ctx, req)
	return protocol.ToServerError(err)
}
func (c *Client) GetCancelContractTypeList(ctx context.Context, req *PB.GetCancelContractTypeListReq) (*PB.GetCancelContractTypeListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetCancelContractTypeList(ctx, req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetGuildSignRight(ctx context.Context, req *PB.GetGuildSignRightReq) (*PB.GetGuildSignRightResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGuildSignRight(ctx, req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) UpdateGuildSignRight(ctx context.Context, req *PB.UpdateGuildSignRightReq) (*PB.UpdateGuildSignRightResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateGuildSignRight(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetGuildCancelContractType(ctx context.Context, req *PB.SetGuildCancelContractTypeReq) (*PB.SetGuildCancelContractTypeResp, protocol.ServerError) {
	resp, err := c.typedStub().SetGuildCancelContractType(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) InvitePromote(ctx context.Context, req *PB.InvitePromoteReq) (*PB.InvitePromoteResp, protocol.ServerError) {
	resp, err := c.typedStub().InvitePromote(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserPromoteInviteInfo(ctx context.Context, req *PB.GetUserPromoteInviteInfoReq) (*PB.GetUserPromoteInviteInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserPromoteInviteInfo(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ProcPromoteInvite(ctx context.Context, req *PB.ProcPromoteInviteReq) (*PB.ProcPromoteInviteResp, protocol.ServerError) {
	resp, err := c.typedStub().ProcPromoteInvite(ctx, req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetCancelPayAmount(ctx context.Context, req *PB.GetCancelPayAmountReq) (*PB.GetCancelPayAmountResp, protocol.ServerError) {
	resp, err := c.typedStub().GetCancelPayAmount(ctx, req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) LockCancelPayAmount(ctx context.Context, req *PB.LockCancelPayAmountReq) (*PB.LockCancelPayAmountResp, protocol.ServerError) {
	resp, err := c.typedStub().LockCancelPayAmount(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGuildContractByCond(ctx context.Context, req *PB.GetGuildContractByCondReq) (*PB.GetGuildContractByCondResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGuildContractByCond(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CensorVideo(ctx context.Context, req *PB.CensorVideoReq) (*PB.CensorVideoResp, protocol.ServerError) {
	resp, err := c.typedStub().CensorVideo(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ContractClaimObsToken(ctx context.Context, req *PB.ContractClaimObsTokenReq) (*PB.ContractClaimObsTokenResp, protocol.ServerError) {
	resp, err := c.typedStub().ContractClaimObsToken(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetNeedConfirmWorkerType(ctx context.Context, req *PB.GetNeedConfirmWorkerTypeReq) (*PB.GetNeedConfirmWorkerTypeResp, protocol.ServerError) {
	resp, err := c.typedStub().GetNeedConfirmWorkerType(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ModifyWorkerType(ctx context.Context, req *PB.ModifyWorkerTypeReq) (*PB.ModifyWorkerTypeResp, protocol.ServerError) {
	resp, err := c.typedStub().ModifyWorkerType(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetContractChangeInfo(ctx context.Context, req *PB.GetContractChangeInfoReq) (*PB.GetContractChangeInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetContractChangeInfo(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) HandleContractChange(ctx context.Context, req *PB.HandleContractChangeReq) (*PB.HandleContractChangeResp, protocol.ServerError) {
	resp, err := c.typedStub().HandleContractChange(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) InviteMemberChangeWorkerType(ctx context.Context, req *PB.InviteMemberChangeWorkerTypeReq) (*PB.InviteMemberChangeWorkerTypeResp, protocol.ServerError) {
	resp, err := c.typedStub().InviteMemberChangeWorkerType(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRejectReason(ctx context.Context, req *PB.GetRejectReasonReq) (*PB.GetRejectReasonResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRejectReason(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetNegotiateReasonType(ctx context.Context, req *PB.GetNegotiateReasonTypeReq) (*PB.GetNegotiateReasonTypeResp, protocol.ServerError) {
	resp, err := c.typedStub().GetNegotiateReasonType(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CheckIsTotalNewMultiAnchor(ctx context.Context, req *PB.CheckIsTotalNewMultiAnchorReq) (*PB.CheckIsTotalNewMultiAnchorResp, protocol.ServerError) {
	resp, err := c.typedStub().CheckIsTotalNewMultiAnchor(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchSetUserExamineCert(ctx context.Context, req *PB.BatchSetUserExamineCertReq) (*PB.CertEmptyMsg, protocol.ServerError) {
	resp, err := c.typedStub().BatchSetUserExamineCert(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchCheckUserExamineCert(ctx context.Context, req *PB.BatchSetUserExamineCertReq) (*PB.BatchSetUserExamineCertResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchCheckUserExamineCert(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserAllExamineCert(ctx context.Context, req *PB.GetUserAllExamineCertReq) (*PB.GetUserAllExamineCertResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserAllExamineCert(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetIsNewbieAnchor(ctx context.Context, req *PB.GetIsNewbieAnchorReq) (*PB.GetIsNewbieAnchorResp, protocol.ServerError) {
	resp, err := c.typedStub().GetIsNewbieAnchor(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) TestHandleYearBanUser(ctx context.Context, req *PB.TestHandleYearBanUserReq) (*PB.TestHandleYearBanUserResp, protocol.ServerError) {
	resp, err := c.typedStub().TestHandleYearBanUser(ctx, req)
	return resp, protocol.ToServerError(err)
}
