// Code generated by MockGen. DO NOT EDIT.
// Source: F:\griffin\clients\userpresent-go\iclient.go

// Package userpresent_go is a generated GoMock package.
package userpresent_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	protocol "golang.52tt.com/pkg/protocol"
	userpresent "golang.52tt.com/protocol/services/userpresent"
	userpresent_go "golang.52tt.com/protocol/services/userpresent-go"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddPresentConfig mocks base method.
func (m *MockIClient) AddPresentConfig(ctx context.Context, req *userpresent_go.AddPresentConfigReq) (*userpresent_go.AddPresentConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentConfig", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.AddPresentConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddPresentConfig indicates an expected call of AddPresentConfig.
func (mr *MockIClientMockRecorder) AddPresentConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentConfig", reflect.TypeOf((*MockIClient)(nil).AddPresentConfig), ctx, req)
}

// BatchSendPresent mocks base method.
func (m *MockIClient) BatchSendPresent(ctx context.Context, req *userpresent_go.BatchSendPresentReq) (*userpresent_go.BatchSendPresentResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSendPresent", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.BatchSendPresentResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchSendPresent indicates an expected call of BatchSendPresent.
func (mr *MockIClientMockRecorder) BatchSendPresent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSendPresent", reflect.TypeOf((*MockIClient)(nil).BatchSendPresent), ctx, req)
}

// ClearScenePresent mocks base method.
func (m *MockIClient) ClearScenePresent(ctx context.Context, req *userpresent_go.ClearScenePresentReq) (*userpresent_go.ClearScenePresentResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearScenePresent", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.ClearScenePresentResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ClearScenePresent indicates an expected call of ClearScenePresent.
func (mr *MockIClientMockRecorder) ClearScenePresent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearScenePresent", reflect.TypeOf((*MockIClient)(nil).ClearScenePresent), ctx, req)
}

// DelPresentConfig mocks base method.
func (m *MockIClient) DelPresentConfig(ctx context.Context, itemId uint32) (*userpresent_go.DelPresentConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPresentConfig", ctx, itemId)
	ret0, _ := ret[0].(*userpresent_go.DelPresentConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelPresentConfig indicates an expected call of DelPresentConfig.
func (mr *MockIClientMockRecorder) DelPresentConfig(ctx, itemId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentConfig", reflect.TypeOf((*MockIClient)(nil).DelPresentConfig), ctx, itemId)
}

// FinishSendPresentToAi mocks base method.
func (m *MockIClient) FinishSendPresentToAi(ctx context.Context, req *userpresent_go.FinishSendPresentToAiReq) (*userpresent_go.FinishSendPresentToAiResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FinishSendPresentToAi", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.FinishSendPresentToAiResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FinishSendPresentToAi indicates an expected call of FinishSendPresentToAi.
func (mr *MockIClientMockRecorder) FinishSendPresentToAi(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FinishSendPresentToAi", reflect.TypeOf((*MockIClient)(nil).FinishSendPresentToAi), ctx, req)
}

// GetLivePresentOrderList mocks base method.
func (m *MockIClient) GetLivePresentOrderList(ctx context.Context, req *userpresent_go.GetLivePresentOrderListReq) (*userpresent_go.GetLivePresentOrderListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLivePresentOrderList", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetLivePresentOrderListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivePresentOrderList indicates an expected call of GetLivePresentOrderList.
func (mr *MockIClientMockRecorder) GetLivePresentOrderList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivePresentOrderList", reflect.TypeOf((*MockIClient)(nil).GetLivePresentOrderList), ctx, req)
}

// GetOrderLogByOrderIds mocks base method.
func (m *MockIClient) GetOrderLogByOrderIds(ctx context.Context, req *userpresent_go.GetOrderLogByOrderIdsReq) (*userpresent_go.GetOrderLogByOrderIdsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderLogByOrderIds", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetOrderLogByOrderIdsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetOrderLogByOrderIds indicates an expected call of GetOrderLogByOrderIds.
func (mr *MockIClientMockRecorder) GetOrderLogByOrderIds(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderLogByOrderIds", reflect.TypeOf((*MockIClient)(nil).GetOrderLogByOrderIds), ctx, req)
}

// GetPresentConfigById mocks base method.
func (m *MockIClient) GetPresentConfigById(ctx context.Context, itemId uint32) (*userpresent_go.GetPresentConfigByIdOldResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigById", ctx, itemId)
	ret0, _ := ret[0].(*userpresent_go.GetPresentConfigByIdOldResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigById indicates an expected call of GetPresentConfigById.
func (mr *MockIClientMockRecorder) GetPresentConfigById(ctx, itemId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigById", reflect.TypeOf((*MockIClient)(nil).GetPresentConfigById), ctx, itemId)
}

// GetPresentConfigByIdList mocks base method.
func (m *MockIClient) GetPresentConfigByIdList(ctx context.Context, itemIdList []uint32) (*userpresent_go.GetPresentConfigByIdListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigByIdList", ctx, itemIdList)
	ret0, _ := ret[0].(*userpresent_go.GetPresentConfigByIdListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentConfigByIdList indicates an expected call of GetPresentConfigByIdList.
func (mr *MockIClientMockRecorder) GetPresentConfigByIdList(ctx, itemIdList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigByIdList", reflect.TypeOf((*MockIClient)(nil).GetPresentConfigByIdList), ctx, itemIdList)
}

// GetPresentConfigByIdNew mocks base method.
func (m *MockIClient) GetPresentConfigByIdNew(ctx context.Context, req *userpresent_go.GetPresentConfigByIdReq) (*userpresent_go.GetPresentConfigByIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigByIdNew", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetPresentConfigByIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentConfigByIdNew indicates an expected call of GetPresentConfigByIdNew.
func (mr *MockIClientMockRecorder) GetPresentConfigByIdNew(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigByIdNew", reflect.TypeOf((*MockIClient)(nil).GetPresentConfigByIdNew), ctx, req)
}

// GetPresentConfigList mocks base method.
func (m *MockIClient) GetPresentConfigList(ctx context.Context, req *userpresent_go.GetPresentConfigListReq) (*userpresent_go.GetPresentConfigListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigList", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetPresentConfigListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentConfigList indicates an expected call of GetPresentConfigList.
func (mr *MockIClientMockRecorder) GetPresentConfigList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigList", reflect.TypeOf((*MockIClient)(nil).GetPresentConfigList), ctx, req)
}

// GetPresentConfigListByIdListNew mocks base method.
func (m *MockIClient) GetPresentConfigListByIdListNew(ctx context.Context, req *userpresent_go.GetPresentConfigListByIdListReq) (*userpresent_go.GetPresentConfigListByIdListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigListByIdListNew", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetPresentConfigListByIdListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentConfigListByIdListNew indicates an expected call of GetPresentConfigListByIdListNew.
func (mr *MockIClientMockRecorder) GetPresentConfigListByIdListNew(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigListByIdListNew", reflect.TypeOf((*MockIClient)(nil).GetPresentConfigListByIdListNew), ctx, req)
}

// GetPresentConfigListV2ByUpdateTime mocks base method.
func (m *MockIClient) GetPresentConfigListV2ByUpdateTime(ctx context.Context, updateTime uint32) (*userpresent_go.GetPresentConfigListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigListV2ByUpdateTime", ctx, updateTime)
	ret0, _ := ret[0].(*userpresent_go.GetPresentConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigListV2ByUpdateTime indicates an expected call of GetPresentConfigListV2ByUpdateTime.
func (mr *MockIClientMockRecorder) GetPresentConfigListV2ByUpdateTime(ctx, updateTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigListV2ByUpdateTime", reflect.TypeOf((*MockIClient)(nil).GetPresentConfigListV2ByUpdateTime), ctx, updateTime)
}

// GetPresentConfigListV3 mocks base method.
func (m *MockIClient) GetPresentConfigListV3(ctx context.Context, req *userpresent_go.GetPresentConfigListV3Req) (*userpresent_go.GetPresentConfigListV3Resp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigListV3", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetPresentConfigListV3Resp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentConfigListV3 indicates an expected call of GetPresentConfigListV3.
func (mr *MockIClientMockRecorder) GetPresentConfigListV3(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigListV3", reflect.TypeOf((*MockIClient)(nil).GetPresentConfigListV3), ctx, req)
}

// GetPresentConfigUpdateTime mocks base method.
func (m *MockIClient) GetPresentConfigUpdateTime(ctx context.Context, req *userpresent_go.GetPresentConfigUpdateTimeReq) (*userpresent_go.GetPresentConfigUpdateTimeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigUpdateTime", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetPresentConfigUpdateTimeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentConfigUpdateTime indicates an expected call of GetPresentConfigUpdateTime.
func (mr *MockIClientMockRecorder) GetPresentConfigUpdateTime(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigUpdateTime", reflect.TypeOf((*MockIClient)(nil).GetPresentConfigUpdateTime), ctx, req)
}

// GetPresentDETConfigById mocks base method.
func (m *MockIClient) GetPresentDETConfigById(ctx context.Context, req *userpresent_go.GetPresentDETConfigByIdReq) (*userpresent_go.GetPresentDETConfigByIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentDETConfigById", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetPresentDETConfigByIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentDETConfigById indicates an expected call of GetPresentDETConfigById.
func (mr *MockIClientMockRecorder) GetPresentDETConfigById(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentDETConfigById", reflect.TypeOf((*MockIClient)(nil).GetPresentDETConfigById), ctx, req)
}

// GetPresentDynaminEffectTemplateConfig mocks base method.
func (m *MockIClient) GetPresentDynaminEffectTemplateConfig(ctx context.Context, req *userpresent_go.GetPresentDynamicEffectTemplateConfigReq) (*userpresent_go.GetPresentDynamicEffectTemplateConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentDynaminEffectTemplateConfig", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetPresentDynamicEffectTemplateConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentDynaminEffectTemplateConfig indicates an expected call of GetPresentDynaminEffectTemplateConfig.
func (mr *MockIClientMockRecorder) GetPresentDynaminEffectTemplateConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentDynaminEffectTemplateConfig", reflect.TypeOf((*MockIClient)(nil).GetPresentDynaminEffectTemplateConfig), ctx, req)
}

// GetPresentFlowConfigList mocks base method.
func (m *MockIClient) GetPresentFlowConfigList(ctx context.Context, req *userpresent_go.GetPresentFlowConfigListReq) (*userpresent_go.GetPresentFlowConfigListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentFlowConfigList", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetPresentFlowConfigListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentFlowConfigList indicates an expected call of GetPresentFlowConfigList.
func (mr *MockIClientMockRecorder) GetPresentFlowConfigList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigList", reflect.TypeOf((*MockIClient)(nil).GetPresentFlowConfigList), ctx, req)
}

// GetPresentFlowConfigUpdateTime mocks base method.
func (m *MockIClient) GetPresentFlowConfigUpdateTime(ctx context.Context, req *userpresent_go.GetPresentFlowConfigUpdateTimeReq) (*userpresent_go.GetPresentFlowConfigUpdateTimeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentFlowConfigUpdateTime", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetPresentFlowConfigUpdateTimeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentFlowConfigUpdateTime indicates an expected call of GetPresentFlowConfigUpdateTime.
func (mr *MockIClientMockRecorder) GetPresentFlowConfigUpdateTime(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigUpdateTime", reflect.TypeOf((*MockIClient)(nil).GetPresentFlowConfigUpdateTime), ctx, req)
}

// GetPresentMarkIconByPresentId mocks base method.
func (m *MockIClient) GetPresentMarkIconByPresentId(ctx context.Context, itemId uint32) (*userpresent_go.GetPresentMarkIconByPresentIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentMarkIconByPresentId", ctx, itemId)
	ret0, _ := ret[0].(*userpresent_go.GetPresentMarkIconByPresentIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentMarkIconByPresentId indicates an expected call of GetPresentMarkIconByPresentId.
func (mr *MockIClientMockRecorder) GetPresentMarkIconByPresentId(ctx, itemId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentMarkIconByPresentId", reflect.TypeOf((*MockIClient)(nil).GetPresentMarkIconByPresentId), ctx, itemId)
}

// GetScenePresentSummary mocks base method.
func (m *MockIClient) GetScenePresentSummary(ctx context.Context, req *userpresent_go.GetScenePresentSummaryReq) (*userpresent_go.GetScenePresentSummaryResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScenePresentSummary", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetScenePresentSummaryResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetScenePresentSummary indicates an expected call of GetScenePresentSummary.
func (mr *MockIClientMockRecorder) GetScenePresentSummary(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScenePresentSummary", reflect.TypeOf((*MockIClient)(nil).GetScenePresentSummary), ctx, req)
}

// GetUserPresentDetailListNew mocks base method.
func (m *MockIClient) GetUserPresentDetailListNew(ctx context.Context, req *userpresent_go.GetUserPresentDetailListReq) (*userpresent_go.GetUserPresentDetailListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentDetailListNew", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetUserPresentDetailListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserPresentDetailListNew indicates an expected call of GetUserPresentDetailListNew.
func (mr *MockIClientMockRecorder) GetUserPresentDetailListNew(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentDetailListNew", reflect.TypeOf((*MockIClient)(nil).GetUserPresentDetailListNew), ctx, req)
}

// GetUserPresentSendDetailList mocks base method.
func (m *MockIClient) GetUserPresentSendDetailList(ctx context.Context, req *userpresent_go.GetUserPresentSendDetailListReq) (*userpresent_go.GetUserPresentSendDetailListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentSendDetailList", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetUserPresentSendDetailListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserPresentSendDetailList indicates an expected call of GetUserPresentSendDetailList.
func (mr *MockIClientMockRecorder) GetUserPresentSendDetailList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSendDetailList", reflect.TypeOf((*MockIClient)(nil).GetUserPresentSendDetailList), ctx, req)
}

// GetUserPresentSummary mocks base method.
func (m *MockIClient) GetUserPresentSummary(ctx context.Context, req *userpresent_go.GetUserPresentSummaryReq) (*userpresent_go.GetUserPresentSummaryResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentSummary", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetUserPresentSummaryResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserPresentSummary indicates an expected call of GetUserPresentSummary.
func (mr *MockIClientMockRecorder) GetUserPresentSummary(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSummary", reflect.TypeOf((*MockIClient)(nil).GetUserPresentSummary), ctx, req)
}

// GetUserPresentSummaryByItemList mocks base method.
func (m *MockIClient) GetUserPresentSummaryByItemList(ctx context.Context, userID uint32, isSend bool, itemIdList []uint32) (*userpresent_go.GetUserPresentSummaryByItemListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentSummaryByItemList", ctx, userID, isSend, itemIdList)
	ret0, _ := ret[0].(*userpresent_go.GetUserPresentSummaryByItemListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserPresentSummaryByItemList indicates an expected call of GetUserPresentSummaryByItemList.
func (mr *MockIClientMockRecorder) GetUserPresentSummaryByItemList(ctx, userID, isSend, itemIdList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSummaryByItemList", reflect.TypeOf((*MockIClient)(nil).GetUserPresentSummaryByItemList), ctx, userID, isSend, itemIdList)
}

// GetValidNamingPresentInfos mocks base method.
func (m *MockIClient) GetValidNamingPresentInfos(ctx context.Context, req *userpresent_go.GetValidNamingPresentInfosReq) (*userpresent_go.GetValidNamingPresentInfosResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetValidNamingPresentInfos", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.GetValidNamingPresentInfosResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetValidNamingPresentInfos indicates an expected call of GetValidNamingPresentInfos.
func (mr *MockIClientMockRecorder) GetValidNamingPresentInfos(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetValidNamingPresentInfos", reflect.TypeOf((*MockIClient)(nil).GetValidNamingPresentInfos), ctx, req)
}

// LinkItemToActivity mocks base method.
func (m *MockIClient) LinkItemToActivity(ctx context.Context, req *userpresent_go.LinkItemToActivityReq) (*userpresent_go.LinkItemToActivityResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LinkItemToActivity", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.LinkItemToActivityResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// LinkItemToActivity indicates an expected call of LinkItemToActivity.
func (mr *MockIClientMockRecorder) LinkItemToActivity(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkItemToActivity", reflect.TypeOf((*MockIClient)(nil).LinkItemToActivity), ctx, req)
}

// PrepareSendPresentToAi mocks base method.
func (m *MockIClient) PrepareSendPresentToAi(ctx context.Context, req *userpresent_go.SendPresentToAiReq) (*userpresent_go.SendPresentToAiResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrepareSendPresentToAi", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.SendPresentToAiResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PrepareSendPresentToAi indicates an expected call of PrepareSendPresentToAi.
func (mr *MockIClientMockRecorder) PrepareSendPresentToAi(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrepareSendPresentToAi", reflect.TypeOf((*MockIClient)(nil).PrepareSendPresentToAi), ctx, req)
}

// RecordSceneSendPresent mocks base method.
func (m *MockIClient) RecordSceneSendPresent(ctx context.Context, req *userpresent_go.RecordSceneSendPresentReq) (*userpresent_go.RecordSceneSendPresentResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordSceneSendPresent", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.RecordSceneSendPresentResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// RecordSceneSendPresent indicates an expected call of RecordSceneSendPresent.
func (mr *MockIClientMockRecorder) RecordSceneSendPresent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordSceneSendPresent", reflect.TypeOf((*MockIClient)(nil).RecordSceneSendPresent), ctx, req)
}

// SendPresent mocks base method.
func (m *MockIClient) SendPresent(ctx context.Context, req *userpresent_go.SendPresentReq) (*userpresent_go.SendPresentResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendPresent", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.SendPresentResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SendPresent indicates an expected call of SendPresent.
func (mr *MockIClientMockRecorder) SendPresent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPresent", reflect.TypeOf((*MockIClient)(nil).SendPresent), ctx, req)
}

// SendPresentToAi mocks base method.
func (m *MockIClient) SendPresentToAi(ctx context.Context, req *userpresent_go.SendPresentToAiReq) (*userpresent_go.SendPresentToAiResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendPresentToAi", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.SendPresentToAiResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPresentToAi indicates an expected call of SendPresentToAi.
func (mr *MockIClientMockRecorder) SendPresentToAi(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPresentToAi", reflect.TypeOf((*MockIClient)(nil).SendPresentToAi), ctx, req)
}

// UpdatePresentConfig mocks base method.
func (m *MockIClient) UpdatePresentConfig(ctx context.Context, req *userpresent_go.UpdatePresentConfigReq) (*userpresent_go.UpdatePresentConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresentConfig", ctx, req)
	ret0, _ := ret[0].(*userpresent_go.UpdatePresentConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdatePresentConfig indicates an expected call of UpdatePresentConfig.
func (mr *MockIClientMockRecorder) UpdatePresentConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentConfig", reflect.TypeOf((*MockIClient)(nil).UpdatePresentConfig), ctx, req)
}

// MockIPresentClient is a mock of IPresentClient interface.
type MockIPresentClient struct {
	ctrl     *gomock.Controller
	recorder *MockIPresentClientMockRecorder
}

// MockIPresentClientMockRecorder is the mock recorder for MockIPresentClient.
type MockIPresentClientMockRecorder struct {
	mock *MockIPresentClient
}

// NewMockIPresentClient creates a new mock instance.
func NewMockIPresentClient(ctrl *gomock.Controller) *MockIPresentClient {
	mock := &MockIPresentClient{ctrl: ctrl}
	mock.recorder = &MockIPresentClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPresentClient) EXPECT() *MockIPresentClientMockRecorder {
	return m.recorder
}

// DelPresentConfig mocks base method.
func (m *MockIPresentClient) DelPresentConfig(ctx context.Context, itemId uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPresentConfig", ctx, itemId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelPresentConfig indicates an expected call of DelPresentConfig.
func (mr *MockIPresentClientMockRecorder) DelPresentConfig(ctx, itemId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentConfig", reflect.TypeOf((*MockIPresentClient)(nil).DelPresentConfig), ctx, itemId)
}

// GetPresentConfigById mocks base method.
func (m *MockIPresentClient) GetPresentConfigById(ctx context.Context, itemId uint32) (*userpresent.GetPresentConfigByIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigById", ctx, itemId)
	ret0, _ := ret[0].(*userpresent.GetPresentConfigByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigById indicates an expected call of GetPresentConfigById.
func (mr *MockIPresentClientMockRecorder) GetPresentConfigById(ctx, itemId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigById", reflect.TypeOf((*MockIPresentClient)(nil).GetPresentConfigById), ctx, itemId)
}

// GetPresentConfigByIdList mocks base method.
func (m *MockIPresentClient) GetPresentConfigByIdList(ctx context.Context, userID uint32, itemIdList []uint32, typeBitmap uint32) (*userpresent.GetPresentConfigByIdListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigByIdList", ctx, userID, itemIdList, typeBitmap)
	ret0, _ := ret[0].(*userpresent.GetPresentConfigByIdListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentConfigByIdList indicates an expected call of GetPresentConfigByIdList.
func (mr *MockIPresentClientMockRecorder) GetPresentConfigByIdList(ctx, userID, itemIdList, typeBitmap interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigByIdList", reflect.TypeOf((*MockIPresentClient)(nil).GetPresentConfigByIdList), ctx, userID, itemIdList, typeBitmap)
}

// GetPresentConfigListV2ByUpdateTime mocks base method.
func (m *MockIPresentClient) GetPresentConfigListV2ByUpdateTime(ctx context.Context, updateTime uint32) (*userpresent.GetPresentConfigListV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigListV2ByUpdateTime", ctx, updateTime)
	ret0, _ := ret[0].(*userpresent.GetPresentConfigListV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigListV2ByUpdateTime indicates an expected call of GetPresentConfigListV2ByUpdateTime.
func (mr *MockIPresentClientMockRecorder) GetPresentConfigListV2ByUpdateTime(ctx, updateTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigListV2ByUpdateTime", reflect.TypeOf((*MockIPresentClient)(nil).GetPresentConfigListV2ByUpdateTime), ctx, updateTime)
}

// GetPresentConfigUpdateTime mocks base method.
func (m *MockIPresentClient) GetPresentConfigUpdateTime(ctx context.Context, req *userpresent.GetPresentConfigUpdateTimeReq) (*userpresent.GetPresentConfigUpdateTimeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigUpdateTime", ctx, req)
	ret0, _ := ret[0].(*userpresent.GetPresentConfigUpdateTimeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentConfigUpdateTime indicates an expected call of GetPresentConfigUpdateTime.
func (mr *MockIPresentClientMockRecorder) GetPresentConfigUpdateTime(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigUpdateTime", reflect.TypeOf((*MockIPresentClient)(nil).GetPresentConfigUpdateTime), ctx, req)
}

// GetPresentDETConfigById mocks base method.
func (m *MockIPresentClient) GetPresentDETConfigById(ctx context.Context, presentId uint32) (*userpresent.GetPresentDETConfigByIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentDETConfigById", ctx, presentId)
	ret0, _ := ret[0].(*userpresent.GetPresentDETConfigByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentDETConfigById indicates an expected call of GetPresentDETConfigById.
func (mr *MockIPresentClientMockRecorder) GetPresentDETConfigById(ctx, presentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentDETConfigById", reflect.TypeOf((*MockIPresentClient)(nil).GetPresentDETConfigById), ctx, presentId)
}

// GetPresentDynaminEffectTemplateConfig mocks base method.
func (m *MockIPresentClient) GetPresentDynaminEffectTemplateConfig(ctx context.Context, req *userpresent.GetPresentDynaminEffectTemplateConfigReq) (*userpresent.GetPresentDynaminEffectTemplateConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentDynaminEffectTemplateConfig", ctx, req)
	ret0, _ := ret[0].(*userpresent.GetPresentDynaminEffectTemplateConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentDynaminEffectTemplateConfig indicates an expected call of GetPresentDynaminEffectTemplateConfig.
func (mr *MockIPresentClientMockRecorder) GetPresentDynaminEffectTemplateConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentDynaminEffectTemplateConfig", reflect.TypeOf((*MockIPresentClient)(nil).GetPresentDynaminEffectTemplateConfig), ctx, req)
}

// GetPresentFlowConfigList mocks base method.
func (m *MockIPresentClient) GetPresentFlowConfigList(ctx context.Context, req *userpresent.GetPresentFlowConfigListReq) (*userpresent.GetPresentFlowConfigListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentFlowConfigList", ctx, req)
	ret0, _ := ret[0].(*userpresent.GetPresentFlowConfigListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentFlowConfigList indicates an expected call of GetPresentFlowConfigList.
func (mr *MockIPresentClientMockRecorder) GetPresentFlowConfigList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigList", reflect.TypeOf((*MockIPresentClient)(nil).GetPresentFlowConfigList), ctx, req)
}

// GetPresentFlowConfigUpdateTime mocks base method.
func (m *MockIPresentClient) GetPresentFlowConfigUpdateTime(ctx context.Context, req *userpresent.GetPresentFlowConfigUpdateTimeReq) (*userpresent.GetPresentFlowConfigUpdateTimeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentFlowConfigUpdateTime", ctx, req)
	ret0, _ := ret[0].(*userpresent.GetPresentFlowConfigUpdateTimeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentFlowConfigUpdateTime indicates an expected call of GetPresentFlowConfigUpdateTime.
func (mr *MockIPresentClientMockRecorder) GetPresentFlowConfigUpdateTime(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigUpdateTime", reflect.TypeOf((*MockIPresentClient)(nil).GetPresentFlowConfigUpdateTime), ctx, req)
}

// GetUserPresentDetailListNew mocks base method.
func (m *MockIPresentClient) GetUserPresentDetailListNew(ctx context.Context, userID uint32) (*userpresent.GetUserPresentDetailListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentDetailListNew", ctx, userID)
	ret0, _ := ret[0].(*userpresent.GetUserPresentDetailListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserPresentDetailListNew indicates an expected call of GetUserPresentDetailListNew.
func (mr *MockIPresentClientMockRecorder) GetUserPresentDetailListNew(ctx, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentDetailListNew", reflect.TypeOf((*MockIPresentClient)(nil).GetUserPresentDetailListNew), ctx, userID)
}

// GetUserPresentSendDetailList mocks base method.
func (m *MockIPresentClient) GetUserPresentSendDetailList(ctx context.Context, userID uint32) (*userpresent.GetUserPresentSendDetailListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentSendDetailList", ctx, userID)
	ret0, _ := ret[0].(*userpresent.GetUserPresentSendDetailListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserPresentSendDetailList indicates an expected call of GetUserPresentSendDetailList.
func (mr *MockIPresentClientMockRecorder) GetUserPresentSendDetailList(ctx, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSendDetailList", reflect.TypeOf((*MockIPresentClient)(nil).GetUserPresentSendDetailList), ctx, userID)
}

// GetUserPresentSummary mocks base method.
func (m *MockIPresentClient) GetUserPresentSummary(ctx context.Context, uid uint32) (*userpresent.GetUserPresentSummaryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentSummary", ctx, uid)
	ret0, _ := ret[0].(*userpresent.GetUserPresentSummaryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSummary indicates an expected call of GetUserPresentSummary.
func (mr *MockIPresentClientMockRecorder) GetUserPresentSummary(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSummary", reflect.TypeOf((*MockIPresentClient)(nil).GetUserPresentSummary), ctx, uid)
}

// GetUserPresentSummaryByItemList mocks base method.
func (m *MockIPresentClient) GetUserPresentSummaryByItemList(ctx context.Context, userID uint32, isSend bool, itemIdList []uint32) (*userpresent.GetUserPresentSummaryByItemListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresentSummaryByItemList", ctx, userID, isSend, itemIdList)
	ret0, _ := ret[0].(*userpresent.GetUserPresentSummaryByItemListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserPresentSummaryByItemList indicates an expected call of GetUserPresentSummaryByItemList.
func (mr *MockIPresentClientMockRecorder) GetUserPresentSummaryByItemList(ctx, userID, isSend, itemIdList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSummaryByItemList", reflect.TypeOf((*MockIPresentClient)(nil).GetUserPresentSummaryByItemList), ctx, userID, isSend, itemIdList)
}

// GetValidNamingPresentInfos mocks base method.
func (m *MockIPresentClient) GetValidNamingPresentInfos(ctx context.Context, req *userpresent.GetValidNamingPresentInfosReq) (*userpresent.GetValidNamingPresentInfosResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetValidNamingPresentInfos", ctx, req)
	ret0, _ := ret[0].(*userpresent.GetValidNamingPresentInfosResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetValidNamingPresentInfos indicates an expected call of GetValidNamingPresentInfos.
func (mr *MockIPresentClientMockRecorder) GetValidNamingPresentInfos(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetValidNamingPresentInfos", reflect.TypeOf((*MockIPresentClient)(nil).GetValidNamingPresentInfos), ctx, req)
}

// IsUsePresentGo mocks base method.
func (m *MockIPresentClient) IsUsePresentGo(uid uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsUsePresentGo", uid)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsUsePresentGo indicates an expected call of IsUsePresentGo.
func (mr *MockIPresentClientMockRecorder) IsUsePresentGo(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUsePresentGo", reflect.TypeOf((*MockIPresentClient)(nil).IsUsePresentGo), uid)
}

// Load mocks base method.
func (m *MockIPresentClient) Load() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Load")
}

// Load indicates an expected call of Load.
func (mr *MockIPresentClientMockRecorder) Load() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Load", reflect.TypeOf((*MockIPresentClient)(nil).Load))
}

// SendPresent mocks base method.
func (m *MockIPresentClient) SendPresent(ctx context.Context, req *userpresent.SendPresentReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendPresent", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendPresent indicates an expected call of SendPresent.
func (mr *MockIPresentClientMockRecorder) SendPresent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPresent", reflect.TypeOf((*MockIPresentClient)(nil).SendPresent), ctx, req)
}
