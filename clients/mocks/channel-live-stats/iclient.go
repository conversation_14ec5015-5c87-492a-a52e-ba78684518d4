// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/channel-live-stats (interfaces: IClient)

// Package mocks is a generated GoMock package.
package channel_live_stats

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	terrors "gitlab.ttyuyin.com/tyr/x/terrors"
	client "golang.52tt.com/pkg/client"
	channel_live_stats "golang.52tt.com/protocol/services/channel-live-stats"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetAnchorBaseInfo mocks base method.
func (m *MockIClient) BatchGetAnchorBaseInfo(arg0 context.Context, arg1 uint32, arg2 []uint32) (*channel_live_stats.BatchGetAnchorBaseInfoResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorBaseInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_live_stats.BatchGetAnchorBaseInfoResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// BatchGetAnchorBaseInfo indicates an expected call of BatchGetAnchorBaseInfo.
func (mr *MockIClientMockRecorder) BatchGetAnchorBaseInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorBaseInfo", reflect.TypeOf((*MockIClient)(nil).BatchGetAnchorBaseInfo), arg0, arg1, arg2)
}

// BatchGetAnchorDailyRecord mocks base method.
func (m *MockIClient) BatchGetAnchorDailyRecord(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32, arg4, arg5 uint32) (*channel_live_stats.BatchGetAnchorDailyRecordResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorDailyRecord", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*channel_live_stats.BatchGetAnchorDailyRecordResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// BatchGetAnchorDailyRecord indicates an expected call of BatchGetAnchorDailyRecord.
func (mr *MockIClientMockRecorder) BatchGetAnchorDailyRecord(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorDailyRecord", reflect.TypeOf((*MockIClient)(nil).BatchGetAnchorDailyRecord), arg0, arg1, arg2, arg3, arg4, arg5)
}

// BatchGetAnchorDailyRecordV2 mocks base method.
func (m *MockIClient) BatchGetAnchorDailyRecordV2(arg0 context.Context, arg1 *channel_live_stats.BatchGetAnchorDailyRecordReq) (*channel_live_stats.BatchGetAnchorDailyRecordResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorDailyRecordV2", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_stats.BatchGetAnchorDailyRecordResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// BatchGetAnchorDailyRecordV2 indicates an expected call of BatchGetAnchorDailyRecordV2.
func (mr *MockIClientMockRecorder) BatchGetAnchorDailyRecordV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorDailyRecordV2", reflect.TypeOf((*MockIClient)(nil).BatchGetAnchorDailyRecordV2), arg0, arg1)
}

// BatchGetAnchorMonthlyStats mocks base method.
func (m *MockIClient) BatchGetAnchorMonthlyStats(arg0 context.Context, arg1 []uint32, arg2, arg3 uint32) (*channel_live_stats.BatchGetAnchorMonthlyStatsResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorMonthlyStats", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*channel_live_stats.BatchGetAnchorMonthlyStatsResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// BatchGetAnchorMonthlyStats indicates an expected call of BatchGetAnchorMonthlyStats.
func (mr *MockIClientMockRecorder) BatchGetAnchorMonthlyStats(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorMonthlyStats", reflect.TypeOf((*MockIClient)(nil).BatchGetAnchorMonthlyStats), arg0, arg1, arg2, arg3)
}

// BatchGetAnchorMonthlyStatsByUid mocks base method.
func (m *MockIClient) BatchGetAnchorMonthlyStatsByUid(arg0 context.Context, arg1 []uint32, arg2 uint32) (*channel_live_stats.BatchGetAnchorMonthlyStatsByUidResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorMonthlyStatsByUid", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_live_stats.BatchGetAnchorMonthlyStatsByUidResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// BatchGetAnchorMonthlyStatsByUid indicates an expected call of BatchGetAnchorMonthlyStatsByUid.
func (mr *MockIClientMockRecorder) BatchGetAnchorMonthlyStatsByUid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorMonthlyStatsByUid", reflect.TypeOf((*MockIClient)(nil).BatchGetAnchorMonthlyStatsByUid), arg0, arg1, arg2)
}

// BatchGetAnchorMonthlyStatsWithDate mocks base method.
func (m *MockIClient) BatchGetAnchorMonthlyStatsWithDate(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32, arg4, arg5 uint32, arg6 []uint32) (*channel_live_stats.BatchGetAnchorMonthlyStatsWithDateResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorMonthlyStatsWithDate", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(*channel_live_stats.BatchGetAnchorMonthlyStatsWithDateResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// BatchGetAnchorMonthlyStatsWithDate indicates an expected call of BatchGetAnchorMonthlyStatsWithDate.
func (mr *MockIClientMockRecorder) BatchGetAnchorMonthlyStatsWithDate(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorMonthlyStatsWithDate", reflect.TypeOf((*MockIClient)(nil).BatchGetAnchorMonthlyStatsWithDate), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// BatchGetAnchorWeeklyRecord mocks base method.
func (m *MockIClient) BatchGetAnchorWeeklyRecord(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32, arg4, arg5 uint32, arg6 []uint32) (*channel_live_stats.BatchGetAnchorWeeklyRecordResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorWeeklyRecord", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(*channel_live_stats.BatchGetAnchorWeeklyRecordResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// BatchGetAnchorWeeklyRecord indicates an expected call of BatchGetAnchorWeeklyRecord.
func (mr *MockIClientMockRecorder) BatchGetAnchorWeeklyRecord(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorWeeklyRecord", reflect.TypeOf((*MockIClient)(nil).BatchGetAnchorWeeklyRecord), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetAnchorBaseInfo mocks base method.
func (m *MockIClient) GetAnchorBaseInfo(arg0 context.Context, arg1, arg2 uint32) (*channel_live_stats.GetAnchorBaseInfoResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorBaseInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_live_stats.GetAnchorBaseInfoResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetAnchorBaseInfo indicates an expected call of GetAnchorBaseInfo.
func (mr *MockIClientMockRecorder) GetAnchorBaseInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorBaseInfo", reflect.TypeOf((*MockIClient)(nil).GetAnchorBaseInfo), arg0, arg1, arg2)
}

// GetAnchorBaseInfoList mocks base method.
func (m *MockIClient) GetAnchorBaseInfoList(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 uint32, arg6 []uint32, arg7 uint32) (*channel_live_stats.GetAnchorBaseInfoListResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorBaseInfoList", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
	ret0, _ := ret[0].(*channel_live_stats.GetAnchorBaseInfoListResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetAnchorBaseInfoList indicates an expected call of GetAnchorBaseInfoList.
func (mr *MockIClientMockRecorder) GetAnchorBaseInfoList(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorBaseInfoList", reflect.TypeOf((*MockIClient)(nil).GetAnchorBaseInfoList), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
}

// GetAnchorBaseInfoListV2 mocks base method.
func (m *MockIClient) GetAnchorBaseInfoListV2(arg0 context.Context, arg1 *channel_live_stats.GetAnchorBaseInfoListReq) (*channel_live_stats.GetAnchorBaseInfoListResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorBaseInfoListV2", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_stats.GetAnchorBaseInfoListResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetAnchorBaseInfoListV2 indicates an expected call of GetAnchorBaseInfoListV2.
func (mr *MockIClientMockRecorder) GetAnchorBaseInfoListV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorBaseInfoListV2", reflect.TypeOf((*MockIClient)(nil).GetAnchorBaseInfoListV2), arg0, arg1)
}

// GetAnchorDailyRecordWithDateList mocks base method.
func (m *MockIClient) GetAnchorDailyRecordWithDateList(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (*channel_live_stats.GetAnchorDailyRecordWithDateListResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorDailyRecordWithDateList", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*channel_live_stats.GetAnchorDailyRecordWithDateListResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetAnchorDailyRecordWithDateList indicates an expected call of GetAnchorDailyRecordWithDateList.
func (mr *MockIClientMockRecorder) GetAnchorDailyRecordWithDateList(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorDailyRecordWithDateList", reflect.TypeOf((*MockIClient)(nil).GetAnchorDailyRecordWithDateList), arg0, arg1, arg2, arg3, arg4)
}

// GetAnchorDailyStatsList mocks base method.
func (m *MockIClient) GetAnchorDailyStatsList(arg0 context.Context, arg1, arg2, arg3, arg4, arg5, arg6 uint32, arg7 []uint32) (*channel_live_stats.GetAnchorDailyStatsListResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorDailyStatsList", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
	ret0, _ := ret[0].(*channel_live_stats.GetAnchorDailyStatsListResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetAnchorDailyStatsList indicates an expected call of GetAnchorDailyStatsList.
func (mr *MockIClientMockRecorder) GetAnchorDailyStatsList(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorDailyStatsList", reflect.TypeOf((*MockIClient)(nil).GetAnchorDailyStatsList), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
}

// GetAnchorDailyStatsListV2 mocks base method.
func (m *MockIClient) GetAnchorDailyStatsListV2(arg0 context.Context, arg1 *channel_live_stats.GetAnchorDailyStatsListReq) (*channel_live_stats.GetAnchorDailyStatsListResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorDailyStatsListV2", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_stats.GetAnchorDailyStatsListResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetAnchorDailyStatsListV2 indicates an expected call of GetAnchorDailyStatsListV2.
func (mr *MockIClientMockRecorder) GetAnchorDailyStatsListV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorDailyStatsListV2", reflect.TypeOf((*MockIClient)(nil).GetAnchorDailyStatsListV2), arg0, arg1)
}

// GetAnchorMonthlyStats mocks base method.
func (m *MockIClient) GetAnchorMonthlyStats(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (*channel_live_stats.GetAnchorMonthlyStatsResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorMonthlyStats", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*channel_live_stats.GetAnchorMonthlyStatsResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetAnchorMonthlyStats indicates an expected call of GetAnchorMonthlyStats.
func (mr *MockIClientMockRecorder) GetAnchorMonthlyStats(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorMonthlyStats", reflect.TypeOf((*MockIClient)(nil).GetAnchorMonthlyStats), arg0, arg1, arg2, arg3, arg4)
}

// GetAnchorMonthlyStatsList mocks base method.
func (m *MockIClient) GetAnchorMonthlyStatsList(arg0 context.Context, arg1, arg2, arg3, arg4, arg5, arg6 uint32, arg7 []uint32) (*channel_live_stats.GetAnchorMonthlyStatsListResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorMonthlyStatsList", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
	ret0, _ := ret[0].(*channel_live_stats.GetAnchorMonthlyStatsListResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetAnchorMonthlyStatsList indicates an expected call of GetAnchorMonthlyStatsList.
func (mr *MockIClientMockRecorder) GetAnchorMonthlyStatsList(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorMonthlyStatsList", reflect.TypeOf((*MockIClient)(nil).GetAnchorMonthlyStatsList), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
}

// GetAnchorMonthlyStatsListV2 mocks base method.
func (m *MockIClient) GetAnchorMonthlyStatsListV2(arg0 context.Context, arg1 *channel_live_stats.GetAnchorMonthlyStatsListReq) (*channel_live_stats.GetAnchorMonthlyStatsListResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorMonthlyStatsListV2", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_stats.GetAnchorMonthlyStatsListResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetAnchorMonthlyStatsListV2 indicates an expected call of GetAnchorMonthlyStatsListV2.
func (mr *MockIClientMockRecorder) GetAnchorMonthlyStatsListV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorMonthlyStatsListV2", reflect.TypeOf((*MockIClient)(nil).GetAnchorMonthlyStatsListV2), arg0, arg1)
}

// GetAnchorMonthlyTotalStats mocks base method.
func (m *MockIClient) GetAnchorMonthlyTotalStats(arg0 context.Context, arg1 uint32, arg2 *channel_live_stats.GetAnchorMonthlyTotalStatsReq) (*channel_live_stats.GetAnchorMonthlyTotalStatsResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorMonthlyTotalStats", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_live_stats.GetAnchorMonthlyTotalStatsResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetAnchorMonthlyTotalStats indicates an expected call of GetAnchorMonthlyTotalStats.
func (mr *MockIClientMockRecorder) GetAnchorMonthlyTotalStats(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorMonthlyTotalStats", reflect.TypeOf((*MockIClient)(nil).GetAnchorMonthlyTotalStats), arg0, arg1, arg2)
}

// GetAnchorTotalStatsBetweenDate mocks base method.
func (m *MockIClient) GetAnchorTotalStatsBetweenDate(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 uint32) (*channel_live_stats.GetAnchorTotalStatsBetweenDateResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorTotalStatsBetweenDate", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*channel_live_stats.GetAnchorTotalStatsBetweenDateResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetAnchorTotalStatsBetweenDate indicates an expected call of GetAnchorTotalStatsBetweenDate.
func (mr *MockIClientMockRecorder) GetAnchorTotalStatsBetweenDate(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorTotalStatsBetweenDate", reflect.TypeOf((*MockIClient)(nil).GetAnchorTotalStatsBetweenDate), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetAnchorWeeklyStatsList mocks base method.
func (m *MockIClient) GetAnchorWeeklyStatsList(arg0 context.Context, arg1, arg2, arg3, arg4, arg5, arg6 uint32, arg7 []uint32) (*channel_live_stats.GetAnchorWeeklyStatsListResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorWeeklyStatsList", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
	ret0, _ := ret[0].(*channel_live_stats.GetAnchorWeeklyStatsListResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetAnchorWeeklyStatsList indicates an expected call of GetAnchorWeeklyStatsList.
func (mr *MockIClientMockRecorder) GetAnchorWeeklyStatsList(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorWeeklyStatsList", reflect.TypeOf((*MockIClient)(nil).GetAnchorWeeklyStatsList), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
}

// GetAnchorWeeklyStatsListV2 mocks base method.
func (m *MockIClient) GetAnchorWeeklyStatsListV2(arg0 context.Context, arg1 *channel_live_stats.GetAnchorWeeklyStatsListReq) (*channel_live_stats.GetAnchorWeeklyStatsListResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorWeeklyStatsListV2", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_stats.GetAnchorWeeklyStatsListResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetAnchorWeeklyStatsListV2 indicates an expected call of GetAnchorWeeklyStatsListV2.
func (mr *MockIClientMockRecorder) GetAnchorWeeklyStatsListV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorWeeklyStatsListV2", reflect.TypeOf((*MockIClient)(nil).GetAnchorWeeklyStatsListV2), arg0, arg1)
}

// GetGuildAnchorBaseInfo mocks base method.
func (m *MockIClient) GetGuildAnchorBaseInfo(arg0 context.Context, arg1, arg2, arg3 uint32) (*channel_live_stats.GetGuildAnchorBaseInfoResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildAnchorBaseInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*channel_live_stats.GetGuildAnchorBaseInfoResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetGuildAnchorBaseInfo indicates an expected call of GetGuildAnchorBaseInfo.
func (mr *MockIClientMockRecorder) GetGuildAnchorBaseInfo(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAnchorBaseInfo", reflect.TypeOf((*MockIClient)(nil).GetGuildAnchorBaseInfo), arg0, arg1, arg2, arg3)
}

// GetGuildAnchorMonthlyStatsList mocks base method.
func (m *MockIClient) GetGuildAnchorMonthlyStatsList(arg0 context.Context, arg1, arg2, arg3, arg4, arg5, arg6 uint32) (*channel_live_stats.GetGuildAnchorMonthlyStatsListResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildAnchorMonthlyStatsList", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(*channel_live_stats.GetGuildAnchorMonthlyStatsListResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetGuildAnchorMonthlyStatsList indicates an expected call of GetGuildAnchorMonthlyStatsList.
func (mr *MockIClientMockRecorder) GetGuildAnchorMonthlyStatsList(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAnchorMonthlyStatsList", reflect.TypeOf((*MockIClient)(nil).GetGuildAnchorMonthlyStatsList), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// GetGuildAnchorMonthlyStatsListWithAgent mocks base method.
func (m *MockIClient) GetGuildAnchorMonthlyStatsListWithAgent(arg0 context.Context, arg1, arg2, arg3, arg4, arg5, arg6, arg7 uint32, arg8, arg9 []uint32) (*channel_live_stats.GetGuildAnchorMonthlyStatsListResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildAnchorMonthlyStatsListWithAgent", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9)
	ret0, _ := ret[0].(*channel_live_stats.GetGuildAnchorMonthlyStatsListResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetGuildAnchorMonthlyStatsListWithAgent indicates an expected call of GetGuildAnchorMonthlyStatsListWithAgent.
func (mr *MockIClientMockRecorder) GetGuildAnchorMonthlyStatsListWithAgent(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAnchorMonthlyStatsListWithAgent", reflect.TypeOf((*MockIClient)(nil).GetGuildAnchorMonthlyStatsListWithAgent), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9)
}

// GetGuildDailyAnchorList mocks base method.
func (m *MockIClient) GetGuildDailyAnchorList(arg0 context.Context, arg1 uint32, arg2 *channel_live_stats.GetGuildDailyAnchorListReq) (*channel_live_stats.GetGuildDailyAnchorListResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildDailyAnchorList", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_live_stats.GetGuildDailyAnchorListResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetGuildDailyAnchorList indicates an expected call of GetGuildDailyAnchorList.
func (mr *MockIClientMockRecorder) GetGuildDailyAnchorList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildDailyAnchorList", reflect.TypeOf((*MockIClient)(nil).GetGuildDailyAnchorList), arg0, arg1, arg2)
}

// GetGuildDailyStatsList mocks base method.
func (m *MockIClient) GetGuildDailyStatsList(arg0 context.Context, arg1 uint32, arg2 *channel_live_stats.GetGuildDailyStatsListReq) (*channel_live_stats.GetGuildDailyStatsListResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildDailyStatsList", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_live_stats.GetGuildDailyStatsListResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetGuildDailyStatsList indicates an expected call of GetGuildDailyStatsList.
func (mr *MockIClientMockRecorder) GetGuildDailyStatsList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildDailyStatsList", reflect.TypeOf((*MockIClient)(nil).GetGuildDailyStatsList), arg0, arg1, arg2)
}

// GetGuildDailyTotalStats mocks base method.
func (m *MockIClient) GetGuildDailyTotalStats(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (*channel_live_stats.GetGuildDailyTotalStatsResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildDailyTotalStats", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*channel_live_stats.GetGuildDailyTotalStatsResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetGuildDailyTotalStats indicates an expected call of GetGuildDailyTotalStats.
func (mr *MockIClientMockRecorder) GetGuildDailyTotalStats(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildDailyTotalStats", reflect.TypeOf((*MockIClient)(nil).GetGuildDailyTotalStats), arg0, arg1, arg2, arg3, arg4)
}

// GetGuildMonthlyStats mocks base method.
func (m *MockIClient) GetGuildMonthlyStats(arg0 context.Context, arg1, arg2, arg3 uint32) (*channel_live_stats.GetGuildMonthlyStatsResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildMonthlyStats", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*channel_live_stats.GetGuildMonthlyStatsResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetGuildMonthlyStats indicates an expected call of GetGuildMonthlyStats.
func (mr *MockIClientMockRecorder) GetGuildMonthlyStats(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildMonthlyStats", reflect.TypeOf((*MockIClient)(nil).GetGuildMonthlyStats), arg0, arg1, arg2, arg3)
}

// GetGuildWeeklyAnchorList mocks base method.
func (m *MockIClient) GetGuildWeeklyAnchorList(arg0 context.Context, arg1 uint32, arg2 *channel_live_stats.GetGuildWeeklyAnchorListReq) (*channel_live_stats.GetGuildWeeklyAnchorListResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildWeeklyAnchorList", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_live_stats.GetGuildWeeklyAnchorListResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetGuildWeeklyAnchorList indicates an expected call of GetGuildWeeklyAnchorList.
func (mr *MockIClientMockRecorder) GetGuildWeeklyAnchorList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildWeeklyAnchorList", reflect.TypeOf((*MockIClient)(nil).GetGuildWeeklyAnchorList), arg0, arg1, arg2)
}

// GetMonthBusinessAnalysis mocks base method.
func (m *MockIClient) GetMonthBusinessAnalysis(arg0 context.Context, arg1 *channel_live_stats.GetMonthBusinessAnalysisReq) (*channel_live_stats.GetMonthBusinessAnalysisResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMonthBusinessAnalysis", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_stats.GetMonthBusinessAnalysisResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetMonthBusinessAnalysis indicates an expected call of GetMonthBusinessAnalysis.
func (mr *MockIClientMockRecorder) GetMonthBusinessAnalysis(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMonthBusinessAnalysis", reflect.TypeOf((*MockIClient)(nil).GetMonthBusinessAnalysis), arg0, arg1)
}

// GetWeekBusinessAnalysis mocks base method.
func (m *MockIClient) GetWeekBusinessAnalysis(arg0 context.Context, arg1 *channel_live_stats.GetWeekBusinessAnalysisReq) (*channel_live_stats.GetWeekBusinessAnalysisResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeekBusinessAnalysis", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_stats.GetWeekBusinessAnalysisResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetWeekBusinessAnalysis indicates an expected call of GetWeekBusinessAnalysis.
func (mr *MockIClientMockRecorder) GetWeekBusinessAnalysis(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeekBusinessAnalysis", reflect.TypeOf((*MockIClient)(nil).GetWeekBusinessAnalysis), arg0, arg1)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// TriggerTimer mocks base method.
func (m *MockIClient) TriggerTimer(arg0 context.Context, arg1 *channel_live_stats.TriggerTimerReq) (*channel_live_stats.TriggerTimerResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TriggerTimer", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_stats.TriggerTimerResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// TriggerTimer indicates an expected call of TriggerTimer.
func (mr *MockIClientMockRecorder) TriggerTimer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerTimer", reflect.TypeOf((*MockIClient)(nil).TriggerTimer), arg0, arg1)
}

// UpdateGuildAnchorAgentInfo mocks base method.
func (m *MockIClient) UpdateGuildAnchorAgentInfo(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) (*channel_live_stats.UpdateGuildAnchorAgentInfoResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGuildAnchorAgentInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*channel_live_stats.UpdateGuildAnchorAgentInfoResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// UpdateGuildAnchorAgentInfo indicates an expected call of UpdateGuildAnchorAgentInfo.
func (mr *MockIClientMockRecorder) UpdateGuildAnchorAgentInfo(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGuildAnchorAgentInfo", reflect.TypeOf((*MockIClient)(nil).UpdateGuildAnchorAgentInfo), arg0, arg1, arg2, arg3)
}

// UpdateGuildOperationalCapabilities mocks base method.
func (m *MockIClient) UpdateGuildOperationalCapabilities(arg0 context.Context, arg1 *channel_live_stats.UpdateGuildOperationalCapabilitiesReq) (*channel_live_stats.UpdateGuildOperationalCapabilitiesResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGuildOperationalCapabilities", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_stats.UpdateGuildOperationalCapabilitiesResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// UpdateGuildOperationalCapabilities indicates an expected call of UpdateGuildOperationalCapabilities.
func (mr *MockIClientMockRecorder) UpdateGuildOperationalCapabilities(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGuildOperationalCapabilities", reflect.TypeOf((*MockIClient)(nil).UpdateGuildOperationalCapabilities), arg0, arg1)
}
