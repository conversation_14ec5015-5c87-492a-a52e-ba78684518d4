// Code generated by MockGen. DO NOT EDIT.
// Source: F:\griffin\clients\numeric-go\iclient.go

// Package numeric_go is a generated GoMock package.
package numeric_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	numeric_go "golang.52tt.com/protocol/services/numeric-go"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddUserNumeric mocks base method.
func (m *MockIClient) AddUserNumeric(ctx context.Context, req *numeric_go.AddUserNumericReq) (*numeric_go.AddUserNumericResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserNumeric", ctx, req)
	ret0, _ := ret[0].(*numeric_go.AddUserNumericResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddUserNumeric indicates an expected call of AddUserNumeric.
func (mr *MockIClientMockRecorder) AddUserNumeric(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserNumeric", reflect.TypeOf((*MockIClient)(nil).AddUserNumeric), ctx, req)
}

// AddUserNumericV2 mocks base method.
func (m *MockIClient) AddUserNumericV2(ctx context.Context, req *numeric_go.AddUserNumericV2Req) (*numeric_go.AddUserNumericV2Resp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserNumericV2", ctx, req)
	ret0, _ := ret[0].(*numeric_go.AddUserNumericV2Resp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddUserNumericV2 indicates an expected call of AddUserNumericV2.
func (mr *MockIClientMockRecorder) AddUserNumericV2(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserNumericV2", reflect.TypeOf((*MockIClient)(nil).AddUserNumericV2), ctx, req)
}

// BatchGetPersonalNumeric mocks base method.
func (m *MockIClient) BatchGetPersonalNumeric(ctx context.Context, uidList []uint32) (*numeric_go.BatchGetPersonalNumericResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPersonalNumeric", ctx, uidList)
	ret0, _ := ret[0].(*numeric_go.BatchGetPersonalNumericResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetPersonalNumeric indicates an expected call of BatchGetPersonalNumeric.
func (mr *MockIClientMockRecorder) BatchGetPersonalNumeric(ctx, uidList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPersonalNumeric", reflect.TypeOf((*MockIClient)(nil).BatchGetPersonalNumeric), ctx, uidList)
}

// BatchGetUserNumericLock mocks base method.
func (m *MockIClient) BatchGetUserNumericLock(ctx context.Context, req *numeric_go.BatchGetUserNumericLockReq) (*numeric_go.BatchGetUserNumericLockResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserNumericLock", ctx, req)
	ret0, _ := ret[0].(*numeric_go.BatchGetUserNumericLockResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserNumericLock indicates an expected call of BatchGetUserNumericLock.
func (mr *MockIClientMockRecorder) BatchGetUserNumericLock(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserNumericLock", reflect.TypeOf((*MockIClient)(nil).BatchGetUserNumericLock), ctx, req)
}

// BatchRecordSendGiftEvent mocks base method.
func (m *MockIClient) BatchRecordSendGiftEvent(ctx context.Context, req *numeric_go.BatchRecordSendGiftEventReq) (*numeric_go.BatchRecordSendGiftEventResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchRecordSendGiftEvent", ctx, req)
	ret0, _ := ret[0].(*numeric_go.BatchRecordSendGiftEventResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchRecordSendGiftEvent indicates an expected call of BatchRecordSendGiftEvent.
func (mr *MockIClientMockRecorder) BatchRecordSendGiftEvent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRecordSendGiftEvent", reflect.TypeOf((*MockIClient)(nil).BatchRecordSendGiftEvent), ctx, req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetPersonalNumericV2 mocks base method.
func (m *MockIClient) GetPersonalNumericV2(ctx context.Context, uid uint32) (*numeric_go.GetPersonalNumericV2Resp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalNumericV2", ctx, uid)
	ret0, _ := ret[0].(*numeric_go.GetPersonalNumericV2Resp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPersonalNumericV2 indicates an expected call of GetPersonalNumericV2.
func (mr *MockIClientMockRecorder) GetPersonalNumericV2(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalNumericV2", reflect.TypeOf((*MockIClient)(nil).GetPersonalNumericV2), ctx, uid)
}

// GetUserNumericLock mocks base method.
func (m *MockIClient) GetUserNumericLock(ctx context.Context, uid uint32) (*numeric_go.GetUserNumericLockResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserNumericLock", ctx, uid)
	ret0, _ := ret[0].(*numeric_go.GetUserNumericLockResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserNumericLock indicates an expected call of GetUserNumericLock.
func (mr *MockIClientMockRecorder) GetUserNumericLock(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserNumericLock", reflect.TypeOf((*MockIClient)(nil).GetUserNumericLock), ctx, uid)
}

// GetUserRichSwitch mocks base method.
func (m *MockIClient) GetUserRichSwitch(ctx context.Context, uid uint32) (*numeric_go.GetUserRichSwitchResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRichSwitch", ctx, uid)
	ret0, _ := ret[0].(*numeric_go.GetUserRichSwitchResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserRichSwitch indicates an expected call of GetUserRichSwitch.
func (mr *MockIClientMockRecorder) GetUserRichSwitch(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRichSwitch", reflect.TypeOf((*MockIClient)(nil).GetUserRichSwitch), ctx, uid)
}

// RecordSendGiftEvent mocks base method.
func (m *MockIClient) RecordSendGiftEvent(ctx context.Context, req *numeric_go.RecordSendGiftEventReq) (*numeric_go.RecordSendGiftEventResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordSendGiftEvent", ctx, req)
	ret0, _ := ret[0].(*numeric_go.RecordSendGiftEventResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// RecordSendGiftEvent indicates an expected call of RecordSendGiftEvent.
func (mr *MockIClientMockRecorder) RecordSendGiftEvent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordSendGiftEvent", reflect.TypeOf((*MockIClient)(nil).RecordSendGiftEvent), ctx, req)
}

// SetUserNumericLock mocks base method.
func (m *MockIClient) SetUserNumericLock(ctx context.Context, req *numeric_go.SetUserNumericLockReq) (*numeric_go.SetUserNumericLockResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserNumericLock", ctx, req)
	ret0, _ := ret[0].(*numeric_go.SetUserNumericLockResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetUserNumericLock indicates an expected call of SetUserNumericLock.
func (mr *MockIClientMockRecorder) SetUserNumericLock(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserNumericLock", reflect.TypeOf((*MockIClient)(nil).SetUserNumericLock), ctx, req)
}

// SetUserRichSwitch mocks base method.
func (m *MockIClient) SetUserRichSwitch(ctx context.Context, uid uint32, enable bool) (*numeric_go.SetUserRichSwitchResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserRichSwitch", ctx, uid, enable)
	ret0, _ := ret[0].(*numeric_go.SetUserRichSwitchResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetUserRichSwitch indicates an expected call of SetUserRichSwitch.
func (mr *MockIClientMockRecorder) SetUserRichSwitch(ctx, uid, enable interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserRichSwitch", reflect.TypeOf((*MockIClient)(nil).SetUserRichSwitch), ctx, uid, enable)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UseRichCard mocks base method.
func (m *MockIClient) UseRichCard(ctx context.Context, req *numeric_go.UseRichCardReq) (*numeric_go.UseRichCardResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UseRichCard", ctx, req)
	ret0, _ := ret[0].(*numeric_go.UseRichCardResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UseRichCard indicates an expected call of UseRichCard.
func (mr *MockIClientMockRecorder) UseRichCard(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UseRichCard", reflect.TypeOf((*MockIClient)(nil).UseRichCard), ctx, req)
}
