// Code generated by MockGen. DO NOT EDIT.
// Source: /root/quicksilver/clients/anchor-level/iclient.go

// Package anchor_level is a generated GoMock package.
package anchor_level

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	AnchorLevel "golang.52tt.com/protocol/services/anchor-level"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

func (m *MockIClient) ReplenishAnchorLevel(ctx context.Context, req *AnchorLevel.ReplenishAnchorLevelReq) (*AnchorLevel.ReplenishAnchorLevelResp, error) {
    //TODO implement me
    panic("implement me")
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetAnchorLevel mocks base method.
func (m *MockIClient) GetAnchorLevel(ctx context.Context, uid uint32) (AnchorLevel.ANCHOR_LEVEL_TYPE, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorLevel", ctx, uid)
	ret0, _ := ret[0].(AnchorLevel.ANCHOR_LEVEL_TYPE)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorLevel indicates an expected call of GetAnchorLevel.
func (mr *MockIClientMockRecorder) GetAnchorLevel(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorLevel", reflect.TypeOf((*MockIClient)(nil).GetAnchorLevel), ctx, uid)
}

// GetAnchorLevelMonthTask mocks base method.
func (m *MockIClient) GetAnchorLevelMonthTask(ctx context.Context, uid uint32) (*AnchorLevel.GetAnchorLevelMonthTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorLevelMonthTask", ctx, uid)
	ret0, _ := ret[0].(*AnchorLevel.GetAnchorLevelMonthTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorLevelMonthTask indicates an expected call of GetAnchorLevelMonthTask.
func (mr *MockIClientMockRecorder) GetAnchorLevelMonthTask(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorLevelMonthTask", reflect.TypeOf((*MockIClient)(nil).GetAnchorLevelMonthTask), ctx, uid)
}

// GetAnchorLevelNewTask mocks base method.
func (m *MockIClient) GetAnchorLevelNewTask(ctx context.Context, uid uint32) (*AnchorLevel.AnchorLevelNewTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorLevelNewTask", ctx, uid)
	ret0, _ := ret[0].(*AnchorLevel.AnchorLevelNewTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorLevelNewTask indicates an expected call of GetAnchorLevelNewTask.
func (mr *MockIClientMockRecorder) GetAnchorLevelNewTask(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorLevelNewTask", reflect.TypeOf((*MockIClient)(nil).GetAnchorLevelNewTask), ctx, uid)
}

// GetAnchorMicPosTask mocks base method.
func (m *MockIClient) GetAnchorMicPosTask(ctx context.Context, uid uint32) (*AnchorLevel.AnchorMicPosTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorMicPosTask", ctx, uid)
	ret0, _ := ret[0].(*AnchorLevel.AnchorMicPosTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorMicPosTask indicates an expected call of GetAnchorMicPosTask.
func (mr *MockIClientMockRecorder) GetAnchorMicPosTask(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorMicPosTask", reflect.TypeOf((*MockIClient)(nil).GetAnchorMicPosTask), ctx, uid)
}

// GetLiveAnchorLevel mocks base method.
func (m *MockIClient) GetLiveAnchorLevel(ctx context.Context, level AnchorLevel.ANCHOR_LEVEL_TYPE) (*AnchorLevel.GetLiveAnchorLevelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiveAnchorLevel", ctx, level)
	ret0, _ := ret[0].(*AnchorLevel.GetLiveAnchorLevelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLiveAnchorLevel indicates an expected call of GetLiveAnchorLevel.
func (mr *MockIClientMockRecorder) GetLiveAnchorLevel(ctx, level interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveAnchorLevel", reflect.TypeOf((*MockIClient)(nil).GetLiveAnchorLevel), ctx, level)
}

// GetLiveAnchorLevelByUid mocks base method.
func (m *MockIClient) GetLiveAnchorLevelByUid(ctx context.Context, uids []uint32) (map[uint32]*AnchorLevel.LiveAnchorLevelInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiveAnchorLevelByUid", ctx, uids)
	ret0, _ := ret[0].(map[uint32]*AnchorLevel.LiveAnchorLevelInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLiveAnchorLevelByUid indicates an expected call of GetLiveAnchorLevelByUid.
func (mr *MockIClientMockRecorder) GetLiveAnchorLevelByUid(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveAnchorLevelByUid", reflect.TypeOf((*MockIClient)(nil).GetLiveAnchorLevelByUid), ctx, uids)
}

// GetLiveAnchorTaskEntry mocks base method.
func (m *MockIClient) GetLiveAnchorTaskEntry(ctx context.Context, uid, channelId uint32) (*AnchorLevel.GetLiveAnchorTaskEntryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiveAnchorTaskEntry", ctx, uid, channelId)
	ret0, _ := ret[0].(*AnchorLevel.GetLiveAnchorTaskEntryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLiveAnchorTaskEntry indicates an expected call of GetLiveAnchorTaskEntry.
func (mr *MockIClientMockRecorder) GetLiveAnchorTaskEntry(ctx, uid, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveAnchorTaskEntry", reflect.TypeOf((*MockIClient)(nil).GetLiveAnchorTaskEntry), ctx, uid, channelId)
}

// SetAnchorCheckPass mocks base method.
func (m *MockIClient) SetAnchorCheckPass(ctx context.Context, uid uint32, checkLevel string) (*AnchorLevel.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAnchorCheckPass", ctx, uid, checkLevel)
	ret0, _ := ret[0].(*AnchorLevel.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAnchorCheckPass indicates an expected call of SetAnchorCheckPass.
func (mr *MockIClientMockRecorder) SetAnchorCheckPass(ctx, uid, checkLevel interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAnchorCheckPass", reflect.TypeOf((*MockIClient)(nil).SetAnchorCheckPass), ctx, uid, checkLevel)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
