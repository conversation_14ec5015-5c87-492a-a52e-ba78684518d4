// Code generated by MockGen. DO NOT EDIT.
// Source: ../../channel-live-mgr/iclient.go

// Package channellivemgr is a generated GoMock package.
package channellivemgr

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	apicentergo "golang.52tt.com/protocol/services/apicentergo"
	channellivemgr "golang.52tt.com/protocol/services/channellivemgr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AcceptAppointPk mocks base method.
func (m *MockIClient) AcceptAppointPk(ctx context.Context, in *channellivemgr.AcceptAppointPkReq) (*channellivemgr.AcceptAppointPkResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcceptAppointPk", ctx, in)
	ret0, _ := ret[0].(*channellivemgr.AcceptAppointPkResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AcceptAppointPk indicates an expected call of AcceptAppointPk.
func (mr *MockIClientMockRecorder) AcceptAppointPk(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptAppointPk", reflect.TypeOf((*MockIClient)(nil).AcceptAppointPk), ctx, in)
}

// AddAppointPkInfo mocks base method.
func (m *MockIClient) AddAppointPkInfo(ctx context.Context, in *channellivemgr.AddAppointPkInfoReq) (*channellivemgr.AddAppointPkInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAppointPkInfo", ctx, in)
	ret0, _ := ret[0].(*channellivemgr.AddAppointPkInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddAppointPkInfo indicates an expected call of AddAppointPkInfo.
func (mr *MockIClientMockRecorder) AddAppointPkInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAppointPkInfo", reflect.TypeOf((*MockIClient)(nil).AddAppointPkInfo), ctx, in)
}

// AddChannelLiveAnchorScore mocks base method.
func (m *MockIClient) AddChannelLiveAnchorScore(ctx context.Context, req channellivemgr.AddChannelLiveAnchorScoreReq) (*channellivemgr.AddChannelLiveAnchorScoreResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChannelLiveAnchorScore", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.AddChannelLiveAnchorScoreResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddChannelLiveAnchorScore indicates an expected call of AddChannelLiveAnchorScore.
func (mr *MockIClientMockRecorder) AddChannelLiveAnchorScore(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelLiveAnchorScore", reflect.TypeOf((*MockIClient)(nil).AddChannelLiveAnchorScore), ctx, req)
}

// AddVirtualAnchorPer mocks base method.
func (m *MockIClient) AddVirtualAnchorPer(ctx context.Context, req *channellivemgr.AddVirtualAnchorPerReq) (*channellivemgr.AddVirtualAnchorPerResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddVirtualAnchorPer", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.AddVirtualAnchorPerResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddVirtualAnchorPer indicates an expected call of AddVirtualAnchorPer.
func (mr *MockIClientMockRecorder) AddVirtualAnchorPer(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddVirtualAnchorPer", reflect.TypeOf((*MockIClient)(nil).AddVirtualAnchorPer), ctx, req)
}

// ApplyPk mocks base method.
func (m *MockIClient) ApplyPk(ctx context.Context, req *channellivemgr.ApplyPkReq) (*channellivemgr.ApplyPkResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyPk", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.ApplyPkResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ApplyPk indicates an expected call of ApplyPk.
func (mr *MockIClientMockRecorder) ApplyPk(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyPk", reflect.TypeOf((*MockIClient)(nil).ApplyPk), ctx, req)
}

// BatGetChannelLiveInfo mocks base method.
func (m *MockIClient) BatGetChannelLiveInfo(ctx context.Context, req *channellivemgr.BatGetChannelLiveInfoReq) (*channellivemgr.BatGetChannelLiveInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetChannelLiveInfo", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.BatGetChannelLiveInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatGetChannelLiveInfo indicates an expected call of BatGetChannelLiveInfo.
func (mr *MockIClientMockRecorder) BatGetChannelLiveInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetChannelLiveInfo", reflect.TypeOf((*MockIClient)(nil).BatGetChannelLiveInfo), ctx, req)
}

// BatchGetAllChannelLive mocks base method.
func (m *MockIClient) BatchGetAllChannelLive(ctx context.Context, req *channellivemgr.BatchGetAllChannelLiveReq) (*channellivemgr.BatchGetAllChannelLiveResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAllChannelLive", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.BatchGetAllChannelLiveResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetAllChannelLive indicates an expected call of BatchGetAllChannelLive.
func (mr *MockIClientMockRecorder) BatchGetAllChannelLive(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAllChannelLive", reflect.TypeOf((*MockIClient)(nil).BatchGetAllChannelLive), ctx, req)
}

// BatchGetAnchorTotalData mocks base method.
func (m *MockIClient) BatchGetAnchorTotalData(ctx context.Context, uidList []uint32) (*channellivemgr.BatchGetAnchorTotalDataResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorTotalData", ctx, uidList)
	ret0, _ := ret[0].(*channellivemgr.BatchGetAnchorTotalDataResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetAnchorTotalData indicates an expected call of BatchGetAnchorTotalData.
func (mr *MockIClientMockRecorder) BatchGetAnchorTotalData(ctx, uidList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorTotalData", reflect.TypeOf((*MockIClient)(nil).BatchGetAnchorTotalData), ctx, uidList)
}

// BatchGetChannelLiveRecord mocks base method.
func (m *MockIClient) BatchGetChannelLiveRecord(ctx context.Context, req channellivemgr.BatchGetChannelLiveRecordReq) (*channellivemgr.BatchGetChannelLiveRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelLiveRecord", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.BatchGetChannelLiveRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetChannelLiveRecord indicates an expected call of BatchGetChannelLiveRecord.
func (mr *MockIClientMockRecorder) BatchGetChannelLiveRecord(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelLiveRecord", reflect.TypeOf((*MockIClient)(nil).BatchGetChannelLiveRecord), ctx, req)
}

// BatchGetChannelLiveStatus mocks base method.
func (m *MockIClient) BatchGetChannelLiveStatus(ctx context.Context, req channellivemgr.BatchGetChannelLiveStatusReq) (*channellivemgr.BatchGetChannelLiveStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelLiveStatus", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.BatchGetChannelLiveStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetChannelLiveStatus indicates an expected call of BatchGetChannelLiveStatus.
func (mr *MockIClientMockRecorder) BatchGetChannelLiveStatus(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelLiveStatus", reflect.TypeOf((*MockIClient)(nil).BatchGetChannelLiveStatus), ctx, req)
}

// BatchGetChannelLiveStatusSimple mocks base method.
func (m *MockIClient) BatchGetChannelLiveStatusSimple(ctx context.Context, req channellivemgr.BatchGetChannelLiveStatusSimpleReq) (*channellivemgr.BatchGetChannelLiveStatusSimpleResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelLiveStatusSimple", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.BatchGetChannelLiveStatusSimpleResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetChannelLiveStatusSimple indicates an expected call of BatchGetChannelLiveStatusSimple.
func (mr *MockIClientMockRecorder) BatchGetChannelLiveStatusSimple(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelLiveStatusSimple", reflect.TypeOf((*MockIClient)(nil).BatchGetChannelLiveStatusSimple), ctx, req)
}

// BatchGetChannelLiveTotalData mocks base method.
func (m *MockIClient) BatchGetChannelLiveTotalData(ctx context.Context, req *channellivemgr.BatchGetChannelLiveTotalDataReq) (*channellivemgr.BatchGetChannelLiveTotalDataResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelLiveTotalData", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.BatchGetChannelLiveTotalDataResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetChannelLiveTotalData indicates an expected call of BatchGetChannelLiveTotalData.
func (mr *MockIClientMockRecorder) BatchGetChannelLiveTotalData(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelLiveTotalData", reflect.TypeOf((*MockIClient)(nil).BatchGetChannelLiveTotalData), ctx, req)
}

// BatchGetGroupFansGiftValue mocks base method.
func (m *MockIClient) BatchGetGroupFansGiftValue(ctx context.Context, req *channellivemgr.BatchGetGroupFansGiftValueReq) (*channellivemgr.BatchGetGroupFansGiftValueResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGroupFansGiftValue", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.BatchGetGroupFansGiftValueResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetGroupFansGiftValue indicates an expected call of BatchGetGroupFansGiftValue.
func (mr *MockIClientMockRecorder) BatchGetGroupFansGiftValue(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGroupFansGiftValue", reflect.TypeOf((*MockIClient)(nil).BatchGetGroupFansGiftValue), ctx, req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CancelPKApply mocks base method.
func (m *MockIClient) CancelPKApply(ctx context.Context, req channellivemgr.CancelPKApplyReq) (*channellivemgr.CancelPKApplyResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelPKApply", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.CancelPKApplyResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CancelPKApply indicates an expected call of CancelPKApply.
func (mr *MockIClientMockRecorder) CancelPKApply(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelPKApply", reflect.TypeOf((*MockIClient)(nil).CancelPKApply), ctx, req)
}

// CancelPkMatch mocks base method.
func (m *MockIClient) CancelPkMatch(ctx context.Context, uid, channelId uint32) (*channellivemgr.CancelPkMatchResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelPkMatch", ctx, uid, channelId)
	ret0, _ := ret[0].(*channellivemgr.CancelPkMatchResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CancelPkMatch indicates an expected call of CancelPkMatch.
func (mr *MockIClientMockRecorder) CancelPkMatch(ctx, uid, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelPkMatch", reflect.TypeOf((*MockIClient)(nil).CancelPkMatch), ctx, uid, channelId)
}

// ChannelLiveHeartbeat mocks base method.
func (m *MockIClient) ChannelLiveHeartbeat(ctx context.Context, req channellivemgr.ChannelLiveHeartbeatReq) (*channellivemgr.ChannelLiveHeartbeatResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelLiveHeartbeat", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.ChannelLiveHeartbeatResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ChannelLiveHeartbeat indicates an expected call of ChannelLiveHeartbeat.
func (mr *MockIClientMockRecorder) ChannelLiveHeartbeat(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelLiveHeartbeat", reflect.TypeOf((*MockIClient)(nil).ChannelLiveHeartbeat), ctx, req)
}

// CheckHasVirtualAnchorPer mocks base method.
func (m *MockIClient) CheckHasVirtualAnchorPer(ctx context.Context, req *channellivemgr.CheckHasVirtualAnchorPerReq) (*channellivemgr.CheckHasVirtualAnchorPerResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckHasVirtualAnchorPer", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.CheckHasVirtualAnchorPerResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckHasVirtualAnchorPer indicates an expected call of CheckHasVirtualAnchorPer.
func (mr *MockIClientMockRecorder) CheckHasVirtualAnchorPer(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHasVirtualAnchorPer", reflect.TypeOf((*MockIClient)(nil).CheckHasVirtualAnchorPer), ctx, req)
}

// CheckIsAnchorInBackList mocks base method.
func (m *MockIClient) CheckIsAnchorInBackList(ctx context.Context, uid uint32) (*channellivemgr.CheckIsAnchorInBackListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIsAnchorInBackList", ctx, uid)
	ret0, _ := ret[0].(*channellivemgr.CheckIsAnchorInBackListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIsAnchorInBackList indicates an expected call of CheckIsAnchorInBackList.
func (mr *MockIClientMockRecorder) CheckIsAnchorInBackList(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIsAnchorInBackList", reflect.TypeOf((*MockIClient)(nil).CheckIsAnchorInBackList), ctx, uid)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// ConfirmAppointPkPush mocks base method.
func (m *MockIClient) ConfirmAppointPkPush(ctx context.Context, in *channellivemgr.ConfirmAppointPkPushReq) (*channellivemgr.ConfirmAppointPkPushResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmAppointPkPush", ctx, in)
	ret0, _ := ret[0].(*channellivemgr.ConfirmAppointPkPushResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ConfirmAppointPkPush indicates an expected call of ConfirmAppointPkPush.
func (mr *MockIClientMockRecorder) ConfirmAppointPkPush(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmAppointPkPush", reflect.TypeOf((*MockIClient)(nil).ConfirmAppointPkPush), ctx, in)
}

// DelAppointPkInfo mocks base method.
func (m *MockIClient) DelAppointPkInfo(ctx context.Context, in *channellivemgr.DelAppointPkInfoReq) (*channellivemgr.DelAppointPkInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelAppointPkInfo", ctx, in)
	ret0, _ := ret[0].(*channellivemgr.DelAppointPkInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelAppointPkInfo indicates an expected call of DelAppointPkInfo.
func (mr *MockIClientMockRecorder) DelAppointPkInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAppointPkInfo", reflect.TypeOf((*MockIClient)(nil).DelAppointPkInfo), ctx, in)
}

// DelChannelLiveInfo mocks base method.
func (m *MockIClient) DelChannelLiveInfo(ctx context.Context, uid uint32, reason string) (*channellivemgr.DelChannelLiveInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelChannelLiveInfo", ctx, uid, reason)
	ret0, _ := ret[0].(*channellivemgr.DelChannelLiveInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelChannelLiveInfo indicates an expected call of DelChannelLiveInfo.
func (mr *MockIClientMockRecorder) DelChannelLiveInfo(ctx, uid, reason interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChannelLiveInfo", reflect.TypeOf((*MockIClient)(nil).DelChannelLiveInfo), ctx, uid, reason)
}

// DelChannelLiveInfoV2 mocks base method.
func (m *MockIClient) DelChannelLiveInfoV2(ctx context.Context, req *channellivemgr.DelChannelLiveInfoReq) (*channellivemgr.DelChannelLiveInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelChannelLiveInfoV2", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.DelChannelLiveInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelChannelLiveInfoV2 indicates an expected call of DelChannelLiveInfoV2.
func (mr *MockIClientMockRecorder) DelChannelLiveInfoV2(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChannelLiveInfoV2", reflect.TypeOf((*MockIClient)(nil).DelChannelLiveInfoV2), ctx, req)
}

// DelVirtualAnchorPer mocks base method.
func (m *MockIClient) DelVirtualAnchorPer(ctx context.Context, req *channellivemgr.DelVirtualAnchorPerReq) (*channellivemgr.DelVirtualAnchorPerResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelVirtualAnchorPer", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.DelVirtualAnchorPerResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelVirtualAnchorPer indicates an expected call of DelVirtualAnchorPer.
func (mr *MockIClientMockRecorder) DelVirtualAnchorPer(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelVirtualAnchorPer", reflect.TypeOf((*MockIClient)(nil).DelVirtualAnchorPer), ctx, req)
}

// GetAllAnchor mocks base method.
func (m *MockIClient) GetAllAnchor(ctx context.Context, req *channellivemgr.GetAllAnchorReq) (*channellivemgr.GetAllAnchorResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllAnchor", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetAllAnchorResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllAnchor indicates an expected call of GetAllAnchor.
func (mr *MockIClientMockRecorder) GetAllAnchor(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllAnchor", reflect.TypeOf((*MockIClient)(nil).GetAllAnchor), ctx, req)
}

// GetAnchorByUidList mocks base method.
func (m *MockIClient) GetAnchorByUidList(ctx context.Context, in *channellivemgr.GetAnchorByUidListReq) (*channellivemgr.GetAnchorByUidListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorByUidList", ctx, in)
	ret0, _ := ret[0].(*channellivemgr.GetAnchorByUidListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAnchorByUidList indicates an expected call of GetAnchorByUidList.
func (mr *MockIClientMockRecorder) GetAnchorByUidList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorByUidList", reflect.TypeOf((*MockIClient)(nil).GetAnchorByUidList), ctx, in)
}

// GetAnchorList mocks base method.
func (m *MockIClient) GetAnchorList(ctx context.Context, page, pageSize uint32) ([]*channellivemgr.AnchorInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorList", ctx, page, pageSize)
	ret0, _ := ret[0].([]*channellivemgr.AnchorInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAnchorList indicates an expected call of GetAnchorList.
func (mr *MockIClientMockRecorder) GetAnchorList(ctx, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorList", reflect.TypeOf((*MockIClient)(nil).GetAnchorList), ctx, page, pageSize)
}

// GetAnchorListMgr mocks base method.
func (m *MockIClient) GetAnchorListMgr(ctx context.Context, req *apicentergo.GetAnchorListReq) (*apicentergo.GetAnchorListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorListMgr", ctx, req)
	ret0, _ := ret[0].(*apicentergo.GetAnchorListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAnchorListMgr indicates an expected call of GetAnchorListMgr.
func (mr *MockIClientMockRecorder) GetAnchorListMgr(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorListMgr", reflect.TypeOf((*MockIClient)(nil).GetAnchorListMgr), ctx, req)
}

// GetAnchorListV2 mocks base method.
func (m *MockIClient) GetAnchorListV2(ctx context.Context, req *channellivemgr.GetAnchorListReq) (*channellivemgr.GetAnchorListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorListV2", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetAnchorListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAnchorListV2 indicates an expected call of GetAnchorListV2.
func (mr *MockIClientMockRecorder) GetAnchorListV2(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorListV2", reflect.TypeOf((*MockIClient)(nil).GetAnchorListV2), ctx, req)
}

// GetAnchorMonthScoreList mocks base method.
func (m *MockIClient) GetAnchorMonthScoreList(ctx context.Context, req *channellivemgr.GetAnchorMonthScoreListReq) (*channellivemgr.GetAnchorMonthScoreListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorMonthScoreList", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetAnchorMonthScoreListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAnchorMonthScoreList indicates an expected call of GetAnchorMonthScoreList.
func (mr *MockIClientMockRecorder) GetAnchorMonthScoreList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorMonthScoreList", reflect.TypeOf((*MockIClient)(nil).GetAnchorMonthScoreList), ctx, req)
}

// GetAnchorOperRecord mocks base method.
func (m *MockIClient) GetAnchorOperRecord(ctx context.Context, req *channellivemgr.GetAnchorOperRecordReq) (*channellivemgr.GetAnchorOperRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorOperRecord", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetAnchorOperRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAnchorOperRecord indicates an expected call of GetAnchorOperRecord.
func (mr *MockIClientMockRecorder) GetAnchorOperRecord(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorOperRecord", reflect.TypeOf((*MockIClient)(nil).GetAnchorOperRecord), ctx, req)
}

// GetAnchorScoreList mocks base method.
func (m *MockIClient) GetAnchorScoreList(ctx context.Context, req *channellivemgr.GetAnchorScoreListReq) (*channellivemgr.GetAnchorScoreListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorScoreList", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetAnchorScoreListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAnchorScoreList indicates an expected call of GetAnchorScoreList.
func (mr *MockIClientMockRecorder) GetAnchorScoreList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorScoreList", reflect.TypeOf((*MockIClient)(nil).GetAnchorScoreList), ctx, req)
}

// GetAnchorScoreOrderList mocks base method.
func (m *MockIClient) GetAnchorScoreOrderList(ctx context.Context, req *channellivemgr.GetAnchorScoreOrderListReq) (*channellivemgr.GetAnchorScoreOrderListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorScoreOrderList", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetAnchorScoreOrderListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAnchorScoreOrderList indicates an expected call of GetAnchorScoreOrderList.
func (mr *MockIClientMockRecorder) GetAnchorScoreOrderList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorScoreOrderList", reflect.TypeOf((*MockIClient)(nil).GetAnchorScoreOrderList), ctx, req)
}

// GetApplyList mocks base method.
func (m *MockIClient) GetApplyList(ctx context.Context, req *channellivemgr.GetApplyListReq) (*channellivemgr.GetApplyListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApplyList", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetApplyListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetApplyList indicates an expected call of GetApplyList.
func (mr *MockIClientMockRecorder) GetApplyList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplyList", reflect.TypeOf((*MockIClient)(nil).GetApplyList), ctx, req)
}

// GetAppointPkInfo mocks base method.
func (m *MockIClient) GetAppointPkInfo(ctx context.Context, in *channellivemgr.GetAppointPkInfoReq) (*channellivemgr.GetAppointPkInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppointPkInfo", ctx, in)
	ret0, _ := ret[0].(*channellivemgr.GetAppointPkInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAppointPkInfo indicates an expected call of GetAppointPkInfo.
func (mr *MockIClientMockRecorder) GetAppointPkInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppointPkInfo", reflect.TypeOf((*MockIClient)(nil).GetAppointPkInfo), ctx, in)
}

// GetAppointPkInfoList mocks base method.
func (m *MockIClient) GetAppointPkInfoList(ctx context.Context, in *channellivemgr.GetAppointPkInfoListReq) (*channellivemgr.GetAppointPkInfoListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppointPkInfoList", ctx, in)
	ret0, _ := ret[0].(*channellivemgr.GetAppointPkInfoListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAppointPkInfoList indicates an expected call of GetAppointPkInfoList.
func (mr *MockIClientMockRecorder) GetAppointPkInfoList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppointPkInfoList", reflect.TypeOf((*MockIClient)(nil).GetAppointPkInfoList), ctx, in)
}

// GetChanneLivePkRankUser mocks base method.
func (m *MockIClient) GetChanneLivePkRankUser(ctx context.Context, req channellivemgr.GetChanneLivePkRankUserReq) (*channellivemgr.GetChanneLivePkRankUserResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChanneLivePkRankUser", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetChanneLivePkRankUserResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChanneLivePkRankUser indicates an expected call of GetChanneLivePkRankUser.
func (mr *MockIClientMockRecorder) GetChanneLivePkRankUser(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChanneLivePkRankUser", reflect.TypeOf((*MockIClient)(nil).GetChanneLivePkRankUser), ctx, req)
}

// GetChannelLiveAnchorScore mocks base method.
func (m *MockIClient) GetChannelLiveAnchorScore(ctx context.Context, uid uint32) (*channellivemgr.GetChannelLiveAnchorScoreResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveAnchorScore", ctx, uid)
	ret0, _ := ret[0].(*channellivemgr.GetChannelLiveAnchorScoreResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelLiveAnchorScore indicates an expected call of GetChannelLiveAnchorScore.
func (mr *MockIClientMockRecorder) GetChannelLiveAnchorScore(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveAnchorScore", reflect.TypeOf((*MockIClient)(nil).GetChannelLiveAnchorScore), ctx, uid)
}

// GetChannelLiveAnchorScoreLog mocks base method.
func (m *MockIClient) GetChannelLiveAnchorScoreLog(ctx context.Context, req *channellivemgr.GetChannelLiveAnchorScoreLogReq) (*channellivemgr.GetChannelLiveAnchorScoreLogResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveAnchorScoreLog", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetChannelLiveAnchorScoreLogResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelLiveAnchorScoreLog indicates an expected call of GetChannelLiveAnchorScoreLog.
func (mr *MockIClientMockRecorder) GetChannelLiveAnchorScoreLog(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveAnchorScoreLog", reflect.TypeOf((*MockIClient)(nil).GetChannelLiveAnchorScoreLog), ctx, req)
}

// GetChannelLiveData mocks base method.
func (m *MockIClient) GetChannelLiveData(ctx context.Context, req channellivemgr.GetChannelLiveDataReq) (*channellivemgr.GetChannelLiveDataResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveData", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetChannelLiveDataResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelLiveData indicates an expected call of GetChannelLiveData.
func (mr *MockIClientMockRecorder) GetChannelLiveData(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveData", reflect.TypeOf((*MockIClient)(nil).GetChannelLiveData), ctx, req)
}

// GetChannelLiveHistoryRecord mocks base method.
func (m *MockIClient) GetChannelLiveHistoryRecord(ctx context.Context, req channellivemgr.GetChannelLiveHistoryRecordReq) (*channellivemgr.GetChannelLiveHistoryRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveHistoryRecord", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetChannelLiveHistoryRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelLiveHistoryRecord indicates an expected call of GetChannelLiveHistoryRecord.
func (mr *MockIClientMockRecorder) GetChannelLiveHistoryRecord(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveHistoryRecord", reflect.TypeOf((*MockIClient)(nil).GetChannelLiveHistoryRecord), ctx, req)
}

// GetChannelLiveInfo mocks base method.
func (m *MockIClient) GetChannelLiveInfo(ctx context.Context, uid uint32, expire bool) (*channellivemgr.GetChannelLiveInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveInfo", ctx, uid, expire)
	ret0, _ := ret[0].(*channellivemgr.GetChannelLiveInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelLiveInfo indicates an expected call of GetChannelLiveInfo.
func (mr *MockIClientMockRecorder) GetChannelLiveInfo(ctx, uid, expire interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveInfo", reflect.TypeOf((*MockIClient)(nil).GetChannelLiveInfo), ctx, uid, expire)
}

// GetChannelLiveInfoByChannelId mocks base method.
func (m *MockIClient) GetChannelLiveInfoByChannelId(ctx context.Context, channelId uint32) (*channellivemgr.GetChannelLiveInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveInfoByChannelId", ctx, channelId)
	ret0, _ := ret[0].(*channellivemgr.GetChannelLiveInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelLiveInfoByChannelId indicates an expected call of GetChannelLiveInfoByChannelId.
func (mr *MockIClientMockRecorder) GetChannelLiveInfoByChannelId(ctx, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveInfoByChannelId", reflect.TypeOf((*MockIClient)(nil).GetChannelLiveInfoByChannelId), ctx, channelId)
}

// GetChannelLivePKRecord mocks base method.
func (m *MockIClient) GetChannelLivePKRecord(ctx context.Context, req channellivemgr.GetChannelLivePKRecordReq) (*channellivemgr.GetChannelLivePKRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLivePKRecord", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetChannelLivePKRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelLivePKRecord indicates an expected call of GetChannelLivePKRecord.
func (mr *MockIClientMockRecorder) GetChannelLivePKRecord(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLivePKRecord", reflect.TypeOf((*MockIClient)(nil).GetChannelLivePKRecord), ctx, req)
}

// GetChannelLiveRandomTitle mocks base method.
func (m *MockIClient) GetChannelLiveRandomTitle(ctx context.Context, req *channellivemgr.GetChannelLiveRandomTitleReq) (*channellivemgr.GetChannelLiveRandomTitleResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveRandomTitle", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetChannelLiveRandomTitleResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelLiveRandomTitle indicates an expected call of GetChannelLiveRandomTitle.
func (mr *MockIClientMockRecorder) GetChannelLiveRandomTitle(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveRandomTitle", reflect.TypeOf((*MockIClient)(nil).GetChannelLiveRandomTitle), ctx, req)
}

// GetChannelLiveRankUser mocks base method.
func (m *MockIClient) GetChannelLiveRankUser(ctx context.Context, req channellivemgr.GetChannelLiveRankUserReq) (*channellivemgr.GetChannelLiveRankUserResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveRankUser", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetChannelLiveRankUserResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelLiveRankUser indicates an expected call of GetChannelLiveRankUser.
func (mr *MockIClientMockRecorder) GetChannelLiveRankUser(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveRankUser", reflect.TypeOf((*MockIClient)(nil).GetChannelLiveRankUser), ctx, req)
}

// GetChannelLiveStatus mocks base method.
func (m *MockIClient) GetChannelLiveStatus(ctx context.Context, req channellivemgr.GetChannelLiveStatusReq) (*channellivemgr.GetChannelLiveStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveStatus", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetChannelLiveStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelLiveStatus indicates an expected call of GetChannelLiveStatus.
func (mr *MockIClientMockRecorder) GetChannelLiveStatus(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveStatus", reflect.TypeOf((*MockIClient)(nil).GetChannelLiveStatus), ctx, req)
}

// GetChannelLiveTOPN mocks base method.
func (m *MockIClient) GetChannelLiveTOPN(ctx context.Context, req channellivemgr.GetChannelLiveTOPNReq) (*channellivemgr.GetChannelLiveTOPNResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveTOPN", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetChannelLiveTOPNResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelLiveTOPN indicates an expected call of GetChannelLiveTOPN.
func (mr *MockIClientMockRecorder) GetChannelLiveTOPN(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveTOPN", reflect.TypeOf((*MockIClient)(nil).GetChannelLiveTOPN), ctx, req)
}

// GetChannelLiveTotalData mocks base method.
func (m *MockIClient) GetChannelLiveTotalData(ctx context.Context, req channellivemgr.GetChannelLiveTotalDataReq) (*channellivemgr.GetChannelLiveTotalDataResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveTotalData", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetChannelLiveTotalDataResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelLiveTotalData indicates an expected call of GetChannelLiveTotalData.
func (mr *MockIClientMockRecorder) GetChannelLiveTotalData(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveTotalData", reflect.TypeOf((*MockIClient)(nil).GetChannelLiveTotalData), ctx, req)
}

// GetChannelLiveWatchTimeRankUser mocks base method.
func (m *MockIClient) GetChannelLiveWatchTimeRankUser(ctx context.Context, req channellivemgr.GetChannelLiveWatchTimeRankUserReq) (*channellivemgr.GetChannelLiveWatchTimeRankUserResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveWatchTimeRankUser", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetChannelLiveWatchTimeRankUserResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelLiveWatchTimeRankUser indicates an expected call of GetChannelLiveWatchTimeRankUser.
func (mr *MockIClientMockRecorder) GetChannelLiveWatchTimeRankUser(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveWatchTimeRankUser", reflect.TypeOf((*MockIClient)(nil).GetChannelLiveWatchTimeRankUser), ctx, req)
}

// GetHeartBeatTimeOut mocks base method.
func (m *MockIClient) GetHeartBeatTimeOut(ctx context.Context, expire int64, del bool) (*channellivemgr.GetHeartBeatTimeOutResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHeartBeatTimeOut", ctx, expire, del)
	ret0, _ := ret[0].(*channellivemgr.GetHeartBeatTimeOutResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetHeartBeatTimeOut indicates an expected call of GetHeartBeatTimeOut.
func (mr *MockIClientMockRecorder) GetHeartBeatTimeOut(ctx, expire, del interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHeartBeatTimeOut", reflect.TypeOf((*MockIClient)(nil).GetHeartBeatTimeOut), ctx, expire, del)
}

// GetItemConfig mocks base method.
func (m *MockIClient) GetItemConfig(ctx context.Context, req *channellivemgr.GetItemConfigReq) (*channellivemgr.GetItemConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetItemConfig", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetItemConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetItemConfig indicates an expected call of GetItemConfig.
func (mr *MockIClientMockRecorder) GetItemConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetItemConfig", reflect.TypeOf((*MockIClient)(nil).GetItemConfig), ctx, req)
}

// GetLiveReadyGuideImg mocks base method.
func (m *MockIClient) GetLiveReadyGuideImg(ctx context.Context, req *channellivemgr.GetLiveReadyGuideImgReq) (*channellivemgr.GetLiveReadyGuideImgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiveReadyGuideImg", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetLiveReadyGuideImgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetLiveReadyGuideImg indicates an expected call of GetLiveReadyGuideImg.
func (mr *MockIClientMockRecorder) GetLiveReadyGuideImg(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveReadyGuideImg", reflect.TypeOf((*MockIClient)(nil).GetLiveReadyGuideImg), ctx, req)
}

// GetMyToolList mocks base method.
func (m *MockIClient) GetMyToolList(ctx context.Context, req *channellivemgr.GetMyToolListReq) (*channellivemgr.GetMyToolListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyToolList", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetMyToolListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMyToolList indicates an expected call of GetMyToolList.
func (mr *MockIClientMockRecorder) GetMyToolList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyToolList", reflect.TypeOf((*MockIClient)(nil).GetMyToolList), ctx, req)
}

// GetPKMatchInfo mocks base method.
func (m *MockIClient) GetPKMatchInfo(ctx context.Context, uid, channelId uint32) (*channellivemgr.GetPKMatchInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPKMatchInfo", ctx, uid, channelId)
	ret0, _ := ret[0].(*channellivemgr.GetPKMatchInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPKMatchInfo indicates an expected call of GetPKMatchInfo.
func (mr *MockIClientMockRecorder) GetPKMatchInfo(ctx, uid, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPKMatchInfo", reflect.TypeOf((*MockIClient)(nil).GetPKMatchInfo), ctx, uid, channelId)
}

// GetPkInfo mocks base method.
func (m *MockIClient) GetPkInfo(ctx context.Context, uid, channelId uint32) (*channellivemgr.GetPkInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPkInfo", ctx, uid, channelId)
	ret0, _ := ret[0].(*channellivemgr.GetPkInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPkInfo indicates an expected call of GetPkInfo.
func (mr *MockIClientMockRecorder) GetPkInfo(ctx, uid, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPkInfo", reflect.TypeOf((*MockIClient)(nil).GetPkInfo), ctx, uid, channelId)
}

// GetUserDayPushCnt mocks base method.
func (m *MockIClient) GetUserDayPushCnt(ctx context.Context, req *channellivemgr.GetUserPushCntReq) (*channellivemgr.GetUserPushCntResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDayPushCnt", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetUserPushCntResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserDayPushCnt indicates an expected call of GetUserDayPushCnt.
func (mr *MockIClientMockRecorder) GetUserDayPushCnt(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDayPushCnt", reflect.TypeOf((*MockIClient)(nil).GetUserDayPushCnt), ctx, req)
}

// GetUserPushCnt mocks base method.
func (m *MockIClient) GetUserPushCnt(ctx context.Context, req *channellivemgr.GetUserPushCntReq) (*channellivemgr.GetUserPushCntResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPushCnt", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetUserPushCntResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserPushCnt indicates an expected call of GetUserPushCnt.
func (mr *MockIClientMockRecorder) GetUserPushCnt(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPushCnt", reflect.TypeOf((*MockIClient)(nil).GetUserPushCnt), ctx, req)
}

// GetVirtualAnchorPerList mocks base method.
func (m *MockIClient) GetVirtualAnchorPerList(ctx context.Context, req *channellivemgr.GetVirtualAnchorPerListReq) (*channellivemgr.GetVirtualAnchorPerListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualAnchorPerList", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetVirtualAnchorPerListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetVirtualAnchorPerList indicates an expected call of GetVirtualAnchorPerList.
func (mr *MockIClientMockRecorder) GetVirtualAnchorPerList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualAnchorPerList", reflect.TypeOf((*MockIClient)(nil).GetVirtualAnchorPerList), ctx, req)
}

// GetVirtualLiveChannelSecret mocks base method.
func (m *MockIClient) GetVirtualLiveChannelSecret(ctx context.Context, req *channellivemgr.GetVirtualLiveChannelSecretReq) (*channellivemgr.GetVirtualLiveChannelSecretResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualLiveChannelSecret", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetVirtualLiveChannelSecretResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetVirtualLiveChannelSecret indicates an expected call of GetVirtualLiveChannelSecret.
func (mr *MockIClientMockRecorder) GetVirtualLiveChannelSecret(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualLiveChannelSecret", reflect.TypeOf((*MockIClient)(nil).GetVirtualLiveChannelSecret), ctx, req)
}

// GetVirtualLiveInfoBySecret mocks base method.
func (m *MockIClient) GetVirtualLiveInfoBySecret(ctx context.Context, req *channellivemgr.GetVirtualLiveInfoBySecretReq) (*channellivemgr.GetVirtualLiveInfoBySecretResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualLiveInfoBySecret", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.GetVirtualLiveInfoBySecretResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetVirtualLiveInfoBySecret indicates an expected call of GetVirtualLiveInfoBySecret.
func (mr *MockIClientMockRecorder) GetVirtualLiveInfoBySecret(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualLiveInfoBySecret", reflect.TypeOf((*MockIClient)(nil).GetVirtualLiveInfoBySecret), ctx, req)
}

// HandlerApply mocks base method.
func (m *MockIClient) HandlerApply(ctx context.Context, req *channellivemgr.HandlerApplyReq) (*channellivemgr.HandlerApplyResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlerApply", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.HandlerApplyResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// HandlerApply indicates an expected call of HandlerApply.
func (mr *MockIClientMockRecorder) HandlerApply(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlerApply", reflect.TypeOf((*MockIClient)(nil).HandlerApply), ctx, req)
}

// PushTest mocks base method.
func (m *MockIClient) PushTest(ctx context.Context, req *channellivemgr.PushTestReq) (*channellivemgr.PushTestResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushTest", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.PushTestResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// PushTest indicates an expected call of PushTest.
func (mr *MockIClientMockRecorder) PushTest(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushTest", reflect.TypeOf((*MockIClient)(nil).PushTest), ctx, req)
}

// ReportClientIDChange mocks base method.
func (m *MockIClient) ReportClientIDChange(ctx context.Context, req *channellivemgr.ReportClientIDChangeReq) (*channellivemgr.ReportClientIDChangeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportClientIDChange", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.ReportClientIDChangeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ReportClientIDChange indicates an expected call of ReportClientIDChange.
func (mr *MockIClientMockRecorder) ReportClientIDChange(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportClientIDChange", reflect.TypeOf((*MockIClient)(nil).ReportClientIDChange), ctx, req)
}

// SearchAnchor mocks base method.
func (m *MockIClient) SearchAnchor(ctx context.Context, req *channellivemgr.SearchAnchorReq) (*channellivemgr.SearchAnchorResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchAnchor", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.SearchAnchorResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SearchAnchor indicates an expected call of SearchAnchor.
func (mr *MockIClientMockRecorder) SearchAnchor(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAnchor", reflect.TypeOf((*MockIClient)(nil).SearchAnchor), ctx, req)
}

// SetAuthFlag mocks base method.
func (m *MockIClient) SetAuthFlag(ctx context.Context, req *channellivemgr.SetAuthFlagReq) (*channellivemgr.SetAuthFlagResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAuthFlag", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.SetAuthFlagResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetAuthFlag indicates an expected call of SetAuthFlag.
func (mr *MockIClientMockRecorder) SetAuthFlag(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAuthFlag", reflect.TypeOf((*MockIClient)(nil).SetAuthFlag), ctx, req)
}

// SetChannelLiveInfo mocks base method.
func (m *MockIClient) SetChannelLiveInfo(ctx context.Context, req *channellivemgr.SetChannelLiveInfoReq) (*channellivemgr.SetChannelLiveInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelLiveInfo", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.SetChannelLiveInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelLiveInfo indicates an expected call of SetChannelLiveInfo.
func (mr *MockIClientMockRecorder) SetChannelLiveInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveInfo", reflect.TypeOf((*MockIClient)(nil).SetChannelLiveInfo), ctx, req)
}

// SetChannelLiveInfoForTest mocks base method.
func (m *MockIClient) SetChannelLiveInfoForTest(ctx context.Context, req *channellivemgr.SetChannelLiveInfoForTestReq) (*channellivemgr.SetChannelLiveInfoForTestResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelLiveInfoForTest", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.SetChannelLiveInfoForTestResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelLiveInfoForTest indicates an expected call of SetChannelLiveInfoForTest.
func (mr *MockIClientMockRecorder) SetChannelLiveInfoForTest(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveInfoForTest", reflect.TypeOf((*MockIClient)(nil).SetChannelLiveInfoForTest), ctx, req)
}

// SetChannelLiveOpponentMicFlag mocks base method.
func (m *MockIClient) SetChannelLiveOpponentMicFlag(ctx context.Context, channelId uint32, flag channellivemgr.ChannelLiveOpponentMicFlag) (*channellivemgr.SetChannelLiveOpponentMicFlagResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelLiveOpponentMicFlag", ctx, channelId, flag)
	ret0, _ := ret[0].(*channellivemgr.SetChannelLiveOpponentMicFlagResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelLiveOpponentMicFlag indicates an expected call of SetChannelLiveOpponentMicFlag.
func (mr *MockIClientMockRecorder) SetChannelLiveOpponentMicFlag(ctx, channelId, flag interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveOpponentMicFlag", reflect.TypeOf((*MockIClient)(nil).SetChannelLiveOpponentMicFlag), ctx, channelId, flag)
}

// SetChannelLiveStatus mocks base method.
func (m *MockIClient) SetChannelLiveStatus(ctx context.Context, req channellivemgr.SetChannelLiveStatusReq) (*channellivemgr.SetChannelLiveStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelLiveStatus", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.SetChannelLiveStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelLiveStatus indicates an expected call of SetChannelLiveStatus.
func (mr *MockIClientMockRecorder) SetChannelLiveStatus(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveStatus", reflect.TypeOf((*MockIClient)(nil).SetChannelLiveStatus), ctx, req)
}

// SetChannelLiveTag mocks base method.
func (m *MockIClient) SetChannelLiveTag(ctx context.Context, in *channellivemgr.SetChannelLiveTagReq) (*channellivemgr.SetChannelLiveTagResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelLiveTag", ctx, in)
	ret0, _ := ret[0].(*channellivemgr.SetChannelLiveTagResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelLiveTag indicates an expected call of SetChannelLiveTag.
func (mr *MockIClientMockRecorder) SetChannelLiveTag(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveTag", reflect.TypeOf((*MockIClient)(nil).SetChannelLiveTag), ctx, in)
}

// SetPkStatus mocks base method.
func (m *MockIClient) SetPkStatus(ctx context.Context, req channellivemgr.SetPkStatusReq) (*channellivemgr.SetPkStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPkStatus", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.SetPkStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetPkStatus indicates an expected call of SetPkStatus.
func (mr *MockIClientMockRecorder) SetPkStatus(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPkStatus", reflect.TypeOf((*MockIClient)(nil).SetPkStatus), ctx, req)
}

// StartPkMatch mocks base method.
func (m *MockIClient) StartPkMatch(ctx context.Context, uid, channelId uint32, ty channellivemgr.ChannelLivePKMatchType) (*channellivemgr.StartPkMatchResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartPkMatch", ctx, uid, channelId, ty)
	ret0, _ := ret[0].(*channellivemgr.StartPkMatchResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// StartPkMatch indicates an expected call of StartPkMatch.
func (mr *MockIClientMockRecorder) StartPkMatch(ctx, uid, channelId, ty interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartPkMatch", reflect.TypeOf((*MockIClient)(nil).StartPkMatch), ctx, uid, channelId, ty)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdateAppointPkInfo mocks base method.
func (m *MockIClient) UpdateAppointPkInfo(ctx context.Context, in *channellivemgr.UpdateAppointPkInfoReq) (*channellivemgr.UpdateAppointPkInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAppointPkInfo", ctx, in)
	ret0, _ := ret[0].(*channellivemgr.UpdateAppointPkInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateAppointPkInfo indicates an expected call of UpdateAppointPkInfo.
func (mr *MockIClientMockRecorder) UpdateAppointPkInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAppointPkInfo", reflect.TypeOf((*MockIClient)(nil).UpdateAppointPkInfo), ctx, in)
}

// UpdateVirtualAnchorPer mocks base method.
func (m *MockIClient) UpdateVirtualAnchorPer(ctx context.Context, req *channellivemgr.UpdateVirtualAnchorPerReq) (*channellivemgr.UpdateVirtualAnchorPerResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateVirtualAnchorPer", ctx, req)
	ret0, _ := ret[0].(*channellivemgr.UpdateVirtualAnchorPerResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateVirtualAnchorPer indicates an expected call of UpdateVirtualAnchorPer.
func (mr *MockIClientMockRecorder) UpdateVirtualAnchorPer(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateVirtualAnchorPer", reflect.TypeOf((*MockIClient)(nil).UpdateVirtualAnchorPer), ctx, req)
}
