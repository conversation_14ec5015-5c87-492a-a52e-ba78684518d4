package server

import (
	"context"
	"fmt"
	guild_cooperation "golang.52tt.com/clients/guild-cooperation"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"golang.52tt.com/clients/account"
	anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
	knightgroupmembers "golang.52tt.com/clients/knight-group-members"
	knightgrouprank "golang.52tt.com/clients/knight-group-rank"
	knightprivilege "golang.52tt.com/clients/knight-privilege"
	"golang.52tt.com/pkg/log"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/knightprivilegelogic"
	anchorcontractgoPB "golang.52tt.com/protocol/services/anchorcontract-go"
	guildCooperationPb "golang.52tt.com/protocol/services/guild-cooperation"
	knightprivilegePB "golang.52tt.com/protocol/services/knight-privilege"
	knightgroupmembersPB "golang.52tt.com/protocol/services/knightgroupmembers"
	knightgrouprankPB "golang.52tt.com/protocol/services/knightgrouprank"
	"golang.52tt.com/services/knight-group/knight-privilege-logic/conf"
	"golang.52tt.com/services/knight-group/knight-privilege-logic/mocks"
	conf2 "golang.52tt.com/services/knight-group/knight-privilege/conf"
	mocks2 "golang.52tt.com/services/knight-group/knight-privilege/mocks"

	mockAccount "golang.52tt.com/clients/mocks/account"
	mockanchorcontract_go "golang.52tt.com/clients/mocks/anchorcontract-go"
	mockGuildCooperation "golang.52tt.com/clients/mocks/guild-cooperation"
	mockknightgroupmembers "golang.52tt.com/clients/mocks/knight-group-members"
	mockknightgrouprank "golang.52tt.com/clients/mocks/knight-group-rank"
	mockknightprivilege "golang.52tt.com/clients/mocks/knight-privilege"
)

var (
	knightSever *KnightPrivilegeLogicServerImpl
	mockServer  *mocks.MockIKnightPrivilegeLogicServerImpl
	mockdyCfg   *mocks2.MockISDyConfigHandler

	mockknightprivilegeCli    *mockknightprivilege.MockIClient
	mockknightgroupmembersCli *mockknightgroupmembers.MockIClient
	mockknightgrouprankCli    *mockknightgrouprank.MockIClient
	mockanchorcontractCli     *mockanchorcontract_go.MockIClient
	mockguildCooperationCli   *mockGuildCooperation.MockIClient
	mockAccountCli            *mockAccount.MockIClient

	genServerMock = func(t *testing.T) {
		ctl := gomock.NewController(t)
		defer ctl.Finish()

		mockdyCfg = mocks2.NewMockISDyConfigHandler(ctl)
		mockServer = mocks.NewMockIKnightPrivilegeLogicServerImpl(ctl)

		mockAccountCli = mockAccount.NewMockIClient(ctl)
		mockknightprivilegeCli = mockknightprivilege.NewMockIClient(ctl)
		mockknightgroupmembersCli = mockknightgroupmembers.NewMockIClient(ctl)
		mockknightgrouprankCli = mockknightgrouprank.NewMockIClient(ctl)
		mockguildCooperationCli = mockGuildCooperation.NewMockIClient(ctl)
		mockanchorcontractCli = mockanchorcontract_go.NewMockIClient(ctl)
	}
)

func init() {
	ctx := context.WithValue(context.Background(), "configfile", "../knight-privilege-logic.json")
	svr, err := NewKnightPrivilegeLogicServerImpl(ctx, nil)
	if err != nil {
		fmt.Println(err)
		return
	}
	knightSever = svr
	log.SetLevel(log.DebugLevel)
}

func TestKnightPrivilegeLogicServerImpl_GetKnightGroupEntry(t *testing.T) {
	genServerMock(t)

	var (
		knightUid, channelId uint32 = 1, 1
		ctx                         = protogrpc.WithServiceInfo(context.TODO(), &protogrpc.ServiceInfo{UserID: knightUid})
		in                          = &pb.GetKnightGroupEntryReq{AnchorUid: knightUid, ChannelId: channelId}
		out                         = &pb.GetKnightGroupEntryResp{
			IsShow:      true,
			Title:       "我的骑士团",
			MemberCnt:   1,
			AccountList: []string{"1"},
		}

		usermap = map[uint32]*account.User{1: {Username: "1"}}
	)

	gomock.InOrder(
		mockdyCfg.EXPECT().CheckWhiteAnchor(gomock.Any()).Return(false),
		mockanchorcontractCli.EXPECT().GetUserContractCacheInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&anchorcontractgoPB.ContractCacheInfo{
			Contract: &anchorcontractgoPB.ContractInfo{GuildId: 1, ExpireTime: uint32(time.Now().Unix()) + 100},
		}, nil),
		mockguildCooperationCli.EXPECT().GetGuildCooperationWithTime(gomock.Any(), gomock.Any(), gomock.Any()).Return(&guildCooperationPb.GetGuildCooperationWithTimeResp{IsExist: true}, nil),
		mockknightgrouprankCli.EXPECT().GetLoveRank(gomock.Any(), gomock.Any()).Return(&knightgrouprankPB.GetLoveRankResp{RankList: []*knightgrouprankPB.RankValueInfo{{Uid: 1}}}, nil),
		mockknightgroupmembersCli.EXPECT().GetKnightGroupMember(gomock.Any(), gomock.Any()).Return(&knightgroupmembersPB.GetKnightGroupMemberResp{TotalMemberCnt: 1}, nil),
		mockAccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermap, nil),
	)

	type fields struct {
		sc                    *conf.ServiceConfigT
		dyconfig              conf2.ISDyConfigHandler
		knightprivilegeCli    knightprivilege.IClient
		knightgroupmembersCli knightgroupmembers.IClient
		knightgrouprankCli    knightgrouprank.IClient
		anchorcontractCli     anchorcontract_go.IClient
		golddiamonnCli        guild_cooperation.IClient
		accountCli            account.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetKnightGroupEntryReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetKnightGroupEntryResp
		wantErr bool
	}{
		{
			name: "GetKnightGroupEntry",
			fields: fields{
				dyconfig:              mockdyCfg,
				knightprivilegeCli:    mockknightprivilegeCli,
				knightgroupmembersCli: mockknightgroupmembersCli,
				knightgrouprankCli:    mockknightgrouprankCli,
				anchorcontractCli:     mockanchorcontractCli,
				golddiamonnCli:        mockguildCooperationCli,
				accountCli:            mockAccountCli,
			},
			args:    args{ctx: ctx, in: in},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &KnightPrivilegeLogicServerImpl{
				sc:                    tt.fields.sc,
				dyconfig:              tt.fields.dyconfig,
				knightprivilegeCli:    tt.fields.knightprivilegeCli,
				knightgroupmembersCli: tt.fields.knightgroupmembersCli,
				knightgrouprankCli:    tt.fields.knightgrouprankCli,
				anchorcontractCli:     tt.fields.anchorcontractCli,
				guildCoopCli:          tt.fields.golddiamonnCli,
				accountCli:            tt.fields.accountCli,
			}
			got, err := s.GetKnightGroupEntry(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("KnightPrivilegeLogicServerImpl.GetKnightGroupEntry() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("KnightPrivilegeLogicServerImpl.GetKnightGroupEntry() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestKnightPrivilegeLogicServerImpl_GetKnightGroupDetialInfo(t *testing.T) {

	genServerMock(t)

	var (
		knightUid, channelId uint32 = 1, 1
		ctx                         = protogrpc.WithServiceInfo(context.TODO(), &protogrpc.ServiceInfo{UserID: knightUid})
		in                          = &pb.GetKnightGroupDetialInfoReq{AnchorUid: knightUid, ChannelId: channelId}
		out                         = &pb.GetKnightGroupDetialInfoResp{
			InfoList: []*pb.KnightGroupDetialInfo{{PicUrl: "1", Title: "1", Content: "1"}},
		}
	)

	gomock.InOrder(
		mockknightprivilegeCli.EXPECT().GetKnightGroupDetialInfo(ctx).Return(&knightprivilegePB.GetKnightGroupDetialInfoResp{
			InfoList: []*knightprivilegePB.KnightGroupDetialInfo{{PicUrl: "1", Title: "1", Content: "1"}},
		}, nil),
	)

	type fields struct {
		sc                    *conf.ServiceConfigT
		dyconfig              conf2.ISDyConfigHandler
		knightprivilegeCli    knightprivilege.IClient
		knightgroupmembersCli knightgroupmembers.IClient
		knightgrouprankCli    knightgrouprank.IClient
		anchorcontractCli     anchorcontract_go.IClient
		guildCoopCli          guild_cooperation.IClient
		accountCli            account.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetKnightGroupDetialInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetKnightGroupDetialInfoResp
		wantErr bool
	}{
		{
			name: "GetKnightGroupDetialInfo",
			fields: fields{
				dyconfig:              mockdyCfg,
				knightprivilegeCli:    mockknightprivilegeCli,
				knightgroupmembersCli: mockknightgroupmembersCli,
				knightgrouprankCli:    mockknightgrouprankCli,
				anchorcontractCli:     mockanchorcontractCli,
				guildCoopCli:          mockguildCooperationCli,
				accountCli:            mockAccountCli,
			},
			args:    args{ctx: ctx, in: in},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &KnightPrivilegeLogicServerImpl{
				sc:                    tt.fields.sc,
				dyconfig:              tt.fields.dyconfig,
				knightprivilegeCli:    tt.fields.knightprivilegeCli,
				knightgroupmembersCli: tt.fields.knightgroupmembersCli,
				knightgrouprankCli:    tt.fields.knightgrouprankCli,
				anchorcontractCli:     tt.fields.anchorcontractCli,
				guildCoopCli:          tt.fields.guildCoopCli,
				accountCli:            tt.fields.accountCli,
			}
			got, err := s.GetKnightGroupDetialInfo(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("KnightPrivilegeLogicServerImpl.GetKnightGroupDetialInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("KnightPrivilegeLogicServerImpl.GetKnightGroupDetialInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestKnightPrivilegeLogicServerImpl_GetKnightGroupOpenStatus(t *testing.T) {

	genServerMock(t)

	var (
		knightUid, channelId, anchorUid, day, price uint32 = 1, 1, 1, 1, 1
		ctx                                                = protogrpc.WithServiceInfo(context.TODO(), &protogrpc.ServiceInfo{UserID: knightUid})
		ctx2                                               = protogrpc.WithServiceInfo(context.TODO(), &protogrpc.ServiceInfo{UserID: knightUid + 1})

		ts       = time.Now()
		expireTs = time.Date(ts.Year(), ts.Month(), ts.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, 1)
		msg      = fmt.Sprintf("开通后，可以守护至%04d/%02d/%02d", expireTs.Year(), expireTs.Month(), expireTs.Day())

		nowDay    = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Local)
		lessDay   = (ts.Unix()-nowDay.Unix())/86400 - 1
		expireTs2 = expireTs.AddDate(0, 0, -1)
		msg2      = fmt.Sprintf("%d天后过期，续费可以守护至%04d/%02d/%02d", lessDay, expireTs2.Year(), expireTs2.Month(), expireTs2.Day())

		in  = &pb.GetKnightGroupOpenStatusReq{AnchorUid: knightUid, ChannelId: channelId}
		out = &pb.GetKnightGroupOpenStatusResp{Status: 0, TbeanPrice: 1, Content: msg}

		in2  = &pb.GetKnightGroupOpenStatusReq{AnchorUid: anchorUid, ChannelId: channelId}
		out2 = &pb.GetKnightGroupOpenStatusResp{Status: 1, TbeanPrice: 1, Content: msg2}
	)

	gomock.InOrder(
		mockknightgroupmembersCli.EXPECT().GetKnightInfo(ctx, &knightgroupmembersPB.GetKnightInfoReq{KnightUid: knightUid, ChannelId: channelId}).Return(&knightgroupmembersPB.GetKnightInfoResp{}, nil),

		mockdyCfg.EXPECT().GetJoinKnightGroupDay().Return(day),
		mockdyCfg.EXPECT().GetJoinKnightGroupPrice().Return(price),
		mockdyCfg.EXPECT().GetWhiteUidPrice(knightUid).Return(uint32(0)),

		mockknightgroupmembersCli.EXPECT().GetKnightInfo(ctx2,
			&knightgroupmembersPB.GetKnightInfoReq{KnightUid: knightUid + 1, ChannelId: channelId}).
			Return(&knightgroupmembersPB.GetKnightInfoResp{KnightInfoList: []*knightgroupmembersPB.KnightInfo{{AnchorUid: anchorUid, EndTime: uint32(time.Now().Unix()) + 100}}}, nil),
		mockdyCfg.EXPECT().GetJoinKnightGroupDay().Return(day),
		mockdyCfg.EXPECT().GetJoinKnightGroupPrice().Return(price),
		mockdyCfg.EXPECT().GetWhiteUidPrice(knightUid+1).Return(uint32(0)),
	)

	type fields struct {
		sc                    *conf.ServiceConfigT
		dyconfig              conf2.ISDyConfigHandler
		knightprivilegeCli    knightprivilege.IClient
		knightgroupmembersCli knightgroupmembers.IClient
		knightgrouprankCli    knightgrouprank.IClient
		anchorcontractCli     anchorcontract_go.IClient
		guildCoopCli          guild_cooperation.IClient
		accountCli            account.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetKnightGroupOpenStatusReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetKnightGroupOpenStatusResp
		wantErr bool
	}{
		{
			name: "GetKnightGroupOpenStatus_1",
			fields: fields{
				dyconfig:              mockdyCfg,
				knightprivilegeCli:    mockknightprivilegeCli,
				knightgroupmembersCli: mockknightgroupmembersCli,
				knightgrouprankCli:    mockknightgrouprankCli,
				anchorcontractCli:     mockanchorcontractCli,
				guildCoopCli:          mockguildCooperationCli,
				accountCli:            mockAccountCli,
			},
			args:    args{ctx: ctx, in: in},
			want:    out,
			wantErr: false,
		},
		{
			name: "GetKnightGroupOpenStatus_2",
			fields: fields{
				dyconfig:              mockdyCfg,
				knightprivilegeCli:    mockknightprivilegeCli,
				knightgroupmembersCli: mockknightgroupmembersCli,
				knightgrouprankCli:    mockknightgrouprankCli,
				anchorcontractCli:     mockanchorcontractCli,
				guildCoopCli:          mockguildCooperationCli,
				accountCli:            mockAccountCli,
			},
			args:    args{ctx: ctx2, in: in2},
			want:    out2,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &KnightPrivilegeLogicServerImpl{
				sc:                    tt.fields.sc,
				dyconfig:              tt.fields.dyconfig,
				knightprivilegeCli:    tt.fields.knightprivilegeCli,
				knightgroupmembersCli: tt.fields.knightgroupmembersCli,
				knightgrouprankCli:    tt.fields.knightgrouprankCli,
				anchorcontractCli:     tt.fields.anchorcontractCli,
				guildCoopCli:          tt.fields.guildCoopCli,
				accountCli:            tt.fields.accountCli,
			}
			got, err := s.GetKnightGroupOpenStatus(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("KnightPrivilegeLogicServerImpl.GetKnightGroupOpenStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("KnightPrivilegeLogicServerImpl.GetKnightGroupOpenStatus() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestKnightPrivilegeLogicServerImpl_GetKnightCardInfo(t *testing.T) {

	genServerMock(t)

	var (
		knightUid, channelId uint32 = 1, 1
		ctx                         = protogrpc.WithServiceInfo(context.TODO(), &protogrpc.ServiceInfo{UserID: knightUid})
		in                          = &pb.GetKnightCardInfoReq{TargetUid: knightUid, ChannelId: channelId}
		out                         = &pb.GetKnightCardInfoResp{
			KnightNameplateResourceUrl: "1",
		}
	)

	gomock.InOrder(
		mockknightprivilegeCli.EXPECT().GetKnightPrivilegeInfo(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&knightprivilegePB.GetKnightPrivilegeInfoResp{
			Status:                1,
			KnightCardResourceUrl: "1",
		}, nil),
	)

	type fields struct {
		sc                    *conf.ServiceConfigT
		dyconfig              conf2.ISDyConfigHandler
		knightprivilegeCli    knightprivilege.IClient
		knightgroupmembersCli knightgroupmembers.IClient
		knightgrouprankCli    knightgrouprank.IClient
		anchorcontractCli     anchorcontract_go.IClient
		guildCoopCli          guild_cooperation.IClient
		accountCli            account.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetKnightCardInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetKnightCardInfoResp
		wantErr bool
	}{
		{
			name: "GetKnightCardInfo",
			fields: fields{
				dyconfig:              mockdyCfg,
				knightprivilegeCli:    mockknightprivilegeCli,
				knightgroupmembersCli: mockknightgroupmembersCli,
				knightgrouprankCli:    mockknightgrouprankCli,
				anchorcontractCli:     mockanchorcontractCli,
				guildCoopCli:          mockguildCooperationCli,
				accountCli:            mockAccountCli,
			},
			args:    args{ctx: ctx, in: in},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &KnightPrivilegeLogicServerImpl{
				sc:                    tt.fields.sc,
				dyconfig:              tt.fields.dyconfig,
				knightprivilegeCli:    tt.fields.knightprivilegeCli,
				knightgroupmembersCli: tt.fields.knightgroupmembersCli,
				knightgrouprankCli:    tt.fields.knightgrouprankCli,
				anchorcontractCli:     tt.fields.anchorcontractCli,
				guildCoopCli:          tt.fields.guildCoopCli,
				accountCli:            tt.fields.accountCli,
			}
			got, err := s.GetKnightCardInfo(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("KnightPrivilegeLogicServerImpl.GetKnightCardInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("KnightPrivilegeLogicServerImpl.GetKnightCardInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}
