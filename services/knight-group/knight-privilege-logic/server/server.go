package server

import (
	"context"
	"errors"
	"fmt"
	guild_cooperation "golang.52tt.com/clients/guild-cooperation"
	"time"

	"golang.52tt.com/clients/account"
	anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
	knightgroupmembers "golang.52tt.com/clients/knight-group-members"
	knightgrouprank "golang.52tt.com/clients/knight-group-rank"
	knightprivilege "golang.52tt.com/clients/knight-privilege"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/knightprivilegelogic"
	"golang.52tt.com/protocol/common/status"
	knightprivilegePB "golang.52tt.com/protocol/services/knight-privilege"
	knightgroupmembersPB "golang.52tt.com/protocol/services/knightgroupmembers"
	knightgrouprankPB "golang.52tt.com/protocol/services/knightgrouprank"
	"golang.52tt.com/services/knight-group/knight-privilege-logic/conf"
	conf2 "golang.52tt.com/services/knight-group/knight-privilege/conf"
	"google.golang.org/grpc"
)

// KnightPrivilegeLogicServer is the server API for KnightPrivilegeLogic service.
type KnightPrivilegeLogicServerImpl struct {
	sc                    *conf.ServiceConfigT
	dyconfig              conf2.ISDyConfigHandler
	knightprivilegeCli    knightprivilege.IClient
	knightgroupmembersCli knightgroupmembers.IClient
	knightgrouprankCli    knightgrouprank.IClient
	anchorcontractCli     anchorcontract_go.IClient
	guildCoopCli          guild_cooperation.IClient
	accountCli            account.IClient
}

func NewKnightPrivilegeLogicServerImpl(ctx context.Context, cfg config.Configer, dopts ...grpc.DialOption) (*KnightPrivilegeLogicServerImpl, error) {
	sc := &conf.ServiceConfigT{}
	cfgPath := ctx.Value("configfile").(string)
	if cfgPath == "" {
		return nil, errors.New("configfile not exist")
	}
	err := sc.Parse(cfgPath)
	if err != nil {
		log.ErrorWithCtx(ctx, "config Parse fail err:%v", err)
		return nil, err
	}

	dyConfig, err := conf2.NewConfigHandler(conf2.ConfigFile)
	if err != nil {
		return nil, err
	}
	err = dyConfig.Start()
	if err != nil {
		return nil, err
	}

	guildCoopCli := guild_cooperation.NewIClient(dopts...)
	knightgroupmembersCli, _ := knightgroupmembers.NewClient(dopts...)
	anchorcontractCli, _ := anchorcontract_go.NewClient(dopts...)
	knightgrouprankCli, _ := knightgrouprank.NewClient(dopts...)
	accountCli, _ := account.NewClient(dopts...)
	return &KnightPrivilegeLogicServerImpl{
		sc:                    sc,
		dyconfig:              dyConfig,
		knightprivilegeCli:    knightprivilege.NewClient(dopts...),
		knightgroupmembersCli: knightgroupmembersCli,
		knightgrouprankCli:    knightgrouprankCli,
		anchorcontractCli:     anchorcontractCli,
		guildCoopCli:          guildCoopCli,
		accountCli:            accountCli,
	}, nil
}

/*
const unsigned int CMD_GetKnightGroupEntry      = 3774; // 骑士团入口-个人资料卡/主播卡片
const unsigned int CMD_GetKnightGroupDetialInfo = 3775; // 骑士团特权页
const unsigned int CMD_GetKnightGroupOpenStatus = 3776; // 骑士团开通按钮
const unsigned int CMD_GetKnightCardInfo        = 3777; // 房间个人资料卡-获取骑士铭牌
*/

func (s *KnightPrivilegeLogicServerImpl) GetKnightGroupEntry(ctx context.Context, in *pb.GetKnightGroupEntryReq) (*pb.GetKnightGroupEntryResp, error) {
	out := &pb.GetKnightGroupEntryResp{}

	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetUserComplaintEntry ctx:%+v", ctx)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	uid := si.UserID
	channelId := in.GetChannelId()
	anchorUid := in.GetAnchorUid()

	log.DebugWithCtx(ctx, "GetKnightGroupEntry in uid %d chnl %d anchorUid %d", uid, channelId, anchorUid)

	if uid == 0 || anchorUid == 0 || channelId == 0 {
		log.Warnf("GetKnightGroupEntry miss parameter in %+v", in)
		return out, nil
	}

	// 测试白名单开放入口
	if !s.dyconfig.CheckWhiteAnchor(anchorUid) {
		// 签约合作库公会的主播 才展示这个入口
		signInfo, err := s.anchorcontractCli.GetUserContractCacheInfo(ctx, 0, anchorUid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetKnightGroupEntry failed to anchorcontractCli.GetUserContractCacheInfo in %+v err %+v", in, err)
			return out, err
		}

		if signInfo.GetContract().GetGuildId() == 0 || signInfo.GetContract().GetExpireTime() < uint32(time.Now().Unix()) {
			log.DebugWithCtx(ctx, "GetKnightGroupEntry not sign anchor uid %d in %+v", uid, in)
			return out, nil
		}

		// 合作库检查
		respGuildTypeInfo, err := s.guildCoopCli.GetGuildCooperationWithTime(ctx, signInfo.GetContract().GetGuildId(), time.Now().Unix())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetKnightGroupEntry failed to guildCoopCli.GetGuildCooperationWithTime uid %d in %+v err %+v", uid, in, err)
			return out, err
		}
		if !respGuildTypeInfo.GetIsExist() {
			log.ErrorWithCtx(ctx, "GetKnightGroupEntry uid %d in %+v, not golddiamonn, out %+v", uid, in, respGuildTypeInfo)
			return out, nil
		}
	}

	loveRankInfo, err := s.knightgrouprankCli.GetLoveRank(ctx, &knightgrouprankPB.GetLoveRankReq{ChannelId: channelId, AnchorUid: anchorUid, KnightUid: uid})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetKnightGroupEntry failed to knightgrouprankCli.GetLoveRank uid %d in %+v err %+v", uid, in, err)
		return out, err
	}
	log.DebugWithCtx(ctx, "GetKnightGroupEntry uid %d in %+v, GetLoveRank %+v", uid, in, loveRankInfo)

	memberResp, err := s.knightgroupmembersCli.GetKnightGroupMember(ctx, &knightgroupmembersPB.GetKnightGroupMemberReq{ChannelId: channelId, AnchorUid: anchorUid})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetKnightGroupEntry failed to knightgroupmembersCli.GetKnightGroupMember uid %d in %+v err %+v", uid, in, err)
		return out, err
	}
	log.DebugWithCtx(ctx, "GetKnightGroupEntry uid %d in %+v, GetKnightGroupMember %+v", uid, in, memberResp)

	memberCnt := memberResp.GetTotalMemberCnt()
	rankCnt := uint32(len(loveRankInfo.GetRankList()))
	out.IsShow = true
	out.MemberCnt = memberCnt

	if rankCnt > 0 {
		uids := []uint32{}
		for i := uint32(0); i < rankCnt && i < 3; i++ {
			uids = append(uids, loveRankInfo.GetRankList()[i].GetUid())
		}

		uid2info, err := s.accountCli.GetUsersMap(ctx, uids)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetKnightGroupEntry failed to accountCli.GetUsersMap uid %d in %+v err %+v", uid, in, err)
			return out, err
		}

		for _, uid := range uids {
			out.AccountList = append(out.AccountList, uid2info[uid].GetUsername())
		}
	}

	if uid == anchorUid {
		out.Title = "我的骑士团"
	} else {
		out.Title = "ta的骑士团"
	}

	log.InfoWithCtx(ctx, "GetKnightGroupEntry uid %d in %+v, out %v %s %d %v", uid, in, out.IsShow, out.Title, out.MemberCnt, out.AccountList)
	return out, nil
}

func (s *KnightPrivilegeLogicServerImpl) GetKnightGroupDetialInfo(ctx context.Context, in *pb.GetKnightGroupDetialInfoReq) (*pb.GetKnightGroupDetialInfoResp, error) {
	out := &pb.GetKnightGroupDetialInfoResp{}

	resp, err := s.knightprivilegeCli.GetKnightGroupDetialInfo(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetKnightGroupDetialInfo failed to knightprivilegeCli.GetKnightGroupDetialInfo in %+v err %+v", in, err)
		return out, err
	}

	for _, info := range resp.GetInfoList() {
		out.InfoList = append(out.InfoList, &pb.KnightGroupDetialInfo{
			PicUrl:  info.GetPicUrl(),
			Title:   info.GetTitle(),
			Content: info.GetContent(),
		})
	}

	log.DebugWithCtx(ctx, "GetKnightGroupDetialInfo in %+v", in)
	return out, nil
}

func (s *KnightPrivilegeLogicServerImpl) GetKnightGroupOpenStatus(ctx context.Context, in *pb.GetKnightGroupOpenStatusReq) (*pb.GetKnightGroupOpenStatusResp, error) {
	out := &pb.GetKnightGroupOpenStatusResp{}

	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetKnightGroupOpenStatus ctx:%+v", ctx)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	knightUid := si.UserID
	anchorUid := in.GetAnchorUid()
	channelId := in.GetChannelId()

	log.DebugWithCtx(ctx, "GetKnightGroupOpenStatus in uid %d chnl %d anchorUid %d", knightUid, channelId, anchorUid)

	if knightUid == 0 || channelId == 0 || anchorUid == 0 {
		log.Warnf("GetKnightGroupOpenStatus miss parameter in %+v uid %d", in, knightUid)
		return out, nil
	}

	// 拿守护主播列表
	resp, err := s.knightgroupmembersCli.GetKnightInfo(ctx, &knightgroupmembersPB.GetKnightInfoReq{KnightUid: knightUid, ChannelId: channelId})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetKnightGroupOpenStatus failed to knightgroupmembersCli.GetKnightInfo uid %d in %+v err %+v", knightUid, in, err)
		return out, err
	}

	// 判断用户有没有守护这个主播
	opStatus := uint32(pb.KnightOpenType_KNIGHT_NO_OPEN)
	ts := time.Now()
	for _, info := range resp.GetKnightInfoList() {
		if anchorUid == info.GetAnchorUid() && info.GetEndTime() > uint32(time.Now().Unix()) {
			opStatus = uint32(pb.KnightOpenType_KNIGHT_OPEN)
			ts = time.Unix(int64(info.GetEndTime()), 0)
			log.DebugWithCtx(ctx, "GetKnightGroupOpenStatus got anchor %+v", info)
			break
		}
	}
	expireTs := time.Date(ts.Year(), ts.Month(), ts.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, int(s.dyconfig.GetJoinKnightGroupDay()))

	content := ""
	if opStatus == uint32(pb.KnightOpenType_KNIGHT_NO_OPEN) {
		content = fmt.Sprintf("开通后，可以守护至%04d/%02d/%02d", expireTs.Year(), expireTs.Month(), expireTs.Day())
	} else {
		nowDay := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Local)
		lessDay := (ts.Unix()-nowDay.Unix())/86400 - 1
		expireTs = expireTs.AddDate(0, 0, -1)
		content = fmt.Sprintf("%d天后过期，续费可以守护至%04d/%02d/%02d", lessDay, expireTs.Year(), expireTs.Month(), expireTs.Day())
	}

	out.Status = opStatus
	out.Content = content
	out.TbeanPrice = s.dyconfig.GetJoinKnightGroupPrice()
	if whitePrice := s.dyconfig.GetWhiteUidPrice(knightUid); whitePrice > 0 { // 白名单价格 for test
		out.TbeanPrice = whitePrice
	}

	log.InfoWithCtx(ctx, "GetKnightGroupOpenStatus uid %d in %+v, out %d %d %s", knightUid, in, out.Status, out.TbeanPrice, out.Content)
	return out, nil
}

func (s *KnightPrivilegeLogicServerImpl) GetKnightCardInfo(ctx context.Context, in *pb.GetKnightCardInfoReq) (*pb.GetKnightCardInfoResp, error) {
	out := &pb.GetKnightCardInfoResp{}

	si, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetKnightGroupOpenStatus ctx:%+v", ctx)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	uid := si.UserID
	knightUid := in.GetTargetUid()
	channelId := in.GetChannelId()

	log.DebugWithCtx(ctx, "GetKnightCardInfo in uid %d chnl %d channelId %d", uid, knightUid, channelId)

	if knightUid == 0 || channelId == 0 {
		log.Warnf("GetKnightCardInfo miss parameter in %+v", in)
		return out, nil
	}

	resp, err := s.knightprivilegeCli.GetKnightPrivilegeInfo(ctx, knightUid, channelId, uint32(knightprivilegePB.RequestType_KNIGHT_CARD_RESOURCE_URL))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetKnightCardInfo failed to knightprivilegeCli.GetKnightPrivilegeInfo in %+v err %+v", in, err)
		return out, err
	}
	log.DebugWithCtx(ctx, "GetKnightCardInfo uid %d in %+v, GetKnightPrivilegeInfo resp %+v", uid, in, resp)

	if resp.GetStatus() == uint32(knightprivilegePB.KnightStatusType_KNIGHT_NO) {
		log.DebugWithCtx(ctx, "GetKnightCardInfo uid %d in %+v, not knight", uid, in)
		return out, nil
	}

	out.KnightNameplateResourceUrl = resp.GetKnightCardResourceUrl()

	log.DebugWithCtx(ctx, "GetKnightCardInfo in %+v, out %+v", in, out)
	return out, nil
}
