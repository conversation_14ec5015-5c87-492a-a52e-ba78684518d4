package server

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/pkg/deal_token"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/knightgrouplogic"
	"golang.52tt.com/protocol/common/status"
	knightpb "golang.52tt.com/protocol/services/knightgroupmembers"
	riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
	"golang.52tt.com/protocol/services/tt_rev_common"
	unifiedPayPB "golang.52tt.com/protocol/services/unified_pay"
	"golang.52tt.com/services/knight-group/knight-group-logic/client"
	conf "golang.52tt.com/services/knight-group/knight-group-logic/common/dyconf"
	"golang.52tt.com/services/knight-group/knight-group-logic/common/filter"
	"google.golang.org/grpc/codes"
	"strings"
	"time"
)

const appID = "TT_KNIGHT"

func genPayTradeNo(knightUid, anchorUid uint32, ts time.Time) string {
	return fmt.Sprintf("%v_%v_%v_%v", appID, knightUid, anchorUid, ts.UnixNano())
}

// riskCheckBeforeConsume 消费前风控检查
func (s *KnightGroupLogic_) riskCheckBeforeConsume(ctx context.Context, serviceInfo *protogrpc.ServiceInfo, req *knightgrouplogic.JoinKnightGroupReq) (*app.BaseResp, protocol.ServerError) {
	uid := serviceInfo.UserID
	baseResp := &app.BaseResp{}
	totalPrice, _, _, serr := conf.GetJoinGroupPara()
	if nil != serr {
		log.ErrorWithCtx(ctx, "riskCheckBeforeConsume GetJoinPrice req:%+v,  err:%v", req, serr)
		return baseResp, protocol.NewExactServerError(nil, status.ErrKnightGroupInvalidConf, serr.Error())
	}

	onlineInfo, err := s.userOnlineCli.GetLatestOnlineInfo(ctx, req.AnchorUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "riskCheckBeforeConsume fail to GetLatestOnlineInfo. err:%v", err)
		serr := protocol.ToServerError(err)
		return baseResp, serr
	}

	checkReq := &riskMngApiPb.CheckReq{
		Scene: "JOIN_KNIGHT",
		SourceEntity: &riskMngApiPb.Entity{
			// 必选
			Uid: uid,
			// 设备信息，如果 ctx 里面有 servceInfo，可以不填，风控媏自行查 ctx
			DeviceIdRaw:  serviceInfo.DeviceID,
			ClientIp:     serviceInfo.ClientIPAddr().String(),
			TerminalType: serviceInfo.TerminalType,
			// 可选，用户详细信息，若有现成的数据，建议填写，未来可能有用
			Phone: "",
			// 可选， 房间信息，若有现成的数据，建议填写，未来可能有用
			ChannelId:     req.ChannelId,
			ChannelViewId: "",
		},
		// 通用参数传递
		CustomParams: map[string]string{
			"rcv_uid":      fmt.Sprintf("%d", req.AnchorUid),
			"rcv_deviceId": strings.ToLower(onlineInfo.GetDeviceIdHex()),
			"rcv_ip":       onlineInfo.GetClientIp(),
			"consume_type": "1", // 1-"普通T豆消费"
			"scene_id":     fmt.Sprintf("%d", tt_rev_common.ConsumeSceneType_CONSUME_SCENE_TYPE_JOIN_KNIGHT),
			"amount":       fmt.Sprintf("%.2f", float64(totalPrice)/100.0),
			"room_id":      fmt.Sprintf("%d", req.ChannelId),
		},
	}
	checkResp, err := s.riskMngApiCli.CheckHelper(ctx, checkReq, req.BaseReq)
	if err != nil {
		// 系统错误，风控非关键路径，可忽略系统错误
		log.ErrorWithCtx(ctx, "riskCheckBeforeConsume risk-mng-api.Check failed, err:%v, req:%+v", err, checkReq)
		return baseResp, nil
	}
	// 命中风控拦截
	if checkResp.GetErrCode() < 0 {
		// 建议打个 info 拦截日志，方便排查，风控拦截日志不会很多
		log.InfoWithCtx(ctx, "riskCheckBeforeConsume risk-mng-api.Check hit, req:%+v, resp:%+v", checkReq, checkResp)
		// 需要返回 ErrInfo 给客户端
		baseResp.ErrInfo = checkResp.GetErrInfo()
		// 返回错误码给客户端，并设置 gRPC 错误码为 OK
		return baseResp, protocol.NewExactServerError(codes.OK, int(checkResp.GetErrCode()), checkResp.GetErrMsg())
	}
	// 无拦截
	return baseResp, nil
}

// commit 消费T豆，获得dealtoken
func (s *KnightGroupLogic_) commit(ctx context.Context, order *knightpb.JoinKnightOrder, user *account.User, body string) (string, protocol.ServerError) {
	uid := order.KnightUid
	outTradeNo := order.OrderId

	tbreanTime, dealToken, err := clientX.PayClient.UnfreezeAndConsume(ctx, &unifiedPayPB.UnfreezeAndConsumeReq{
		AppId:        appID,
		Uid:          uid,
		UserName:     user.GetUsername(),
		ItemId:       2000,
		ItemName:     body,
		ItemNum:      1,
		ItemPrice:    order.Price,
		TotalPrice:   order.Price,
		Platform:     "0",
		OutTradeNo:   outTradeNo,
		Notes:        "knight-group-logic",
		OutOrderTime: time.Now().Format("2006-01-02 15:04:05"),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "commit fail to UnfreezeAndConsume. uid:%v, outTradeNo:%v, err:%v", uid, outTradeNo, err)
		return "", err
	}
	serr := deal_token.Check(dealToken)
	if nil != serr {
		log.ErrorWithCtx(ctx, "commit deal_token.Check knight_uid:%v serr:%v", uid, serr)
		return dealToken, protocol.NewExactServerError(nil, -5872, serr.Error())
	}
	sendTime, perr := time.ParseInLocation("2006-01-02 15:04:05", tbreanTime, time.Local)
	if nil != perr {
		log.ErrorWithCtx(ctx, "commit tbean ctime ParseInLocation uid:%d, outTradeNo:%v, tbreanTime:%v  err:%v", uid, outTradeNo, tbreanTime, perr)
		return "", protocol.NewExactServerError(nil, status.ErrKnightGroupSysErr)
	}
	order.ServerTime = uint32(sendTime.Unix())
	log.InfoWithCtx(ctx, "commit ok uid:%v, outTradeNo:%v  tbeanTime:%v", uid, outTradeNo, tbreanTime)
	return dealToken, nil
}

func (s *KnightGroupLogic_) JoinKnightGroup(ctx context.Context, req *knightgrouplogic.JoinKnightGroupReq) (*knightgrouplogic.JoinKnightGroupResp, error) {
	log.DebugWithCtx(ctx, "JoinKnightGroup req:%v", req)
	resp := &knightgrouplogic.JoinKnightGroupResp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "JoinKnightGroup ServiceInfoFromContext fail. req:%+v", req)
		return resp, protocol.NewExactServerError(codes.InvalidArgument, status.ErrKnightGroupInvalidConf)
	}

	uid := serviceInfo.UserID
	var guildId, channelType uint32 = 0, 0
	err := filter.Build(filter.BuildContractFilter(req.AnchorUid, req.ChannelId, &guildId, &channelType),
		filter.BuildAnchorOnlineFilter(req.AnchorUid, req.KnightUid, req.ChannelId),
		filter.BuildUserFilter(serviceInfo.UserID, req.KnightUid, req.AnchorUid, req.ChannelId, false),
		filter.BuildYKWFilter(req.KnightUid),
		filter.BuildNobilityFilter(req.KnightUid, req.ChannelId)).Do(ctx)

	if nil != err {
		log.ErrorWithCtx(ctx, "JoinKnightGroup filter uid:%d, req:%v err:%v", uid, req, err)

		if !conf.TestNotFilter() {
			return resp, err
		}
	}
	// 风控检查
	resp.BaseResp, err = s.riskCheckBeforeConsume(ctx, serviceInfo, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "JoinKnightGroup riskCheckBeforeConsume uid:%d, req:%v err:%v", uid, req, err)
		return resp, err
	}

	price, _, body, serr := conf.GetJoinGroupPara()
	if nil != serr {
		log.ErrorWithCtx(ctx, "JoinKnightGroup GetJoinPrice uid:%v err:%v", uid, serr)
		return resp, protocol.NewExactServerError(nil, status.ErrKnightGroupSysErr, serr.Error())
	}
	if whitePrice := conf.GetWhiteUidPrice(req.KnightUid); whitePrice > 0 { // 白名单价格 for test
		price = whitePrice
	}
	user, err := clientX.AccountClient.GetUser(ctx, uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "JoinKnightGroup AccountClient GetUser knight_uid:%v err:%v", uid, err)
		return resp, err
	}

	nowTs := time.Now()
	outTradeNo := genPayTradeNo(req.KnightUid, req.AnchorUid, nowTs)
	outsideTime := nowTs.Format("2006-01-02 15:04:05")
	// 1. 冻结T豆
	_, err = clientX.PayClient.PresetFreeze(ctx, uid, price, appID, outTradeNo, outsideTime, body)
	if err != nil { // 失败，Notify里回滚
		log.ErrorWithCtx(ctx, "freezeAndCommit fail to PresetFreeze. uid:%v, outTradeNo:%v, outsideTime:%v, err:%v", uid, outTradeNo, outsideTime, err)
		if err.Code() == status.ErrTbeanNoEnoughBalance {
			err = protocol.NewExactServerError(nil, err.Code(), "T豆不足，请充值后再次开通")
		}
		return resp, err
	}
	// 2. 预下单
	order := &knightpb.JoinKnightOrder{
		ServerTime:  uint32(nowTs.Unix()),
		OrderId:     outTradeNo,
		KnightUid:   req.KnightUid,
		AnchorUid:   req.AnchorUid,
		ChannelId:   req.ChannelId,
		GuildId:     guildId,
		ChannelType: channelType,
		Price:       price,
	}
	_, err = clientX.KnightMemberClient.JoinKnightPreOrder(ctx, &knightpb.JoinKnightGroupReq{
		JoinOrder: order,
	})
	if nil != err { //失败，Notify里回滚
		log.ErrorWithCtx(ctx, "JoinKnightPreOrder server uid:%d, outTradeNo:%v, req:%v err:%v", uid, outTradeNo, req, err)
		return resp, err
	}

	// 3. 消费T豆，获得 dealToken
	dealToken, err := s.commit(ctx, order, user, body)
	if err != nil { // 若commit失败，Notify里回滚。 若commit成功,但接口超时，对账补单
		log.ErrorWithCtx(ctx, "JoinKnightGroup commit fail. uid:%v, outTradeNo:%v, err:%v", uid, outTradeNo, err)
		return resp, err
	}

	// 4.加入骑士团
	_, err = clientX.KnightMemberClient.JoinKnightGroup(ctx, &knightpb.JoinKnightGroupReq{
		JoinOrder: order,
		DealToken: dealToken,
	})
	if nil != err { // 对账补单
		log.ErrorWithCtx(ctx, "JoinKnightGroup server uid:%d,  req:%v err:%v", uid, req, err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "JoinKnightGroup uid:%d, outTradeNo:%v, req:%v resp:%v", uid, outTradeNo, req, resp)

	return resp, nil
}
