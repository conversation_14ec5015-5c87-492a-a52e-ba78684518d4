package controllers

import (
	"golang.52tt.com/pkg/commission"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	exchangePB "golang.52tt.com/protocol/services/exchange"
	"golang.52tt.com/services/exchange-logic/conf"
	"golang.52tt.com/services/exchange-logic/models"
	apiPB "golang.52tt.com/services/exchange-logic/models/gen-go"
	"time"
)

type PointsToCashCheckController struct {
	APIController
}

func GetCommissionClient(scoreType uint32) commission.Client {
	switch scoreType {
	case userScoreType:
		return models.CommissionClient
	case anchorScoreType:
		return models.AnchorCommissionClient
	case maskedPkScoreType:
		return models.MaskedPkCommissionClient
	case knightScoreType:
		return models.KnightCommissionClient
	case eSportScoreType:
		return models.EsportCommissionClient
	case userScoreTbeanOnlyType:
		return models.CommissionClient
	}

	return nil
}

//判断周结月结时间段，但不能判断是否已经提现过
func (c *PointsToCashCheckController) Get() {
	ctx := c.Context()
	uid := c.AuthInfo().UserID
	pointType := c.GetUInt32("pointType", 0)

	log.DebugfWithCtx(ctx, "PointsToCashCheckController check %+v", c.AuthInfo())

	// 检查黑名单限制时间
	inBlacklist, err := models.CheckUserIfInBlacklistV2(ctx, uid, uint32(exchangePB.AddBlacklistReq_PointToCash))
	if err != nil || inBlacklist {
		log.ErrorWithCtx(ctx, "PointsToCashCheckController fail. CheckUserIfInBlacklist uid(%d) err:%v", uid, err)
		c.ServeAPIJsonWithError(status.ErrExchangeCannotCash, "暂不可提现积分，请联系客服")
		return
	}

	/*inBlacklist := models.CheckUserIfInBlacklist(uid)
	if inBlacklist {
		log.ErrorWithCtx(ctx, "PointsToCashCheckController fail. CheckUserIfInBlacklist uid(%d)", uid)
		c.ServeAPIJsonWithError(-2, "暂不可进行积分提现")
		return
	}*/

	if pointType == userScoreType || pointType == userScoreTbeanOnlyType {
		uidList := []uint32{uid}

		typeMap,_, err := models.ScoreTypeMgr.GetScoreTypeMap(ctx, 0, 0, uidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "ScoreTypeMgr.GetScoreTypeMap err=%v", err)
			sErr := protocol.ToServerError(err)
			c.ServeAPIJsonWithError(sErr.Code(), sErr.Error())
			return
		}

		log.InfoWithCtx(ctx, "typeMap[uid]=%v", typeMap[uid])

		if typeMap[uid] > 0 {
			log.InfoWithCtx(ctx, "uid=%d score_type>0", uid)
			c.ServeAPIJsonWithError(status.ErrExchangeSignErr, "")
			return
		}
	}

	commissionCli := GetCommissionClient(pointType)
	if commissionCli == nil {
		log.ErrorWithCtx(ctx, "PointsExchange bad pointType uid(%d)", uid)
		c.ServeAPIJsonWithError(status.ErrExchangeParamErr)
		return
	}

	resp, err := commissionCli.GetEncashment(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "PointsExchange GetEncashment failed")
		c.ServeAPIJsonWithError(status.ErrCommissionApiRespError)
		return
	}

	//检查银行卡是否异常
	err = commissionCli.CheckEncashBlock(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommissionClient.CheckEncashBlock uid(%d) %+v", uid, err)

		if e, ok := err.(commission.APIError); ok && e.Code() == commission.CodeErrorBindCard {
			c.ServeAPIJsonWithError(status.ErrCommissionUserBankInfoError)
			return
		}

		c.ServeAPIJsonWithError(status.ErrCommissionApiRespError, err.Error())
		return
	}

	result := &apiPB.CheckEncashmentResult{}

	isEveryDay := conf.UidInEveryDay(uid)

	var canCashByWeek bool
	if !isEveryDay {
		//周结or月结
		canCashByWeek = checkIfCanCashByWeek(pointType)
		result.WithdrawType = 1

		if canCashByWeek {
			if !checkTodayCanCashByWeekend(ctx) {
				//今天不能提现
				log.DebugfWithCtx(ctx, "today can not cash uid(%d) by week", uid)
				result.DateStatus = 0
				c.ServeAPIJson(result)
				return
			}
		} else {
			if !checkTodayCanCashByMonth() {
				//今天不能提现
				log.DebugfWithCtx(ctx, "today can not cash uid(%d) by month", uid)
				result.DateStatus = 0
				c.ServeAPIJson(result)
				return
			}
		}
	}

	log.DebugfWithCtx(ctx, "today can cash uid(%d)", uid)

	//今天可以提现的
	result.DateStatus = 1

	//半年内无交易记录
	if len(resp.Rows) == 0 {
		result.WithdrawStatus = 0
		c.ServeAPIJson(result)
		return
	}

	//半年内有交易记录
	var tm time.Time
	for _, row := range resp.Rows {
		tm, _ = time.Parse("2006-01-02 15:04:05", row.CreateTime)
		break
	}

	if isEveryDay {
		result.WithdrawStatus = 0
	} else {
		//允许周提的
		if canCashByWeek {
			result.WithdrawType = 1
			year, week := tm.ISOWeek()
			nowY, nowW := time.Now().ISOWeek()

			if year == nowY && week == nowW {
				//本周已经提现过
				result.WithdrawStatus = 1
				log.InfoWithCtx(ctx, "this week already cash uid(%d) week(%d)", uid, week)
			} else {
				result.WithdrawStatus = 0
			}
		} else {
			//月提
			year, month, _ := tm.Date()
			nowY, nowM, _ := time.Now().Date()

			if year == nowY && month == nowM {
				//本月提现过
				result.WithdrawStatus = 1
				log.InfoWithCtx(ctx, "this month already cash uid(%d) month(%v)", uid, month)
			} else {
				result.WithdrawStatus = 0
			}
		}
	}

	c.ServeAPIJson(result)
}

func (c *PointsToCashCheckController) Post() {
}
