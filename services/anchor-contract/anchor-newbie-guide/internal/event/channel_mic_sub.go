package event

import (
	"context"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkasimplemic"
)

type ChannelMicEventLinkSub struct {
	channelMicEvent subscriber.Subscriber
	handle          func(ctx context.Context, e *kafkasimplemic.SimpleMicEvent) error
}

func NewChannelMicEventLinkSub(ctx context.Context, kfkConf *config.KafkaConfig, handle func(ctx context.Context, e *kafkasimplemic.SimpleMicEvent) error) (*ChannelMicEventLinkSub, error) {
	e := ChannelMicEventLinkSub{}
	cfg := kafka.DefaultConfig()
	cfg.ClientID = kfkConf.ClientID
	cfg.Consumer.Offsets.Initial = kafka.OffsetNewest
	cfg.Consumer.Return.Errors = true
	kafkaSub, err := kafka.NewSubscriber(kfkConf.BrokerList(), cfg, subscriber.WithMaxRetryTimes(5))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewChannelMicEventLinkSub NewSubscriber err", err)
		panic(err)
	}
	err = kafkaSub.SubscribeContext(kfkConf.GroupID, kfkConf.TopicList(), subscriber.ProcessorContextFunc(e.handlerChannelMicEvent))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewChannelMicEventLinkSub SubscribeContext err", err)
		panic(err)
	}
	e.channelMicEvent = kafkaSub
	e.handle = handle
	return &e, nil
}

func (s *ChannelMicEventLinkSub) handlerChannelMicEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	e := &kafkasimplemic.SimpleMicEvent{}
	err := proto.Unmarshal(msg.Value, e)
	if err != nil {
		log.Errorf(" handlerChannelMicEvent proto.Unmarshal err:%s", err)
		return err, false
	}

	log.Debugf("handlerChannelMicEvent %+v", e)
	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()

	err = s.handle(ctx, e)
	if err != nil {
		log.Errorf("HandlerChannelMicEvent ChannelMicEventHandle err:%v", err)
		return err, true
	}
	return nil, false
}

func (s *ChannelMicEventLinkSub) Close() {
	if s == nil || s.channelMicEvent == nil {
		return
	}
	_ = s.channelMicEvent.Stop()
}
