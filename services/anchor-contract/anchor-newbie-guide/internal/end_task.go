package internal

import (
	"context"
	"fmt"
	"math/rand"
	"sort"
	"time"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/anchor-newbie-guide"
	anchorcontract_go "golang.52tt.com/protocol/services/anchorcontract-go"
	userpresent_go "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/anchor-contract/anchor-newbie-guide/internal/cache"
)

// 结束直播后的引导

const (
	UserTypePresent     = 1 // 送礼
	UserTypeDanmu       = 2 // 发弹幕
	UserTypeStayChannel = 3 // 在房
)

func (s *Server) GetChannelLiveEndGuidePopup(ctx context.Context, request *pb.GetChannelLiveEndGuidePopupRequest) (*pb.GetChannelLiveEndGuidePopupResponse, error) {
	resp := &pb.GetChannelLiveEndGuidePopupResponse{}

	uid := request.GetUid()
	// 先查用户最后一场
	liveId, err := s.cache.GetLiveIdByUid(ctx, int64(request.GetUid()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveEndGuidePopup GetLiveIdByUid failed req %v err %v", request, err)
		return resp, err
	}

	// 检查是否有结束任务
	hasTask, err := s.cache.CheckLiveEndTask(ctx, liveId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveEndGuidePopup CheckLiveEndTask failed liveId %d err %v", liveId, err)
		return resp, err
	}

	if hasTask {
		log.DebugWithCtx(ctx, "GetChannelLiveEndGuidePopup no live end task for liveId %d", liveId)
		return resp, nil // 已经获取过了，直接返回
	}

	// 获取用户是否是新主播
	isNewbieResp, err := s.rpc.GetIsNewbieAnchor(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitNewbieAnchorGuideTask GetIsNewbieAnchor failed uid %d err %v", uid, err)
		return resp, err
	}

	isNewBie := false
	for _, item := range isNewbieResp.IdentityList {
		if item.GetIdentityType() == uint32(anchorcontract_go.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) && item.ObtainDay <= 30 {
			isNewBie = item.GetIsNewbie()
		}
	}

	if s.dyconf.GetDyConf().TestEndTask[uid] != 0 {
		isNewBie = true // 测试uid下强制认为是新主播
		log.InfoWithCtx(ctx, "GetChannelLiveEndGuidePopup TestEndTask %d isNewbie %v", uid, isNewBie)
	}

	if !isNewBie {
		log.DebugWithCtx(ctx, "InitNewbieAnchorGuideTask user %d is not newbie anchor, skip task init", uid)
		return resp, nil // 如果不是新主播，直接返回
	}

	// 获取活跃观众

	userList, err := s.cache.GetActiveUsers(ctx, uint32(liveId))
	if err != nil {
		log.ErrorWithCtx(ctx, "InitNewbieAnchorGuideTask GetActiveUsers failed liveId %d err %v", liveId, err)
		return resp, err
	}

	// 排除自己
	for i := 0; i < len(userList); i++ {
		if userList[i] == uid {
			userList = append(userList[:i], userList[i+1:]...)
			break
		}
	}

	// 获取进房时间，避免下播的时候，在房用户时间还没加上
	enterMap, err := s.cache.BatchGetChannelEnter(ctx, uint32(liveId), userList)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitNewbieAnchorGuideTask BatchGetChannelEnter failed liveId %d err %v", liveId, err)
		return resp, err
	}

	// 获取活跃观众信息
	activeMap, err := s.cache.BatchGetUserInteractionRecordDetail(ctx, uint32(liveId), userList)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitNewbieAnchorGuideTask BatchGetUserInteractionRecordDetail failed liveId %d err %v", liveId, err)
		return resp, err
	}

	for uid, item := range enterMap {
		for _, active := range activeMap[uid] {
			if active.InteractionType == UserTypeStayChannel && item != 0 {
				active.Count += int(time.Now().Unix() - item)
				log.InfoWithCtx(ctx, "InitNewbieAnchorGuideTask user %d stay time updated to %d minutes", uid, active.Count)
				continue
			}
		}

		if activeMap[uid] == nil {
			activeMap[uid] = make([]*cache.UserInteractionRecord, 0)
		}

		// 如果没有停留记录，则添加一个停留记录
		activeMap[uid] = append(activeMap[uid], &cache.UserInteractionRecord{
			InteractionType: UserTypeStayChannel,
			Count:           int(time.Now().Unix() - item),
			Price:           0, // 停留不涉及价格
		})

		log.InfoWithCtx(ctx, "InitNewbieAnchorGuideTask user %d enter time %d, stay time %d minutes", uid, item, activeMap[uid][0].Count)
	}

	// 过滤当日接收建联任务发送的消息条数上限的用户
	activeMap, err = s.filterSendLimitUser(ctx, activeMap)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitNewbieAnchorGuideTask filterSendLimitUser failed liveId %d err %v", liveId, err)
		return resp, err
	}

	finalMap := s.GetUserActiveTypeAndValue(activeMap)
	finalUserList := make([]uint32, 0)
	for userId, _ := range finalMap {
		finalUserList = append(finalUserList, userId)
	}

	if len(finalMap) < int(s.dyconf.GetDyConf().ActiveUserLimit) {
		resp.PopupType = uint32(pb.ChannelLiveEndGuidePopupType_CHANNEL_LIVE_END_GUIDE_POPUP_TYPE_ANCHOR_RECOMMEND)

		log.DebugWithCtx(ctx, "InitNewbieAnchorGuideTask no enough active users for liveId %d", liveId)
		_, _, tag, err := s.rpc.GetCertAndTag(ctx, uid, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "InitNewbieAnchorGuideTask GetCertAndTag failed uid %d err %v", uid, err)
			return resp, err
		}

		recommendUidList, err := s.GetRecommendAnchor(ctx, uid, tag) // 获取推荐主播
		if err != nil {
			log.ErrorWithCtx(ctx, "InitNewbieAnchorGuideTask GetRecommendAnchor failed uid %d err %v", uid, err)
			return resp, err
		}

		if len(recommendUidList) < 3 {
			log.DebugWithCtx(ctx, "InitNewbieAnchorGuideTask not enough recommend anchors for uid %d, got %d, need at least 3", uid, len(recommendUidList))
			resp.PopupType = uint32(pb.ChannelLiveEndGuidePopupType_CHANNEL_LIVE_END_GUIDE_POPUP_TYPE_INVALID_UNSPECIFIED)
			return resp, nil // 如果推荐主播不足3个，直接返回
		}

		cidMap, certMap, accountMap := s.rpc.BatchGetAnchorExtInfo(ctx, request.GetUid(), recommendUidList)

		for _, uid := range recommendUidList {
			resp.AnchorInfoList = append(resp.AnchorInfoList, &pb.RecommendAnchorInfo{
				Uid:         uid,
				ChannelId:   cidMap[uid],
				Account:     accountMap[uid].GetUsername(),
				Nickname:    accountMap[uid].GetNickname(),
				CertUrlList: certMap[uid],
				IsUserOl:    true,
			})
		}

		err = s.cache.SetLiveEndTask(ctx, liveId)
		if err != nil {
			log.ErrorWithCtx(ctx, "InitNewbieAnchorGuideTask SetLiveEndTask failed liveId %d err %v", liveId, err)
		}
		return resp, nil
	}

	// 查询在线状态以便后续筛选
	onlineMap, err := s.rpc.BatchGetUserOnlineStatus(ctx, finalUserList)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitNewbieAnchorGuideTask BatchGetUserOnlineStatus failed liveId %d err %v", liveId, err)
		return resp, err
	}
	log.InfoWithCtx(ctx, "InitNewbieAnchorGuideTask BatchGetUserOnlineStatus success liveId %d onlineMap %v", liveId, onlineMap)

	finalUserList = s.sortUserInfo(finalUserList, finalMap, onlineMap)

	// 取前五
	if len(finalUserList) > 5 {
		finalUserList = finalUserList[:5]
	}

	// 获取用户扩展信息
	nobilityMap, tagMap, accountMap := s.rpc.BatchGetUserExtInfo(ctx, uid, finalUserList)

	resp = &pb.GetChannelLiveEndGuidePopupResponse{
		PopupType:    uint32(pb.ChannelLiveEndGuidePopupType_CHANNEL_LIVE_END_GUIDE_POPUP_TYPE_USER_INFO),
		UserInfoList: make([]*pb.InteractionUserInfo, 0),
	}

	for _, userId := range finalUserList {
		resp.UserInfoList = append(resp.UserInfoList, &pb.InteractionUserInfo{
			Uid:                   userId,
			Account:               accountMap[userId].GetUsername(),
			Nickname:              accountMap[userId].GetNickname(),
			NobilityLevel:         nobilityMap[userId],
			TagList:               tagMap[userId],
			InteractionButtonText: fmt.Sprintf(s.dyconf.GetDyConf().ActiveButtonText[uint32(int(finalMap[userId].InteractionType))], finalMap[userId].Count),
			IsUserOl:              onlineMap[userId],
		})
	}

	err = s.cache.SetLiveEndTask(ctx, liveId)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitNewbieAnchorGuideTask SetLiveEndTask failed liveId %d uid %d err %v", liveId, uid, err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "GetChannelLiveEndGuidePopup success liveId %d uid %d resp %v", liveId, uid, resp)
	return resp, nil
}

func (s *Server) SendChannelLiveThanks(ctx context.Context, request *pb.SendChannelLiveThanksRequest) (*pb.SendChannelLiveThanksResponse, error) {
	resp := &pb.SendChannelLiveThanksResponse{}

	// 先查用户最后一场
	liveId, err := s.cache.GetLiveIdByUid(ctx, int64(request.GetFromUid()))
	if err != nil {
		log.ErrorWithCtx(ctx, "SendChannelLiveThanks GetLiveIdByUid failed req %v err %v", request, err)
		return resp, err
	}

	// 确认有没有发过
	triggerMap, err := s.cache.GetTriggerMark(ctx, uint32(liveId), request.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "SendChannelLiveThanks GetTriggerMark failed liveId %d req %v err %v", liveId, request, err)
		return resp, err
	}

	// 获取一下当日发放次数
	countMap, err := s.cache.BatchGetTriggerCount(ctx, request.GetFromUid(), request.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "SendChannelLiveThanks BatchGetTriggerCount failed liveId %d req %v err %v", liveId, request, err)
		return resp, err
	}

	// 获取用户类型

	addMap := make(map[uint32]uint32)
	triggerList := make([]uint32, 0, len(request.GetUidList()))
	for _, item := range request.GetUidList() {
		if triggerMap[item] == false {
			userType, err := s.cache.GetUserInteractionRecordType(ctx, uint32(liveId), item)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendChannelLiveThanks GetUserInteractionRecordType failed liveId %d uid %d err %v", liveId, item, err)
				continue
			}

			if countMap[item] == 0 {
				firstRand := rand.Intn(5) + 1 // 第一次随机次数
				addMap[item] = uint32(firstRand)
				countMap[item] = int64(firstRand)
			} else {
				addMap[item] = 1 // 非第一次，直接加1
				countMap[item] += 1
			}

			switch userType {
			case UserTypePresent:
				err = s.rpc.SendThanksAssistantIm(ctx, request.GetFromUid(), item, s.dyconf.GetDyConf().ThanksTextPresent[int(countMap[item])%len(s.dyconf.GetDyConf().ThanksTextPresent)])
			case UserTypeDanmu:
				err = s.rpc.SendThanksAssistantIm(ctx, request.GetFromUid(), item, s.dyconf.GetDyConf().ThanksTextMsg[int(countMap[item])%len(s.dyconf.GetDyConf().ThanksTextMsg)])
			case UserTypeStayChannel:
				err = s.rpc.SendThanksAssistantIm(ctx, request.GetFromUid(), item, s.dyconf.GetDyConf().ThanksTextStayChannel[int(countMap[item])%len(s.dyconf.GetDyConf().ThanksTextStayChannel)])
			}

			if err != nil {
				log.ErrorWithCtx(ctx, "SendChannelLiveThanks SendThanksAssistantIm failed liveId %d uid %d err %v", liveId, item, err)
				continue
			}

			// 记录触发
			triggerList = append(triggerList, item)

		}
	}

	// 记录触发
	if err := s.cache.SetTriggerMark(ctx, uint32(liveId), triggerList); err != nil {
		log.ErrorWithCtx(ctx, "SendChannelLiveThanks SetTriggerMark failed liveId %d uid %d err %v", liveId, triggerList, err)
		return resp, err
	}

	// 批量设置当天触发次数
	if err := s.cache.BatchAddTriggerCount(ctx, request.GetFromUid(), addMap); err != nil {
		log.ErrorWithCtx(ctx, "SendChannelLiveThanks BatchSetTriggerCount failed liveId %d uid %d err %v", liveId, request.GetFromUid(), err)
		return resp, err
	}

	// 批量设置用户当天点击次数
	if err := s.cache.BatchAddUidSend(ctx, triggerList); err != nil {
		log.ErrorWithCtx(ctx, "SendChannelLiveThanks BatchAddUidSend failed liveId %d uid %d target %v err %v", liveId, request.GetFromUid(), triggerList, err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "SendChannelLiveThanks success liveId %d fromUid %d toUidList %v", liveId, request.GetFromUid(), request.GetUidList())
	return resp, nil

}

func (s *Server) sortUserInfo(uidList []uint32, userInfoMap map[uint32]*cache.UserInteractionRecord, olMap map[uint32]bool) []uint32 {

	//活跃用户定义：本场直播中，满足以下任一行为的用户
	//送出礼物次数 ≥1次（含付费礼物、免费礼物、红钻礼物）
	//发送弹幕次数 ≥3次（含公屏文字消息、图片消息、魔法表情及互动表情等）
	//本场直播累计观看时长 ≥1分钟

	//  - 活跃行为优先级：送礼＞弹幕＞停留

	log.InfoWithCtx(context.Background(), "sortUserInfo start uidList %v userInfoMap %v olMap %v", uidList, userInfoMap, olMap)

	sort.Slice(uidList, func(i, j int) bool {
		// 在线是最优先的
		if olMap[uidList[i]] != olMap[uidList[j]] {
			if olMap[uidList[i]] {
				return true // 在线用户排在前面
			}
			return false // 离线用户排在后面
		}

		// 不同类型用户排序
		if userInfoMap[uidList[i]].InteractionType != userInfoMap[uidList[j]].InteractionType {

			iPresent := userInfoMap[uidList[i]].InteractionType == UserTypePresent && userInfoMap[uidList[i]].Count >= int(s.dyconf.GetDyConf().ActivePresentCnt)
			jPresent := userInfoMap[uidList[j]].InteractionType == UserTypePresent && userInfoMap[uidList[j]].Count >= int(s.dyconf.GetDyConf().ActivePresentCnt)

			iDanmu := userInfoMap[uidList[i]].InteractionType == UserTypeDanmu && userInfoMap[uidList[i]].Count >= int(s.dyconf.GetDyConf().ActiveMsgCnt)
			jDanmu := userInfoMap[uidList[j]].InteractionType == UserTypeDanmu && userInfoMap[uidList[j]].Count >= int(s.dyconf.GetDyConf().ActiveMsgCnt)

			iStay := userInfoMap[uidList[i]].InteractionType == UserTypeStayChannel && userInfoMap[uidList[i]].Count >= int(s.dyconf.GetDyConf().ActiveStayMin*60)
			jStay := userInfoMap[uidList[j]].InteractionType == UserTypeStayChannel && userInfoMap[uidList[j]].Count >= int(s.dyconf.GetDyConf().ActiveStayMin*60)

			if iPresent != jPresent {
				return iPresent // 送礼用户排在前面
			}

			if iDanmu != jDanmu {
				return iDanmu // 弹幕用户排在前面
			}

			if iStay != jStay {
				return iStay // 停留用户排在前面
			}
		}

		if userInfoMap[uidList[i]].InteractionType == UserTypePresent {
			if userInfoMap[uidList[i]].Price != userInfoMap[uidList[j]].Price {
				return userInfoMap[uidList[i]].Price > userInfoMap[uidList[j]].Price // 价格多的排在前面
			}
		}

		return userInfoMap[uidList[i]].Count > userInfoMap[uidList[j]].Count // 数量多的排在前面
	})

	return uidList
}

func (s *Server) HandleLiveStart(ctx context.Context, uid, liveId, channelId uint32) error {
	// 添加对应关系
	err := s.cache.SetUidLive(ctx, int64(uid), int64(liveId))
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleLiveStrat SetUidLive failed uid %d liveId %d err %v", uid, liveId, err)
		return err
	}

	// 获取品类信息和标识信息

	level, examine, tag, err := s.rpc.GetCertAndTag(ctx, uid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleLiveStrat GetCertAndTag failed uid %d channelId %d err %v", uid, channelId, err)
		return err
	}

	log.InfoWithCtx(ctx, "HandleLiveStrat GetCertAndTag success uid %d channelId %d level %v examine %v tag %d", uid, channelId, level, examine, tag)

	// 获取主播积分
	score := getAnchorScore(level.GetLevel(), examine.GetLevel(), tag)

	// 设置主播积分
	if err := s.cache.SetUidScore(ctx, int64(uid), int64(score)); err != nil {
		log.ErrorWithCtx(ctx, "HandleLiveStrat SetUidScore failed uid %d score %d err %v", uid, score, err)
		return err
	}

	log.InfoWithCtx(ctx, "HandleLiveStrat success liveId %d", liveId)
	return nil
}

func (s *Server) HandleLiveEnd(ctx context.Context, uid, liveId uint32) error {
	// 删除主播积分
	if err := s.cache.DelUidScore(ctx, int64(uid)); err != nil {
		log.ErrorWithCtx(ctx, "HandleLiveEnd SetUidScore failed uid %d err %v", uid, err)
		return err
	}

	log.InfoWithCtx(ctx, "HandleLiveEnd success liveId %d", liveId)
	return nil
}

type UidScore struct {
	Uid   uint32
	Score int64
}

// GetRecommendAnchor 获取推荐主播
func (s *Server) GetRecommendAnchor(ctx context.Context, uid, tagId uint32) ([]uint32, error) {
	// 获取全部主播与积分
	score, err := s.cache.GetAllUidScores(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendAnchor GetUidScore failed tagId %d err %v", tagId, err)
		return nil, err
	}

	// 按积分排序，但是同品类要考虑优先级
	for uid, anchorScore := range score {
		tmpTagId := uint32((anchorScore % 100000) / 10)
		if tmpTagId != tagId {
			score[uid] = anchorScore - int64(tmpTagId*10) // 去掉品类的影响
		}
	}

	// 按积分排序
	anchorList := make([]UidScore, 0, len(score))
	for tmp, anchorScore := range score {
		// 排除自己
		if uid == uint32(tmp) {
			continue
		}
		anchorList = append(anchorList, UidScore{
			Uid:   uint32(tmp),
			Score: anchorScore,
		})
	}

	sort.Slice(anchorList, func(i, j int) bool {
		if anchorList[i].Score == anchorList[j].Score {
			return anchorList[i].Uid < anchorList[j].Uid // 如果积分相同，按UID升序
		}
		return anchorList[i].Score > anchorList[j].Score // 按积分降序
	})

	// 取前5个主播
	uidList := make([]uint32, 0, 5)
	for i := 0; i < len(anchorList) && i < 5; i++ {
		uidList = append(uidList, anchorList[i].Uid)
	}

	return uidList, nil
}

func (s *Server) HandlePresentEvent(ctx context.Context, anchorUid, sendUid, priceType, price uint32) error {
	log.InfoWithCtx(ctx, "HandlePresentEvent anchorUid %d sendUid %d priceType %d price %d", anchorUid, sendUid, priceType, price)
	liveId, err := s.cache.GetLiveIdByUid(ctx, int64(anchorUid))
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlePresentEvent GetLiveIdByUid failed uid %d err %v", anchorUid, err)
		return err
	}

	if liveId == 0 {
		log.DebugWithCtx(ctx, "HandlePresentEvent no live for uid %d", anchorUid)
		return nil // 如果没有直播，直接返回
	}

	isUkw, err := s.rpc.CheckIsUkw(ctx, uint32(sendUid))
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlePresentEvent CheckIsUkw failed sendUid %d err %v", sendUid, err)
		return err
	}

	if isUkw {
		log.InfoWithCtx(ctx, "HandlePresentEvent sendUid %d is UKW, skip", sendUid)
		return nil // UKW用户不计入活跃用户
	}

	err = s.cache.IncreaseUserGiftCount(ctx, uint32(liveId), sendUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlePresentEvent IncreaseUserGiftCount failed liveId %d toUid %d err %v", liveId, sendUid, err)
		return err
	}

	if priceType == uint32(userpresent_go.PresentPriceType_PRESENT_PRICE_TBEAN) {
		err = s.cache.IncreaseUserGiftPrice(ctx, uint32(liveId), sendUid, int64(price))
		if err != nil {
			log.ErrorWithCtx(ctx, "HandlePresentEvent IncreaseUserGiftPrice failed liveId %d toUid %d err %v", liveId, sendUid, err)
			return err
		}
	}

	// 记录用户行为
	if err := s.cache.AddActiveUser(ctx, uint32(liveId), sendUid); err != nil {
		log.ErrorWithCtx(ctx, "HandlePresentEvent AddActiveUser failed liveId %d toUid %d err %v", liveId, sendUid, err)
		return err
	}

	return nil
}

func (s *Server) HandleMsgEvent(ctx context.Context, uid, cid uint32) error {
	log.InfoWithCtx(ctx, "HandleMsgEvent uid %d cid %d", uid, cid)
	anchorUid, err := s.rpc.GetUidByChannelId(ctx, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleMsgEvent GetUidByChannelId failed cid %d err %v", cid, err)
		return err
	}

	liveId, err := s.cache.GetLiveIdByUid(ctx, int64(anchorUid))
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleMsgEvent GetLiveIdByUid failed uid %d err %v", anchorUid, err)
		return err
	}

	if liveId == 0 {
		log.DebugWithCtx(ctx, "HandleMsgEvent no live for uid %d", anchorUid)
		return nil // 如果没有直播，直接返回
	}

	isUkw, err := s.rpc.CheckIsUkw(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlePresentEvent CheckIsUkw failed sendUid %d err %v", uid, err)
		return err
	}

	if isUkw {
		log.InfoWithCtx(ctx, "HandlePresentEvent sendUid %d is UKW, skip", uid)
		return nil // UKW用户不计入活跃用户
	}

	err = s.cache.IncreaseUserDanmuCount(ctx, uint32(liveId), uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleMsgEvent IncreaseUserGiftCount failed liveId %d toUid %d err %v", liveId, uid, err)
		return err
	}

	// 记录用户行为
	if err := s.cache.AddActiveUser(ctx, uint32(liveId), uid); err != nil {
		log.ErrorWithCtx(ctx, "HandleMsgEvent AddActiveUser failed liveId %d toUid %d err %v", liveId, uid, err)
		return err
	}

	return nil
}

func (s *Server) HandleChannelEvent(ctx context.Context, uid, cid uint32, isEnter bool) error {
	log.InfoWithCtx(ctx, "HandleChannelEvent uid %d cid %d isEnter %v", uid, cid, isEnter)
	anchorUid, err := s.rpc.GetUidByChannelId(ctx, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleChannelEvent GetUidByChannelId failed cid %d err %v", cid, err)
		return err
	}

	liveId, err := s.cache.GetLiveIdByUid(ctx, int64(anchorUid))
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleChannelEvent GetLiveIdByUid failed uid %d err %v", anchorUid, err)
		return err
	}

	if liveId == 0 {
		log.DebugWithCtx(ctx, "HandleChannelEvent no live for uid %d", anchorUid)
		return nil // 如果没有直播，直接返回
	}

	isUkw, err := s.rpc.CheckIsUkw(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlePresentEvent CheckIsUkw failed sendUid %d err %v", uid, err)
		return err
	}

	if isUkw {
		log.InfoWithCtx(ctx, "HandlePresentEvent sendUid %d is UKW, skip", uid)
		return nil // UKW用户不计入活跃用户
	}

	if isEnter {
		err = s.cache.SetChannelEnter(ctx, uint32(liveId), uid, uint32(time.Now().Unix()))
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleChannelEvent SetChannelEnter failed liveId %d toUid %d err %v", liveId, uid, err)
			return err
		}

		// 记录用户行为
		if err := s.cache.AddActiveUser(ctx, uint32(liveId), uid); err != nil {
			log.ErrorWithCtx(ctx, "HandleChannelEvent AddActiveUser failed liveId %d toUid %d err %v", liveId, uid, err)
			return err
		}
	} else {
		enterTs, err := s.cache.GetChannelEnter(ctx, uint32(liveId), uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleChannelEvent GetChannelEnter failed liveId %d toUid %d err %v", liveId, uid, err)
			return err
		}

		if enterTs == 0 {
			log.DebugWithCtx(ctx, "HandleChannelEvent no enter record for liveId %d toUid %d", liveId, uid)
			return nil // 如果没有进房记录，直接返回
		}

		stayDuration := uint32(time.Now().Unix() - int64(enterTs))
		err = s.cache.IncreaseUserStayDuration(ctx, uint32(liveId), uid, int64(stayDuration))
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleChannelEvent IncreaseUserStayDuration failed liveId %d toUid %d err %v", liveId, uid, err)
			return err
		}

		// 删掉进房
		err = s.cache.DeleteChannelEnter(ctx, uint32(liveId), uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleChannelEvent DelChannelEnter failed liveId %d toUid %d err %v", liveId, uid, err)
			return err
		}

	}

	return nil
}

func (s *Server) HandleChannelLiveEventEndTask(ctx context.Context, anchorUid, liveId, cid uint32, isBegin bool) error {
	log.InfoWithCtx(ctx, "HandleChannelLiveEventEndTask anchorUid %d liveId %d", anchorUid, liveId)

	if isBegin {
		err := s.cache.SetUidLive(ctx, int64(anchorUid), int64(liveId))
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleChannelLiveEventEndTask SetUidLive failed uid %d liveId %d err %v", anchorUid, liveId, err)
			return err
		}

		err = s.HandleLiveStart(ctx, anchorUid, liveId, cid)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleChannelLiveEventEndTask HandleLiveStart failed uid %d liveId %d err %v", anchorUid, liveId, err)
			return err
		}
	} else {
		err := s.HandleLiveEnd(ctx, anchorUid, liveId)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleChannelLiveEventEndTask HandleLiveEnd failed uid %d liveId %d err %v", anchorUid, liveId, err)
			return err
		}
	}

	return nil
}

// 推荐主播的积分
// 积分计算：星级等级*10000+品类*10+认证等级

func getAnchorScore(level, examineLevel, tagId uint32) uint32 {
	score := level * 100000 // 星级等级
	if tagId > 0 {
		score += tagId * 10 // 品类
	}
	score += examineLevel // 认证等级
	return score
}

func (s *Server) GetUserActiveTypeAndValue(activeMap map[uint32][]*cache.UserInteractionRecord) map[uint32]*cache.UserInteractionRecord {
	finalActiveMap := make(map[uint32]*cache.UserInteractionRecord)

	for uid, records := range activeMap {
		// 按类型升序
		sort.Slice(records, func(i, j int) bool {
			return records[i].InteractionType < records[j].InteractionType
		})
		for _, record := range records {
			log.InfoWithCtx(context.TODO(), "GetUserActiveTypeAndValue uid %d record %v", uid, record)
			if record.InteractionType == UserTypePresent && uint32(record.Count) >= s.dyconf.GetDyConf().ActivePresentCnt {
				finalActiveMap[uid] = record
				break
			}

			if record.InteractionType == UserTypeDanmu && uint32(record.Count) >= s.dyconf.GetDyConf().ActiveMsgCnt {
				finalActiveMap[uid] = record
				break
			}

			if record.InteractionType == UserTypeStayChannel && uint32(record.Count) >= (s.dyconf.GetDyConf().ActiveStayMin*60) {
				record.Count = record.Count / 60 // 转换为分钟
				finalActiveMap[uid] = record
				break
			}
		}
	}

	return finalActiveMap
}

func (s *Server) filterSendLimitUser(ctx context.Context, activeMap map[uint32][]*cache.UserInteractionRecord) (map[uint32][]*cache.UserInteractionRecord, error) {
	// 过滤当日接收建联任务发送的消息条数上限的用户
	limit := s.dyconf.GetDyConf().UserSendLimit
	if limit == 0 {
		limit = 5
	}

	uidList := make([]uint32, 0, len(activeMap))
	for uid, _ := range activeMap {
		uidList = append(uidList, uid)
	}

	// 批量获取用户当天发送次数
	countMap, err := s.cache.BatchGetUidSend(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "filterSendLimitUser BatchGetUidSend failed uidList %v err %v", uidList, err)
		return nil, err
	}

	filteredMap := make(map[uint32][]*cache.UserInteractionRecord)
	for uid, r := range activeMap {
		records := r
		if count, exists := countMap[uid]; exists && count >= limit {
			log.DebugWithCtx(ctx, "filterSendLimitUser uid %d has reached send limit %d, skipping", uid, limit)
			continue // 如果用户当天发送次数超过限制，跳过
		}

		filteredMap[uid] = records // 保留符合条件的用户记录
		log.InfoWithCtx(ctx, "filterSendLimitUser uid %d records %v", uid, records)
	}

	return filteredMap, nil
}
