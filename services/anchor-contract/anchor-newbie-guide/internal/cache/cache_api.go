package cache

import (
	context "context"

	redis1 "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	acPb "golang.52tt.com/protocol/services/anchorcontract-go"
)

type ICache interface {
	AddActiveUser(ctx context.Context, liveId, uid uint32) error
	BatchAddTriggerCount(ctx context.Context, uid uint32, toUid map[uint32]uint32) error
	BatchAddUidSend(ctx context.Context, uidList []uint32) error
	BatchGetChannelEnter(ctx context.Context, liveId uint32, uids []uint32) (map[uint32]int64, error)
	BatchGetTriggerCount(ctx context.Context, uid uint32, toUid []uint32) (map[uint32]int64, error)
	BatchGetUidSend(ctx context.Context, uidList []uint32) (map[uint32]uint32, error)
	BatchGetUserInteractionRecordDetail(ctx context.Context, liveId uint32, uidList []uint32) (map[uint32][]*UserInteractionRecord, error)
	BatchSetTriggerCount(ctx context.Context, uid uint32, uidMap map[uint32]uint32) error
	CheckLiveEndTask(ctx context.Context, liveId int64) (bool, error)
	Close() error
	DeleteChannelEnter(ctx context.Context, liveId, uid uint32) error
	GetActiveUsers(ctx context.Context, liveId uint32) ([]uint32, error)
	GetAllUidScores(ctx context.Context) (map[int64]int64, error)
	GetChannelEnter(ctx context.Context, liveId, uid uint32) (int64, error)
	GetLiveIdByUid(ctx context.Context, uid int64) (int64, error)
	GetRedis() redis1.Cmdable
	GetTriggerMark(ctx context.Context, liveId uint32, targetUidList []uint32) (map[uint32]bool, error)
	GetUserInteractionRecordType(ctx context.Context, liveId, uid uint32) (int, error)
	IncreaseUserDanmuCount(ctx context.Context, liveId, uid uint32) error
	IncreaseUserGiftCount(ctx context.Context, liveId, uid uint32) error
	IncreaseUserGiftPrice(ctx context.Context, liveId, uid uint32, price int64) error
	IncreaseUserStayDuration(ctx context.Context, liveId, uid uint32, duration int64) error
	IsLiveFirstReady(ctx context.Context, uid uint32) (bool, error)
	SetChannelEnter(ctx context.Context, liveId, uid, ts uint32) error
	SetLiveEndTask(ctx context.Context, liveId int64) error
	SetLiveFirstReady(ctx context.Context, uid uint32) error
	SetTriggerMark(ctx context.Context, liveId uint32, targetUidList []uint32) error
	SetUidLive(ctx context.Context, uid int64, liveId int64) error
	SetUidScore(ctx context.Context, uid int64, score int64) error
	DelUidScore(ctx context.Context, uid int64) error
	SetNewbieUid(ctx context.Context, uid uint32) error
	IsNewbieUid(ctx context.Context, uid uint32) (bool, error)
	GetUserClaimLock(ctx context.Context, uid uint32) (bool, error)
	ReleaseUserClaimLock(ctx context.Context, uid uint32)
	AppendAutoClaimReward(ctx context.Context, uid uint32, rewardId uint32, days uint32) error
	GetAutoClaimRewardList(ctx context.Context, uid uint32) ([]uint32, error)
	GetAccessFrequencyLock(ctx context.Context, uid uint32) (int64, error)
	SetTaskTotalPercent(ctx context.Context, uid uint32, percent uint32) error
	GetTaskTotalPercent(ctx context.Context, uid uint32) (uint32, error)
	HasUnclaimedReward(ctx context.Context, uid uint32) (bool, bool, error)
	SetHasUnclaimedReward(ctx context.Context, uid uint32, hasUnclaimedReward bool) error
	SetNewbieTaskPreInit(ctx context.Context, uid uint32, identityType acPb.SIGN_ANCHOR_IDENTITY) error
	GetNewbieTaskPreInit(ctx context.Context, uid uint32, identityType acPb.SIGN_ANCHOR_IDENTITY) (bool, error)
	DelNewbieTaskPreInit(ctx context.Context, uid uint32, identityType acPb.SIGN_ANCHOR_IDENTITY) error
}
