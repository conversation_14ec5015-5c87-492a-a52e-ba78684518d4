package cache

import (
	"context"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
)

// 存储uid已经发送感谢的次数

const UidSendDayKey = "uid_send_day"

func getUidSendKey(uid uint32) string {
	return UidSendDayKey + strconv.Itoa(int(uid)) + ":" + time.Now().Format("20060102")
}

// BatchAddUidSend 批量增加uid发送感谢的次数
func (c *Cache) BatchAddUidSend(ctx context.Context, uidList []uint32) error {
	pipe := c.cmder.Pipeline()
	for _, uid := range uidList {
		key := getUidSendKey(uid)
		log.DebugWithCtx(ctx, "BatchAddUidSend: uid:%d", uid)
		pipe.IncrBy(ctx, key, 1)
		pipe.Expire(ctx, key, 24*time.Hour) // 设置过期时间为24小时
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		return err
	}

	return nil
}

// BatchGetUidSend 批量获取uid发送感谢的次数
func (c *Cache) BatchGetUidSend(ctx context.Context, uidList []uint32) (map[uint32]uint32, error) {
	results := make(map[uint32]uint32)
	if len(uidList) == 0 {
		return results, nil
	}
	keys := make([]string, 0, len(uidList))
	for _, uid := range uidList {
		keys = append(keys, getUidSendKey(uid))
	}

	tmp, err := c.cmder.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, err
	}

	for i, val := range tmp {
		if val == nil {
			results[uidList[i]] = 0 // 如果没有值，设置为0
			continue
		}
		count, err := strconv.ParseInt(val.(string), 10, 64)
		if err != nil {
			return nil, err // 转换错误
		}
		results[uidList[i]] = uint32(count)
	}

	return results, nil
}
