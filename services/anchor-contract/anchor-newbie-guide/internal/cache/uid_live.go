package cache

import (
	"context"
	"errors"
	redis1 "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"strconv"
	"time"
)

// 存储uid和liveId的映射关系

const UidLiveKey = "uid_live:"

// SetUidLive 设置用户和直播间的映射关系
func (c *Cache) SetUidLive(ctx context.Context, uid int64, liveId int64) error {
	_, err := c.cmder.Set(ctx, UidLiveKey+strconv.FormatInt(uid, 10), liveId, time.Second*86400).Result()
	if err != nil {
		return err
	}
	return nil
}

// GetLiveIdByUid 获取用户对应的直播间ID
func (c *Cache) GetLiveIdByUid(ctx context.Context, uid int64) (int64, error) {
	liveIdStr, err := c.cmder.Get(ctx, UidLiveKey+strconv.FormatInt(uid, 10)).Result()
	if err != nil {
		if errors.Is(err, redis1.Nil) {
			// 如果没有找到对应的直播间ID，返回0
			return 0, nil
		}
		return 0, err
	}
	liveId, err := strconv.ParseInt(liveIdStr, 10, 64)
	if err != nil {
		return 0, err
	}
	return liveId, nil
}
