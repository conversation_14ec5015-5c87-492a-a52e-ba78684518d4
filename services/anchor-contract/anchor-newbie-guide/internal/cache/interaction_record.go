package cache

import (
	"context"
	"github.com/go-redis/redis/v8"
	"golang.52tt.com/pkg/log"
	"strconv"
	"time"
)

// 以liveId维度，记录用户在该场次直播的送礼次数、弹幕次数、停留时长

const giftCountField = "gift_count"
const danmuCountField = "danmu_count"
const stayDurationField = "stay_duration"
const giftPriceField = "gift_price"

func getInteractionRecordSetKey(liveId uint32) string {
	return "interaction_record:" + strconv.Itoa(int(liveId))
}

func getInteractionRecordUserStatsKey(liveId, uid uint32) string {
	return "interaction_record_stats:" + strconv.Itoa(int(liveId)) + ":" + strconv.Itoa(int(uid))
}

// IncreaseUserGiftCount 设置用户在直播间的互动记录
func (c *Cache) IncreaseUserGiftCount(ctx context.Context, liveId, uid uint32) error {
	_, err := c.cmder.HIncrBy(ctx, getInteractionRecordUserStatsKey(liveId, uid), giftCountField, 1).Result()
	if err != nil {
		return err
	}
	_ = c.cmder.Expire(ctx, getInteractionRecordUserStatsKey(liveId, uid), time.Second*86400)
	return nil
}

// IncreaseUserDanmuCount 设置用户在直播间的弹幕记录
func (c *Cache) IncreaseUserDanmuCount(ctx context.Context, liveId, uid uint32) error {
	_, err := c.cmder.HIncrBy(ctx, getInteractionRecordUserStatsKey(liveId, uid), danmuCountField, 1).Result()
	if err != nil {
		return err
	}
	_ = c.cmder.Expire(ctx, getInteractionRecordUserStatsKey(liveId, uid), time.Second*86400)

	return nil
}

// IncreaseUserGiftPrice 设置用户在直播间的送礼总金额
func (c *Cache) IncreaseUserGiftPrice(ctx context.Context, liveId, uid uint32, price int64) error {
	_, err := c.cmder.HIncrBy(ctx, getInteractionRecordUserStatsKey(liveId, uid), giftPriceField, price).Result()
	if err != nil {
		return err
	}
	_ = c.cmder.Expire(ctx, getInteractionRecordUserStatsKey(liveId, uid), time.Second*86400)

	return nil
}

// IncreaseUserStayDuration 设置用户在直播间的停留时长
func (c *Cache) IncreaseUserStayDuration(ctx context.Context, liveId, uid uint32, duration int64) error {
	_, err := c.cmder.HIncrBy(ctx, getInteractionRecordUserStatsKey(liveId, uid), stayDurationField, duration).Result()
	if err != nil {
		return err
	}
	_ = c.cmder.Expire(ctx, getInteractionRecordUserStatsKey(liveId, uid), time.Second*86400)

	return nil
}

// GetUserInteractionRecordType 获取用户在直播间的互动类型
func (c *Cache) GetUserInteractionRecordType(ctx context.Context, liveId, uid uint32) (int, error) {
	record, err := c.cmder.HGetAll(ctx, getInteractionRecordUserStatsKey(liveId, uid)).Result()
	if err != nil {
		return 0, err
	}

	// 将结果转换为int64类型
	if record[giftCountField] != "" {
		giftCount, err := strconv.ParseInt(record[giftCountField], 10, 64)
		if err != nil {
			return 0, err
		}
		if giftCount != 0 {
			return 1, nil
		}
	}

	if record[danmuCountField] != "" {
		danmuCount, err := strconv.ParseInt(record[danmuCountField], 10, 64)
		if err != nil {
			return 0, err
		}

		if danmuCount != 0 {
			return 2, nil
		}

	}

	if record[stayDurationField] != "" {
		stayDuration, err := strconv.ParseInt(record[stayDurationField], 10, 64)
		if err != nil {
			return 0, err
		}

		if stayDuration != 0 {
			return 3, nil
		}
	}

	return 0, nil
}

type UserInteractionRecord struct {
	InteractionType uint32
	Count           int
	Price           int64 // 送礼总金额
}

// BatchGetUserInteractionRecordDetail 批量获取用户在直播间最多的的互动记录
func (c *Cache) BatchGetUserInteractionRecordDetail(ctx context.Context, liveId uint32, uidList []uint32) (map[uint32][]*UserInteractionRecord, error) {
	pipe := c.cmder.Pipeline()

	tmpResMap := make(map[uint32]map[string]string)
	cmdMap := make(map[uint32]*redis.StringStringMapCmd)
	for _, userId := range uidList {
		tmpKey := getInteractionRecordUserStatsKey(liveId, userId)

		tmpCmd := pipe.HGetAll(ctx, tmpKey)

		cmdMap[userId] = tmpCmd
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		return nil, err
	}

	for userId, cmd := range cmdMap {
		res, err := cmd.Result()
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetUserInteractionRecordDetail HGetAll failed uid %d err %v", userId, err)
			continue
		}
		tmpResMap[userId] = res
	}

	result := make(map[uint32][]*UserInteractionRecord, len(uidList))

	for uid, record := range tmpResMap {
		if result[uid] == nil {
			result[uid] = make([]*UserInteractionRecord, 0)
		}

		// 将结果转换为int64类型
		if record[giftCountField] != "" {
			giftCount, err := strconv.ParseInt(record[giftCountField], 10, 64)
			if err != nil {
				log.ErrorWithCtx(ctx, "BatchGetUserInteractionRecordDetail ParseInt failed uid %d err %v", uid, err)
				continue
			}

			priceCount, err := strconv.ParseInt(record[giftPriceField], 10, 64)
			if err != nil {
				log.ErrorWithCtx(ctx, "BatchGetUserInteractionRecordDetail ParseInt failed uid %d err %v", uid, err)
			}
			result[uid] = append(result[uid], &UserInteractionRecord{
				InteractionType: 1,
				Count:           int(giftCount),
				Price:           priceCount,
			})
		}

		if record[danmuCountField] != "" {
			danmuCount, err := strconv.ParseInt(record[danmuCountField], 10, 64)
			if err != nil {
				log.ErrorWithCtx(ctx, "BatchGetUserInteractionRecordDetail ParseInt failed uid %d err %v", uid, err)
				continue
			}
			result[uid] = append(result[uid], &UserInteractionRecord{
				InteractionType: 2,
				Count:           int(danmuCount),
			})
		}

		if record[stayDurationField] != "" {
			stayDuration, err := strconv.ParseInt(record[stayDurationField], 10, 64)
			if err != nil {
				log.ErrorWithCtx(ctx, "BatchGetUserInteractionRecordDetail ParseInt failed uid %d err %v", uid, err)
				continue
			}
			result[uid] = append(result[uid], &UserInteractionRecord{
				InteractionType: 3,
				Count:           int(stayDuration),
			})
		}

	}

	return result, nil
}

// AddActiveUser 记录房间内的活跃用户
func (c *Cache) AddActiveUser(ctx context.Context, liveId, uid uint32) error {
	_, err := c.cmder.SAdd(ctx, getInteractionRecordSetKey(liveId), uid).Result()
	if err != nil {
		return err
	}
	_ = c.cmder.Expire(ctx, getInteractionRecordSetKey(liveId), time.Second*86400)
	return nil
}

// GetActiveUsers 获取直播间内的活跃用户列表
func (c *Cache) GetActiveUsers(ctx context.Context, liveId uint32) ([]uint32, error) {
	activeUsers, err := c.cmder.SMembers(ctx, getInteractionRecordSetKey(liveId)).Result()
	if err != nil {
		return nil, err
	}

	var userIds []uint32
	for _, userIdStr := range activeUsers {
		userId, err := strconv.ParseUint(userIdStr, 10, 32)
		if err != nil {
			return nil, err
		}
		userIds = append(userIds, uint32(userId))
	}

	return userIds, nil
}
