package cache

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"golang.52tt.com/pkg/log"
	acPb "golang.52tt.com/protocol/services/anchorcontract-go"
)

// 新主播标记 string
func genNewbieUidKey(uid uint32) string {
	return fmt.Sprintf("newbie_uid:%d", uid)
}

// 自动领取奖励记录 list
func genAutoClaimRewardKey(uid uint32) string {
	return fmt.Sprintf("auto_claim_reward:%d", uid)
}

// 用户领取锁
func genUserClaimLockKey(uid uint32) string {
	return fmt.Sprintf("user_claim_lock:%d", uid)
}

// 访问频次锁
func genAccessFrequencyLockKey(uid uint32) string {
	return fmt.Sprintf("access_frequency_lock:%d", uid)
}

// 用户任务总进度百分比
func genTaskTotalPercentKey(uid uint32) string {
	return fmt.Sprintf("task_total_percent:%d", uid)
}

// 是由有未领取奖励
func genHasUnclaimedRewardKey(uid uint32) string {
	return fmt.Sprintf("has_unclaimed_reward:%d", uid)
}

// 新主播任务预初始化
func genNewbieTaskPreInitKey(uid uint32, identityType acPb.SIGN_ANCHOR_IDENTITY) string {
	return fmt.Sprintf("newbie_pre_init:%d:%d", uid, identityType)
}

// SetNewbieUid 设置新主播标记，用于快速排除大部分非新签约用户，不保证准确性，需再次查询签约
func (c *Cache) SetNewbieUid(ctx context.Context, uid uint32) error {
	key := genNewbieUidKey(uid)
	expire := time.Hour * 24 * 30 // 30day
	_, err := c.cmder.Set(ctx, key, "1", expire).Result()
	if err != nil {
		return err
	}
	return nil
}

func (c *Cache) IsNewbieUid(ctx context.Context, uid uint32) (bool, error) {
	key := genNewbieUidKey(uid)
	exist, err := c.cmder.Exists(ctx, key).Result()
	if err != nil {
		return false, err
	}
	return exist == 1, nil
}

// AppendAutoClaimReward 自动领取奖励列表
func (c *Cache) AppendAutoClaimReward(ctx context.Context, uid uint32, rewardId uint32, days uint32) error {
	log.InfoWithCtx(ctx, "AppendAutoClaimReward uid:%d, rewardId:%d, days:%d", uid, rewardId, days)
	key := genAutoClaimRewardKey(uid)
	_, err := c.cmder.RPush(ctx, key, rewardId).Result()
	if err != nil {
		return err
	}
	// 设置过期时间
	_, err = c.cmder.Expire(ctx, key, time.Hour*24*time.Duration(days)).Result()
	if err != nil {
		return err
	}
	return nil
}

// GetAutoClaimRewardList 获取自动领取奖励列表
func (c *Cache) GetAutoClaimRewardList(ctx context.Context, uid uint32) ([]uint32, error) {
	key := genAutoClaimRewardKey(uid)
	list, err := c.cmder.LRange(ctx, key, 0, -1).Result()
	if err != nil {
		return nil, err
	}
	rewardIdList := make([]uint32, 0, len(list))
	for _, item := range list {
		rewardId, err := strconv.ParseUint(item, 10, 32)
		if err != nil {
			return nil, err
		}
		rewardIdList = append(rewardIdList, uint32(rewardId))
	}
	// 删除key，避免重复弹窗
	_, err = c.cmder.Del(ctx, key).Result()
	if err != nil {
		return nil, err
	}
	return rewardIdList, nil
}

// GetUserClaimLock 获取用户领取锁
func (c *Cache) GetUserClaimLock(ctx context.Context, uid uint32) (bool, error) {
	key := genUserClaimLockKey(uid)
	ok, err := c.cmder.SetNX(ctx, key, "1", time.Second*5).Result()
	if err != nil {
		return false, err
	}
	return ok, nil
}

// ReleaseUserClaimLock 释放用户领取锁
func (c *Cache) ReleaseUserClaimLock(ctx context.Context, uid uint32) {
	key := genUserClaimLockKey(uid)
	_, err := c.cmder.Del(ctx, key).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "ReleaseUserClaimLock Del err, uid:%d, err %+v", uid, err)
	}
}

// GetAccessFrequencyLock 访问频次锁，每次访问+1，返回总数
func (c *Cache) GetAccessFrequencyLock(ctx context.Context, uid uint32) (int64, error) {
	key := genAccessFrequencyLockKey(uid)
	count, err := c.cmder.Incr(ctx, key).Result()
	if err != nil {
		return 0, err
	}
	if count <= 3 {
		c.cmder.Expire(ctx, key, time.Second*10)
	}
	return count, nil
}

// SetTaskTotalPercent 设置缓存用户任务总进度百分比
func (c *Cache) SetTaskTotalPercent(ctx context.Context, uid uint32, percent uint32) error {
	key := genTaskTotalPercentKey(uid)
	_, err := c.cmder.Set(ctx, key, percent, time.Second*10).Result()
	if err != nil {
		return err
	}
	return nil
}

// GetTaskTotalPercent 获取缓存用户任务总进度百分比
func (c *Cache) GetTaskTotalPercent(ctx context.Context, uid uint32) (uint32, error) {
	key := genTaskTotalPercentKey(uid)
	percentStr, err := c.cmder.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return 0, nil
		}
		return 0, err
	}
	percent, err := strconv.ParseUint(percentStr, 10, 32)
	if err != nil {
		return 0, err
	}
	return uint32(percent), nil
}

// HasUnclaimedReward 是否有未领取奖励
func (c *Cache) HasUnclaimedReward(ctx context.Context, uid uint32) (bool, bool, error) {
	key := genHasUnclaimedRewardKey(uid)
	value, err := c.cmder.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return false, false, nil
		}
		return false, false, err
	}
	// key 存在，解析值
	hasUnclaimedReward, err := strconv.ParseBool(value)
	if err != nil {
		log.ErrorWithCtx(ctx, "HasUnclaimedReward ParseBool err, uid:%d, value:%s, err %+v", uid, value, err)
		return false, false, err
	}
	return hasUnclaimedReward, true, nil
}

// SetHasUnclaimedReward 设置是否有未领取奖励
func (c *Cache) SetHasUnclaimedReward(ctx context.Context, uid uint32, hasUnclaimedReward bool) error {
	key := genHasUnclaimedRewardKey(uid)
	_, err := c.cmder.Set(ctx, key, hasUnclaimedReward, time.Second*10).Result()
	if err != nil {
		return err
	}
	return nil
}

// SetNewbieTaskPreInit 设置新主播任务预初始化标记
func (c *Cache) SetNewbieTaskPreInit(ctx context.Context, uid uint32, identityType acPb.SIGN_ANCHOR_IDENTITY) error {
	key := genNewbieTaskPreInitKey(uid, identityType)
	_, err := c.cmder.Set(ctx, key, "1", time.Hour*24*4).Result()
	if err != nil {
		return err
	}
	return nil
}

// GetNewbieTaskPreInit 获取新主播任务预初始化标记
func (c *Cache) GetNewbieTaskPreInit(ctx context.Context, uid uint32, identityType acPb.SIGN_ANCHOR_IDENTITY) (bool, error) {
	key := genNewbieTaskPreInitKey(uid, identityType)
	exist, err := c.cmder.Exists(ctx, key).Result()
	if err != nil {
		return false, err
	}
	return exist == 1, nil
}

// DelNewbieTaskPreInit 删除新主播任务预初始化标记
func (c *Cache) DelNewbieTaskPreInit(ctx context.Context, uid uint32, identityType acPb.SIGN_ANCHOR_IDENTITY) error {
	key := genNewbieTaskPreInitKey(uid, identityType)
	_, err := c.cmder.Del(ctx, key).Result()
	if err != nil {
		return err
	}
	return nil
}
