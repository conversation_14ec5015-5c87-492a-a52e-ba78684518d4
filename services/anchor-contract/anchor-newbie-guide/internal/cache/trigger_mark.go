package cache

import (
	"context"
	"strconv"
	"time"

	"golang.52tt.com/pkg/log"
)

// TriggerMark 标记本次直播已经展示过下播后建联任务的uid, 是一个set
// TriggerCount 标记当天已经展示过下播后建联任务的次数 , 是一个hash结构

const TriggerMark = "trigger_mark:"
const TriggerCount = "trigger_count:"

func getTriggerMarkKey(liveId uint32) string {
	return TriggerMark + strconv.Itoa(int(liveId))
}

// SetTriggerMark 设置触发标记
func (c *Cache) SetTriggerMark(ctx context.Context, liveId uint32, targetUidList []uint32) error {
	key := getTriggerMarkKey(liveId)

	targetList := make([]interface{}, 0)
	for _, uid := range targetUidList {
		targetList = append(targetList, strconv.Itoa(int(uid)))
	}

	err := c.cmder.SAdd(ctx, key, targetList...).Err()
	if err != nil {
		return err
	}

	_ = c.cmder.Expire(ctx, key, time.Second*86400)

	return nil
}

// GetTriggerMark 获取触发标记
func (c *Cache) GetTriggerMark(ctx context.Context, liveId uint32, targetUidList []uint32) (map[uint32]bool, error) {
	key := getTriggerMarkKey(liveId)

	results := make(map[uint32]bool)
	members, err := c.cmder.SMembers(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	for _, member := range members {
		uid, err := strconv.ParseUint(member, 10, 32)
		if err != nil {
			return nil, err // 转换错误
		}
		results[uint32(uid)] = true // 设置为true表示已触发
	}

	for _, to := range targetUidList {
		if _, exists := results[to]; !exists {
			results[to] = false // 如果没有触发，设置为false
		}
	}

	return results, nil
}

func getTriggerCountKey(uid uint32) string {
	return TriggerCount + strconv.Itoa(int(uid)) + ":" + time.Now().Format("20060102")
}

// BatchAddTriggerCount 批量设置当天触发次数
func (c *Cache) BatchAddTriggerCount(ctx context.Context, uid uint32, toUid map[uint32]uint32) error {
	key := getTriggerCountKey(uid)
	pipe := c.cmder.Pipeline()
	for to, count := range toUid {
		log.DebugWithCtx(ctx, "BatchAddTriggerCount: uid:%d, toUid:%v", uid, toUid)
		field := strconv.Itoa(int(to))
		pipe.HIncrBy(ctx, key, field, int64(count))
	}
	pipe.Expire(ctx, key, time.Hour*24)

	_, err := pipe.Exec(ctx)
	if err != nil {
		return err
	}

	return nil
}

// BatchSetTriggerCount 批量设置当天触发次数
func (c *Cache) BatchSetTriggerCount(ctx context.Context, uid uint32, uidMap map[uint32]uint32) error {
	err := c.cmder.HMSet(ctx, getTriggerCountKey(uid), uidMap).Err()
	if err != nil {
		return err
	}

	return nil

}

// BatchGetTriggerCount 获取当天触发次数
func (c *Cache) BatchGetTriggerCount(ctx context.Context, uid uint32, toUid []uint32) (map[uint32]int64, error) {
	key := getTriggerCountKey(uid)

	results := make(map[uint32]int64)
	filedList := make([]string, 0, len(toUid))
	for _, to := range toUid {
		filedList = append(filedList, strconv.Itoa(int(to)))
	}

	tmp, err := c.cmder.HMGet(ctx, key, filedList...).Result()
	if err != nil {
		return nil, err
	}

	for i, val := range tmp {
		if val == nil {
			results[toUid[i]] = 0 // 如果没有值，设置为0
			continue
		}
		count, err := strconv.ParseInt(val.(string), 10, 64)
		if err != nil {
			return nil, err // 转换错误
		}
		results[toUid[i]] = count
	}

	return results, nil
}
