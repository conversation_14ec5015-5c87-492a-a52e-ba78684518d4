package mysql

import (
	"context"
	"database/sql"
	"time"

	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
)

func NewMysql(db, readonlyDb *gorm.DB) *Store {
	return &Store{
		db:         db,
		readonlyDb: readonlyDb,
	}
}

type Store struct {
	db, readonlyDb *gorm.DB
}

func (s *Store) AddUserContract(ctx context.Context, tx *gorm.DB, applyInfo *ContractApply, now, expireTime time.Time) error {
	// 添加签约信息
	err := s.AddContract(tx, &ContractInfo{
		Uid:                applyInfo.Uid,
		GuildId:            applyInfo.GuildId,
		IdentityNum:        applyInfo.IdentityNum,
		ContractDuration:   applyInfo.ContractDuration,
		SignTime:           now,
		ContractExpireTime: expireTime,
		UpdateTime:         now,
		WorkerType:         applyInfo.WorkerType,
	})
	if err != nil {
		log.Errorf("AddUserContract fail to AddContract in:%+v, err:%v", applyInfo, err)
		return err
	}

	err = s.RecordContractChangeLog(tx, &ContractChangeLog{
		Uid:                applyInfo.Uid,
		GuildId:            applyInfo.GuildId,
		IdentityNum:        applyInfo.IdentityNum,
		ContractDuration:   applyInfo.ContractDuration,
		SignTime:           now,
		ContractExpireTime: now.AddDate(0, int(applyInfo.ContractDuration), 0),
		ChangeType:         uint32(pb.CONTRACT_CHANGE_TYPE_ENUM_CONTRACT_CHANGE_ACCEPT),
		ChangeDesc:         "accept sign apply",
		ChangeTime:         now,
		WorkerType:         applyInfo.WorkerType,
	})
	if err != nil {
		log.Errorf("AddUserContract fail to RecordContractChangeLog in:%+v, err:%v", applyInfo, err)
		return err
	}

	return nil
}

func (s *Store) AddUserContract2(ctx context.Context, tx *gorm.DB, applyInfo *ContractApply, signTime, expireTime, changeTime time.Time) error {
	// 添加签约信息
	err := s.AddContract(tx, &ContractInfo{
		Uid:                applyInfo.Uid,
		GuildId:            applyInfo.GuildId,
		IdentityNum:        applyInfo.IdentityNum,
		ContractDuration:   applyInfo.ContractDuration,
		SignTime:           signTime,
		ContractExpireTime: expireTime,
		UpdateTime:         changeTime,
		WorkerType:         applyInfo.WorkerType,
	})
	if err != nil {
		log.Errorf("AddUserContract fail to AddContract in:%+v, err:%v", applyInfo, err)
		return err
	}

	err = s.RecordContractChangeLog(tx, &ContractChangeLog{
		Uid:                applyInfo.Uid,
		GuildId:            applyInfo.GuildId,
		IdentityNum:        applyInfo.IdentityNum,
		ContractDuration:   applyInfo.ContractDuration,
		SignTime:           signTime,
		ContractExpireTime: signTime.AddDate(0, int(applyInfo.ContractDuration), 0),
		ChangeType:         uint32(pb.CONTRACT_CHANGE_TYPE_ENUM_CONTRACT_CHANGE_ACCEPT),
		ChangeDesc:         "accept sign apply",
		ChangeTime:         changeTime,
		WorkerType:         applyInfo.WorkerType,
	})
	if err != nil {
		log.Errorf("AddUserContract fail to RecordContractChangeLog in:%+v, err:%v", applyInfo, err)
		return err
	}

	return nil
}

func (s *Store) AddUserAnchorIdentity(ctx context.Context, tx *gorm.DB, uid, guildId, identityType uint32, now, signTime, contractExpireTime time.Time, handler string) error {
	// 添加身份信息
	err := s.AddAnchorIdentity(tx, &AnchorIdentity{
		Uid:          uid,
		IdentityType: identityType,
		GuildId:      guildId,
		ObtainTime:   now,
		UpdateTime:   now,
	})
	if err != nil {
		log.Errorf("AddUserAnchorIdentity fail to AddAnchorIdentity uid:%+v, guildId:%+v, identityType:%+v, err:%v", uid, guildId, identityType, err)
		return err
	}

	err = s.RecordAnchorIdentityChangeLog(tx, &AnchorIdentityChangeLog{
		Uid:                uid,
		GuildId:            guildId,
		IdentityType:       identityType,
		SignTime:           signTime,
		ContractExpireTime: contractExpireTime,
		ObtainTime:         now,
		ChangeType:         uint32(pb.ANCHOR_IDENTITY_CHANGE_TYPE_ANCHOR_IDENTITY_CHANGE_TYPE_ADD),
		ChangeDesc:         "add",
		ChangeTime:         now,
		Handler:            handler,
	})
	if err != nil {
		log.Errorf("AddUserAnchorIdentity fail to RecordAnchorIdentityChangeLog uid:%+v, guildId:%+v, identityType:%+v, err:%v", uid, guildId, identityType, err)
		return err
	}

	return nil
}

func (s *Store) DelUserAnchorIdentity(ctx context.Context, tx *gorm.DB, uid, guildId, identityType uint32, now, signTime, contractExpireTime time.Time, handler string) error {
	info, exist, err := s.GetAnchorIdentityWithType(uid, identityType)
	if err != nil {
		log.Errorf("DelUserAnchorIdentity fail to GetAnchorIdentityWithType uid:%+v, guildId:%+v, identityType:%+v, err:%v", uid, guildId, identityType, err)
		return err
	}

	if !exist || info.Uid != uid || info.GuildId != guildId {
		return nil
	}

	// 回收身份信息
	err = s.DelAnchorIdentity(tx, uid, guildId, identityType)
	if err != nil {
		log.Errorf("DelUserAnchorIdentity fail to DelAnchorIdentity uid:%+v, guildId:%+v, identityType:%+v, err:%v", uid, guildId, identityType, err)
		return err
	}

	err = s.RecordAnchorIdentityChangeLog(tx, &AnchorIdentityChangeLog{
		Uid:                uid,
		GuildId:            guildId,
		IdentityType:       identityType,
		SignTime:           signTime,
		ContractExpireTime: contractExpireTime,
		ObtainTime:         info.ObtainTime,
		ChangeType:         uint32(pb.ANCHOR_IDENTITY_CHANGE_TYPE_ANCHOR_IDENTITY_CHANGE_TYPE_DEL),
		ChangeDesc:         "del",
		ChangeTime:         now,
		Handler:            handler,
	})
	if err != nil {
		log.Errorf("DelUserAnchorIdentity fail to RecordAnchorIdentityChangeLog uid:%+v, guildId:%+v, identityType:%+v, err:%v", uid, guildId, identityType, err)
		return err
	}

	return nil
}

func (s *Store) CancelContractByUid(ctx context.Context, opUid, targetUid, guildId, changeType uint32, changeDesc string) (radioLive bool, multiPlayer bool, err error) {
	now := time.Now()

	contract, exist, err := s.GetContractWithUid(targetUid)
	if err != nil {
		log.Errorf("CancelContractByUid fail to GetContractWithUid uid:%+v, guildId:%+v, opUid:%+v, err:%v", targetUid, guildId, opUid, err)
		return
	}

	if !exist || contract.GuildId != guildId {
		log.Errorf("CancelContractByUid fail to GetContractWithUid uid:%+v, guildId:%+v, opUid:%+v, err:contract not exist", targetUid, guildId, opUid)
		err = protocol.NewExactServerError(nil, status.ErrContractNonexist)
		return
	}
	log.Infof("CancelContractByUid targetUid=%d changeType=%d changeDesc=%s contract=%+v", targetUid, changeType, changeDesc, contract)

	err = s.Transaction(ctx, func(tx *gorm.DB) error {
		err := s.DelContract(tx, targetUid, guildId)
		if err != nil {
			log.Errorf("CancelContractByUid fail to DelContract uid:%+v, guildId:%+v, opUid:%+v, err:%v", targetUid, guildId, opUid, err)
			return err
		}

		err = s.RecordContractChangeLog(tx, &ContractChangeLog{
			Uid:                contract.Uid,
			GuildId:            contract.GuildId,
			IdentityNum:        contract.IdentityNum,
			ContractDuration:   contract.ContractDuration,
			SignTime:           contract.SignTime,
			ContractExpireTime: contract.ContractExpireTime,
			ChangeType:         changeType,
			ChangeDesc:         changeDesc,
			ChangeTime:         now,
		})
		if err != nil {
			log.Errorf("CancelContractByUid fail to RecordContractChangeLog uid:%+v, guildId:%+v, opUid:%+v, err:%v", targetUid, guildId, opUid, err)
			return err
		}

		list, err := s.GetAnchorIdentity(tx, targetUid)
		if err != nil {
			log.Errorf("CancelContractByUid fail to GetAnchorIdentity uid:%+v, guildId:%+v, opUid:%+v, err:%v", targetUid, guildId, opUid, err)
			return err
		}

		for _, info := range list {
			if info.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) { //签约平台达人的，不回收
				continue
			}
			// 回收身份
			err = s.DelUserAnchorIdentity(ctx, tx, targetUid, info.GuildId, info.IdentityType, now, contract.SignTime, contract.ContractExpireTime, "guild cancel contract")
			if err != nil {
				log.Errorf("CancelContractByUid fail to DelUserAnchorIdentity uid:%+v, guildId:%+v, opUid:%+v, err:%v", targetUid, guildId, opUid, err)
				return err
			}

			if info.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
				err = s.CancelLiveAnchorExamine(tx, targetUid, guildId, "sys", changeDesc)
				if err != nil {
					log.Errorf("CancelContractByUid fail to CancelLiveAnchorExamine uid:%+v, guildId:%+v, opUid:%+v, err:%v", targetUid, guildId, opUid, err)
					//return err
				}

				radioLive = true
			}
			if info.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
				multiPlayer = true
			}
		}

		err = s.DelCancelContractApply(tx, targetUid, guildId)
		if err != nil {
			log.Errorf("CancelContractByUid fail to DelCancelContractApply uid:%+v, guildId:%+v, opUid:%+v, err:%v", targetUid, guildId, opUid, err)
			return err
		}

		// 更新解约记录表状态为已解约, 如果是会长主动解约，则空删
		_, err = s.FinishCancelContractApplyLog(tx, targetUid, guildId)
		if err != nil {
			log.Errorf("CancelContractByUid fail to UpdateCancelContractApplyLog uid:%+v, guildId:%+v, opUid:%+v, err:%v", targetUid, guildId, opUid, err)
			return err
		}

		// 删除主播额外信息表
		err = s.DeleteAnchorExtInfo(tx, targetUid)
		if err != nil {
			log.Errorf("CancelContractByUid fail to DeleteAnchorExtInfo uid:%+v, guildId:%+v, opUid:%+v, err:%v", targetUid, guildId, opUid, err)
			return err
		}

		preStatus := []uint32{
			uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_HANDLING),
			uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_HANDLING),
		}
		updateStatus := uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_INVALID)

		_, err = s.UpdateContractApplyStatusAllIdentity(tx, targetUid, preStatus, updateStatus)
		if err != nil {
			log.Errorf("CancelContractByUid fail to UpdateContractApplyStatusAllIdentity uid:%+v, guildId:%+v, opUid:%+v, err:%v", targetUid, guildId, opUid, err)
			return err
		}

		return nil
	})
	if err != nil {
		log.Errorf("CancelContractByUid fail to Transaction uid:%+v, guildId:%+v, opUid:%+v, err:%v", targetUid, guildId, opUid, err)
		return
	}

	return
}

func (s *Store) Transaction(ctx context.Context, f func(tx *gorm.DB) error) error {
	tx := s.db.BeginTx(ctx, &sql.TxOptions{})

	err := f(tx)
	if err != nil {
		log.Errorf("Transaction fail err %v", err)
		_ = tx.Rollback()
		return err
	}

	_ = tx.Commit()
	return nil
}

func (s *Store) CreateTable() error {
	if !s.db.HasTable(&ContractLimitWhitelist{}) {
		if err := s.db.CreateTable(&ContractLimitWhitelist{}).Error; err != nil {
			log.Errorf("CreateTable ContractLimitWhitelist fail %v", err)
		}
	}
	if !s.db.HasTable(&CancelContractApplyLog{}) {
		if err := s.db.Exec(CreateCancelContractApplyLogTbl).Error; err != nil {
			log.Errorf("CreateTable CancelContractApplyLog fail %v", err)
		}
	}
	if !s.db.HasTable(&AnchorExtInfo{}) {
		if err := s.db.Exec(CreateAnchorExtInfoTbl).Error; err != nil {
			log.Errorf("CreateTable AnchorExtInfo fail %v", err)
		}
	}
	if !s.db.HasTable(&LiveAnchorDailyRecord{}) {
		if err := s.db.Exec(CreateLiveAnchorDailyRecordTbl).Error; err != nil {
			log.Errorf("CreateTable LiveAnchorDailyRecord fail %v", err)
		}
	}

	if err := s.db.Exec(tblAnchorCertUpgradeTask).Error; err != nil {
		log.Errorf("CreateTable tblAnchorCertUpgradeTask fail %v", err)
	}
	if err := s.db.Exec(tblAnchorCertCompetitionRecord).Error; err != nil {
		log.Errorf("CreateTable tblAnchorCertCompetitionRecord fail %v", err)
	}
	if err := s.db.Exec(tblAnchorCertCompetitionRecordLog).Error; err != nil {
		log.Errorf("CreateTable tblAnchorCertCompetitionRecordLog fail %v", err)
	}
	if err := s.db.Exec(tblAnchorCertMonthTaskAwardLog).Error; err != nil {
		log.Errorf("CreateTable tblAnchorCertMonthTaskAwardLog fail %v", err)
	}
	if err := s.db.Exec(tblAnchorCertMonthTaskAwardChangeLog).Error; err != nil {
		log.Errorf("CreateTable tblAnchorCertMonthTaskAwardChangeLog fail %v", err)
	}

	if err := s.db.Exec(tblGuildSignAnchorSql).Error; err != nil {
		log.Errorf("CreateTable tblGuildSignAnchorSql fail %v", err)
	}

	if err := s.db.Exec(tblAnchorCertExtraAwardLog).Error; err != nil {
		log.Errorf("CreateTable tblAnchorCertExtraAwardLog fail %v", err)
	}

	if err := s.db.Exec(TblNegotiateCoolDown).Error; err != nil {
		log.Errorf("CreateTable TblNegotiateCoolDown fail %v", err)
	}

	if err := s.db.Exec(TblNegotiateTimesRecord).Error; err != nil {
		log.Errorf("CreateTable TblNegotiateTimesRecord fail %v", err)
	}

	if err := s.db.Exec(CreateHandleNoticeLogTbl).Error; err != nil {
		log.Errorf("CreateTable CreateHandleNoticeLogTbl fail %v", err)
	}

	if err := s.db.Exec(CreateWorkerTypeConfirmTbl).Error; err != nil {
		log.Errorf("CreateTable CreateWorkerTypeConfirm fail %v", err)
	}

	if err := s.db.Exec(CreateWorkerTypeChangeApplyTbl).Error; err != nil {
		log.Errorf("CreateTable CreateWorkerTypeChangeApplyTbl fail %v", err)
	}

	if err := s.db.Exec(CreateWorkerTypeChangeInfoTbl).Error; err != nil {
		log.Errorf("CreateTable CreateWorkerTypeChangeApplyTbl fail %v", err)
	}

	if err := s.db.Exec(CreateWorkerTypeChangeSnapshotTbl).Error; err != nil {
		log.Errorf("CreateTable CreateWorkerTypeChangeApplyTbl fail %v", err)
	}

	if err := s.db.Exec(CreateCancelReasonSnapshotTbl).Error; err != nil {
		log.Errorf("CreateTable CreateCancelReasonSnapshotTbl fail %v", err)
	}

	for i := 0; i < 100; i++ {
		s.db.AutoMigrate(&CancelContractApply{GuildId: uint32(i)})
	}
	s.db.AutoMigrate(&CancelContractApplyLog{})

	return nil
}
