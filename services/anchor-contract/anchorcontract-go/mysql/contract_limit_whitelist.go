package mysql

import (
	"context"
	"time"

	"github.com/jinzhu/gorm"
)

type ContractLimitWhitelist struct {
	Id         uint32    `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	Uid        uint32    `gorm:"column:uid"`
	Operator   string    `gorm:"column:operator"`
	LimitCount uint32    `gorm:"column:limit_count"`
	CreateTime time.Time `gorm:"column:create_time"`
	UpdateTime time.Time `gorm:"column:update_time"`
}

func (m *ContractLimitWhitelist) TableName() string {
	return "contract_limit_whitelist"
}

func (s *Store) GetAnchorContractLimit(ctx context.Context, uids []uint32, offset, limit uint32) (uint32, []*ContractLimitWhitelist, error) {
	var total uint32
	var list []*ContractLimitWhitelist

	// 子查询：取每个 uid 的最新一条（以 update_time 为准）
	subQuery := s.readonlyDb.Table("contract_limit_whitelist as t1").
		Select("t1.*").
		Where("NOT EXISTS (" +
			"SELECT 1 FROM contract_limit_whitelist t2 " +
			"WHERE t2.uid = t1.uid AND (t2.update_time > t1.update_time OR (t2.update_time = t1.update_time AND t2.id > t1.id))" +
			")")

	if len(uids) > 0 {
		subQuery = subQuery.Where("t1.uid IN (?)", uids)
	}

	// 统计总数（不同 uid 的数量）
	err := subQuery.Count(&total).Error
	if err != nil {
		return 0, nil, err
	}

	if total == 0 {
		return 0, list, nil
	}

	// 分页 + 排序（按 update_time desc）
	err = subQuery.Order("t1.update_time desc").
		Offset(int(offset)).
		Limit(int(limit)).
		Find(&list).Error
	if err != nil {
		return 0, nil, err
	}

	return total, list, nil
}

func (s *Store) AddContractLimitWhiteList(tx *gorm.DB, uid uint32, operator string, limitCount uint32) error {

	db := s.db
	if tx != nil {
		db = tx
	}

	now := time.Now()
	var values []interface{}
	sql := "INSERT INTO contract_limit_whitelist (uid, operator, create_time, update_time , limit_count) VALUES "
	sql += "(?, ?, ?, ?, ?),"
	values = append(values, uid, operator, now, now, limitCount)

	sql = sql[0 : len(sql)-1]
	sql += " ON DUPLICATE KEY UPDATE operator=VALUES(operator), limit_count = VALUES(limit_count),  update_time=VALUES(update_time)"

	return db.Exec(sql, values...).Error
}

// GetAnchorContractLimitByUid 获取某个uid最近一条记录
func (s *Store) GetAnchorContractLimitByUid(ctx context.Context, uid uint32) (*ContractLimitWhitelist, error) {
	var info ContractLimitWhitelist
	err := s.readonlyDb.Where("uid = ?", uid).Order("id desc").First(&info).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, nil
		}
		return nil, err
	}
	return &info, nil
}
