package mysql

import (
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"time"
)

// 字段： 公会id、会长id、公告内容、申请审核时间、操作人、操作时间、操作、备注
//message AnchorNoticeHandle {
//uint32 guild_id = 1; // 公会id
//uint32 uid = 2; // 会长id
//string notice_content = 3; // 公告内容
//uint32 apply_time = 4; // 申请审核时间
//string operator = 5; // 操作人
//uint32 update_time = 6; // 操作时间
//uint32 operate_type = 7; // 操作类型
//string remark = 8; // 备注
//}

// HandleNoticeLog 处理公告列表
type HandleNoticeLog struct {
	GuildId       uint32    `db:"guild_id"`                                 // 公会id
	Uid           uint32    `db:"uid"`                                      // 会长id
	NoticeContent string    `db:"notice_content" gorm:"type:varchar(2048)"` // 公告内容
	ApplyTime     time.Time `db:"apply_time"`                               // 申请审核时间
	Operator      string    `db:"operator" gorm:"type:varchar(256)"`        // 操作人
	UpdateTime    time.Time `db:"update_time"`                              // 操作时间
	OperateType   uint32    `db:"operate_type"`                             // 操作类型
	Remark        string    `db:"remark" gorm:"type:varchar(2048)"`         // 备注
}

const CreateHandleNoticeLogTbl = `CREATE TABLE IF NOT EXISTS handle_notice_log (
	guild_id INT(11) NOT NULL DEFAULT 0 COMMENT '公会id',
	uid INT(11) NOT NULL DEFAULT 0 COMMENT '会长id',
	notice_content VARCHAR(2048) NOT NULL DEFAULT '' COMMENT '公告内容',
	apply_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请审核时间',
	operator VARCHAR(255) NOT NULL DEFAULT '' COMMENT '操作人',
	update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '操作时间',
	operate_type INT(11) NOT NULL DEFAULT 0 COMMENT '操作类型',
	remark VARCHAR(2048) NOT NULL DEFAULT '' COMMENT '备注',
	PRIMARY KEY (guild_id, uid, apply_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公会公告处理记录表';`

func (h *HandleNoticeLog) TableName() string {
	return "handle_notice_log"
}

func (s *Store) AddHandleNoticeLog(tx *gorm.DB, info *HandleNoticeLog) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	// 如果时间为0
	if info.UpdateTime.IsZero() {
		info.UpdateTime = time.Now()
	}

	log.Infof("AddCancelContractApplyLog info=%+v", info)
	return db.Create(info).Error
}

func (s *Store) GetHandleNoticeLog(tx *gorm.DB, guildId, uid, page, pageSize uint32, beginCensorTs, endCensorTs, beginUpdateTs, endUpdateDateTs, status uint32) (uint32, []*HandleNoticeLog, error) {
	total := uint32(0)

	out := make([]*HandleNoticeLog, 0)
	db := s.db
	if tx != nil {
		db = tx
	}

	// base query with filters (without pagination)
	base := db.Table("handle_notice_log")

	if uid != 0 {
		base = base.Where("uid = ?", uid)
	}

	if guildId != 0 {
		base = base.Where("guild_id = ?", guildId)
	}

	// ApplyTime range filters
	if beginCensorTs != 0 {
		base = base.Where("apply_time >= ?", time.Unix(int64(beginCensorTs), 0))
	}
	if endCensorTs != 0 {
		base = base.Where("apply_time <= ?", time.Unix(int64(endCensorTs), 0))
	}

	// UpdateTime range filters
	if beginUpdateTs != 0 {
		base = base.Where("update_time >= ?", time.Unix(int64(beginUpdateTs), 0))
	}
	if endUpdateDateTs != 0 {
		base = base.Where("update_time <= ?", time.Unix(int64(endUpdateDateTs), 0))
	}

	// OperateType filter
	if status != 0 {
		if status == 1{
			base = base.Where("operate_type = ?", status)
		}else if status == 2{
			base = base.Where("operate_type = ?", 0)
		}
	}

	// Query with pagination
	err := base.Offset(page * pageSize).Limit(pageSize).Order("update_time desc").Find(&out).Error
	if err != nil {
		log.Errorf("GetHandleNoticeLog guildId=%d , err=%v", guildId, err)
		return total, nil, err
	}

	// 一个都没有就不用total了
	if len(out) == 0 {
		log.Infof("GetHandleNoticeLog out = nil")
		return total, out, nil
	}

	// Count total with same filters (without pagination)
	err = base.Count(&total).Error
	if err != nil {
		log.Errorf("GetHandleNoticeLog Count err=%v", err)
		return total, nil, err
	}

	log.Infof("GetHandleNoticeLog out=%+v", out)
	return total, out, nil
}
