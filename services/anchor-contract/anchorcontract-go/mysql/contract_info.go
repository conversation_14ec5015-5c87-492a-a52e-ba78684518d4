package mysql

import (
	"context"
	"fmt"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"

	pb "golang.52tt.com/protocol/services/anchorcontract-go"
)

/*
CREATE TABLE IF NOT EXISTS tbl_contract (
    uid int unsigned NOT NULL unique,
    guild_id int unsigned  NOT NULL,
    identity_num varchar(30) NOT NULL COMMENT '签约人的身份证',
    sign_time timestamp NOT NULL COMMENT '签约时间',
    contract_duration int unsigned  NOT NULL COMMENT '合约有效天数',
    contract_expire_time timestamp NOT NULL COMMENT '到期时间',
    update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    permissions int unsigned NOT NULL DEFAULT 0 COMMENT '成员特殊权限',
 	worker_type tinyint NOT NULL DEFAULT 0 COMMENT '从业者类型：see ContractWorkerType',
	pay_amount int NOT NULL DEFAULT 0 COMMENT '核心管理解约金额',
    PRIMARY KEY (uid),
    INDEX index_uid (uid),
    INDEX index_guild_id (guild_id),
    INDEX indentity_num (identity_num)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='主播合约表';
*/

const tblContractName = "tbl_contract"
const queryContractSql = "uid,guild_id,identity_num,contract_duration,permissions,sign_time,contract_expire_time,update_time, worker_type, pay_amount"

// 签约信息
type ContractInfo struct {
	Uid                uint32    `db:"uid"`
	GuildId            uint32    `db:"guild_id"`
	IdentityNum        string    `db:"identity_num"`
	ContractDuration   uint32    `db:"contract_duration"`
	Permissions        uint32    `db:"permissions"`
	SignTime           time.Time `db:"sign_time"`
	ContractExpireTime time.Time `db:"contract_expire_time"`
	UpdateTime         time.Time `db:"update_time"`
	WorkerType         uint32    `db:"worker_type"`
	PayAmount          int64     `db:"pay_amount"`
}

func (t *ContractInfo) TableName() string {
	return tblContractName
}

func (t *ContractInfo) String() string {
	return utils.ToJson(t)
}

func (info *ContractInfo) FillPb() *pb.ContractInfo {
	return &pb.ContractInfo{
		ActorUid:         info.Uid,
		GuildId:          info.GuildId,
		SignTime:         uint32(info.SignTime.Unix()),
		ContractDuration: info.ContractDuration,
		ExpireTime:       uint32(info.ContractExpireTime.Unix()),
		Permission:       info.Permissions,
		IdentityNum:      info.IdentityNum,
		WorkerType:       info.WorkerType,
		PayAmount:        info.PayAmount,
	}
}

func (s *Store) AddContract(tx *gorm.DB, info *ContractInfo) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	err := db.Create(info).Error
	if err != nil {
		log.Errorf("AddContract fail to Create. ContractInfo:%+v, err:%v", info, err)
		return err
	}

	return nil
}

func (s *Store) ContractExtension(tx *gorm.DB, uid, guildId, contractDuration uint32, signTime, newExpireTime time.Time) (bool, error) {
	db := s.db
	if tx != nil {
		db = tx
	}

	sql := fmt.Sprintf("update %s set contract_duration=contract_duration+?, contract_expire_time=?, sign_time=? where uid=? and guild_id =?", tblContractName)

	res := db.Exec(sql, contractDuration, newExpireTime, signTime, uid, guildId)
	err := res.Error
	if err != nil {
		log.Errorf("ContractExtension fail to Create. uid:%v, guildId:%v, err:%v", uid, guildId, err)
		return false, err
	}

	return res.RowsAffected > 0, nil
}

func (s *Store) GetValidContractWithUid(uid uint32) (*ContractInfo, bool, error) {
	info := &ContractInfo{}

	err := s.db.Table(tblContractName).Select(queryContractSql).
		Where("uid=? and contract_expire_time >= ?", uid, time.Now()).Scan(info).Error

	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return info, false, nil
		}

		log.Errorf("GetValidContractWithUid fail to Select. uid:%v, err:%v", uid, err)
		return info, false, err
	}

	log.Debugf("GetValidContractWithUid uid:%d, info:%+v", uid, info)
	return info, true, nil
}

func (s *Store) BatchGetValidContractWithUid(uids []uint32) (map[uint32]*ContractInfo, error) {
	list := []*ContractInfo{}

	err := s.db.Table(tblContractName).Select(queryContractSql).
		Where("uid in(?) and contract_expire_time >= ?", uids, time.Now()).Scan(&list).Error

	m := map[uint32]*ContractInfo{}
	for _, info := range list {
		m[info.Uid] = info
	}
	return m, err
}

func (s *Store) GetContractWithUid(uid uint32) (*ContractInfo, bool, error) {
	info := &ContractInfo{}

	err := s.db.Table(tblContractName).Select(queryContractSql).
		Where("uid=?", uid).Scan(info).Error

	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return info, false, nil
		}

		log.Errorf("GetValidContractWithUid fail to Select. uid:%v, err:%v", uid, err)
		return info, false, err
	}

	log.Debugf("GetValidContractWithUid uid:%d, info:%+v", uid, info)
	return info, true, nil
}

func (s *Store) GetContractByUid(tx *gorm.DB, uid uint32) (*ContractInfo, error) {
	info := &ContractInfo{}
	db := s.db
	if tx != nil {
		db = tx
	}

	err := db.Table(tblContractName).Select(queryContractSql).
		Where("uid=?", uid).Scan(info).Error

	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return info, nil
		}
		log.Errorf("GetContractByUid fail to Select. uid:%v, err:%v", uid, err)
		return info, err
	}

	log.Debugf("GetContractByUid uid:%d, info:%+v", uid, info)
	return info, nil
}

func (s *Store) GetContract(begin, limit uint32) ([]*ContractInfo, error) {
	list := make([]*ContractInfo, 0, limit)

	err := s.db.Table(tblContractName).Select(queryContractSql).
		Offset(begin).Limit(limit).Scan(&list).Error

	if err != nil {
		log.Errorf("GetContract fail to Select. err:%v", err)
		return list, err
	}

	log.Debugf("GetContract list:%+v", list)
	return list, nil
}

func (s *Store) GetGuildContract(guildId, begin, limit uint32) ([]*ContractInfo, error) {
	list := make([]*ContractInfo, 0, limit)

	err := s.db.Table(tblContractName).Select(queryContractSql).Where("guild_id=?", guildId).
		Offset(begin).Limit(limit).Scan(&list).Error

	if err != nil {
		log.Errorf("GetGuildContract fail to Select. err:%v", err)
		return list, err
	}

	log.Debugf("GetGuildContract list:%+v", list)
	return list, nil
}

func (s *Store) GetGuildContractByCondition(guildId, begin, limit, workerType uint32, isSelectWorker bool) ([]*ContractInfo, error) {
	list := make([]*ContractInfo, 0, limit)

	db := s.db.Table(tblContractName).Select(queryContractSql).Where("guild_id=?", guildId)
	if isSelectWorker {
		db = db.Where("worker_type=?", workerType)
	}

	err := db.Offset(begin).Limit(limit).Scan(&list).Error
	if err != nil {
		log.Errorf("GetGuildContractByCondition fail to Select. err:%v", err)
		return list, err
	}

	log.Debugf("GetGuildContractByCondition list:%+v", list)
	return list, nil
}

func (s *Store) GetGuildContractByIdentity(guildId, agentUid, identity, anchorFlag, offset, limit uint32, nowTm time.Time, agentUidList, uidList []uint32) ([]*ContractInfo, error) {
	list := make([]*ContractInfo, 0)

	baseSql := fmt.Sprintf("SELECT a.* FROM %s a INNER JOIN %s b on a.guild_id = b.guild_id and a.uid = b.uid", tblContractName, tblAnchorIdentityName)
	var conditions []string
	var args []interface{}

	conditions = append(conditions, "a.guild_id = ?")
	args = append(args, guildId)
	conditions = append(conditions, "b.identity_type = ?")
	args = append(args, identity)

	switch pb.ANCHOR_FLAG(anchorFlag) {
	case pb.ANCHOR_FLAG_AllAnchor:
		// 获取所有
	case pb.ANCHOR_FLAG_NewSignAnchor:
		// 近30天内（含30天）签约公会并开通直播权限的主播
		nowBeginTm := time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day(), 0, 0, 0, 0, time.Local)
		lastBeginTm := nowBeginTm.AddDate(0, 0, -29)

		// 签约时间大于等于lastBeginTm
		conditions = append(conditions, "a.sign_time >= ?")
		args = append(args, lastBeginTm)

	case pb.ANCHOR_FLAG_SoonSignExpireAnchor:
		// 指将于近30天内（含30天）签约到期的主播
		nowBeginTm := time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day(), 0, 0, 0, 0, time.Local)
		NextBeginTm := nowBeginTm.AddDate(0, 0, 30)

		// 签约到期时间小于NextBeginTm
		conditions = append(conditions, "a.contract_expire_time < ?")
		args = append(args, NextBeginTm)
	}

	if agentUid != 0 {
		// 查询指定经纪人
		conditions = append(conditions, "b.agent_uid = ?")
		args = append(args, agentUid)
	}

	if len(agentUidList) > 0 {
		// 查询指定经纪人列表
		conditions = append(conditions, "b.agent_uid in (?)")
		args = append(args, agentUidList)
	}

	if len(uidList) > 0 {
		// 查询指定uid列表
		conditions = append(conditions, "b.uid in (?)")
		args = append(args, uidList)
	}

	if len(conditions) > 0 {
		baseSql += " WHERE " + strings.Join(conditions, " AND ")
	}

	err := s.db.Raw(baseSql, args...).Offset(offset).Limit(limit).Scan(&list).Error
	if gorm.IsRecordNotFoundError(err) {
		return list, nil
	}
	return list, err
}

func (s *Store) GetGuildContractTotalCntByIdentity(guildId, agentUid, identity, anchorFlag uint32, nowTm time.Time, agentUidList, uidList []uint32) (uint32, error) {
	type Result struct {
		Cnt uint32
	}

	baseSql := fmt.Sprintf("SELECT count(1) as cnt FROM %s a INNER JOIN %s b on a.guild_id = b.guild_id and a.uid = b.uid", tblContractName, tblAnchorIdentityName)
	var conditions []string
	var args []interface{}

	conditions = append(conditions, "a.guild_id = ?")
	args = append(args, guildId)
	conditions = append(conditions, "b.identity_type = ?")
	args = append(args, identity)

	//db = db.Where("identity_type = ?", identity)
	switch pb.ANCHOR_FLAG(anchorFlag) {
	case pb.ANCHOR_FLAG_AllAnchor:
		// 获取所有
	case pb.ANCHOR_FLAG_NewSignAnchor:
		// 近30天内（含30天）签约公会并开通直播权限的主播
		nowBeginTm := time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day(), 0, 0, 0, 0, time.Local)
		lastBeginTm := nowBeginTm.AddDate(0, 0, -29)

		conditions = append(conditions, "a.sign_time >= ?")
		args = append(args, lastBeginTm)
	case pb.ANCHOR_FLAG_SoonSignExpireAnchor:
		// 指将于近30天内（含30天）签约到期的主播
		nowBeginTm := time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day(), 0, 0, 0, 0, time.Local)
		NextBeginTm := nowBeginTm.AddDate(0, 0, 30)

		conditions = append(conditions, "a.contract_expire_time < ?")
		args = append(args, NextBeginTm)
	}

	// 查询指定经纪人
	if agentUid != 0 {
		conditions = append(conditions, "b.agent_uid = ?")
		args = append(args, agentUid)
	}

	if len(agentUidList) > 0 {
		// 查询指定经纪人列表
		conditions = append(conditions, "b.agent_uid in (?)")
		args = append(args, agentUidList)
	}

	// 查询指定uid列表
	if len(uidList) > 0 {
		conditions = append(conditions, "b.uid in (?)")
		args = append(args, uidList)
	}

	if len(conditions) > 0 {
		baseSql += " WHERE " + strings.Join(conditions, " AND ")
	}

	res := &Result{Cnt: 0}

	err := s.db.Raw(baseSql, args...).Scan(res).Error

	if gorm.IsRecordNotFoundError(err) {
		return res.Cnt, nil
	}
	return res.Cnt, err
}

func (s *Store) GetGuildContractWithUidList(guildId uint32, uidList []uint32) ([]*ContractInfo, error) {
	list := make([]*ContractInfo, 0, len(uidList))
	if len(uidList) == 0 {
		return list, nil
	}

	err := s.db.Table(tblContractName).Select(queryContractSql).
		Where("guild_id=? and uid in (?) and contract_expire_time >= ?", guildId, uidList, time.Now()).Scan(&list).Error

	if err != nil {
		log.Errorf("GetGuildContractWithUidList fail to Select. guildId:%v, uidList:%v, err:%v", guildId, uidList, err)
		return list, err
	}

	return list, nil
}

func (s *Store) GetContractWithUidList(uidList []uint32) ([]*ContractInfo, error) {
	list := make([]*ContractInfo, 0, len(uidList))
	if len(uidList) == 0 {
		return list, nil
	}

	err := s.db.Table(tblContractName).Select(queryContractSql).
		Where("uid in (?) and contract_expire_time >= ?", uidList, time.Now()).Scan(&list).Error

	if err != nil {
		log.Errorf("GetGuildContractWithUidList fail to Select. uidList:%v, err:%v", uidList, err)
		return list, err
	}

	return list, nil
}

func (s *Store) GetContractWithIdentityNum(identityNum string) (*ContractInfo, bool, error) {
	info := &ContractInfo{}

	err := s.db.Table(tblContractName).Select(queryContractSql).
		Where("identity_num=? and contract_expire_time >= ?", identityNum, time.Now()).Scan(info).Error

	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return info, false, nil
		}

		log.Errorf("GetContractWithIdentityNum fail to Select. identityNum:%v, err:%v", identityNum, err)
		return info, false, err
	}

	return info, true, nil
}

func (s *Store) DelContract(tx *gorm.DB, uid, guildId uint32) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	err := db.Table(tblContractName).Delete(&ContractInfo{}, "uid=? and guild_id=?", uid, guildId).Error
	if err != nil {
		log.Errorf("DelContract fail to Delete. uid:%v, guildId:%v, err:%v", uid, guildId, err)
		return err
	}

	return nil
}

func (s *Store) GetExpireContract(expiredTime time.Time) ([]*ContractInfo, error) {
	list := make([]*ContractInfo, 0)

	err := s.db.Table(tblContractName).Select(queryContractSql).
		Where("contract_expire_time < ?", expiredTime).Scan(&list).Error

	if err != nil {
		log.Errorf("GetExpireContract fail to Select. expiredTime:%v, err:%v", expiredTime, err)
		return list, err
	}

	return list, nil
}

func (s *Store) DelExpireContract(expiredTime time.Time) error {

	err := s.db.Table(tblContractName).Delete(&ContractInfo{}, "contract_expire_time < ?", expiredTime).Error
	if err != nil {
		log.Errorf("DelExpireContract fail to Delete. expiredTime:%v, err:%v", expiredTime, err)
		return err
	}

	return nil
}

func (s *Store) GetAllSignGuildList() ([]uint32, error) {
	list := make([]uint32, 0)

	rows, err := s.db.Table(tblContractName).Select("distinct(guild_id)").Rows()

	if err != nil {
		log.Errorf("GetGuildUidListWithIdentity fail to Select. err:%v", err)
		return list, err

	}
	if rows.Err() != nil {
		log.Errorln(rows.Err())
	}
	defer rows.Close()

	for rows.Next() {
		var guildId uint32
		err = rows.Scan(&guildId)
		if err == nil {
			list = append(list, guildId)
		}
	}

	return list, nil
}

func (s *Store) GetGuildContractCount(guildId uint32) (uint32, error) {
	rows, err := s.db.Table(tblContractName).Select("count(distinct(uid))").
		Where("guild_id=? and contract_expire_time >= ?", guildId, time.Now()).Rows()

	if err != nil {
		log.Errorf("GetGuildContractCount fail to Select. guildId:%v, err:%v", guildId, err)
		return 0, err
	}

	cnt := uint32(0)

	if rows.Err() != nil {
		log.Errorln(rows.Err())
	}

	defer rows.Close()
	if rows.Next() {
		_ = rows.Scan(&cnt)
	}

	log.Debugf("GetGuildContractCount guildId:%d, cnt:%+v", guildId, cnt)
	return cnt, nil
}

func (s *Store) GetRenewAbleContractCnt(guildId uint32) (uint32, error) {
	now := time.Now()
	thisMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	theMonthAfterNext := thisMonth.AddDate(0, 2, 0)

	rows, err := s.db.Table(tblContractName).Select("count(distinct(uid))").
		Where("guild_id=? and contract_expire_time >= ? and contract_expire_time < ?",
			guildId, now, theMonthAfterNext).Rows()

	if err != nil {
		log.Errorf("GetRenewAbleContractCnt fail to Select. guildId:%v, err:%v", guildId, err)
		return 0, err
	}

	if rows.Err() != nil {
		log.Errorln(rows.Err())
	}

	cnt := uint32(0)

	defer rows.Close()
	if rows.Next() {
		_ = rows.Scan(&cnt)
	}

	log.Debugf("GetRenewAbleContractCnt guildId:%d, cnt:%+v", guildId, cnt)
	return cnt, nil
}

func (s *Store) GetGuildContractByQuery(queryType, guildId uint32, uids []uint32, begin, limit uint32) (uint32, []*ContractInfo, error) {
	list := []*ContractInfo{}

	db := s.readonlyDb
	db = db.Table(tblContractName).Where("guild_id=?", guildId)
	if len(uids) > 0 {
		db = db.Where("uid in(?)", uids)
	}
	db = db.Where("contract_expire_time >= ?", time.Now())

	// 签约时间小于等于30日的签约用户   （ 今日日期 - 签约生效日期 ≤30天）
	if queryType == uint32(pb.QUERY_ANCHOR_TYPE_QUERY_ANCHOR_TYPE_NEWSIGN) {
		now := time.Now()
		begin := now.AddDate(0, 0, -30)
		db = db.Where("sign_time>=?", begin)

	} else if queryType == uint32(pb.QUERY_ANCHOR_TYPE_QUERY_ANCHOR_TYPE_READYEXT) {
		// 签约到期时间小于等于30天的用户（ 签约到期日期 - 今日日期 ≤30天）
		now := time.Now()
		begin := now.AddDate(0, 0, +30)
		db = db.Where("contract_expire_time<=?", begin)
	}

	total, err := getRowsCnt(db)
	if err != nil {
		return 0, list, err
	}

	err = db.Order("sign_time DESC, uid DESC").Offset(begin).Limit(limit).Find(&list).Error
	log.Debugf("GetGuildContractByQuery queryType:%d, guildId:%d, uids:%v, begin:%d, limit:%d, total:%d, list:%+v", queryType, guildId, uids, begin, limit, total, list)
	return total, list, err
}

func (s *Store) GetGuildContractByUids(guildId uint32, uids []uint32) ([]*ContractInfo, error) {
	list := []*ContractInfo{}

	db := s.readonlyDb.Table(tblContractName).Where("guild_id=?", guildId)
	if len(uids) > 0 {
		db = db.Where("uid in(?)", uids)
	}
	db = db.Where("contract_expire_time >= ?", time.Now())

	err := db.Order("sign_time DESC").Find(&list).Error
	return list, err
}

func (s *Store) GetContractByUids(uids []uint32, offset, limit uint32) ([]uint32, []*ContractInfo, map[uint32]*ContractInfo, error) {
	uid2contract := map[uint32]*ContractInfo{}
	list := []*ContractInfo{}
	fetchuids := []uint32{}

	db := s.db.Table(tblContractName)
	db = db.Where("uid in(?)", uids).Order("sign_time desc").Offset(offset).Limit(limit).Find(&list)

	for _, info := range list {
		uid2contract[info.Uid] = info
		fetchuids = append(fetchuids, info.Uid)
	}

	return fetchuids, list, uid2contract, db.Error
}

func (s *Store) GetContractReadOnly(offset, limit uint32) (uint32, []*ContractInfo, error) {
	list := make([]*ContractInfo, 0, limit)
	db := s.readonlyDb.Table(tblContractName)
	err := db.Select(queryContractSql).Order("uid").
		Offset(offset).Limit(limit).Find(&list).Error
	if err != nil {
		log.Errorf("GetContractReadOnly fail to Select. err:%v", err)
		return 0, list, err
	}
	var cnt uint32
	err = db.Count(&cnt).Error
	if err != nil {
		return 0, nil, err
	}
	return cnt, list, nil
}

func (s *Store) GetAllContractGuildId() ([]uint32, error) {
	// select distinct(guild_id) from tbl_contract;
	rows, err := s.db.Table(tblContractName).Select("distinct(guild_id) as guild_id").Rows()
	if err != nil {
		return nil, err
	}
	if rows.Err() != nil {
		log.Errorln(rows.Err())
	}
	defer rows.Close()

	guildIds := make([]uint32, 0)
	for rows.Next() {
		var gid uint32
		err = rows.Scan(&gid)
		if err == nil {
			guildIds = append(guildIds, gid)
		}
	}
	return guildIds, nil
}

func (s *Store) UpdateContractWorkerType(tx *gorm.DB, uid, guildId, workerType uint32) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	err := db.Table(tblContractName).Where("uid=? and guild_id=?", uid, guildId).
		Update("worker_type", workerType).Error
	if err != nil {
		log.Errorf("UpdateContractWorkerType fail to Update. uid:%v, guildId:%v, err:%v", uid, guildId, err)
		return err
	}
	log.Infof("UpdateContractWorkerType success. uid:%v, guildId:%v, workerType:%v", uid, guildId, workerType)
	return nil
}

// PromoteManagerExtensionContract 晋升管理续约
func (s *Store) PromoteManagerExtensionContract(tx *gorm.DB, uid, guildId, contractDuration uint32, signTime, newExpireTime time.Time, payAmount int64) (bool, error) {
	db := s.db
	if tx != nil {
		db = tx
	}
	res := db.Table(tblContractName).Where("uid=? and guild_id=?", uid, guildId).Updates(map[string]interface{}{
		"contract_duration":    contractDuration,
		"contract_expire_time": newExpireTime,
		"sign_time":            signTime,
		"worker_type":          uint32(pb.ContractWorkerType_ContractWorkerType_Manager),
		"pay_amount":           payAmount,
	})

	if res.Error != nil {
		log.Errorf("PromoteManagerExtensionContract fail to Create. uid:%v, guildId:%v, err:%v", uid, guildId, res.Error)
		return false, res.Error
	}
	log.Infof("PromoteManagerExtensionContract success. uid:%v, guildId:%v, contractDuration:%v, signTime:%v, newExpireTime:%v, payAmount:%v", uid, guildId, contractDuration, signTime, newExpireTime, payAmount)
	return res.RowsAffected > 0, nil
}

func (s *Store) UpdateContractSignTime(ctx context.Context, uid, guildId uint32, signTime time.Time) error {
	err := s.db.Table(tblContractName).Where("uid=? and guild_id=?", uid, guildId).
		Update("sign_time", signTime).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateContractSignTime fail to Update. uid:%v, guildId:%v, err:%v", uid, guildId, err)
		return err
	}
	log.InfoWithCtx(ctx, "UpdateContractSignTime success. uid:%v, guildId:%v, signTime:%v", uid, guildId, signTime)
	return nil
}

func (s *Store) GetAllContractUids(ctx context.Context) ([]uint32, error) {
	allUids := make([]uint32, 0)
	limit := 2000
	offset := 0
	log.InfoWithCtx(ctx, "GetAllContractUids Start to get all contract uids with ")
	uidsMap := make(map[uint32]struct{})
	type Uid struct {
		Uid uint32 `gorm:"column:uid" db:"uid"`
	}
	for {
		uids := make([]*Uid, 0)
		err := s.db.Table(tblContractName).Select("uid").Offset(offset).Limit(limit).Find(&uids).Error
		if err != nil {
			log.Errorf("Failed to get all contract uids: %v", err)
			return nil, err
		}
		for _, uid := range uids {
			if _, exists := uidsMap[uid.Uid]; !exists {
				uidsMap[uid.Uid] = struct{}{}
				allUids = append(allUids, uid.Uid)
			}
		}

		if len(uids) < limit {
			break
		}
		offset += limit
		time.Sleep(50 * time.Millisecond) // 避免对数据库造成过大压力
	}
	log.InfoWithCtx(ctx, "GetAllContractUids Get %d uids from tbl_contract", len(allUids))
	return allUids, nil
}
