package mysql

import (
	"golang.52tt.com/pkg/log"
)

const (
	guildNoticeVerifyTableName = "guild_notice_verify"
	guildNoticeTableName       = "guild_notice"
)

type GuildNoticeVerify struct {
	Uid      uint32 `gorm:"column:uid"`
	GuildId  uint32 `gorm:"column:guild_id"`
	Title    string `gorm:"column:title"`
	Content  string `gorm:"column:content"`
	SummitTs uint32 `gorm:"column:summit_ts"`
}

func (t *GuildNoticeVerify) TableName() string {
	return guildNoticeVerifyTableName
}

func (s *Store) GetVerifyingNoticeList(offset, limit, beginTs, endTs, guildId, masterId uint32) (uint32, []*GuildNoticeVerify, error) {
	list := make([]*GuildNoticeVerify, 0)

	// Build base query with optional time and id filters
	db := s.db.Table(guildNoticeVerifyTableName)
	if beginTs > 0 {
		db = db.Where("summit_ts >= FROM_UNIXTIME(?)", beginTs)
	}
	if endTs > 0 {
		db = db.Where("summit_ts <= FROM_UNIXTIME(?)", endTs)
	}
	if guildId > 0 {
		db = db.Where("guild_id = ?", guildId)
	}
	if masterId > 0 {
		db = db.Where("uid = ?", masterId)
	}

	var total uint32
	if err := db.Count(&total).Error; err != nil {
		log.Errorf("GetVerifyingNoticeList count failed, err:%v", err)
		return 0, list, err
	}

	if total == 0 {
		return 0, list, nil
	}

	query := db.Select("uid, guild_id, CAST(CONVERT(title USING latin1) AS BINARY) as title, CAST(CONVERT(content USING latin1) AS BINARY) as content, UNIX_TIMESTAMP(summit_ts) as summit_ts")
	if err := query.Order("summit_ts").Offset(offset).Limit(limit).Scan(&list).Error; err != nil {
		log.Errorf("GetVerifyingNoticeList scan failed, err:%v", err)
		return 0, nil, err
	}

	return total, list, nil
}

type GuildNotice struct {
	Uid      uint32 `gorm:"column:uid"`
	GuildId  uint32 `gorm:"column:guild_id"`
	Title    string `gorm:"column:title"`
	Content  string `gorm:"column:content"`
	UpdateTs uint32 `gorm:"column:update_ts"`
}

func (t *GuildNotice) TableName() string {
	return guildNoticeTableName
}

func (s *Store) GetOnlineNoticeList(offset, limit, beginTs, endTs, guildId, masterId uint32) (uint32, []*GuildNotice, error) {
	list := make([]*GuildNotice, 0)

	// Build base query with optional time and id filters
	db := s.db.Table(guildNoticeTableName)
	if beginTs > 0 {
		db = db.Where("update_ts >= FROM_UNIXTIME(?)", beginTs)
	}
	if endTs > 0 {
		db = db.Where("update_ts <= FROM_UNIXTIME(?)", endTs)
	}
	if guildId > 0 {
		db = db.Where("guild_id = ?", guildId)
	}
	if masterId > 0 {
		db = db.Where("uid = ?", masterId)
	}

	var total uint32
	if err := db.Count(&total).Error; err != nil {
		log.Errorf("GetOnlineNoticeList count failed, err:%v", err)
		return 0, list, err
	}

	if total == 0 {
		return 0, list, nil
	}

	query := db.Select("uid, guild_id, CAST(CONVERT(title USING latin1) AS BINARY) as title, CAST(CONVERT(content USING latin1) AS BINARY) as content,  UNIX_TIMESTAMP(update_ts) as update_ts")
	if err := query.Order("update_ts DESC").Offset(offset).Limit(limit).Scan(&list).Error; err != nil {
		log.Errorf("GetOnlineNoticeList scan failed, err:%v", err)
		return 0, nil, err
	}

	return total, list, nil
}
