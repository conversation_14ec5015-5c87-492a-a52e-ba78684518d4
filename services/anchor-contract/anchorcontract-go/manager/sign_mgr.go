package manager

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
	apicenterPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	"golang.52tt.com/protocol/services/apicentergo"
	channellivestats "golang.52tt.com/protocol/services/channel-live-stats"
	livePb "golang.52tt.com/protocol/services/channellivemgr"
	"golang.52tt.com/protocol/services/esport_role"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkaanchorcontract"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql"
)

func (m *AnchorContractMgr) CheckIsSignWhiteUid(ctx context.Context, in *pb.CheckIsSignWhiteUidReq) (bool, error) {
	log.InfoWithCtx(ctx, "CheckIsSignWhiteUid begin ActorUid=%d ", in.GetUid())
	inWhiteList, err := m.store.CheckIsSignWhiteUid(in.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignPreCheck fail to GetUserApplyBlacklist in:%+v, err:%v", in, err)
		return false, err
	}

	return inWhiteList, nil
}

func (m *AnchorContractMgr) AddSignWhiteUid(ctx context.Context, in *pb.AddSignWhiteUidReq) error {
	log.InfoWithCtx(ctx, "AddSignWhiteUid begin ActorUid=%d ", in.GetUid())
	for _, uid := range in.GetUid() {
		err := m.store.AddSignWhitelist(nil, &mysql.SignWhitelist{Uid: uid})
		if err != nil {
			log.ErrorWithCtx(ctx, "AddSignWhiteUid fail to AddSignWhiteUid uid:%+v, err:%v", uid, err)
			return err
		}
	}
	return nil
}

func (m *AnchorContractMgr) DelSignWhiteUid(ctx context.Context, in *pb.DelSignWhiteUidReq) error {
	log.InfoWithCtx(ctx, "DelSignWhiteUid begin ActorUid=%d ", in.GetUid())
	for _, uid := range in.GetUid() {
		err := m.store.DelSignWhitelist(nil, &mysql.SignWhitelist{Uid: uid})
		if err != nil {
			log.ErrorWithCtx(ctx, "DelSignWhiteUid fail to DelSignWhiteUid uid:%+v, err:%v", uid, err)
			return err
		}
	}
	return nil
}

// 申请签约/身份
func (m *AnchorContractMgr) ApplySignContract(ctx context.Context, in *pb.ApplySignContractReq) error {
	log.InfoWithCtx(ctx, "ApplySignContract begin ActorUid=%d GuildId=%d AnchorIdentity=%d IdentityNum=%q ContractDuration=%d Form=%+v",
		in.ActorUid, in.GuildId, in.AnchorIdentity, in.IdentityNum, in.ContractDuration, in.Form)

	now := time.Now()
	err := m.ApplySignPreCheck(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignContract fail to ApplySignPreCheck in:%+v, err:%v", in, err)
		return err
	}

	signEsportGuildId, err := m.EsportRoleCli.CheckSignGuildRiskAuditing(ctx, in.ActorUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignContract fail to CheckSignGuildRiskAuditing in:%+v, err:%v", in, err)
		return err
	}
	if signEsportGuildId == in.GuildId {
		log.ErrorWithCtx(ctx, "ApplySignContract CheckSignGuildRiskAuditing hit. anchor_uid=%d guild_id=%d", in.ActorUid, in.GuildId)
		return protocol.NewExactServerError(nil, status.ErrContractApplyIdentityTypeLimit)
	}

	//主播当前是否有对公结算签约，且对公结算签约的公会与当前申请签约公会不一致时，拦截申请
	anchorInfo, err := m.ExchangeGuildClient.GetUidGuildExchangeData(ctx, in.ActorUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignContract fail to GetAnchorInfo in:%+v, err:%v", in, err)
		return err
	}
	if anchorInfo.GetGuildId() != 0 && anchorInfo.GetGuildId() != in.GetGuildId() {
		log.ErrorWithCtx(ctx, "ApplySignContract fail to GetAnchorInfo in:%+v, anchorInfo:%v", in, anchorInfo)
		return protocol.NewExactServerError(nil, status.ErrContractApplySignSettlement)
	}

	form := in.GetForm()

	formExtra, err := json.Marshal(form)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignContract fail to Marshal. in:%+v, :%v", in, err)
	}

	isNewbieAnchor, err := m.IsNewbieAnchor(ctx, in.ActorUid, in.IdentityNum, in.AnchorIdentity)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignContract fail to IsNewbieAnchor in:%+v, err:%v", in, err)
		return err
	}
	isNewbie := uint32(0)
	if isNewbieAnchor { // 新主播
		isNewbie = 1
	}

	tmpCtx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	recharge, _ := m.GetTotalRecharge(tmpCtx, in.GetActorUid())
	age := m.GetAgeWithIdentificationNumber(in.GetIdentityNum())
	isGreatAnchor := bool(false)
	conformStatus := uint32(1)

	err = m.store.AddContractApply(nil, &mysql.ContractApply{
		Uid:              in.GetActorUid(),
		GuildId:          in.GetGuildId(),
		ContractDuration: in.GetContractDuration(),
		IdentityType:     in.GetAnchorIdentity(),
		IdentityNum:      in.GetIdentityNum(),
		ApplyStatus:      uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_HANDLING),
		ApplyTime:        time.Now(),
		UpdateTime:       time.Now(),
		FormExtra:        string(formExtra),
		Extra:            m.GenApplyExtraInfoString(ctx, in, isGreatAnchor, recharge),
		ConformStatus:    conformStatus,
		Age:              uint32(age),
		RechargeNum:      recharge,
		WorkerType:       in.WorkerType,
		IsNewbie:         isNewbie,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignContract fail to AddContractApply in:%+v, err:%v", in, err)
		return err
	}

	log.InfoWithCtx(ctx, "ApplySignContract in %+v identity %d recharge %d age %d isGreatAnchor %v conformStatus %d cost:%v",
		in, in.GetAnchorIdentity(), recharge, age, isGreatAnchor, conformStatus, time.Since(now))
	return nil
}

// 会长审批
func (m *AnchorContractMgr) PresidentHandleApplySign(ctx context.Context, in *pb.PresidentHandleApplySignReq) error {
	log.InfoWithCtx(ctx, "PresidentHandleApplySign ApplyId %d HandleOpr %d", in.ApplyId, in.HandleOpr)

	var goalStatus uint32

	switch pb.HANDLE_SIGN_APPLY_OPR(in.GetHandleOpr()) {
	case pb.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_ACCEPT:
		goalStatus = uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_HANDLING)

	case pb.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_REJECT:
		goalStatus = uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_REJECT)

	default:
		log.ErrorWithCtx(ctx, "PresidentHandleApplySign fail. ErrBadRequest in:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrBadRequest)
	}

	var applyPbInfo *pb.ApplySignRecord

	checkDay := int(m.dyConfig.GetGuildSignAnchorLimitDayTh())

	var applyInfo = &mysql.ContractApply{}
	var err error

	// 开启事务
	err = m.store.Transaction(ctx, func(tx *gorm.DB) error {

		applyInfo, err = m.store.GetContractApplyWithId(tx, in.GetApplyId())
		if err != nil {
			log.ErrorWithCtx(ctx, "PresidentHandleApplySign fail to GetContractApplyWithId in:%+v, err:%v", in, err)
			return err
		}
		applySignGuildId := applyInfo.GuildId
		if in.GetHandleOpr() == uint32(pb.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_ACCEPT) {
			if applyInfo.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
				now := time.Now()
				monthBegin := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
				monthEnd := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second)
				yearmonth := fmt.Sprintf("%d-%02d", now.Year(), now.Month())
				exist, info, err := m.store.GetGuildSignAnchorInfo(tx, applySignGuildId, yearmonth)
				if err != nil {
					log.ErrorWithCtx(ctx, "PresidentHandleApplySign GetGuildSignAnchorInfo fail %v,guildId=%d applyId=%d", err, applySignGuildId, in.ApplyId)
					return err
				}
				// 如果没有配置，则info值都为0直接限制
				if !exist { //这里计条日志
					log.ErrorWithCtx(ctx, "PresidentHandleApplySign GetGuildSignAnchorInfo not exist, guildId=%d applyId=%d", applySignGuildId, in.ApplyId)
				}

				monthUsedCnt, nowDayUsedCnt, err := m.store.GetGuildSignAnchorTimeRangeCnt(tx, applySignGuildId, monthBegin, monthEnd, now, true)
				if err != nil {
					log.ErrorWithCtx(ctx, "PresidentHandleApplySign GetGuildSignAnchorTimeRangeCnt fail %v,guildId=%d applyId=%d", err, applySignGuildId, in.ApplyId)
					return err
				}
				if now.Day() <= checkDay {
					if nowDayUsedCnt >= info.MonthBefore25thCnt {
						err = protocol.NewExactServerError(nil, status.ErrContractAudioLilmitStatusDay)
						log.ErrorWithCtx(ctx, "PresidentHandleApplySign fail %v, guildId=%d applyId=%d, checkDay:%d", err, applySignGuildId, in.ApplyId, checkDay)
						return err
					}
				}
				if monthUsedCnt >= info.MonthCnt {
					err = protocol.NewExactServerError(nil, status.ErrContractAudioLilmitStatusMonth)
					log.ErrorWithCtx(ctx, "PresidentHandleApplySign fail %v, guildId=%d applyId=%d", err, applySignGuildId, in.ApplyId)
					return err
				}

			}
		}

		change, err := m.store.UpdateContractApplyStatusWithId(tx, in.GetApplyId(), uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_HANDLING),
			goalStatus, "guild president", "")
		if err != nil {
			log.ErrorWithCtx(ctx, "PresidentHandleApplySign fail to UpdateContractApplyStatusWithId in:%+v, err:%v", in, err)
			return err
		}

		if !change {
			log.ErrorWithCtx(ctx, "PresidentHandleApplySign fail to UpdateContractApplyStatusWithId, nothing update. in:%+v", in)
			return protocol.NewExactServerError(nil, status.ErrContractHandleInvalid)
		}

		if in.GetHandleOpr() == uint32(pb.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_ACCEPT) {

			preStatusList := []uint32{
				uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_HANDLING),
			}
			// 该用户其他正在审批中的申请更新为失效状态
			_, err = m.store.UpdateContractApplyStatus(tx, applyInfo.Uid, applyInfo.IdentityType, preStatusList, uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_INVALID))
			if err != nil {
				log.ErrorWithCtx(ctx, "PresidentHandleApplySign fail to UpdateContractApplyStatus in:%+v, err:%v", applyInfo, err)
				return err
			}

			if applyInfo.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
				afterApplyInfo, err := m.store.GetContractApplyWithId(tx, in.GetApplyId())
				if err != nil {
					log.ErrorWithCtx(ctx, "PresidentHandleApplySign fail to GetContractApplyWithId in:%+v, err:%v", in, err)
					return err
				}
				guildHandleTime := afterApplyInfo.UpdateTime
				log.DebugfWithCtx(ctx, "PresidentHandleApplySign GetContractApplyWithId info=%+v, guildHandleTime=%d", afterApplyInfo, guildHandleTime.Unix())
				// 会长审批通过后，记录个数
				err = m.store.AddGuildSignAnchorLog(tx, applyInfo.GuildId, guildHandleTime)
				if err != nil {
					log.ErrorWithCtx(ctx, "PresidentHandleApplySign fail to store.AddGuildSignAnchorLog in:%+v, applyPbInfo:%+v err:%v",
						in, applyPbInfo, err)
					return err
				}
				log.InfoWithCtx(ctx, "PresidentHandleApplySign AddGuildSignAnchorLog guildId=%d applyId=%d", applyInfo.GuildId, in.ApplyId)
			}

			applyPbInfo = m.genApplyRecordPb(applyInfo)
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PresidentHandleApplySign fail to begin Transaction in:%+v, err:%v", in, err)
		return err
	}

	if in.GetHandleOpr() == uint32(pb.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_ACCEPT) &&
		applyPbInfo.GetAnchorIdentity() == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
		go m.CheckIfNeedSysQuickHandle(applyPbInfo, applyInfo)
	}

	if applyInfo.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS) {

		auditToken, err := m.store.GetSignEsportAuditToken(applyInfo.Id)
		if err != nil {
			log.ErrorWithCtx(ctx, "PresidentHandleApplySign GetSignEsportAuditToken fail %v, applyInfo=%+v", err, applyInfo)
			return err
		}
		if auditToken == "" {
			log.ErrorWithCtx(ctx, "PresidentHandleApplySign GetSignEsportAuditToken no find, applyInfo=%+v", err, applyInfo)
			return nil
		}
		req := &esport_role.ESportGuildAuditResultReq{
			AuditToken: auditToken,
			Uid:        applyInfo.Uid,
			GuildId:    applyInfo.GuildId,
			AuditType:  uint32(esport_role.ApplyESportAuditType_ESPORT_AUDIT_TYPE_GUILD_PASS),
		}
		if in.HandleOpr == uint32(pb.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_REJECT) {
			req.AuditType = uint32(esport_role.ApplyESportAuditType_ESPORT_AUDIT_TYPE_GUILD_REJECT)
		}
		log.Infof("PresidentHandleApplySign ESportGuildAuditResult uid=%d id=%d req=%s", applyInfo.Uid, applyInfo.Id, req.String())
		for retry := 0; retry < 3; retry++ {
			_, err = m.EsportRoleCli.ESportGuildAuditResult(ctx, req)
			if err == nil {
				break
			}
			log.Errorf("PresidentHandleApplySign ESportGuildAuditResult fail %v, retry=%d applyInfo=%+v",
				err, retry, applyInfo)
			time.Sleep(time.Second)
		}
	}

	log.InfoWithCtx(ctx, "PresidentHandleApplySign end in %+v, applyPbInfo %+v", in, applyPbInfo)
	return nil
}

// 系统自动快速审批
func (m *AnchorContractMgr) CheckIfNeedSysQuickHandle(applyPbInfo *pb.ApplySignRecord, apply *mysql.ContractApply) {
	//age, _ := utils.GetAgeByIdentityNum(applyPbInfo.GetIdentityNum())
	age := apply.Age //统一用数据库的

	log.Infof("CheckIfNeedSysQuickHandle applyPbInfo uid %d guild %d age %d AutoHandleAge %d Recharge %d AutoHandleRecharge %d GetLoginOtherUids cnt %d",
		applyPbInfo.GetUid(), applyPbInfo.GetGuildId(), age, m.sc.AutoHandleAge,
		applyPbInfo.GetMultiAnchorExtra().GetRecharge(), m.sc.AutoHandleRecharge,
		len(applyPbInfo.GetMultiAnchorExtra().GetLoginOtherUids()))
	log.Infof("CheckIfNeedSysQuickHandle apply=%+v", *apply)

	if age < 45 && apply.RechargeNum <= 5000 &&
		len(applyPbInfo.GetMultiAnchorExtra().GetLoginOtherUids()) == 0 {

		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		// 官方审批通过
		err := m.apiCenterGoCli.OfficialHandleApplySign(ctx, 0, &apicentergo.OfficialHandleApplySignReq{
			ApplyId:    applyPbInfo.GetApplyId(),
			HandleOpr:  uint32(pb.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_ACCEPT),
			Uid:        applyPbInfo.GetUid(),
			AnchorType: applyPbInfo.GetAnchorIdentity(),
			Operator:   "sys",
			Remarks:    "system auto handle",
			GuildId:    applyPbInfo.GetGuildId(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckIfNeedSysQuickHandle fail to OfficialHandleApplySign applyPbInfo:%+v, err:%v", applyPbInfo, err)
			return
		}

		log.InfoWithCtx(ctx, "CheckIfNeedSysQuickHandle applyPbInfo %+v", applyPbInfo)
	}
}

// 会长审批
func (m *AnchorContractMgr) PresidentHandleAllApplySign(ctx context.Context, in *pb.PresidentHandleAllApplySignReq) (*pb.PresidentHandleAllApplySignResp, error) {
	log.InfoWithCtx(ctx, "PresidentHandleAllApplySign GuildId %d HandleOpr %d", in.GuildId, in.HandleOpr)

	out := &pb.PresidentHandleAllApplySignResp{}
	guildId := in.GetGuildId()
	handleOpr := in.GetHandleOpr()
	limit := uint32(50) // 一次最多处理50个
	conflictCnt := uint32(0)
	okCnt := uint32(0)

	// 会长全部同意 只能同意多人互动类型
	applyList, err := m.store.GetGuildContractApplyListWithIdentity(guildId,
		uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER), 0, limit, []uint32{uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_HANDLING)})
	if err != nil {
		log.ErrorWithCtx(ctx, "PresidentHandleAllApplySign fail to GetGuildContractApplyList in:%+v, err:%v", in, err)
		return out, err
	}

	req := &pb.PresidentHandleApplySignReq{
		HandleOpr: handleOpr,
	}

	for _, apply := range applyList {
		req.ApplyId = apply.Id

		err = m.PresidentHandleApplySign(ctx, req)
		if err != nil {
			if serr, ok := err.(protocol.ServerError); ok {
				if serr.Code() == status.ErrContractHandleInvalid {
					conflictCnt++
				}
			}
			log.ErrorWithCtx(ctx, "PresidentHandleAllApplySign fail to GetGuildContractApplyList in:%+v, applyId :%d, err:%v", in, apply.Id, err)
			continue
		}
		okCnt++
	}

	out.ConflictCnt = conflictCnt
	out.OkCnt = okCnt
	out.HandleCnt = uint32(len(applyList))

	log.InfoWithCtx(ctx, "PresidentHandleAllApplySign in %+v limit %d out %+v", in, limit, out)
	return out, nil
}

// 官方审批
func (m *AnchorContractMgr) OfficialHandleApplySign(ctx context.Context, in *pb.OfficialHandleApplySignReq) error {
	log.InfoWithCtx(ctx, "OfficialHandleApplySign ApplyId %d HandleOpr %d Operator %q Remarks %q",
		in.ApplyId, in.HandleOpr, in.Operator, in.Remarks)

	applyInfo, err := m.store.GetContractApplyWithId(nil, in.GetApplyId())
	if err != nil {
		log.ErrorWithCtx(ctx, "OfficialHandleApplySign fail to GetContractApplyWithId in:%+v, err:%v", in, err)
		return err
	}

	switch pb.HANDLE_SIGN_APPLY_OPR(in.GetHandleOpr()) {
	case pb.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_ACCEPT:
		err = m.OfficialAcceptApplySign(ctx, applyInfo, in.GetOperator(), in.GetRemarks())
		if err != nil {
			log.ErrorWithCtx(ctx, "OfficialHandleApplySign fail to OfficialAcceptApplySign in:%+v, err:%v", in, err)
			return err
		}

	case pb.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_REJECT:
		err = m.OfficialRejectApplySign(ctx, applyInfo, in.GetOperator(), in.GetRemarks())
		if err != nil {
			log.ErrorWithCtx(ctx, "OfficialHandleApplySign fail to OfficialRejectApplySign in:%+v, err:%v", in, err)
			return err
		}

	default:
		log.ErrorWithCtx(ctx, "OfficialHandleApplySign fail. ErrBadRequest in:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrBadRequest)
	}

	log.InfoWithCtx(ctx, "OfficialHandleApplySign end in %+v, applyInfo %+v", in, applyInfo)
	return nil
}

func (m *AnchorContractMgr) OfficialAcceptApplySign(ctx context.Context, applyInfo *mysql.ContractApply, operator, remarks string) error {
	log.InfoWithCtx(ctx, "OfficialAcceptApplySign uid %d guildId %d identity_type %d apply_id %d contract_duration %d operator %q remarks %q",
		applyInfo.Uid, applyInfo.GuildId, applyInfo.IdentityType, applyInfo.Id, applyInfo.ContractDuration, operator, remarks)

	now := time.Now()
	uid, guildId := applyInfo.Uid, applyInfo.GuildId
	goalStatus := uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PASS)
	needAddLiveIdentity := false

	// 签约非语音直播身份时需检查直播权限，有直播权限需自动增加语音直播身份
	if applyInfo.ContractDuration > 0 && applyInfo.IdentityType != uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
		needAddLiveIdentity = m.checkIfNeedAddLiveIdentity(ctx, uid, guildId, now)
		log.InfoWithCtx(ctx, "OfficialAcceptApplySign auto add live Identity, needAddLiveIdentity %v, applyInfo %+v", needAddLiveIdentity, applyInfo)
	}

	// 开启事务
	err := m.store.Transaction(ctx, func(tx *gorm.DB) error {
		change, err := m.store.UpdateContractApplyStatusWithId(tx, applyInfo.Id, uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_HANDLING),
			goalStatus, operator, remarks)
		if err != nil {
			log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to UpdateContractApplyStatusWithId in:%+v, err:%v", applyInfo, err)
			return err
		}
		if !change {
			log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to UpdateContractApplyStatusWithId, nothing update. in:%+v", applyInfo)
			return protocol.NewExactServerError(nil, status.ErrContractApplysignNonexist, "签约申请不存在或已处理")
		}

		var signTime time.Time
		var contractExpireTime time.Time

		// 新签约
		if applyInfo.ContractDuration > 0 {
			signTime = now
			contractExpireTime = m.calculateExpireTime(applyInfo.ContractDuration, now)
			err = m.store.AddUserContract(ctx, tx, applyInfo, now, contractExpireTime)
			if err != nil {
				log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to AddUserContract in:%+v, err:%v", applyInfo, err)
				return err
			}
		} else {
			contractInfo, exist, err := m.store.GetValidContractWithUid(uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to GetUserContractInfo in:%+v, err:%v", applyInfo, err)
			}
			if exist {
				signTime = contractInfo.SignTime
				contractExpireTime = contractInfo.ContractExpireTime

				if applyInfo.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) { //新申请身份，需要同步跟新WorkType
					err = m.store.UpdateContractWorkerType(tx, applyInfo.Uid, applyInfo.GuildId, applyInfo.WorkerType)
					if err != nil {
						log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to UpdateContractWorkerType in:%+v, err:%v", applyInfo, err)
						return err
					}
				}
			}
		}

		err = m.store.AddUserAnchorIdentity(ctx, tx, uid, guildId, applyInfo.IdentityType, now, signTime, contractExpireTime, operator)
		if err != nil {
			log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to AddAnchorIdentity in:%+v, err:%v", applyInfo, err)
			return err
		}

		// 需要增加语音直播身份
		if needAddLiveIdentity {
			err = m.store.AddUserAnchorIdentity(ctx, tx, uid, guildId, uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE),
				now, signTime, contractExpireTime, operator)
			if err != nil {
				log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to AddAnchorIdentity in:%+v, err:%v", applyInfo, err)
				return err
			}
		}
		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to begin Transaction in:%+v, err:%v", applyInfo, err)
		return err
	}

	// 删除缓存, 拿到kfk事件前删除
	err = m.cache.DelUserContractInfoV2(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to DelUserContractInfoV2 in:%+v, err:%+v", applyInfo, err)
	}

	// 新签约的
	if applyInfo.ContractDuration > 0 {
		log.DebugWithCtx(ctx, "OfficialAcceptApplySign notify %+v", applyInfo)
		m.SignContractNotify(ctx, applyInfo, now)

	} else {
		// 已签约，申请语音主播身份
		if applyInfo.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
			m.KafkaProduceAnchorContractEvent(ctx, uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_CONTRACT_ADD_LIVE_PERMISSION), uid, guildId, now)
		} else if applyInfo.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
			m.KafkaProduceAnchorContractEvent(ctx, uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_CONTRACT_ADD_MULTIPLAYER), uid, guildId, now)
		}
	}

	if applyInfo.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
		// 多人合作身份主播，加入到多人合作积分列表
		err = m.cache.BatchUpdateGuildUserScore(guildId, map[uint32]int64{uid: 0})
		if err != nil {
			log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to BatchUpdateGuildUserScore in:%+v, err:%+v", applyInfo, err)
		}

		if m.dyConfig.GetFollowPublicSwitch(applyInfo.Uid) {
			go func(uid uint32) {
				_, err := m.apiCenterCli.FollowPublic(context.Background(), &apicenterPB.FollowPublicReq{
					Uid:         uid,
					PublicType:  apicenterPB.FollowPublicReq_SYSTEM, // 系统公众号
					BindedId:    90005,                              // 公众号id
					IsFollow:    true,                               // 关注
					WithWelcome: true,                               // 关注后推送消息
				})
				log.Infof("OfficialAcceptApplySign FollowPublic uid %d, err %v", uid, err)
			}(applyInfo.Uid)
		}

	} else if applyInfo.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {

		fromExtra := &pb.LiveAnchorForm{}
		err := json.Unmarshal([]byte(applyInfo.FormExtra), fromExtra)
		if err != nil {
			log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to Unmarshal. err:%v", err)
		}

		// 加入语音主播考核列表
		err = m.store.AddLiveAnchorExamine(nil, &mysql.LiveAnchorExamine{
			Uid:          applyInfo.Uid,
			GuildId:      applyInfo.GuildId,
			Status:       uint32(pb.LiveAnchorExamine_ENUM_HANDLING),
			IsSetTime:    false,
			Giver:        operator,
			ChannelTagId: fromExtra.TagId,
			CreateTime:   now,
			UpdateTime:   now,
			ExamineTime:  now,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to AddLiveAnchorExamine in:%+v, err:%+v", applyInfo, err)
		}

		err = m.cache.PushToExamineNotify(applyInfo.Uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to cache.PushToExamineNotify in:%+v, err:%+v", applyInfo, err)
		}

		//msg := "恭喜！你已通过主播审核，需在即日起三天内开播，并上传直播音频以完成考核 立即开播> \n\n注：直播音频仅能上传1次，请谨慎点击确认按钮"
		msg := m.anchorCheckDyconfig.GetAnchorCheckImMsg().CheckBegin
		err = m.SendIMMsgWithJumpUrl(ctx, applyInfo.Uid, msg.Content, msg.Highlight, msg.Url)
		log.InfoWithCtx(ctx, "OfficialAcceptApplySign sendIm anchorCheck uid %d err %v", applyInfo.Uid, err)
	}

	go func() { // 异步检查新手主播身份
		subCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()
		_ = m.CheckSignAnchorIdentityNewbieAnchor(subCtx, applyInfo.Uid, applyInfo.IdentityType)
	}()
	log.InfoWithCtx(ctx, "OfficialAcceptApplySign end applyInfo %+v", applyInfo)
	return nil
}

func (m *AnchorContractMgr) calculateExpireTime(contractDuration uint32, now time.Time) time.Time {
	return now.AddDate(0, int(contractDuration), 0)
}

func (m *AnchorContractMgr) checkIfNeedAddLiveIdentity(ctx context.Context, uid, guildId uint32, now time.Time) bool {
	//根据需求 申请其他身份时不需要给予语音直播身份
	return false
	_, exist, err := m.store.GetAnchorIdentityWithType(uid, uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE))
	if err != nil {
		log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to GetAnchorIdentityWithType uid:%+v, err:%v", uid, err)
		return false
	}

	if !exist {
		resp, err := m.liveMgrCli.GetChannelLiveInfo(ctx, uid, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to GetChannelLiveInfo uid:%+v, err:%v", uid, err)
			return false
		}

		if resp.GetChannelLiveInfo().GetEndTime() > uint32(now.Unix()) {
			// 检查公会是否在多人合作库合作库
			bIn, err := m.CheckGuildIfInCoop(ctx, guildId, GOLDDIAMONN_RADIO_LIVE_LIB)
			if err != nil {
				log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to checkGuildIfInCoop uid:%+v, guildId:%v, err:%v", uid, guildId, err)
				return false
			}

			if bIn {
				return true
			}
		}
	}

	return false
}

// 签约完成后通知
func (m *AnchorContractMgr) SignContractNotify(ctx context.Context, applyInfo *mysql.ContractApply, now time.Time) {
	uid, guildId := applyInfo.Uid, applyInfo.GuildId

	// 数据上报
	m.ContractDataCenterReport(ctx, uid, guildId, 1)

	// kafka事件
	m.KafkaProduceAnchorContractEvent(ctx, uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_SIGN_CONTRACT), uid, guildId, now)

	gid := guildId
	guildResp, serr := m.guildCli.GetGuild(ctx, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "SignContractNotify fail to GetGuild in:%+v, err:%+v", applyInfo, serr)

	} else {
		if guildResp.GetShortId() > 0 {
			gid = guildResp.GetShortId()
		}
	}

	highLight := "前往签约管理>"
	msg := fmt.Sprintf("您已成功和公会 【%s】(ID:%d) 签约,签约期限至 %s。签约期间请严格遵守签约协议，禁止在非签约公会接档，一旦违规签约账号将受到封号处理，详情请查看签约须知。点击%s",
		guildResp.GetName(), gid, now.AddDate(0, int(applyInfo.ContractDuration), 0).Format("2006-01-02"), highLight)
	// tt助手消息
	_ = m.SendIMMsgWithJumpUrl(ctx, uid, msg, highLight, m.sc.SignMgrWebUrl)

}

func (m *AnchorContractMgr) OfficialRejectApplySign(ctx context.Context, applyInfo *mysql.ContractApply, operator, remarks string) error {
	log.InfoWithCtx(ctx, "OfficialRejectApplySign uid %d guildId %d identity_type %d apply_id %d contract_duration %d operator %q remarks %q",
		applyInfo.Uid, applyInfo.GuildId, applyInfo.IdentityType, applyInfo.Id, applyInfo.ContractDuration, operator, remarks)

	now := time.Now()
	goalStatus := uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_REJECT)

	// 开启事务
	err := m.store.Transaction(ctx, func(tx *gorm.DB) error {
		change, err := m.store.UpdateContractApplyStatusWithId(tx, applyInfo.Id, uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_HANDLING),
			goalStatus, operator, remarks)
		if err != nil {
			log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to UpdateContractApplyStatusWithId in:%+v, err:%v", applyInfo, err)
			return err
		}

		if !change {
			log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to UpdateContractApplyStatusWithId, nothing update. in:%+v", applyInfo)
			return protocol.NewExactServerError(nil, status.ErrContractApplysignNonexist, "签约申请不存在或已处理")
		}

		err = m.store.RecordContractChangeLog(tx, &mysql.ContractChangeLog{
			Uid:                applyInfo.Uid,
			GuildId:            applyInfo.GuildId,
			IdentityNum:        applyInfo.IdentityNum,
			ContractDuration:   applyInfo.ContractDuration,
			SignTime:           now,
			ContractExpireTime: now.AddDate(0, int(applyInfo.ContractDuration), 0),
			ChangeType:         uint32(pb.CONTRACT_CHANGE_TYPE_ENUM_CONTRACT_CHANGE_REJECT),
			ChangeDesc:         "Official reject sign apply",
			ChangeTime:         now,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to RecordContractChangeLog in:%+v, err:%v", applyInfo, err)
			return err
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to begin Transaction in:%+v, err:%v", applyInfo, err)
		return err
	}

	// add apply blacklist
	blackDuration := m.GetApplyBlacklistDuration(applyInfo.IdentityType)
	blackInfo := &mysql.ApplyBlacklist{
		Uid:          applyInfo.Uid,
		IdentityType: applyInfo.IdentityType,
		BeginTime:    now,
		EndTime:      now.AddDate(0, 0, int(blackDuration)),
		Handler:      operator,
		Remarks:      remarks,
	}
	err = m.store.AddUserToApplyBlacklist(nil, blackInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "OfficialAcceptApplySign fail to AddUserToApplyBlacklist in:%+v, err:%v", applyInfo, err)
	}

	return nil
}

func (m *AnchorContractMgr) getExtraInfo(ctx context.Context, anchorUid uint32, identity, guildId uint32, recharge uint32) (*pb.MultiAnchorExtra, error) {
	// 获取近三十天充值金额

	//ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	//defer cancel()
	multiExtra := &pb.MultiAnchorExtra{}

	multiExtra.Recharge = recharge

	devInfo, err := m.userAuthHistoryCli.GetUserLoginDevice(ctx, uint64(anchorUid))
	if err != nil {
		log.ErrorWithCtx(ctx, "GenApplyExtraInfoString fail to GetUserLoginDevice %v, uid %d", err, anchorUid)
		return multiExtra, err
	}
	log.InfoWithCtx(ctx, "getExtraInfo anchorUid:%d devInfo:%+v", anchorUid, devInfo)

	if len(devInfo) > 0 && len(devInfo[len(devInfo)-1].GetDeviceId()) == 32 {
		devId := devInfo[len(devInfo)-1].GetDeviceId()

		loginResp, err := m.userAuthHistoryCli.GetUserLoginWithDevice(ctx, devId, 0, 0) // 不传返回近7天的记录
		if err != nil {
			log.ErrorWithCtx(ctx, "GenApplyExtraInfoString fail to GetUserLoginWithDevice %v ,uid %d", err, anchorUid)
			return multiExtra, err
		}
		log.InfoWithCtx(ctx, "getExtraInfo GetUserLoginWithDevice anchorUid:%d devId=%q loginResp:%+v", anchorUid, devId, loginResp)

		uids := make([]uint32, 0)
		now := time.Now()
		loginBegin := now.AddDate(0, 0, -7)
		for i, user := range loginResp.GetLoginUsers() {
			if i >= 50 {
				break
			}
			if uint32(user.GetUid()) == anchorUid {
				continue
			}

			if uint32(user.LoginAt) >= uint32(loginBegin.Unix()) && uint32(user.LoginAt) <= uint32(now.Unix()) {
				uids = append(uids, uint32(user.GetUid()))
				log.DebugfWithCtx(ctx, "getExtraInfo got LoginOtherUid=%+v uid=%d", user, anchorUid)
			}
		}
		log.InfoWithCtx(ctx, "getExtraInfo LoginOtherUids=%v uid=%d", uids, anchorUid)

		contractList, err2 := m.store.GetContractWithUidList(uids)
		if err2 != nil {
			log.ErrorWithCtx(ctx, "GenApplyExtraInfoString fail to GetUserLoginWithDevice. uids:%+v, :%v", uids, err2)
			return multiExtra, err2
		}

		multiExtra.LoginOtherUids = make([]uint32, 0)
		for _, contract := range contractList {
			//  新增疑似小号限制逻辑
			// 签约的公会与该申请账号申请的签约公会，非同一家（子母公会统计为一家）
			if !m.dyConfig.IsParentChildGuild(guildId, contract.GuildId) {
				multiExtra.LoginOtherUids = append(multiExtra.LoginOtherUids, contract.Uid)
				log.InfoWithCtx(ctx, "getExtraInfo identity is not same guild, append. uid %d guild %d identity %d, other: uid %d, guild %d",
					anchorUid, guildId, identity, contract.Uid, contract.GuildId)
			} else {
				log.InfoWithCtx(ctx, "getExtraInfo identity is same guild, igonre. uid %d guild %d identity %d, other: uid %d, guild %d",
					anchorUid, guildId, identity, contract.Uid, contract.GuildId)
			}
		}
	}

	//for test
	//if m.sc.TestMod && 0 == len(multiExtra.LoginOtherUids) {
	//	multiExtra.LoginOtherUids = append(multiExtra.LoginOtherUids, 1024)
	//}

	log.InfoWithCtx(ctx, "getExtraInfo anchorUid:%v multiExtra:%+v", anchorUid, multiExtra)
	return multiExtra, nil
}

func (m *AnchorContractMgr) GenApplyExtraInfoString(ctx context.Context, in *pb.ApplySignContractReq, isGreatAnchor bool, recharge uint32) (extra string) {

	multiExtra, err := m.getExtraInfo(ctx, in.GetActorUid(), in.GetAnchorIdentity(), in.GetGuildId(), recharge)
	if nil != err {
		return
	}

	if in.GetAnchorIdentity() == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
		b, err2 := json.Marshal(multiExtra)
		if err2 != nil {
			log.ErrorWithCtx(ctx, "GenApplyExtraInfoString fail to Marshal. in:%+v, :%v", in, err2)
		}
		extra = string(b)
	} else if in.GetAnchorIdentity() == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
		liveExtra := &pb.LiveAnchorExtra{
			GreatAnchor:    isGreatAnchor, //m.CheckIfGreatLiveAnchor(ctx, in.GetActorUid()),
			Recharge:       multiExtra.Recharge,
			LoginOtherUids: multiExtra.LoginOtherUids,
		}
		b, err := json.Marshal(liveExtra)
		if err != nil {
			log.ErrorWithCtx(ctx, "GenApplyExtraInfoString fail to Marshal. in:%+v, :%v", in, err)
		}
		extra = string(b)
	}

	return extra
}

func (m *AnchorContractMgr) CheckIfGreatLiveAnchor(ctx context.Context, uid uint32) bool {
	liveInfoResp, err := m.liveMgrCli.GetChannelLiveInfo(ctx, uid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckIfGreatLiveAnchor fail to GetChannelLiveInfo uid:%+v, err:%v", uid, err)
		return false
	}

	if liveInfoResp.GetChannelLiveInfo().GetUid() != uid ||
		liveInfoResp.GetChannelLiveInfo().GetEndTime() <= uint32(time.Now().Unix()) {
		return false
	}

	monthTimeList := make([]uint32, 0)
	monthStrList := make([]string, 0)
	now := time.Now()
	for i := 0; i < 12; i++ {
		monthTm := now.AddDate(0, -i, 0)
		monthTimeList = append(monthTimeList, uint32(monthTm.Unix()))
		monthStrList = append(monthStrList, monthTm.Format("2006-01"))
	}

	req := &channellivestats.GetAnchorMonthlyTotalStatsReq{
		AnchorUid:     uid,
		MonthTimeList: monthTimeList,
	}

	resp, err := m.liveStatsCli.GetAnchorMonthlyTotalStats(ctx, uid, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckIfGreatLiveAnchor fail to GetAnchorMonthlyTotalStats uid:%+v, err:%v", uid, err)
		return false
	}

	mapTimeStr2Income := make(map[string]uint32, 0)
	for _, stats := range resp.GetStats() {
		log.DebugWithCtx(ctx, "CheckIfGreatLiveAnchor uid %d stats %v", uid, stats)
		mapTimeStr2Income[time.Unix(int64(stats.GetMonthTime()), 0).Format("2006-01")] = stats.GetAnchorIncome()
	}

	// 大主播定义
	// 近3个月（包含当月）中，有某一个月在自己的直播间的主播收礼值≥20000元的主播
	// 近6个月（包含当月）中， 月均主播收礼值≥20000元的主播
	var totalIncome uint64 = 0
	for index, monthStr := range monthStrList {
		log.DebugWithCtx(ctx, "CheckIfGreatLiveAnchor uid %d index %d stats %v", uid, index, monthStr)

		if index > 5 {
			break
		}

		if income, ok := mapTimeStr2Income[monthStr]; ok {
			// 近3个月（包含当月）中，有某一个月在自己的直播间的主播收礼值≥20000元的主播
			if index < 3 && income >= 20000*100 {
				return true
			}

			totalIncome += uint64(income)
		}
	}

	//近6个月（包含当月）中， 月均主播收礼值≥20000元的主播
	return (totalIncome / 6) >= (20000 * 100)
}

func (m *AnchorContractMgr) ApplySignPreCheck(ctx context.Context, in *pb.ApplySignContractReq) error {
	var err error
	now := time.Now()

	// check apply blacklist
	blackInfo, err := m.store.GetUserApplyBlacklist(in.GetActorUid(), in.GetAnchorIdentity())
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignPreCheck fail to GetUserApplyBlacklist in:%+v, err:%v", in, err)
		return err
	}

	if blackInfo.BeginTime.Before(now) && blackInfo.EndTime.After(now) {
		log.ErrorWithCtx(ctx, "ApplySignPreCheck fail. User is in apply blacklist in:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrContractApplyBlacklistLimit)
	}

	// 检查公会合作库
	bIn, err := m.CheckGuildIfInCoop(ctx, in.GetGuildId(), in.GetAnchorIdentity())
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignPreCheck fail to checkGuildIfInCoop in:%+v, err:%v", in, err)
		return err
	}

	if !bIn {
		log.ErrorWithCtx(ctx, "ApplySignPreCheckContract fail. guild not in this libraryType in:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrContractApplyGuildNotCoopType)
	}

	// 付费解约过，限制检查
	if err = m.CheckPayCancelSignLimit(ctx, in.GetActorUid(), in.GuildId, in.IdentityNum); err != nil {
		log.ErrorWithCtx(ctx, "ApplySignPreCheck fail to CheckPayCancelSignLimit in:%+v, err:%v", in, err)
		return err
	}

	// 检查申请记录
	err = m.ApplySignPreCheckApplyRecord(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignPreCheck fail to ApplySignPreCheckApplyRecord in:%+v, err:%v", in, err)
		return err
	}

	// 检查签约信息
	err = m.ApplySignPreCheckContract(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignPreCheck fail to ApplySignPreCheckContract in:%+v, err:%v", in, err)
		return err
	}

	return m.ApplySignPreCheckChangeLog(ctx, in)
}

func (m *AnchorContractMgr) ApplySignPreCheckContract(ctx context.Context, in *pb.ApplySignContractReq) error {
	uid := in.GetActorUid()

	contract, contractExist, err := m.store.GetValidContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignPreCheckContract fail to GetValidContractWithUid in:%+v, err:%v", in, err)
		return err
	}

	// ContractDuration大于0为签约， 等于0为申请新身份
	if in.GetContractDuration() > 0 {
		// 已有合约在身
		if contractExist {
			log.ErrorWithCtx(ctx, "ApplySignPreCheckContract fail. Uid already sign contract in:%+v", in)
			return protocol.NewExactServerError(nil, status.ErrContractApplyHaveContract)
		}

		// 检查该身份证下是否有合约
		_, exist, err := m.store.GetContractWithIdentityNum(in.GetIdentityNum())
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplySignPreCheckContract fail to GetContractWithIdentityNum in:%+v, err:%v", in, err)
			return err
		}

		if !m.sc.TestMod && exist {
			log.ErrorWithCtx(ctx, "ApplySignPreCheckContract fail. Identity_num already sign contract in:%+v", in)
			return protocol.NewExactServerError(nil, status.ErrContractApplyIdentityLimit)
		}

	} else if contractExist {
		// 申请新身份时，与现签约信息不符
		if contract.GuildId != in.GuildId {
			log.ErrorWithCtx(ctx, "ApplySignPreCheckContract fail. Uid already sign another guild contract in:%+v", in)
			return protocol.NewExactServerError(nil, status.ErrContractHandleConflict)

		} else {
			// 获取用户身份
			anchorIdentityList, err := m.store.GetAnchorIdentity(nil, uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "ApplySignPreCheckContract fail to GetAnchorIdentity in:%+v, err:%v", in, err)
				return err
			}

			for _, info := range anchorIdentityList {
				// 已拥有该身份
				if info.IdentityType == in.GetAnchorIdentity() {
					log.ErrorWithCtx(ctx, "ApplySignPreCheckContract fail. Uid already have this AnchorIdentity in:%+v", in)
					return protocol.NewExactServerError(nil, status.ErrContractApplyHaveIdentityType)
				}
			}
		}

	} else {
		// 申请新身份却没有签约
		log.ErrorWithCtx(ctx, "ApplySignPreCheckContract fail. Uid get this AnchorIdentity not sign in:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrBadRequest)
	}

	return nil
}

func (m *AnchorContractMgr) ApplySignPreCheckChangeLog(ctx context.Context, in *pb.ApplySignContractReq) error {
	uid := in.GetActorUid()
	now := time.Now()
	begin := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	end := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, time.Local)

	changeTypeList := []uint32{
		uint32(pb.CONTRACT_CHANGE_TYPE_ENUM_CONTRACT_CHANGE_DEL),
		uint32(pb.CONTRACT_CHANGE_TYPE_ENUM_CONTRACT_CHANGE_TIMEOUT),
	}

	// 检查 uid 今天是否有解约的合约
	list, err := m.store.GetContractChangeLogWithTypes(uid, changeTypeList, begin, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignPreCheckChangeLog fail to GetContractChangeLogWithTypes in:%+v, err:%v", in, err)
		return err
	}

	if !m.sc.TestMod && len(list) > 0 {
		log.ErrorWithCtx(ctx, "ApplySignPreCheckChangeLog fail. Today have cancel (ENUM_CONTRACT_CHANGE_DEL) in:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrContractApplyTodayLimit)
	}

	// 查这个身份证号今年的签约记录
	yearBegin := time.Date(now.Year(), 1, 1, 0, 0, 0, 0, time.Local)
	if m.sc.TestMod {
		yearBegin = time.Now().Add(-15 * time.Minute)
	}

	list, err = m.store.GetContractChangeLogWithIdNum(in.GetIdentityNum(), uint32(pb.CONTRACT_CHANGE_TYPE_ENUM_CONTRACT_CHANGE_ACCEPT), yearBegin, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignPreCheckChangeLog fail to GetContractChangeLogWithIdNum in:%+v, err:%v", in, err)
		return err
	}

	noReasonCount := 0
	uidList := make([]uint32, 0)
	for _, item := range list {
		uidList = append(uidList, item.Uid)
	}

	cancelLog, err := m.store.BatchGetCancelContractApplyLogList(uidList, yearBegin)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignPreCheckChangeLog fail to GetCancelContractApplyLogList in:%+v, err:%v", in, err)
		// 这里不用退出
	}
	cancelMap := make(map[time.Time]bool)
	for _, item := range cancelLog {
		if item.CancelType == uint32(pb.CancelContractType_CancelContractType_NoReason) && item.Status == uint32(pb.CancelContractStatus_CancelContractStatus_Finish) {
			cancelMap[item.SignTime] = true
		}
	}

	log.DebugWithCtx(ctx, "ApplySignPreCheckChangeLog cancel list:%+v", cancelMap)

	for _, item := range list {
		if cancelMap[item.SignTime] == true {
			noReasonCount++
		}
	}

	if noReasonCount > len(changeTypeList) {
		noReasonCount = len(changeTypeList)
	}

	yearLimit := m.sc.YearSignLimitCnt
	limit, err := m.store.GetAnchorContractLimitByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplySign fail to GetAnchorContractLimitByUid in:%+v, err:%v", in, err)
		//return out, err
	}
	if limit != nil && limit.CreateTime.Year() == now.Year() {
		yearLimit = limit.LimitCount
	}

	log.DebugWithCtx(ctx, "ApplySignPreCheckChangeLog yearLimit:%d, noReasonCount:%d", yearLimit, noReasonCount)
	if !m.sc.TestMod && in.GetContractDuration() > 0 && uint32(len(list)-noReasonCount) >= yearLimit {
		log.ErrorWithCtx(ctx, "ApplySignPreCheckChangeLog fail. This year sign count is over limit in:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrContractYearSignLimit)
	}

	return nil
}

func (m *AnchorContractMgr) ApplySignPreCheckApplyRecord(ctx context.Context, in *pb.ApplySignContractReq) error {
	uid := in.GetActorUid()

	statusList := []uint32{
		uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_HANDLING),
		uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_HANDLING),
	}

	// 检查该身份证下是否有正在审批中的申请记录
	applyList, err := m.store.GetContractApplyWithIdNum(in.GetIdentityNum(), statusList)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignPreCheck fail to GetContractApplyWithIdNum in:%+v, err:%v", in, err)
		return err
	}

	for _, apply := range applyList {
		if uid != apply.Uid {
			log.ErrorWithCtx(ctx, "ApplySignPreCheck fail. Identity_num have apply in:%+v", in)
			if m.sc.TestMod {
				log.DebugWithCtx(ctx, "ApplySignPreCheckApplyRecord skip IdentityNum check in %+v", in)
				//return nil
			}
			return protocol.NewExactServerError(nil, status.ErrContractApplyIdentityApplyLimit)
		}
	}

	// 检查该用户官方正在审批中的申请记录
	OfficeHandleApplyList, err := m.store.GetContractApplyWithUid(uid, []uint32{uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_HANDLING)})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignPreCheck fail to GetContractApplyWithUid in:%+v, err:%v", in, err)
		return err
	}

	if len(OfficeHandleApplyList) > 0 {
		log.ErrorWithCtx(ctx, "ApplySignPreCheck fail. Apply limit in:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrContractApplyHasHandling)
	}

	// 检查该用户正在审批中的申请记录
	applyList2, err := m.store.GetContractApplyWithUid(uid, statusList)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplySignPreCheck fail to GetContractApplyWithUid in:%+v, err:%v", in, err)
		return err
	}

	if uint32(len(applyList)) >= m.sc.ApplySignLimitCnt {
		log.ErrorWithCtx(ctx, "ApplySignPreCheck fail. Apply limit in:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrContractApplyLimit)
	}

	for _, apply := range applyList2 {
		if uid == apply.Uid && in.GetGuildId() == apply.GuildId {
			log.ErrorWithCtx(ctx, "ApplySignPreCheck fail. Already apply in:%+v", in)
			return protocol.NewExactServerError(nil, status.ErrContractApplyAlready)
		}

		// 正在申请别的身份
		if in.GetAnchorIdentity() != apply.IdentityType {
			log.ErrorWithCtx(ctx, "ApplySignPreCheck fail. Already apply another IdentityType in:%+v, apply=%+v", in, apply)
			return protocol.NewExactServerError(nil, status.ErrContractApplyIdentityTypeLimit)
		}
	}

	return nil
}

func (m *AnchorContractMgr) GetApplyBlacklistDuration(identityType uint32) uint32 {
	duration := uint32(0)

	switch pb.SIGN_ANCHOR_IDENTITY(identityType) {
	case pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER:
		duration = m.sc.MultiplayerBlackDuration
	case pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE:
		duration = m.sc.RadioLiveBlackDuration
	}

	return duration
}
func (m *AnchorContractMgr) GetAgeWithIdentificationNumber(identificationNumber string) int {
	age := 0
	currYear := int(time.Now().Year())
	currMonth := int(time.Now().Month())
	currDay := int(time.Now().Day())
	if len(identificationNumber) == 15 {
		birYear, _ := strconv.Atoi("19" + identificationNumber[6:8])
		birMonth, _ := strconv.Atoi(identificationNumber[8:10])
		birDay, _ := strconv.Atoi(identificationNumber[10:12])
		age = currYear - birYear
		if currMonth < birMonth {
			age--
		} else if currMonth == birMonth {
			if currDay < birDay {
				age--
			}
		}
	} else if len(identificationNumber) == 18 {
		birYear, _ := strconv.Atoi(identificationNumber[6:10])
		birMonth, _ := strconv.Atoi(identificationNumber[10:12])
		birDay, _ := strconv.Atoi(identificationNumber[12:14])
		age = currYear - birYear
		if currMonth < birMonth {
			age--
		} else if currMonth == birMonth {
			if currDay < birDay {
				age--
			}
		}
	}
	return age
}

// 获取最近30天充值金额
func (m *AnchorContractMgr) GetTotalRecharge(ctx context.Context, uid uint32) (uint32, error) {
	now := time.Now()
	recharge, err := m.tBeanCli.GetTotalRecharge(ctx, uid, now.AddDate(0, 0, -30), now)
	if err != nil {
		log.ErrorWithCtx(ctx, "fail to GetTotalRecharge %v uid=%d", err, uid)
	}
	return recharge, err
}

// 之前写的啥玩意 还搞逻辑分离 循环依赖调用apicentergo
// 官方自动审批通过
func (m *AnchorContractMgr) OfficialHandleApplySignV2(ctx context.Context,
	uid uint32,
	tagId uint32,
	applyId uint32,
	guildId uint32,
	anchorType pb.SIGN_ANCHOR_IDENTITY,
	handleOpr pb.HANDLE_SIGN_APPLY_OPR,
	operator string, remark string) (err error) {

	defer func() {
		log.InfoWithCtx(ctx, "OfficialHandleApplySignV2 uid=%d tagId=%d applyId=%d guildId=%d anchorType=%d handleOpr=%d operator=%s err=%+v",
			uid, tagId, applyId, guildId, anchorType, handleOpr, operator, err)
	}()

	// 同意语音直播身份申请时，需要先开通语音直播权限
	if anchorType == pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE &&
		handleOpr == pb.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_ACCEPT &&
		tagId > 0 {

		_, err := m.liveMgrCli.SetChannelLiveInfo(ctx, &livePb.SetChannelLiveInfoReq{
			ChannelLiveInfo: &livePb.ChannelLiveInfo{
				Uid:      uid,
				TagId:    tagId,
				OperName: operator,
			},
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "OfficialHandleApplySignV2 SetChannelLiveInfo failed uid:%d err:%v", uid, err)
			return err
		}
		log.InfoWithCtx(ctx, "OfficialHandleApplySignV2 SetChannelLiveInfo done uid=%d tagId=%d", uid, tagId)
	}

	err = m.OfficialHandleApplySign(ctx, &pb.OfficialHandleApplySignReq{
		ApplyId:   applyId,
		HandleOpr: uint32(handleOpr),
		Operator:  operator,
		Remarks:   remark,
	})
	if err != nil {
		return err
	}

	if handleOpr == pb.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_ACCEPT {
		if anchorType == pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER {
			pushMsg := "您的多人互动成员身份申请已经通过，请按照平台要求认真经营。"
			m.SendOfficialTTMsg(uid, pushMsg)
		}
		if anchorType == pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE {
			pushMsg := "您的语音主播身份申请已经通过，请按照平台要求认真经营。"
			m.SendOfficialTTMsg(uid, pushMsg)
		}
	}
	return
}

func (m *AnchorContractMgr) SendOfficialTTMsg(uid uint32, content string) error {
	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	sendResp, err := m.imApiCli.SimpleSendTTAssistantText(ctx, uid, content, "", "")
	if err != nil {
		log.ErrorWithCtx(ctx, "SendOfficialTTMsg im-api.SimpleSendTTAssistantText failed, uid:%d, err:%v", uid, err)
		return err
	}
	log.InfoWithCtx(ctx, "SendOfficialTTMsg done. uid:%d, content:%s, resp:%+v", uid, content, sendResp)
	return nil
}

func (m *AnchorContractMgr) ActorHandleExtensionContract(ctx context.Context, in *pb.ActorHandleExtensionContractReq) (*pb.ActorHandleExtensionContractResp, error) {
	log.InfoWithCtx(ctx, "ActorHandleExtensionContract begin=%+v", in)
	out := &pb.ActorHandleExtensionContractResp{}
	uid := in.ActorUid
	guildId := in.GuildId
	if uid == 0 || guildId == 0 {
		return out, nil
	}

	if in.HandleFlag == uint32(pb.EXTENSION_CONTRACT_HANDLE_OPR_EXTENSION_CONTRACT_HANDLE_OPR_REJECT) {
		// del
		exist, err := m.store.DelExtensionContract(nil, uid, guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "ActorHandleExtensionContract DelExtensionContract fail %v, in=%+v", err, in)
			return out, err
		}
		log.InfoWithCtx(ctx, "ActorHandleExtensionContract REJECT uid=%d, guildId=%d exist=%v", uid, guildId, exist)
	}

	if in.HandleFlag == uint32(pb.EXTENSION_CONTRACT_HANDLE_OPR_EXTENSION_CONTRACT_HANDLE_OPR_ACCEPT) {
		guildResp, serr := m.guildCli.GetGuild(ctx, guildId)
		if serr != nil {
			log.ErrorWithCtx(ctx, "ActorHandleExtensionContract GetGuild fail %v, in=%+v", serr, in)
			return out, serr
		}

		contract, exist, err := m.store.GetValidContractWithUid(uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ActorHandleExtensionContract GetValidContractWithUid fail %v, in=%+v", err, in)
			return out, err
		}
		if !exist {
			log.ErrorWithCtx(ctx, "ActorHandleExtensionContract GetValidContractWithUid fail contract no exist, in=%+v", in)
			return out, protocol.NewExactServerError(nil, status.ErrContractNonexist)
		}

		contractExpireTime := m.calculateExpireTime(in.ContractDuration, contract.ContractExpireTime)
		newContract := &mysql.ContractInfo{}
		err = m.store.Transaction(ctx, func(tx *gorm.DB) error {

			exist, err := m.store.DelExtensionContract(tx, uid, guildId)
			if err != nil {
				log.ErrorWithCtx(ctx, "ActorHandleExtensionContract DelExtensionContract fail %v, in=%+v", err, in)
				return err
			}
			if !exist {
				log.ErrorWithCtx(ctx, "ActorHandleExtensionContract ErrContractExtensionNonexist, in=%+v", in)
				return protocol.NewExactServerError(nil, status.ErrContractExtensionNonexist)
			}

			ok, err := m.store.ContractExtension(tx, uid, guildId, in.ContractDuration, contract.SignTime, contractExpireTime)
			if err != nil {
				log.ErrorWithCtx(ctx, "ActorHandleExtensionContract ContractExtension fail %v, in=%+v", err, in)
				return err
			}
			if !ok {
				log.ErrorWithCtx(ctx, "ActorHandleExtensionContract ErrContractNonexist, in=%+v", in)
				return protocol.NewExactServerError(nil, status.ErrContractNonexist)
			}

			newContract, err = m.store.GetContractByUid(tx, uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "ActorHandleExtensionContract GetContractByUid fail %v, in=%+v", err, in)
				return err
			}

			err = m.store.RecordContractChangeLog(tx, &mysql.ContractChangeLog{
				Uid:                newContract.Uid,
				GuildId:            newContract.GuildId,
				IdentityNum:        newContract.IdentityNum,
				ContractDuration:   newContract.ContractDuration,
				SignTime:           newContract.SignTime,
				ContractExpireTime: newContract.ContractExpireTime,
				ChangeType:         uint32(pb.CONTRACT_CHANGE_TYPE_ENUM_CONTRACT_CHANGE_EXTENSION), // 2
				ChangeDesc:         "Extension contract new",
				ChangeTime:         time.Now(),
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "ActorHandleExtensionContract fail to RecordContractChangeLog in:%+v, err:%v", in, err)
				return err
			}

			return nil
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "ActorHandleExtensionContract Transaction fail %v, in=%+v", err, in)
			return out, err
		}

		// 删除缓存
		err = m.cache.DelUserContractInfoV2(uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ActorHandleExtensionContract fail to DelUserContractInfoV2 in:%+v, err:%+v", in, err)
		}

		// kafka事件
		m.KafkaProduceAnchorContractEvent(ctx, uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_EXTENSION_CONTRACT), uid, guildId, time.Now())

		gid := guildId
		if guildResp.GetShortId() > 0 {
			gid = guildResp.GetShortId()
		}
		highLight := "前往签约管理>"
		msg := fmt.Sprintf("您已成功和公会 【%s】(ID:%d) 续约,签约期限延长至 %s。签约期间请严格遵守签约协议，禁止在非签约公会接档，一旦违规签约账号将受到封号处理，详情请查看签约须知。点击%s",
			guildResp.GetName(), gid, newContract.ContractExpireTime.Format("2006-01-02"), highLight)
		// tt助手消息
		_ = m.SendIMMsgWithJumpUrl(ctx, uid, msg, highLight, m.sc.SignMgrWebUrl)

		out.EndTime = uint32(newContract.ContractExpireTime.Unix())
		log.InfoWithCtx(ctx, "ActorHandleExtensionContract newContract=%+v", newContract)
	}

	return out, nil
}

func (m *AnchorContractMgr) GetRecommendTopGuildList(ctx context.Context, in *pb.GetRecommendTopGuildListReq) (*pb.GetRecommendTopGuildListResp, error) {
	// 获取推荐公会
	out := &pb.GetRecommendTopGuildListResp{}
	list, err := m.store.GetRecommendTopGuildList(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendTopGuildList fail to GetRecommendTopGuildList %v", err)
		return out, err
	}
	for _, item := range list {
		abilityTagList := strings.Split(item.AbilityTag, ",")
		out.GuildList = append(out.GuildList, &pb.TopGuildInfo{
			GuildId:      item.GuildId,
			Rank:         item.RecommendRank,
			RecommendTag: item.RecommendTag,
			//AbilityTag:   item.AbilityTag,
			AbilityTagList: abilityTagList,
			HonorTitle:     item.HonorTitle,
			FromTime:       item.FromTime.Unix(),
			ToTime:         item.ToTime.Unix(),
		})
	}

	log.DebugWithCtx(ctx, "GetRecommendTopGuildList in=%+v out:%+v", in, out)
	return out, nil
}

// PromoteContract 晋升管理 payAmount(单位分)
func (m *AnchorContractMgr) PromoteContract(ctx context.Context, uid, guildId uint32, contractDuration uint32, payAmount int64) error {
	contract, exist, err := m.store.GetValidContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "PromoteContract GetValidContractWithUid fail uid:%d, guild_id:%d, err:%v", uid, guildId, err)
		return err
	}
	if !exist || contract.GuildId != guildId {
		log.ErrorWithCtx(ctx, "PromoteContract GetValidContractWithUid fail uid:%d, guild_id:%d, not contract ", uid, guildId)
		return protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}

	now := time.Now()
	contractExpireTime := m.calculateExpireTime(contractDuration, now)
	err = m.store.Transaction(ctx, func(tx *gorm.DB) error {
		ok, err := m.store.PromoteManagerExtensionContract(tx, uid, guildId, contractDuration, now, contractExpireTime, payAmount)
		if err != nil {
			log.ErrorWithCtx(ctx, "PromoteContract ContractExtension fail uid:%d, guild_id:%d, err:%v", uid, guildId, err)
			return err
		}
		if !ok {
			log.ErrorWithCtx(ctx, "PromoteContract ContractExtension fail uid:%d, guild_id:%d, not contract ", uid, guildId)
			return protocol.NewExactServerError(nil, status.ErrContractNonexist)
		}

		//更新身份获取时间
		err = m.store.UpdateAnchorIdentityObtainTime(ctx, tx, uid, guildId, now)
		if err != nil {
			log.ErrorWithCtx(ctx, "PromoteContract UpdateAnchorIdentityObtainTime fail uid:%d, guild_id:%d, err:%v", uid, guildId, err)
			return err
		}
		newContract, err := m.store.GetContractByUid(tx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "PromoteContract GetContractByUid fail uid:%d, guild_id:%d, err:%v", uid, guildId, err)
			return err
		}

		err = m.store.RecordContractChangeLog(tx, &mysql.ContractChangeLog{
			Uid:                newContract.Uid,
			GuildId:            newContract.GuildId,
			IdentityNum:        newContract.IdentityNum,
			ContractDuration:   newContract.ContractDuration,
			SignTime:           newContract.SignTime,
			ContractExpireTime: newContract.ContractExpireTime,
			ChangeType:         uint32(pb.CONTRACT_CHANGE_TYPE_ENUM_CONTRACT_CHANGE_PROMOTE),
			ChangeDesc:         "Promote contract new",
			ChangeTime:         time.Now(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "PromoteContract fail to RecordContractChangeLog in:%+v, err:%v", err)
			return err
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PromoteContract Transaction fail uid:%d, guild_id:%d, err:%v", uid, guildId, err)
		return err
	}
	err = m.cache.DelUserContractInfoV2(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "PromoteContract fail to DelUserContractInfoV2 uid:%d, guild_id:%d, err:%+v", uid, guildId, err)
	}
	log.InfoWithCtx(ctx, "PromoteContract ok uid:%d, guild_id:%d", uid, guildId)
	return nil
}

// IsNewbieAnchor 签约时判断是否新手主播( 新手主播：身份证未签约过）
func (m *AnchorContractMgr) IsNewbieAnchor(ctx context.Context, uid uint32, identityNum string, identityType uint32) (bool, error) {
	// 获取该身份证下的所有签约记录
	contractApplies, err := m.store.GetContractApplyWithIdNum(identityNum, []uint32{uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PASS)})
	if err != nil {
		log.ErrorWithCtx(ctx, "IsNewbieAnchor fail to GetContractApplyWithIdNum uid:%d, identityNum:%s, err:%v", uid, identityNum, err)
		return false, err
	}
	for _, apply := range contractApplies {
		if apply.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) {
			continue
		}
		log.InfoWithCtx(ctx, "IsNewbieAnchor uid:%d, identityNum:%s, identityType:%d is not newbie anchor", uid, identityNum, identityType)
		return false, nil
	}
	return true, nil
}
