package manager

import (
	"context"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
)

func (m *AnchorContractMgr) AddContractLimitWhiteList(ctx context.Context, req *pb.AddContractLimitWhiteListReq) (*pb.AddContractLimitWhiteListResp, error) {
	out := &pb.AddContractLimitWhiteListResp{}
	err := m.store.AddContractLimitWhiteList(nil, req.GetUid(), req.GetOperator(), req.GetCount())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddContractLimitWhiteList failed to AddContractLimitWhiteList. req:%+v, err:%v", req, err)
		return out, err
	}
	log.InfoWithCtx(ctx, "AddContractLimitWhiteList success. req:%+v", req)
	return out, nil
}

func (m *AnchorContractMgr) GetAnchorContractLimit(ctx context.Context, req *pb.GetAnchorContractLimitReq) (*pb.GetAnchorContractLimitResp, error) {
	out := &pb.GetAnchorContractLimitResp{}
	total, list, err := m.store.GetAnchorContractLimit(ctx, req.GetUidList(), req.GetOffset(), req.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorContractLimit failed to GetAnchorContractLimit. req:%+v, err:%v", req, err)
		return out, err
	}

	out.TotalCnt = total
	out.NextOffset = req.GetOffset() + uint32(len(list))

	uidList := make([]uint32, 0, len(list))
	for _, item := range list {
		out.LimitList = append(out.LimitList, &pb.AnchorContractLimitInfo{
			Uid:        item.Uid,
			Operator:   item.Operator,
			UpdateTs:   uint32(item.UpdateTime.Unix()),
			LimitCount: item.LimitCount,
			EffectYear: uint32(item.UpdateTime.Year()),
		})
		uidList = append(uidList, item.Uid)
	}

	userResp, err := m.accountCli.GetUsersMap(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorContractLimit failed to GetUsersMap. req:%+v, err:%v", req, err)
		return out, err
	}

	for _, item := range out.LimitList {
		if user, ok := userResp[item.Uid]; ok {
			item.Ttid = user.GetAlias()
		}
	}

	log.InfoWithCtx(ctx, "GetAnchorContractLimit success. req:%+v", req)
	return out, nil
}
