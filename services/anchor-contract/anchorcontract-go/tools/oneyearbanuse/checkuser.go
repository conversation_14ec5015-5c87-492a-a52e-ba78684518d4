package main

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/ban_server"
	"golang.52tt.com/clients/account"
	anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
	guild_go "golang.52tt.com/clients/guild-go"
	sms_go "golang.52tt.com/clients/sms-go"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
	smsgoPb "golang.52tt.com/protocol/services/sms-go"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"os"
	"strconv"
	"time"
)

var ReasonList = []string{
	"其他规范要求", // 这里替换为实际的封禁原因
}

func GetAllContractUids(mysqlDb *gorm.DB) ([]uint32, error) {
	allUids := make([]uint32, 0)
	limit := 2000
	offset := 0
	log.Infof("Start to get all contract uids with ")
	uidsMap := make(map[uint32]struct{})
	for {
		var uids []uint32
		err := mysqlDb.Table("tbl_contract").Select("uid").Offset(offset).Limit(limit).Scan(&uids).Error
		if err != nil {
			log.Errorf("Failed to get all contract uids: %v", err)
			return nil, err
		}
		for _, uid := range uids {
			if _, exists := uidsMap[uid]; !exists {
				uidsMap[uid] = struct{}{}
				allUids = append(allUids, uid)
			}
		}

		if len(uids) < limit {
			break
		}
		offset += limit
		time.Sleep(50 * time.Millisecond) // 避免对数据库造成过大压力
	}
	log.Infof("Get %d uids from tbl_contract", len(allUids))
	return allUids, nil
}

func GetCurrentBanUser(uidList []uint32) ([]uint32, error) {
	ctx := context.Background()
	banServerCli, err := ban_server.NewClient(ctx)
	if err != nil {
		log.Errorf("New ban_server client fail %v", err)
		return nil, err
	}
	banUidList := make([]uint32, 0)
	limit := 50
	now := time.Now()
	lastYear := now.AddDate(-1, 0, 0) // 获取昨天的时间
	log.Infof("HandleYearBanUser start to get user banned status, now=%s, lastYear=%s", now.Format("2006-01-02"), lastYear.Format("2006-01-02"))

	idx := 0
	for offset := uint32(0); offset < uint32(len(uidList)); offset += uint32(limit) {
		end := offset + uint32(limit)
		if end > uint32(len(uidList)) {
			end = uint32(len(uidList))
		}
		userResp, err := banServerCli.BatchGetUserBannedStatus(ctx, &ban_server.BatchGetUserBannedStatusReq{
			UidList: uidList[offset:end],
		})
		if err != nil {
			log.Errorf("HandleYearBanUser BatchGetUserBannedStatus fail %v, offset=%d", err, offset)
			continue
		}
		for _, banStatus := range userResp.StatusList {
			if banStatus.Status != ban_server.BanStatusType_BAN_STATUS_BANNED {
				//log.Infof("HandleYearBanUser uid %d is not banned, skip", banStatus.Uid)
				continue
			}
			if banStatus.BannedAt > lastYear.Unix() { // 只有超过一年的封禁用户
				//log.Infof("HandleYearBanUser uid %d is not one year ban, :%v skip", banStatus.Uid, banStatus.BannedAt)
				continue
			}
			isMatch := false
			for _, reason := range ReasonList {
				if banStatus.Reason == reason {
					isMatch = true
				}
			}
			if !isMatch { // 只处理指定原因的封禁用户
				//log.Infof("HandleYearBanUser uid %d is not match reason %s, skip", banStatus.Uid, banStatus.Reason)
				continue
			}
			banUidList = append(banUidList, banStatus.Uid)
		}
		idx++
		if idx%10000 == 0 {
			log.Infof("HandleYearBanUser processed %d batches, current banUidList length %d", idx, len(banUidList))
		}

	}
	log.InfoWithCtx(ctx, "HandleYearBanUser get %d uids with one year ban", len(banUidList))
	return banUidList, nil
}

func GetUserInfo(uidList []uint32) ([]uint32, map[uint32]*account.User, error) {
	ctx := context.Background()
	userInfoMap := make(map[uint32]*account.User)
	accountCli, err := account.NewClient()
	if err != nil {
		log.Errorf("New account client fail %v", err)
		return nil, nil, err
	}
	finalUidList := make([]uint32, 0)
	idx := 0
	for offset := uint32(0); offset < uint32(len(uidList)); offset += uint32(50) {
		end := offset + uint32(50)
		if end > uint32(len(uidList)) {
			end = uint32(len(uidList))
		}
		userResp, err := accountCli.GetUsersMap(ctx, uidList[offset:end])
		if err != nil {
			log.Errorf("HandleYearBanUser GetUsersMap fail %v, offset=%d", err, offset)
			continue
		}
		for _, userInfo := range userResp {
			userInfoMap[userInfo.Uid] = userInfo
			finalUidList = append(finalUidList, userInfo.Uid)
		}
		idx++
		if idx%1000 == 0 {
			log.Infof("HandleYearBanUser processed %d batches, current userInfoMap length %d", idx, len(userInfoMap))
		}
	}
	log.Infof("GetUserInfo get %d user info", len(userInfoMap))
	return finalUidList, userInfoMap, nil
}

func GetContractOkUids(banUserList []uint32) ([]uint32, map[uint32]*pb.ContractCacheInfo, error) {
	contractUidList := make([]uint32, 0)
	contractRespMap := make(map[uint32]*pb.ContractCacheInfo)
	ctx := context.Background()
	anchorGoCli := anchorcontract_go.NewIClient()
	limit := 50
	idx := 0
	for i := 0; i < len(banUserList); i += limit {
		contractResp, sErr := anchorGoCli.BatchGetContractInfo(ctx, &pb.BatchGetContractInfoReq{Uids: banUserList[i:min(i+limit, len(banUserList))]})
		if sErr != nil {
			log.Errorf("HandleYearBanUser BatchGetContractInfo fail %v ", sErr)
			return nil, nil, sErr
		}
		for _, contractInfo := range contractResp.GetUid2ContractInfo() {
			if contractInfo.GetContract().GetGuildId() != 0 && contractInfo.GetContract().GetExpireTime() > uint32(time.Now().Unix()) {
				onlyMultiplayer := true
				for _, identity := range contractInfo.AnchorIdentityList {
					if identity == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_DOYEN) {
						continue
					}
					if identity != uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
						onlyMultiplayer = false
					}
				}
				if !onlyMultiplayer { //有其他身份，只处理单独娱乐厅身份的用户
					continue
				}
				contractUidList = append(contractUidList, contractInfo.GetContract().GetActorUid())
				contractRespMap[contractInfo.GetContract().GetActorUid()] = contractInfo
			}
		}
		idx++
		if idx%1000 == 0 {
			log.Infof("HandleYearBanUser processed %d batches, current contractUidList length %d", idx, len(contractUidList))
		}
	}
	log.Infof("GetContractOkUids get %d contract uids", len(contractUidList))
	return contractUidList, contractRespMap, nil
}

func CheckAllContract() {
	mysqlCfg := &config.MysqlConfig{
		Host:     "*************",
		Port:     3306,
		UserName: "godman",
		Password: "thegodofman",
		Database: "contract",
		Charset:  "utf8",
	}

	mysqlDb, err := gorm.Open(mysql.Open(mysqlCfg.ConnectionString()), &gorm.Config{
		Logger:                 logger.Default.LogMode(logger.Silent),
		SkipDefaultTransaction: true,
		PrepareStmt:            false,
	})
	if err != nil {
		log.Errorf("Failed to newMysqlStore err: (%v)", err)
		return
	}
	mysqlDb = mysqlDb.Debug()

	allUids, err := GetAllContractUids(mysqlDb)
	if err != nil {
		log.Errorf("GetAllContractUids fail %v", err)
		return
	}
	banUserList, err := GetCurrentBanUser(allUids)
	if err != nil {
		log.Errorf("GetCurrentBanUser fail %v", err)
		return
	}
	log.Infof("GetCurrentBanUser get %d ban user", len(banUserList))
	contractUserUidList, contractRespMap, err := GetContractOkUids(banUserList)
	if err != nil {
		log.Errorf("GetContractOkUids fail %v", err)
		return
	}
	log.Infof("GetContractOkUids get %d contract user", len(contractUserUidList))

	finialUids, uid2UseMap, err := GetUserInfo(contractUserUidList)
	if err != nil {
		log.Errorf("GetUserInfo fail %v", err)
		return
	}

	ctx := context.Background()
	log.InfoWithCtx(ctx, "HandleYearBanUser finalUidList %v", finialUids)

	successNum := 0
	anchorGoCli := anchorcontract_go.NewIClient()
	guildCli := guild_go.NewIClient()
	smsCli := sms_go.NewIClient()

	// Create or open the file
	fileName := fmt.Sprintf("oneyearbanuse_%s.log", time.Now().Format("20060102"))
	file, err := os.OpenFile(fileName, os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		fmt.Printf("Failed to create file: %v\n", err)
		return
	}
	defer file.Close()

	for _, userInfo := range uid2UseMap {
		uid := userInfo.Uid
		contractInfo := contractRespMap[uid]
		if contractInfo == nil || contractInfo.GetContract() == nil || contractInfo.GetContract().GetGuildId() == 0 {
			continue
		}
		// 4. 如果有签约，则解约
		_, sErr := anchorGoCli.CancelContractByUid(ctx, &pb.CancelContractByUidReq{
			OpUid:     uid,
			TargetUid: uid,
			GuildId:   contractInfo.Contract.GuildId,
		})
		if sErr != nil {
			log.Errorf("HandleYearBanUser CancelContractByUid fail %v, uid=%d, guildId=%d", sErr, uid, contractInfo.GetContract().GetGuildId())
		} else {
			log.Infof("HandleYearBanUser CancelContractByUid success, uid=%d, guildId=%d", uid, contractInfo.GetContract().GetGuildId())
		}

		// 5. 发送短信
		// 获取电话
		phone := userInfo.GetPhone()
		//phone = "13544354624"
		log.Infof("HandleYearBanUser use test phone %s for uid=%d", phone, uid)

		// 公会信息
		guildInfo, err := guildCli.GetGuild(ctx, contractInfo.GetContract().GetGuildId(), false)
		if err != nil {
			log.Errorf("HandleYearBanUser GetGuild fail %v, guildId=%d", err, contractInfo.GetContract().GetGuildId())
			continue
		}

		guildId := guildInfo.GetGuildId()
		if guildInfo.GetShortId() != 0 {
			guildId = guildInfo.GetShortId()
		}

		err = smsCli.SendCommonSms(ctx, &smsgoPb.SendCommonSmsReq{
			Usage: "Template_2025052702",
			UserInfoList: []*smsgoPb.UserInfo{
				{
					Phone:     phone,
					Uid:       userInfo.Uid,
					Sign:      "TT语音",
					ParamList: []string{userInfo.GetAlias(), guildInfo.GetName(), strconv.Itoa(int(guildId))},
				},
			},
		})
		if err != nil {
			log.Errorf("HandleYearBanUser SendCommonSms fail %v, uid=%d, phone=%s", err, userInfo.Uid, phone)
		} else {
			successNum++
		}

		content := fmt.Sprintf("%d,%s,%s,%d,%s\n", userInfo.Uid, userInfo.Alias, phone, guildId, guildInfo.Name)
		_, e := file.WriteString(content)
		if e != nil {
			log.Errorf("Content:%v,Failed to write to file: %v", content, e)
			return
		}
	}
	log.Infof("HandleYearBanUser success send sms %d times", successNum)
}
