package store

import (
    "context"
    "fmt"
    "strings"
    "time"

    "gitlab.ttyuyin.com/bizFund/bizFund/protocol/common/status"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/services/public-notice/internal/util"
    "google.golang.org/grpc/codes"
)

type ChannelBreakingNewsConfig struct {
    NewsID            uint32    `db:"id"`
    NewsName          string    `db:"news_name"`
    NewsTemplate      string    `db:"news_template"`
    RollingTime       uint32    `db:"rolling_time"`
    RollingCount      uint32    `db:"rolling_count"`
    MainBodyFontColor string    `db:"main_body_font_color"`
    NameFontColor     string    `db:"name_font_color"`
    FontShadowColor   string    `db:"font_shadow_color"`
    MarginLeft        uint32    `db:"margin_left"`
    AnimeType         uint32    `db:"anime_type"`
    AnimeZip          string    `db:"anime_zip"`
    AnimeMD5          string    `db:"anime_md5"`
    AnimePreview      string    `db:"anime_preview"`
    BgPicture         string    `db:"bg_picture"`
    CreateTime        time.Time `db:"create_time"`
    UpdateTime        time.Time `db:"update_time"`
}

func (cfg *ChannelBreakingNewsConfig) allField() []interface{} {
    return []interface{}{
        cfg.NewsName,
        cfg.NewsTemplate,
        cfg.RollingTime,
        cfg.RollingCount,
        cfg.MainBodyFontColor,
        cfg.NameFontColor,
        cfg.FontShadowColor,
        cfg.MarginLeft,
        cfg.AnimeType,
        cfg.AnimeZip,
        cfg.AnimeMD5,
        cfg.AnimePreview,
        cfg.BgPicture,
    }
}

const (
    createChannelBreakingNewsConfigTable = `
CREATE TABLE IF NOT EXISTS channel_breaking_news_config (
  id int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  news_name varchar(255) NOT NULL DEFAULT '' COMMENT '公告名称',
  news_template blob NULL COMMENT '公告模板',
  rolling_time int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '滚动时间',
  rolling_count int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '滚动次数',
  main_body_font_color varchar(10) NOT NULL DEFAULT '' COMMENT '正文字体色值',
  name_font_color varchar(10) NOT NULL DEFAULT '' COMMENT '名称字体色值',
  font_shadow_color varchar(10) NOT NULL DEFAULT '' COMMENT '字体投影色值',
  margin_left int(10) NOT NULL DEFAULT 0 COMMENT '左边距',

  anime_type int(10) NOT NULL DEFAULT 0 COMMENT '动画类型',
  anime_zip varchar(255) NOT NULL DEFAULT '' COMMENT '动画url',
  anime_md5 varchar(128) NOT NULL DEFAULT '' COMMENT '动画md5',
  anime_preview varchar(255) NOT NULL DEFAULT '' COMMENT '动画预览',
  bg_picture varchar(255) NOT NULL DEFAULT '' COMMENT '背景图片',
  create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id)
) engine=InnoDB default charset=utf8mb4 comment '频道全服公告配置表';`
)

var channelBreakingFieldList = []string{
    "news_name",
    "news_template",
    "rolling_time",
    "rolling_count",
    "main_body_font_color",
    "name_font_color",
    "font_shadow_color",
    "margin_left",
    "anime_type",
    "anime_zip",
    "anime_md5",
    "anime_preview",
    "bg_picture",
}

var channelBreakingQueryFieldList = []string{
    "id",
    "news_name",
    "news_template",
    "rolling_time",
    "rolling_count",
    "main_body_font_color",
    "name_font_color",
    "font_shadow_color",
    "margin_left",
    "anime_type",
    "anime_zip",
    "anime_md5",
    "anime_preview",
    "bg_picture",
    "create_time",
    "update_time",
}

func (cfg *ChannelBreakingNewsConfig) genAddFieldString() string {
    return strings.Join(channelBreakingFieldList, ",")
}

func (cfg *ChannelBreakingNewsConfig) genAddFieldPlaceholder() string {
    placeholder := make([]string, len(channelBreakingFieldList))
    for i := range channelBreakingFieldList {
        placeholder[i] = "?"
    }
    return strings.Join(placeholder, ",")
}

func (st *Store) CreateChannelBreakingNewsConfigTable() error {
    _, err := st.Exec(createChannelBreakingNewsConfigTable)
    if err != nil {
        log.Errorf("CreateChannelBreakingNewsConfigTable fail. err:%v", err)
        return err
    }

    return nil
}

func genChannelBreakingUpdateSetFieldSql() string {
    setField := make([]string, len(channelBreakingFieldList))
    for i, field := range channelBreakingFieldList {
        setField[i] = field + "=?"
    }
    return strings.Join(setField, ",")
}

func (st *Store) AddChannelBreakingNewsConfig(ctx context.Context, newsCfg *ChannelBreakingNewsConfig) error {
    sql := fmt.Sprintf(`INSERT INTO channel_breaking_news_config(%s) VALUES(%s)`, newsCfg.genAddFieldString(), newsCfg.genAddFieldPlaceholder())

    rs, err := st.Exec(sql, newsCfg.allField()...)
    if err != nil {
        log.ErrorWithCtx(ctx, "AddChannelBreakingNewsConfig fail. sql:%s, err:%v", sql, err)
        return err
    }
    if rowAffect, _ := rs.RowsAffected(); rowAffect < 1 {
        log.ErrorWithCtx(ctx, "AddChannelBreakingNewsConfig fail. sql:%s, err:%v", sql, err)
        return fmt.Errorf("not affect")
    }
    newsId, _ := rs.LastInsertId()
    newsCfg.NewsID = uint32(newsId)
    log.InfoWithCtx(ctx, "AddChannelBreakingNewsConfig success. newsCfg:%+v", newsCfg)
    return nil
}

func (st *Store) UpdateChannelBreakingNewsConfig(ctx context.Context, newsCfg *ChannelBreakingNewsConfig) error {
    sql := fmt.Sprintf(`UPDATE channel_breaking_news_config set %s WHERE id=?`, genChannelBreakingUpdateSetFieldSql())

    args := append(newsCfg.allField(), newsCfg.NewsID)
    _, err := st.Exec(sql, args...)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateChannelBreakingNewsConfig fail. sql:%s, err:%v", sql, err)
        return err
    }

    log.InfoWithCtx(ctx, "UpdateChannelBreakingNewsConfig success. newsCfg:%+v", newsCfg)
    return nil
}

func (st *Store) BatchDelChannelBreakingNewsConfig(ctx context.Context, newsIdsList []uint32) error {
    if len(newsIdsList) == 0 {
        return nil
    }
    sql := fmt.Sprintf(`DELETE FROM channel_breaking_news_config WHERE id in (%s)`, util.Uint32Slice2Str(newsIdsList))
    _, err := st.Exec(sql)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchDelChannelBreakingNewsConfig fail. sql:%s, err:%v", sql, err)
        return err
    }
    log.InfoWithCtx(ctx, "BatchDelChannelBreakingNewsConfig success. newsIdsList:%+v", newsIdsList)
    return nil
}

func genChannelBreakingQueryFieldString() string {
    return strings.Join(channelBreakingQueryFieldList, ",")
}

func (st *Store) BatchGetChannelBreakingNewsConfig(ctx context.Context, newsId uint32, searchKeyword string) ([]*ChannelBreakingNewsConfig, error) {
    sql := fmt.Sprintf(`SELECT %s FROM channel_breaking_news_config`, genChannelBreakingQueryFieldString())
    execSql := sql
    if newsId != 0 {
        execSql = fmt.Sprintf(sql+" WHERE id=%d", newsId)
    }
    if searchKeyword != "" {
        execSql = sql + " WHERE news_name like '%" + searchKeyword + "%'"
    }
    execSql += " order by create_time desc"
    rows, err := st.Queryx(execSql)

    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetChannelBreakingNewsConfig fail. sql:%s, err:%v", execSql, err)
        return nil, err
    }
    defer rows.Close()

    rs := make([]*ChannelBreakingNewsConfig, 0, 8)

    for rows.Next() {
        tmp := &ChannelBreakingNewsConfig{}
        err := rows.StructScan(tmp)
        if err != nil {
            log.ErrorWithCtx(ctx, "BatchGetChannelBreakingNewsConfig StructScan fail. err:%v", err)
            return nil, err
        }
        rs = append(rs, tmp)
    }
    return rs, nil
}

func (st *Store) GetChannelBreakingNewsByNewsId(ctx context.Context, newsId uint32) (*ChannelBreakingNewsConfig, error) {
    sql := fmt.Sprintf(`SELECT %s FROM channel_breaking_news_config WHERE id=%d`, genChannelBreakingQueryFieldString(), newsId)
    rows, err := st.Queryx(sql)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelBreakingNewsByNewsId fail. sql:%s, err:%v, newsId:%d", sql, err, newsId)
        return nil, err
    }

    defer rows.Close()
    if rows.Next() {
        tmp := &ChannelBreakingNewsConfig{}
        err := rows.StructScan(tmp)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetChannelBreakingNewsByNewsId StructScan fail. err:%v, newsId:%d", err, newsId)
            return nil, err
        }
        return tmp, nil
    } else {
        log.ErrorWithCtx(ctx, "GetChannelBreakingNewsByNewsId failed, newsId:%d not found", newsId)
        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "全服公告不存在")
    }
}
