package mgr

import (
    "bytes"
    "context"
    "encoding/json"
    "fmt"
    "regexp"
    "text/template"

    "gitlab.ttyuyin.com/tyr/x/compatible/proto"
    "golang.52tt.com/clients/account"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/app/channel"
    pushPb "golang.52tt.com/protocol/app/push"
    "golang.52tt.com/protocol/common/status"
    public_notice "golang.52tt.com/protocol/services/public-notice"
    userpresentGoPb "golang.52tt.com/protocol/services/userpresent-go"
    "golang.52tt.com/services/public-notice/internal/conf"
    "golang.52tt.com/services/public-notice/internal/store"
    "google.golang.org/grpc/codes"
)

const (
    templateTypeUnknown = iota
    templateTypeText
    templateTypeUserNickname
    templateTypeUserFaceMD5
    templateTypeGiftName
    templateTypeGiftCount
    templateTypeGiftIcon
    templateTypeGiftPrice
)

type templateItemInfo struct {
    Type  uint32
    Value string
}

var templateTypeMap = map[string]string{
    "{{user.nickname}}":   "用户名称",
    "{{user.face_md5}}":   "用户头像",
    "{{gift.gift_name}}":  "礼物名称",
    "{{gift.gift_cnt}}":   "数量",
    "{{gift.gift_icon}}":  "礼物图标",
    "{{gift.gift_price}}": "礼物价格",
}

func regexChannelBreakingTemplate(template string) (needUser bool, needGift bool, needUserFace bool, result []*templateItemInfo, err error) {
    re := regexp.MustCompile(`(\{\{.*?}})`)
    parts := re.Split(template, -1)
    matches := re.FindAllString(template, -1)

    matchCntMap := make(map[string]uint32)
    for _, match := range matches {
        matchCntMap[match]++
    }

    for key, cnt := range matchCntMap {
        if cnt >= 2 {
            log.Errorf("regexChannelBreakingTemplate matchCntMap key:%s, cnt:%d > 2", key, cnt)
            return false, false, false, nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("播报信息【%s】重复，请重新配置", templateTypeMap[key]))
        }
    }

    // 初始化结果切片
    result = make([]*templateItemInfo, 0)

    // 合并普通文本和变量，保证顺序
    for i := 0; i < len(parts) || i < len(matches); i++ {
        if i < len(parts) && parts[i] != "" {
            result = append(result, &templateItemInfo{
                Type:  templateTypeText,
                Value: parts[i],
            })
        }
        if i < len(matches) {
            // 确定模板变量的类型
            var itemType uint32
            match := matches[i]
            switch match {
            case "{{user.nickname}}":
                itemType = templateTypeUserNickname
            case "{{user.face_md5}}":
                itemType = templateTypeUserFaceMD5
            case "{{gift.gift_name}}":
                itemType = templateTypeGiftName
            case "{{gift.gift_cnt}}":
                itemType = templateTypeGiftCount
            case "{{gift.gift_icon}}":
                itemType = templateTypeGiftIcon
            case "{{gift.gift_price}}":
                itemType = templateTypeGiftPrice
            default:
                itemType = templateTypeUnknown
            }

            result = append(result, &templateItemInfo{
                Type:  itemType,
                Value: match,
            })
        }
    }

    for _, item := range result {
        if item.Type == templateTypeUserNickname {
            needUser = true
        } else if item.Type == templateTypeUserFaceMD5 {
            needUserFace = true
        } else if item.Type == templateTypeGiftName ||
            item.Type == templateTypeGiftCount ||
            item.Type == templateTypeGiftIcon ||
            item.Type == templateTypeGiftPrice {
            needGift = true
        }
    }

    return needUser, needGift, needUserFace, result, nil
}

func (m *BreakingNewsMgr) PushBreakingNewsInRoom(c context.Context, req *public_notice.PushBreakingNewsInRoomReq) error {
    var err error
    channelNews, err := m.st.GetChannelBreakingNewsByNewsId(c, req.GetNewsId())
    if err != nil {
        log.ErrorWithCtx(c, "PushBreakingNewsInRoom GetChannelBreakingNewsByNewsId failed, err:%v, req:%+v", err, req)
        return err
    }
    if channelNews == nil {
        log.ErrorWithCtx(c, "PushBreakingNewsInRoom GetChannelBreakingNewsByNewsId failed, newsId:%d not found", req.GetNewsId())
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "全服公告不存在")
    }

    needUser, needGift, needUserFace, result, err := regexChannelBreakingTemplate(channelNews.NewsTemplate)

    var giftInfoResp *userpresentGoPb.GetPresentConfigByIdResp
    if needGift {
        if req.GetGiftId() == 0 {
            log.ErrorWithCtx(c, "PushBreakingNewsInRoom failed, invalid request: %+v", req)
            return  protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
        }
        giftInfoResp, err = m.presentCli.GetPresentConfigById(c, &userpresentGoPb.GetPresentConfigByIdReq{ItemId: req.GetGiftId()})
        if err != nil {
            log.ErrorWithCtx(c, "PushBreakingNewsInRoom GetPresentConfigById failed, err:%v, req:%+v", err, req)
            return err
        }
    }
    var userInfo *account.User
    var headImage string
    if needUser {
        userInfo, err = m.accountCli.GetUser(c, req.GetUid())
        if err != nil {
            log.ErrorWithCtx(c, "PushBreakingNewsInRoom GetUser failed, err:%v, req:%+v", err, req)
            return err
        }
        if needUserFace {
            headImage, err = m.headImageCli.GetHeadImageMd5(c, req.GetUid(), userInfo.GetUsername())
            if err != nil {
                log.ErrorWithCtx(c, "PushBreakingNewsInRoom GetHeadImageMd5 failed, err:%v, req:%+v", err, req)
                return err
            }
        }
    }

    var body string
    for _, res := range result {
        switch res.Type {
        case templateTypeText:
            body += genSimpleTextTemplate(res.Value)
        case templateTypeUserNickname:
            body += genNicknameTemplate(userInfo)
        case templateTypeUserFaceMD5:
            body += genUserFaceTemplate(userInfo, headImage)
        case templateTypeGiftName:
            // 礼物名称共用simpleTextTemplate
            body += genSimpleTextTemplate(giftInfoResp.GetItemConfig().GetBaseConfig().GetName())
        case templateTypeGiftIcon:
            body += genGiftIconTemplate(giftInfoResp.GetItemConfig().GetBaseConfig().GetIconUrl())
        case templateTypeGiftCount:
            body += genSimpleTextTemplate(fmt.Sprintf("x%d", req.GetGiftCnt()))
        case templateTypeGiftPrice:
            var unit string
            if giftInfoResp.GetItemConfig().GetBaseConfig().GetPriceType() == uint32(userpresentGoPb.PresentPriceType_PRESENT_PRICE_TBEAN) {
                unit = "豆"
            } else {
                unit = "钻"
            }
            body += genSimpleTextTemplate(fmt.Sprintf("%d%s", giftInfoResp.GetItemConfig().GetBaseConfig().GetPrice(), unit))
        default:
            log.WarnWithCtx(c, "PushBreakingNewsInRoom unknow templateType, err:%v, res:%+v", err, res)
        }
    }

    finalTemplate := fmt.Sprintf(conf.GetTemplateByAnimeType(c, channelNews.AnimeType), body)
    if channelNews.BgPicture == "" {
        // 默认底图
        channelNews.BgPicture = "https://ga-album-cdnqn.52tt.com/web/tt-desktop/20250722154839_83115365.png"
    }

    temp, err := template.New("tpl").Parse(finalTemplate)
    if err != nil {
        log.ErrorWithCtx(c, "PushBreakingNewsInRoom Parse failed, err:%v, finalTemplate:%s", err, finalTemplate)
        return err
    }
    var buf bytes.Buffer
    if err = temp.Execute(&buf, channelNews); err != nil {
        log.ErrorWithCtx(c, "PushBreakingNewsInRoom Execute failed, err:%v, finalTemplate:%s", err, finalTemplate)
        return err
    }

    opt := &pushPb.ChannelBreakingNewsNotify{
        NewsContent: buf.String(),
        Duration:    channelNews.RollingCount * channelNews.RollingTime,
    }
    optData, err := proto.Marshal(opt)
    if err != nil {
        log.ErrorWithCtx(c, "PushBreakingNewsInRoom Marshal optPb failed, err:%v, optPb:%+v", err, opt)
        return err
    }

    res, err := m.channelMsgApiCli.SimplePushToUsers(c, []uint32{req.GetUid()}, 1024, req.GetChannelId(), uint32(channel.ChannelMsgType_CHANNEL_BREAKING_NEWS_NOTIFY), "测试", optData, false)
    if err != nil {
        log.ErrorWithCtx(c, "PushBreakingNewsInRoom SimplePushToUsers failed, err:%v, req:%+v, res:%+v", err, req, res)
        return err
    }
    log.InfoWithCtx(c, "PushBreakingNewsInRoom SimplePushToUsers success, req:%+v, res:%+v, opt:%+v", req, res, opt)
    return nil
}

func (m *BreakingNewsMgr) convert2ChannelBreakingNewsConfig(pbChannelBreakingNewsConfig *public_notice.ChannelBreakingNewsConfig) *store.ChannelBreakingNewsConfig {

    return &store.ChannelBreakingNewsConfig{
        NewsID:            pbChannelBreakingNewsConfig.GetNewsId(),
        NewsName:          pbChannelBreakingNewsConfig.GetNewsName(),
        NewsTemplate:      pbChannelBreakingNewsConfig.GetNewsTemplate(),
        RollingTime:       pbChannelBreakingNewsConfig.GetRollingTime(),
        RollingCount:      pbChannelBreakingNewsConfig.GetRollingCount(),
        MainBodyFontColor: pbChannelBreakingNewsConfig.GetMainBodyFontColor(),
        NameFontColor:     pbChannelBreakingNewsConfig.GetNameFontColor(),
        FontShadowColor:   pbChannelBreakingNewsConfig.GetFontShadowColor(),
        MarginLeft:        pbChannelBreakingNewsConfig.GetMarginLeft(),
        AnimeType:         pbChannelBreakingNewsConfig.GetAnimeType(),
        AnimeZip:          pbChannelBreakingNewsConfig.GetAnimeZip(),
        AnimeMD5:          pbChannelBreakingNewsConfig.GetAnimeMd5(),
        AnimePreview:      pbChannelBreakingNewsConfig.GetAnimePreview(),
        BgPicture:         pbChannelBreakingNewsConfig.GetBgPictureUrl(),
    }
}

func (m *BreakingNewsMgr) AddChannelBreakingNewsConfig(ctx context.Context, req *public_notice.AddChannelBreakingNewsConfigReq) error {
    _, _, _, _, err := regexChannelBreakingTemplate(req.GetChannelBreakingNewsConfig().GetNewsTemplate())
    if err != nil {
        log.ErrorWithCtx(ctx, "AddChannelBreakingNewsConfig regexChannelBreakingTemplate failed, err:%v", err)
        return err
    }
    newsCfg := m.convert2ChannelBreakingNewsConfig(req.GetChannelBreakingNewsConfig())
    if newsCfg == nil {
        return nil
    }
    err = m.st.AddChannelBreakingNewsConfig(ctx, newsCfg)
    if err != nil {
        log.ErrorWithCtx(ctx, "AddChannelBreakingNewsConfig failed, err:%v", err)
        return err
    }
    return nil
}

func (m *BreakingNewsMgr) UpdateChannelBreakingNewsConfig(ctx context.Context, req *public_notice.UpdateChannelBreakingNewsConfigReq) error {
    _, _, _, _, err := regexChannelBreakingTemplate(req.GetChannelBreakingNewsConfig().GetNewsTemplate())
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateChannelBreakingNewsConfig regexChannelBreakingTemplate failed, err:%v", err)
        return err
    }
    newsCfg := m.convert2ChannelBreakingNewsConfig(req.GetChannelBreakingNewsConfig())
    if newsCfg == nil {
        log.ErrorWithCtx(ctx, "UpdateChannelBreakingNewsConfig convert2ChannelBreakingNewsConfig failed")
        return nil
    }
    err = m.st.UpdateChannelBreakingNewsConfig(ctx, newsCfg)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateChannelBreakingNewsConfig failed, err:%v", err)
        return err
    }
    return nil
}

func (m *BreakingNewsMgr) BatchDelChannelBreakingNewsConfig(ctx context.Context, req *public_notice.BatchDelChannelBreakingNewsConfigReq) error {
    newsIdsList := req.GetNewsIdList()
    if len(newsIdsList) == 0 {
        log.InfoWithCtx(ctx, "BatchDelChannelBreakingNewsConfig newsIdsList is empty")
        return nil
    }
    err := m.st.BatchDelChannelBreakingNewsConfig(ctx, newsIdsList)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchDelChannelBreakingNewsConfig failed, err:%v", err)
        return err
    }
    return nil
}

func (m *BreakingNewsMgr) BatchGetChannelBreakingNewsConfig(ctx context.Context, req *public_notice.BatchGetChannelBreakingNewsConfigReq) (*public_notice.BatchGetChannelBreakingNewsConfigResp, error) {
    out := &public_notice.BatchGetChannelBreakingNewsConfigResp{}

    newsCfgList, err := m.st.BatchGetChannelBreakingNewsConfig(ctx, req.GetSearchNewsId(), req.GetSearchKeyword())
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetChannelBreakingNewsConfig failed, err:%v", err)
        return out, err
    }

    // 转换store层的数据结构为protobuf结构
    pbNewsCfgList := make([]*public_notice.ChannelBreakingNewsConfig, 0, len(newsCfgList))
    for _, newsCfg := range newsCfgList {
        pbNewsCfg := m.convertStore2PbChannelBreakingNewsConfig(newsCfg)
        if pbNewsCfg != nil {
            pbNewsCfgList = append(pbNewsCfgList, pbNewsCfg)
        }
    }

    out.ChannelBreakingNewsConfigList = pbNewsCfgList
    return out, nil
}

func (m *BreakingNewsMgr) convertStore2PbChannelBreakingNewsConfig(storeCfg *store.ChannelBreakingNewsConfig) *public_notice.ChannelBreakingNewsConfig {
    if storeCfg == nil {
        return nil
    }

    // 反序列化模板列表
    var newsTemplateList []*public_notice.NewsTemplateItem
    if storeCfg.NewsTemplate != "" {
        err := json.Unmarshal([]byte(storeCfg.NewsTemplate), &newsTemplateList)
        if err != nil {
            log.Errorf("convertStore2PbChannelBreakingNewsConfig json.Unmarshal failed err:%v, storeCfg:%+v", err, storeCfg)
            newsTemplateList = []*public_notice.NewsTemplateItem{}
        }
    }

    return &public_notice.ChannelBreakingNewsConfig{
        NewsId:            storeCfg.NewsID,
        NewsName:          storeCfg.NewsName,
        NewsTemplate:      storeCfg.NewsTemplate,
        RollingTime:       storeCfg.RollingTime,
        RollingCount:      storeCfg.RollingCount,
        MainBodyFontColor: storeCfg.MainBodyFontColor,
        NameFontColor:     storeCfg.NameFontColor,
        FontShadowColor:   storeCfg.FontShadowColor,
        MarginLeft:        storeCfg.MarginLeft,
        AnimeType:         storeCfg.AnimeType,
        AnimeZip:          storeCfg.AnimeZip,
        AnimeMd5:          storeCfg.AnimeMD5,
        AnimePreview:      storeCfg.AnimePreview,
        BgPictureUrl:      storeCfg.BgPicture,
    }
}
