package server

import (
	context0 "context"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	public_notice "golang.52tt.com/protocol/services/public-notice"
	"google.golang.org/grpc/codes"
)

func (s *publicNoticeServer) PushBreakingNewsInRoom(c context0.Context, req *public_notice.PushBreakingNewsInRoomReq) (*public_notice.PushBreakingNewsInRoomResp, error) {
	out := &public_notice.PushBreakingNewsInRoomResp{}
	defer func() {
		log.InfoWithCtx(c, "PushBreakingNewsInRoom req:%+v, out:%+v", req, out)
	}()
	if req.GetNewsId() == 0 || req.GetChannelId() == 0 || req.GetUid() == 0 {
		log.ErrorWithCtx(c, "PushBreakingNewsInRoom failed, invalid request: %+v", req)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	err := s.breakingNewsMgr.PushBreakingNewsInRoom(c, req)
	if err != nil {
		log.ErrorWithCtx(c, "PushBreakingNewsInRoom failed, err:%v, req:%+v", err, req)
		return out, err
	}
	return out, nil
}

func (s *publicNoticeServer) AddChannelBreakingNewsConfig(c context0.Context, req *public_notice.AddChannelBreakingNewsConfigReq) (*public_notice.AddChannelBreakingNewsConfigResp, error) {
	out := &public_notice.AddChannelBreakingNewsConfigResp{}
	defer func() {
		log.InfoWithCtx(c, "AddChannelBreakingNewsConfig req:%+v, out:%+v", req, out)
	}()

	err := s.breakingNewsMgr.AddChannelBreakingNewsConfig(c, req)
	if err != nil {
		log.ErrorWithCtx(c, "AddChannelBreakingNewsConfig failed, err:%v", err)
		return out, err
	}
	return out, nil
}

func (s *publicNoticeServer) UpdateChannelBreakingNewsConfig(c context0.Context, req *public_notice.UpdateChannelBreakingNewsConfigReq) (*public_notice.UpdateChannelBreakingNewsConfigResp, error) {
	out := &public_notice.UpdateChannelBreakingNewsConfigResp{}
	defer func() {
		log.InfoWithCtx(c, "UpdateChannelBreakingNewsConfig req:%+v, out:%+v", req, out)
	}()

	err := s.breakingNewsMgr.UpdateChannelBreakingNewsConfig(c, req)
	if err != nil {
		log.ErrorWithCtx(c, "UpdateChannelBreakingNewsConfig failed, err:%v", err)
		return out, err
	}
	return out, nil
}

func (s *publicNoticeServer) BatchDelChannelBreakingNewsConfig(c context0.Context, req *public_notice.BatchDelChannelBreakingNewsConfigReq) (*public_notice.BatchDelChannelBreakingNewsConfigResp, error) {
	out := &public_notice.BatchDelChannelBreakingNewsConfigResp{}
	defer func() {
		log.InfoWithCtx(c, "BatchDelChannelBreakingNewsConfig req:%+v, out:%+v", req, out)
	}()

	err := s.breakingNewsMgr.BatchDelChannelBreakingNewsConfig(c, req)
	if err != nil {
		log.ErrorWithCtx(c, "BatchDelChannelBreakingNewsConfig failed, err:%v", err)
		return out, err
	}
	return out, nil
}

func (s *publicNoticeServer) BatchGetChannelBreakingNewsConfig(c context0.Context, req *public_notice.BatchGetChannelBreakingNewsConfigReq) (*public_notice.BatchGetChannelBreakingNewsConfigResp, error) {
	out, err := s.breakingNewsMgr.BatchGetChannelBreakingNewsConfig(c, req)
	defer func() {
		log.InfoWithCtx(c, "BatchGetChannelBreakingNewsConfig req:%+v, out:%+v", req, out)
	}()

	if err != nil {
		log.ErrorWithCtx(c, "BatchGetChannelBreakingNewsConfig failed, err:%v", err)
		return out, err
	}
	return out, nil
}
