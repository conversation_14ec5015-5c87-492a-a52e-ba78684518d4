package internal

import (
    "context"
    "crypto/hmac"
    "crypto/sha256"
    "encoding/hex"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    "golang.52tt.com/protocol/common/status"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
    channel "golang.52tt.com/protocol/services/channelsvr"
    "google.golang.org/grpc/codes"
)

/*婚礼后沉淀相关*/

func (s *Server) GetUserWeddingPrecipitation(ctx context.Context, request *channel_wedding_logic.GetUserWeddingPrecipitationRequest) (*channel_wedding_logic.GetUserWeddingPrecipitationResponse, error) {
    out := &channel_wedding_logic.GetUserWeddingPrecipitationResponse{
        WeddingClipList: make([]*channel_wedding_logic.WeddingClipInfo, 0),
        GroupPhotoList:  make([]*channel_wedding_logic.WeddingClipInfo, 0),
    }
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok || request.GetTargetUid() == 0 {
        log.ErrorWithCtx(ctx, "ApplyToJoinChairGame ServiceInfoFromContext or req:%d invalid", request)
        return out, ErrParamInValid
    }
    opUid := svrInfo.UserID

    // 获取用户婚礼关系信息
    marriageResp, err := s.weddingPlanCli.GetMarriageStatus(ctx, &channel_wedding_plan.GetMarriageStatusRequest{
        Uid: request.GetTargetUid(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserWeddingPrecipitation fail to GetMarriageStatus. req: %+v, err:%v", request, err)
        return out, err
    }

    if marriageResp.GetRelationInfo() == nil || // 没有婚姻关系
        marriageResp.GetRelationInfo().GetPartnerUid() == 0 ||
        (marriageResp.GetIsHide() && opUid != request.GetTargetUid()) { // 用户隐藏关系且非主态
        return out, nil
    }

    // get user profile
    userMap, err := s.userProfile.BatchGetUserProfileV2(ctx, []uint32{request.GetTargetUid(), marriageResp.GetRelationInfo().GetPartnerUid()}, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserWeddingPrecipitation fail to BatchGetUserProfileV2. req: %+v, err:%v", request, err)
        return out, err
    }

    certificateResp, err := s.weddingCli.GetUserWeddingCertificate(ctx, &channel_wedding.GetUserWeddingCertificateReq{
        UidA:      marriageResp.GetRelationInfo().GetUid(),
        UidB:      marriageResp.GetRelationInfo().GetPartnerUid(),
        BeginTime: marriageResp.GetRelationInfo().GetRelationCtime(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserWeddingPrecipitation fail to GetUserWeddingCertificate. req: %+v, err:%v", request, err)
        return out, err
    }

    certificate := certificateResp.GetWeddingCertificate()
    if certificate.GetWeddingId() == 0 {
        return out, nil
    }

    // 沉淀信息存在 && 主态才返回以下信息
    if opUid == request.GetTargetUid() {
        out.HideStatus = uint32(channel_wedding_logic.HideOpType_HIDE_OP_TYPE_SHOW)
        if marriageResp.GetIsHide() {
            out.HideStatus = uint32(channel_wedding_logic.HideOpType_HIDE_OP_TYPE_HIDE)
        }
        out.DivorceFreezeDay = marriageResp.GetAutoDivorceDay()
        out.DivorceFreezeEndTs = marriageResp.GetDivorceDeadline()
        out.InDivorceFreeze = marriageResp.GetDivorceDeadline() > 0
    }

    // 婚礼沉淀信息
    out.WeddingCertificate = &channel_wedding_logic.WeddingCertificate{
        WeddingId:      certificate.GetWeddingId(),
        Groom:          userMap[certificate.GetGroomUid()],
        Bride:          userMap[certificate.GetBrideUid()],
        WeddingTime:    certificate.GetWeddingTime(),
        WeddingThemeId: certificate.GetWeddingThemeId(),
        PicUrl:         certificate.GetPicUrl(),
    }

    clipResp, err := s.weddingCli.GetUserWeddingWeddingClips(ctx, &channel_wedding.GetUserWeddingWeddingClipsReq{
        UidA: certificate.GetGroomUid(),
        UidB: certificate.GetBrideUid(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserWeddingPrecipitation fail to GetUserWeddingWeddingClips. req: %+v, err:%v", request, err)
        return out, err
    }

    cidMap := make(map[uint32]interface{})
    for _, clip := range append(clipResp.GetGroupPhotoList(), clipResp.GetWeddingClipList()...) {
        cidMap[clip.GetCid()] = nil
    }

    cidList := make([]uint32, 0, len(cidMap))
    for cid := range cidMap {
        cidList = append(cidList, cid)
    }

    channelInfoMap, err := s.channelCli.BatchGetChannelSimpleInfo(ctx, opUid, cidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserWeddingPrecipitation fail to BatchGetChannelSimpleInfo. req: %+v, err:%v", request, err)
        return out, err
    }

    for _, clip := range clipResp.GetWeddingClipList() {
        out.WeddingClipList = append(out.WeddingClipList, fillWeddingClipInfo(clip, channelInfoMap[clip.GetCid()]))
    }

    for _, clip := range clipResp.GetGroupPhotoList() {
        out.GroupPhotoList = append(out.GroupPhotoList, fillWeddingClipInfo(clip, channelInfoMap[clip.GetCid()]))
    }

    return out, nil
}

func fillWeddingClipInfo(clip *channel_wedding.WeddingClipInfo, channelInfo *channel.ChannelSimpleInfo) *channel_wedding_logic.WeddingClipInfo {
    clipInfo := &channel_wedding_logic.WeddingClipInfo{
        ThemeId:      clip.GetThemeId(),
        ThemeName:    clip.GetThemeName()+"婚礼",
        WeddingId:    clip.GetWeddingId(),
        ChannelName:  channelInfo.GetName(),
        ScenePicList: make([]*channel_wedding_logic.WeddingScenePic, 0),
    }

    for _, scene := range clip.GetScenePicList() {
        clipInfo.ScenePicList = append(clipInfo.ScenePicList, &channel_wedding_logic.WeddingScenePic{
            Scene:      scene.GetScene(),
            PicUrl:     scene.GetPicUrl(),
            CreateTime: scene.GetCreateTime(),
            SceneIcon:  scene.GetSceneIcon(),
        })
    }
    return clipInfo
}

func (s *Server) ReportWeddingScenePic(ctx context.Context, req *channel_wedding_logic.ReportWeddingScenePicRequest) (*channel_wedding_logic.ReportWeddingScenePicResponse, error) {
    var err error
    out := &channel_wedding_logic.ReportWeddingScenePicResponse{}
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok || req.GetWeddingId() == 0 || req.GetPicUrl() == "" {
        log.ErrorWithCtx(ctx, "ApplyToJoinChairGame ServiceInfoFromContext or req:%d invalid", req)
        return out, ErrParamInValid
    }
    opUid := svrInfo.UserID

    signature := generateSignature(req.GetPicUrl(), opUid)
    if signature != req.GetSignature() {
        log.ErrorWithCtx(ctx, "ReportWeddingScenePic fail to generateSignature. req: %+v, svr_signature:%s, err:%v", req, signature, status.ErrRequestParamInvalid)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "签名错误")
    }

    //isHost, err := s.isHost(ctx, opUid, req.GetCid())
    //if err != nil {
    //    log.ErrorWithCtx(ctx, "ReportWeddingScenePic fail to isHost. req: %+v, err:%v", req, err)
    //    return out, err
    //}
    //if !isHost {
    //    log.ErrorWithCtx(ctx, "ReportWeddingScenePic not host. req: %+v", req)
    //    return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "非主态无法操作")
    //}

    _, err = s.weddingCli.ReportWeddingScenePic(ctx, &channel_wedding.ReportWeddingScenePicReq{
        OpUid:     opUid,
        Cid:       req.GetCid(),
        WeddingId: req.GetWeddingId(),
        Scene:     req.GetScene(),
        PicUrl:    req.GetPicUrl(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "ReportWeddingScenePic fail to ReportWeddingScenePic. req: %+v, err:%v", req, err)
        return out, err
    }

    log.InfoWithCtx(ctx, "ReportWeddingScenePic success. req: %+v", req)
    return out, nil
}

// 生成签名
func generateSignature(url string, opUid uint32) string {
    secretKey := fmt.Sprintf("channel-wedding-%d", opUid)
    key := []byte(secretKey)
    h := hmac.New(sha256.New, key)
    h.Write([]byte(url))
    return hex.EncodeToString(h.Sum(nil))
}

func (s *Server) HideWeddingRelation(ctx context.Context, request *channel_wedding_logic.HideWeddingRelationRequest) (*channel_wedding_logic.HideWeddingRelationResponse, error) {
    out := &channel_wedding_logic.HideWeddingRelationResponse{}
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok || request.GetOpType() == 0 {
        log.ErrorWithCtx(ctx, "ApplyToJoinChairGame ServiceInfoFromContext or req:%d invalid", request)
        return out, ErrParamInValid
    }
    opUid := svrInfo.UserID

    setResp, err := s.weddingPlanCli.SetUserRelationHideStatus(ctx, &channel_wedding_plan.SetUserRelationHideStatusRequest{
        Uid:    opUid,
        OpType: request.GetOpType(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "HideWeddingRelation fail to SetUserRelationHideStatus. req: %+v, err:%v", request, err)
        return out, err
    }
    out.HideStatus = setResp.GetHideStatus()
    log.InfoWithCtx(ctx, "HideWeddingRelation success. req: %+v out:%v", request, out)
    return out, nil
}

func (s *Server) GetWeddingRankEntry(ctx context.Context, request *channel_wedding_logic.GetWeddingRankEntryRequest) (*channel_wedding_logic.GetWeddingRankEntryResponse, error) {
    out := &channel_wedding_logic.GetWeddingRankEntryResponse{}

    if request.GetChannelId() == 0 {
        log.ErrorWithCtx(ctx, "GetWeddingRankEntry fail to GetWeddingRankEntry. req: %+v, err:%v", request, status.ErrRequestParamInvalid)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    svrResp, err := s.weddingCli.GetChannelWeddingRankEntryInfo(ctx, &channel_wedding.GetChannelWeddingRankEntryInfoReq{
        ChannelId: request.GetChannelId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingRankEntry fail to GetChannelWeddingRankEntryInfo. req: %+v, err:%v", request, err)
        return out, err
    }
    brideAccount := svrResp.GetRankInfo().GetBrideAccount()
    groomAccount := svrResp.GetRankInfo().GetGroomAccount()

    out.WeddingRankEntry = &channel_wedding_logic.WeddingRankEntry{
        ShowEntry: svrResp.GetIsShow(),
        H5Url:     svrResp.GetH5Url(),
    }

    if brideAccount == "" || groomAccount == "" && (svrResp.GetRankInfo().GetBrideUid() > 0) {
        userMap, err := s.userProfile.BatchGetUserProfileV2(ctx, []uint32{svrResp.GetRankInfo().GetBrideUid(), svrResp.GetRankInfo().GetGroomUid()}, false)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetWeddingRankEntry fail to BatchGetUserProfileV2. req: %+v, err:%v", request, err)
            return out, nil // 忽略错误
        }
        brideAccount = userMap[svrResp.GetRankInfo().GetBrideUid()].GetAccount()
        groomAccount = userMap[svrResp.GetRankInfo().GetGroomUid()].GetAccount()
    }

    out.WeddingRankEntry.BrideAccount = brideAccount
    out.WeddingRankEntry.GroomAccount = groomAccount

    return out, nil
}
