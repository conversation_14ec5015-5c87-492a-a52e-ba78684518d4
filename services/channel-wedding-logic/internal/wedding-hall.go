package internal

import (
    "context"
    "encoding/json"
    "fmt"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/pkg/ttversion"
    "golang.52tt.com/protocol/app"
    pb "golang.52tt.com/protocol/app/channel_wedding_logic"
    errCode "golang.52tt.com/protocol/common/status"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_minigame "golang.52tt.com/protocol/services/channel-wedding-minigame"
    channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
    "golang.52tt.com/services/channel-wedding-logic/internal/local_cache"
    "golang.52tt.com/services/tt-rev/esport/common/collection/transform"
    "golang.org/x/sync/errgroup"
    "math/rand"
    "sort"
    "time"
)

const (
    loadMoreBatSize = 10
)

var (
    version667 = ttversion.Parse("婚礼前置优化V4", "android-6.67.5", "ios-6.67.5", "pc-2.9.5")
    version668 = ttversion.Parse("婚礼前置优化6.68.5", "android-6.68.5", "ios-6.68.5", "pc-2.10.5")
)
func (s *Server) GetWeddingEntrySwitch(ctx context.Context, in *pb.GetWeddingEntrySwitchRequest) (out *pb.GetWeddingEntrySwitchResponse, err error) {
    out = &pb.GetWeddingEntrySwitchResponse{}
    defer func() {
        log.DebugWithCtx(ctx, "GetWeddingEntrySwitch in: %+v, out: %+v, err: %v", in, out, err)
    }()

    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        err = protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
        return
    }
    
    // 客态
    if in.GetTargetUid() != 0 && svrInfo.UserID != in.GetTargetUid() {
        showWeddingTab, _ := s.showWeddingTab(ctx, in.GetTargetUid())
        out.ShowWeddingTab = showWeddingTab
        return out, nil
    }
 
    
    conf := s.dyConfig.GetConfig().WeddingHallConf
    // 生成随机数
    interval := conf.WeddingHallReserveEntryHintRight - conf.WeddingHallReserveEntryHintLeft
    if interval == 0 {
        interval = 100
    }
    left := conf.WeddingHallReserveEntryHintLeft
    if left == 0 {
        left = 1
    }
    randIns := rand.New(rand.NewSource(time.Now().Unix() / 60))
    randNum := randIns.Intn(int(interval)) + int(left)

    out.WeddingHallEntryBackground = conf.WeddingHallEntryBackground
    out.WeddingHallEntryLottie = conf.WeddingHallEntryLottie
    out.WeddingHallEntryLottieMd5 = conf.WeddingHallEntryLottieMD5
    out.WeddingReserveEntryBackground = conf.WeddingReserveEntryBackground
    out.WeddingReserveEntryLottie = conf.WeddingReserveEntryLottie
    out.WeddingReserveEntryLottieMd5 = conf.WeddingReserveEntryLottieMd5
    out.WeddingHallBackground = conf.WeddingHallBackground
    out.WeddingHallReserveEntryBackground = conf.WeddingHallReserveEntryBackground
    out.WeddingHallReserveEntryHint = fmt.Sprintf("有%d对爱侣在结婚", randNum)
    // 婚礼优化v4版本资源
    log.DebugWithCtx(ctx, "GetWeddingEntrySwitch svrInfo: %+v", svrInfo)
    if version667.Atleast(svrInfo.ClientType, svrInfo.ClientVersion) {
        out.WeddingHallEntryBackground = conf.WeddingHallEntryBackgroundV4
        out.WeddingHallReserveEntryBackground = conf.WeddingHallReserveEntryBackgroundV4
        log.InfoWithCtx(ctx, "GetWeddingEntrySwitch, out: %+v", out)
    }
    if version668.Atleast(svrInfo.ClientType, svrInfo.ClientVersion) {
        out.WeddingHallReserveEntryBackground = conf.WeddingHallReserveEntryBackground668
    }

    /*
     * 开关可见逻辑：
     * 1. 低于版本一定不可见
     * 2. 若开启人群限制（即配置的人群列表不为空），且没命中人群，则不可见
     * 3. 以上都没被限制到的话，则按照开关配置返回
     */
    feature := ttversion.Parse("婚礼房开放版本", conf.ShowWeddingEffectVersionList...)
    if !feature.Atleast(svrInfo.ClientType, svrInfo.ClientVersion) {
        log.DebugWithCtx(ctx, "GetWeddingEntrySwitch version not match")
        return
    }
    if !s.isInGroup(ctx, svrInfo.UserID) {
        log.DebugWithCtx(ctx, "GetWeddingEntrySwitch group not match")
        return
    }
    
    showWeddingTab, _ := s.showWeddingTab(ctx, svrInfo.UserID)
    out.ShowWeddingTab = showWeddingTab
    out.ShowWeddingHallEntry = conf.ShowWeddingHallEntry
    out.ShowWeddingHallFloatingEntry = conf.ShowWeddingHallFloatingEntry

    return
}

func (s *Server) isInGroup(ctx context.Context, uid uint32) bool {
    groupList := s.dyConfig.GetConfig().WeddingHallConf.ShowWeddingGroupList
    if len(groupList) == 0 {
        return true
    }

    ctx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, 500*time.Millisecond)
    defer cancel()
    entityId := fmt.Sprintf("%d", uid)
    matchMap, err := s.lpmProxy.GetGroupMatchMap(ctx, []string{entityId}, groupList)
    if err != nil {
        log.ErrorWithCtx(ctx, "isInGroup err: %v", err)
        return false
    }
    if matchMap[entityId] {
        return true
    } else {
        return false
    }
}

func (s *Server) SubscribeWedding(ctx context.Context, in *pb.SubscribeWeddingRequest) (out *pb.SubscribeWeddingResponse, err error) {
    out = &pb.SubscribeWeddingResponse{}
    defer func() {
        log.InfoWithCtx(ctx, "SubscribeWedding in: %+v, out: %+v, err: %v", in, out, err)
    }()

    // 参数校验
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        err = protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
        return
    }

    batRsp, err := s.weddingPlanCli.BatGetWeddingInfoById(ctx, &channel_wedding_plan.BatGetWeddingInfoByIdRequest{WeddingPlanIdList: []uint32{in.WeddingPlanId}})
    if err != nil {
        log.ErrorWithCtx(ctx, "SubscribeWedding BatGetWeddingInfoById err: %v", err)
        return
    }
    weddingInfo := batRsp.GetWeddingInfoMap()[in.WeddingPlanId]
    if weddingInfo == nil || weddingInfo.Status == uint32(channel_wedding_plan.WeddingPlanStatus_WEDDING_PLAN_STATUS_CANCEL) || weddingInfo.GetReserveInfo().GetStartTs() == 0 {
        err = protocol.NewExactServerError(nil, errCode.ErrChannelWeddingHallStatusChanged, "预约观礼失败～")
        return
    }
    if weddingInfo.Status == uint32(channel_wedding_plan.WeddingPlanStatus_WEDDING_PLAN_STATUS_PLAYING) && weddingInfo.GetReserveInfo().GetStartTs() <= uint32(time.Now().Unix()) {
        err = protocol.NewExactServerError(nil, errCode.ErrChannelWeddingHallStatusChanged, "婚礼已开始～")
        return
    }
    if weddingInfo.Status == uint32(channel_wedding_plan.WeddingPlanStatus_WEDDING_PLAN_STATUS_FINISH) {
        err = protocol.NewExactServerError(nil, errCode.ErrChannelWeddingHallStatusChanged, "婚礼已结束～")
        return
    }

    _, err = s.weddingPlanCli.SubscribeWedding(ctx, &channel_wedding_plan.SubscribeWeddingRequest{
        Uid:           svrInfo.UserID,
        WeddingPlanId: in.WeddingPlanId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SubscribeWedding err: %v", err)
        return
    }

    return
}

type WeddingHallLoadMore struct {
    Index int `json:"index"`
}

func (s *Server) GetWeddingHallList(ctx context.Context, in *pb.GetWeddingHallListRequest) (out *pb.GetWeddingHallListResponse, err error) {
    out = &pb.GetWeddingHallListResponse{}
    defer func() {
        log.DebugWithCtx(ctx, "GetWeddingHallList in: %+v, out: %+v, err: %v", in, out, err)
    }()

    // 参数校验
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        err = protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
        return
    }
    if (in.TimeStatus != pb.WeddingTimeStatus_WEDDING_TIME_STATUS_GOING && in.TimeStatus != pb.WeddingTimeStatus_WEDDING_TIME_STATUS_COMING) ||
        (in.ChargeType != pb.WeddingChargeType_WEDDING_CHARGE_TYPE_PAID && in.ChargeType != pb.WeddingChargeType_WEDDING_CHARGE_TYPE_FREE && in.ChargeType != pb.WeddingChargeType_WEDDING_CHARGE_TYPE_MIX) {
        err = protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
        return
    }
    loadMore := &WeddingHallLoadMore{}
    if len(in.LoadMore) > 0 {
        err = json.Unmarshal([]byte(in.LoadMore), loadMore)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetWeddingHallList Unmarshal err: %v", err)
            err = protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
            return
        }
        if loadMore.Index < 0 {
            err = protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
            return
        }
    }

    for i := 0; i < 10; i++ {
        var weddingList []*pb.WeddingHallItem
        var nextLoadMore *WeddingHallLoadMore
        weddingList, nextLoadMore, err = s.getWeddingHallList(ctx, in, loadMore, svrInfo)
        if err != nil {
            return
        }

        if len(weddingList) == 0 && nextLoadMore != nil {
            loadMore = nextLoadMore
            continue
        }

        out.WeddingList = weddingList
        if nextLoadMore != nil {
            nextLoadMoreStr, _ := json.Marshal(nextLoadMore)
            out.LoadMore = string(nextLoadMoreStr)
        }
        break
    }

    return
}

func (s *Server) getWeddingHallList(ctx context.Context, in *pb.GetWeddingHallListRequest, loadMore *WeddingHallLoadMore,
    svrInfo *protogrpc.ServiceInfo) (weddingList []*pb.WeddingHallItem, nextLoadMore *WeddingHallLoadMore, err error) {
    // 获取推荐队列
    var cacheList []*local_cache.WeddingHallItemCache
    if in.TimeStatus & pb.WeddingTimeStatus_WEDDING_TIME_STATUS_GOING > 0 {
        if in.ChargeType == pb.WeddingChargeType_WEDDING_CHARGE_TYPE_PAID {
            cacheList = s.localCache.GetWeddingHallList(true, true)
            cacheList = reSortGoingPaidWeddingHallList(svrInfo.UserID, cacheList) // 将uid参与到排序中
        }
        if in.ChargeType == pb.WeddingChargeType_WEDDING_CHARGE_TYPE_FREE {
            cacheList = s.localCache.GetWeddingHallList(true, false)
            cacheList = filterNoHotValue(cacheList)                               // 过滤热度值为0的
            cacheList = reSortGoingFreeWeddingHallList(svrInfo.UserID, cacheList) // 将uid参与到排序中
        }
        if in.ChargeType == pb.WeddingChargeType_WEDDING_CHARGE_TYPE_MIX {
            paidCacheList := s.localCache.GetWeddingHallList(true, true)
            markItemChargeType(paidCacheList, uint32(pb.WeddingChargeType_WEDDING_CHARGE_TYPE_PAID))
            paidCacheList = reSortGoingPaidWeddingHallList(svrInfo.UserID, paidCacheList) // 将uid参与到排序中
            cacheList = append(cacheList, paidCacheList...)
            freeCacheList := s.localCache.GetWeddingHallList(true, false)
            markItemChargeType(freeCacheList, uint32(pb.WeddingChargeType_WEDDING_CHARGE_TYPE_FREE))
            freeCacheList = filterNoHotValue(freeCacheList)
            freeCacheList = reSortGoingFreeWeddingHallList(svrInfo.UserID, freeCacheList)
            cacheList = append(cacheList, freeCacheList...)
        }
    }
    if in.TimeStatus == pb.WeddingTimeStatus_WEDDING_TIME_STATUS_COMING {
        if in.ChargeType == pb.WeddingChargeType_WEDDING_CHARGE_TYPE_PAID {
            cacheList = s.localCache.GetWeddingHallList(false, true)
        }
        if in.ChargeType == pb.WeddingChargeType_WEDDING_CHARGE_TYPE_FREE {
            cacheList = s.localCache.GetWeddingHallList(false, false)
        }
        if in.ChargeType == pb.WeddingChargeType_WEDDING_CHARGE_TYPE_MIX {
            paidCacheList := s.localCache.GetWeddingHallList(false, true)
            markItemChargeType(paidCacheList, uint32(pb.WeddingChargeType_WEDDING_CHARGE_TYPE_PAID))
            cacheList = append(cacheList, paidCacheList...)
            freeCacheList := s.localCache.GetWeddingHallList(false, false)
            markItemChargeType(freeCacheList, uint32(pb.WeddingChargeType_WEDDING_CHARGE_TYPE_FREE))
            cacheList = append(cacheList, freeCacheList...)
        }
    }
    if len(cacheList) == 0 || loadMore.Index >= len(cacheList) {
        return
    }

    channelWhiteMap := map[uint32]struct{}{}
    for _, cid := range s.dyConfig.GetWeddingHallChannelWhiteList() {
        channelWhiteMap[cid] = struct{}{}
    }
    tmpCacheList := make([]*local_cache.WeddingHallItemCache, 0, len(cacheList))
    for _, item := range cacheList {
        if _, ok := channelWhiteMap[item.ChannelId]; ok {
            continue
        }
        tmpCacheList = append(tmpCacheList, item)
    }
    cacheList = tmpCacheList
    // 新版本才能看到空闲房间
    if !version668.Atleast(svrInfo.ClientType, svrInfo.ClientVersion) {
        tmpCacheList = make([]*local_cache.WeddingHallItemCache, 0, len(cacheList))
        for _, item := range cacheList {
            if item.IsIdle {
                continue
            }
            tmpCacheList = append(tmpCacheList, item)
        }
        cacheList = tmpCacheList
    }


    // 截取片段
    stopIndex := loadMore.Index + loadMoreBatSize
    if stopIndex >= len(cacheList) {
        stopIndex = len(cacheList)
    } else { // 说明还有数据未返回
        nextLoadMore = &WeddingHallLoadMore{Index: stopIndex}
    }
    curCacheList := cacheList[loadMore.Index:stopIndex]

    // 填充各种信息
    var chain FillFuncList
    if in.TimeStatus == pb.WeddingTimeStatus_WEDDING_TIME_STATUS_GOING {
        weddingList, err = s.getGoingWeddingListDetail(ctx, curCacheList)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetWeddingHallList getGoingWeddingListDetail err: %v", err)
            return
        }
        chain = FillFuncList{
            s.fillWeddingHallItemUserInfo,
            s.fillWeddingHallItemChannelHotValue,
            s.fillWeddingHallItemChannelName,
            s.fillWeddingHallItemChannelStageDesc,
        }
    }
    if in.TimeStatus == pb.WeddingTimeStatus_WEDDING_TIME_STATUS_COMING {
        weddingList, err = s.getComingWeddingListDetail(ctx, curCacheList)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetWeddingHallList getComingWeddingListDetail err: %v", err)
            return
        }
        chain = FillFuncList{
            s.fillWeddingHallItemUserInfo,
            s.fillWeddingHallItemSubscribeStatus,
            s.fillWeddingComingHallItemChannelName,
        }
    }
    err = chain.Do(ctx, svrInfo.UserID, weddingList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingHallList FillFuncList.Do err: %v", err)
        return
    }

    return
}

func markItemChargeType(cacheList []*local_cache.WeddingHallItemCache, chargeType uint32) {
    for _, hall := range cacheList {
        hall.ChargeType = chargeType
    }
}

func filterNoHotValue(hallList []*local_cache.WeddingHallItemCache) []*local_cache.WeddingHallItemCache {
    newHallList := make([]*local_cache.WeddingHallItemCache, 0, len(hallList))
    for _, hall := range hallList {
        if hall.ChannelHotValue > 0 {
            newHallList = append(newHallList, hall)
        }
    }
    return newHallList
}

func reSortGoingPaidWeddingHallList(uid uint32, hallList []*local_cache.WeddingHallItemCache) []*local_cache.WeddingHallItemCache {
    newHallList := make([]*local_cache.WeddingHallItemCache, len(hallList))
    copy(newHallList, hallList)

    sort.Slice(newHallList, func(i, j int) bool {
        a, b := newHallList[i], newHallList[j]
        aIsKey := a.BrideUid == uid || a.GroomUid == uid
        bIsKey := b.BrideUid == uid || b.GroomUid == uid
        _, aIsJoin := a.JoinUidSet[uid]
        _, bIsJoin := b.JoinUidSet[uid]

        if a.IsIdle != b.IsIdle {
            return !a.IsIdle
        }
        if a.IsHot != b.IsHot {
            return a.IsHot
        }
        if aIsKey != bIsKey {
            return aIsKey
        }
        if aIsJoin != bIsJoin {
            return aIsJoin
        }
        if a.NewcomersCnt != b.NewcomersCnt {
            return a.NewcomersCnt > b.NewcomersCnt
        }
        if a.ChannelHotValue != b.ChannelHotValue {
            return a.ChannelHotValue > b.ChannelHotValue
        }
        if a.ChannelWeddingLevel != b.ChannelWeddingLevel {
            return a.ChannelWeddingLevel > b.ChannelWeddingLevel
        }

        return a.WeddingPlanId < b.WeddingPlanId
    })

    return newHallList
}

func reSortGoingFreeWeddingHallList(uid uint32, hallList []*local_cache.WeddingHallItemCache) []*local_cache.WeddingHallItemCache {
    newHallList := make([]*local_cache.WeddingHallItemCache, len(hallList))
    copy(newHallList, hallList)

    sort.Slice(newHallList, func(i, j int) bool {
        a, b := newHallList[i], newHallList[j]
        aIsKey := a.BrideUid == uid || a.GroomUid == uid
        bIsKey := b.BrideUid == uid || b.GroomUid == uid
        _, aIsJoin := a.JoinUidSet[uid]
        _, bIsJoin := b.JoinUidSet[uid]

        if a.IsHot == b.IsHot {
            if aIsKey == bIsKey {
                if aIsJoin == bIsJoin {
                    return rand.Int31()%2 == 0
                }
                return aIsJoin
            }
            return aIsKey
        }
        return a.IsHot
    })

    return newHallList
}

func (s *Server) getGoingWeddingListDetail(ctx context.Context, cacheList []*local_cache.WeddingHallItemCache) ([]*pb.WeddingHallItem, error) {
    results := make([]*pb.WeddingHallItem, 0, len(cacheList))
    for _, item := range cacheList {
        if item.IsIdle {

            rsItem := &pb.WeddingHallItem{
                WeddingView: &pb.WeddingHallItem_GoingView{
                    GoingView: &pb.WeddingHallItem_WeddingHallItemGoingView{
                        ChannelId:        item.ChannelId,
                    },
                },
                ThemeName:         "等你们来牵手领证",
                BackgroundPicture: s.dyConfig.GetConfig().WeddingHallConf.WeddingHallIdlePayChannelBackgroundPicture,
                FramePicture:      s.dyConfig.GetConfig().WeddingHallConf.WeddingHallIdlePayChannelGoingItemFrame,
                IsIdle:            true,
            }
            results = append(results, rsItem)
            continue
        }

        theme := s.localCache.GetThemeCfg(item.ThemeId)
        if theme == nil {
            continue
        }

        var backgroundPicture string
        for _, cfg := range theme.GetThemeLevelCfgList() {
            if item.ChannelWeddingLevel >= cfg.GetLevel() {
                backgroundPicture = cfg.GetWeddingHallGoingBackground()
            }
        }

        rsItem := &pb.WeddingHallItem{
            WeddingPlanId: item.WeddingPlanId,
            WeddingView: &pb.WeddingHallItem_GoingView{
                GoingView: &pb.WeddingHallItem_WeddingHallItemGoingView{
                    ChannelId: item.ChannelId,
                },
            },
            MaleUserInfo:      &app.UserProfile{Uid: item.GroomUid},
            FemaleUserInfo:    &app.UserProfile{Uid: item.BrideUid},
            ThemeName:         fmt.Sprintf("%s婚礼", theme.GetThemeName()),
            BackgroundPicture: backgroundPicture,
            FramePicture:      s.dyConfig.GetConfig().WeddingHallConf.WeddingHallGoingItemFrame,
            ChargeType:        item.ChargeType,
        }
        if item.IsHot {
            rsItem.HotLabel = s.dyConfig.GetConfig().WeddingHallConf.WeddingHallHotLabelUrl
        }
        results = append(results, rsItem)
    }
    return results, nil
}

func (s *Server) getComingWeddingListDetail(ctx context.Context, cacheList []*local_cache.WeddingHallItemCache) ([]*pb.WeddingHallItem, error) {
    idList := transform.Map(cacheList, func(item *local_cache.WeddingHallItemCache) uint32 { return item.WeddingPlanId })
    batRsp, err := s.weddingPlanCli.BatGetWeddingInfoById(ctx, &channel_wedding_plan.BatGetWeddingInfoByIdRequest{WeddingPlanIdList: idList})
    if err != nil {
        log.ErrorWithCtx(ctx, "getComingWeddingListDetail BatGetWeddingInfoById err: %v", err)
        return nil, err
    }
    weddingMap := batRsp.GetWeddingInfoMap()

    results := make([]*pb.WeddingHallItem, 0, len(cacheList))
    nowTs := uint32(time.Now().Unix())
    for _, item := range cacheList {
        weddingInfo, ok := weddingMap[item.WeddingPlanId]
        if !ok {
            continue
        }
        if weddingInfo.GetReserveInfo().GetStartTs() < nowTs {
            continue
        }
        theme := s.localCache.GetThemeCfg(weddingInfo.GetThemeId())
        if theme == nil {
            continue
        }

        rsItem := &pb.WeddingHallItem{
            WeddingPlanId: item.WeddingPlanId,
            WeddingView: &pb.WeddingHallItem_NotStartView{
                NotStartView: &pb.WeddingHallItem_WeddingHallItemNotStartView{
                    ChannelId:        weddingInfo.GetReserveInfo().GetChannelId(),
                    StartTime:        time.Unix(int64(weddingInfo.GetReserveInfo().GetStartTs()), 0).Format("1月2日 15:04"),
                    WeddingStartTime: int64(weddingInfo.GetReserveInfo().GetStartTs()),
                },
            },
            MaleUserInfo:      &app.UserProfile{Uid: weddingInfo.GroomUid},
            FemaleUserInfo:    &app.UserProfile{Uid: weddingInfo.BrideUid},
            ThemeName:         fmt.Sprintf("%s婚礼", theme.GetThemeName()),
            BackgroundPicture: theme.GetWeddingHallComingBackground(),
            FramePicture:      s.dyConfig.GetConfig().WeddingHallConf.WeddingHallComingItemFrame,
            ChargeType:        item.ChargeType,
        }
        if item.IsHot {
            rsItem.HotLabel = s.dyConfig.GetConfig().WeddingHallConf.WeddingHallHotLabelUrl
        }
        results = append(results, rsItem)
    }
    return results, nil
}

func (s *Server) fillWeddingHallItemUserInfo(ctx context.Context, uid uint32, weddingList []*pb.WeddingHallItem) error {
    userMap, err := s.userProfile.BatchGetUserProfileV2(ctx, extractUidList(weddingList), false)
    if err != nil {
        log.ErrorWithCtx(ctx, "fillWeddingHallItemUserInfo BatchGetUserProfileV2 err: %v", err)
        return err
    }

    for _, item := range weddingList {
        if item.IsIdle {
            continue
        }
        if userInfo, ok := userMap[item.GetMaleUserInfo().Uid]; ok {
            item.MaleUserInfo = userInfo
        }
        if userInfo, ok := userMap[item.GetFemaleUserInfo().Uid]; ok {
            item.FemaleUserInfo = userInfo
        }
    }
    return nil
}

func (s *Server) fillWeddingHallItemChannelHotValue(ctx context.Context, uid uint32, weddingList []*pb.WeddingHallItem) error {
    hotValueMap, err := s.channelStatCli.BatchGetChannelHotValue(ctx, extractChannelList(weddingList))
    if err != nil {
        log.ErrorWithCtx(ctx, "fillWeddingHallItemChannelHotValue BatchGetChannelHotValue err: %v", err)
        return err
    }

    for _, item := range weddingList {
        if hotValue, ok := hotValueMap[item.GetGoingView().GetChannelId()]; ok {
            item.GetGoingView().ChannelHotValue = hotValue
        }
    }
    return nil
}

func (s *Server) fillWeddingHallItemChannelName(ctx context.Context, uid uint32, weddingList []*pb.WeddingHallItem) error {
    channelInfoMap, err := s.channelCli.BatchGetChannelSimpleInfo(ctx, uid, extractChannelList(weddingList))
    if err != nil {
        log.ErrorWithCtx(ctx, "fillWeddingHallItemChannelName BatchGetChannelSimpleInfo err: %v", err)
        return err
    }

    for _, item := range weddingList {
        if channelInfo, ok := channelInfoMap[item.GetGoingView().GetChannelId()]; ok {
            item.GetGoingView().ChannelName = channelInfo.GetName()
            item.GetGoingView().ChannelIcon = channelInfo.GetIconMd5()
        }
    }
    return nil
}

func (s *Server) fillWeddingComingHallItemChannelName(ctx context.Context, uid uint32, weddingList []*pb.WeddingHallItem) error {
    channelInfoMap, err := s.channelCli.BatchGetChannelSimpleInfo(ctx, uid, extractComingChannelList(weddingList))
    if err != nil {
        log.ErrorWithCtx(ctx, "fillWeddingHallItemChannelName BatchGetChannelSimpleInfo err: %v", err)
        return err
    }

    for _, item := range weddingList {
        if channelInfo, ok := channelInfoMap[item.GetNotStartView().GetChannelId()]; ok {
            item.GetNotStartView().ChannelName = channelInfo.GetName()
        }
    }
    return nil
}
func (s *Server) fillWeddingHallItemChannelStageDesc(ctx context.Context, uid uint32, weddingList []*pb.WeddingHallItem) error {
    channelList := extractChannelList(weddingList)

    weddingRsp, err := s.weddingCli.BatchGetChannelWeddingSimpleInfo(ctx, &channel_wedding.BatchGetChannelWeddingSimpleInfoReq{CidList: channelList})
    if err != nil {
        log.ErrorWithCtx(ctx, "fillWeddingHallItemChannelStageDesc BatchGetChannelWeddingSimpleInfo err: %v", err)
        return err
    }
    weddingInfoMap := make(map[uint32]*channel_wedding.SimpleWeddingInfo, len(channelList))
    for _, info := range weddingRsp.GetWeddingInfoList() {
        weddingInfoMap[info.GetCid()] = info
    }

    gameRsp, err := s.miniGameCli.BatGetIfChannelInChairGame(ctx, &channel_wedding_minigame.BatGetIfChannelInChairGameReq{ChannelIds: channelList})
    if err != nil {
        log.ErrorWithCtx(ctx, "fillWeddingHallItemChannelStageDesc BatGetIfChannelInChairGame err: %v", err)
        return err
    }
    inGameSet := make(map[uint32]bool, len(gameRsp.GetGamingCidList()))
    for _, cid := range gameRsp.GetGamingCidList() {
        inGameSet[cid] = true
    }

    for _, item := range weddingList {
        if item.IsIdle {
            sdLen := len(s.dyConfig.GetConfig().WeddingHallConf.WeddingHallIdlePayChannelStageDesc)
            randSd := s.dyConfig.GetConfig().WeddingHallConf.WeddingHallIdlePayChannelStageDesc[rand.Intn(sdLen)]
            item.GetGoingView().ChannelStageDesc = randSd
            continue
        }
        if item.IsIdle {
            continue
        }
        stageDesc := "婚礼中"
        if info, ok := weddingInfoMap[item.GetGoingView().GetChannelId()]; ok {
            switch info.GetCurrStage() {
            case uint32(channel_wedding.WeddingStage_WEDDING_STAGE_WELCOME_GUEST):
                stageDesc = "新人进场中"
            case uint32(channel_wedding.WeddingStage_WEDDING_STAGE_BRIDE_GROOM_ENTER):
                stageDesc = "迎接最美的新娘"
            case uint32(channel_wedding.WeddingStage_WEDDING_STAGE_LOVE_DECLARATION):
                stageDesc = "新人互诉衷肠"
            case uint32(channel_wedding.WeddingStage_WEDDING_STAGE_EXCHANGE_RING):
                stageDesc = "见证爱的仪式"
            case uint32(channel_wedding.WeddingStage_WEDDING_STAGE_HIGHLIGHT):
                stageDesc = "追忆爱的高光"
            case uint32(channel_wedding.WeddingStage_WEDDING_STAGE_GROUP_PHOTO):
                stageDesc = "留下最美的纪念"
            }
        }
        if inGameSet[item.GetGoingView().GetChannelId()] {
            stageDesc = "抢椅子赢奖励"
        }
        item.GetGoingView().ChannelStageDesc = stageDesc
    }
    return nil
}

func (s *Server) fillWeddingHallItemSubscribeStatus(ctx context.Context, uid uint32, weddingList []*pb.WeddingHallItem) error {
    resp, err := s.weddingPlanCli.BatGetWeddingSubscribeStatus(ctx, &channel_wedding_plan.BatGetWeddingSubscribeStatusRequest{
        Uid:               uid,
        WeddingPlanIdList: extractWeddingPlanList(weddingList),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "fillWeddingHallItemSubscribeStatus BatGetWeddingSubscribeStatus err: %v", err)
        return err
    }
    statusMap := resp.GetSubscribeStatusMap()

    for _, item := range weddingList {
        if statusMap[item.GetWeddingPlanId()] {
            item.GetNotStartView().SubscribeStatus = pb.WeddingSubscribeStatus_WEDDING_SUBSCRIBE_STATUS_SUBSCRIBED
        } else {
            item.GetNotStartView().SubscribeStatus = pb.WeddingSubscribeStatus_WEDDING_SUBSCRIBE_STATUS_NOT_SUBSCRIBED
        }
    }
    return nil
}

type FillFunc func(ctx context.Context, uid uint32, weddingList []*pb.WeddingHallItem) error
type FillFuncList []FillFunc

func (c FillFuncList) Do(oldCtx context.Context, uid uint32, weddingList []*pb.WeddingHallItem) error {
    if len(weddingList) == 0 {
        return nil
    }
    eg, ctx := errgroup.WithContext(oldCtx)
    for _, fillFunc := range c {
        this := fillFunc
        eg.Go(func() error {
            return this(ctx, uid, weddingList)
        })
    }
    return eg.Wait()
}

func extractUidList(weddingList []*pb.WeddingHallItem) []uint32 {
    uidList := make([]uint32, 0, len(weddingList)*2)
    for _, item := range weddingList {
        if item.IsIdle {
             continue
        }
        uidList = append(uidList, item.MaleUserInfo.Uid, item.FemaleUserInfo.Uid)
    }
    return uidList
}

func extractChannelList(weddingList []*pb.WeddingHallItem) []uint32 {
    channelList := make([]uint32, 0, len(weddingList))
    for _, item := range weddingList {
        channelList = append(channelList, item.GetGoingView().GetChannelId())
    }
    return channelList
}

func extractComingChannelList(weddingList []*pb.WeddingHallItem) []uint32 {
    channelList := make([]uint32, 0, len(weddingList))
    for _, item := range weddingList {
        channelList = append(channelList, item.GetNotStartView().GetChannelId())
    }
    return channelList
}

func extractWeddingPlanList(weddingList []*pb.WeddingHallItem) []uint32 {
    planList := make([]uint32, 0, len(weddingList))
    for _, item := range weddingList {
        planList = append(planList, item.GetWeddingPlanId())
    }
    return planList
}

func (s *Server)showWeddingTab(ctx context.Context, uid uint32) (bool, error) {
    // 获取用户婚礼关系信息
    marriageResp, err := s.weddingPlanCli.GetMarriageStatus(ctx, &channel_wedding_plan.GetMarriageStatusRequest{
        Uid: uid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "showWeddingTab fail to GetMarriageStatus. uid: %d, err:%v", uid, err)
        return false, err
    }
    
    if marriageResp.GetRelationInfo() == nil || // 没有婚姻关系
        marriageResp.GetRelationInfo().GetPartnerUid() == 0 {
        return false, nil
    }
    
    // 获取用户婚礼证书信息
    certificateResp, err := s.weddingCli.GetUserWeddingCertificate(ctx, &channel_wedding.GetUserWeddingCertificateReq{
        UidA:      marriageResp.GetRelationInfo().GetUid(),
        UidB:      marriageResp.GetRelationInfo().GetPartnerUid(),
        BeginTime: marriageResp.GetRelationInfo().GetRelationCtime(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "showWeddingTab fail to GetUserWeddingCertificate. uid: %d, err:%v", uid, err)
        return false, err
    }
    
    certificate := certificateResp.GetWeddingCertificate()
    if certificate.GetWeddingId() == 0 {
        return false, nil
    }
    log.InfoWithCtx(ctx, "showWeddingTab uid: %d, certificate: %v", uid, certificate)
    return true, nil
}