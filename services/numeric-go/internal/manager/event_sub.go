package manager

import (
	"context"
	"fmt"
	"time"

	"golang.52tt.com/services/numeric-go/internal/common"

	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkatbean"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	numericlogic "golang.52tt.com/protocol/app/numeric-logic"
	kfk_esport_trade "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka-esport-trade"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_knight_event"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	pb "golang.52tt.com/protocol/services/numeric-go"
	userpresent "golang.52tt.com/protocol/services/userpresent-go"
)

// PresentEventHandle T豆送礼事件
func (m *Manager) PresentEventHandle(ctx context.Context, e *kafkapresent.PresentEvent) error {
	if m.configCenter.DisableSubscribe() {
		log.WarnWithCtx(ctx, "PresentEventHandle DisableSubscribe order_id:%s", e.GetOrderId())
		return nil
	}

	// only t bean
	if e.GetPriceType() != uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN) {
		return nil
	}

	// order lock, from c++
	lock, err := m.cacheClient.IncrOrderID(ctx, e.GetOrderId())
	if err != nil {
		log.ErrorWithCtx(ctx, "PresentEventHandle IncrOrderID err:%s, order_id:%s", err, e.GetOrderId())
		return err
	}
	if lock > 1 {
		log.WarnWithCtx(ctx, "PresentEventHandle IncrOrderID repeat, order_id:%s", e.GetOrderId())
		return nil
	}

	gn, err := m.GeneralNumericHandle(ctx, common.GeneralNumeric{
		SourceType:      pb.SourceT_SourceTPresentTBean,
		Sender:          e.GetUid(),
		Receiver:        e.GetTargetUid(),
		TotalAmount:     uint64(e.GetPrice() * e.GetItemCount()),
		AddRich:         uint64(e.GetAddRich()),
		AddCharm:        uint64(e.GetAddCharm()),
		GiftId:          e.GetItemId(),
		OrderId:         e.GetOrderId(),
		ChannelId:       e.GetChannelId(),
		ChannelGuildId:  e.GetGuildId(),
		ReceiverGuildId: e.GetReceiverGuildId(),
		UserGuildId:     e.GetGiverGuildId(),
		SendTime:        time.Unix(int64(e.GetSendTime()), 0),
		PriceType:       e.GetPriceType(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PresentEventHandle GeneralNumericHandle err:%s, order_id:%s", err, e.GetOrderId())
		return err
	}
	if gn == nil {
		log.WarnWithCtx(ctx, "PresentEventHandle NotHandle order_id:%s", e.GetOrderId())
		return nil
	}

	// push 房间送礼触发的升级推送
	m.broadcastUserRichOrCharmLevelUpdate(ctx, gn)
	return nil
}

// ESportEventHandle 电竞结算事件
func (m *Manager) ESportEventHandle(ctx context.Context, e *kfk_esport_trade.EsportTradeSettleEvent) error {
	// 2024.11.08 需求 电竞业务不增加贵族值/财富值/魅力值
	if true {
		return nil
	}
	playerPrice := e.GetTotalPrice()
	coachPrice := e.GetCoachTotalPrice()
	gn, err := m.GeneralNumericHandle(ctx, common.GeneralNumeric{
		SourceType:  pb.SourceT_SourceTESport,
		Sender:      e.GetPlayerUid(),
		Receiver:    e.GetCoachUid(),
		TotalAmount: uint64(coachPrice),
		AddRich:     uint64(playerPrice),
		AddCharm:    uint64(coachPrice),
		OrderId:     e.GetOrderId(),
		UserGuildId: e.GetSignGuildId(),
		SendTime:    time.Unix(e.GetSettleTime(), 0),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ESportEventHandle GeneralNumericHandle err:%s, order_id:%s", err, e.GetOrderId())
		return err
	}
	log.InfoWithCtx(ctx, "ESportEventHandle GeneralNumericHandle success, order_id:%s, gn:%+v", e.GetOrderId(), gn)
	return nil
}

// KnightEventHandle 加入骑士团事件
func (m *Manager) KnightEventHandle(ctx context.Context, e *kafka_knight_event.JoinKnightGroupEvent) error {
	if m.configCenter.DisableSubscribe() {
		log.WarnWithCtx(ctx, "KnightEventHandle DisableSubscribe order_id:%s", e.GetOrderId())
		return nil
	}

	// order lock, from c++
	lock, err := m.cacheClient.IncrOrderID(ctx, e.GetOrderId())
	if err != nil {
		log.ErrorWithCtx(ctx, "KnightEventHandle IncrOrderID err:%s, order_id:%s", err, e.GetOrderId())
		return err
	}
	if lock > 1 {
		log.WarnWithCtx(ctx, "KnightEventHandle IncrOrderID repeat, order_id:%s", e.GetOrderId())
		return nil
	}

	// 检查加速卡
	accelerateCardMap, err := m.checkAccelerateCard(ctx, e.GetKnightUid(), e.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "KnightEventHandle checkAccelerateCard err:%s, order_id:%s", err, e.GetOrderId())
		return err
	}
	originAddRich := uint64(e.GetPrice())
	acceleratorRateRich := 1.0
	addRich := originAddRich
	if times, ok := accelerateCardMap[e.GetKnightUid()]; ok {
		acceleratorRateRich = times
		addRich = uint64(float64(addRich) * times)
	}
	addCharm := originAddRich
	if times, ok := accelerateCardMap[e.GetAnchorUid()]; ok {
		addCharm = uint64(float64(addCharm) * times)
	}

	gn, err := m.GeneralNumericHandle(ctx, common.GeneralNumeric{
		SourceType:  pb.SourceT_SourceTKnight,
		Sender:      e.GetKnightUid(),
		Receiver:    e.GetAnchorUid(),
		TotalAmount: uint64(e.GetPrice()),
		AddRich:     addRich,
		AddCharm:    addCharm,
		OrderId:     e.GetOrderId(),
		SendTime:    time.Unix(int64(e.GetCreateTime()), 0),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "KnightEventHandle GeneralNumericHandle err:%s, order_id:%s", err, e.GetOrderId())
		return err
	}
	log.InfoWithCtx(ctx, "KnightEventHandle GeneralNumericHandle success, order_id:%s, gn:%+v", e.GetOrderId(), gn)

	// push 公屏消息
	pushMsg := &numericlogic.RichCharmChangeMsg{
		FromType:              numericlogic.FromType_KnightChange,
		ChangeType:            numericlogic.ChangeType_Rich,
		Value:                 uint32(gn.AddRich),
		Uid:                   e.GetKnightUid(),
		OriginValue:           uint32(originAddRich),
		FromDesc:              "我 加入骑士团",
		HasAcceleratorCard:    acceleratorRateRich > 1,
		AcceleratorRate:       float32(acceleratorRateRich),
		AcceleratorExtraValue: uint32(gn.AddRich - originAddRich),
	}
	log.InfoWithCtx(ctx, "KnightEventHandle RichCharmChangeMsg channel_id:%d, pushMsg:%+v", e.GetChannelId(), pushMsg)
	pushMsgBytes, _ := proto.Marshal(pushMsg)
	_ = m.SendChannelReliable1v1Msg(ctx, e.GetChannelId(), e.GetKnightUid(), pushMsgBytes)

	// push 升级推送
	m.broadcastUserRichOrCharmLevelUpdate(ctx, gn)
	return nil
}

// TBeanEventHandle T豆送礼事件
func (m *Manager) TBeanEventHandle(ctx context.Context, e *kafkatbean.TBeanConsumeEvent) error {
	if m.configCenter.DisableSubscribe() {
		log.WarnWithCtx(ctx, "TBeanEventHandle DisableSubscribe order_id:%s", e.GetOrderId())
		return nil
	}

	// 参数校验
	if e.GetUid() == 0 || e.GetValue() == 0 {
		log.ErrorWithCtx(ctx, "TBeanEventHandle missing parameter, event:%+v", e)
		return fmt.Errorf("missing parameter")
	}

	// 订单锁，防止重复消费
	lock, err := m.cacheClient.IncrOrderID(ctx, e.GetOrderId())
	if err != nil {
		log.ErrorWithCtx(ctx, "TBeanEventHandle IncrOrderID err:%s, order_id:%s", err, e.GetOrderId())
		return err
	}
	if lock > 1 {
		log.WarnWithCtx(ctx, "TBeanEventHandle IncrOrderID repeat, order_id:%s", e.GetOrderId())
		return nil
	}

	var fromType numericlogic.FromType
	switch kafkatbean.TBeanConsumeSourceType(e.GetSourceType()) {
	case kafkatbean.TBeanConsumeSourceType_ENUM_BUY_YKW_TYPE:
		fromType = numericlogic.FromType_YouKnownWhoChange
	case kafkatbean.TBeanConsumeSourceType_ENUM_WEREWOLF_BUY_IDENTITY,
		kafkatbean.TBeanConsumeSourceType_ENUM_WEREWOLF_BUY_TIME:
		fromType = numericlogic.FromType_WolfChange
	case kafkatbean.TBeanConsumeSourceType_ENUM_TREASURE_HOUSE_BUY:
		fromType = numericlogic.FromType_BuyTreasurePrivilege
	case kafkatbean.TBeanConsumeSourceType_ENUM_AI_PRESENT:
		fromType = numericlogic.FromType_AiPresentChange
	default:
		log.InfoWithCtx(ctx, "TBeanEventHandle skip event, unknown source_type:%d", e.GetSourceType())
		return nil
	}

	sendUID := e.GetUid()
	channelID := e.GetChannelId()
	orderID := e.GetOrderId()
	addRich := uint64(e.GetValue())
	var addCharm uint64
	var targetUID uint32
	var channelGuildID uint32

	channelResp, err := m.channelCli.GetChannelSimpleInfo(ctx, e.GetUid(), channelID)
	if err != nil {
		log.ErrorWithCtx(ctx, "TBeanEventHandle GetChannelSimpleInfo err:%s, channel_id:%d", err, channelID)
	} else {
		channelGuildID = channelResp.GetBindId()
	}

	// 获取主持人
	if fromType == numericlogic.FromType_WolfChange {
		gameResp, err := m.channelOpenGameController.GetChannelGameHost(ctx, channelID)
		if err != nil {
			log.ErrorWithCtx(ctx, "TBeanEventHandle GetChannelGameHost err:%s, channel_id:%d", err, channelID)
		} else {
			targetUID = gameResp.GetUid()
			addCharm = uint64(e.GetValue())
		}
	}

	// 检查加速卡
	originAddRich := addRich
	acceleratorRateRich := 1.0

	// react 加速卡
	accelerateCardMap, err := m.checkAccelerateCard(ctx, sendUID, targetUID)
	if err != nil {
		log.ErrorWithCtx(ctx, "TBeanEventHandle checkAccelerateCard err:%s, uid:%d", err, sendUID)
	}
	if times, ok := accelerateCardMap[sendUID]; ok {
		acceleratorRateRich = times
		addRich = uint64(float64(addRich) * times)
	}
	if times, ok := accelerateCardMap[targetUID]; ok {
		addCharm = uint64(float64(addCharm) * times)
	}

	// 核心处理
	gn, err := m.GeneralNumericHandle(ctx, common.GeneralNumeric{
		SourceType:     pb.SourceT_SourceTBean,
		Sender:         sendUID,
		Receiver:       targetUID,
		TotalAmount:    uint64(e.GetValue()),
		AddRich:        addRich,
		AddCharm:       addCharm,
		OrderId:        orderID,
		ChannelId:      channelID,
		ChannelGuildId: channelGuildID,
		SendTime:       time.Unix(int64(e.GetTimestamp()), 0),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "TBeanEventHandle GeneralNumericHandle err:%s, order_id:%s", err, orderID)
		return err
	}
	if gn == nil {
		log.WarnWithCtx(ctx, "TBeanEventHandle NotHandle order_id:%s", orderID)
		return nil
	}

	// 我的公屏消息
	fromDesc := ""
	if fromType == numericlogic.FromType_WolfChange {
		switch e.GetSourceType() {
		case uint32(kafkatbean.TBeanConsumeSourceType_ENUM_WEREWOLF_BUY_IDENTITY):
			fromDesc = "我 在狼人杀游戏里抢中身份"
		case uint32(kafkatbean.TBeanConsumeSourceType_ENUM_WEREWOLF_BUY_TIME):
			fromDesc = "我 在狼人杀游戏里延长时间"
		default:
		}
	}

	// 开通神秘人时使用加速卡才展示toast
	if fromType == numericlogic.FromType_YouKnownWhoChange && acceleratorRateRich > 1 && gn.AddRich > 0 {
		msg := fmt.Sprintf("正在使用%.1f倍的财富加速卡，开通神秘人增加财富值%d", acceleratorRateRich, gn.AddRich)
		_ = m.PushToast(ctx, sendUID, msg)
	}

	// 不在房间不推送
	if channelID == 0 {
		return nil
	}

	// push 公屏消息
	pushMsg := &numericlogic.RichCharmChangeMsg{
		FromType:              fromType,
		ChangeType:            numericlogic.ChangeType_Rich,
		Value:                 uint32(gn.AddRich),
		Uid:                   sendUID,
		OriginValue:           uint32(originAddRich),
		FromDesc:              fromDesc,
		HasAcceleratorCard:    acceleratorRateRich > 1,
		AcceleratorRate:       float32(acceleratorRateRich),
		AcceleratorExtraValue: uint32(gn.AddRich - originAddRich),
	}
	log.InfoWithCtx(ctx, "TBeanEventHandle RichCharmChangeMsg channel_id:%d, pushMsg:%+v", channelID, pushMsg)
	pushMsgBytes, _ := proto.Marshal(pushMsg)
	_ = m.SendChannelReliable1v1Msg(ctx, channelID, sendUID, pushMsgBytes)

	// push 升级推送
	m.broadcastUserRichOrCharmLevelUpdate(ctx, gn)

	return nil
}
