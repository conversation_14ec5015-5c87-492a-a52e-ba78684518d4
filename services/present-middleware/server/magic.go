package server

import (
	"context"
	"encoding/json"
	"fmt"
	account "golang.52tt.com/clients/account-go"
	ga "golang.52tt.com/protocol/app"
	backpack_base "golang.52tt.com/protocol/services/backpack-base"
	userpresent "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/present-middleware/conf"
	"sort"
	"sync"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelPB_ "golang.52tt.com/protocol/app/channel"
	magic_spirit_logic "golang.52tt.com/protocol/app/magic-spirit-logic"
	pushPb "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/common/status"
	accountPB "golang.52tt.com/protocol/services/account-go"
	backpackPB "golang.52tt.com/protocol/services/backpacksvr"
	channelPB "golang.52tt.com/protocol/services/channelsvr"
	pb "golang.52tt.com/protocol/services/present-middleware"
	publicNoticePb "golang.52tt.com/protocol/services/public-notice"
	presentPB "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/present-middleware/rpc/client"
)

func (s *MagicPresentMiddlewareMgr) BeforeSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {
	// 类型断言
	_, ok := outInter.(*pb.MagicSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.MagicSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- para inter need *PresentSendBase type. ")
		return nil
	}

	if in.ServiceInfo == nil {
		in.ServiceInfo = &pb.ServiceCtrlInfo{}
	}
	sendExtend.nowTs = time.Unix(int64(in.SendTime), 0)

	ctx = s.SetPresentGoCtx(ctx, in.GetSendUid())

	targetUidList := make([]uint32, 0)
	itemList := make([]uint32, 0)
	targetUidMap := make(map[uint32]uint32, 0)
	itemMap := make(map[uint32]uint32, 0)

	for _, item := range in.GetGiftItemList() {
		if _, ok := targetUidMap[item.GetUid()]; !ok {
			targetUidList = append(targetUidList, item.GetUid())
			targetUidMap[item.Uid] = 1
		}

		if _, ok := itemMap[item.GetItemId()]; !ok {
			itemList = append(itemList, item.GetItemId())
			itemMap[item.GetItemId()] = 1
		}
	}
	sendExtend.uniqOrderId = in.ConsumeOrderId

	// 先检查是不是需要ban掉的礼物id
	if s.GeneralConfig.GetPresentConfig().BanItemMap != nil {
		for _, item := range itemList {
			if source, ok := s.GeneralConfig.GetPresentConfig().BanItemMap[item]; ok {
				if source == conf.BanItemTypeMagic || source == conf.BanItemTypeAll {
					return protocol.NewExactServerError(nil, status.ErrUserPresentConfigNotExist)
				}
			}
		}
	}

	// 预处理，获得送礼要用的相关信息
	err = s.preSendPresent(ctx, in.SendUid, targetUidList, itemList, in.ChannelId, sendExtend)
	if err != nil {
		log.ErrorWithCtx(ctx, "PresentMiddlewareMgr -- preSendPresent fail. uid:%v c:%v err:%v",
			in.SendUid, in.ChannelId, err)
		return err
	}

	// 计算总价值
	for _, item := range in.GetGiftItemList() {
		if config, ok := sendExtend.presentConfigMap[item.GetItemId()]; ok {
			sendExtend.totalPrice += config.GetPrice() * item.Count
		}
	}

	//if sendExtend.totalPrice >= 2000000 {
	//	return protocol.NewExactServerError(nil,status.ErrUserPresentSpendOverLimit)
	//}
	return err
}

func (s *MagicPresentMiddlewareMgr) preSendPresent(ctx context.Context, sendUid uint32, targetUidList []uint32, itemIdList []uint32, channelId uint32, sendExtend *baseSendExtend) (
	err error) {

	sendExtend.presentConfigMap = make(map[uint32]*presentPB.StPresentItemConfig)

	// 如果是单送，要区分下是不是旧的神秘人，防止从资料卡送礼暴露神秘人身份
	_, err = checkOldFakeUid(ctx, targetUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "preSendPresent checkOldFakeUid err : %v, uid:%d", err, sendUid)
		return err
	}

	// 首先要对targetUidList做一次转换
	fake2RealUidMap, real2FakeUidMap, realUidList, err := getRealUidByFakeUid(ctx, append(targetUidList, sendUid))
	if err != nil {
		log.ErrorWithCtx(ctx, "preSendPresent getRealUidByFakeUid err : %v, uid:%d", err, sendUid)
		return err
	}
	sendExtend.fake2RealUidMap = fake2RealUidMap
	sendExtend.real2FakeUidMap = real2FakeUidMap

	wg := sync.WaitGroup{}

	wg.Add(3)

	//用户信息
	go func() {
		defer wg.Done()

		var sErr error
		sendExtend.targetUserMap, sErr = s.accountCli.GetUsersMap(ctx, realUidList)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "preSendPresent GetUsersMap err : %v, uid:%d", sErr, sendUid)
			err = sErr
		}

	}()

	ukwInfoMap := make(map[uint32]*ga.UserProfile)
	//用户神秘人信息
	go func() {
		defer wg.Done()

		var sErr error
		ukwInfoMap, sErr = client.UserProfileCli.BatchGetUserProfileV2(ctx, realUidList, true)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "preSendPresent GetUsersMap err : %v, uid:%d", sErr, sendUid)
			err = sErr
		}

	}()

	//礼物信息
	go func() {
		defer wg.Done()

		var sErr error
		resp, sErr := client.PresentCli.GetPresentConfigByIdList(ctx, sendUid, itemIdList, 0)
		if sErr == nil {
			for _, item := range resp.GetItemList() {
				sendExtend.presentConfigMap[item.ItemId] = item
			}
		}

		if sErr != nil {
			log.ErrorWithCtx(ctx, "preSendPresent GetPresentConfigByIdList err : %v, uid:%d", sErr, sendUid)
			err = sErr
		}
	}()

	if channelId != 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()

			channelInfo, sErr := s.channelCli.GetChannelSimpleInfo(ctx, sendUid, channelId)
			sendExtend.channelSimpleInfo = &channelPB.GetChannelSimpleInfoResp{ChannelSimple: channelInfo}
			if sErr != nil {
				log.ErrorWithCtx(ctx, "preSendPresent GetChannelSimpleInfo err : %v, uid:%d", sErr, sendUid)
				err = sErr
			}
		}()
	} else {
		sendExtend.channelSimpleInfo = &channelPB.GetChannelSimpleInfoResp{ChannelSimple: &channelPB.ChannelSimpleInfo{}}
	}

	wg.Wait()

	if err != nil {
		return err
	}

	if isPgc(sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType()) {
		sendExtend.ukwInfoMap = ukwInfoMap
	} else {
		sendExtend.ukwInfoMap = FillUgcUserProfile(sendExtend.targetUserMap)
	}

	sendExtend.sendUser = sendExtend.targetUserMap[sendUid]
	delete(sendExtend.targetUserMap, sendUid)

	// 用userprofile更新下map
	for uid, item := range sendExtend.ukwInfoMap {
		if uid != item.GetUid() {
			sendExtend.fake2RealUidMap[item.GetUid()] = uid
			sendExtend.real2FakeUidMap[uid] = item.GetUid()
		}
	}

	// scoreType计算
	sendExtend.ScoreTypeMap = make(map[uint32]uint32)
	// t豆礼物，且时间晚于积分检测的开始时间
	if sendExtend.presentConfigMap[itemIdList[0]].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) &&
		s.GeneralConfig.GetPresentConfig().ScoreCheckBegin < time.Now().Unix() {
		sendExtend.ScoreTypeMap, sendExtend.IdentityTypeMap, err = s.ScoreTypeMgr.GetScoreTypeMap(ctx, itemIdList[0], sendUid, realUidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "preSendPresent - FillScoreType fail , uid: %+v", sendUid)
			return err
		}
		log.DebugWithCtx(ctx, "GetScoreTypeMap %v", sendExtend.ScoreTypeMap)
	} else {
		sendExtend.ScoreTypeMap = make(map[uint32]uint32)
		for _, item := range realUidList {
			sendExtend.ScoreTypeMap[item] = 1
		}
	}

	return
}

func (s *MagicPresentMiddlewareMgr) SendingPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {

	// 类型断言
	_, ok := outInter.(*pb.MagicSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "SendingPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.MagicSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "SendingPresent -- para inter need *SendPresentReq type. ")
		return nil
	}

	sendExtend.extendInfo = &ExtendInfo{targetInfoMap: map[uint32]*TargetExtendInfo{}}
	sendExtend.extendInfo.multiTargetInfoMap = make(map[uint32]map[uint32]*TargetExtendInfo)

	// 送礼人是否记录财富值
	ctx = context.WithValue(ctx, IsRecordSenderRichKey, s.isRecordRichMap(ctx, sendExtend.sendUser.GetUid(), sendExtend.targetUserMap))

	//赠送礼物
	//  优化耗时 0.3s
	sendExtend.sucUsers, err = s.sendPresent(ctx, sendExtend.sendUser, in.GetGiftItemList(), sendExtend, sendExtend.extendInfo, uint32(sendExtend.nowTs.Unix()),
		in.AppId, in.MarketId, in.GetServiceInfo().GetClientIp(), in.GetServiceInfo().GetClientType(), in.GetBindChannelId(), in.GetIsBatch())
	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- __SendPresent fail. uid:%v ret:%v req:%v",
			in.SendUid, err, in)
		return err
	}
	log.ErrorWithCtx(ctx, "after send present extend info : %v", sendExtend.extendInfo)

	//礼物周边信息处理

	itemCountMap := make(map[uint32]uint32, 0)
	for _, item := range in.GiftItemList {
		itemCountMap[item.ItemId] += item.Count
	}

	//  优化耗时 0.3s
	err = s.ProcPresentWatchWithList(ctx, sendExtend, itemCountMap)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- __ProcPresentWatch fail:%v. _req:%v", err, in)
	}

	//提交支付订单

	//err = s.commitPayOrder(ctx, sendExtend.sendUser.GetUid(), sendExtend.orderMap, sendExtend.sucUsers)

	return err
}

func (s *MagicPresentMiddlewareMgr) AfterSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {

	// 类型断言
	out, ok := outInter.(*pb.MagicSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.MagicSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- para inter need *SendPresentReq type. ")
		return nil
	}

	//【防小白骚扰】24小时内单笔送出/收到1000元礼物的用户 存redis
	consumeMap := make(map[uint32]uint32, 0)
	for _, item := range in.GetGiftItemList() {
		consumeMap[item.Uid] = sendExtend.presentConfigMap[item.ItemId].GetPrice()
	}

	err = s.SetHighConsume(ctx, consumeMap, in.SendUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- SetHighConsume err, uid %d ,  err : %v. ", in.SendUid, err)
		return nil
	}

	//送礼后处理，填充动效、推送等
	s.FillRespAndPush(ctx, sendExtend, in, out)

	return err
}

//func (s *MagicPresentMiddlewareMgr) getStrMicroTime(time time.Time) string {
//	return fmt.Sprintf("batch_%s_%d", time.Format("20060102150405"), (time.UnixNano()/1000)%1000000)
//}

func (s *MagicPresentMiddlewareMgr) procPushEvent(ctx context.Context, sendExtend *baseSendExtend, in *pb.MagicSendPresentReq, out *pb.MagicSendPresentResp, itemMap map[uint32]*pb.MagicSpiritSendItem, comboInfo *magic_spirit_logic.CombInfo) {

	sendUser := sendExtend.sendUser

	tmpGaPushBatchInfo := magic_spirit_logic.SendMagicSpiritOpt{ItemList: []*magic_spirit_logic.MagicSpiritSendItem{}}

	tmpGaPushBatchInfo.MagicSpiritId = in.GetMagicSpiritId()
	tmpGaPushBatchInfo.MagicSpiritCnt = in.GetMagicSpiritCnt()
	tmpGaPushBatchInfo.ChannelId = in.GetChannelId()
	tmpGaPushBatchInfo.IsBatch = in.GetIsBatch()
	tmpGaPushBatchInfo.SendUid = sendUser.GetUid()
	tmpGaPushBatchInfo.SendAccount = sendUser.GetUsername()
	tmpGaPushBatchInfo.SendNickname = sendUser.GetNickname()
	tmpGaPushBatchInfo.TotalItemPrice = sendExtend.totalPrice
	tmpGaPushBatchInfo.SysAutoSend = in.GetSysAutoSend()
	tmpGaPushBatchInfo.IsMulti = in.GetIsMulti()

	fillSendMagicUserProfile(&tmpGaPushBatchInfo, sendExtend)

	newComboInfo := *comboInfo
	nickName := sendUser.GetNickname()
	if sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege() != nil {
		nickName = sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege().GetNickname()
	}
	if comboInfo.GetLevelUp() && comboInfo.GetCombLevel() >= uint32(magic_spirit_logic.CombLevelType_COMB_LEVEL_LV2) {
		newComboInfo.ComboText = fmt.Sprintf("%s 连送触发了 %s", nickName, comboInfo.GetLevelName())
	}

	tmpGaPushBatchInfo.CombInfo = &newComboInfo

	for _, target := range out.SpiritOpt.ItemList {

		extendJson, _ := json.Marshal(sendExtend.extendInfo.sendInfoMap[target.ItemId])

		awardText := ""
		if target.GetEffect() >= uint32(magic_spirit_logic.MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_LV3) {
			nickName := sendUser.GetNickname()
			if sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege() != nil {
				nickName = sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege().GetNickname()
			}

			if len([]rune(nickName)) > 5 {
				nickName = string([]rune(nickName)[:5]) + "..."
			}
			awardText = fmt.Sprintf("超幸运！%s 送出了超值稀缺礼物 %s", nickName, sendExtend.presentConfigMap[target.GetItemId()].GetName())
		}

		tmpItem := &magic_spirit_logic.MagicSpiritSendItem{
			ItemId:            target.GetItemId(),
			ShowEffect:        target.GetShowEffect(),
			ShowEffectV2:      target.GetShowEffectV2(),
			Effect:            target.GetEffect(),
			FlowId:            target.GetFlowId(),
			IsBatch:           target.GetIsBatch(),
			ShowBatchEffect:   target.GetShowBatchEffect(),
			SendType:          target.GetSendType(),
			DynamicTemplateId: target.GetDynamicTemplateId(),
			AwardText:         awardText,
			ExternInfo:        string(extendJson),
		}
		fmt.Printf("%s", tmpItem.AwardText)
		tmpItem.AwardList = make([]*magic_spirit_logic.MagicSpiritAwardInfo, 0)

		for _, item := range target.AwardList {
			tmpExtendJson := "{}"
			if sendExtend.extendInfo.multiTargetInfoMap[item.TargetUid] != nil && sendExtend.extendInfo.multiTargetInfoMap[item.TargetUid][item.GetItemId()] != nil {
				tmpExtendJson = sendExtend.extendInfo.multiTargetInfoMap[item.TargetUid][item.GetItemId()].userExtendJson
			}

			msg := &magic_spirit_logic.MagicSpiritAwardInfo{
				ItemId:         item.GetItemId(),
				TargetUid:      item.GetTargetUid(),
				TargetNickname: item.GetTargetNickname(),
				TargetAccount:  item.GetTargetAccount(),
				Count:          item.GetCount(),
				SendTime:       item.GetSendTime(),
				ChannelId:      item.GetChannelId(),
				ItemOrderId:    item.GetItemOrderId(),
				ExtendJson:     tmpExtendJson,
			}
			fillMagicUserProfile(msg, sendExtend)
			tmpItem.AwardList = append(tmpItem.AwardList, msg)

		}

		tmpGaPushBatchInfo.ItemList = append(tmpGaPushBatchInfo.ItemList, tmpItem)

	}

	err := s.PushMagicInfoMsgToChannel(ctx, sendExtend, &tmpGaPushBatchInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushMagicInfoMsgToChannel -- PushMulticast. uid:%v, channel_id:%v", sendUser.GetUid(),
			sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId())
	}
	sendExtend.timeCollect.AddTimeEvent("PushMagicInfoMsgToChannel")

	wg := sync.WaitGroup{}

	for _, item := range in.GetGiftItemList() {
		//messageItemInfo := &presentPB_.PresentSendItemInfo{
		//	ItemId:            itemMap[item.GetItemId()].GetItemId(),
		//	ShowEffect:        itemMap[item.GetItemId()].GetShowEffect(),
		//	ShowEffectV2:      itemMap[item.GetItemId()].GetShowEffectV2(),
		//	FlowId:            itemMap[item.GetItemId()].GetFlowId(),
		//	IsBatch:           itemMap[item.GetItemId()].GetIsBatch(),
		//	ShowBatchEffect:   itemMap[item.GetItemId()].GetShowBatchEffect(),
		//	SendType:          itemMap[item.GetItemId()].GetSendType(),
		//	DynamicTemplateId: itemMap[item.GetItemId()].GetDynamicTemplateId(),
		//}

		tmpItem := item

		itemConfig := sendExtend.presentConfigMap[item.GetItemId()]

		// 暂时不推个推
		//jsonTargetUser := jsonTargetUser{int32(itemConfig.GetScore() * item.Count), "", int32(itemConfig.GetCharm() * item.Count), ""}
		//
		//userExtendJson, _ := json.Marshal(jsonTargetUser)
		//
		//wg.Add(1)
		//go func() {
		//	defer wg.Done()
		//	_ = s.pushNotificationToUser(ctx, sendUser, &presentPB.GetPresentConfigByIdResp{ItemConfig: itemConfig}, tmpItem.Count, sendExtend.nowTs,
		//		in.ChannelId, messageItemInfo, sendExtend.targetUserMap[tmpItem.GetUid()], string(userExtendJson), tmpItem.GetUid())
		//}()

		//if channelId != 0 {
		//	_ = s.pushNotificationToChannel(ctx, sendUser, itemConfig, totalItemCount, timeVal, channelId, &messageItemInfo, user, bindChannel)
		//}

		//如果是超级大奖全服推送
		if item.AwardEffect == uint32(magic_spirit_logic.MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_LV4) {
			wg.Add(1)
			go func() {
				defer wg.Done()
				for i := 0; i < int(tmpItem.GetCount()); i++ {
					s.PushMagicBreakingNewsToAll(ctx, sendExtend, tmpItem.GetUid(), tmpItem.GetItemId(), tmpItem.GetCount(), in.GetMagicSpiritId(), item.GetBreakingNewsId(), in.GetMagicSpiritName(), in.GetMagicSpiritIcon())
				}
			}()
		}

		// 单笔万元全服
		if sendExtend.presentConfigMap[item.GetItemId()].GetPrice()*tmpItem.GetCount() >= 1000000 {
			_ = s.PushHighSpendPresentBreakingNews(ctx, sendUser, &presentPB.GetPresentConfigByIdResp{ItemConfig: itemConfig}, tmpItem.GetCount(),
				sendExtend.nowTs, sendExtend.channelSimpleInfo.GetChannelSimple(), sendExtend.targetUserMap[item.GetUid()], sendExtend)
		}
	}
	wg.Wait()
	sendExtend.timeCollect.AddTimeEvent("pushNotificationToUser")

}

func (s *MagicPresentMiddlewareMgr) FillRespAndPush(ctx context.Context, sendExtend *baseSendExtend, in *pb.MagicSendPresentReq, out *pb.MagicSendPresentResp) {

	sendUser := sendExtend.sendUser

	out.MsgInfo = &pb.MagicPresentInfoMsg{}
	for _, item := range in.GetGiftItemList() {
		out.MsgInfo.TotalItemCount += item.Count
	}

	// 填msgInfo
	out.MsgInfo.ChannelId = in.ChannelId
	out.MsgInfo.SendTime = uint64(sendExtend.nowTs.UnixNano() / 1000000)
	out.MsgInfo.SendUid = sendUser.GetUid()
	out.MsgInfo.SendNickname = sendUser.GetNickname()
	out.MsgInfo.SendAccount = sendUser.GetUsername()

	if sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege() != nil {
		out.MsgInfo.SendNickname = sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege().GetNickname()
		out.MsgInfo.SendAccount = sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege().GetAccount()
		out.MsgInfo.SendUid = sendExtend.ukwInfoMap[sendUser.GetUid()].GetUid()
	}

	out.MsgInfo.ExtendJson = sendExtend.extendInfo.userExtendJson
	out.MsgInfo.BatchType = uint32(pb.PresentBatchSendType_PRESENT_SOURCE_WITH_ITEM_LIST)
	out.MsgInfo.TotalPrice = sendExtend.totalPrice

	optPb := &pb.SendMagicSpiritOpt{}

	optPb.ItemList = make([]*pb.MagicSpiritSendItem, 0)

	optPb.MagicSpiritId = in.MagicSpiritId
	optPb.MagicSpiritCnt = in.MagicSpiritCnt
	optPb.IsBatch = in.IsBatch
	optPb.TotalItemPrice = sendExtend.totalPrice
	optPb.SysAutoSend = in.SysAutoSend

	comboInfo := &magic_spirit_logic.CombInfo{}
	_ = proto.Unmarshal(in.CombConfig, comboInfo)
	comboText := ""
	if comboInfo.GetLevelUp() && comboInfo.GetCombLevel() >= uint32(magic_spirit_logic.CombLevelType_COMB_LEVEL_LV2) {
		comboText = fmt.Sprintf("%s 连送触发了 %s", "我", comboInfo.GetLevelName())
	}
	comboInfo.ComboText = comboText

	comboByte, _ := proto.Marshal(comboInfo)

	optPb.CombInfo = comboByte
	optPb.ChannelId = in.ChannelId
	optPb.SendUid = in.SendUid
	optPb.SendNickname = sendUser.GetNickname()
	optPb.SendAccount = sendUser.GetUsername()
	optPb.IsMulti = in.GetIsMulti()

	//推送
	itemMap := make(map[uint32]*pb.MagicSpiritSendItem)

	for _, item := range in.GetGiftItemList() {

		awardText := ""
		if item.GetAwardEffect() >= uint32(magic_spirit_logic.MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_LV3) {
			awardText = fmt.Sprintf("超幸运！%s 送出了超值稀缺礼物 %s", "我", sendExtend.presentConfigMap[item.GetItemId()].GetName())
		}

		if _, ok := itemMap[item.GetItemId()]; !ok {
			showEffect, showEffectV2, flowId := s.getPresentEffect(ctx, sendExtend.presentConfigMap[item.GetItemId()], item.Count)
			templateId, err := s.getPresentDynamicEffectTemplate(ctx, sendUser.GetUid(), sendExtend.presentConfigMap[item.GetItemId()], item.Count)
			if err != nil {
				return
			}

			showBatchEffect := true
			if sendExtend.presentConfigMap[item.GetItemId()].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) &&
				(sendExtend.presentConfigMap[item.GetItemId()].GetExtend().GetShowEffect() == 1 || sendExtend.presentConfigMap[item.GetItemId()].GetPrice() >= 10000) {
				showBatchEffect = false
			}

			extendJson, _ := json.Marshal(sendExtend.extendInfo.sendInfoMap[item.GetItemId()])
			itemMap[item.GetItemId()] = &pb.MagicSpiritSendItem{
				ItemId:            item.GetItemId(),
				ShowEffect:        showEffect,
				ShowEffectV2:      showEffectV2,
				FlowId:            flowId,
				IsBatch:           in.IsBatch,
				ShowBatchEffect:   showBatchEffect,
				SendType:          uint32(pb.PresentSendType_PRESENT_SEND_NORMAL),
				DynamicTemplateId: templateId,
				Effect:            item.GetAwardEffect(),
				AwardText:         awardText,
				ExternInfo:        string(extendJson),
			}

			tmpExtendJson := "{}"
			if sendExtend.extendInfo.multiTargetInfoMap[item.GetUid()] != nil && sendExtend.extendInfo.multiTargetInfoMap[item.GetUid()][item.GetItemId()] != nil {
				tmpExtendJson = sendExtend.extendInfo.multiTargetInfoMap[item.GetUid()][item.GetItemId()].userExtendJson
			}
			itemMap[item.GetItemId()].AwardList = make([]*pb.MagicSpiritAwardInfo, 0)

			award := &pb.MagicSpiritAwardInfo{
				ItemId:         item.GetItemId(),
				Count:          item.GetCount(),
				ItemOrderId:    item.GetOrderId(),
				ChannelId:      in.GetChannelId(),
				TargetUid:      sendExtend.targetUserMap[item.GetUid()].GetUid(),
				TargetAccount:  sendExtend.targetUserMap[item.GetUid()].GetUsername(),
				TargetNickname: sendExtend.targetUserMap[item.GetUid()].GetNickname(),
				SendTime:       uint64(time.Now().UnixNano() / 1000000), // 这里没用传进来的ctime ，因为秒级时间戳客户端显示会出问题
				ExtendJson:     tmpExtendJson,
			}
			award.UserProfile = genUserProfile(sendExtend.ukwInfoMap[award.GetTargetUid()])
			if sendExtend.ukwInfoMap[award.GetTargetUid()].GetPrivilege() != nil {
				award.TargetNickname = sendExtend.ukwInfoMap[award.GetTargetUid()].GetPrivilege().GetNickname()
				award.TargetAccount = sendExtend.ukwInfoMap[award.GetTargetUid()].GetPrivilege().GetAccount()
				award.TargetUid = sendExtend.ukwInfoMap[award.GetTargetUid()].GetUid()
			}

			itemMap[item.GetItemId()].AwardList = append(itemMap[item.GetItemId()].AwardList, award)

		} else {
			tmpExtendJson := "{}"
			if sendExtend.extendInfo.multiTargetInfoMap[item.GetUid()] != nil && sendExtend.extendInfo.multiTargetInfoMap[item.GetUid()][item.GetItemId()] != nil {
				tmpExtendJson = sendExtend.extendInfo.multiTargetInfoMap[item.GetUid()][item.GetItemId()].userExtendJson
			}
			award := &pb.MagicSpiritAwardInfo{
				ItemId:         item.GetItemId(),
				Count:          item.GetCount(),
				ItemOrderId:    item.GetOrderId(),
				ChannelId:      in.GetChannelId(),
				TargetUid:      sendExtend.targetUserMap[item.GetUid()].GetUid(),
				TargetAccount:  sendExtend.targetUserMap[item.GetUid()].GetUsername(),
				TargetNickname: sendExtend.targetUserMap[item.GetUid()].GetNickname(),
				SendTime:       uint64(time.Now().UnixNano() / 1000000), // 这里没用传进来的ctime ，因为秒级时间戳客户端显示会出问题
				ExtendJson:     tmpExtendJson,
			}
			award.UserProfile = genUserProfile(sendExtend.ukwInfoMap[award.GetTargetUid()])
			if sendExtend.ukwInfoMap[award.GetTargetUid()].GetPrivilege() != nil {
				award.TargetNickname = sendExtend.ukwInfoMap[award.GetTargetUid()].GetPrivilege().GetNickname()
				award.TargetAccount = sendExtend.ukwInfoMap[award.GetTargetUid()].GetPrivilege().GetAccount()
				award.TargetUid = sendExtend.ukwInfoMap[award.GetTargetUid()].GetUid()
			}

			itemMap[item.GetItemId()].AwardList = append(itemMap[item.GetItemId()].AwardList, award)
		}
	}

	optPb.FromUserProfile = genUserProfile(sendExtend.ukwInfoMap[optPb.GetSendUid()])
	out.SpiritOpt = optPb
	out.SpiritOpt.ItemList = make([]*pb.MagicSpiritSendItem, 0)
	for _, v := range itemMap {
		out.SpiritOpt.ItemList = append(out.SpiritOpt.ItemList, v)
	}

	sort.Slice(out.SpiritOpt.ItemList, func(i, j int) bool {
		return sendExtend.presentConfigMap[out.SpiritOpt.GetItemList()[i].GetItemId()].GetPrice() < sendExtend.presentConfigMap[out.SpiritOpt.GetItemList()[j].GetItemId()].GetPrice()
	})

	s.procPushEvent(ctx, sendExtend, in, out, itemMap, comboInfo)
}

func (s *MagicPresentMiddlewareMgr) sendPresent(ctx context.Context, sendUser *accountPB.UserResp, itemSendList []*pb.GiftItem,
	sendExtend *baseSendExtend, extendInfo *ExtendInfo, timeVal uint32, appId uint32, marketId uint32, clientIp string,
	clientType uint32, bindChannelId uint32, isBatch bool) (sucUser map[uint32]*account.User, err error) {
	sucUser = make(map[uint32]*account.User)

	if len(itemSendList) == 0 {
		return nil, protocol.NewExactServerError(nil, status.ErrUserPresentInvalidTargetUserSize)
	}

	var channelGuildId uint32
	//1 : 公会房  4：公会公开厅（娱乐房）
	if sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType() == uint32(channelPB_.ChannelType_GUILD_TYPE) || sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType() == uint32(channelPB_.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		channelGuildId = sendExtend.channelSimpleInfo.GetChannelSimple().GetBindId()
	}

	baseAddRich := uint32(0)
	valueMap := make(map[uint32]uint32, 0)
	for _, item := range itemSendList {
		total := sendExtend.presentConfigMap[item.GetItemId()].GetPrice() * item.GetCount()
		if sendExtend.presentConfigMap[item.GetItemId()].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
			// 如果是t豆，额外计算一下倍率
			total = s.calRichRatio(timeVal, item.GetItemId(), total)
		}
		baseAddRich += total
		//append(itemList, &valueItem{Uid: item.Uid, AddValue:})
		if _, ok := valueMap[item.GetUid()]; ok {
			valueMap[item.GetUid()] += sendExtend.presentConfigMap[item.GetItemId()].GetPrice() * item.GetCount()
		} else {
			valueMap[item.GetUid()] = sendExtend.presentConfigMap[item.GetItemId()].GetPrice() * item.GetCount()
		}
	}

	//baseAddCharm := itemCfg.GetCharm() * itemCount
	extendInfo.baseRichValue = baseAddRich
	extendInfo.realAddRichValue = baseAddRich
	extendInfo.sendUserCurrRichValue = uint64(baseAddRich)
	extendInfo.bIsSendUserRichLevelChanged = false

	//红钻加魅力值财富值。T豆走kafka事件加

	userBonusCardMap := make(map[uint32]*UserBonusCardEffect, 0)
	//sendBonusCardMap := make(map[uint32]*UserBonusCardEffect, 0)
	charmCardTimes := s.calBonusCardEffects(ctx, valueMap, userBonusCardMap, uint32(backpackPB.PackageItemType_BACKPACK_CARD_CHARM_EXP))
	sendExtend.timeCollect.AddTimeEvent("calBonusCardEffects")

	itemCountMap := make(map[uint32]uint32, 0)
	for _, item := range itemSendList {
		itemCountMap[item.ItemId] += item.Count
	}

	sendExtend.extendInfo.sendInfoMap = make(map[uint32]*jsonSendUser)

	//  卡同一时间只能用一张，所以循环覆盖是没问题的 有机会优化一下这里的逻辑
	richCardTimes := uint32(100)
	for id, count := range itemCountMap {
		// 合并显示，人数直接填1
		value := sendExtend.presentConfigMap[id].GetPrice() * count
		if sendExtend.presentConfigMap[id].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
			// 如果是t豆，显示这里也要额外计算一下倍率
			value = s.calRichRatio(timeVal, id, value)
		}
		richCardTimes = s.calUserBonusCardEffect(ctx, sendUser, 1, value, true, sendExtend, id)
		sendExtend.timeCollect.AddTimeEvent(fmt.Sprintf("calUserBonusCardEffect uid %d item %d", sendUser.GetUid(), id))
	}

	for _, user := range userBonusCardMap {
		extend := &TargetExtendInfo{}
		extend.uid = user.uid
		extend.realAddCharm = user.finalValue
		extend.baseCharm = user.addValue
		extend.score = valueMap[extend.uid] / 2 // 这里先用价值/2替代一下？
		extend.recvUserCurrCharmValue = 0
		extend.bIsSendUserRichLevelChanged = false
		extendInfo.targetInfoMap[user.uid] = extend
	}

	for _, item := range itemSendList {
		extend := &TargetExtendInfo{}
		extend.uid = item.Uid
		extend.baseCharm = sendExtend.presentConfigMap[item.ItemId].GetPrice() * item.GetCount()
		extend.realAddCharm = extend.baseCharm * charmCardTimes[item.Uid] / 100
		extend.score = sendExtend.presentConfigMap[item.ItemId].GetPrice() * item.GetCount() / 2

		extend.recvUserCurrCharmValue = 0
		extend.bIsSendUserRichLevelChanged = false

		if _, ok := extendInfo.multiTargetInfoMap[item.Uid]; !ok {
			extendInfo.multiTargetInfoMap[item.Uid] = make(map[uint32]*TargetExtendInfo)
		}
		extendInfo.multiTargetInfoMap[item.Uid][item.ItemId] = extend
	}

	//addRichOnce := extendInfo.realAddRichValue / uint32(len(targetUserMap))
	//relayChannelId := uint32(0)

	//在官频， 这里逻辑直接由幸运礼物服务处理了
	//if sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType() == uint32(channelPB_.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
	//	resp, err := s.offcialLiveCli.GetRelay(ctx, sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId())
	//	if err != nil {
	//		log.ErrorWithCtx(ctx,"sendPresent -- GetRelay fail ,  channelId %d err %v", sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId(), err)
	//	}
	//	relayChannelId = resp.GetChannelId()
	//}

	wg := sync.WaitGroup{}
	lock := sync.Mutex{}

	for _, item := range itemSendList {
		if _, ok := sendExtend.presentConfigMap[item.ItemId]; !ok {
			continue
		}

		ok, err := CheckDealToken(item.GetDealToken(), item.GetOrderId(), sendUser.GetUid(), true, true)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendPresent CheckDealToken fail , err %v , uid %d", err, sendUser.GetUid())
			// deal_token处理失败，暂时不返回错误，防止送礼失败;deal_token相关链路稳定后可以正常返回错误
			//return nil, err
		}

		if !ok {
			log.ErrorWithCtx(ctx, "SendPresent CheckDealToken not ok, uid %d", err, sendUser.GetUid())
			// deal_token处理失败，暂时不返回错误，防止送礼失败;deal_token相关链路稳定后可以正常返回错误
			//err := protocol.NewExactServerError(nil,status.ErrRiskControlAwardCenterOrderInvalid)
			//return nil, err
		}

		dealToken, err := UpdateDealTokenInfo(ctx, item.GetDealToken(), item.GetOrderId(), isBatch)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendPresent UpdateDealTokenInfo fail , err %v , uid %d", err, sendUser.GetUid())
			// deal_token处理失败，暂时不返回错误，防止送礼失败;deal_token相关链路稳定后可以正常返回错误
			//continue
		}

		addRich := sendExtend.presentConfigMap[item.ItemId].RichValue * item.Count * richCardTimes / 100
		if sendExtend.presentConfigMap[item.ItemId].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
			// 如果是t豆，显示这里也要额外计算一下倍率
			addRich = s.calRichRatio(timeVal, item.ItemId, addRich)
			if !s.isRecordSenderRich(ctx, sendUser.GetUid(), sendExtend.presentConfigMap[item.ItemId].GetPriceType()) {
				addRich = 0
			}
		}

		addCharm := sendExtend.presentConfigMap[item.ItemId].GetCharm() * item.Count * charmCardTimes[item.Uid] / 100

		sendReq := presentPB.SendPresentReq{
			Uid:              sendUser.GetUid(),
			ItemId:           item.ItemId,
			OrderId:          item.OrderId,
			ItemConfig:       sendExtend.presentConfigMap[item.ItemId],
			ChannelId:        sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId(),
			ChannelType:      sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType(),
			ChannelName:      sendExtend.channelSimpleInfo.GetChannelSimple().GetName(),
			GuildId:          channelGuildId,
			ItemCount:        item.Count,
			SendTime:         timeVal,
			OptInvalid:       true,
			UserFromIp:       clientIp,
			ItemSource:       uint32(pb.PresentSourceType_PRESENT_SOURCE_MAGIC),
			AsyncFlag:        true,
			AppId:            appId,
			MarketId:         marketId,
			ChannelDisplayId: sendExtend.channelSimpleInfo.GetChannelSimple().GetDisplayId(),
			SendSource:       uint32(pb.PresentSendSourceType_E_SEND_SOURCE_MAGIC),
			SendPlatform:     clientType,
			BatchType:        uint32(pb.PresentBatchSendType_PRESENT_SOURCE_WITH_ITEM_LIST),
			AddCharm:         addCharm,
			AddRich:          addRich,
			BindChannelId:    bindChannelId,
			SendMethod:       uint32(pb.PresentSendMethodType_PRESENT_TYPE_ROOM),
			TargetUid:        item.Uid,
			DealToken:        dealToken,
			GiverGuildId:     sendUser.GetCurrentGuildId(),
			ReceiverGuildId:  sendExtend.targetUserMap[item.Uid].GetCurrentGuildId(),
		}

		if sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege() != nil {
			sendReq.FromUkwAccount = sendExtend.ukwInfoMap[sendUser.GetUid()].GetAccount()
			sendReq.FromUkwNickname = sendExtend.ukwInfoMap[sendUser.GetUid()].GetNickname()
		}

		if sendExtend.ukwInfoMap[item.Uid].GetPrivilege() != nil {
			sendReq.ToUkwAccount = sendExtend.ukwInfoMap[item.Uid].GetAccount()
			sendReq.ToUkwNickname = sendExtend.ukwInfoMap[item.Uid].GetNickname()
		}

		log.InfoWithCtx(ctx, "PresentMiddlewareMgr -- SendPresent %v ", sendReq)

		_, targetOk := extendInfo.targetInfoMap[item.Uid]
		if targetOk {
			sendReq.AddCharm = sendExtend.presentConfigMap[item.ItemId].GetCharm() * item.Count * charmCardTimes[item.Uid] / 100
		} else {
			sendReq.AddCharm = 0
		}

		if !s.isRecordTargetCharm(ctx, item.Uid, sendExtend.presentConfigMap[item.ItemId].GetPriceType()) {
			sendReq.AddCharm = 0
		}

		wg.Add(1)
		item := item
		go func() {
			defer func() {
				wg.Done()
				lock.Unlock()
			}()

			isVirtualLive := s.checkLiveStatus(ctx, sendExtend.channelSimpleInfo.GetChannelSimple(), bindChannelId)

			sendReq.IsVirtualLive = isVirtualLive
			sendReq.ScoreType = sendExtend.ScoreTypeMap[item.GetUid()]
			sendReq.IdentityType = sendExtend.IdentityTypeMap[item.GetUid()]

			err := client.PresentCli.SendPresent(ctx, &sendReq)
			if err == nil {
				// 送礼成功，完成任务
				if sendExtend.channelSimpleInfo.ChannelSimple.GetChannelId() > 0 {
					reqSerialize, _ := proto.Marshal(&sendReq)
					_ = client.MissionCli.HandleMission(ctx, sendUser.GetUid(), 1165, reqSerialize)
				}
			}
			lock.Lock()
			if err == nil {
				sucUser[sendReq.GetTargetUid()] = sendExtend.targetUserMap[sendReq.GetTargetUid()]
			} else {
				log.ErrorWithCtx(ctx, "sendPresent -- userpresent.SendPresent fail ,  uid %d cid %d err %v", sendReq.GetUid(), sendReq.GetChannelId(), err)
			}
		}()
	}

	//开协程去同步获取送礼结果
	wg.Wait()

	return sucUser, err
}

// 不存在复数人送礼的情况，所以请求复数用户的加速卡情况，一定是收礼人
func (s *MagicPresentMiddlewareMgr) calBonusCardEffects(ctx context.Context, targetMap map[uint32]uint32,
	userCardEffectMap map[uint32]*UserBonusCardEffect, expType uint32) (cardTimesMap map[uint32]uint32) {
	cardTimesMap = make(map[uint32]uint32)

	for key, value := range targetMap {
		cardTimesMap[key] = 100

		resp, err := s.backpackFuncCli.GetAccelerateCardUsage(ctx, key)
		if err != nil {
			log.ErrorWithCtx(ctx, "%s -- GetUserFuncCardUse fail. err:%v targetUserMap item:%v", err, key)
		}

		userCardEffectMap[key] = &UserBonusCardEffect{uid: key, addValue: value, finalValue: value, bonusMsg: ""}

		if len(resp.GetUserItemList()) == 0 {
			continue
		}

		for _, cardEffect := range resp.GetUserItemList() {
			if cardEffect.CardType != backpack_base.PackageItemType(expType) {
				continue
			}

			if 100 > cardEffect.CardTimes {
				continue
			}

			cardTimesMap[key] = cardEffect.CardTimes

			userCardEffectMap[key].finalValue = userCardEffectMap[key].finalValue * cardEffect.CardTimes / 100

			if cardEffect.CardType == backpack_base.PackageItemType_BACKPACK_CARD_RICH_ACCELERATOR {
				// 卡片类型是财富卡，且签约用户开启不增加财富值
				if !s.isRecordSenderRich(ctx, value, uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN)) {
					break
				}
				userCardEffectMap[key].bonusMsg = fmt.Sprintf("财富卡%d.%d倍额外加成 +%d", cardEffect.CardTimes/100,
					cardEffect.CardTimes%100/10, userCardEffectMap[key].finalValue-userCardEffectMap[key].addValue)
			}

			break
		}
	}
	return
}

//type itemWithCount struct {
//	ItemId uint32
//	Count  uint32
//}

func (s *MagicPresentMiddlewareMgr) calUserBonusCardEffect(ctx context.Context, sendUser *accountPB.UserResp, userCount uint32, addValue uint32,
	isSend bool, sendExtend *baseSendExtend, itemId uint32) (cardTimes uint32) {
	cardType := uint32(0)
	cardTimes = 100

	if isSend {
		cardType = uint32(backpackPB.PackageItemType_BACKPACK_CARD_RICH_EXP)
	} else {
		cardType = uint32(backpackPB.PackageItemType_BACKPACK_CARD_CHARM_EXP)
	}

	resp, err := s.backpackFuncCli.GetAccelerateCardUsage(ctx, sendUser.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "%s -- GetUserFuncCardUse fail. err:%v uid:%d", err, sendUser.GetUid())
	}

	jsonSendUser := jsonSendUser{
		RichValue:                int32(addValue),
		RichValuePrefix:          "",
		MemberContribution:       0,
		MemberContributionPrefix: "",
		BonusMsg:                 "",
	}

	sendExtend.extendInfo.sendInfoMap[itemId] = &jsonSendUser

	for _, cardEffect := range resp.GetUserItemList() {
		if cardEffect.CardType != backpack_base.PackageItemType(cardType) {
			continue
		}
		if 100 > cardEffect.CardTimes {
			continue
		}
		cardTimes = cardEffect.CardTimes

		finalValue := addValue * cardEffect.CardTimes / 100
		if cardEffect.CardType == backpack_base.PackageItemType_BACKPACK_CARD_RICH_ACCELERATOR {
			// 卡片类型是财富卡，且签约用户开启不增加财富值
			if !s.isRecordSenderRich(ctx, sendUser.GetUid(), uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN)) {
				break
			}
			if userCount >= 1 {
				perUserBonusValue := (finalValue - addValue) / userCount

				richValueBonusMsg := fmt.Sprintf("财富卡%d.%d倍额外加成 +%d", cardEffect.CardTimes/100,
					cardEffect.CardTimes%100/10, perUserBonusValue)

				sendExtend.extendInfo.sendInfoMap[itemId].RichValue = int32(finalValue)
				sendExtend.extendInfo.sendInfoMap[itemId].BonusMsg = richValueBonusMsg

				break
			} else {
				continue
			}
		}
	}
	return
}

func (s *MagicPresentMiddlewareMgr) PushMagicBreakingNewsToAll(ctx context.Context, sendExtend *baseSendExtend, targetUid uint32, giftId uint32, count uint32, magicId, breakingNewsId uint32, magicName, magicIcon string) {

	sendUser := sendExtend.sendUser
	targetUser := sendExtend.targetUserMap[targetUid]
	channelInfo := sendExtend.channelSimpleInfo.GetChannelSimple()
	itemCfg := sendExtend.presentConfigMap[giftId]
	OldNewsContent := fmt.Sprintf("送出了价值%dT豆的 %s ", itemCfg.GetPrice(), itemCfg.GetName())

	BreakingNewsMessage := &publicNoticePb.CommonBreakingNewsV3{
		FromUid: sendUser.GetUid(),
		FromUserInfo: &publicNoticePb.UserInfo{
			Nick:    sendUser.GetNickname(),
			Account: sendUser.GetUsername(),
		},
		TargetUid: targetUser.GetUid(),
		TargetUserInfo: &publicNoticePb.UserInfo{
			Account: targetUser.GetUsername(),
			Nick:    targetUser.GetNickname(),
		},
		ChannelId: channelInfo.GetChannelId(),
		ChannelInfo: &publicNoticePb.ChannelInfo{
			ChannelType:      channelInfo.GetChannelType(),
			ChannelName:      channelInfo.GetName(),
			ChannelBindid:    channelInfo.GetBindId(),
			ChannelDisplayid: channelInfo.GetDisplayId(),
		},

		NewsPrefix: "欧皇诞生！",
		PresentNewsBaseOpt: &publicNoticePb.PresentBreakingNewsBaseOpt{
			GiftId:      itemCfg.GetItemId(),
			GiftCount:   count,
			GiftIconUrl: itemCfg.GetIconUrl(),
			GiftName:    itemCfg.GetName(),
			GiftWorth:   itemCfg.GetPrice(),
			MagicId:     magicId,
			MagicName:   magicName,
			MagicIcon:   magicIcon,
		},
		BreakingNewsBaseOpt: &publicNoticePb.CommBreakingNewsBaseOpt{},
		IsOldDeal:           1,
		OldNewsContent:      OldNewsContent,
	}

	BreakingNewsMessage.BreakingNewsBaseOpt = &publicNoticePb.CommBreakingNewsBaseOpt{
		TriggerType:      uint32(pushPb.CommBreakingNewsBaseOpt_MAGIC_PRESENT_SUPER_AWARD),
		RollingCount:     1,
		RollingTime:      10,
		AnnounceScope:    uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL + pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL),
		JumpType:         uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_ClICK_OUTSIDE),
		AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
		JumpPosition:     uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_TO_CHANNEL_CLICK),
	}

	breakingReq := &publicNoticePb.PushBreakingNewsReq{
		CommonBreakingNews: BreakingNewsMessage,
	}
	if breakingNewsId != 0 {
		breakingReq.CommonBreakingNews.BreakingNewsBaseOpt.TriggerType = 999
		breakingReq.RichTextNews = &publicNoticePb.RichTextNews{
			NewsId: breakingNewsId,
		}
	}

	_, err := s.publicNoticeCli.PushBreakingNews(ctx, breakingReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushPresentBreakingNewsToAll -- PushBreakingNews fail. uid:%v, channel_id:%v, err:%v", sendUser.GetUid(),
			channelInfo.GetChannelId(), err)
	} else {
		log.InfoWithCtx(ctx, "PushPresentBreakingNewsToAll -- PushBreakingNews. uid:%v, channel_id:%v", sendUser.GetUid(),
			channelInfo.GetChannelId())
	}
}
