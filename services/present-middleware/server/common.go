package server

//
import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/protocol/grpc"
	backpack_base "golang.52tt.com/protocol/services/backpack-base"
	backpack_func_card "golang.52tt.com/protocol/services/backpack-func-card"
	"golang.52tt.com/protocol/services/presentextraconf"
	"golang.52tt.com/services/present-middleware/cache"
	"golang.52tt.com/services/present-middleware/conf"
	"strconv"
	"sync"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	ga "golang.52tt.com/protocol/app"
	channelPB_ "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	accountPB "golang.52tt.com/protocol/services/account-go"
	backpackPB "golang.52tt.com/protocol/services/backpacksvr"
	channelPB "golang.52tt.com/protocol/services/channelsvr"
	pb "golang.52tt.com/protocol/services/present-middleware"
	presentPB "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/present-middleware/models/pay"
	"golang.52tt.com/services/present-middleware/rpc/client"
)

func (s *CommonPresentMiddlewareMgr) BeforeSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {
	// 类型断言
	out, ok := outInter.(*pb.SendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.SendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- para inter need *PresentSendBase type. ")
		return nil
	}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent ServiceInfoFromContext fail. req:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrUserPresentServiceInfoInvalid)
	}

	ctx = s.SetPresentGoCtx(ctx, in.GetSendUid())

	// 如果请求房没填，尝试填充ClientInfo
	if in.GetClientInfo() == nil {
		in.ClientInfo = &pb.PresentClientInfo{
			AppId:    0, // 默认tt
			MarketId: serviceInfo.MarketID,
			ServiceInfo: &pb.ServiceCtrlInfo{
				ClientIp:      strconv.Itoa(int(serviceInfo.ClientIP)),
				ClientType:    uint32(serviceInfo.ClientType),
				ClientVersion: serviceInfo.ClientVersion,
				DeviceId:      serviceInfo.DeviceID,
				TerminalType:  serviceInfo.TerminalType,
			}}
	} else {
		if in.GetClientInfo().GetServiceInfo() == nil {
			in.GetClientInfo().ServiceInfo = &pb.ServiceCtrlInfo{
				ClientIp:      strconv.Itoa(int(serviceInfo.ClientIP)),
				ClientType:    uint32(serviceInfo.ClientType),
				ClientVersion: serviceInfo.ClientVersion,
				DeviceId:      serviceInfo.DeviceID,
				TerminalType:  serviceInfo.TerminalType,
			}
		}
	}

	// 如果填了sendTime，就跟传入的时间保持一致
	sendExtend.nowTs = time.Now()
	if in.GetSendTime() != 0 {
		sendExtend.nowTs = time.Unix(in.GetSendTime(), 0).Local()
	}

	sendExtend.userBonusCardMap = make(map[uint32][]*backpack_func_card.FuncCardCfg)
	out.ItemSource = in.ItemSource

	// 预处理，获得送礼要用的相关信息

	targetUidList := make([]uint32, 0)
	// 不是全麦，从对应的oneOf结构里获取目标用户uid
	if in.GetBatchType() != uint32(pb.PresentBatchSendType_PRESENT_SOURCE_ALL_MIC) {
		switch in.GetTargetInfo().GetTarget().(type) {
		case *pb.PresentTargetInfo_SingleTarget:
			targetUidList = append(targetUidList, in.GetTargetInfo().GetSingleTarget().GetUid())
		case *pb.PresentTargetInfo_MultiTarget:
			if in.GetTargetInfo().GetMultiTarget().GetUserInfo() == nil {
				targetUidList = append(targetUidList, in.GetTargetInfo().GetMultiTarget().GetUid()...)
			} else {
				for _, item := range in.GetTargetInfo().GetMultiTarget().GetUserInfo() {
					targetUidList = append(targetUidList, item.GetUid())
				}
			}
		case *pb.PresentTargetInfo_MultiItem:
			for _, item := range in.GetTargetInfo().GetMultiItem().GetItemInfo() {
				targetUidList = append(targetUidList, item.GetUid())
			}
		}
	} else {
		// 是全麦，如果有target就用传进来的，否则拿麦上用户
		switch in.GetTargetInfo().GetTarget().(type) {
		case *pb.PresentTargetInfo_MultiTarget:
			if len(in.GetTargetInfo().GetMultiTarget().GetUid()) > 0 {
				targetUidList = append(targetUidList, in.GetTargetInfo().GetMultiTarget().GetUid()...)
			} else if len(in.GetTargetInfo().GetMultiTarget().GetUserInfo()) > 0 {
				for _, item := range in.GetTargetInfo().GetMultiTarget().GetUserInfo() {
					targetUidList = append(targetUidList, item.GetUid())
				}
			} else {
				// 全麦拿麦上用户
				err = s.fillBatchInfo(ctx, in, &targetUidList, serviceInfo)
				if err != nil {
					log.ErrorWithCtx(ctx, "BeforeSendPresent -- fillBatchInfo fail. uid:%v c:%v err:%v",
						in.SendUid, in.ChannelId, err)
					return err
				}
			}
		default:
			// 全麦拿麦上用户
			err = s.fillBatchInfo(ctx, in, &targetUidList, serviceInfo)
			if err != nil {
				log.ErrorWithCtx(ctx, "BeforeSendPresent -- fillBatchInfo fail. uid:%v c:%v err:%v",
					in.SendUid, in.ChannelId, err)
				return err
			}
		}
	}

	itemList := make([]uint32, 0)
	switch in.GetTargetInfo().GetTarget().(type) {
	case *pb.PresentTargetInfo_SingleTarget:
		itemList = append(itemList, in.GetTargetInfo().GetSingleTarget().GetItemId())
	case *pb.PresentTargetInfo_MultiTarget:
		itemList = append(itemList, in.GetTargetInfo().GetMultiTarget().GetItemId())
	case *pb.PresentTargetInfo_MultiItem:
		for _, item := range in.GetTargetInfo().GetMultiItem().GetItemInfo() {
			itemList = append(itemList, item.GetItemId())
		}
	}

	// 先检查是不是需要ban掉的礼物id
	if s.GeneralConfig.GetPresentConfig().BanItemMap != nil {
		for _, item := range itemList {
			if source, ok := s.GeneralConfig.GetPresentConfig().BanItemMap[item]; ok {
				if source == conf.BanItemTypeCommon || source == conf.BanItemTypeAll {
					return protocol.NewExactServerError(nil, status.ErrUserPresentConfigNotExist)
				}
			}
		}
	}

	sendExtend.BusinessType = in.GetBusinessType()
	err = s.preSendPresent(ctx, in.SendUid, targetUidList, itemList, in.ChannelId, sendExtend)
	if err != nil {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- preSendPresent fail. uid:%v c:%v err:%v",
			in.SendUid, in.ChannelId, err)
		return err
	}

	err = s.genOrderId(sendExtend, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- genOrderId fail. uid:%v c:%v err:%v",
			in.SendUid, in.ChannelId, err)
		return err
	}

	for _, item := range sendExtend.presentConfigMap {
		err := s.checkPresentValid(item, in.ItemSource, in.GetSendUid(), in.GetBirthdayType())
		log.DebugWithCtx(ctx, "checkPresentValid res %v", err)
		if err != nil {
			sErr := checkPresentPrivilege(ctx, sendExtend.primaryCustomGift.GetItemId(), sendExtend.sendUser.GetUid(),
				uint32(sendExtend.nowTs.Unix()), item, err)
			return sErr
		}
	}

	// 如果是高价值的背包礼物，查一下来源
	needSourceItem := uint32(0)
	for _, item := range itemList {
		itemCfg := sendExtend.presentConfigMap[item]
		if itemCfg.GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) && itemCfg.GetPrice() >= 100000 &&
			itemCfg.GetPrice() < 999999 && in.GetItemSource() == uint32(pb.PresentSourceType_PRESENT_SOURCE_PACKAGE) {
			needSourceItem = itemCfg.GetItemId()
		}
	}

	if needSourceItem != 0 {
		bg, err := client.BackpackCli.GetUserBackpack(ctx, in.GetSendUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "BeforeSendPresent -- GetUserBackpack fail. uid:%v c:%v err:%v",
				in.SendUid, in.ChannelId, err)
			return err
		}

		log.DebugWithCtx(ctx, "BeforeSendPresent GetUserBackpack uid %d bg len %d", in.GetSendUid(), len(bg.GetUserItemList()))

		for _, item := range bg.GetUserItemList() {
			if item.GetSourceId() == needSourceItem {
				sendExtend.bgPresentSource = item.GetSourceType()
				break
			}
		}
	}

	return err
}

func checkPresentPrivilege(ctx context.Context, itemId, uid, nowTs uint32, item *presentPB.StPresentItemConfig, err protocol.ServerError) error {
	if err.Code() == status.ErrUserPresentLmtEffectedEnd || err.Code() == status.ErrUserPresentLmtEffectedBegin {
		// 未上架的情况，检查是不是有发放礼物权限
		resp, sErr := client.PresentPrivilege.GetPresentPrivilege(ctx, uid)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "BeforeSendPresent -- GetPresentPrivilege fail. uid:%v err:%v",
				uid, err)
		}
		log.DebugWithCtx(ctx, "GetPresentPrivilege res %v ", resp)
		for _, privilege := range resp.GetPrivilegeList() {
			log.DebugWithCtx(ctx, "privilege , sendExtend.nowTs  %v %v", resp, time.Now().Unix())
			if (privilege.GetGiftId() == item.GetItemId() || privilege.GetGiftId() == itemId) &&
				privilege.GetBeginTime() < nowTs && privilege.GetExpireTime() > nowTs {
				return nil
			}
		}

		if item.GetExtend() != nil {
			// 再检查是不是延长期限的显示礼物
			if item.GetExtend().GetEffectEndDelay() && err.Code() == status.ErrUserPresentLmtEffectedEnd {
				resp, sErr := client.PresentExtraCli.GetPresentEffectTime(ctx, &presentextraconf.GetPresentEffectTimeReq{
					Uid: uid,
				})
				if sErr != nil {
					log.ErrorWithCtx(ctx, "BeforeSendPresent -- GetPresentEffectTime fail. uid:%v err:%v",
						uid, err)
				}
				for _, info := range resp.GetPresentEffectTimeInfos() {
					if info.GetGiftId() == item.GetItemId() && (info.GetEffectEnd() >= uint32(time.Now().Unix()) ||
						info.GetEffectEnd() == 0) {
						err = nil
					}
				}
			}
		}
	}
	return err
}

func (s *CommonPresentMiddlewareMgr) SendingPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {

	// 类型断言
	_, ok := outInter.(*pb.SendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "SendingPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.SendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "SendingPresent -- para inter need *SendPresentReq type. ")
		return nil
	}

	//冻结红钻/t豆
	if in.WithPay {
		// 计算物品个数
		itemCount := uint32(0)
		switch in.GetTargetInfo().GetTarget().(type) {
		case *pb.PresentTargetInfo_SingleTarget:
			itemCount = in.GetTargetInfo().GetSingleTarget().GetCount()
		case *pb.PresentTargetInfo_MultiTarget:
			itemCount = in.GetTargetInfo().GetMultiTarget().GetCount() * uint32(len(in.GetTargetInfo().GetMultiTarget().GetUserInfo()))
			if len(in.GetTargetInfo().GetMultiTarget().GetUserInfo()) == 0 {
				itemCount = in.GetTargetInfo().GetMultiTarget().GetCount() * uint32(len(in.GetTargetInfo().GetMultiTarget().GetUid()))
			}
		}
		itemId := uint32(0)
		switch in.GetTargetInfo().GetTarget().(type) {
		case *pb.PresentTargetInfo_SingleTarget:
			itemId = in.GetTargetInfo().GetSingleTarget().GetItemId()
		case *pb.PresentTargetInfo_MultiTarget:
			itemId = in.GetTargetInfo().GetMultiTarget().GetItemId()
		}

		sendExtend.remainCurrency, sendExtend.remainTbeans, sendExtend.remainSource, sendExtend.realItemSource, sendExtend.expireTime, err =
			s.tryFreeze(ctx, sendExtend, itemId, itemCount, in.ItemSource, in.BackpackItemId, in.SendMethod, in.BusinessType)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendingPresent -- tryFreeze fail. uid:%v c:%v err:%v",
				in.SendUid, in.ChannelId, err)
			return err
		}
		if sendExtend.realItemSource != in.GetItemSource() {
			in.ItemSource = sendExtend.realItemSource
		}
	}

	sendExtend.timeCollect.AddTimeEvent("FreezeEnd")
	sendExtend.extendInfo = &ExtendInfo{targetInfoMap: map[uint32]*TargetExtendInfo{}}

	// 送礼人是否记录财富值
	ctx = context.WithValue(ctx, IsRecordSenderRichKey, s.isRecordRichMap(ctx, sendExtend.sendUser.GetUid(), sendExtend.targetUserMap))

	// 人为制造失败
	for _, item := range s.GeneralConfig.GetPresentConfig().BanUidList {
		if item == sendExtend.sendUser.GetUid() {
			return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
		}
	}

	//赠送礼物
	sendExtend.sucOrders, err = s.sendPresent(ctx, sendExtend, in.GetClientInfo(), in)

	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- __SendPresent fail. uid:%v ret:%v req:%v",
			in.SendUid, err, in)
		return err
	}

	//礼物周边信息处理
	itemMap := make(map[uint32]uint32)
	for _, item := range sendExtend.sucOrders {
		itemMap[item.giftId] += item.count
	}

	sendExtend.timeCollect.AddTimeEvent("before ProcPresentWatchWithList")
	log.DebugWithCtx(ctx, "before ProcPresentWatchWithList present extend info : %+v", sendExtend.extendInfo)

	err = s.ProcPresentWatchWithList(ctx, sendExtend, itemMap)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- __ProcPresentWatch fail:%v. _req:%v", err, in)
	}
	log.DebugWithCtx(ctx, "after send present extend info : %+v", sendExtend.extendInfo)

	//提交支付订单
	if in.WithPay {
		err = s.commitPayOrder(ctx, sendExtend, uint32(pb.PresentSendMethodType_PRESENT_TYPE_ROOM), in.ItemSource)
	}

	return err
}

func (s *CommonPresentMiddlewareMgr) AfterSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {

	// 类型断言
	out, ok := outInter.(*pb.SendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.SendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- para inter need *SendPresentReq type. ")
		return nil
	}

	//送礼后处理，填充动效、推送等
	s.FillRespAndPush(ctx, sendExtend, in, out)
	return err
}

func (s *CommonPresentMiddlewareMgr) preSendPresent(ctx context.Context, sendUid uint32, targetUidList []uint32,
	itemIdList []uint32, channelId uint32, sendExtend *baseSendExtend) (err error) {

	sendExtend.presentConfigMap = make(map[uint32]*presentPB.StPresentItemConfig)

	// 如果是单送，要区分下是不是旧的神秘人，防止从资料卡送礼暴露神秘人身份
	// isReal代表传入的就是真实的uid，可以直接给对应的真实信息
	isReal, err := checkOldFakeUid(ctx, targetUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "preSendPresent checkOldFakeUid err : %v, uid:%d", err, sendUid)
		return err
	}

	// 首先要对targetUidList做一次转换
	fake2RealUidMap, real2FakeUidMap, realUidList, err := getRealUidByFakeUid(ctx, append(targetUidList, sendUid))
	if err != nil {
		log.ErrorWithCtx(ctx, "preSendPresent getRealUidByFakeUid err : %v, uid:%d", err, sendUid)
		return err
	}
	sendExtend.fake2RealUidMap = fake2RealUidMap
	sendExtend.real2FakeUidMap = real2FakeUidMap

	sendUid = fake2RealUidMap[sendUid]

	wg := sync.WaitGroup{}

	wg.Add(3)

	//用户信息
	go func() {
		defer wg.Done()

		var sErr error
		sendExtend.targetUserMap, sErr = client.AccountCli.GetUsersMap(ctx, realUidList)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "preSendPresent - GetUsersMap fail , uid: %+v", sendUid)
			err = sErr
		}

	}()

	ukwInfoMap := make(map[uint32]*ga.UserProfile)
	//用户神秘人信息
	go func() {
		defer wg.Done()

		var sErr error
		ukwInfoMap, sErr = client.UserProfileCli.BatchGetUserProfileV2(ctx, realUidList, true)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "preSendPresent BatchGetUserProfileV2 err : %v, uid:%d", sErr, sendUid)
			err = sErr
		}

	}()

	//礼物信息
	go func() {
		defer wg.Done()

		var sErr error
		resp, sErr := client.PresentCli.GetPresentConfigByIdList(ctx, sendUid, itemIdList, 0)
		if sErr == nil {
			for _, item := range resp.GetItemList() {
				sendExtend.presentConfigMap[item.ItemId] = item
			}
		}

		if sErr != nil {
			log.ErrorWithCtx(ctx, "preSendPresent - GetPresentConfigByIdList fail , uid: %+v", sendUid)
			err = sErr
		}
	}()

	if channelId != 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()

			channelInfo, sErr := client.ChannelCli.GetChannelSimpleInfo(ctx, sendUid, channelId)
			sendExtend.channelSimpleInfo = &channelPB.GetChannelSimpleInfoResp{ChannelSimple: channelInfo}
			if sErr != nil {
				log.ErrorWithCtx(ctx, "preSendPresent - GetChannelSimpleInfo fail , uid: %+v", sendUid)
				err = sErr
			}
		}()
	} else {
		sendExtend.channelSimpleInfo = &channelPB.GetChannelSimpleInfoResp{ChannelSimple: &channelPB.ChannelSimpleInfo{}}
	}

	wg.Wait()

	if err != nil {
		return err
	}

	if isPgc(sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType()) {
		sendExtend.ukwInfoMap = ukwInfoMap
	} else {
		sendExtend.ukwInfoMap = FillUgcUserProfile(sendExtend.targetUserMap)
	}

	sendExtend.sendUser = sendExtend.targetUserMap[sendUid]
	delete(sendExtend.targetUserMap, sendUid)

	if len(sendExtend.targetUserMap) == 0 {
		err = protocol.NewExactServerError(nil, status.ErrUserPresentInvalidTargetUserSize)
		return
	}

	if len(sendExtend.presentConfigMap) == 0 {
		err = protocol.NewExactServerError(nil, status.ErrUserPresentConfigNotExist)
	}

	// t豆礼物，且时间晚于积分检测的开始时间
	if sendExtend.presentConfigMap[itemIdList[0]].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) &&
		s.GeneralConfig.GetPresentConfig().ScoreCheckBegin < time.Now().Unix() {
		if s.GeneralConfig.GetPresentConfig().IntimatePresentSwitch {
			sendExtend.ScoreTypeMap, sendExtend.IdentityTypeMap, err = s.ScoreTypeMgr.GetScoreTypeMapWithBusinessType(ctx, sendUid, realUidList, sendExtend.BusinessType)
		} else {
			sendExtend.ScoreTypeMap, sendExtend.IdentityTypeMap, err = s.ScoreTypeMgr.GetScoreTypeMap(ctx, 0, sendUid, realUidList)
		}
		if err != nil {
			log.ErrorWithCtx(ctx, "preSendPresent - FillScoreType fail , uid: %+v", sendUid)
			return err
		}
		log.DebugWithCtx(ctx, "GetScoreTypeMap %v", sendExtend.ScoreTypeMap)
	} else {
		sendExtend.ScoreTypeMap = make(map[uint32]uint32)
		for _, item := range realUidList {
			sendExtend.ScoreTypeMap[item] = 1
		}
	}

	//加速卡信息, 如果有T豆礼物才要考虑
	for _, item := range sendExtend.presentConfigMap {
		if item.GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
			resp, err := client.BackpackFuncCli.BatchGetUserFuncCardUse(ctx, realUidList)
			if err != nil {
				log.ErrorWithCtx(ctx, "preSendPresent - GetPresentConfigByIdList fail , uid: %+v", sendUid)
				continue
			}

			for uid, item := range resp {
				sendExtend.userBonusCardMap[uid] = item
			}
			break
		}
	}

	// 限时礼物信息
	if sendExtend.presentConfigMap[itemIdList[0]].GetExtend().GetTag() == uint32(ga.PresentTagType_PRESENT_TAG_CUSTOMIZED) {
		resp, err := client.PresentExtraCli.CheckCustomizedGift(ctx, &presentextraconf.CheckCustomizedGiftReq{
			Uid: sendUid,
			Id:  itemIdList[0],
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "preSendPresent - CheckCustomizedGift fail , uid: %+v", sendUid)
			return err
		}
		// 暂时先不处理
		if !resp.GetIsAble() {
			return protocol.NewExactServerError(nil, status.ErrUserPresentConfigParam)
		}
		presentResp, err := client.PresentCli.GetPresentConfigById(ctx, resp.GetPrimaryGiftId())
		if err != nil {
			log.ErrorWithCtx(ctx, "preSendPresent - GetPresentConfigById fail , uid: %+v", sendUid)
			return err
		}

		sendExtend.primaryCustomGift = presentResp.GetItemConfig()
	}

	// 用userprofile更新下map
	for uid, item := range sendExtend.ukwInfoMap {
		if uid != item.GetUid() {
			sendExtend.fake2RealUidMap[item.GetUid()] = uid
			sendExtend.real2FakeUidMap[uid] = item.GetUid()
		}
	}

	// 一对一，且对方是真实uid，则返回真实信息
	if isReal {
		uid := targetUidList[0]
		sendExtend.ukwInfoMap[targetUidList[0]] = &ga.UserProfile{
			Uid:          sendExtend.targetUserMap[uid].GetUid(),
			Account:      sendExtend.targetUserMap[uid].GetUsername(),
			Nickname:     sendExtend.targetUserMap[uid].GetNickname(),
			AccountAlias: sendExtend.targetUserMap[uid].GetAlias(),
			Sex:          uint32(sendExtend.targetUserMap[uid].GetSex()),
		}
	}

	return
}

func (s *CommonPresentMiddlewareMgr) genOrderWithItemList(sendUid uint32, timeVal time.Time, itemList []*pb.PresentItemInfo) (string, map[string]*PayOrderInfo) {
	//order_id
	strMicroTime := s.getStrMicroTime(timeVal)
	uniqOrderId := s.createOrderId(0, sendUid, 11, 0, strMicroTime)

	orderMap := make(map[string]*PayOrderInfo, 0)

	for _, j := range itemList {
		orderId := fmt.Sprintf("%s_%v_%d", uniqOrderId, j.GetUid(), j.GetItemId())
		orderMap[orderId] = &PayOrderInfo{targetUid: j.GetUid(), orderId: orderId, giftId: j.GetItemId(), count: j.GetCount()}
	}
	return uniqOrderId, orderMap
}

func (s *CommonPresentMiddlewareMgr) genOrderWithUidList(sendUid, itemId, count uint32, timeVal time.Time, itemList []uint32) (string, map[string]*PayOrderInfo) {
	//order_id
	strMicroTime := s.getStrMicroTime(timeVal)
	uniqOrderId := s.createOrderId(0, sendUid, 11, 0, strMicroTime)

	orderMap := make(map[string]*PayOrderInfo, 0)

	for _, j := range itemList {
		orderId := fmt.Sprintf("%s_%v", uniqOrderId, j)
		orderMap[orderId] = &PayOrderInfo{targetUid: j, orderId: orderId, giftId: itemId, count: count}
	}
	return uniqOrderId, orderMap
}

func (s *CommonPresentMiddlewareMgr) genOrderWithUid(sendUid, uid, itemId, count uint32, timeVal time.Time) (string, map[string]*PayOrderInfo) {
	//order_id
	strMicroTime := s.getStrMicroTime(timeVal)
	uniqOrderId := s.createOrderId(0, sendUid, 11, 0, strMicroTime)

	orderMap := make(map[string]*PayOrderInfo, 0)
	orderId := fmt.Sprintf("%s_%v", uniqOrderId, uid)
	orderMap[orderId] = &PayOrderInfo{targetUid: uid, orderId: orderId, giftId: itemId, count: count}

	return uniqOrderId, orderMap
}

func (s *PresentMiddlewareMgr) freezeCurrencyWithGiftList(ctx context.Context, sendExtend *baseSendExtend, presentSource uint32) (remainCurrency int64, remainTbean int64, err error) {
	payOrderList := sendExtend.orderMap
	sendUser := sendExtend.sendUser
	uniqOrderId := sendExtend.uniqOrderId
	timeVal := sendExtend.nowTs

	if len(payOrderList) == 0 {
		log.ErrorWithCtx(ctx, "freezeCurrency -- ErrUserPresentInvalidTargetUserSize , uid:%v, orderId:%s",
			sendUser.GetUid(), uniqOrderId)
		return 0, 0, protocol.NewExactServerError(nil, status.ErrUserPresentInvalidTargetUserSize)
	}

	// 这里加一个秒级订单锁，防止真的有订单重复的情况
	ok, err := cache.PresentMiddlewareCacheClient.LockOrder(ctx, uniqOrderId, time.Second)
	if !ok {
		if err != nil {
			log.ErrorWithCtx(ctx, "freezeCurrency -- LockOrder error , uid:%v, orderId:%s, err:%v", sendUser.GetUid(), uniqOrderId, err)
		} else {
			log.ErrorWithCtx(ctx, "freezeCurrency -- LockOrder Failed , uid:%v, orderId:%s", sendUser.GetUid(), uniqOrderId)
		}
		return 0, 0, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	redDiamondFac := s.PayFactory.GetPayer(uint32(pay.PayType_Rediamond))
	tbeanFac := s.PayFactory.GetPayer(uint32(pay.PayType_Tbean_Old))

	AppId := "TT_HZ"
	if presentSource == uint32(pb.PresentSourceType_PRESENT_SOURCE_FELLOW) {
		AppId = "TT_ZY"
	}

	freezeCurrencyReq := &pay.FreezeReq{
		FreezeUser:    sendUser,
		FreezeTime:    timeVal,
		AppId:         AppId,
		UniqueOrderId: uniqOrderId,
	}
	freezeCurrencyReq.OrderInfoList = make(map[string]*pay.OrderInfo)

	freezeTbeanReq := &pay.FreezeReq{
		FreezeUser:    sendUser,
		FreezeTime:    timeVal,
		AppId:         AppId,
		UniqueOrderId: uniqOrderId,
	}
	freezeTbeanReq.OrderInfoList = make(map[string]*pay.OrderInfo)

	for _, order := range payOrderList {
		if sendExtend.presentConfigMap[order.giftId].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {
			freezeCurrencyReq.OrderInfoList[order.orderId] = &pay.OrderInfo{
				OrderId:       order.orderId,
				TargetUid:     order.targetUid,
				PresentConfig: sendExtend.presentConfigMap[order.giftId],
				Count:         order.count,
				Reason:        "赠送礼物",
			}
		}

		if sendExtend.presentConfigMap[order.giftId].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
			freezeTbeanReq.OrderInfoList[order.orderId] = &pay.OrderInfo{
				OrderId:       order.orderId,
				TargetUid:     order.targetUid,
				PresentConfig: sendExtend.presentConfigMap[order.giftId],
				Count:         order.count,
				Reason:        "赠送礼物",
			}
		}

	}

	if len(freezeCurrencyReq.OrderInfoList) > 0 {
		resp, err := redDiamondFac.Freeze(ctx, freezeCurrencyReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "freezeCurrency -- freezeCurrency err , uid:%v, orderId:%s",
				sendUser.Uid, uniqOrderId)
			return 0, 0, err
		}

		for _, value := range resp.OrderResp {
			if value.Err != nil {
				return 0, 0, value.Err
			}
		}

		for _, order := range payOrderList {
			if _, ok := resp.OrderResp[order.orderId]; ok {
				order.dealToken = resp.OrderResp[order.orderId].DealToken
				order.tbTime = resp.OrderResp[order.orderId].TbeanSystemTime
				order.payOrderId = resp.OrderResp[order.orderId].PayOrderId
			}
		}

		remainCurrency = resp.Balance
	} else {
		remainCurrency = -1
	}

	if len(freezeTbeanReq.OrderInfoList) > 0 {
		resp, err := tbeanFac.Freeze(ctx, freezeTbeanReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "freezeCurrency -- freezeTbean err , uid:%v, orderId:%s",
				sendUser.Uid, uniqOrderId)
			return 0, 0, err
		}

		for _, value := range resp.OrderResp {
			if value.Err != nil {
				log.ErrorWithCtx(ctx, "freezeCurrency -- freezeTbean err , uid:%v, orderId:%s", sendUser.Uid, value.OrderId)
				return 0, 0, value.Err
			}
		}

		for _, order := range payOrderList {
			if _, ok := resp.OrderResp[order.orderId]; ok {
				order.dealToken = resp.OrderResp[order.orderId].DealToken
				order.tbTime = resp.OrderResp[order.orderId].TbeanSystemTime
				order.payOrderId = resp.OrderResp[order.orderId].PayOrderId
			}
		}

		remainTbean = resp.Balance
	} else {
		remainTbean = -1
	}

	return remainCurrency, remainTbean, nil
}

func (s *CommonPresentMiddlewareMgr) tryFreeze(ctx context.Context, sendExtend *baseSendExtend, itemId, itemCount, itemSource, useItemId, sendMethod, businessType uint32) (remainCurrency, remainTbean int64, SourceRemain, realItemSource, expireTime uint32, err error) {

	realItemSource = itemSource
	sendUser := sendExtend.sendUser
	presentConfig := sendExtend.presentConfigMap[itemId]
	uniqQrderId := sendExtend.uniqOrderId
	//timeVal := sendExtend.nowTs
	//payOrderList := sendExtend.orderMap

	// 考虑一下背包优先的逻辑：如果背包物品够扣就走背包，否则走购买;背包优先传入的是礼物的source_id,匹配的时候需要注意
	// 防止混乱这部分就先判断，记住userpresent没有背包优先类型，所以调sendPresent前，一定要先把实际送礼方式收敛到背包/购买
	if uint32(pb.PresentSourceType_PRESENT_SOURCE_PACKAGE_FIRST) == realItemSource {
		backpackList, rErr := client.BackpackCli.GetUserBackpack(ctx, sendUser.GetUid())
		if rErr != nil {
			log.ErrorWithCtx(ctx, "PresentMiddlewareMgr -- GetUserBackpack fail. uid:%v err:%v", sendUser.GetUid(), err)
			return remainCurrency, remainTbean, SourceRemain, realItemSource, expireTime, rErr
		}
		realItemSource = uint32(pb.PresentSourceType_PRESENT_SOURCE_BUY)
		for _, item := range backpackList.GetUserItemList() {
			if item.GetSourceId() == presentConfig.GetItemId() && item.ItemType == uint32(backpackPB.PackageItemType_BACKPACK_PRESENT) &&
				item.ItemCount >= itemCount {
				// 够扣，走背包去扣
				realItemSource = uint32(pb.PresentSourceType_PRESENT_SOURCE_PACKAGE)
				break
			}
		}
	}

	// 购买，走unify服务的freezeCurrency并返回remainCurrency
	if uint32(pb.PresentSourceType_PRESENT_SOURCE_BUY) == realItemSource {
		currency, tbean, err := s.freezeCurrencyWithGiftList(ctx, sendExtend, sendMethod)
		if err != nil {
			log.ErrorWithCtx(ctx, "PresentMiddlewareMgr -- freezeCurrency fail. uid:%v item_id:%v price:%v total_item_count:%v "+
				"uniq_order_id %s err:%v", sendUser.GetUid(), presentConfig.GetItemId(), presentConfig.GetPrice(), itemCount, uniqQrderId, err)
			return remainCurrency, remainTbean, SourceRemain, realItemSource, expireTime, err
		}
		// T豆余额，用于客户端更新t豆显示
		remainCurrency = currency
		remainTbean = tbean
	} else if uint32(pb.PresentSourceType_PRESENT_SOURCE_PACKAGE) == realItemSource {
		balance, expire, err := s.freezeBackpackItem(ctx, sendExtend, itemCount, useItemId, itemId, businessType, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "PresentMiddlewareMgr -- freezeBackpackItem fail. uid:%v item_id:%v price:%v total_item_count:%v "+
				"uniq_order_id %s err:%v", sendUser.Uid, presentConfig.GetItemId(), presentConfig.GetPrice(), itemCount, uniqQrderId, err)
			return remainCurrency, remainTbean, SourceRemain, realItemSource, expireTime, err
		}
		// 物品余额，用于客户端更新背包显示
		SourceRemain = balance
		expireTime = expire
		// -1代表t豆余额没有变化
		// 背包送礼的话，t豆不会改变
		remainTbean = -1
	} else {
		return remainCurrency, remainTbean, SourceRemain, realItemSource, expireTime, protocol.NewExactServerError(nil, status.ErrPresentSourceInvalid)
	}
	return remainCurrency, remainTbean, SourceRemain, realItemSource, expireTime, nil
}

func (s *CommonPresentMiddlewareMgr) sendPresent(ctx context.Context, sendExtend *baseSendExtend,
	clientInfo *pb.PresentClientInfo, in *pb.SendPresentReq) (sucOrders map[string]*PayOrderInfo, err error) {
	sucOrders = make(map[string]*PayOrderInfo)
	sendUser := sendExtend.sendUser
	itemSendList := sendExtend.orderMap
	extendInfo := sendExtend.extendInfo
	timeVal := uint32(sendExtend.nowTs.Unix())

	if len(itemSendList) == 0 {
		return nil, protocol.NewExactServerError(nil, status.ErrUserPresentInvalidTargetUserSize)
	}

	var channelGuildId uint32
	//1 : 公会房  4：公会公开厅（娱乐房）
	if sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType() == uint32(channelPB_.ChannelType_GUILD_TYPE) || sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType() == uint32(channelPB_.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		channelGuildId = sendExtend.channelSimpleInfo.GetChannelSimple().GetBindId()
	}

	baseAddRich := uint32(0)
	valueMap := make(map[uint32]uint32, 0)

	for _, item := range itemSendList {
		total := sendExtend.presentConfigMap[item.giftId].GetPrice() * item.count
		if sendExtend.presentConfigMap[item.giftId].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
			// 如果是t豆，额外计算一下倍率
			total = s.calRichRatio(timeVal, item.giftId, total)
		}
		// 锁了不加
		if !s.isRecordSenderRich(ctx, sendUser.GetUid(), sendExtend.presentConfigMap[item.giftId].GetPriceType()) {
			total = 0
		}
		baseAddRich += total
		//append(itemList, &valueItem{Uid: item.Uid, AddValue:})
		valueMap[item.targetUid] += sendExtend.presentConfigMap[item.giftId].GetPrice() * item.count
	}

	//baseAddCharm := itemCfg.GetCharm() * itemCount
	extendInfo.baseRichValue = baseAddRich
	extendInfo.realAddRichValue = baseAddRich
	extendInfo.sendUserCurrRichValue = uint64(baseAddRich)
	extendInfo.bIsSendUserRichLevelChanged = false

	userBonusCardMap := make(map[uint32]*UserBonusCardEffect, 0)
	//sendBonusCardMap := make(map[uint32]*UserBonusCardEffect, 0)
	s.calBonusCardEffects(valueMap, userBonusCardMap, uint32(backpackPB.PackageItemType_BACKPACK_CARD_CHARM_EXP), sendExtend)
	sendExtend.timeCollect.AddTimeEvent("calBonusCardEffects")

	itemCountMap := make(map[uint32]uint32, 0)
	for _, item := range itemSendList {
		itemCountMap[item.giftId] += item.count
	}

	sendExtend.extendInfo.sendInfoMap = make(map[uint32]*jsonSendUser)
	bonusCardResp := sendExtend.userBonusCardMap[sendUser.GetUid()]
	cardTimes := uint32(100)
	for id, count := range itemCountMap {
		addValue := sendExtend.presentConfigMap[id].GetPrice() * count
		if !s.isRecordSenderRich(ctx, sendUser.GetUid(), sendExtend.presentConfigMap[id].GetPriceType()) {
			addValue = 0
		}
		addValue = s.calRichRatio(timeVal, id, addValue)

		log.DebugWithCtx(ctx, "id %d , GetPriceType %d , addValue %d", id, sendExtend.presentConfigMap[id].GetPriceType(), addValue)
		cardTimes = s.calUserBonusCardEffect(ctx, *sendUser, uint32(len(sendExtend.targetUserMap)), addValue, true, sendExtend, id, bonusCardResp)
	}
	extendInfo.realAddRichValue = extendInfo.baseRichValue * cardTimes / 100
	sendExtend.timeCollect.AddTimeEvent("calUserBonusCardEffect")

	for k, v := range sendExtend.extendInfo.sendInfoMap {
		fmt.Println(k, v)
	}

	for _, user := range userBonusCardMap {
		extend := &TargetExtendInfo{}
		extend.uid = user.uid
		extend.realAddCharm = user.finalValue
		extend.baseCharm = user.addValue
		extend.score = valueMap[extend.uid] / 2 // 这里先用价值/2替代一下？
		extend.recvUserCurrCharmValue = 0
		extend.bIsSendUserRichLevelChanged = false
		extendInfo.targetInfoMap[user.uid] = extend
		log.DebugWithCtx(ctx, "sendPresent -- user %d , baseCharm %d , realAddCharm %d", user.uid, extend.baseCharm, extend.realAddCharm)
	}

	//addRichOnce := extendInfo.realAddRichValue / uint32(len(targetUserMap))
	relayChannelId := uint32(0)

	if sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType() == uint32(channelPB_.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
		resp, err := client.OffcialLiveCli.GetRelay(ctx, sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId())
		if err != nil {
			log.ErrorWithCtx(ctx, "sendPresent -- GetRelay fail ,  channelId %d err %v", sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId(), err)
		}
		relayChannelId = resp.GetChannelId()
	}

	wg := sync.WaitGroup{}
	lock := sync.Mutex{}
	extendInfo.targetInfoMutex = sync.Mutex{}

	//红钻加魅力值财富值。T豆走kafka事件加
	//全麦合成一次
	if in.GetTargetInfo().GetMultiTarget() != nil {
		for itemId, count := range itemCountMap {
			itemId := itemId
			count := count
			if sendExtend.presentConfigMap[itemId].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {
				s.batchAddRedDiamondCharmAndRich(ctx, sendUser.GetUid(), sendExtend.uniqOrderId, sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId(),
					channelGuildId, sendUser.GetCurrentGuildId(), sendExtend.presentConfigMap[itemId].GetPriceType(),
					baseAddRich, sendExtend.presentConfigMap[itemId].Charm*count/uint32(len(sendExtend.targetUserMap)),
					itemId, sendExtend.targetUserMap, extendInfo)

				if in.GetPushInfo().GetChannelPushType() != uint32(pb.PushInfo_Channel_ALLMIC) {
					extendInfo.sendInfoMap[itemId].RichValue = extendInfo.sendInfoMap[itemId].RichValue / int32(len(itemSendList))
				}
			}
		}
	}

	for _, item := range itemSendList {
		if _, ok := sendExtend.presentConfigMap[item.giftId]; !ok {
			continue
		}

		item := item

		if in.GetTargetInfo().GetSingleTarget() != nil {
			//红钻加魅力值财富值。T豆走kafka事件加
			if sendExtend.presentConfigMap[item.giftId].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {
				s.batchAddRedDiamondCharmAndRich(ctx, sendUser.GetUid(), item.orderId, sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId(),
					channelGuildId, sendUser.GetCurrentGuildId(), sendExtend.presentConfigMap[item.giftId].GetPriceType(),
					sendExtend.presentConfigMap[item.giftId].RichValue*item.count, sendExtend.presentConfigMap[item.giftId].RichValue*item.count,
					sendExtend.presentConfigMap[item.giftId].ItemId, map[uint32]*accountPB.UserResp{item.targetUid: sendExtend.targetUserMap[item.targetUid]}, extendInfo)
			}
		}

		isVirtualLive := s.checkLiveStatus(ctx, sendExtend.channelSimpleInfo.GetChannelSimple(), relayChannelId)

		sendReq := presentPB.SendPresentReq{
			Uid:              sendUser.GetUid(),
			ItemId:           item.giftId,
			OrderId:          item.orderId,
			ItemConfig:       sendExtend.presentConfigMap[item.giftId],
			ChannelId:        sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId(),
			ChannelType:      sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType(),
			ChannelName:      sendExtend.channelSimpleInfo.GetChannelSimple().GetName(),
			GuildId:          channelGuildId,
			ItemCount:        item.count,
			SendTime:         timeVal,
			OptInvalid:       in.GetIsOptValid(),
			UserFromIp:       clientInfo.GetServiceInfo().GetClientIp(),
			ItemSource:       in.GetItemSource(),
			AsyncFlag:        true,
			AppId:            clientInfo.GetAppId(),
			MarketId:         clientInfo.GetMarketId(),
			ChannelDisplayId: sendExtend.channelSimpleInfo.GetChannelSimple().GetDisplayId(),
			SendSource:       in.GetSendSource(),
			SendPlatform:     clientInfo.GetServiceInfo().GetClientType(),
			BatchType:        in.GetBatchType(),
			AddRich:          extendInfo.realAddRichValue / uint32(len(itemSendList)),
			BindChannelId:    relayChannelId,
			SendMethod:       in.GetSendMethod(),
			TargetUid:        item.targetUid,
			DealToken:        item.dealToken,
			ChannelGameId:    in.GetChannelGameId(),
			IsVirtualLive:    isVirtualLive,
			ScoreType:        sendExtend.ScoreTypeMap[item.targetUid],
			SendChannelId:    in.GetSendChannelId(),
			GiverGuildId:     sendUser.GetCurrentGuildId(),
			ReceiverGuildId:  sendExtend.targetUserMap[item.targetUid].GetCurrentGuildId(),
			BusinessType:     in.GetBusinessType(),
			IdentityType:     sendExtend.IdentityTypeMap[item.targetUid],
		}

		//产品改动，不是送给本房间主播的礼物不记为房间弹幕游戏送出
		if item.targetUid != sendExtend.channelSimpleInfo.GetChannelSimple().GetBindId() {
			sendReq.ChannelGameId = 0
		}

		if sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege() != nil {
			sendReq.FromUkwAccount = sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege().GetAccount()
			sendReq.FromUkwNickname = sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege().GetNickname()
		}
		if sendExtend.ukwInfoMap[item.targetUid].GetPrivilege() != nil {
			sendReq.ToUkwAccount = sendExtend.ukwInfoMap[item.targetUid].GetPrivilege().GetAccount()
			sendReq.ToUkwNickname = sendExtend.ukwInfoMap[item.targetUid].GetPrivilege().GetNickname()
		}

		if sendExtend.primaryCustomGift != nil {
			sendReq.ItemId = sendExtend.primaryCustomGift.GetItemId()
			sendReq.ItemConfig = sendExtend.primaryCustomGift
		}

		log.InfoWithCtx(ctx, "PresentMiddlewareMgr -- SendPresent %+v ", sendReq)

		if item.tbTime != "" {
			sendTime, _ := time.ParseInLocation("2006-01-02 15:04:05", item.tbTime, time.Local)
			sendReq.SendTime = uint32(sendTime.Unix())
		}

		if !in.WithPay {
			dealToken, err := UpdateDealTokenInfo(ctx, item.dealToken, item.orderId, false)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendPresent UpdateDealTokenInfo fail , err %v , uid %d", err, sendUser.GetUid())
				//deal_token处理失败，暂时不返回错误，防止送礼失败;deal_token相关链路稳定后可以正常返回错误
				//continue
			}
			sendReq.DealToken = dealToken
		}

		_, targetOk := extendInfo.targetInfoMap[item.targetUid]
		if targetOk && s.isRecordTargetCharm(ctx, item.targetUid, sendExtend.presentConfigMap[item.giftId].GetPriceType()) {
			sendReq.AddCharm = extendInfo.targetInfoMap[item.targetUid].realAddCharm
			log.DebugWithCtx(ctx, "sendPresent -- targetUid %d AddCharm %d", item.targetUid, sendReq.AddCharm)
		} else {
			sendReq.AddCharm = 0
			log.DebugWithCtx(ctx, "sendPresent -- targetUid %d AddCharm %d", item.targetUid, sendReq.AddCharm)
		}
		tmpOrder := item

		wg.Add(1)
		go func() {
			defer func() {
				wg.Done()
				lock.Unlock()
			}()

			err := client.PresentCli.SendPresent(ctx, &sendReq)
			if err == nil {
				// 送礼成功，完成任务，更新专属礼物信息
				s.HandleHandleMission(ctx, sendExtend, &sendReq)
				s.HandleReportCustomPresentSend(ctx, sendExtend, &sendReq, item.giftId)
			}

			lock.Lock()
			if err == nil {
				sucOrders[sendReq.GetOrderId()] = tmpOrder
				if sendExtend.presentConfigMap[tmpOrder.giftId].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
					sendExtend.totalPrice += sendExtend.presentConfigMap[tmpOrder.giftId].GetPrice()
				}
			} else {
				log.ErrorWithCtx(ctx, "sendPresent -- userpresent.SendPresent fail ,  uid %d cid %d err %v", sendReq.GetUid(), sendReq.GetChannelId(), err)
			}
		}()
	}

	//开协程去同步获取送礼结果
	wg.Wait()

	return sucOrders, err
}

func (s *CommonPresentMiddlewareMgr) calUserBonusCardEffect(ctx context.Context, sendUser accountPB.UserResp, userCount uint32, addValue uint32,
	isSend bool, sendExtend *baseSendExtend, itemId uint32, resp []*backpack_func_card.FuncCardCfg) (cardTimes uint32) {
	cardType := uint32(0)
	cardTimes = 100
	if isSend {
		cardType = uint32(backpackPB.PackageItemType_BACKPACK_CARD_RICH_EXP)
	} else {
		cardType = uint32(backpackPB.PackageItemType_BACKPACK_CARD_CHARM_EXP)
	}

	jsonSendUser := jsonSendUser{
		RichValue:                int32(addValue),
		RichValuePrefix:          "",
		MemberContribution:       0,
		MemberContributionPrefix: "",
		BonusMsg:                 "",
	}

	sendExtend.extendInfo.sendInfoMap[itemId] = &jsonSendUser

	for _, cardEffect := range resp {
		if cardEffect.CardType != backpack_base.PackageItemType(cardType) {
			continue
		}
		if 100 > cardEffect.CardTimes {
			continue
		}

		finalValue := addValue * cardEffect.CardTimes / 100
		if cardEffect.CardType == backpack_base.PackageItemType_BACKPACK_CARD_RICH_ACCELERATOR {
			if userCount >= 1 {
				perUserBonusValue := (finalValue - addValue) / userCount
				if addValue != 0 {
					richValueBonusMsg := fmt.Sprintf("财富卡%d.%d倍额外加成 +%d", cardEffect.CardTimes/100,
						cardEffect.CardTimes%100/10, perUserBonusValue)
					sendExtend.extendInfo.sendInfoMap[itemId].BonusMsg = richValueBonusMsg
				}

				sendExtend.extendInfo.sendInfoMap[itemId].RichValue = int32(finalValue)

				cardTimes = cardEffect.CardTimes

				break
			} else {
				continue
			}
		}
	}
	return
}

// 不存在复数人送礼的情况，所以请求复数用户的加速卡情况，一定是收礼人
func (s *PresentMiddlewareMgr) calBonusCardEffects(targetMap map[uint32]uint32,
	userCardEffectMap map[uint32]*UserBonusCardEffect, expType uint32, extend *baseSendExtend) {
	for key, value := range targetMap {
		resp := extend.userBonusCardMap[key]

		userCardEffectMap[key] = &UserBonusCardEffect{uid: key, addValue: value, finalValue: value, bonusMsg: ""}

		log.DebugWithCtx(context.Background(), "calBonusCardEffects -- key %d , value %d", key, value)

		if len(resp) == 0 {
			continue
		}

		for _, cardEffect := range resp {
			if cardEffect.CardType != backpack_base.PackageItemType(expType) {
				continue
			}

			if 100 > cardEffect.CardTimes {
				continue
			}

			userCardEffectMap[key].finalValue = userCardEffectMap[key].finalValue * cardEffect.CardTimes / 100

			if cardEffect.CardType == backpack_base.PackageItemType_BACKPACK_CARD_RICH_ACCELERATOR {
				userCardEffectMap[key].bonusMsg = fmt.Sprintf("财富卡%d.%d倍额外加成 +%d", cardEffect.CardTimes/100,
					cardEffect.CardTimes%100/10, userCardEffectMap[key].finalValue-userCardEffectMap[key].addValue)
			}

			break
		}
	}
}

// 这里的支付需要通用一点
func (s *CommonPresentMiddlewareMgr) commitPayOrder(ctx context.Context, sendExtend *baseSendExtend,
	sendMethod uint32, presentSource uint32) (err error) {
	appId := "TT_HZ"
	if sendMethod == uint32(pb.PresentSendMethodType_PRESENT_TYPE_FELLOW) {
		appId = "TT_ZY"
	}

	// 旧的支付不需要填freezeUser
	commitReq := &pay.CommitReq{
		AppId: appId,
	}

	//  目前只有T豆需要commit，可以这么写;不然还要考虑不同情况分开处理

	commitReq.OrderDetailList = make(map[string]*pay.OrderDetail)
	for _, order := range sendExtend.sucOrders {
		if sendExtend.presentConfigMap[order.giftId].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
			commitReq.OrderDetailList[order.orderId] = &pay.OrderDetail{
				OrderId:         order.orderId,
				PayOrderId:      order.payOrderId,
				TbeanSystemTime: order.tbTime,
				DealToken:       order.dealToken,
				Err:             nil,
			}
		}
	}

	resp, err := s.PayFactory.GetPayer(uint32(pay.PayType_Tbean_Old)).Commit(ctx, commitReq)

	// 由于分了订单，所以还要检查每个订单的成功失败，这里就只有一笔，有报错就直接返回错误
	for _, item := range resp.OrderResp {
		if item.Err != nil {
			return item.Err
		}
	}

	return err
}

func (s *CommonPresentMiddlewareMgr) genOrderId(sendExtend *baseSendExtend, in *pb.SendPresentReq) error {
	// 生成支付order
	sendExtend.orderMap = make(map[string]*PayOrderInfo)
	if in.WithPay {
		switch in.GetTargetInfo().GetTarget().(type) {
		case *pb.PresentTargetInfo_SingleTarget:
			uid := sendExtend.fake2RealUidMap[in.GetTargetInfo().GetSingleTarget().GetUid()]
			sendExtend.uniqOrderId, sendExtend.orderMap = s.genOrderWithUid(in.SendUid, uid,
				in.GetTargetInfo().GetSingleTarget().GetItemId(), in.GetTargetInfo().GetSingleTarget().GetCount(), sendExtend.nowTs)
		case *pb.PresentTargetInfo_MultiTarget:
			realUidList := make([]uint32, 0)
			if in.GetTargetInfo().GetMultiTarget().GetUserInfo() == nil {
				realUidList := make([]uint32, 0)
				for _, item := range in.GetTargetInfo().GetMultiTarget().GetUid() {
					realUidList = append(realUidList, sendExtend.fake2RealUidMap[item])
				}
				sendExtend.uniqOrderId, sendExtend.orderMap = s.genOrderWithUidList(in.SendUid, in.GetTargetInfo().GetMultiTarget().GetItemId(),
					in.GetTargetInfo().GetMultiTarget().GetCount(), sendExtend.nowTs, realUidList)
			} else {
				for _, item := range in.GetTargetInfo().GetMultiTarget().GetUserInfo() {
					realUidList = append(realUidList, sendExtend.fake2RealUidMap[item.GetUid()])
				}
				sendExtend.uniqOrderId, sendExtend.orderMap = s.genOrderWithUidList(in.SendUid, in.GetTargetInfo().GetMultiTarget().GetItemId(),
					in.GetTargetInfo().GetMultiTarget().GetCount(), sendExtend.nowTs, realUidList)
			}
		case *pb.PresentTargetInfo_MultiItem:
			sendExtend.uniqOrderId, sendExtend.orderMap = s.genOrderWithItemList(in.SendUid, sendExtend.nowTs, in.GetTargetInfo().GetMultiItem().GetItemInfo())
		}

		// 算一下总金额，如果超过两万要报错
		totalPrice := uint64(0)
		for _, order := range sendExtend.orderMap {
			if sendExtend.presentConfigMap[order.giftId].GetPriceType() != uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
				continue
			}
			totalPrice += uint64(sendExtend.presentConfigMap[order.giftId].GetPrice()) * uint64(order.count)
		}
		if totalPrice >= 2000000 {
			return protocol.NewExactServerError(nil, status.ErrUserPresentSpendOverLimit)
		}

	} else {
		switch in.GetTargetInfo().GetTarget().(type) {
		case *pb.PresentTargetInfo_SingleTarget:
			if in.GetTargetInfo().GetSingleTarget().GetOrderId() == "" {
				return protocol.NewExactServerError(nil, status.ErrUserPresentOrderNotExist)
			}
			sendExtend.uniqOrderId = in.GetTargetInfo().GetSingleTarget().GetOrderId()
			sendExtend.orderMap[in.GetTargetInfo().GetSingleTarget().GetOrderId()] = &PayOrderInfo{
				targetUid: sendExtend.fake2RealUidMap[in.GetTargetInfo().GetSingleTarget().GetUid()],
				orderId:   in.GetTargetInfo().GetSingleTarget().GetOrderId(),
				giftId:    in.GetTargetInfo().GetSingleTarget().GetItemId(),
				count:     in.GetTargetInfo().GetSingleTarget().GetCount(),
				dealToken: in.GetTargetInfo().GetSingleTarget().GetDealToken(),
			}
		case *pb.PresentTargetInfo_MultiTarget:
			sendExtend.uniqOrderId = in.GetTargetInfo().GetMultiTarget().GetUniqueOrderId()
			if in.GetTargetInfo().GetMultiTarget().GetUserInfo() == nil {
				if in.GetTargetInfo().GetMultiTarget().GetUniqueOrderId() == "" {
					return protocol.NewExactServerError(nil, status.ErrUserPresentOrderNotExist)
				}
				sendExtend.uniqOrderId = in.GetTargetInfo().GetMultiTarget().GetUniqueOrderId()
				for _, uid := range in.GetTargetInfo().GetMultiTarget().GetUid() {
					orderId := in.GetTargetInfo().GetMultiTarget().GetUniqueOrderId() + fmt.Sprintf("_%d", uid)
					sendExtend.orderMap[orderId] = &PayOrderInfo{
						targetUid: sendExtend.fake2RealUidMap[uid],
						orderId:   orderId,
						giftId:    in.GetTargetInfo().GetMultiTarget().GetItemId(),
						count:     in.GetTargetInfo().GetMultiTarget().GetCount(),
					}
				}
			} else {
				for _, item := range in.GetTargetInfo().GetMultiTarget().GetUserInfo() {
					orderId := item.GetOrderId()
					if item.GetOrderId() == "" {
						if in.GetTargetInfo().GetMultiTarget().GetUniqueOrderId() == "" {
							return protocol.NewExactServerError(nil, status.ErrUserPresentOrderNotExist)
						}
						orderId = in.GetTargetInfo().GetMultiTarget().GetUniqueOrderId() + fmt.Sprintf("_%d", item.GetUid())
					}
					sendExtend.orderMap[orderId] = &PayOrderInfo{
						targetUid: sendExtend.fake2RealUidMap[item.GetUid()],
						orderId:   orderId,
						dealToken: item.GetDealToken(),
						giftId:    in.GetTargetInfo().GetMultiTarget().GetItemId(),
						count:     in.GetTargetInfo().GetMultiTarget().GetCount(),
					}
				}
			}
		case *pb.PresentTargetInfo_MultiItem:
			for _, order := range in.GetTargetInfo().GetMultiItem().GetItemInfo() {
				if order.GetOrderId() == "" {
					return protocol.NewExactServerError(nil, status.ErrUserPresentOrderNotExist)
				}
				sendExtend.orderMap[order.OrderId] = &PayOrderInfo{
					targetUid: order.GetUid(),
					orderId:   order.GetOrderId(),
					giftId:    order.GetItemId(),
					count:     order.GetCount(),
					dealToken: order.GetDealToken(),
				}
			}
		}
	}
	return nil
}

func (s *CommonPresentMiddlewareMgr) fillBatchInfo(ctx context.Context, in *pb.SendPresentReq, targetUidList *[]uint32,
	serInfo *grpc.ServiceInfo) (err error) {
	// 如果是全麦，从麦位获取TargetUid
	// 如果是官频，要获取其转播房间的麦位
	micChannel := in.ChannelId
	channelInfo, sErr := client.ChannelCli.GetChannelSimpleInfo(ctx, in.SendUid, in.ChannelId)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- GetChannelSimpleInfo fail. uid:%v c:%v err:%v",
			in.SendUid, in.ChannelId, err)
		return err
	}
	if channelInfo.GetChannelType() == uint32(channelPB_.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
		tmpChannel, err := client.OffcialLiveCli.GetRelay(ctx, in.ChannelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "BeforeSendPresent -- GetChannelSimpleInfo fail. uid:%v c:%v err:%v",
				in.SendUid, in.ChannelId, err)
			return err
		}
		micChannel = tmpChannel.GetChannelId()
	}

	resp, err := s.channelMicCli.GetMicrList(ctx, micChannel, in.SendUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- GetMicrList fail. uid:%v c:%v err:%v",
			in.SendUid, in.ChannelId, err)
		return err
	}
	if in.TargetInfo.GetMultiTarget() == nil {
		return protocol.NewExactServerError(nil, status.ErrUserPresentInvalidTargetUserSize)
	}

	in.GetTargetInfo().GetMultiTarget().UserInfo = make([]*pb.TargetUserInfo, 0)

	for _, item := range resp.GetAllMicList() {
		if item.GetMicUid() == 0 || item.GetMicUid() == in.SendUid {
			continue
		}
		// 这里预留风控代码，后面可能会用到

		if checkRisk(ctx, in.GetSendUid(), item.GetMicUid(), serInfo.DeviceID) {
			continue
		}

		*targetUidList = append(*targetUidList, item.GetMicUid())
		in.TargetInfo.GetMultiTarget().UserInfo = append(in.TargetInfo.GetMultiTarget().UserInfo, &pb.TargetUserInfo{
			Uid:       item.GetMicUid(),
			OrderId:   "",
			DealToken: "",
		})
	}

	// 麦位上没有可以送礼的对象
	if len(in.TargetInfo.GetMultiTarget().GetUserInfo()) == 0 {
		return protocol.NewExactServerError(nil, status.ErrUserPresentInvalidTargetUserSize)
	}
	return nil
}

func checkRisk(ctx context.Context, uid, targetUid uint32, deviceId []byte) bool {
	tmpCtx, cancel := context.WithTimeout(ctx, time.Millisecond*100)
	defer cancel()
	_, err := client.RiskApiCli.CheckSendBackpackPresent(tmpCtx, uid, targetUid, deviceId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckSendBackpackPresent error , uid %d , err %v", uid, err)
	}
	//if risk {
	//	return true
	//}
	return false
}
