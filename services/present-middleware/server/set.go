package server

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/decoration"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	ga "golang.52tt.com/protocol/app"
	channelPB_ "golang.52tt.com/protocol/app/channel"
	present_go_logic "golang.52tt.com/protocol/app/present-go-logic"
	pushPb "golang.52tt.com/protocol/app/push"
	presentPB_ "golang.52tt.com/protocol/app/userpresent"
	"golang.52tt.com/protocol/common/status"
	accountPB "golang.52tt.com/protocol/services/account-go"
	backpack_func_card "golang.52tt.com/protocol/services/backpack-func-card"
	channelPB "golang.52tt.com/protocol/services/channelsvr"
	guildmemberlvPB "golang.52tt.com/protocol/services/guildMemberLvSvr"
	pb "golang.52tt.com/protocol/services/present-middleware"
	present_set "golang.52tt.com/protocol/services/present-set"
	presentPB "golang.52tt.com/protocol/services/userpresent"
	userpresentGoPb "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/present-middleware/models/presentPush"
	"golang.52tt.com/services/present-middleware/rpc/client"
	"strconv"
	"sync"
	"time"
)

func (s *SetPresentMiddlewareMgr) BeforeSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {
	// 类型断言
	out, ok := outInter.(*pb.SetSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.SetSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- para inter need *PresentSendBase type. ")
		return nil
	}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent ServiceInfoFromContext fail. req:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrUserPresentServiceInfoInvalid)
	}

	ctx = s.SetPresentGoCtx(ctx, in.GetSendUid())

	// 如果请求房没填，尝试填充ClientInfo
	if in.GetClientInfo() == nil {
		in.ClientInfo = &pb.PresentClientInfo{
			AppId:    0, // 默认tt
			MarketId: serviceInfo.MarketID,
			ServiceInfo: &pb.ServiceCtrlInfo{
				ClientIp:      strconv.Itoa(int(serviceInfo.ClientIP)),
				ClientType:    uint32(serviceInfo.ClientType),
				ClientVersion: serviceInfo.ClientVersion,
				DeviceId:      serviceInfo.DeviceID,
				TerminalType:  serviceInfo.TerminalType,
			}}
	} else {
		if in.GetClientInfo().GetServiceInfo() == nil {
			in.GetClientInfo().ServiceInfo = &pb.ServiceCtrlInfo{
				ClientIp:      strconv.Itoa(int(serviceInfo.ClientIP)),
				ClientType:    uint32(serviceInfo.ClientType),
				ClientVersion: serviceInfo.ClientVersion,
				DeviceId:      serviceInfo.DeviceID,
				TerminalType:  serviceInfo.TerminalType,
			}
		}
	}

	sendExtend.setId = in.GetEmperorSetId()
	// 如果填了sendTime，就跟传入的时间保持一致
	sendExtend.nowTs = time.Now()
	if in.GetSendTime() != 0 {
		sendExtend.nowTs = time.Unix(in.GetSendTime(), 0).Local()
	}

	sendExtend.userBonusCardMap = make(map[uint32][]*backpack_func_card.FuncCardCfg)
	out.ItemSource = in.ItemSource

	// 预处理，获得送礼要用的相关信息
	// 这里只会是一对一，多个礼物，所以直接获取multiItem

	if len(in.GetTargetInfo().GetItemInfo()) == 0 {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- invalid target user size. uid:%v c:%v err:%v")
		return protocol.NewExactServerError(nil, status.ErrUserPresentInvalidTargetUserSize)
	}

	targetUid := in.GetTargetInfo().GetItemInfo()[0].GetUid()
	for _, item := range in.GetTargetInfo().GetItemInfo() {
		if targetUid != item.GetUid() {
			log.ErrorWithCtx(ctx, "BeforeSendPresent -- targetUid not equal. uid:%v c:%v err:%v")
			return protocol.NewExactServerError(nil, status.ErrUserPresentInvalidTargetUserSize)
		}
	}

	itemList := make([]uint32, 0)
	for _, item := range in.GetTargetInfo().GetItemInfo() {
		itemList = append(itemList, item.GetItemId())
	}

	err = s.preSendPresent(ctx, in.SendUid, targetUid, itemList, in.ChannelId, in.GetEmperorSetId(), sendExtend)
	if err != nil {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- preSendPresent fail. uid:%v c:%v err:%v",
			in.SendUid, in.ChannelId, err)
		return err
	}

	err = s.genOrderId(sendExtend, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- genOrderId fail. uid:%v c:%v err:%v",
			in.SendUid, in.ChannelId, err)
		return err
	}

	for _, item := range sendExtend.presentConfigMap {
		err := s.checkPresentSetValid(item, in.ItemSource, in.GetSendUid())
		log.DebugWithCtx(ctx, "checkPresentValid res %v", err)
		if err != nil {
			sErr := checkPresentPrivilege(ctx, sendExtend.primaryCustomGift.GetItemId(), sendExtend.sendUser.GetUid(),
				uint32(sendExtend.nowTs.Unix()), item, err)
			return sErr
		}
	}

	// 如果是高价值的背包礼物，查一下来源
	needSourceItem := uint32(0)
	for _, item := range itemList {
		itemCfg := sendExtend.presentConfigMap[item]
		if itemCfg.GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) && itemCfg.GetPrice() >= 100000 &&
			itemCfg.GetPrice() < 999999 && in.GetItemSource() == uint32(pb.PresentSourceType_PRESENT_SOURCE_PACKAGE) {
			needSourceItem = itemCfg.GetItemId()
		}
	}

	if needSourceItem != 0 {
		bg, err := client.BackpackCli.GetUserBackpack(ctx, in.GetSendUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "BeforeSendPresent -- GetUserBackpack fail. uid:%v c:%v err:%v",
				in.SendUid, in.ChannelId, err)
			return err
		}

		log.DebugWithCtx(ctx, "BeforeSendPresent GetUserBackpack uid %d bg len %d", in.GetSendUid(), len(bg.GetUserItemList()))

		for _, item := range bg.GetUserItemList() {
			if item.GetSourceId() == needSourceItem {
				sendExtend.bgPresentSource = item.GetSourceType()
				break
			}
		}
	}

	return err
}

func (s *SetPresentMiddlewareMgr) preSendPresent(ctx context.Context, sendUid uint32, targetUid uint32, itemIdList []uint32, channelId uint32, setId uint32, sendExtend *baseSendExtend) (err error) {
	wg := sync.WaitGroup{}

	targetUidList := []uint32{targetUid}
	_, err = checkOldFakeUid(ctx, targetUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "preSendPresent checkOldFakeUid err : %v, uid:%d", err, sendUid)
		return err
	}

	// 首先要对targetUidList做一次转换
	fake2RealUidMap, real2FakeUidMap, realUidList, err := getRealUidByFakeUid(ctx, append(targetUidList, sendUid))
	if err != nil {
		log.ErrorWithCtx(ctx, "preSendPresent getRealUidByFakeUid err : %v, uid:%d", err, sendUid)
		return err
	}
	sendExtend.fake2RealUidMap = fake2RealUidMap
	sendExtend.real2FakeUidMap = real2FakeUidMap

	sendUid = fake2RealUidMap[sendUid]
	targetUid = fake2RealUidMap[targetUid]

	//送礼人用户信息
	wg.Add(3)
	go func() {
		defer wg.Done()
		var sErr error
		sendExtend.targetUserMap, sErr = s.accountCli.GetUsersMap(ctx, realUidList)
		if sErr != nil {
			err = sErr
			return
		}
	}()

	ukwInfoMap := make(map[uint32]*ga.UserProfile)
	//用户神秘人信息
	go func() {
		defer wg.Done()

		var sErr error
		ukwInfoMap, sErr = client.UserProfileCli.BatchGetUserProfileV2(ctx, realUidList, true)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "preSendPresent GetUsersMap err : %v, uid:%d", sErr, sendUid)
			err = sErr
		}

	}()

	//礼物信息
	go func() {
		defer wg.Done()
		tmpResp, sErr := client.PresentCli.GetPresentConfigByIdList(ctx, 0, itemIdList, 0)
		if sErr != nil {
			err = sErr
			return
		}
		sendExtend.presentConfigMap = make(map[uint32]*presentPB.StPresentItemConfig)

		for _, item := range tmpResp.GetItemList() {
			sendExtend.presentConfigMap[item.GetItemId()] = item
		}
	}()

	if channelId != 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			channelInfo, sErr := s.channelCli.GetChannelSimpleInfo(ctx, sendUid, channelId)
			if sErr != nil {
				err = sErr
				return
			}
			sendExtend.channelSimpleInfo = &channelPB.GetChannelSimpleInfoResp{ChannelSimple: channelInfo}
		}()
	} else {
		sendExtend.channelSimpleInfo = &channelPB.GetChannelSimpleInfoResp{ChannelSimple: &channelPB.ChannelSimpleInfo{}}
	}

	if setId != 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			setInfo, sErr := client.PresentSetCli.GetEmperorSetInfo(ctx, &present_set.GetEmperorSetInfoReq{
				SetId: setId,
			})
			if sErr != nil {
				err = sErr
				return
			}
			sendExtend.setConfig = setInfo.GetEmperorSet()
		}()
	} else {
		sendExtend.setConfig = &present_set.EmperorSetConfig{}
	}

	wg.Wait()

	if err != nil {
		return err
	}

	if isPgc(sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType()) {
		sendExtend.ukwInfoMap = ukwInfoMap
	} else {
		sendExtend.ukwInfoMap = FillUgcUserProfile(sendExtend.targetUserMap)
	}

	// 检查一下礼物配置是不是有删除的
	for _, item := range sendExtend.presentConfigMap {
		if item.GetIsDel() {
			return protocol.NewExactServerError(nil, status.ErrUserPresentConfigNotExist, "系统异常，请联系客服处理")
		}
	}

	if s.GeneralConfig.GetPresentConfig().ScoreCheckBegin < time.Now().Unix() {
		sendExtend.ScoreTypeMap, sendExtend.IdentityTypeMap, err = s.ScoreTypeMgr.GetScoreTypeMap(ctx, 0, sendUid, realUidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "preSendPresent - FillScoreType fail , uid: %+v", sendUid)
			return err
		}
		log.DebugWithCtx(ctx, "scoreTypeMap %v", sendExtend.ScoreTypeMap)
	} else {
		sendExtend.ScoreTypeMap = make(map[uint32]uint32)
		for _, item := range realUidList {
			sendExtend.ScoreTypeMap[item] = 1
		}
	}

	sendExtend.sendUser = sendExtend.targetUserMap[sendUid]
	sendExtend.setTargetUser = sendExtend.targetUserMap[targetUid]
	delete(sendExtend.targetUserMap, sendUid)

	return
}

func (s *SetPresentMiddlewareMgr) genOrderId(sendExtend *baseSendExtend, in *pb.SetSendPresentReq) error {
	// 生成支付order
	sendExtend.orderMap = make(map[string]*PayOrderInfo)
	if in.WithPay {
		sendExtend.uniqOrderId, sendExtend.orderMap = s.genOrderWithItemList(in.SendUid, sendExtend.nowTs, in.GetTargetInfo().GetItemInfo(), sendExtend)
		// 算一下总金额，如果超过两万要报错
		totalPrice := uint64(0)
		for _, order := range sendExtend.orderMap {
			if sendExtend.presentConfigMap[order.giftId].GetPriceType() != uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
				continue
			}
			totalPrice += uint64(sendExtend.presentConfigMap[order.giftId].GetPrice()) * uint64(order.count)
		}
		if totalPrice >= 2000000 {
			return protocol.NewExactServerError(nil, status.ErrUserPresentSpendOverLimit)
		}
	} else {
		for _, order := range in.GetTargetInfo().GetItemInfo() {
			if order.GetOrderId() == "" {
				return protocol.NewExactServerError(nil, status.ErrUserPresentOrderNotExist)
			}
			sendExtend.orderMap[order.OrderId] = &PayOrderInfo{
				targetUid: order.GetUid(),
				orderId:   order.GetOrderId(),
				giftId:    order.GetItemId(),
				count:     order.GetCount(),
				dealToken: order.GetDealToken(),
			}
		}

	}
	return nil
}

func (s *SetPresentMiddlewareMgr) genOrderWithItemList(sendUid uint32, timeVal time.Time, itemList []*pb.PresentItemInfo, sendExtend *baseSendExtend) (string, map[string]*PayOrderInfo) {
	//order_id
	strMicroTime := s.getStrMicroTime(timeVal)
	uniqOrderId := s.createOrderId(0, sendUid, 11, 0, strMicroTime)

	orderMap := make(map[string]*PayOrderInfo)

	for _, j := range itemList {
		orderId := fmt.Sprintf("%s_%v_%d", uniqOrderId, j.GetUid(), j.GetItemId())
		orderMap[orderId] = &PayOrderInfo{targetUid: sendExtend.fake2RealUidMap[j.GetUid()], orderId: orderId, giftId: j.GetItemId(), count: j.GetCount()}
	}
	return uniqOrderId, orderMap
}

func (s *SetPresentMiddlewareMgr) SendingPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {

	// 类型断言
	_, ok := outInter.(*pb.SetSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "SendingPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.SetSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "SendingPresent -- para inter need *SendPresentReq type. ")
		return nil
	}

	//冻结红钻/t豆
	if in.WithPay {
		sendExtend.remainCurrency, sendExtend.remainTbeans, sendExtend.remainSource, sendExtend.realItemSource, sendExtend.expireTime, err =
			s.tryFreeze(ctx, sendExtend, in.ItemSource, in.SendMethod)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendingPresent -- tryFreeze fail. uid:%v c:%v err:%v",
				in.SendUid, in.ChannelId, err)
			return err
		}
		if sendExtend.realItemSource != in.GetItemSource() {
			in.ItemSource = sendExtend.realItemSource
		}
	}

	sendExtend.timeCollect.AddTimeEvent("FreezeEnd")
	sendExtend.extendInfo = &ExtendInfo{targetInfoMap: map[uint32]*TargetExtendInfo{}}

	// 送礼人是否记录财富值
	ctx = context.WithValue(ctx, IsRecordSenderRichKey, s.isRecordRichMap(ctx, sendExtend.sendUser.GetUid(), sendExtend.targetUserMap))

	//赠送礼物
	sendExtend.sucOrders, err = s.sendPresent(ctx, sendExtend, in.GetClientInfo(), in)

	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- __SendPresent fail. uid:%v ret:%v req:%v",
			in.SendUid, err, in)
		return err
	}

	//礼物周边信息处理
	itemMap := make(map[uint32]uint32)
	for _, item := range sendExtend.sucOrders {
		itemMap[item.giftId] += item.count
	}

	sendExtend.timeCollect.AddTimeEvent("before ProcPresentWatchWithList")
	log.DebugWithCtx(ctx, "before ProcPresentWatchWithList present extend info : %+v", sendExtend.extendInfo)

	err = s.ProcPresentWatchWithList(ctx, sendExtend, itemMap)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- __ProcPresentWatch fail:%v. _req:%v", err, in)
	}
	log.DebugWithCtx(ctx, "after send present extend info : %+v", sendExtend.extendInfo)

	//提交支付订单
	if in.WithPay {
		err = s.commitPayOrder(ctx, sendExtend.orderMap, uint32(pb.PresentSendMethodType_PRESENT_TYPE_ROOM), in.ItemSource, uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN))
	}

	return err
}

func (s *SetPresentMiddlewareMgr) tryFreeze(ctx context.Context, sendExtend *baseSendExtend, itemSource, sendMethod uint32) (remainCurrency, remainTbean int64, SourceRemain, realItemSource, expireTime uint32, err error) {

	realItemSource = itemSource
	sendUser := sendExtend.sendUser
	uniqQrderId := sendExtend.uniqOrderId
	// 如果不是直接扣t豆/红钻的送礼方式，就报错返回
	// 购买，走unify服务的freezeCurrency并返回remainCurrency
	if uint32(pb.PresentSourceType_PRESENT_SOURCE_BUY) == realItemSource {
		currency, tbean, err := s.freezeCurrencyWithGiftList(ctx, sendExtend, sendMethod)
		if err != nil {
			log.ErrorWithCtx(ctx, "PresentMiddlewareMgr -- freezeCurrency fail. uid:%v uniq_order_id %s err:%v",
				sendUser.GetUid(), uniqQrderId, err)
			return remainCurrency, remainTbean, SourceRemain, realItemSource, expireTime, err
		}
		// T豆余额，用于客户端更新t豆显示
		remainCurrency = currency
		remainTbean = tbean
	} else {
		return remainCurrency, remainTbean, SourceRemain, realItemSource, expireTime, protocol.NewExactServerError(nil, status.ErrPresentSourceInvalid)
	}
	return remainCurrency, remainTbean, SourceRemain, realItemSource, expireTime, nil
}

func (s *SetPresentMiddlewareMgr) sendPresent(ctx context.Context, sendExtend *baseSendExtend,
	clientInfo *pb.PresentClientInfo, in *pb.SetSendPresentReq) (sucOrders map[string]*PayOrderInfo, err error) {
	sucOrders = make(map[string]*PayOrderInfo)
	sendUser := sendExtend.sendUser
	itemSendList := sendExtend.orderMap
	//extendInfo := sendExtend.extendInfo
	timeVal := uint32(sendExtend.nowTs.Unix())

	targetUid := sendExtend.setTargetUser.GetUid()

	if len(itemSendList) == 0 {
		return nil, protocol.NewExactServerError(nil, status.ErrUserPresentInvalidTargetUserSize)
	}

	var channelGuildId uint32
	//1 : 公会房  4：公会公开厅（娱乐房）
	if sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType() == uint32(channelPB_.ChannelType_GUILD_TYPE) || sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType() == uint32(channelPB_.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		channelGuildId = sendExtend.channelSimpleInfo.GetChannelSimple().GetBindId()
	}

	// 成套送出，魅力财富msg的计算，因为不涉及红钻可以简化

	// 先计算基本的加值

	baseAddRich := uint32(0)
	baseAddCharm := uint32(0)
	scoreValue := uint32(0)
	richValueMap := make(map[uint32]uint32)

	for _, item := range itemSendList {
		total := sendExtend.presentConfigMap[item.giftId].GetPrice() * item.count
		if s.isRecordTargetCharm(ctx, targetUid, sendExtend.presentConfigMap[item.giftId].GetPriceType()) {
			baseAddCharm += total
		}
		scoreValue += sendExtend.presentConfigMap[item.giftId].GetScore() * item.count
		if sendExtend.presentConfigMap[item.giftId].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
			// 如果是t豆，额外计算一下倍率
			total = s.calRichRatio(timeVal, item.giftId, total)
		}
		richValueMap[item.giftId] = total
		if s.isRecordSenderRich(ctx, sendUser.GetUid(), sendExtend.presentConfigMap[item.giftId].GetPriceType()) {
			baseAddRich += total
		}
	}

	finalRichValue, richBonusMsg, richTimes := s.calUserBonusCardEffect(ctx, *sendUser, 1, baseAddRich, true)
	finalCharmValue, _, charmTimes := s.calUserBonusCardEffect(ctx, *sendExtend.targetUserMap[targetUid], 1, baseAddCharm, false)

	sendExtend.extendInfo.emperorSetSendUser = &jsonSendUser{
		RichValue:                int32(finalRichValue),
		RichValuePrefix:          "",
		MemberContribution:       0,
		MemberContributionPrefix: "",
		BonusMsg:                 richBonusMsg,
	}

	sendExtend.extendInfo.emperorSetTargetUser = &jsonTargetUser{
		Score:       int32(scoreValue),
		ScorePrefix: "",
		Charm:       int32(finalCharmValue),
		CharmPrefix: "",
	}

	log.DebugWithCtx(ctx, "cardTime %d , %d", richTimes, charmTimes)

	//addRichOnce := extendInfo.realAddRichValue / uint32(len(targetUserMap))
	relayChannelId := uint32(0)

	if sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType() == uint32(channelPB_.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
		resp, err := client.OffcialLiveCli.GetRelay(ctx, sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId())
		if err != nil {
			log.ErrorWithCtx(ctx, "sendPresent -- GetRelay fail ,  channelId %d err %v", sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId(), err)
		}
		relayChannelId = resp.GetChannelId()
	}

	//目前的成套送出只能送t豆礼物，不处理魅力财富了。
	//如果要支持红钻的，需要魅力财富那边增加一个可以传入多个礼物id的批量接口

	reqList := make([]*userpresentGoPb.SendPresentItem, 0)
	for _, item := range itemSendList {
		if _, ok := sendExtend.presentConfigMap[item.giftId]; !ok {
			continue
		}

		isVirtualLive := s.checkLiveStatus(ctx, sendExtend.channelSimpleInfo.GetChannelSimple(), relayChannelId)
		sendReq := userpresentGoPb.SendPresentItem{
			Uid:              sendUser.GetUid(),
			ItemId:           item.giftId,
			OrderId:          item.orderId,
			ItemConfig:       tranPresentSvrConfigToOldConfig(sendExtend.presentConfigMap[item.giftId]),
			ChannelId:        sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId(),
			ChannelType:      sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType(),
			ChannelName:      sendExtend.channelSimpleInfo.GetChannelSimple().GetName(),
			GuildId:          channelGuildId,
			ItemCount:        item.count,
			SendTime:         timeVal,
			OptInvalid:       true,
			UserFromIp:       clientInfo.GetServiceInfo().GetClientIp(),
			ItemSource:       in.GetItemSource(),
			AsyncFlag:        true,
			AppId:            clientInfo.GetAppId(),
			MarketId:         clientInfo.GetMarketId(),
			ChannelDisplayId: sendExtend.channelSimpleInfo.GetChannelSimple().GetDisplayId(),
			SendSource:       in.GetSendSource(),
			SendPlatform:     clientInfo.GetServiceInfo().GetClientType(),
			BatchType:        0,
			AddRich:          richValueMap[item.giftId] * richTimes / 100,
			AddCharm:         sendExtend.presentConfigMap[item.giftId].GetPrice() * item.count * charmTimes / 100,
			BindChannelId:    relayChannelId,
			SendMethod:       in.GetSendMethod(),
			TargetUid:        item.targetUid,
			DealToken:        item.dealToken,
			ChannelGameId:    in.GetChannelGameId(),
			IsVirtualLive:    isVirtualLive,
			ScoreType:        sendExtend.ScoreTypeMap[item.targetUid],
			IdentityType:     sendExtend.IdentityTypeMap[item.targetUid],
			GiverGuildId:     sendUser.GetCurrentGuildId(),
			ReceiverGuildId:  sendExtend.targetUserMap[item.targetUid].GetCurrentGuildId(),
		}

		//产品改动，不是送给本房间主播的礼物不记为房间弹幕游戏送出
		if item.targetUid != sendExtend.channelSimpleInfo.GetChannelSimple().GetBindId() {
			sendReq.ChannelGameId = 0
		}

		if sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege() != nil {
			sendReq.FromUkwAccount = sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege().GetAccount()
			sendReq.FromUkwNickname = sendExtend.ukwInfoMap[sendUser.GetUid()].GetPrivilege().GetNickname()
		}
		if sendExtend.ukwInfoMap[item.targetUid].GetPrivilege() != nil {
			sendReq.ToUkwAccount = sendExtend.ukwInfoMap[item.targetUid].GetPrivilege().GetAccount()
			sendReq.ToUkwNickname = sendExtend.ukwInfoMap[item.targetUid].GetPrivilege().GetNickname()
		}

		if sendExtend.primaryCustomGift != nil {
			sendReq.ItemId = sendExtend.primaryCustomGift.GetItemId()
			sendReq.ItemConfig = tranPresentSvrConfigToOldConfig(sendExtend.primaryCustomGift)
		}

		log.InfoWithCtx(ctx, "PresentMiddlewareMgr -- SendPresent %+v ", sendReq)

		if item.tbTime != "" {
			sendTime, _ := time.ParseInLocation("2006-01-02 15:04:05", item.tbTime, time.Local)
			sendReq.SendTime = uint32(sendTime.Unix())
		}

		if !in.WithPay {
			dealToken, err := UpdateDealTokenInfo(ctx, item.dealToken, item.orderId, false)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendPresent UpdateDealTokenInfo fail , err %v , uid %d", err, sendUser.GetUid())
				//deal_token处理失败，暂时不返回错误，防止送礼失败;deal_token相关链路稳定后可以正常返回错误
				//continue
			}
			sendReq.DealToken = dealToken
		}

		reqList = append(reqList, &sendReq)

		sucOrders[sendReq.GetOrderId()] = item
		if sendExtend.presentConfigMap[item.giftId].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
			sendExtend.totalPrice += sendExtend.presentConfigMap[item.giftId].GetPrice()
		}

	}

	_, err = client.PresentGoCli.BatchSendPresent(ctx, &userpresentGoPb.BatchSendPresentReq{
		PresentList: reqList,
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "sendPresent -- BatchSendPresent fail , err %v , uid %d", err, sendUser.GetUid())
		return sucOrders, err
	}

	wg := sync.WaitGroup{}

	for _, sendReq := range reqList {
		req := tranSendPresentReqToOld(sendReq)
		wg.Add(1)
		go func() {
			defer func() {
				wg.Done()
			}()

			if err == nil {
				// 送礼成功，完成任务，更新专属礼物信息
				s.HandleHandleMission(ctx, sendExtend, req)
			}
		}()
	}

	//开协程去同步获取送礼结果
	wg.Wait()

	return sucOrders, err
}

func (s *SetPresentMiddlewareMgr) ProcPresentWatchWithList(ctx context.Context, sendExtend *baseSendExtend, itemWithCount map[uint32]uint32) (err error) {

	extendInfo := sendExtend.extendInfo
	bIsOpUserNeedSyncGrowInfo := false
	if extendInfo.bIsSendUserRichLevelChanged {
		bIsOpUserNeedSyncGrowInfo = true
	}
	channelInfo := sendExtend.channelSimpleInfo

	channelSimpleInfo := channelInfo.GetChannelSimple()
	channelGuildid := uint32(0)
	sendUser := sendExtend.sendUser

	if channelSimpleInfo.GetChannelType() == uint32(1) || channelSimpleInfo.GetChannelType() == uint32(4) {
		channelGuildid = channelSimpleInfo.GetBindId()
	}

	memberContributionAdded := make(map[uint32]int32)
	needSyncReddiamond := false
	hasTbeanPresent := false

	//log.ErrorWithCtx(ctx,"totalPrice is : %v , order : %v ,  isOptValid : %v", totalPrice, orderId)

	// 公会贡献，因为需要返回，所以没法异步处理，这里并发处理下。
	wg := sync.WaitGroup{}

	for id, count := range itemWithCount {
		wg.Add(1)
		go func(id uint32, count uint32) {
			// 加工会贡献
			defer func() {
				wg.Done()
			}()
			if channelGuildid > 0 && sendUser.GetCurrentGuildId() == channelGuildid {
				orderDesc := fmt.Sprintf("送出礼物\"%s\"", sendExtend.presentConfigMap[id].GetName())
				if count > 1 {
					orderDesc += fmt.Sprintf(" x %d", count)
				}
				sv, _ := protogrpc.ServiceInfoFromContext(ctx)
				if sv.UserID == 0 {
					sv.UserID = sendUser.GetUid()
				}
				newCtx := protogrpc.WithServiceInfo(ctx, sv)
				contributionResp, err := s.guildmemberlvCli.AddMemberContribution(newCtx, &guildmemberlvPB.AddMemberContributionReq{
					GuildId: channelGuildid,
					OperInfo: &guildmemberlvPB.StMemberContributionOper{
						OperType:   uint32(guildmemberlvPB.OPER_TYPE_OPER_SEND_PRESENT),
						OperValue:  int32(sendExtend.presentConfigMap[id].GetPrice() * count),
						OrderId:    fmt.Sprintf("%v_%d", sendExtend.uniqOrderId, id),
						OrderDesc:  orderDesc,
						OptInvalid: true,
					},
				}, sendUser.GetUid())
				log.InfoWithCtx(ctx, "%s %d", fmt.Sprintf("%v_%d", sendExtend.uniqOrderId, id), sendUser.GetUid())
				if err != nil {
					log.ErrorWithCtx(ctx, "ProcPresentWatch -- addMemberContribution fail:%v, op:%v, guild:%v, gift:%v",
						err, sendUser.GetUid(), sendUser.CurrentGuildId, sendExtend.presentConfigMap[id].GetItemId())
				} else {
					memberContributionAdded[id] = contributionResp.ContributionAdded
					bIsOpUserNeedSyncGrowInfo = true
				}
			}
		}(id, count)

		if sendExtend.presentConfigMap[id].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {
			needSyncReddiamond = true
		}

		if sendExtend.presentConfigMap[id].GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
			hasTbeanPresent = true
			s.HandlePresentSetRecord(ctx, sendExtend, sendUser.GetUid(), id)
		}

		log.DebugWithCtx(ctx, "NotifyPresentSend %v", sendExtend.presentConfigMap[id].GetExtend().GetEffectEndDelay())
		if sendExtend.presentConfigMap[id].GetExtend().GetEffectEndDelay() {
			s.HandleNotifyPresentSend(ctx, sendExtend, id, count)
		}

	}

	wg.Wait()

	s.HandleConsumePrivilege(ctx, sendExtend)

	sucUserMap := make(map[uint32]bool)
	for _, order := range sendExtend.sucOrders {
		sucUserMap[order.targetUid] = true
	}

	if len(sucUserMap) == 0 {
		for _, item := range sendExtend.sucUsers {
			sucUserMap[item.GetUid()] = true
		}
	}

	for uid, _ := range sucUserMap {
		// 测试用，如果在这个名单里直接发
		for _, item := range s.GeneralConfig.GetPresentConfig().FirstScoreMsgTrigger {
			if item == uid {
				_ = s.SendFirstScoreMsg(ctx, uid, 0, 0)
				if sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId() != 0 {
					_ = s.PushFirstScore(ctx, uid)
				}
				break
			}
		}

		// 无签约（不可提现积分）
		if sendExtend.ScoreTypeMap[uid] == 1 && hasTbeanPresent && s.GeneralConfig.GetPresentConfig().ScoreCheckBegin < time.Now().Unix() {
			log.DebugWithCtx(ctx, "HandleFirstPresent %d", uid)
			s.HandleFirstPresent(ctx, sendExtend, uid)
		}

		// 测试用，如果在这个名单里直接发
		for _, item := range s.GeneralConfig.GetPresentConfig().FirstIntimatePresentTrigger {
			if item == uid {
				log.DebugWithCtx(ctx, "PushFirstIntimatePresent %d", uid)
				_ = s.PushFirstIntimatePresent(ctx, uid)
				break
			}
		}

		// 送的是亲密礼物
		if sendExtend.BusinessType == uint32(presentPB_.PresentBusinessType_E_PRESENT_BUSINESS_TYPE_INTIMATE_PRESENT) && sendExtend.ScoreTypeMap[uid] == 1 {
			s.HandleFirstIntimatePresent(ctx, sendExtend, uid)
		}
	}

	s.HandleEmperorSetRecord(ctx, sendExtend, sendUser.GetUid(), sendExtend.setId, sendUser.GetCurrentGuildId())

	if needSyncReddiamond {
		s.HandleUpdateUserGrowTimeline(ctx, sendExtend)
		// 红钻才需要这个值
		s.HandleVipPrivilege(ctx, sendExtend)
	}

	if needSyncReddiamond {
		s.HandleUserRichOrCharmLevelUpdate(ctx, sendExtend)
	}

	if bIsOpUserNeedSyncGrowInfo {
		s.HandleSendUserScoreAndCharmRichSync(ctx, sendExtend)
	}

	for uid, _ := range sucUserMap {
		s.HandleTargetUserScoreAndCharmRichSync(ctx, sendExtend, &TargetExtendInfo{
			uid: uid,
		})
	}

	for _, target := range extendInfo.targetInfoMap {
		jsonTargetUser := jsonTargetUser{int32(target.score), "", int32(target.realAddCharm), ""}
		userExtendJson, _ := json.Marshal(jsonTargetUser)
		target.userExtendJson = string(userExtendJson)
	}

	for _, target := range extendInfo.multiTargetInfoMap {
		for _, item := range target {
			jsonTargetUser := jsonTargetUser{int32(item.score), "", int32(item.realAddCharm), ""}
			userExtendJson, _ := json.Marshal(jsonTargetUser)
			item.userExtendJson = string(userExtendJson)
		}
	}

	sendExtend.extendInfo.emperorSetSendUser.MemberContribution = memberContributionAdded[sendUser.GetUid()]

	return err
}

func (s *SetPresentMiddlewareMgr) AfterSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {

	// 类型断言
	out, ok := outInter.(*pb.SetSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.SetSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- para inter need *SendPresentReq type. ")
		return nil
	}

	//送礼后处理，填充动效、推送等
	s.FillRespAndPush(ctx, sendExtend, in, out)
	return err
}

func (s *SetPresentMiddlewareMgr) FillRespAndPush(ctx context.Context, sendExtend *baseSendExtend, in *pb.SetSendPresentReq, out *pb.SetSendPresentResp) {
	remainSource := sendExtend.remainSource
	remainTbeans := sendExtend.remainTbeans
	extendInfo := sendExtend.extendInfo

	out.SourceRemain = remainSource
	out.CurTbeans = remainTbeans

	out.MsgInfo = &pb.SetPresentSendMsg{}
	targetUid := sendExtend.setTargetUser.GetUid()

	// 把推送格式弄出来
	sendTime := uint64(time.Now().UnixNano() / 1000000)
	tmpCustomText := make(map[uint32]string)

	// 按照传入的itemInfo的顺序，填充msgInfo
	extendJson, _ := json.Marshal(sendExtend.extendInfo.emperorSetSendUser)
	msgInfo := s.genSetMsgInfo(ctx, sendExtend, in.GetTargetInfo().GetItemInfo(), 0, sendTime, string(extendJson))

	msgInfo.TargetUid = sendExtend.targetUserMap[targetUid].GetUid()
	msgInfo.TargetAccount = sendExtend.targetUserMap[targetUid].GetUsername()
	msgInfo.TargetNickname = sendExtend.targetUserMap[targetUid].GetNickname()

	msgInfo.SendUid = sendExtend.sendUser.GetUid()
	msgInfo.SendAccount = sendExtend.sendUser.GetUsername()
	msgInfo.SendNickname = sendExtend.sendUser.GetNickname()

	msgInfo.FromUserProfile = genUserProfile(sendExtend.ukwInfoMap[msgInfo.SendUid])
	if sendExtend.ukwInfoMap[msgInfo.SendUid].GetPrivilege() != nil {
		msgInfo.SendNickname = sendExtend.ukwInfoMap[msgInfo.GetSendUid()].GetPrivilege().GetNickname()
		msgInfo.SendAccount = sendExtend.ukwInfoMap[msgInfo.GetSendUid()].GetPrivilege().GetAccount()
		msgInfo.SendUid = sendExtend.ukwInfoMap[msgInfo.GetSendUid()].GetUid()
	}

	msgInfo.TargetUserProfile = genUserProfile(sendExtend.ukwInfoMap[msgInfo.TargetUid])
	if sendExtend.ukwInfoMap[msgInfo.TargetUid].GetPrivilege() != nil {
		msgInfo.TargetNickname = sendExtend.ukwInfoMap[msgInfo.GetTargetUid()].GetPrivilege().GetNickname()
		msgInfo.TargetAccount = sendExtend.ukwInfoMap[msgInfo.GetTargetUid()].GetPrivilege().GetAccount()
		msgInfo.TargetUid = sendExtend.ukwInfoMap[msgInfo.GetTargetUid()].GetUid()
	}

	msgInfo.EmperorEffectJson = decoration.GetEmperorSet(sendExtend.ukwInfoMap[sendExtend.sendUser.GetUid()], sendExtend.ukwInfoMap[sendExtend.setTargetUser.GetUid()])
	msgInfo.ViewingEffectJson = decoration.GetEmperorSetViewing(sendExtend.ukwInfoMap[sendExtend.sendUser.GetUid()], sendExtend.ukwInfoMap[sendExtend.setTargetUser.GetUid()])

	for _, item := range msgInfo.GetItemInfo() {
		text, ok := tmpCustomText[targetUid]
		if ok {
			item.CustomTextJson = text
		} else {
			item.CustomTextJson = decoration.GetCustomPresentText(sendExtend.presentConfigMap[item.GetItemId()],
				sendExtend.ukwInfoMap[sendExtend.sendUser.GetUid()], sendExtend.ukwInfoMap[targetUid])
		}
	}

	out.MsgInfo = msgInfo

	if in.GetWithPush() {
		s.procPushEvent(ctx, sendExtend, in, out)
	}

	log.DebugWithCtx(ctx, "extend info : %v", extendInfo)
}

func (s *SetPresentMiddlewareMgr) procPushEvent(ctx context.Context, sendExtend *baseSendExtend, in *pb.SetSendPresentReq, out *pb.SetSendPresentResp) {
	sendUser := sendExtend.sendUser
	channelInfo := sendExtend.channelSimpleInfo
	targetIndex := 0
	targetUid := sendExtend.setTargetUser.GetUid()
	targetUser := sendExtend.targetUserMap[targetUid]
	msgInfo := out.MsgInfo

	extendJson, _ := json.Marshal(sendExtend.extendInfo.emperorSetTargetUser)

	presentSetMsg := &present_go_logic.EmperorSetPresentMsg{
		PresentList:       make([]*presentPB_.PresentSendItemInfo, 0),
		SendTime:          msgInfo.GetSendTime(),
		ChannelId:         msgInfo.GetChannelId(),
		SendUid:           msgInfo.GetSendUid(),
		SendAccount:       msgInfo.GetSendAccount(),
		SendNickname:      msgInfo.GetSendNickname(),
		TargetUid:         msgInfo.GetTargetUid(),
		TargetAccount:     msgInfo.GetTargetAccount(),
		TargetNickname:    msgInfo.GetTargetNickname(),
		ExtendJson:        string(extendJson),
		FromUserProfile:   sendExtend.ukwInfoMap[sendUser.GetUid()],
		ToUserProfile:     sendExtend.ukwInfoMap[targetUid],
		EmperorSetId:      sendExtend.setId,
		IsBoxBreaking:     sendExtend.setConfig.GetEffect().GetIsBreakingBox(),
		EmperorEffectJson: msgInfo.GetEmperorEffectJson(),
		ViewingEffectJson: msgInfo.GetViewingEffectJson(),
	}

	presentSetMsgItem := &present_go_logic.EmperorSetPresentInfo{
		PresentList:       make([]*presentPB_.PresentSendItemInfo, 0),
		EmperorSetId:      sendExtend.setId,
		EmperorEffectJson: msgInfo.GetEmperorEffectJson(),
		ViewingEffectJson: msgInfo.GetViewingEffectJson(),
	}

	for _, item := range out.GetMsgInfo().GetItemInfo() {
		itemCfg := sendExtend.presentConfigMap[item.GetItemId()]
		messageItemInfo := &presentPB_.PresentSendItemInfo{
			ItemId:            item.ItemId,
			Count:             item.Count,
			IsBatch:           item.IsBatch,
			ShowBatchEffect:   item.ShowBatchEffect,
			ShowEffect:        item.ShowEffect,
			ShowEffectV2:      item.ShowEffectV2,
			FlowId:            item.FlowId,
			SendType:          item.SendType,
			DynamicTemplateId: item.DynamicTemplateId,
			IsVisibleToSender: false,
			IsShowSurprise:    item.IsShowSurprise,
			SurpriseCount:     item.SurpriseCount,
			CustomTextJson:    item.GetCustomTextJson(),
		}

		presentSetMsg.PresentList = append(presentSetMsg.PresentList, messageItemInfo)
		presentSetMsgItem.PresentList = append(presentSetMsgItem.PresentList, messageItemInfo)

		s.PushPresentBreakingNewsToAll(ctx, sendUser, &presentPB.GetPresentConfigByIdResp{ItemConfig: itemCfg},
			item.GetCount(), time.Now(), channelInfo.GetChannelSimple().GetChannelId(), targetUser,
			sendExtend)

		// 单次1w全服
		if item.GetCount()*itemCfg.GetPrice() >= 1000000 &&
			itemCfg.GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) &&
			itemCfg.GetExtend().GetTag() != uint32(ga.PresentTagType_PRESENT_TAG_LEVELUP) {
			_ = s.PushHighSpendPresentBreakingNews(ctx, sendUser, &presentPB.GetPresentConfigByIdResp{ItemConfig: itemCfg},
				item.GetCount(), time.Now(), channelInfo.GetChannelSimple(), targetUser, sendExtend)
		}
	}

	pushDelay := uint32(0)
	log.DebugWithCtx(ctx, "sendExtend.setConfig %v", sendExtend.setConfig)
	if sendExtend.setConfig.GetEffect().GetIsBreakingBox() {
		// 默认22s
		if s.GeneralConfig.GetPresentConfig().BoxPushDelay != 0 {
			pushDelay = s.GeneralConfig.GetPresentConfig().BoxPushDelay
		} else {
			pushDelay = PushDelayTime
		}
	}

	_ = s.PushFactory.GetPushMgr(presentPush.PushType_User).Push(ctx, &presentPush.PushReq{
		FromUser:             sendUser,
		Channel:              channelInfo.GetChannelSimple(),
		MsgType:              uint32(pushPb.PushMessage_EMPEROR_SET_PRESENT_PUSH),
		MsgContent:           presentSetMsg,
		ToUsers:              map[uint32]*accountPB.UserResp{targetUser.GetUid(): targetUser},
		SendTime:             int64(out.GetMsgInfo().GetSendTime()),
		UkwInfoMap:           sendExtend.ukwInfoMap,
		TargetIndex:          int64(targetIndex),
		PushDelay:            pushDelay,
		IsBoxVisible:         false,
		EmperorSetConfig:     sendExtend.setConfig,
		EmperorSetPresentMsg: presentSetMsg,
	})

	req := &presentPush.PushReq{
		FromUser:             sendUser,
		Channel:              channelInfo.GetChannelSimple(),
		MsgType:              uint32(channelPB_.ChannelMsgType_ENUM_CHANNEL_EMPEROR_SET_PRESENT_PUSH),
		MsgContent:           presentSetMsgItem,
		ToUsers:              map[uint32]*accountPB.UserResp{targetUser.GetUid(): targetUser},
		SendTime:             int64(out.GetMsgInfo().GetSendTime()),
		UkwInfoMap:           sendExtend.ukwInfoMap,
		TargetIndex:          int64(targetIndex),
		PushDelay:            pushDelay,
		EmperorSetConfig:     sendExtend.setConfig,
		EmperorSetPresentMsg: presentSetMsg,
	}
	_ = s.PushFactory.GetPushMgr(presentPush.PushType_Channel).Push(ctx, req)

	if sendExtend.setConfig.GetEffect().GetIsBreakingBox() {
		out.BoxDetail = presentPush.TranPushReqToEmperorBoxInfo(req, targetUser).GetBoxDetail()
	}
}

func (s *SetPresentMiddlewareMgr) genSetMsgInfo(ctx context.Context, sendExtend *baseSendExtend, itemInfo []*pb.PresentItemInfo, sendType uint32, sendTime uint64, extendJson string) *pb.SetPresentSendMsg {
	//涂鸦送礼
	tmpMsgInfo := &pb.SetPresentSendMsg{ItemInfo: make([]*pb.PresentSendItemInfo, 0)}
	for _, item := range itemInfo {
		giftId := item.GetItemId()
		count := item.GetCount()

		presentConfig := sendExtend.presentConfigMap[giftId]

		//送礼特效与动效
		tmpItemInfo := &pb.PresentSendItemInfo{}

		showEffect, showEffectV2, FlowId := s.getSetPresentEffect(ctx, presentConfig, count)
		tmpItemInfo.ItemId = presentConfig.GetItemId()
		tmpItemInfo.Count = count
		tmpItemInfo.ShowEffect = showEffect
		tmpItemInfo.ShowEffectV2 = showEffectV2
		tmpItemInfo.FlowId = FlowId
		tmpItemInfo.IsBatch = false
		tmpItemInfo.SendType = sendType
		tmpItemInfo.ShowBatchEffect = false

		tmpMsgInfo.ItemInfo = append(tmpMsgInfo.ItemInfo, tmpItemInfo)
	}

	tmpMsgInfo.ChannelId = sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId()
	tmpMsgInfo.SendTime = sendTime
	tmpMsgInfo.SendUid = sendExtend.sendUser.GetUid()
	tmpMsgInfo.SendNickname = sendExtend.sendUser.GetNickname()
	tmpMsgInfo.SendAccount = sendExtend.sendUser.GetUsername()
	tmpMsgInfo.ExtendJson = extendJson
	return tmpMsgInfo
}

func (s *SetPresentMiddlewareMgr) getSetPresentEffect(ctx context.Context, itemCfg *presentPB.StPresentItemConfig, itemCount uint32) (
	showEffect uint32, showEffectV2 uint32, flowId uint32) {

	showEffect = itemCfg.GetExtend().GetShowEffect()
	showEffectV2 = showEffect
	flowId = itemCfg.GetExtend().GetFlowId()
	if showEffect > 0 {
		return showEffect, showEffectV2, flowId
	}
	totalPrice := itemCfg.GetPrice() * itemCount
	//这里的enum见ga_base.proto , PRESENT_SHOW_EFFECT_TYPE
	if totalPrice >= 100000 {
		showEffect = 3
	} else if totalPrice >= 10000 {
		showEffect = 2
	} else {
		showEffect = 0
	}
	//有流光，用配置的
	//ga_base.proto PRESENT_SHOW_EFFECT_TYPE_V2
	if flowId > 0 {
		showEffectV2 = uint32(ga.PRESENT_SHOW_EFFECT_TYPE_V2_PRESENT_SHOW_EFFECT_V2_FLOW)
		return showEffect, showEffectV2, flowId
	}

	// 小礼物全部用flowId = 1 , showEffectV2 = 2
	flowId = 1
	showEffectV2 = uint32(ga.PRESENT_SHOW_EFFECT_TYPE_V2_PRESENT_SHOW_EFFECT_V2_FLOW)
	showEffect = uint32(ga.PRESENT_SHOW_EFFECT_TYPE_PRESENT_SHOW_EFFECT_FLOW_1)

	return showEffect, showEffectV2, flowId
}

func tranPresentSvrConfigToOldConfig(svrConfig *presentPB.StPresentItemConfig) *userpresentGoPb.StPresentItemConfig {
	customText := make([]*userpresentGoPb.CustomText, 0)
	for _, item := range svrConfig.GetExtend().GetCustomText() {
		customText = append(customText, &userpresentGoPb.CustomText{
			Key:  item.GetKey(),
			Text: item.GetText(),
		})
	}

	return &userpresentGoPb.StPresentItemConfig{
		ItemId:      svrConfig.GetItemId(),
		Name:        svrConfig.GetName(),
		IconUrl:     svrConfig.GetIconUrl(),
		Price:       svrConfig.GetPrice(),
		Score:       svrConfig.GetScore(),
		Charm:       svrConfig.GetCharm(),
		Rank:        svrConfig.GetRank(),
		EffectBegin: svrConfig.GetEffectBegin(),
		EffectEnd:   svrConfig.GetEffectEnd(),
		UpdateTime:  svrConfig.GetUpdateTime(),
		CreateTime:  svrConfig.GetCreateTime(),
		IsDel:       svrConfig.GetIsDel(),
		PriceType:   svrConfig.GetPriceType(),
		RichValue:   svrConfig.GetRichValue(),
		Extend: &userpresentGoPb.StPresentItemConfigExtend{
			ItemId:            svrConfig.GetItemId(),
			VideoEffectUrl:    svrConfig.GetExtend().GetVideoEffectUrl(),
			ShowEffect:        svrConfig.GetExtend().GetShowEffect(),
			UnshowBatchOption: svrConfig.GetExtend().GetUnshowBatchOption(),
			IsTest:            svrConfig.GetExtend().GetIsTest(),
			FlowId:            svrConfig.GetExtend().GetFlowId(),
			IosExtend: &userpresentGoPb.StConfigIosExtend{
				VideoEffectUrl: svrConfig.GetExtend().GetIosExtend().GetVideoEffectUrl(),
			},
			NotifyAll:          svrConfig.GetExtend().GetNotifyAll(),
			Tag:                svrConfig.GetExtend().GetTag(),
			ForceSendable:      svrConfig.GetExtend().GetForceSendable(),
			NobilityLevel:      svrConfig.GetExtend().GetNobilityLevel(),
			UnshowPresentShelf: svrConfig.GetExtend().GetUnshowPresentShelf(),
			ShowEffectEnd:      svrConfig.GetExtend().GetShowEffectEnd(),
			EffectEndDelay:     svrConfig.GetExtend().GetEffectEndDelay(),
			CustomText:         customText,
			SmallVapUrl:        svrConfig.GetExtend().GetSmallVapUrl(),
			SmallVapMd5:        svrConfig.GetExtend().GetSmallVapMd5(),
			MicEffectUrl:       svrConfig.GetExtend().GetMicEffectUrl(),
			MicEffectMd5:       svrConfig.GetExtend().GetMicEffectMd5(),
			FusionPresent:      svrConfig.GetExtend().GetFusionPresent(),
			IsBoxBreaking:      svrConfig.GetExtend().GetIsBoxBreaking(),
			FansLevel:          svrConfig.GetExtend().GetFansLevel(),
		},
		RankFloat: svrConfig.GetRankFloat(),
	}
}

func tranPresentSvrConfigToNewConfig(svrConfig *userpresentGoPb.StPresentItemConfig) *presentPB.StPresentItemConfig {
	customText := make([]*presentPB.CustomText, 0)
	for _, item := range svrConfig.GetExtend().GetCustomText() {
		customText = append(customText, &presentPB.CustomText{
			Key:  item.GetKey(),
			Text: item.GetText(),
		})
	}

	return &presentPB.StPresentItemConfig{
		ItemId:      svrConfig.GetItemId(),
		Name:        svrConfig.GetName(),
		IconUrl:     svrConfig.GetIconUrl(),
		Price:       svrConfig.GetPrice(),
		Score:       svrConfig.GetScore(),
		Charm:       svrConfig.GetCharm(),
		Rank:        svrConfig.GetRank(),
		EffectBegin: svrConfig.GetEffectBegin(),
		EffectEnd:   svrConfig.GetEffectEnd(),
		UpdateTime:  svrConfig.GetUpdateTime(),
		CreateTime:  svrConfig.GetCreateTime(),
		IsDel:       svrConfig.GetIsDel(),
		PriceType:   svrConfig.GetPriceType(),
		RichValue:   svrConfig.GetRichValue(),
		Extend: &presentPB.StPresentItemConfigExtend{
			ItemId:            svrConfig.GetItemId(),
			VideoEffectUrl:    svrConfig.GetExtend().GetVideoEffectUrl(),
			ShowEffect:        svrConfig.GetExtend().GetShowEffect(),
			UnshowBatchOption: svrConfig.GetExtend().GetUnshowBatchOption(),
			IsTest:            svrConfig.GetExtend().GetIsTest(),
			FlowId:            svrConfig.GetExtend().GetFlowId(),
			IosExtend: &presentPB.StConfigIosExtend{
				VideoEffectUrl: svrConfig.GetExtend().GetIosExtend().GetVideoEffectUrl(),
			},
			NotifyAll:          svrConfig.GetExtend().GetNotifyAll(),
			Tag:                svrConfig.GetExtend().GetTag(),
			ForceSendable:      svrConfig.GetExtend().GetForceSendable(),
			NobilityLevel:      svrConfig.GetExtend().GetNobilityLevel(),
			UnshowPresentShelf: svrConfig.GetExtend().GetUnshowPresentShelf(),
			ShowEffectEnd:      svrConfig.GetExtend().GetShowEffectEnd(),
			EffectEndDelay:     svrConfig.GetExtend().GetEffectEndDelay(),
			CustomText:         customText,
			SmallVapUrl:        svrConfig.GetExtend().GetSmallVapUrl(),
			SmallVapMd5:        svrConfig.GetExtend().GetSmallVapMd5(),
			MicEffectUrl:       svrConfig.GetExtend().GetMicEffectUrl(),
			MicEffectMd5:       svrConfig.GetExtend().GetMicEffectMd5(),
			FusionPresent:      svrConfig.GetExtend().GetFusionPresent(),
			IsBoxBreaking:      svrConfig.GetExtend().GetIsBoxBreaking(),
			FansLevel:          svrConfig.GetExtend().GetFansLevel(),
		},
		RankFloat: svrConfig.GetRankFloat(),
	}
}

func tranSendPresentReqToOld(req *userpresentGoPb.SendPresentItem) *presentPB.SendPresentReq {
	return &presentPB.SendPresentReq{
		Uid:              req.GetUid(),
		TargetUid:        req.GetTargetUid(),
		OrderId:          req.GetOrderId(),
		ItemId:           req.GetItemId(),
		ChannelId:        req.GetChannelId(),
		GuildId:          req.GetGuildId(),
		ItemCount:        req.GetItemCount(),
		AddCharm:         req.GetAddCharm(),
		SendTime:         req.GetSendTime(),
		ItemConfig:       tranPresentSvrConfigToNewConfig(req.GetItemConfig()),
		OptInvalid:       req.GetOptInvalid(),
		AsyncFlag:        req.GetAsyncFlag(),
		UserFromIp:       req.GetUserFromIp(),
		ChannelType:      req.GetChannelType(),
		ItemSource:       req.GetItemSource(),
		ChannelName:      req.GetChannelName(),
		ChannelDisplayId: req.GetChannelDisplayId(),
		SendSource:       req.GetSendSource(),
		SendPlatform:     req.GetSendPlatform(),
		BatchType:        req.GetBatchType(),
		AppId:            req.GetAppId(),
		MarketId:         req.GetMarketId(),
		ReceiverGuildId:  req.GetReceiverGuildId(),
		GiverGuildId:     req.GetGiverGuildId(),
		AddRich:          req.GetAddRich(),
		SendMethod:       req.GetSendMethod(),
		BindChannelId:    req.GetBindChannelId(),
		DealToken:        req.GetDealToken(),
		FromUkwAccount:   req.GetFromUkwAccount(),
		FromUkwNickname:  req.GetFromUkwNickname(),
		ToUkwAccount:     req.GetToUkwAccount(),
		ToUkwNickname:    req.GetToUkwNickname(),
		ChannelGameId:    req.GetChannelGameId(),
		IsVirtualLive:    req.GetIsVirtualLive(),
		ScoreType:        req.GetScoreType(),
	}
}
