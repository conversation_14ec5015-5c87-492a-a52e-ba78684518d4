syntax = "proto3";

package present_middleware;
option go_package = "golang.52tt.com/protocol/services/present-middleware";

// 涂鸦礼物图
message DrawPresentPicture
{
  repeated PresentLine line_list = 1;
}

message PresentLine
{
  uint32 item_id = 1;      // 礼物id
  repeated PresentPoint point_list = 2;   // 礼物坐标
}

message PresentPoint
{
  float x = 1;
  float y = 2;
}

//赠送礼物
message PresentSendMsg
{
  PresentSendItemInfo item_info = 1;
  uint64 send_time = 2;
  uint32 channel_id = 3;
  uint32 send_uid = 4;
  string send_account = 5;
  string send_nickname = 6;
  uint32 target_uid = 7;
  string target_account = 8;
  string target_nickname = 9;
  string extend_json = 10;
}

message SendPresentReq
{
  uint32 send_uid = 1;

  uint32 batch_type = 2;  // 批量送礼方式，为0则为送给个人
  PresentTargetInfo target_info = 3;    // 礼物信息

  uint32 channel_id = 4;  // 通过房间赠送礼物时才需要填
  uint32 send_source = 5;  // 赠送时的点击来源 PresentSendSourceType 0.默认类型(兼容旧版本) 1.送礼转盘 2.礼物架（点击头像或成员列表） 3.语音球
  uint32 item_source = 6;  // 礼物来源 PresentSourceType ，注意这个影响后面的支付，暂时只支持 T豆/背包，不支持挚友和幸运礼物
  uint32 send_method = 7;  //  赠送方式 PresentSendMethod, 主要用于区分客户端收礼记录处的标记

  uint32 send_type = 8; // PresentSendType 涂鸦/粉丝团特殊处理
  DrawPresentPicture draw_present_pic = 9;       // 涂鸦礼物图

  bool is_opt_valid = 10; // 操作是否可用，主要用来防止小号刷红钻礼物

  bool with_pay = 11;  // 是否需要礼物中间件扣除消耗的t豆/红钻/背包礼物，只支持item_source= 0/1/2
  uint32 backpack_item_id = 12; // 如果从背包送出，背包中物品的id

  bool with_push = 13; // 是否需要礼物中间件发放各种推送
  PushInfo push_info = 14; // 推送相关参数

  PresentClientInfo client_info = 15;  // clientInfo ,不填会自动从ctx中获取
  int64 sendTime = 16; // 送礼时间，0则为送礼服务自己的系统时间

  uint32 surprise_effect_count =17; // 赠送多少个礼物会触发升级礼物的彩蛋特效，0为不触发

  //  : 可选是否会触发礼物全服，目前只要送出对应礼物，且房间不为0，就会触发全服
}

message PresentTargetInfo
{
  oneof Target{
    SingleTargetUser single_target = 1;   // 单个收礼人
    MultiTargetUser multi_target = 2;     // 多个收礼人，单种礼物（全麦可以不填收礼人，会自动从麦位上获取）
    MultiTargetItem multi_item = 3;       // 多个收礼人，多种礼物
  }
}

// 对应PresentBatchSendType = 0
message SingleTargetUser
{
  uint32 uid = 1;
  uint32 item_id = 2;
  uint32 count = 3;
  string order_id = 4;
}

// 对应PresentBatchSendType = 1/2
message MultiTargetUser
{
  repeated uint32 uid = 1;
  uint32 item_id = 2;
  uint32 count = 3;
  string unique_order_id = 4;
}


// 对应PresentBatchSendType = 3
message MultiTargetItem
{
  repeated PresentItemInfo item_info = 1;
}

message PresentItemInfo{
  uint32 uid = 1;
  uint32 item_id = 2;
  uint32 count = 3;
  string order_id = 4;
}

message PushInfo{
  // 送礼方式
  enum ChannelPushType
  {
    Channel_NONE = 0;      // 没有push
    Channel_NORMAL = 1;      // 普通的送礼房间push  ChannelMsgType_CHANNEL_PRESENT_MSG
    Channel_NORMAL_ALL = 2;  // 普通的送礼房间push，送礼者也会收到  ChannelMsgType_CHANNEL_PRESENT_MSG , IsVisibleToSender = true
    Channel_MAGIC_PUSH = 3;  //  暂未实现，后续考虑兼容 幸运礼物类型的房间puhs ChannelMsgType_MAGIC_SPIRIT_PRESENT
    Channel_ALLMIC = 4; // 全麦push类型，会同时飞到麦位上所有用户 ChannelMsgType_CHANNEL_BATCH_SEND_PRESENT_NOTIFY
    Channel_ALLMIC_ALL = 5; // 全麦push类型，会同时飞到麦位上所有用户 ChannelMsgType_CHANNEL_BATCH_SEND_PRESENT_NOTIFY, IsVisibleToSender = true
  }

  enum PersonalPushType
  {
    Person_NONE = 0;      // 没有push
    Person_NORMAL = 1;        //普通的礼物个人推送
    Person_NORMAL_SENDER = 2; //普通的礼物个人推送，但是会给送礼人也发一条财富值的推送
  }

  enum ImType
  {
    IM_NONE = 0;      // 没有im
    IM_NORMAL = 1;      //普通的im送礼卡片
  }

  uint32 channel_push_type = 1;  // 房间推送类型
  uint32 personal_push_type = 2; // 个人推送类型
  uint32 im_msg_type = 3;        // im消息类型
}

message PresentClientInfo
{
  uint32 appId = 1; //设备的appId，上报用
  uint32 marketId = 2; // 设备的marketId，上报用
  ServiceCtrlInfo service_info = 3; //service_info，部分逻辑会用到
}


message SendPresentResp
{
  repeated PresentSendMsg msg_info = 1;  // 送礼成功，送出礼物的相关信息
  uint32 member_contribution_added = 2;  // 增加的个人公会贡献
  uint32 count = 3;    // 礼物数量
  int64 cur_tbeans = 4;    // 当前T豆余额
  uint32 item_source = 5;  // 礼物来源 PresentSourceType
  uint32 source_id = 6;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品ID
  uint32 source_remain = 7;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品的剩余数量
  repeated PresentBatchInfoMsg batch_info = 8;  // 如果是全麦，相关信息放在这里
  repeated OrderInfo order_info = 9; // 成功的订单信息
}

message OrderInfo
{
  uint32 uid = 1;
  string order_id = 2;
}

message PresentTargetUserInfo
{
  uint32 uid = 1;
  string account = 2;
  string name = 3;
}

message PresentBatchTargetInfo
{
  uint32 uid = 1;
  string account = 2;
  string nickname = 3;
  string extend_json = 4;
}

message PresentSendItemInfo
{
  uint32 item_id = 1;
  uint32 count = 2;
  uint32 show_effect = 3;  // 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE
  uint32 show_effect_v2 = 4;  // 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE_V2
  uint32 flow_id = 5;    // 流光id
  bool is_batch = 6;      // 0.普通送礼 1.批量送礼
  bool show_batch_effect = 7;
  uint32 send_type = 8;      // 送礼类型 PresentSendType
  DrawPresentPicture draw_present_pic = 9;  // 涂鸦礼物图
  uint32 dynamic_template_id = 10;   // 非全屏礼物动效模板id
  bool is_visible_to_sender = 11;  //消息是否送礼者可见
  bool is_show_surprise = 12; // 是否触发了升级礼物的彩蛋特效，触发了就不展示原本的礼物特效
  uint32 surprise_count = 13; // 如果触发了彩蛋特效，要展示几次
}

// 批量送礼信息
message PresentBatchInfoMsg
{
  uint32 item_id = 1;
  uint32 total_item_count = 2;  // 送出的礼物总数量
  uint32 batch_type = 3;  // 批量送礼类型 PresentBatchSendType
  uint64 send_time = 4;
  uint32 channel_id = 5;
  uint32 send_uid = 6;
  string send_account = 7;
  string send_nickname = 8;
  string extend_json = 9;
  repeated PresentBatchTargetInfo target_list = 10;
  PresentSendItemInfo item_info = 11;
}

message ServiceCtrlInfo{
  string client_ip = 1;
  uint32 client_port = 2;
  bytes device_id = 3;
  uint32 client_type = 4;
  uint32 terminal_type = 5;
  uint32 client_id = 6;
  uint32 client_version = 7;
}

//批量赠送礼物
message BatchSendPresentReq
{
  uint32 item_id = 1;
  uint32 channel_id = 2;  // 通过房间赠送礼物时才需要填
  uint32 count = 3;  // 礼物数量
  uint32 send_source = 4;  // 赠送时的点击来源 PresentSendSourceType 0.默认类型(兼容旧版本) 1.送礼转盘 2.礼物架（点击头像或成员列表） 3.语音球
  uint32 item_source = 5;  // 礼物来源 PresentSourceType
  uint32 source_id = 6;    // 如果是背包里的礼物，就是背包礼物对应的唯一id
  uint32 batch_type = 7;  // 批量送礼类型 PresentBatchSendType
  uint32 send_type = 8;     // 送礼类型 PresentSendType
  DrawPresentPicture draw_present_pic = 9;       // 涂鸦礼物图
  uint32 app_id = 10; //base_req里的appId
  uint32 market_id = 11; // base_req里的marketId
  repeated uint32 target_uid_list = 12; // 收礼用户id列表
  uint32 send_uid = 13; // 送礼用户的uid
  ServiceCtrlInfo service_info = 14; //service_info，部分逻辑会用到
  uint32 another_channel_id = 15; //另一个房间id ，如果channelid是官频则是排班直播间，反之则是官频
}

message BatchSendPresentResp
{
  PresentBatchInfoMsg msg_info = 1;
  uint64 cur_tbeans = 2; // 当前T豆余额
  uint32 item_source = 3;  // 礼物来源 PresentSourceType
  uint32 source_id = 4;    // 如果是背包里的礼物，就是背包礼物对应的唯一id
  uint32 source_remain = 5;    // 如果是背包里的礼物，就是背包礼物的剩余数量
  repeated PresentTargetUserInfo target_list = 6;  // 收礼对象列表
  PresentSendItemInfo item_info = 7;    // 礼物信息
}

message presentSendBase{
  uint32 item_id = 1;
  uint32 channel_id = 2;  // 通过房间赠送礼物时才需要填
  uint32 count = 3;  // 礼物数量
  uint32 send_source = 4;  // 赠送时的点击来源 PresentSendSourceType 0.默认类型(兼容旧版本) 1.送礼转盘 2.礼物架（点击头像或成员列表） 3.语音球
  uint32 item_source = 5;  // 礼物来源 PresentSourceType
  uint32 source_id = 6;    // 如果是背包里的礼物，就是背包礼物对应的唯一id
  uint32 batch_type = 7;  // 批量送礼类型 PresentBatchSendType
  uint32 send_type = 8;     // 送礼类型 PresentSendType
  DrawPresentPicture draw_present_pic = 9;       // 涂鸦礼物图
  uint32 app_id = 10; //base_req里的appId
  uint32 market_id = 11; // base_req里的marketId
  repeated uint32 target_uid_list = 12; // 收礼用户id列表
  uint32 send_uid = 13; // 送礼用户的uid
  ServiceCtrlInfo service_info = 14; //service_info，部分逻辑会用到
}

message ImSendPresentReq
{
  uint32 target_uid = 1;
  uint32 item_id = 2;    // 礼物ID
  uint32 count = 3;          // 兼容旧版本，代码需要特殊处理，值为0时默认为1
  uint32 send_source = 4;  // 赠送时的点击来源 PresentSendSourceType 0.默认类型(兼容旧版本) 1.送礼转盘 2.礼物架（点击头像或成员列表） 3.语音球
  uint32 item_source = 5;  // 礼物来源 PresentSourceType
  uint32 source_id = 6;    // item_source对应的类型ID。比如如果是背包物品，那这里就是背包物品ID
  uint32 send_type = 7;   // 送礼类型 PresentSendType
  uint32 appId = 8; //设备的appId，上报用
  uint32 marketId = 9; // 设备的marketId，上报用
  uint32 send_uid = 10; // 送礼用户的uid
  ServiceCtrlInfo service_info = 11; //service_info，部分逻辑会用到
  bool is_opt_valid = 12; // 操作是否可用，涉及工会贡献之类的一些数值会不会增加，主要用来防止小号刷红钻礼物
  uint32 present_text_type = 13; // send_source == E_SEND_SOURCE_IM 时才填，enum PresentTextType 送礼文案类型，根据不同活动区分IM的富文本内容
  uint32 send_method = 14; // 送礼方法，用于展示收/送礼记录
}

message ImSendPresentResp
{
  uint32 item_id = 1;
  PresentSendMsg msg_info = 2;
  uint32 member_contribution_added = 3;  // 增加的个人公会贡献
  uint32 count = 4;    // 礼物数量
  uint64 cur_tbeans = 5;    // 当前T豆余额
  uint32 item_source = 6;  // 礼物来源 PresentSourceType
  uint32 source_id = 7;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品ID
  uint32 source_remain = 8;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品的剩余数量
}

message FellowSendPresentReq
{
  uint32 target_uid = 1;
  uint32 item_id = 2;    // 礼物ID
  uint32 count = 3;          // 兼容旧版本，代码需要特殊处理，值为0时默认为1
  uint32 send_source = 4;  // 赠送时的点击来源 PresentSendSourceType 0.默认类型(兼容旧版本) 1.送礼转盘 2.礼物架（点击头像或成员列表） 3.语音球
  uint32 item_source = 5;  // 礼物来源 PresentSourceType
  uint32 source_id = 6;    // item_source对应的类型ID。比如如果是背包物品，那这里就是背包物品ID
  uint32 send_type = 7;   // 送礼类型 PresentSendType
  uint32 appId = 8; //设备的appId，上报用
  uint32 marketId = 9; // 设备的marketId，上报用
  uint32 send_uid = 10; // 送礼用户的uid
  ServiceCtrlInfo service_info = 11; //service_info，部分逻辑会用到
  bool is_opt_valid = 12; // 操作是否可用，涉及工会贡献之类的一些数值会不会增加，主要用来防止小号刷红钻礼物
  string order_id = 13; // 邀请函送礼从外部传入orderid
  string ctime = 14; // t豆系统的外部时间
  uint32 channel_id = 15; // 房间id
}

message FellowSendPresentResp
{
  uint32 item_id = 1;
  PresentSendMsg msg_info = 2;
  uint32 member_contribution_added = 3;  // 增加的个人公会贡献
  uint32 count = 4;    // 礼物数量
  uint64 cur_tbeans = 5;    // 当前T豆余额
  uint32 item_source = 6;  // 礼物来源 PresentSourceType
  uint32 source_id = 7;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品ID, m
  uint32 source_remain = 8;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品的剩余数量
}


message AllMicSendPresentReq
{
  uint32 item_id = 1;
  uint32 channel_id = 2;  // 通过房间赠送礼物时才需要填
  uint32 count = 3;  // 礼物数量
  uint32 send_source = 4;  // 赠送时的点击来源 PresentSendSourceType 0.默认类型(兼容旧版本) 1.送礼转盘 2.礼物架（点击头像或成员列表） 3.语音球
  uint32 item_source = 5;  // 礼物来源 PresentSourceType
  uint32 source_id = 6;    // 如果是背包里的礼物，就是背包礼物对应的唯一id
  uint32 batch_type = 7;  // 批量送礼类型 PresentBatchSendType
  uint32 send_type = 8;     // 送礼类型 PresentSendType
  DrawPresentPicture draw_present_pic = 9;       // 涂鸦礼物图
  uint32 app_id = 10; //base_req里的appId
  uint32 market_id = 11; // base_req里的marketId
  repeated uint32 target_uid_list = 12; // 收礼用户id列表
  uint32 send_uid = 13; // 送礼用户的uid
  ServiceCtrlInfo service_info = 14; //service_info，部分逻辑会用到
  bool is_opt_valid = 15; // 操作是否可用，主要用来防止小号刷红钻礼物
}

message AllMicSendPresentResp
{
  PresentBatchInfoMsg msg_info = 1;
  uint64 cur_tbeans = 2; // 当前T豆余额
  uint32 item_source = 3;  // 礼物来源 PresentSourceType
  uint32 source_id = 4;    // 如果是背包里的礼物，就是背包礼物对应的唯一id
  uint32 source_remain = 5;    // 如果是背包里的礼物，就是背包礼物的剩余数量
  repeated PresentTargetUserInfo target_list = 6;  // 收礼对象列表
  PresentSendItemInfo item_info = 7;    // 礼物信息
}

message MagicSendPresentReq
{
  repeated GiftItem gift_item_list = 1;  // 收礼列表，包括每个用户中了什么礼物
  uint32 channel_id = 2;    // 通过房间赠送礼物时才需要填
  uint32 send_uid = 3; // 送礼用户的uid
  uint32 app_id = 4;       //base_req里的appId
  uint32 market_id = 5;    // base_req里的marketId
  ServiceCtrlInfo service_info = 6; //service_info，部分逻辑会用到
  string consume_order_id = 7; // 总订单id
  bytes comb_config = 8; //see CombConfig
  bool is_batch = 9;     // 0.普通送礼 1.批量送礼
  uint32 magic_spirit_id = 10;     // ID
  uint32 magic_spirit_cnt = 11;  // 数量,送出礼物个数，全麦是麦上用户数量
  string magic_spirit_name = 12;  // 幸运精灵名字
  string magic_spirit_icon = 13;  // 幸运精灵icon
  uint32 send_time = 14;  // 送礼时间，对账以此为准
  uint32 bind_channel_id = 15; // 官频房间id
  bool sys_auto_send = 16;  // 是否是到期系统自动送出（仅用于开箱礼物）
}


message GiftItem{
  uint32 uid = 1;
  uint32 item_id = 2;
  string order_id = 3;
  uint32 count = 4;
  uint32 award_effect = 5; // 中奖特效 see MagicSpiritEffectType
  string deal_token = 6;
}

message MagicSendPresentResp
{
  MagicPresentInfoMsg msg_info = 1;
  SendMagicSpiritOpt  spirit_opt = 2; //
}

// 幸运礼物回包信息
message MagicPresentInfoMsg
{
  uint32 total_item_count = 1;  // 送出的礼物总数量
  uint32 batch_type = 2;  // 批量送礼类型 PresentBatchSendType
  uint64 send_time = 3;
  uint32 channel_id = 5;
  uint32 send_uid = 6;
  string send_account = 7;
  string send_nickname = 8;
  string extend_json = 9;
  uint32 total_price = 10;
}

//各种type，方便查阅

// 送礼来源类型
enum PresentSendSourceType
{
  E_SEND_SOURCE_DEFLAUTE = 0;         //默认类型(兼容旧版本)
  E_SEND_SOURCE_GIFT_TURNTABLE = 1;   //送礼转盘
  E_SEND_SOURCE_GIFT_SHELF = 2;       //礼物架
  E_SEND_SOURCE_SPEECH_BALL = 3;      //语音球
  E_SEND_SOURCE_DRAW_GIFT = 4;         //手绘
  E_SEND_SOURCE_MASKED_CALL = 5;      //语音匹配聊天
  E_SEND_SOURCE_IM = 6;                // IM
  E_SEND_SOURCE_OFFICIAL_CHANNEL = 7;      // 粉丝团抽奖
  E_SEND_SOURCE_FELLOW = 8;      //挚友信物
  E_SEND_SOURCE_MAGIC = 9;      //幸运礼物
}

//购买还是背包
enum PresentSourceType
{
  PRESENT_SOURCE_BUY = 0;      // 购买（红钻、T豆）
  PRESENT_SOURCE_PACKAGE = 1;    // 背包
  PRESENT_SOURCE_PACKAGE_FIRST = 2;    // 背包优先
  PRESENT_SOURCE_FELLOW = 3;    // 挚友信物
  PRESENT_SOURCE_MAGIC = 4;    // 幸运礼物
}

// 送礼类型
enum PresentSendType
{
  PRESENT_SEND_NORMAL = 0;       // 普通送礼
  PRESENT_SEND_DRAW = 1;         // 涂鸦送礼
  PRESENT_SENT_FANS_LOTTERY = 2; // 粉丝团送礼
}

// 批量送礼类型
enum PresentBatchSendType
{
  PRESENT_SOURCE_NONE = 0;
  PRESENT_SOURCE_ALL_MIC = 1;    // 全麦送礼
  PRESENT_SOURCE_WITH_UID = 2;    // 用传入的uid_list批量送礼
  PRESENT_SOURCE_WITH_ITEM_LIST = 3;    // 用传入的item_list批量送礼
}

// 送礼方式
enum PresentSendMethodType
{
  PRESENT_TYPE_ROOM = 0;      //房间送礼
  PRESENT_TYPE_IM = 1;      //IM送礼
  PRESENT_TYPE_FELLOW = 2;      //挚友信物
}

message SendMagicSpiritOpt {
  uint32 magic_spirit_id = 1;     // ID
  uint32 magic_spirit_cnt = 2;  // 数量,送出礼物个数，全麦是麦上用户数量
  uint32 send_uid = 3;
  string send_account = 4;
  string send_nickname = 5;
  uint32 channel_id = 6;   // 房间ID
  bool is_batch = 7;       // 0.普通送礼 1.批量送礼
  repeated MagicSpiritSendItem item_list = 8;  //收到的每个礼物对应信息
  bytes comb_info = 9; //see CombInfo
  uint32 total_item_price = 10;

  bool sys_auto_send = 11;  // 是否是到期系统自动送出（仅用于开箱礼物）
}

message MagicSpiritSendItem
{
  uint32 item_id = 1;
  uint32 show_effect = 2;  // 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE
  uint32 show_effect_v2 = 3; // 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE_V2
  uint32 flow_id = 4;    // 流光id
  bool is_batch = 5;     // 0.普通送礼 1.批量送礼
  bool show_batch_effect = 6;
  uint32 send_type = 7;      // 送礼类型 PresentSendType
  uint32 dynamic_template_id = 8;   // 非全屏礼物动效模板id
  bool is_visible_to_sender = 9;  //消息是否送礼者可见
  repeated MagicSpiritAwardInfo award_list = 10;  //奖品数量和获得者列表
  uint32 effect = 11;      // 中奖特效 see MagicSpiritEffectType
  string award_text = 12;
  string extern_info = 13;
}

message MagicSpiritAwardInfo
{
  uint32 item_id = 1;
  uint32 count = 2;
  uint64 send_time = 3;
  uint32 channel_id = 4;
  uint32 target_uid = 5;
  string target_account = 6;
  string target_nickname = 7;
  string item_order_id = 8; // 订单号，唯一标识
  string extend_json = 9; // 收礼人的魅力值相关信息
}

message LevelUpSurpriseKafkaInfo
{
  uint32 item_id = 1;
  uint32 count = 2;
  uint64 send_time = 3;
  uint32 channel_id = 4;
  uint32 target_uid = 5;
  string target_account = 6;
  string target_nickname = 7;
  string item_order_id = 8; // 订单号，唯一标识
  string extend_json = 9; // 收礼人的魅力值相关信息
}

service PresentMiddleware
{
  rpc SendPresent(SendPresentReq) returns (SendPresentResp){
  }

  rpc BatchSendPresent(BatchSendPresentReq) returns (BatchSendPresentResp){
  }

  rpc ImSendPresent(ImSendPresentReq) returns (ImSendPresentResp){
  }

  rpc AllMicSendPresent(AllMicSendPresentReq) returns (AllMicSendPresentResp){
  }

  rpc FellowSendPresent(FellowSendPresentReq) returns (FellowSendPresentResp){
  }

  rpc MagicSendPresent (MagicSendPresentReq) returns (MagicSendPresentResp){

  }

}