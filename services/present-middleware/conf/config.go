package conf

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/config"
)

type ServiceConfigT struct {
	StoreConfig        *config.MysqlConfig
	RedisConfig        *config.RedisConfig
	KafkaConfig        *config.KafkaConfig
	LevelupKafkaConfig *config.KafkaConfig
	AiKafkaConfig      *config.KafkaConfig
}

const (
	Debug      = "debug"
	Testing    = "testing"
	Staging    = "staging"
	Production = "production"
)

var Environment = Debug

func (sc *ServiceConfigT) Parse(ctx context.Context, configer config.Configer) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	Environment = configer.String("environment")

	sc.RedisConfig = config.NewRedisConfigWithSection(configer, "redis")
	sc.StoreConfig = config.NewMysqlConfigWithSection(configer, "mysql")
	sc.KafkaConfig = config.NewKafkaConfigWithSection(configer, "kafka")
	sc.LevelupKafkaConfig = config.NewKafkaConfigWithSection(configer, "levelup_kafka")
	sc.AiKafkaConfig = config.NewKafkaConfigWithSection(configer, "ai_kafka")
	return
}
