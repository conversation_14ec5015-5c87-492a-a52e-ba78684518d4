package pay

import (
	"context"
	"golang.52tt.com/pkg/log"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	backpackPB "golang.52tt.com/protocol/services/backpack-base"
	"golang.52tt.com/services/present-middleware/rpc/client"
)

type BackpackPayer struct {
}

func NewBackpackPayer() *BackpackPayer {
	s := &BackpackPayer{}
	return s
}

func (*BackpackPayer) Freeze(ctx context.Context, req *FreezeReq) (*FreezeResp, error) {
	resp := &FreezeResp{}
	resp.OrderResp = make(map[string]*OrderDetail)

	//  并发使用物品，使用同个物品要处理下
	for _, item := range req.OrderInfoList {
		resp.OrderResp[item.OrderId] = &OrderDetail{}

		useReq := &backpackPB.UseBackpackItemReq{
			Uid:      req.FreezeUser.GetUid(),
			ItemType: uint32(backpackPB.PackageItemType_BACKPACK_PRESENT),
			//UserItemId:    item.BackpackInfo.BackpackItemId,
			SourceId:      item.PresentConfig.GetItemId(),
			OrderId:       item.OrderId,
			OutsideTime:   uint32(req.FreezeTime.Unix()),
			UseCount:      item.Count,
			PriceType:     item.PresentConfig.GetPriceType(),
			ItemPrice:     item.PresentConfig.GetPrice() * item.Count,
			TargetUidList: item.BackpackInfo.UidList,
			ExtraInfo: &backpackPB.UseBackpackExtraInfo{
				ChannelId: item.BackpackInfo.ChannelId,
				UseCount:  item.Count,
			},
			BusinessType: req.BusinessType,
		}

		if req.IsAi {
			useReq.UseReasionType = uint32(backpackPB.LogType_LOG_TYPE_AI_PRESENT_USE)
		}

		useReq.OrderIdList = make([]string, 0)
		useReq.OrderIdList = append(useReq.OrderIdList, item.BackpackInfo.PayOrderList...)

		sv, _ := protogrpc.ServiceInfoFromContext(ctx)
		if sv.UserID == 0 {
			sv.UserID = useReq.Uid
			ctx = protogrpc.WithServiceInfo(ctx, sv)
		}

		res, err := client.BackpackCli.UseBackpackItem(ctx, useReq)
		resp.OrderResp[item.OrderId] = &OrderDetail{}
		if err != nil {
			log.ErrorWithCtx(ctx, "BackpackPayer - Freeze - UseBackpackItem fail. uid:%v backpackReq:%v err:%v", req.FreezeUser.Uid, useReq, err)
			resp.OrderResp[item.OrderId].Err = err
			continue
		}
		// 物品余额/过期时间，用于客户端更新背包显示，这里暂时没考虑用多种物品的情况
		resp.Balance = int64(res.GetRemain())
		resp.ExpireTime = int64(res.GetFinTime())
		resp.OrderResp[item.OrderId].DealToken = res.GetDealToken()
		resp.OrderResp[item.OrderId].OrderId = item.OrderId
	}

	//log.DebugWithCtx(ctx, "BackpackPayer -- Freeze done, %+v ", req)
	return resp, nil
}

// 背包不用commit
func (*BackpackPayer) Commit(ctx context.Context, req *CommitReq) (*CommitResp, error) {
	resp := &CommitResp{}
	return resp, nil
}
