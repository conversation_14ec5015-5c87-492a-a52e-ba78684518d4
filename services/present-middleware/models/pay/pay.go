package pay

import (
	"context"
	account "golang.52tt.com/clients/account-go"
	"golang.52tt.com/protocol/services/userpresent"
	"time"
)

type PayType uint32

const (
	PayType_Uknown    PayType = 0
	PayType_Tbean_Old PayType = 1
	PayType_Tbean_New PayType = 2
	PayType_Rediamond PayType = 3
	PayType_Backpack  PayType = 4
)

type OrderInfo struct {
	OrderId       string
	TargetUid     uint32
	PresentConfig *userpresent.StPresentItemConfig
	Count         uint32
	Reason        string
	BackpackInfo  *BackpackInfo
}

type OrderDetail struct {
	OrderId         string
	PayOrderId      string
	TbeanSystemTime string
	DealToken       string

	PresentConfig *userpresent.StPresentItemConfig
	Count         uint32

	Err error
}

type FreezeReq struct {
	FreezeUser    *account.User         // 被冻结的用户
	FreezeTime    time.Time             // 冻结请求时间
	AppId         string                // T豆系统的APPID
	UniqueOrderId string                // 总的订单id
	OrderInfoList map[string]*OrderInfo // 每一笔订单的信息
	BusinessType  uint32                // 业务类型，见userpresent_.proto BusinessType
	IsAi          bool
}

type FreezeResp struct {
	Balance    int64
	ExpireTime int64 // 背包物品的过期时间
	OrderResp  map[string]*OrderDetail
}

type CommitReq struct {
	FreezeUser      *account.User           // 被冻结的用户
	AppId           string                  // T豆系统的APPID
	UniqueOrderId   string                  // 总的订单id
	OrderDetailList map[string]*OrderDetail // 每一笔订单的信息
}

type CommitResp struct {
	OrderResp map[string]*OrderDetail
}

type BackpackInfo struct {
	BackpackItemId uint32
	PayOrderList   []string // 兼容背包的处理方式，一次性使用物品，但拆成多笔订单入库
	UidList        []uint32 // 兼容背包的处理方式，一次性使用物品，但拆成多笔订单入库
	ChannelId      uint32   // 背包需要房间id
}

type TbeanInfo struct {
}

type RedDiamondInfo struct {
}

type Factory struct {
	payer map[uint32]Payer
}

type Payer interface {
	Freeze(context.Context, *FreezeReq) (*FreezeResp, error)
	Commit(context.Context, *CommitReq) (*CommitResp, error)
}

func NewFactory() *Factory {
	sf := &Factory{}
	sf.payer = map[uint32]Payer{
		uint32(PayType_Tbean_Old): NewTbeanOldPayer(),
		uint32(PayType_Rediamond): NewDiamondPayer(),
		uint32(PayType_Backpack):  NewBackpackPayer(),
		uint32(PayType_Tbean_New): NewTbeanNewPayer(),
	}
	return sf
}

func (s *Factory) GetPayer(ty uint32) Payer {
	if s == nil {
		return nil
	}
	if _, ok := s.payer[ty]; !ok {
		return nil
	}
	return s.payer[ty]
}
