package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/sentinel"
	"golang.52tt.com/protocol/app"
	channelpb "golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/app/masked-pk-logic"
	"golang.52tt.com/protocol/common/status"
	liveFansPb "golang.52tt.com/protocol/services/channellivefans"
	masked_pk_live "golang.52tt.com/protocol/services/masked-pk-live"
	revenueExtGamePb "golang.52tt.com/protocol/services/revenue-ext-game"
	youknowwhopb "golang.52tt.com/protocol/services/youknowwho"
	"time"
)

// 开始pk流程（包括领取筹码）
func (s *MaskedPKLogic_) StartLiveChannelMaskedPK(ctx context.Context, in *pb.StartLiveChannelMaskedPKReq) (*pb.StartLiveChannelMaskedPKResp, error) {
	out := &pb.StartLiveChannelMaskedPKResp{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || in.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "StartLiveChannelMaskedPK fail to ServiceInfoFromContext. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID
	cid, serr := s.channelOLCli.GetUserChannelId(ctx, opUid, opUid)
	if serr != nil {
		log.ErrorWithCtx(ctx, "StartChannelMaskedPK fail, uid:%d, err:%v", opUid, serr)
		return out, serr
	}
	err := s.checkClientVersion(serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartLiveChannelMaskedPK fail to checkClientVersion. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	err = s.checkLiveChannelPermission(ctx, opUid, cid, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartLiveChannelMaskedPK fail to checkLiveChannelPermission. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	err = s.checkIfCanStartPK(ctx, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartLiveChannelMaskedPK fail to checkIfCanStartPK. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	_, err = s.maskedPKLiveCli.StartLiveChannelMaskedPK(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartLiveChannelMaskedPK fail to StartLiveChannelMaskedPK. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "StartLiveChannelMaskedPK uid:%v, in:%+v out:%+v", opUid, in, out)
	return out, nil
}

func (s *MaskedPKLogic_) checkIfCanStartPK(ctx context.Context, cid uint32) error {
	resp, err := s.revenueExtGameCli.GetMountExtGame(ctx, &revenueExtGamePb.GetMountExtGameReq{ChannelId: cid})
	if nil != err {
		log.ErrorWithCtx(ctx, "checkIfCanStartPK GetMountExtGame cid:%+v err:%v", cid, err)
		return err
	}

	if resp.GetGameType() > 0 {
		return protocol.NewExactServerError(nil, status.ErrRevenueExtGameCannotPlayOther, "互动玩法进行时不支持开启PK哦")
	}

	return nil
}

// 放弃参与本次比赛
func (s *MaskedPKLogic_) GiveUpLiveChannelMaskedPK(ctx context.Context, in *pb.GiveUpLiveChannelMaskedPKReq) (*pb.GiveUpLiveChannelMaskedPKResp, error) {
	out := &pb.GiveUpLiveChannelMaskedPKResp{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || in.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "GiveUpLiveChannelMaskedPK fail to ServiceInfoFromContext. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID
	cid, serr := s.channelOLCli.GetUserChannelId(ctx, opUid, opUid)
	if serr != nil {
		log.ErrorWithCtx(ctx, "StartChannelMaskedPK fail, uid:%d, err:%v", opUid, serr)
		return out, serr
	}
	
	err := s.checkClientVersion(serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GiveUpLiveChannelMaskedPK fail to checkClientVersion. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	err = s.checkLiveChannelPermission(ctx, opUid, cid, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GiveUpLiveChannelMaskedPK fail to checkLiveChannelPermission. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	_, err = s.maskedPKLiveCli.GiveUpLiveChannelMaskedPK(ctx, opUid, in.GetConfId(), cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GiveUpLiveChannelMaskedPK fail to StartLiveChannelMaskedPK. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "GiveUpLiveChannelMaskedPK uid:%v, in:%+v out:%+v", opUid, in, out)
	return out, nil
}

// 取消pk匹配
func (s *MaskedPKLogic_) CancelLiveChannelMaskedPK(ctx context.Context, in *pb.CancelLiveChannelMaskedPKReq) (*pb.CancelLiveChannelMaskedPKResp, error) {
	out := &pb.CancelLiveChannelMaskedPKResp{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || in.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "CancelLiveChannelMaskedPK fail to ServiceInfoFromContext. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID
	cid, serr := s.channelOLCli.GetUserChannelId(ctx, opUid, opUid)
	if serr != nil {
		log.ErrorWithCtx(ctx, "StartChannelMaskedPK fail, uid:%d, err:%v", opUid, serr)
		return out, serr
	}
	err := s.checkClientVersion(serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelLiveChannelMaskedPK fail to checkClientVersion. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	err = s.checkLiveChannelPermission(ctx, opUid, cid, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelLiveChannelMaskedPK fail to checkLiveChannelPermission. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	_, err = s.maskedPKLiveCli.CancelLiveChannelMaskedPK(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelLiveChannelMaskedPK fail to CancelLiveChannelMaskedPK. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "CancelLiveChannelMaskedPK uid:%v, in:%+v out:%+v", opUid, in, out)
	return out, nil
}

// 获取pk信息
func (s *MaskedPKLogic_) GetLiveChannelMaskedPKInfo(ctx context.Context, in *pb.GetLiveChannelMaskedPKInfoReq) (*pb.GetLiveChannelMaskedPKInfoResp, error) {
	out := &pb.GetLiveChannelMaskedPKInfoResp{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || in.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "GetLiveChannelMaskedPKInfo fail to ServiceInfoFromContext. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID
	cid := in.GetChannelId()

	err := s.checkClientVersion(serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveChannelMaskedPKInfo fail to checkClientVersion. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	err = s.checkLiveChannelPermission(ctx, opUid, cid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveChannelMaskedPKInfo fail to checkEntertainmentChannelType. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	resp, err := s.maskedPKLiveCli.GetLiveChannelMaskedPKInfo(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveChannelMaskedPKInfo fail to GetLiveChannelMaskedPKInfo. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	out.StatusInfo = s.fillPKStatusLivePb(resp.GetStatusInfo())

	if resp.GetStatusInfo().GetStatus() == uint32(pb.ChannelMaskedPKStatus_InPKing) {
		out.PkBattleInfo = s.fillPKBattleLivePb(ctx, resp.GetPkBattleInfo())
	}

	if resp.GetStatusInfo().GetStatus() == uint32(pb.ChannelMaskedPKStatus_InReviving) {
		out.ReviveInfo = &pb.ChannelMaskedPKRevive{
			ChannelId: resp.GetReviveInfo().GetChannelId(),
			Goal:      resp.GetReviveInfo().GetGoal(),
			Curr:      resp.GetReviveInfo().GetCurr(),
			EndTs:     resp.GetReviveInfo().GetEndTs(),
			ServerNs:  resp.GetReviveInfo().GetServerNs(),
		}
	}

	out.ServerNs = time.Now().UnixNano()

	log.DebugfWithCtx(ctx, "GetLiveChannelMaskedPKInfo in:%+v, out:%+v", in, out)
	return out, nil
}

// 获取当前pk活动配置
func (s *MaskedPKLogic_) GetLiveChannelMaskedPKConf(ctx context.Context, in *pb.GetLiveChannelMaskedPKConfReq) (*pb.GetLiveChannelMaskedPKConfResp, error) {
	out := &pb.GetLiveChannelMaskedPKConfResp{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || in.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "GetLiveChannelMaskedPKConf fail to ServiceInfoFromContext. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID
	cid := in.GetChannelId()

	err := s.checkClientVersion(serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveChannelMaskedPKConf fail to checkClientVersion. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	err = s.checkLiveChannelPermission(ctx, opUid, cid, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveChannelMaskedPKConf fail to checkLiveChannelPermission. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	resp, err := s.maskedPKLiveCli.GetLiveChannelMaskedPKCurrConfWithUser(ctx, opUid, in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveChannelMaskedPKConf fail to GetLiveChannelMaskedPKCurrConfWithUser. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	out.Conf = &pb.ChannelMaskedPKConf{
		BeginTs:          resp.GetConf().GetBeginTs(),
		EndTs:            resp.GetConf().GetEndTs(),
		ChipRole:         resp.GetConf().GetChipRole(),
		Chip:             resp.GetConf().GetChip(),
		ContinueMatch:    resp.GetConf().GetContinueMatch(),
		AutoMatchingCnt:  resp.GetConf().GetAutoMatchingCnt(),
		JumpUrl:          resp.GetConf().GetJumpUrl(),
		ConfId:           resp.GetConf().GetConfId(),
		ChipNotEnough:    resp.GetConf().GetChipNotEnough(),
		DivideType:       resp.GetConf().GetDivideType(),
		ServerNs:         time.Now().UnixNano(),
		ChipReceiveEndTs: resp.GetConf().GetChipReceiveEndTs(),
	}

	out.IsGiveUp = resp.GetIsGiveUp()

	log.DebugWithCtx(ctx, "GetLiveChannelMaskedPKConf in:%+v, out:%+v", in, out)
	return out, nil
}

// 获取语音直播间观众火力榜
func (s *MaskedPKLogic_) GetLivePKAudienceRank(ctx context.Context, in *pb.GetLivePKAudienceRankReq) (*pb.GetLivePKAudienceRankResp, error) {
	out := &pb.GetLivePKAudienceRankResp{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || in.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "GetLivePKAudienceRank fail to ServiceInfoFromContext. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID
	cid := in.GetChannelId()

	err := s.checkClientVersion(serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLivePKAudienceRank fail to checkClientVersion. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	err = s.checkLiveChannelPermission(ctx, opUid, cid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLivePKAudienceRank fail to checkLiveChannelPermission. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	resp, err := s.maskedPKLiveCli.GetAudienceRankList(ctx, opUid, in.GetChannelId(), in.GetBegin(), in.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLivePKAudienceRank fail to GetAudienceRankList. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	uidList := make([]uint32, 0)
	out.RankList = make([]*pb.LiveMaskedPKAudienceInfo, 0, len(resp.GetRankList()))

	for _, info := range resp.GetRankList() {
		uidList = append(uidList, info.GetUid())
	}

	fansResp, err := s.fansCli.BatchGetFansInfo(ctx, opUid, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLivePKAudienceRank fail to BatchGetFansInfo. uid:%v, in:%+v err:%v", opUid, in, err)
	}

	// 贵族信息
	nobilityInfoMap, err := s.nobilityClient.BatchGetNobilityInfo(ctx, in.ChannelId, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLivePKAudienceRank fail to BatchGetNobilityInfo. uid:%v, in:%+v err:%v", opUid, in, err)
	}
	//房间成员vip信息
	memberVipMap, err := s.memberRank.BatGetUserConsumeInfo(ctx, opUid, in.GetChannelId(), uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLivePKAudienceRank fail to BatGetUserConsumeInfo. uid:%v, in:%+v err:%v", opUid, in, err)
	}

	mapNum, err := s.numericClient.BatchGetPersonalNumeric(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLivePKAudienceRank fail to BatchGetPersonalNumeric. uid:%v, in:%+v err:%v", opUid, in, err)
	}

	ykwMap := s.getYKWMap(ctx, uidList)

	tureUidMap := s.youKnowWhoClient.BatchGetMapTrueUidByFake(ctx, uidList)

	for _, rank := range resp.GetRankList() {
		var rich, charm uint32 = 0, 0
		if num, ok := mapNum[rank.Uid]; ok {
			rich = uint32(int(num.Rich64 / 1000))
			charm = uint32(int(num.Charm64 / 1000))
		}

		var pinfo *app.FansPlateInfo
		groupName := ""
		var fansLv uint32 = 0

		if fansResp != nil {
			fan, ok := fansResp[rank.Uid]
			if ok {
				groupName = fan.GetGroupName()
				plateInfo := fan.GetPlateInfo()
				fansLv = fan.GetFansLevel()
				pinfo = s.fillPlateConfigMsg(plateInfo)
			}
		}

		ukwInfo := &app.UserUKWInfo{}
		ykwInfo := ykwMap[rank.Uid]
		log.Debugf("get  trueUid:%d, rank.Uid:%d", tureUidMap[rank.Uid], rank.Uid)
		if tureUidMap[rank.Uid] != rank.Uid {
			ukwInfo = &app.UserUKWInfo{
				Level:     ykwInfo.GetUkwPersonInfo().GetLevel(),
				Medal:     ykwInfo.GetUkwPersonInfo().GetMedal(),
				HeadFrame: ykwInfo.GetUkwPersonInfo().GetHeadFrame(),
			}
		}
		out.RankList = append(out.RankList, &pb.LiveMaskedPKAudienceInfo{
			Uid:      rank.Uid,
			Account:  rank.GetProfile().GetAccount(),
			Nickname: rank.GetProfile().GetNickname(),
			Score:    rank.Score,
			Sex:      rank.GetProfile().GetSex(),
			UserProfile: &app.UserProfile{
				Uid:      rank.Uid,
				Account:  rank.GetProfile().GetAccount(),
				Nickname: rank.GetProfile().GetNickname(),
				Sex:      rank.GetProfile().GetSex(),
				Privilege: &app.UserPrivilege{
					Account:  rank.GetProfile().GetAccount(),
					Nickname: rank.GetProfile().GetNickname(),
					Type:     rank.GetProfile().GetPrivilege().GetType(),
					Options:  rank.GetProfile().GetPrivilege().GetOptions(),
				},
			},
			Rich:            rich,
			Charm:           charm,
			NobilityLevel:   nobilityInfoMap[rank.Uid].GetLevel(),
			GroupFansLevel:  fansLv,
			ChannelMemLevel: memberVipMap[rank.Uid].GetCurrLevelId(),
			GroupName:       groupName,
			PlateInfo:       pinfo,
			UkwInfo:         ukwInfo,
			NobilityInfo: &app.NobilityInfo{
				Level:  nobilityInfoMap[rank.Uid].GetLevel(),
				FLevel: nobilityInfoMap[rank.Uid].GetFLevel(),
			},
		})
	}

	log.DebugWithCtx(ctx, "GetLivePKAudienceRank in:%+v, out:%+v", in, out)
	return out, nil
}

// 获取直播房快捷送礼列表
func (s *MaskedPKLogic_) GetLiveQuickSendPresentConfig(ctx context.Context, in *pb.GetLiveQuickSendPresentConfigReq) (*pb.GetLiveQuickSendPresentConfigResp, error) {
	out := &pb.GetLiveQuickSendPresentConfigResp{}
	out.Tips = s.sc.LiveTips
	out.PresentList = s.sc.GetLivePresentConfig()

	res, err := s.channelCli.GetChannelSimpleInfo(ctx, 0, in.ChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetQuickSendPresentConfig fail to GetChannelQualifiedAnchorList. cid:%+v err:%v", in.ChannelId, err)
		return out, err
	}

	binUid := res.GetBindId()
	ykwMap := s.getYKWMap(ctx, []uint32{binUid})
	out.PkUidList = append(out.PkUidList, ykwMap[binUid].GetUid())

	return out, nil
}

// 填充返回客户端的粉丝铭牌信息
func (s *MaskedPKLogic_) fillPlateConfigMsg(fansPlateInfo *liveFansPb.FansPlateInfo) *app.FansPlateInfo {
	var info *app.FansPlateInfo
	if fansPlateInfo.GetType() == uint32(app.FansPlateInfo_E_PLATE_COMMON) && fansPlateInfo.GetCommonPlateInfo() != nil {
		info = &app.FansPlateInfo{
			Type: fansPlateInfo.GetType(),
			CommonPlateInfo: &app.CommonPlateInfo{
				CommonImgUrl:     fansPlateInfo.GetCommonPlateInfo().GetCommonImgUrl(),
				LevelFontColor:   fansPlateInfo.GetCommonPlateInfo().GetLevelFontColor(),
				LevelShadowColor: fansPlateInfo.GetCommonPlateInfo().GetLevelShadowColor(),
			},
		}
	}

	var decorationConfig app.PlateDecorationConfig
	if fansPlateInfo.GetNamePlateInfo() != nil && fansPlateInfo.GetNamePlateInfo().GetPlateDecoration() != nil {
		decorationConfig.ImgUrl = fansPlateInfo.GetNamePlateInfo().GetPlateDecoration().GetImgUrl()
		decorationConfig.Width = fansPlateInfo.GetNamePlateInfo().GetPlateDecoration().GetWidth()
		decorationConfig.Height = fansPlateInfo.GetNamePlateInfo().GetPlateDecoration().GetHeight()
	}

	if fansPlateInfo.GetType() == uint32(app.FansPlateInfo_E_PLATE_GROUP_NAME) && fansPlateInfo.GetNamePlateInfo() != nil {
		info = &app.FansPlateInfo{
			Type: fansPlateInfo.GetType(),
			NamePlateInfo: &app.GroupNamePlateInfo{
				ImgUrl:           fansPlateInfo.GetNamePlateInfo().GetImgUrl(),
				NameFontColor:    fansPlateInfo.GetNamePlateInfo().GetNameFontColor(),
				Width:            fansPlateInfo.GetNamePlateInfo().GetWidth(),
				Height:           fansPlateInfo.GetNamePlateInfo().GetHeight(),
				PlateDecoration:  &decorationConfig,
				NameShadowColor:  fansPlateInfo.GetNamePlateInfo().GetNameShadowColor(),
				LevelShadowColor: fansPlateInfo.GetNamePlateInfo().GetLevelShadowColor(),
				LevelFontColor:   fansPlateInfo.GetNamePlateInfo().GetLevelFontColor(),
			},
		}
	}
	return info
}

func (s *MaskedPKLogic_) checkLiveChannelPermission(ctx context.Context, opUid, cid uint32, opPermission bool) error {
	channelInfo, err := s.channelCli.GetChannelSimpleInfo(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkLiveChannelPermission fail to GetChannelSimpleInfo. uid:%v, cid:%+v err:%v", opUid, cid, err)
		return err
	}

	// 检查房间类型
	if channelInfo.GetChannelType() != uint32(channelpb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		log.ErrorWithCtx(ctx, "checkLiveChannelPermission fail. uid:%v, cid:%+v err: wrong channel type ", opUid, cid)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)

	}

	if opPermission {
		if channelInfo.GetBindId() != opUid {
			log.ErrorWithCtx(ctx, "checkLiveChannelPermission fail. uid:%v, cid:%+v err: user not permission ", opUid, cid)
			return protocol.NewExactServerError(nil, status.ErrAccountPermissionDenied)
		}
	}

	return nil
}

func (s *MaskedPKLogic_) fillPKStatusLivePb(info *masked_pk_live.ChannelMaskedPKStatus) *pb.ChannelMaskedPKStatus {
	return &pb.ChannelMaskedPKStatus{
		ChannelId:     info.GetChannelId(),
		CurrChip:      info.GetCurrChip(),
		WinCnt:        info.GetWinCnt(),
		Status:        info.GetStatus(),
		RevivedCnt:    info.GetRevivedCnt(),
		RestReviveCnt: info.GetRestReviveCnt(),
		LossChip:      info.GetLossChip(),
		StatusEndTs:   info.GetStatusEndTs(),
		StatusDesc:    info.GetStatusDesc(),
		RestCancelCnt: info.GetRestCancelCnt(),
		ServerNs:      info.GetServerNs(),
		PkConf: &pb.ChannelMaskedPKConf{
			BeginTs:          info.GetPkConf().GetBeginTs(),
			EndTs:            info.GetPkConf().GetEndTs(),
			ChipRole:         info.GetPkConf().GetChipRole(),
			Chip:             info.GetPkConf().GetChip(),
			ContinueMatch:    info.GetPkConf().GetContinueMatch(),
			AutoMatchingCnt:  info.GetPkConf().GetAutoMatchingCnt(),
			JumpUrl:          info.GetPkConf().GetJumpUrl(),
			ConfId:           info.GetPkConf().GetConfId(),
			ChipNotEnough:    info.GetPkConf().GetChipNotEnough(),
			DivideType:       info.GetPkConf().GetDivideType(),
			ServerNs:         info.GetPkConf().GetServerNs(),
			ChipReceiveEndTs: info.GetPkConf().GetChipReceiveEndTs(),
		},
	}
}

func (s *MaskedPKLogic_) fillPKBattleLivePb(ctx context.Context, info *masked_pk_live.ChannelMaskedPKBattle) *pb.ChannelMaskedPKBattle {
	memList := make([]*pb.ChannelMaskedPKInfo, 0)
	for _, info := range info.GetMemList() {
		topAnchorList := make([]*pb.ChannelMaskedPKAnchor, 0)
		for _, anchor := range info.GetTopAnchorList() {
			profile := anchor.GetProfile()
			channelMaskedPKAnchor := &pb.ChannelMaskedPKAnchor{
				Uid:      anchor.GetUid(),
				Score:    anchor.GetScore(),
				Account:  anchor.GetProfile().GetAccount(),
				NickName: anchor.GetProfile().GetNickname(),
				Sex:      anchor.GetProfile().GetSex(),
				UserProfile: &app.UserProfile{
					Uid:      anchor.GetUid(),
					Account:  profile.GetAccount(),
					Nickname: profile.GetNickname(),
					Sex:      profile.GetSex(),
				}}

			if len(profile.GetPrivilege().GetAccount()) > 0 {
				channelMaskedPKAnchor.UserProfile.Privilege = &app.UserPrivilege{
					Account:  profile.GetPrivilege().GetAccount(),
					Nickname: profile.GetPrivilege().GetNickname(),
					Type:     profile.GetPrivilege().GetType(),
					Options:  profile.GetPrivilege().GetOptions(),
				}
			}
			topAnchorList = append(topAnchorList, channelMaskedPKAnchor)
		}

		memList = append(memList, &pb.ChannelMaskedPKInfo{
			ChannelId:     info.GetChannelId(),
			CurrChip:      info.GetCurrChip(),
			WinCnt:        info.GetWinCnt(),
			RevivedCnt:    info.GetReviveCnt(),
			Score:         info.GetScore(),
			LossDesc:      info.GetLossDesc(),
			LossChip:      info.GetLossChip(),
			TopAnchorList: topAnchorList,
			QuickKillInfo: &pb.QuickKillInfo{
				QuickKillDesc:       info.GetQuickKillInfo().GetQuickKillDesc(),
				QuickKillDescPrefix: info.GetQuickKillInfo().GetQuickKillDescPrefix(),
				QuickKillEndTs:      info.GetQuickKillInfo().GetQuickKillEndTs(),
				EnableMinPkSec:      info.GetQuickKillInfo().GetEnableMinPkSec(),
				EnableMaxPkSec:      info.GetQuickKillInfo().GetEnableMaxPkSec(),
				ConditionValue:      info.GetQuickKillInfo().GetConditionValue(),
			},
			PeakPkInfo: &pb.PeakPkInfo{PeakDesc: info.GetPeakPkInfo().GetPeakDesc()},
		})
	}
	out := &pb.ChannelMaskedPKBattle{
		PkId:        info.GetPkId(),
		MemList:     memList,
		PkEndTs:     info.GetPkEndTs(),
		ChipRole:    info.GetChipRole(),
		ServerNs:    time.Now().UnixNano(),
		ValidPkDesc: info.GetValidPkDesc(),
		ValidPk:     info.GetValidPk(),
		SubPhrase:   info.GetSubPhrase(),
	}
	return out
}

func (s *MaskedPKLogic_) getYKWMap(ctx context.Context, uidList []uint32) map[uint32]*youknowwhopb.UKWInfo {
	ykwMap := make(map[uint32]*youknowwhopb.UKWInfo)

	var trueUidList []uint32
	uidMap := make(map[uint32]uint32)
	for _, uid := range uidList {
		trueUid, err := s.youKnowWhoClient.GetTrueUidByFake(ctx, uid)
		if err != nil {
			if !sentinel.IsBreakerServerErr(err) {
				continue
			}
		}
		if trueUid > 0 {
			trueUidList = append(trueUidList, trueUid)
			uidMap[trueUid] = uid
		} else {
			uidMap[uid] = uid
		}

	}
	log.Debugf("GetTrueUidByFake trueUidList:%+v", trueUidList)

	ykwInfo, err := s.youKnowWhoClient.BatchGetUKWInfo(ctx, trueUidList)
	if err != nil {
		if !sentinel.IsBreakerServerErr(err) {
			log.ErrorWithCtx(ctx, "BatchGetUKWInfo fail  in:%+v err:%v", uidList, err)
			return ykwMap
		}
	}

	for _, v := range ykwInfo {
		uid := uidMap[v.Uid]
		if v.GetUkwPersonInfo().GetFakeUid() > 0 {
			v.Uid = v.GetUkwPersonInfo().GetFakeUid()
			ykwMap[uid] = v
		}
	}
	log.Debugf("getYKWMap ykwMap:%+v", ykwMap)
	return ykwMap
}
