package rpc

import (
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/clients/account"
    backpack_base "golang.52tt.com/clients/backpack-base"
    "golang.52tt.com/clients/expsvr"
    imApi "golang.52tt.com/clients/im-api"
    "golang.52tt.com/clients/nobility"
    numeric "golang.52tt.com/clients/numeric-go"
    parent_guardian "golang.52tt.com/clients/parent-guardian"
    push_notification "golang.52tt.com/clients/push-notification/v2"
    risk_mng_api "golang.52tt.com/clients/risk-mng-api"
    "golang.52tt.com/services/tt-rev/esport/common/user_group"
    coin_api "golang.52tt.com/services/virtual-image/virtual-image-card/internal/coin-api"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/config"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/pay_api"
)

type Client struct {
    AccountCli   account.IClient
    ImApiCli     imApi.IClient
    PushCli      push_notification.IClient
    PayCli       pay_api.PayIClient
    CoinCli      coin_api.CoinIClient
    UserGroupCli *user_group.UserGroupCli
    ExpCli       *expsvr.Client
    NobilityCli  *nobility.Client
    NumericCli   *numeric.Client
    BackpackCli  backpack_base.IClient
    ParentGuardianCli parent_guardian.IClient
    RiskMngApiCli     risk_mng_api.IClient
}

func NewClient() (*Client, error) {
    var err error
    ctx := context.Background()
    client := &Client{}

    client.PushCli = push_notification.NewIClient()
    client.AccountCli = account.NewIClient()
    client.ImApiCli, _ = imApi.NewClient()
    client.BackpackCli = backpack_base.NewIClient()

    payConf := config.GetDynamicConfig().PayApiConf
    coinConf := config.GetDynamicConfig().CoinClientConfig
    if payConf.UseGrpcRequest {
        client.PayCli, err = pay_api.NewPayGrpcClient(ctx, payConf.PayApiClientID)
        if err != nil {
            log.Errorf("NewPayGrpcClient err: %v", err)
            return nil, err
        }
        client.CoinCli, err = coin_api.NewCoinGrpcClient(ctx, payConf.PayApiClientID)
        if err != nil {
            log.Errorf("NewCoinGrpcClient err: %v", err)
            return nil, err
        }
    } else {
        client.PayCli = pay_api.NewPayHttpClient(payConf.PayApiHost, payConf.PayApiClientID, payConf.PayApiKey)
        client.CoinCli, err = coin_api.NewCoinHttpClient(coinConf.ContextPath, coinConf.BusinessCode, coinConf.SecretKey)
        if err != nil {
            log.Errorf("NewCoinClient failed, coinConf:%+v, err: %v", coinConf, err)
            return nil, err
        }
    }

    ExpCli := expsvr.NewClient()
    NumericCli, _ := numeric.NewClient()

    NobilityCli, err := nobility.NewClient()
    if err != nil {
        log.ErrorWithCtx(context.Background(), "nobility.NewClient() failed err:%v", err)
        return nil, err
    }

    userGroupServerCfg := config.GetDynamicConfig().UserGroupServerCfg
    if userGroupServerCfg != nil {
        userGroupCli := user_group.NewUserGroupCli(
            userGroupServerCfg.DspLpmAdminHost,
            userGroupServerCfg.DspLpmOfflineGroupHost,
            userGroupServerCfg.DspLpmApiserverHost)
        client.UserGroupCli = userGroupCli
    }

    client.ExpCli = ExpCli
    client.NobilityCli = NobilityCli
    client.NumericCli = NumericCli
    client.ParentGuardianCli  = parent_guardian.NewIClient()
    client.RiskMngApiCli  = risk_mng_api.NewIClient()
    return client, nil
}
