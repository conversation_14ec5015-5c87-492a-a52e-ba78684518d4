package mgr

import (
	"context"
	"fmt"
	"github.com/google/uuid"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protoGrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	kfk_virtual_image_card "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_virtual_image_card"
	riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
	pb "golang.52tt.com/protocol/services/virtual-image-card"
	"golang.52tt.com/services/tt-rev/common/goroutineex"
	"golang.52tt.com/services/virtual-image/virtual-image-card/internal/config"
	"golang.52tt.com/services/virtual-image/virtual-image-card/internal/pay_api"
	"golang.52tt.com/services/virtual-image/virtual-image-card/internal/store"
	"math"
	"strings"
	"time"
)

func (m *Mgr) PlaceOrder(ctx context.Context, in *pb.PlaceOrderReq, gainChannel uint8) (out *pb.PlaceOrderResp, err error) {
	log.InfoWithCtx(ctx, "PlaceOrder in: %+v", in)
	out = &pb.PlaceOrderResp{}

	// 获得对应的套餐配置
	packageConf := m.localCache.GetPackageConf(in.PackageId)
	if packageConf == nil {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "套餐不存在")
		return
	}
	isAuto := false // 是否自动续费套餐
	if packageConf.PackageType == uint32(pb.PackageType_PACKAGE_TYPE_AUTO_RENEW) {
		isAuto = true
	}

	// 共用参数
	svrInfo, _ := protoGrpc.ServiceInfoFromContext(ctx)
	now := time.Now()
	orderId := store.GenOrderId(in.Uid, now)
	var contractId string
	if isAuto {
		contractId = store.GenContractId(in.Uid, now)
	}

	// 如果订单享受优惠，要做频控。避免下了优惠单后，还没来得及处理回调，又下了另一个优惠单
	if isAuto && in.PayPriceCent == packageConf.DiscountPrice {
		err = m.cache.TryLockPlaceDiscountOrder(ctx, in.Uid, time.Second*time.Duration(config.GetDynamicConfig().PlaceDiscountOrderSecond))
		if err != nil {
			log.ErrorWithCtx(ctx, "PlaceOrder TryLockPlaceDiscountOrder err: %v", err)
			err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "发起订单太频繁，请稍后再试")
			return
		}
	}

	// 获取用户当前背包
	remain, err := m.store.GetRemain(ctx, in.Uid, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceOrder GetRemain err: %v", err)
		return
	}

	// 叠加开通时长上限检查，开通后有效期不能超过2年
	oldLeftDays := uint32(0)
	if remain != nil && remain.ExpireTime.After(now) {
		oldLeftDays = uint32(math.Ceil(remain.ExpireTime.Sub(now).Hours() / 24))
	}
	if oldLeftDays+packageConf.Days > config.GetDynamicConfig().MaxOpenDays {
		msg := fmt.Sprintf(config.GetDynamicConfig().MaxOpenDaysLimitHint, oldLeftDays, packageConf.Days)
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, msg)
		log.ErrorWithCtx(ctx, "PlaceOrder MaxOpenDaysLimit err: %v", err)
		return
	}

	// 计算下次扣款时间，主要是支付宝用，选在新的到期日前几天
	var nextPayTime time.Time
	if isAuto {
		newExpireTime := alignTime(now).Add(time.Duration(packageConf.Days) * time.Hour * 24)
		if remain != nil && remain.ExpireTime.After(now) { // 还没过期
			newExpireTime = alignTime(remain.ExpireTime).Add(time.Duration(packageConf.Days) * time.Hour * 24)
		}
		nextPayTime = config.GetAlipayNextPayTime(newExpireTime)
	}

	// 订单db参数
	order := &store.Order{
		Uid:        in.Uid,
		OrderId:    orderId,
		OrderRole:  store.OrderRoleUser,
		ContractId: contractId,
		PayChannel: uint8(in.PayChannel),
		PriceCent:  in.PayPriceCent,
		PackageId:  in.PackageId,
		CreateTime: now,
		Package:    *packageConf,
		GainChannel: gainChannel ,
	}
	log.DebugWithCtx(ctx, "PlaceOrder order: %+v", *order)

	// 签约db参数
	var contract *store.Contract
	if isAuto {
		contract = &store.Contract{
			Uid:           in.Uid,
			ContractId:    contractId,
			MarketId:      uint8(svrInfo.MarketID),
			ClientType:    uint8(svrInfo.ClientType),
			ClientVersion: svrInfo.ClientVersion,
			PayChannel:    uint8(in.PayChannel),
			PackageId:     in.PackageId,
			NextPayTime:   nextPayTime,
			Package:       *packageConf,
		}
		log.DebugWithCtx(ctx, "PlaceOrder contract: %+v", *contract)
	}

	// 发起订单请求参数
	placeOrderReq := &pb.ApiPlaceOrderReq{
		OrderType:              pay_api.FormatOrderType(pb.PackageType(packageConf.PackageType)),
		OsType:                 pay_api.FormatOsType(svrInfo.ClientType),
		PayChannel:             pay_api.FormatPayChannel(in.PayChannel),
		BusinessId:             config.GetMarketBusinessId(svrInfo.MarketID, isAuto),
		Fm:                     config.GetMarketFm(svrInfo.MarketID),
		Version:                fmt.Sprintf("%d", svrInfo.ClientVersion),
		CliOrderNo:             orderId,
		CliBuyerId:             fmt.Sprintf("%d", in.Uid),
		CliPrice:               pay_api.ConvertCent2Yuan(order.PriceCent),
		CliOrderTitle:          fmt.Sprintf("购买无限换装卡%s", packageConf.Name),
		CliOrderDesc:           fmt.Sprintf("购买无限换装卡%s", packageConf.Name),
		CliNotifyUrl:           config.GetDynamicConfig().PayApiConf.PayNotifyUrl,
		CreateTime:             now.Format("2006-01-02 15:04:05"),
		BundleId:               packageConf.ProductId,
		ProductId:              packageConf.ProductId,
		TimeOut:                fmt.Sprintf("%d", config.GetDynamicConfig().PayApiConf.PlaceOrderTimeoutMinute),
		OriginalTransactionIds: in.OriginalTransactionIds,
		BusinessScenceCode:     pay_api.BusinessScenceCode,
	}
	if isAuto {
		placeOrderReq.PeriodParam = &pb.ApiPeriodParam{
			ContractId:        contractId,
			PlanId:            pay_api.FormatPlanId(in.PayChannel),
			PeriodType:        "DAY",
			Period:            int64(packageConf.Days),
			ContractNotifyUrl: config.GetDynamicConfig().PayApiConf.ContractNotifyUrl,
			ExecuteTime:       nextPayTime.Format("2006-01-02 15:04:05"),
			ProductCode:       pay_api.FormatProductCode(in.PayChannel, svrInfo.MarketID),
			SingleAmount:      pay_api.ConvertCent2Yuan(packageConf.Price),
		}
	}
	log.DebugWithCtx(ctx, "PlaceOrder placeOrderReq: %+v", placeOrderReq)

	// 事务做各种更新
	var outRsp *pb.ApiPlaceOrderResp
	err = m.store.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
		var terr error

		// db添加订单
		_, terr = m.store.AddOrder(ctx, order, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PlaceOrder AddOrder err: %v", terr)
			return terr
		}

		// db添加签约
		if contract != nil {
			_, terr = m.store.AddContract(ctx, contract, tx)
			if terr != nil {
				log.ErrorWithCtx(ctx, "PlaceOrder AddContract err: %v", terr)
				return terr
			}
		}

		// 发起支付请求
		outRsp, terr = m.rpcCli.PayCli.PlaceOrder(ctx, in.Uid, svrInfo.MarketID, placeOrderReq)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PlaceOrder PlaceOrder err: %v", terr)
			if svrErr, transOk := terr.(protocol.ServerError); transOk {
				if svrErr.Code() == status.ErrSuperPlayerInvalidAppstoreUser { // 苹果AB账号重复订阅错误
					return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidAppstoreUser, svrErr.Message())
				}
			}
			return terr
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceOrder Transaction err: %v", err)
		return
	}
	log.DebugWithCtx(ctx, "PlaceOrder placeOrderRsp: %+v", outRsp)

	// 返回结果
	out.OrderNo = outRsp.GetOrderNo()
	out.Token = outRsp.GetToken()
	out.CliOrderNo = outRsp.GetCliOrderNo()
	out.CliOrderTitle = outRsp.GetCliOrderTitle()
	out.OrderPrice = outRsp.GetOrderPrice()
	out.Tsk = outRsp.GetTsk()
	out.ChannelMap = outRsp.GetChannelMap()
	log.InfoWithCtx(ctx, "PlaceOrder success, in: %+v, out: %+v, packageConf: %+v", in, out, packageConf)

	return
}

func (m *Mgr) PlaceAlipayAutoOrder(ctx context.Context, contract *store.Contract) {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 公共参数
	now := time.Now()
	orderId := store.GenSystemOrderId(contract.Uid, contract.NextPayTime)
	packageConf := contract.Package

	// 避免重试太频繁，需要间隔一段时间
	err := m.cache.TryLockPlaceAutoOrder(ctx, contract.Uid, contract.NextPayTime, time.Hour)
	if err != nil {
		return
	}

	// 获取当前背包
	remain, err := m.store.GetRemain(ctx, contract.Uid, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceAlipayAutoOrder GetRemain err: %v", err)
		return
	}
	if remain == nil { // 不应该出现
		log.ErrorWithCtx(ctx, "PlaceAlipayAutoOrder remain not found, uid: %d", contract.Uid)
		return
	}
	// 已经过期好久就不继续往下走了
	if contract.NextPayTime.Sub(remain.ExpireTime) > time.Second*time.Duration(config.GetDynamicConfig().PayApiConf.StopAutoOrderWaitSecond) {
		return
	}

	log.DebugWithCtx(ctx, "PlaceAlipayAutoOrder enter, contract: %+v", *contract)

	// 一直扣款不成功，发起主动解约。免得后面突然有钱扣上了，用户忘记这事，然后来投诉
	if config.CanSystemCancelContract(contract.NextPayTime) {
		m.systemCancelContract(ctx, contract, "自动下单一直失败")
		return
	}

	// 检查对应订单是否ok了，有的话直接返回成功
	preOrder, err := m.store.GetOrder(ctx, orderId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceAlipayAutoOrder GetOrder err: %v", err)
		return
	}
	if preOrder != nil && preOrder.Status == store.OrderStatusPaid {
		log.WarnWithCtx(ctx, "PlaceAlipayAutoOrder order already handle, order: %+v", *preOrder)
		return
	}

	// 订单db参数
	order := &store.Order{
		Uid:        contract.Uid,
		OrderId:    orderId,
		OrderRole:  store.OrderRoleSystem,
		ContractId: contract.ContractId,
		PayChannel: contract.PayChannel,
		PriceCent:  packageConf.Price,
		PackageId:  contract.PackageId,
		CreateTime: contract.NextPayTime,
		Package:    packageConf,
	}
	log.DebugWithCtx(ctx, "PlaceAlipayAutoOrder order: %+v", *order)

	// 发起订单请求参数
	autoPayReq := &pb.ApiAutoPayReq{
		OrderType:     pay_api.FormatOrderType(pb.PackageType(packageConf.PackageType)),
		OsType:        pay_api.FormatOsType(uint16(contract.ClientType)),
		PayChannel:    pay_api.FormatPayChannel(pb.PayChannel(contract.PayChannel)),
		BusinessId:    config.GetMarketBusinessId(uint32(contract.MarketId), true),
		Fm:            config.GetMarketFm(uint32(contract.MarketId)),
		Version:       fmt.Sprintf("%d", contract.ClientVersion),
		CliOrderNo:    orderId,
		CliBuyerId:    fmt.Sprintf("%d", contract.Uid),
		CliPrice:      pay_api.ConvertCent2Yuan(packageConf.Price),
		CliOrderTitle: fmt.Sprintf("购买无限换装卡%s", packageConf.Name),
		CliOrderDesc:  fmt.Sprintf("购买无限换装卡%s", packageConf.Name),
		CliNotifyUrl:  config.GetDynamicConfig().PayApiConf.PayNotifyUrl,
		CreateTime:    now.Format("2006-01-02 15:04:05"),
		BundleId:      packageConf.ProductId,
		ProductId:     packageConf.ProductId,
		DeductParam: &pb.DeductParam{
			ContractId:   contract.ContractId,
			PlanId:       pay_api.FormatPlanId(pb.PayChannel(contract.PayChannel)),
			ProductCode:  pay_api.FormatProductCode(pb.PayChannel(contract.PayChannel), uint32(contract.MarketId)),
			SingleAmount: pay_api.ConvertCent2Yuan(packageConf.Price),
		},
		BusinessScenceCode: pay_api.BusinessScenceCode,
	}
	log.DebugWithCtx(ctx, "PlaceAlipayAutoOrder autoPayReq: %+v", autoPayReq)

	// 事务做各种更新
	err = m.store.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
		var terr error

		// db添加订单
		_, terr = m.store.AddOrder(ctx, order, tx)
		if terr != nil && !mysql.IsDupEntryError(terr) { // 忽略重复错误
			log.ErrorWithCtx(ctx, "PlaceAlipayAutoOrder AddOrder err: %v", terr)
			return terr
		}

		// 发起支付请求
		_, terr = m.rpcCli.PayCli.AutoPay(ctx, contract.Uid, uint32(contract.MarketId), autoPayReq)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PlaceAlipayAutoOrder AutoPay err: %v", terr)
			return m.handleAlipayAutoOrderErr(ctx, contract, remain, terr) // 识别错误，以便决定是否保留订单记录
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceAlipayAutoOrder Transaction err: %v", err)
	} else {
		log.InfoWithCtx(ctx, "PlaceAlipayAutoOrder success, contract: %+v", *contract)
	}
}

func (m *Mgr) handleAlipayAutoOrderErr(ctx context.Context, contract *store.Contract, remain *store.Remain, err error) error {
	if pay_api.IsBalanceNotEnoughErr(err) { // 余额不足
		// 要发送一次余额不足通知
		m.processPush4FailAlipayAutoPay(ctx, contract, remain)

	} else if pay_api.IsDelayPayErr(err) { // 扣款时间不对
		delayPayReq := &pb.ApiDelayPayReq{
			ContractId:         contract.ContractId,
			BusinessId:         config.GetMarketBusinessId(uint32(contract.MarketId), true),
			BuyerId:            fmt.Sprintf("%d", contract.Uid),
			DeductTime:         time.Now().Format("2006-01-02 15:04:05"),
			Memo:               "发起延期扣款",
			PayChannel:         pay_api.FormatPayChannel(pb.PayChannel(contract.PayChannel)),
			BusinessScenceCode: pay_api.BusinessScenceCode,
		}
		_, terr := m.rpcCli.PayCli.DelayPay(ctx, contract.Uid, uint32(contract.MarketId), delayPayReq)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PlaceAlipayAutoOrder DelayPay err: %v, delayPayReq: %+v", terr, delayPayReq)
		} else {
			log.InfoWithCtx(ctx, "PlaceAlipayAutoOrder DelayPay success, delayPayReq: %+v", delayPayReq)
		}

	} else if pay_api.IsCloseOrderErr(err) {
		reason := fmt.Sprintf("自动下单err：%s", err.Error())
		m.systemCancelContract(ctx, contract, reason)

	} else {
		lines := []string{
			fmt.Sprintf("contract_id: %s", contract.ContractId),
			fmt.Sprintf("uid: %d", contract.Uid),
			fmt.Sprintf("err: %v", err),
		}
		m.SendFeiShuMsg("支付宝自动扣费未知错误", lines, "")
		return nil // 未知错误，提交事务，保留订单记录
	}

	return err // 已知错误，回滚事务，避免重试订单太多
}

// systemCancelContract 系统取消签约
func (m *Mgr) systemCancelContract(ctx context.Context, contract *store.Contract, reason string) {
	log.InfoWithCtx(ctx, "systemCancelContract: %+v", *contract)
	contractId, uid := contract.ContractId, contract.Uid

	// 发起解约请求参数
	cancelContractReq := &pb.ApiCancelContractReq{
		ContractId:         contract.ContractId,
		BusinessId:         config.GetMarketBusinessId(uint32(contract.MarketId), true),
		BuyerId:            fmt.Sprintf("%v", contract.Uid),
		ContractNotifyUrl:  config.GetDynamicConfig().PayApiConf.ContractNotifyUrl,
		ProductCode:        pay_api.FormatProductCode(pb.PayChannel(contract.PayChannel), uint32(contract.MarketId)),
		BusinessScenceCode: pay_api.BusinessScenceCode,
	}
	log.DebugWithCtx(ctx, "systemCancelContract cancelContractReq: %+v", cancelContractReq)

	// 事务做各种更新
	err := m.store.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
		// 更新db状态
		_, terr := m.store.UpdateContractStatus(ctx, contract.ContractId, store.ContractStatusCanceled, store.ContractStatusSigned, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "%s systemCancelContract UpdateContractStatus err: %v", contractId, terr)
			return terr
		}

		// 把变更写个db记录
		_, terr = m.store.AddContractHistory(ctx, &store.ContractHistory{
			ContractId:    contract.ContractId,
			Uid:           contract.Uid,
			PayChannel:    contract.PayChannel,
			PackageId:     contract.PackageId,
			PackageInfo:   contract.PackageInfo,
			Status:        store.ContractStatusCanceled,
			OperationRole: store.ContractHistoryOperationRoleSystem,
			Reason:        reason,
			CreateTime:    time.Now(),
		}, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "%s systemCancelContract AddContractHistory err: %v", contractId, terr)
			return terr
		}

		// 发起解约
		cancelContractResp, terr := m.rpcCli.PayCli.CancelContract(ctx, contract.Uid, uint32(contract.MarketId), cancelContractReq)
		if terr != nil {
			log.ErrorWithCtx(ctx, "%s systemCancelContract CancelContract err: %v", contractId, terr)
			return terr
		}
		log.DebugWithCtx(ctx, "%s systemCancelContract cancelContractResp: %+v", contractId, cancelContractResp)
		if !pay_api.IsCancelContractCodeOk(cancelContractResp.Code) {
			log.ErrorWithCtx(ctx, "%s systemCancelContract CancelContract fail: %+v", contractId, cancelContractResp)
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "发起解约失败")
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "%s systemCancelContract Transaction err: %v", contractId, err)
		return
	}

	// 各种变更通知
	goroutineex.GoroutineWithTimeoutCtx(ctx, time.Second*10, func(ctx context.Context) {
		_ = m.cache.DelUserCard(ctx, uid) // 删除用户缓存
	})
}

func (m *Mgr) PlaceAutoPayOrder(ctx context.Context, in *pb.PlaceAutoPayOrderReq) error {
	log.InfoWithCtx(ctx, "PlaceAutoPayOrder in: %+v", in)

	contract, err := m.store.GetContract(ctx, in.ContractId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceAutoPayOrder GetContract err: %v", err)
		return err
	}
	if contract == nil {
		log.ErrorWithCtx(ctx, "PlaceAutoPayOrder contract not found, contractId: %s", in.ContractId)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "签约信息不存在")
	}
	log.DebugWithCtx(ctx, "PlaceAutoPayOrder contract: %+v", *contract)

	// 公共参数
	now := time.Now()
	orderId := store.GenSystemOrderId(contract.Uid, contract.NextPayTime)
	packageConf := contract.Package

	// 避免重试太频繁，需要间隔一段时间
	err = m.cache.TryLockPlaceAutoOrder(ctx, contract.Uid, contract.NextPayTime, time.Minute)
	if err != nil {
		return err
	}

	// 苹果的话，套餐可能变了，要更新一下
	if len(in.ProductId) > 0 && in.ProductId != contract.Package.ProductId {
		log.WarnWithCtx(ctx, "PlaceAutoPayOrder productId changed, contractId: %s, old: %s, new: %s", contract.ContractId, contract.Package.ProductId, in.ProductId)
		newConf := m.localCache.GetProductConf(in.ProductId)
		if newConf == nil {
			log.ErrorWithCtx(ctx, "PlaceAutoPayOrder productId not found, productId: %s", in.ProductId)
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "套餐不存在")
		}
		contract.PackageId = newConf.Id
		contract.Package = *newConf
		packageConf = *newConf
	}

	// 检查对应订单是否ok了，有的话直接返回成功
	preOrder, err := m.store.GetOrder(ctx, orderId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceAutoPayOrder GetOrder err: %v", err)
		return err
	}
	if preOrder != nil && preOrder.Status == store.OrderStatusPaid {
		log.WarnWithCtx(ctx, "PlaceAutoPayOrder order already handle, order: %+v", *preOrder)
		return nil
	}

	// 订单db参数
	order := &store.Order{
		Uid:        contract.Uid,
		OrderId:    orderId,
		OrderRole:  store.OrderRoleSystem,
		ContractId: contract.ContractId,
		PayChannel: contract.PayChannel,
		PriceCent:  packageConf.Price,
		PackageId:  contract.PackageId,
		CreateTime: contract.NextPayTime,
		Package:    packageConf,
	}
	log.DebugWithCtx(ctx, "PlaceAutoPayOrder order: %+v", *order)

	// 发起订单请求参数
	autoPayReq := &pb.ApiAutoPayReq{
		OrderType:     pay_api.FormatOrderType(pb.PackageType(packageConf.PackageType)),
		OsType:        pay_api.FormatOsType(uint16(contract.ClientType)),
		PayChannel:    pay_api.FormatPayChannel(pb.PayChannel(contract.PayChannel)),
		BusinessId:    config.GetMarketBusinessId(uint32(contract.MarketId), true),
		Fm:            config.GetMarketFm(uint32(contract.MarketId)),
		Version:       fmt.Sprintf("%d", contract.ClientVersion),
		CliOrderNo:    orderId,
		CliBuyerId:    fmt.Sprintf("%d", contract.Uid),
		CliPrice:      pay_api.ConvertCent2Yuan(packageConf.Price),
		CliOrderTitle: fmt.Sprintf("购买无限换装卡%s", packageConf.Name),
		CliOrderDesc:  fmt.Sprintf("购买无限换装卡%s", packageConf.Name),
		CliNotifyUrl:  config.GetDynamicConfig().PayApiConf.PayNotifyUrl,
		CreateTime:    now.Format("2006-01-02 15:04:05"),
		BundleId:      packageConf.ProductId,
		ProductId:     packageConf.ProductId,
		DeductParam: &pb.DeductParam{
			ContractId:   contract.ContractId,
			PlanId:       pay_api.FormatPlanId(pb.PayChannel(contract.PayChannel)),
			ProductCode:  pay_api.FormatProductCode(pb.PayChannel(contract.PayChannel), uint32(contract.MarketId)),
			SingleAmount: pay_api.ConvertCent2Yuan(packageConf.Price),
		},
		BusinessScenceCode: pay_api.BusinessScenceCode,
	}
	log.DebugWithCtx(ctx, "PlaceAutoPayOrder autoPayReq: %+v", autoPayReq)

	// 事务做各种更新
	err = m.store.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
		var terr error

		// db添加订单
		_, terr = m.store.AddOrder(ctx, order, tx)
		if terr != nil && !mysql.IsDupEntryError(terr) { // 忽略重复错误
			log.ErrorWithCtx(ctx, "PlaceAutoPayOrder AddOrder err: %v", terr)
			return terr
		}

		// 更新签约信息
		_, terr = m.store.UpdateContractPackageInfo(ctx, contract, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PlaceAutoPayOrder UpdateContractPackageInfo err: %v", terr)
			return terr
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceAutoPayOrder Transaction err: %v", err)
		return err
	}

	// 发起支付请求
	_, err = m.rpcCli.PayCli.AutoPay(ctx, contract.Uid, uint32(contract.MarketId), autoPayReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceAutoPayOrder AutoPay err: %v", err)
		return err
	}

	return nil
}

func (m *Mgr) PayCallback(ctx context.Context, in *pb.PayCallbackReq) error {
	log.InfoWithCtx(ctx, "PayCallback in: %+v", in)
	orderId := in.GetCliOrderNo()
	payTime := time.Unix(in.GetPayTs(), 0)

	// 为了保险，还是先加个锁，避免同时触发
	lockVal := uuid.NewString()
	err := m.cache.TryLockCalcUserRemain(ctx, in.GetActiveUid(), lockVal)
	if err != nil {
		log.ErrorWithCtx(ctx, "PayCallback TryLockCalcUserRemain err: %v", err)
		return err
	}
	defer func() {
		_ = m.cache.UnLockCalcUserRemain(ctx, in.GetActiveUid(), lockVal)
	}()

	// 查询订单状态
	order, err := m.store.GetOrder(ctx, orderId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "PayCallback GetOrder err: %v", err)
		return err
	}
	if order == nil {
		log.ErrorWithCtx(ctx, "PayCallback order not found, orderId: %s", orderId)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "订单不存在")
	}
	if order.Status == store.OrderStatusPaid {
		log.InfoWithCtx(ctx, "PayCallback order already handle, order: %+v", *order)
		return nil
	}

	// 查询用户背包
	oldRemain, err := m.store.GetRemain(ctx, in.GetActiveUid(), nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "PayCallback GetRemain err: %v", err)
		return err
	}
	if oldRemain == nil {
		oldRemain = &store.Remain{Uid: in.GetActiveUid()} // 给个空的，避免空指针
	}

	// 计算变更前后的有效期
	newRemain := calcNewRemainTime(oldRemain, payTime, order.Package.Days, false)
	// 设置首购订单
	if len(newRemain.FirstBuyOrderId) == 0 {
		newRemain.FirstBuyOrderId = orderId
	}
	// 设置首购优惠标记
	if order.Package.DiscountPrice != 0 && in.PayPriceCent == order.Package.DiscountPrice && len(newRemain.DiscountOrderId) == 0 {
		newRemain.DiscountOrderId = orderId
	}

	// 计算本单的核销起止时间
	redemptionEndTime := newRemain.BuyExpireTime
	redemptionBeginTime := redemptionEndTime.Add(-time.Hour * 24 * time.Duration(order.Package.Days))

	// 如果是支付宝关联系统订单，更新下次扣款时间
	var nextPayTime time.Time
	if order.OrderRole == store.OrderRoleSystem && order.PayChannel == uint8(pb.PayChannel_PAY_CHANNEL_ALIPAY) && len(order.ContractId) > 0 {
		nextPayTime = config.GetAlipayNextPayTime(newRemain.ExpireTime)
	}

	// 事务做各种更新
	err = m.store.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
		var ok bool
		var terr error

		// 更新订单信息
		ok, terr = m.store.UpdateOrder(ctx, &store.Order{
			OrderId:        orderId,
			CoinOrderNo:    in.GetOrderNo(),
			ChannelOrderNo: in.GetOtherOrderNo(),
			PayTime:        payTime,
			PayPriceCent:   in.GetPayPriceCent(),
			CoinPayChannel: in.GetCoinPayChannel(),
			ActiveUid:      in.GetActiveUid(),
			Status:         store.OrderStatusPaid,
		}, order.Status, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PayCallback UpdatePaidOrder err: %v", terr)
			return terr
		}
		if !ok {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "订单信息已更新")
		}

		// 添加or更新背包
		_, terr = m.store.UpsertRemain(ctx, newRemain, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PayCallback UpsertRemain err: %v", terr)
			return terr
		}

		// 添加背包变更记录
		history := &store.RemainHistory{
			Uid:             in.GetActiveUid(),
			OrderId:         orderId,
			OrderInvokeTime: payTime,
			IsRevoke:        false,
			Reason:          "下单",
		}
		history.FillTime(oldRemain, newRemain)
		_, terr = m.store.AddRemainHistory(ctx, history, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PayCallback AddRemainHistory err: %v", terr)
			return terr
		}

		// 相应地更新下次扣款时间
		if !nextPayTime.IsZero() {
			_, terr = m.store.UpdateContractNextPayTime(ctx, order.ContractId, nextPayTime, tx)
			if terr != nil {
				log.ErrorWithCtx(ctx, "PayCallback UpdateContractNextPayTime err: %v", terr)
				return terr
			}
		}

		// 添加订单核销记录，这里只是简单append，后续可能需要优化为调整已有记录
		terr = m.store.AddOrderRedemption(ctx, &store.OrderRedemption{
			Uid:         in.GetActiveUid(),
			OrderId:     orderId,
			Days:        order.Package.Days,
			PackageType: order.Package.PackageType,
			BeginTime:   redemptionBeginTime,
			EndTime:     redemptionEndTime,
			Status:      store.OrderRedemptionStatusNormal,
		}, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PayCallback AddOrderRedemption err: %v", terr)
			return terr
		}

		// 处理体验卡核销逻辑
		terr = m.handleTrialRedemptionForPurchase(ctx, in.GetActiveUid(), orderId, payTime, newRemain, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PayCallback handleTrialRedemptionForPurchase err: %v", terr)
			return terr
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PayCallback Transaction err: %v", err)
		return err
	}

     gainChannel := kfk_virtual_image_card.GainChannel_GAIN_CHANNEL_PLACE_ORDER
	if order.GainChannel > 0 {
		gainChannel = kfk_virtual_image_card.GainChannel(order.GainChannel)
	}

	// 各种变更通知
	goroutineex.GoroutineWithTimeoutCtx(ctx, time.Second*10, func(ctx context.Context) {
		_ = m.cache.DelUserCard(ctx, in.GetActiveUid())                             // 删除用户缓存
		_ = m.cache.DelUserRedemption(ctx, in.GetActiveUid())                       // 删除用户核销缓存
		m.pushCardStatusChangeNotify(ctx, in.GetActiveUid(), newRemain)             // 给客户端推送有效期变更
		m.processPush4PayCallback(ctx, orderId)                                     // 大部分推送逻辑
		_ = m.cache.SetUserExpireTime(ctx, in.GetActiveUid(), newRemain.ExpireTime) // 设置用户到期队列
		// 发送变更kafka
		m.producer.SendCardChangeEvent(ctx, &kfk_virtual_image_card.UserVirtualImageCardChangeEvent{
			Uid:       in.GetActiveUid(),
			EventTs:   time.Now().Unix(),
			EventType: kfk_virtual_image_card.EventType_EVENT_TYPE_GAIN,
			EffectTs:  newRemain.EffectTime.Unix(),
			ExpireTs:  newRemain.ExpireTime.Unix(),
			Opt: &kfk_virtual_image_card.UserVirtualImageCardChangeEvent_GainOption{
				GainOption: &kfk_virtual_image_card.GainOption{
					GainChannel:      gainChannel, //kfk_virtual_image_card.GainChannel_GAIN_CHANNEL_PLACE_ORDER,
					IsFirstPlaceOrder: len(oldRemain.FirstBuyOrderId) == 0,
				},
			},
		})
	})

	return nil
}

func (m *Mgr) NotifyContract(ctx context.Context, in *pb.NotifyContractReq) error {
	log.InfoWithCtx(ctx, "NotifyContract in: %+v", in)

	// 查询签约信息
	oldContract, err := m.store.GetContract(ctx, in.ContractId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "NotifyContract GetContract err: %v", err)
		return err
	}
	if oldContract == nil {
		log.ErrorWithCtx(ctx, "NotifyContract contract not found, contractId: %s", in.ContractId)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "签约信息不存在")
	}
	log.DebugWithCtx(ctx, "NotifyContract old contract: %+v", *oldContract)

	// 构造新的签约信息
	newContract := &store.Contract{
		ContractId:  oldContract.ContractId,
		Uid:         oldContract.Uid,
		NextPayTime: oldContract.NextPayTime,
		Status:      oldContract.Status,
		PackageId:   oldContract.PackageId,
		Package:     oldContract.Package,
	}
	if in.IsSign {
		newContract.Status = store.ContractStatusSigned
	} else {
		newContract.Status = store.ContractStatusCanceled
	}

	// 苹果的话，套餐可能变了，要更新一下
	if len(in.ProductId) > 0 && in.ProductId != oldContract.Package.ProductId {
		log.WarnWithCtx(ctx, "NotifyContract productId changed, contractId: %s, old: %s, new: %s", oldContract.ContractId, oldContract.Package.ProductId, in.ProductId)
		newConf := m.localCache.GetProductConf(in.ProductId)
		if newConf == nil {
			log.ErrorWithCtx(ctx, "NotifyContract productId not found, productId: %s", in.ProductId)
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "套餐不存在")
		}
		newContract.PackageId = newConf.Id
		newContract.Package = *newConf
	}

	// 苹果的扣费时间由苹果决定
	if in.NextPayTs > 0 && oldContract.PayChannel == uint8(pb.PayChannel_PAY_CHANNEL_APPSTORE) {
		newContract.NextPayTime = time.Unix(in.NextPayTs, 0)
	}

	// 还是苹果，签约uid可能和请求的不一样，坑爹
	if in.ActiveUid > 0 && in.ActiveUid != oldContract.Uid {
		log.WarnWithCtx(ctx, "NotifyContract uid changed, contractId: %s, old: %d, new: %d", oldContract.ContractId, oldContract.Uid, in.ActiveUid)
		newContract.Uid = in.ActiveUid
	}

	// 事务做各种更新
	err = m.store.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
		var ok bool
		var terr error

		// 更新签约信息
		newContract.EncodePackageInfo()
		ok, terr = m.store.UpdateContract(ctx, newContract, oldContract.Status, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "NotifyContract UpdateContract err: %v", terr)
			return terr
		}
		if !ok {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "签约信息已更新")
		}

		// 添加签约变更历史
		if newContract.Status != oldContract.Status {
			_, terr = m.store.AddContractHistory(ctx, &store.ContractHistory{
				ContractId:    newContract.ContractId,
				Uid:           newContract.Uid,
				PayChannel:    oldContract.PayChannel,
				PackageId:     newContract.PackageId,
				PackageInfo:   newContract.PackageInfo,
				Status:        newContract.Status,
				OperationRole: store.ContractHistoryOperationRoleUser,
				Reason:        "",
				CreateTime:    time.Now(),
			}, tx)
			if terr != nil {
				log.ErrorWithCtx(ctx, "NotifyContract AddContractHistory err: %v", terr)
				return terr
			}
		}

		return nil
	})

	// 各种变更通知
	goroutineex.GoroutineWithTimeoutCtx(ctx, time.Second*10, func(ctx context.Context) {
		_ = m.cache.DelUserCard(ctx, in.GetActiveUid()) // 删除用户缓存
		isNewSign := oldContract.Status == store.ContractStatusInit && newContract.Status == store.ContractStatusSigned
		m.processPush4NotifyContract(ctx, in.ContractId, isNewSign) // 大部分推送逻辑
	})

	return nil
}

// RevokeOrder 订单退款
func (m *Mgr) RevokeOrder(ctx context.Context, in *pb.RevokeOrderReq) error {
	log.InfoWithCtx(ctx, "RevokeOrder in: %+v", in)
	orderId := in.GetOrderId()
	if orderId == "" || in.GetUid() == 0 {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	revokeTime, err := time.ParseInLocation("2006-01-02 15:04:05", in.GetNotifyTime(), time.Local)
	if err != nil {
		log.ErrorWithCtx(ctx, "RevokeOrder fail to ParseInLocation. in:%+v, err:%v", in, err)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "时间格式错误")
	}

	// 查询订单状态
	order, err := m.store.GetOrder(ctx, orderId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "RevokeOrder fail to GetOrder. in:%+v, err:%v", in, err)
		return err
	}
	if order == nil {
		log.ErrorWithCtx(ctx, "RevokeOrder order not found. in:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "订单不存在")
	}
	if order.Status == store.OrderStatusRevoke {
		return nil
	}
	if order.Status != store.OrderStatusPaid {
		log.ErrorWithCtx(ctx, "RevokeOrder order status err. in:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该订单未完成支付，无法退款")
	}

	// 为了保险，还是先加个锁，避免同时触发
	lockVal := uuid.NewString()
	err = m.cache.TryLockCalcUserRemain(ctx, order.ActiveUid, lockVal)
	if err != nil {
		log.ErrorWithCtx(ctx, "RevokeOrder TryLockCalcUserRemain err: %v", err)
		return err
	}
	defer func() {
		_ = m.cache.UnLockCalcUserRemain(ctx, order.ActiveUid, lockVal)
	}()

	// 查询用户背包
	uid := order.ActiveUid
	oldRemain, err := m.store.GetRemain(ctx, uid, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "RevokeOrder fail to GetRemain. in:%+v, err:%v", in, err)
		return err
	}
	if oldRemain == nil {
		log.ErrorWithCtx(ctx, "RevokeOrder remain not found. in:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "权益信息不存在")
	}

	now := time.Now()
	newRemain := oldRemain.Copy()

	// 事务操作
	err = m.store.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
		// 更新订单状态
		order.Status = store.OrderStatusRevoke
		ok, err := m.store.UpdateOrder(ctx, order, store.OrderStatusPaid, tx)
		if err != nil {
			log.ErrorWithCtx(ctx, "RevokeOrder fail to UpdateOrder. in:%+v, err:%v", in, err)
			return err
		}
		if !ok {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "订单信息已更新")
		}

		// 获取核销订单列表
		list, err := m.store.GetUserOrderRedemptionList(ctx, uid, now, tx)
		if err != nil {
			log.ErrorWithCtx(ctx, "RevokeOrder fail to GetUserOrderRedemptionList. in:%+v, err:%v", in, err)
			return err
		}

		// 生成待更新核销订单列表
		revokeRedemption, redemptionUpdateList, err := genRevokeRedemptionUpdateList(orderId, list, now)
		if err != nil {
			log.ErrorWithCtx(ctx, "RevokeOrder fail to genRevokeRedemptionUpdateList. in:%+v, err:%v", in, err)
			return err
		}

		// 更新核销订单列表
		err = m.store.BatchUpdateUserOrderRedemptionList(ctx, redemptionUpdateList, tx)
		if err != nil {
			log.ErrorWithCtx(ctx, "RevokeOrder fail to BatchUpdateUserOrderRedemptionList. in:%+v, err:%v", in, err)
			return err
		}

		// 不一定完全退，计算能退几天
		useDays := uint32(revokeRedemption.EndTime.Sub(revokeRedemption.BeginTime).Hours() / 24)
		if revokeRedemption.Days < useDays {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "订单已用天数大于剩余天数，无法回收权益")
		}
		reclaimDays := revokeRedemption.Days - useDays

		// 重新计算用户的权益信息
		newRemain.ExpireTime = oldRemain.ExpireTime.Add(-time.Duration(reclaimDays) * 24 * time.Hour)       // 总过期时间前移
		newRemain.BuyExpireTime = oldRemain.BuyExpireTime.Add(-time.Duration(reclaimDays) * 24 * time.Hour) // 购买过期时间前移
		if oldRemain.TrialExpireTime.After(now) {                                                           // 有体验时间未使用的话，往前移体验时间
			newRemain.TrialExpireTime = newRemain.ExpireTime
			newRemain.TrialEffectTime = newRemain.BuyExpireTime
		}

		// 更新背包
		_, err = m.store.UpsertRemain(ctx, newRemain, tx)
		if err != nil {
			log.ErrorWithCtx(ctx, "RevokeOrder fail to UpsertRemain. in:%+v, err:%v", in, err)
			return err
		}

		// 添加背包变更流水
		history := &store.RemainHistory{
			Uid:             uid,
			OrderId:         orderId,
			OrderInvokeTime: now,
			IsRevoke:        true,
			Reason:          "退款",
		}
		history.FillTime(oldRemain, newRemain)
		_, err = m.store.AddRemainHistory(ctx, history, tx)
		if err != nil {
			log.ErrorWithCtx(ctx, "RevokeOrder fail to AddRemainHistory. in:%+v, err:%v", in, err)
			return err
		}

		// 插入撤销记录
		revokeFlow := &store.OrderRevokeFlow{
			OrderId:       orderId,
			Uid:           order.Uid,
			PackageId:     order.PackageId,
			PayChannel:    order.PayChannel,
			PriceCent:     order.PriceCent,
			RevokeDays:    reclaimDays,
			RevokeOrderId: fmt.Sprintf("%s_revoke", orderId),
			ServerTime:    order.PayTime,
			CreateTime:    now,
			UpdateTime:    now,
		}
		if err = m.store.AddRevokeFlow(ctx, revokeFlow, tx); err != nil {
			log.ErrorWithCtx(ctx, "RevokeOrder AddRevokeFlow in:%+v, err: %v", in, err)
			return err
		}

		// 处理体验卡核销逻辑
		err = m.handleTrialRedemptionForRefund(ctx, uid, orderId, now, newRemain, tx)
		if err != nil {
			log.ErrorWithCtx(ctx, "RevokeOrder handleTrialRedemptionForRefund err: %v", err)
			return err
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "RevokeOrder Transaction in:%+v, err: %v", in, err)
		return err
	}

	// 各种变更通知
	goroutineex.GoroutineWithTimeoutCtx(ctx, time.Second*10, func(ctx context.Context) {
		_ = m.cache.DelUserCard(ctx, uid)                             // 删除用户缓存
		_ = m.cache.DelUserRedemption(ctx, uid)                       // 删除用户核销缓存
		m.pushCardStatusChangeNotify(ctx, uid, newRemain)             // 给客户端推送有效期变更
		_ = m.cache.SetUserExpireTime(ctx, uid, newRemain.ExpireTime) // 设置用户到期队列

		// 发送TT助手消息通知
		content := fmt.Sprintf("您在%s购买的无限换装卡【%s】已退款成功，已扣除相应有效期。",
			order.PayTime.Format("01月02日 15:04:05"), order.Package.Name)
		m.sendTTAssistantText(ctx, uid, content, "", "")
	})

	log.InfoWithCtx(ctx, "RevokeOrder done. in:%+v, %v", in, revokeTime)
	return nil
}

func genRevokeRedemptionUpdateList(orderId string, list []*store.OrderRedemption, now time.Time) (*store.OrderRedemption, []*store.OrderRedemption, error) {
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	updateList := make([]*store.OrderRedemption, 0)
	var revokeRedemption *store.OrderRedemption
	for _, v := range list {
		if v.OrderId == orderId {
			revokeRedemption = v
			break
		}
	}

	if revokeRedemption == nil || revokeRedemption.EndTime.Before(today) {
		// 该笔订单已核销完，无法处理退款
		return revokeRedemption, updateList, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该笔订单已核销完，无法处理退款")
	}

	if revokeRedemption.BeginTime.Before(today) {
		revokeRedemption.EndTime = today
	} else {
		revokeRedemption.EndTime = revokeRedemption.BeginTime
	}
	revokeRedemption.Status = store.OrderRedemptionStatusRefund
	updateList = append(updateList, revokeRedemption)

	// 重新计算后续订单的核销起始时间
	lastOrderEndTime := revokeRedemption.EndTime
	for _, v := range list {
		if v.OrderId == orderId || v.EndTime.Before(lastOrderEndTime) {
			// 之前的订单不处理
			continue
		}

		v.BeginTime = lastOrderEndTime
		v.EndTime = v.BeginTime.Add(time.Duration(v.Days) * 24 * time.Hour)
		lastOrderEndTime = v.EndTime

		updateList = append(updateList, v)
	}

	return revokeRedemption, updateList, nil
}

func (m *Mgr) GetContractById(ctx context.Context, contractId string) (*pb.GetContractByIdResp, error) {
	contract, err := m.store.GetContract(ctx, contractId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractById store err: %v", err)
		return nil, err
	}

	_, err = m.rpcCli.PayCli.GetContract(ctx, contract.Uid, uint32(contract.MarketId), &pb.ApiGetContractReq{
		ContractId:         contractId,
		BusinessId:         config.GetMarketBusinessId(uint32(contract.MarketId), true),
		BuyerId:            fmt.Sprintf("%d", contract.Uid),
		ProductCode:        pay_api.FormatProductCode(pb.PayChannel(contract.PayChannel), uint32(contract.MarketId)),
		BusinessScenceCode: pay_api.BusinessScenceCode,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractById payCli err: %v", err)
		return nil, err
	}

	// 货币返回的code乱七八糟，所以暂时未返回任何信息，只是打个日志
	return &pb.GetContractByIdResp{}, nil
}


func (m *Mgr) ActivityPlaceOrder(ctx context.Context, req *pb.ActivityPlaceOrderReq) (*pb.ActivityPlaceOrderResp, error) {
	out := &pb.ActivityPlaceOrderResp{}
	log.InfoWithCtx(ctx, "ActivityPlaceOrder req: %+v", req)
	if req.GetUid() == 0 {
		log.ErrorWithCtx(ctx, "ActivityPlaceOrder req: %+v, err: %v", req)
		return out,  protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求UID信息")
	}
	
	ctx = protoGrpc.InjectServiceInfo(ctx, &protoGrpc.ServiceInfo{
		ClientType:   uint16(req.GetClientType()),
		UserID:       req.GetUid(),
		MarketID:     req.GetMarketId(),
	})
	
	// 青少年模式检查
	parentGuardianSwitch, err := m.rpcCli.ParentGuardianCli.GetParentGuardianState(ctx, uint64(req.GetUid()))
	if nil != err {
		log.ErrorWithCtx(ctx, "ActivityPlaceOrder GetParentGuardianState err:%v", err)
		return out,  protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "未成年用户和未成年人模式不支持购买")
	}
	if parentGuardianSwitch {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "未成年用户和未成年人模式不支持购买")
	}
	
	// 风控检查
	checkReq := &riskMngApiPb.CheckReq{
		Scene: "H5_CONSUME",
		SourceEntity: &riskMngApiPb.Entity{
			Uid:          req.GetUid(),
			FaceAuthInfo: &riskMngApiPb.FaceAuthInfo{ResultToken: req.GetFaceAuthResultToken()},
		},
		CustomParams: map[string]string{
			"consume_type": "3",  // 3-人民币直购消费
			"scene_id":     "17", // 17-虚拟形象月卡
			"amount":       convertCent2Yuan(req.GetPayPriceCent()),
		},
	}
	checkRsp, err := m.rpcCli.RiskMngApiCli.Check(ctx, checkReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "ActivityPlaceOrder Check err: %v", err)
		return out, err
	}
	
	log.InfoWithCtx(ctx, "ActivityPlaceOrder Check req: %+v, rsp: %+v", checkReq, checkRsp)
	if checkRsp.ErrCode < 0 {
		out.FaceAuthContextJson = string(checkRsp.GetErrInfo())
		return out, protocol.NewExactServerError(nil, int(checkRsp.GetErrCode()), checkRsp.GetErrMsg())
	}
	
	// 业务下单逻辑
	orderRsp, serr := m.PlaceOrder(ctx, &pb.PlaceOrderReq{
		Uid:                    req.GetUid(),
		PackageId:              req.GetPackageId(),
		PayChannel:             req.GetPayChannel(),
		OriginalTransactionIds: req.GetOriginalTransactionIds(),
		PayPriceCent:           req.GetPayPriceCent(),
	}, uint8(kfk_virtual_image_card.GainChannel_GAIN_CHANNEL_ACTIVITY_ORDER))
	if serr != nil {
		log.ErrorWithCtx(ctx, "ActivityPlaceOrder PlaceOrder err: %v", serr)
		return out, serr
	}
	
	out.OrderNo = orderRsp.GetOrderNo()
	out.Token = orderRsp.GetToken()
	out.Tsk = orderRsp.GetTsk()
	out.CliOrderNo = orderRsp.GetCliOrderNo()
	out.OrderPrice = orderRsp.GetOrderPrice()
	out.ChannelMap = orderRsp.GetChannelMap()
	log.InfoWithCtx(ctx, "ActivityPlaceOrder Check req: %+v, rsp: %+v", req, out)
	return out, nil
}

func convertCent2Yuan(cent uint32) string {
	a := cent / 100
	b := cent % 100
	
	if b == 0 {
		return fmt.Sprintf("%d", a)
	}
	
	priceStr := fmt.Sprintf("%d.%02d", a, b)
	priceStr = strings.TrimSuffix(strings.TrimSuffix(priceStr, "0"), ".")
	return priceStr
}