package main

import (
    "context"
    "fmt"
    pb "golang.52tt.com/protocol/services/virtual-image-card"
    "google.golang.org/grpc"
    "google.golang.org/grpc/metadata"
)

func main() {
    ctx := metadata.AppendToOutgoingContext(context.Background(), "x-qw-traffic-mark", "tt-dev-mars")
    client := pb.MustNewClientTo(ctx, "10.34.4.7:80", grpc.<PERSON>(), grpc.WithAuthority("virtual-image-card.52tt.local"))
    
    resp, err := client.ActivityPlaceOrder(ctx, &pb.ActivityPlaceOrderReq{
        Uid: 2212647,
        PackageId: 1,
        PayChannel: 1,
        PayPriceCent: 1,
    })
    
    fmt.Println(err, resp)
    
}

