package internal

import (
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/core/log"
    "golang.52tt.com/pkg/protocol"
    protocolgrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/common/status"
    "golang.52tt.com/protocol/services/demo/echo"
    kfk_virtual_image_card "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_virtual_image_card"
    reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/config"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/event"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/local_cache"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/mgr"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/pay_api"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/rpc"
     "golang.org/x/net/context"
    "google.golang.org/grpc/codes"
    "sort"
    "time"
    
    pb "golang.52tt.com/protocol/services/virtual-image-card"
    
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/cache"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/store"
)

type Server struct {
    mgr *mgr.Mgr
}

func (s *Server) GetPackageListByIds(ctx context.Context, req *pb.GetPackageListByIdsReq) (*pb.GetPackageListByIdsResp, error) {
    out := &pb.GetPackageListByIdsResp{}
    defer func() {
        log.DebugWithCtx(ctx, "GetPackageListByIds in:%+v, out:%+v", req, out)
    }()
    
    packageList, err := s.mgr.GetPackageListByIds(ctx, req.GetPackageIds())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPackageListByIds fail, err:%v", err)
        return out, nil
    }
    
    out.PackageList = packageList
    log.DebugWithCtx(ctx, "GetPackageListByIds out:%+v", out)
    return out, err
}

func (s *Server) ActivityPlaceOrder(ctx context.Context, req *pb.ActivityPlaceOrderReq) (*pb.ActivityPlaceOrderResp, error) {
    return s.mgr.ActivityPlaceOrder(ctx, req)
}

func (s *Server) GetVirtualImageCardCommonCfg(c context.Context, request *pb.GetVirtualImageCardCommonCfgRequest) (*pb.GetVirtualImageCardCommonCfgResponse, error) {
    cfg := config.GetDynamicConfig()

    var aboutToExpireCfgList []*pb.AboutToExpireCfg
    for _, cfgItem := range cfg.AboutToExpireCfgList {
        aboutToExpireCfgList = append(aboutToExpireCfgList, &pb.AboutToExpireCfg{
            Icon:            cfgItem.Icon,
            ExpireAlertTime: cfgItem.ExpireAlertTime,
        })
    }
    out := &pb.GetVirtualImageCardCommonCfgResponse{
        WaitToBuyIcon:          cfg.WaitToBuyIcon,
        AlreadyBuyIcon:         cfg.AlreadyBuyIcon,
        AboutToExpireCfgList:   aboutToExpireCfgList,
        FirstEnterCardStoreUrl: cfg.FirstEnterCardStoreUrl,
        AdText:                 cfg.AdText,
        NDayShowOnce:           cfg.NDayShowOnce,
        CfgVersion:             cfg.CfgVersion,
        WaitToBuyBg:            cfg.WaitToBuyBg,
        AlreadyBuyBg:           cfg.AlreadyBuyBg,
        AboutToExpireBg:        cfg.AboutToExpireBg,
        StoreResidentEntryIcon: cfg.StoreResidentEntryIcon,
        StoreTabIconSelected:   cfg.StoreTabIconSelected,
        StoreTabIconUnselected: cfg.StoreTabIconUnselected,
        ExpireAlertTime:        cfg.ExpireAlertStatusKeepHour,
        FirstEnterCardStoreMd5: cfg.FirstEnterCardStoreMd5,
        PcWaitToBuyBg:          cfg.PcWaitToBuyBg,
        PcAlreadyBuyBg:         cfg.PcAlreadyBuyBg,
        PcAboutToExpireBg:      cfg.PcAboutToExpireBg,
    }
    return out, nil
}

func (s *Server) GetVirtualImageCardEntryStatus(c context.Context, request *pb.GetVirtualImageCardEntryStatusRequest) (*pb.GetVirtualImageCardEntryStatusResponse, error) {
    out := &pb.GetVirtualImageCardEntryStatusResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetVirtualImageCardEntryStatus in:%+v, out:%+v", request, out)
    }()
    cardInfo, err := s.mgr.GetUserCardInfo(c, request.GetUid())
    if err != nil {
        log.ErrorWithCtx(c, "GetVirtualImageCardEntryStatus fail to GetUserCardInfo. err:%v, request:%+v", err, request)
        return out, err
    }
    cfg := config.GetDynamicConfig()

    entrySwitch := config.GetDynamicConfig().Switch
    if !entrySwitch {
        if _, ok := cfg.WhiteListMap[request.GetUid()]; ok {
            log.DebugWithCtx(c, "GetVirtualImageCardEntryStatus user in white list. request:%+v", request)
            entrySwitch = true
        }
        
        if ok := s.mgr.CheckUserGroup(c, request.GetUid(), config.GetWhiteListGroup()); ok {
            log.DebugWithCtx(c, "GetVirtualImageCardEntryStatus user in white list group. request:%+v", request)
            entrySwitch = true
        }

        if !entrySwitch {
            // 判断卡是否生效中
            if cardInfo.GetExpireTs() > time.Now().Unix() {
                log.DebugWithCtx(c, "GetVirtualImageCardEntryStatus user already buy card. request:%+v", request)
                entrySwitch = true
            }
        }
    }

    serviceInfo, ok := protocolgrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "GetVirtualImageCardEntryStatus fail to get service info from context. request:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    packageListResp, err := s.mgr.GetUserPackageList(c, request.GetUid(), serviceInfo.MarketID, uint32(serviceInfo.ClientType))
    if err != nil {
        log.ErrorWithCtx(c, "GetVirtualImageCardEntryStatus fail to GetUserPackageList. err:%v, request:%+v", err, request)
        return out, err
    }
    var lowPriceStr string
    var lowestPrice uint32
    for i, pkg := range packageListResp.GetPackageList() {
        if i == 0 {
            lowestPrice = pkg.GetDailyPriceCent()
            continue
        }
        if pkg.GetDailyPriceCent() < lowestPrice {
            lowestPrice = pkg.GetDailyPriceCent()
        }
    }
    if lowestPrice != 0 {
        lowPriceStr = fmt.Sprintf(cfg.LowPriceText, pay_api.ConvertCent2Yuan(lowestPrice))
    }
    out.CfgVersion = cfg.CfgVersion
    out.ExpireTime = uint32(cardInfo.GetExpireTs())
    out.Switch = entrySwitch
    out.LowPriceText = lowPriceStr
    out.AdIdx = cfg.AdIndex
    out.TrialEffectTs = cardInfo.GetTrialEffectTs()
    out.TrialExpireTs = cardInfo.GetTrialExpireTs()
    out.BuyEffectTs = cardInfo.GetBuyEffectTs()
    out.BuyExpireTs = cardInfo.GetBuyExpireTs()

    return out, nil
}

func (s *Server) AddPackage(c context.Context, req *pb.AddPackageReq) (*pb.AddPackageResp, error) {
    out := &pb.AddPackageResp{}
    defer func() {
        log.InfoWithCtx(c, "AddPackage req:%+v, out:%+v", req, out)
    }()
    err := s.mgr.AddPackage(c, req.GetPackageInfo())
    if nil != err {
        log.ErrorWithCtx(c, "AddPackage err: %v", err)
        return nil, err
    }
    return out, nil
}

func (s *Server) BatchAddGroupPackage(c context.Context, req *pb.BatchAddGroupPackageReq) (*pb.BatchAddGroupPackageResp, error) {
    out := &pb.BatchAddGroupPackageResp{}
    defer func() {
        log.InfoWithCtx(c, "BatchAddGroupPackage in:%+v, out:%+v", req, out)
    }()
    if len(req.GetGroupList()) == 0 {
        log.ErrorWithCtx(c, "BatchAddGroupPackage GroupList is empty, req:%+v", req)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "GroupList为空")
    }
    err := s.mgr.BatchAddGroupPackage(c, req.GetGroupList(), req.GetIsCheck())
    if nil != err {
        log.ErrorWithCtx(c, "BatchAddGroupPackage err: %v", err)
        return nil, err
    }
    return out, nil
}

func (s *Server) ModifyPackage(c context.Context, req *pb.ModifyPackageReq) (*pb.ModifyPackageResp, error) {
    out := &pb.ModifyPackageResp{}
    defer func() {
        log.InfoWithCtx(c, "ModifyPackage in:%+v, out:%+v", req, out)
    }()

    err := s.mgr.UpdatePackage(c, req.GetPackageInfo())
    if nil != err {
        log.ErrorWithCtx(c, "ModifyPackage err: %v", err)
        return nil, err
    }
    return out, nil
}

func (s *Server) UpdatePackageStatus(c context.Context, req *pb.UpdatePackageStatusReq) (*pb.UpdatePackageStatusResp, error) {
    out := &pb.UpdatePackageStatusResp{}
    defer func() {
        log.InfoWithCtx(c, "UpdatePackageStatus in:%+v, out:%+v", req, out)
    }()
    var err error
    if !req.GetIsGroup() {
        err = s.mgr.UpdatePackageStatus(c, req.GetId(), req.GetIsEnabled(), req.GetOperator())
    } else {
        err = s.mgr.SetGroupPackageStatus(c, req.GetId(), req.GetIsEnabled(), req.GetOperator())
    }

    if nil != err {
        log.ErrorWithCtx(c, "UpdatePackageStatus err: %v", err)
        return nil, err
    }
    return out, nil
}

func (s *Server) GetPackageListByStatus(c context.Context, req *pb.GetPackageListByStatusReq) (*pb.GetPackageListByStatusResp, error) {
    out := &pb.GetPackageListByStatusResp{}
    defer func() {
        log.DebugWithCtx(c, "GetPackageListByStatus in:%+v, out:%+v", req, out)
    }()
    packList, err := s.mgr.GetPackageListByStatus(c, req.GetIsEnabled(), uint32(req.GetPackageType()))
    if err != nil {
        log.ErrorWithCtx(c, "GetPackageListByStatus failed, err:%v", err)
        return nil, err
    }
    groupMap := make(map[uint32][]*pb.Package)

    for _, pack := range packList {
        if _, ok := groupMap[pack.GroupId]; !ok {
            groupMap[pack.GroupId] = make([]*pb.Package, 0)
        }
        groupMap[pack.GroupId] = append(groupMap[pack.GroupId], pack)
    }

    for groupId, packageList := range groupMap {
        if len(packageList) == 0 {
            continue
        }
        packageItem := packageList[0]
        groupPkg := &pb.GroupPackage{
            GroupId:           groupId,
            PackageType:       packageItem.PackageType,
            Name:              packageItem.Name,
            Desc:              packageItem.Desc,
            OriginalPriceCent: packageItem.OriginalPriceCent,
            PriceCent:         packageItem.PriceCent,
            Days:              int32(packageItem.Days),
            IsEnabled:         packageItem.IsEnabled,
            Operator:          packageItem.Operator,
        }
        if groupId > 0 {
            for _, pkg := range packageList {
                groupPkg.MarketList = append(groupPkg.MarketList, &pb.MarketInfo{
                    MarketId:  pkg.MarketId,
                    PackageId: pkg.Id,
                    ProductId: pkg.ProductId,
                    UpdateTs:  pkg.UpdateTs,
                })
            }
            sort.Slice(groupPkg.MarketList, func(i, j int) bool {
                // 按更新时间排序
                return groupPkg.MarketList[i].UpdateTs > groupPkg.MarketList[j].UpdateTs
            })

            out.PackageList = append(out.PackageList, &pb.MixPackage{
                IsGroup:      true,
                GroupPackage: groupPkg,
            })
        } else {
            for _, pack := range packageList {
                out.PackageList = append(out.PackageList, &pb.MixPackage{
                    IsGroup:     false,
                    PackageInfo: pack,
                })
            }
        }
    }
    sort.Slice(out.PackageList, func(i, j int) bool {
        itemI := out.PackageList[i]
        itemJ := out.PackageList[j]
        if itemI.IsGroup {
            if itemJ.IsGroup {
                return itemI.GetGroupPackage().GetMarketList()[0].UpdateTs > itemJ.GetGroupPackage().GetMarketList()[0].UpdateTs
            } else {
                return itemI.GetGroupPackage().GetMarketList()[0].UpdateTs > itemJ.GetPackageInfo().GetUpdateTs()
            }
        } else {
            if itemJ.IsGroup {
                return itemI.GetPackageInfo().GetUpdateTs() > itemJ.GetGroupPackage().GetMarketList()[0].UpdateTs
            } else {
                return itemI.GetPackageInfo().GetUpdateTs() > itemJ.GetPackageInfo().GetUpdateTs()
            }
        }
    })
    return out, nil
}

func (s *Server) EditGroupPackage(c context.Context, req *pb.EditGroupPackageReq) (*pb.EditGroupPackageResp, error) {
    out := &pb.EditGroupPackageResp{}
    defer func() {
        log.InfoWithCtx(c, "EditGroupPackage in:%+v, out:%+v", req, out)
    }()

    if req.GetGroupId() == 0 {
        err := s.mgr.AddGroup(c, req.GetMarketList(), req.GetOperator())
        if err != nil {
            log.ErrorWithCtx(c, "AddGroup failed, err:%v", err)
            return nil, err
        }
    } else {
        err := s.mgr.EditGroup(c, req.GetGroupId(), req.GetMarketList(), req.GetOperator())
        if err != nil {
            log.ErrorWithCtx(c, "EditGroup failed, err:%v", err)
            return out, err
        }
    }
    return out, nil
}

func (s *Server) ModifyGroupPackage(c context.Context, req *pb.ModifyGroupPackageReq) (*pb.ModifyGroupPackageResp, error) {
    out := &pb.ModifyGroupPackageResp{}
    defer func() {
        log.InfoWithCtx(c, "ModifyGroupPackage in:%+v, out:%+v", req, out)
    }()

    err := s.mgr.ModifyGroupPackage(c, req.GetGroupPackage())
    if nil != err {
        log.ErrorWithCtx(c, "ModifyGroupPackage err: %v", err)
        return out, err
    }
    return out, nil
}

func (s *Server) AddSalePackage(c context.Context, req *pb.AddSalePackageReq) (*pb.AddSalePackageResp, error) {
    out := &pb.AddSalePackageResp{}
    defer func() {
        log.InfoWithCtx(c, "AddSalePackage in:%+v, out:%+v", req, out)
    }()

    if req.GetInfo().GetBeginTs() >= req.GetInfo().GetEndTs() {
        log.ErrorWithCtx(c, "AddSalePackage, beginTs >= endTs, req:%+v", req)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "结束时间需大于开始时间")
    }

    if req.GetInfo().GetPackageInfo().GetId() == 0 && req.GetInfo().GetGroupId() == 0 {
        log.ErrorWithCtx(c, "AddSalePackage, packageId and groupId can not be both 0, req:%+v", req)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "套餐id和分组id不能同时为0")
    }

    if req.GetInfo().GetGroupId() != 0 {
        err := s.mgr.AddSalePackageByGroup(c, req.GetInfo(), req.GetIsCheck())
        if nil != err {
            log.ErrorWithCtx(c, "AddSalePackageByGroup err: %v", err)
            return out, err
        }
        return out, nil
    }

    out, err := s.mgr.AddSalePackage(c, req.GetInfo(), req.GetIsCheck())
    if nil != err {
        log.ErrorWithCtx(c, "AddSalePackage err: %v", err)
        return out, err
    }
    return out, nil
}

func (s *Server) UpdateSalePackage(c context.Context, req *pb.UpdateSalePackageReq) (*pb.UpdateSalePackageResp, error) {
    out := &pb.UpdateSalePackageResp{}
    defer func() {
        log.InfoWithCtx(c, "UpdateSalePackage in:%+v, out:%+v", req, out)
    }()
    err := s.mgr.UpdateSalePackage(c, req.GetInfo())
    if nil != err {
        log.ErrorWithCtx(c, "UpdateSalePackage err: %v", err)
        return out, err
    }
    return out, nil
}

func (s *Server) GetSalePackageListByStatus(c context.Context, req *pb.GetSalePackageListByStatusReq) (*pb.GetSalePackageListByStatusResp, error) {
    out := &pb.GetSalePackageListByStatusResp{}
    defer func() {
        log.DebugWithCtx(c, "GetSalePackageListByStatus in:%+v, out:%+v", req, out)
    }()

    list, err := s.mgr.GetSalePackageListByStatus(c, uint32(req.GetStatus()))
    if nil != err {
        log.ErrorWithCtx(c, "GetSalePackageListByStatus err: %v", err)
        return out, err
    }
    for _, sale := range list {
        out.PackageList = append(out.PackageList, sale)
    }
    return out, nil
}

func (s *Server) SalePackageSort(c context.Context, req *pb.SalePackageSortReq) (*pb.SalePackageSortResp, error) {
    out := &pb.SalePackageSortResp{}
    defer func() {
        log.InfoWithCtx(c, "SalePackageSort in:%+v, out:%+v", req, out)
    }()
    if len(req.GetSaleIdList()) == 0 {
        log.ErrorWithCtx(c, "SalePackageSort SaleIdList is empty, req:%+v", req)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "SaleIdList为空")
    }
    if len(req.GetSaleIdList()) > 100 {
        log.ErrorWithCtx(c, "SalePackageSort saleIdList is too long")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "列表长度超过100")
    }
    err := s.mgr.SalePackageSort(c, req.GetSaleIdList())
    if nil != err {
        log.ErrorWithCtx(c, "SalePackageSort err: %v", err)
        return out, err
    }
    return out, nil
}

func NewServer(ctx context.Context, cfg *config.StartConfig) (*Server, error) {
    log.Infof("server startup with cfg: %+v", *cfg)

    cache_, err := cache.NewCache(ctx, cfg.RedisConfig)
    if nil != err {
        log.ErrorWithCtx(ctx, "init redis fail, err: %v", err)
        return nil, err
    }

    store_, err := store.NewStore(ctx, cfg.MysqlConfig, cfg.ReadonlyMysqlConfig)
    if nil != err {
        log.ErrorWithCtx(ctx, "init store fail, err: %v", err)
        return nil, err
    }

    rpcCli_, err := rpc.NewClient()
    if nil != err {
        log.ErrorWithCtx(ctx, "init rpc fail, err: %v", err)
        return nil, err
    }

    localCache_, err := local_cache.NewLocalCache(store_)
    if nil != err {
        log.ErrorWithCtx(ctx, "init local cache fail, err: %v", err)
        return nil, err
    }

    producer_, err := event.NewCardProducer(ctx, cfg.ProducerConfig)
    if nil != err {
        log.ErrorWithCtx(ctx, "init producer fail, err: %v", err)
        return nil, err
    }

    mgr_, err := mgr.NewMgr(ctx, store_, cache_, rpcCli_, localCache_, producer_)
    if nil != err {
        log.ErrorWithCtx(ctx, "init mgr fail, err: %v", err)
        return nil, err
    }

    return &Server{mgr: mgr_}, nil
}

func (s *Server) ShutDown() {
    s.mgr.Close()
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
    return req, nil
}

func (s *Server) PlaceOrder(ctx context.Context, in *pb.PlaceOrderReq) (*pb.PlaceOrderResp, error) {
    return s.mgr.PlaceOrder(ctx, in, uint8(kfk_virtual_image_card.GainChannel_GAIN_CHANNEL_PLACE_ORDER))
}

func (s *Server) CancelOrder(ctx context.Context, in *pb.CancelOrderReq) (*pb.CancelOrderResp, error) {
    log.InfoWithCtx(ctx, "CancelOrder in: %+v", in)
    // 暂时不干啥事，后续可优化更新db状态
    return &pb.CancelOrderResp{}, nil
}

func (s *Server) PayCallback(ctx context.Context, in *pb.PayCallbackReq) (*pb.PayCallbackResp, error) {
    err := s.mgr.PayCallback(ctx, in)
    if err != nil {
        lines := []string{
            fmt.Sprintf("order_id: %s", in.CliOrderNo),
            fmt.Sprintf("uid: %d", in.ActiveUid),
            fmt.Sprintf("err: %v", err),
        }
        go s.mgr.SendFeiShuMsg("支付回调处理失败", lines, "")
    }
    return &pb.PayCallbackResp{}, err
}

func (s *Server) NotifyContract(ctx context.Context, in *pb.NotifyContractReq) (*pb.NotifyContractResp, error) {
    err := s.mgr.NotifyContract(ctx, in)
    if err != nil {
        lines := []string{
            fmt.Sprintf("contract_id: %s", in.ContractId),
            fmt.Sprintf("uid: %d", in.ActiveUid),
            fmt.Sprintf("err: %v", err),
        }
        go s.mgr.SendFeiShuMsg("签约回调处理失败", lines, "")
    }
    return &pb.NotifyContractResp{}, err
}

func (s *Server) PlaceAutoPayOrder(ctx context.Context, in *pb.PlaceAutoPayOrderReq) (*pb.PlaceAutoPayOrderResp, error) {
    err := s.mgr.PlaceAutoPayOrder(ctx, in)
    if err != nil {
        lines := []string{
            fmt.Sprintf("contract_id: %s", in.ContractId),
            fmt.Sprintf("err: %v", err),
        }
        go s.mgr.SendFeiShuMsg("苹果自动扣费回调处理失败", lines, "")
    }
    return &pb.PlaceAutoPayOrderResp{}, err
}

func (s *Server) UseVirtualImageTrialCard(ctx context.Context, in *pb.UseVirtualImageTrialCardReq) (*pb.UseVirtualImageTrialCardResp, error) {
    return &pb.UseVirtualImageTrialCardResp{}, s.mgr.UseVirtualImageTrialCard(ctx, in)
}

func (s *Server) GetUserCardInfo(ctx context.Context, in *pb.GetUserCardInfoReq) (out *pb.GetUserCardInfoResp, err error) {
    out = &pb.GetUserCardInfoResp{}
    out.Card, err = s.mgr.GetUserCardInfo(ctx, in.Uid)
    out.ExpireAlertStatusKeepHour = config.GetDynamicConfig().ExpireAlertStatusKeepHour
    return out, err
}

func (s *Server) GetUserRedemptionInfo(ctx context.Context, in *pb.GetUserRedemptionInfoReq) (*pb.GetUserRedemptionInfoResp, error) {
    return s.mgr.GetUserRedemptionInfo(ctx, in)
}

func (s *Server) GetPurchaseHistory(ctx context.Context, in *pb.GetPurchaseHistoryReq) (*pb.GetPurchaseHistoryResp, error) {
    return s.mgr.GetPurchaseHistory(ctx, in)
}

func (s *Server) GetUserPackageList(ctx context.Context, in *pb.GetUserPackageListReq) (*pb.GetUserPackageListResp, error) {
    return s.mgr.GetUserPackageList(ctx, in.GetUid(), in.GetMarketId(), in.GetClientType())
}

func (s *Server) RevokeOrder(ctx context.Context, in *pb.RevokeOrderReq) (*pb.RevokeOrderResp, error) {
    err := s.mgr.RevokeOrder(ctx, in)
    if err != nil {
        lines := []string{
            fmt.Sprintf("order_id: %s", in.OrderId),
            fmt.Sprintf("uid: %d", in.Uid),
            fmt.Sprintf("err: %v", err),
        }
        go s.mgr.SendFeiShuMsg("退款回调处理失败", lines, "")
    }
    return &pb.RevokeOrderResp{}, err
}

func (s *Server) GetContractById(ctx context.Context, in *pb.GetContractByIdReq) (*pb.GetContractByIdResp, error) {
    return s.mgr.GetContractById(ctx, in.ContractId)
}

func (s *Server) GenerateStat(ctx context.Context, in *pb.GenerateStatReq) (*pb.GenerateStatResp, error) {
    startTime, err := time.ParseInLocation("2006-01-02 15:04:05", in.StartTime, time.Local)
    if err != nil {
        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "开始时间格式错误")
    }
    endTime, err := time.ParseInLocation("2006-01-02 15:04:05", in.EndTime, time.Local)
    if err != nil {
        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "结束时间格式错误")
    }
    go s.mgr.GenerateStat(ctx, startTime, endTime) // 异步执行
    return &pb.GenerateStatResp{}, nil
}

func (s *Server) GetTrialCardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	out := &reconcile_v2.CountResp{}
	beginTime := time.Unix(in.BeginTime, 0)
	endTime := time.Unix(in.EndTime, 0)

	count, err := s.mgr.GetTrialCardTotalCount(ctx, beginTime, endTime)
	if err != nil {
        log.ErrorWithCtx(ctx, "GetTrialCardTotalCount failed: %v, beginTime: %v, endTime: %v", err, beginTime, endTime)
		return out, err
	}
	out.Count = count
    out.Value = count // 这里的value，指扣的数量，所以跟count一致

	return out, nil
}

func (s *Server) GetTrialCardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	out := &reconcile_v2.OrderIdsResp{}
	beginTime := time.Unix(in.BeginTime, 0)
	endTime := time.Unix(in.EndTime, 0)

	orderIds, err := s.mgr.GetTrialCardOrderIds(ctx, beginTime, endTime)
	if err != nil {
        log.ErrorWithCtx(ctx, "GetTrialCardOrderIds failed: %v, beginTime: %v, endTime: %v", err, beginTime, endTime)
		return out, err
	}
	out.OrderIds = orderIds

	return out, nil
}

func (s *Server) ReplaceTrialCardOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
    return &reconcile_v2.EmptyResp{}, s.mgr.ReplaceTrialCardOrder(ctx, in.OrderId)
}
