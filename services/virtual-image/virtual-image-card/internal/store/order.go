package store

import (
	"context"
	"encoding/json"
	"fmt"
	mysql2 "github.com/go-sql-driver/mysql"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/pkg/log"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

const (
	OrderStatusInit   = 0 // 初始化
	OrderStatusPaid   = 1 // 已支付
	OrderStatusRevoke = 2 // 已退款
	OrderRoleUser     = 0 // 用户下单
	OrderRoleSystem   = 1 // 系统续费
)

func GenOrderId(uid uint32, createTime time.Time) string {
	return fmt.Sprintf("card_%d_%d", uid, createTime.Unix())
}

// GenSystemOrderId 系统下单时用的订单id，加入当前ts才能重试下单
func GenSystemOrderId(uid uint32, createTime time.Time) string {
	return fmt.Sprintf("card_%d_%d_%d", uid, time.Now().Unix(), createTime.Unix())
}

type Order struct {
	Uid            uint32    `db:"uid"`
	OrderId        string    `db:"order_id"`
	OrderRole      uint8     `db:"order_role"`
	ContractId     string    `db:"contract_id"`
	PayChannel     uint8     `db:"pay_channel"`
	PriceCent      uint32    `db:"price_cent"`
	PackageId      uint32    `db:"package_id"`
	CoinOrderNo    string    `db:"coin_order_no"`
	ChannelOrderNo string    `db:"channel_order_no"`
	PayTime        time.Time `db:"pay_time"`
	PayPriceCent   uint32    `db:"pay_price_cent"`
	CoinPayChannel string    `db:"coin_pay_channel"`
	ActiveUid      uint32    `db:"active_uid"`
	Status         uint8     `db:"status"`
	CreateTime     time.Time `db:"create_time"`
	UpdateTime     time.Time `db:"update_time"`
	PackageInfo    string    `db:"package_info"`
	GainChannel    uint8     `db:"gain_channel"`
	Package        Package   `db:"-"`
}

func (o *Order) EncodePackageInfo() {
	if o.Package.Id == 0 {
		return
	}
	packageInfo, _ := json.Marshal(o.Package)
	o.PackageInfo = string(packageInfo)
}

func (o *Order) DecodePackageInfo() error {
	if o == nil || o.PackageInfo == "" {
		return nil
	}
	err := json.Unmarshal([]byte(o.PackageInfo), &o.Package)
	if err != nil {
		log.Errorf("Order DecodePackageInfo err: %v", err)
		return err
	}
	return nil
}

 //alter table tbl_virtual_image_card_order_202509 add column gain_channel tinyint(1) not NULL DEFAULT 0 comment '获取渠道 1APP下单获得 2-体验卡获得 3-活动下单获得';

const createOrderTblSQL = `CREATE TABLE IF NOT EXISTS %s (
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    uid int(10) unsigned NOT NULL DEFAULT 0 COMMENT 'uid',
    order_id varchar(64) NOT NULL default '' COMMENT '订单id',
    
    order_role tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '订单类型 0-用户下单 1-系统续费',
    contract_id varchar(64) NOT NULL default '' COMMENT '关联的签约id',
    pay_channel tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '支付渠道',
    price_cent int(10) unsigned NOT NULL DEFAULT 0 COMMENT '订单价格(分)',
    package_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '套餐id',
    
    coin_order_no varchar(64) NOT NULL default '' COMMENT '货币订单号',
    channel_order_no varchar(64) NOT NULL default '' COMMENT '渠道订单号',
    pay_time timestamp NOT NULL default 0 COMMENT '支付时间',
    pay_price_cent int(10) unsigned NOT NULL DEFAULT 0 COMMENT '支付价格(分)',
    coin_pay_channel varchar(64) NOT NULL default '' COMMENT '货币返回的支付渠道',
    active_uid int(10) unsigned NOT NULL DEFAULT 0 COMMENT '最终生效的uid',

    status tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '订单状态 0-初始化 1-已支付 2-已退款',
    create_time timestamp NOT NULL default CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time timestamp NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    package_info varchar(1024) NOT NULL default '' COMMENT '套餐信息json',
    gain_channel tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '获取渠道 1APP下单获得 2-体验卡获得 3-活动下单获得',
    
    primary key (id),
    unique key uniq_order_id (order_id),
    key idx_active_uid (active_uid)
) engine=InnoDB default charset=utf8 COMMENT "无限换装卡订单表";`

const queryOrderFields = "uid, order_id, order_role, contract_id, pay_channel, price_cent, package_id, coin_order_no, channel_order_no, pay_time, pay_price_cent, coin_pay_channel, active_uid, status, create_time, update_time, package_info, gain_channel"

func getOrderTblNameById(orderId string) string {
	createTime := parseCreateTimeFromOrderId(orderId)
	return getOrderTblName(createTime)
}

func getOrderTblName(createTime time.Time) string {
	return fmt.Sprintf("tbl_virtual_image_card_order_%s", createTime.Format("200601"))
}

func parseCreateTimeFromOrderId(orderId string) time.Time {
	strList := strings.Split(orderId, "_")
	if len(strList) == 0 {
		log.Errorf("parseCreateTimeFromOrderId invalid order id: %s", orderId)
		return time.Unix(0, 0)
	}
	sec, err := strconv.ParseInt(strList[len(strList)-1], 10, 64)
	if err != nil {
		log.Errorf("parseCreateTimeFromOrderId invalid order id: %s, err: %v", orderId, err)
		return time.Unix(0, 0)
	}
	return time.Unix(sec, 0)
}

func (s *Store) AddOrder(ctx context.Context, order *Order, tx mysql.Txx) (bool, error) {
	if tx == nil {
		return false, nil
	}

	order.EncodePackageInfo()
	query := fmt.Sprintf("INSERT INTO %s (uid, order_id, order_role, contract_id, pay_channel, price_cent, package_id, create_time, package_info, gain_channel) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", getOrderTblName(order.CreateTime))
	params := []interface{}{order.Uid, order.OrderId, order.OrderRole, order.ContractId, order.PayChannel, order.PriceCent, order.PackageId, order.CreateTime, order.PackageInfo, order.GainChannel}

	_, err := tx.ExecContext(ctx, query, params...)
	if err != nil {
		return false, err
	}
	return true, nil
}

func (s *Store) UpdateOrder(ctx context.Context, order *Order, oldStatus uint8, tx mysql.Txx) (bool, error) {
	if tx == nil {
		return false, nil
	}

	query := fmt.Sprintf("UPDATE %s SET coin_order_no = ?, channel_order_no = ?, pay_time = ?, pay_price_cent = ?, coin_pay_channel = ?, active_uid = ?, status = ? WHERE order_id = ? AND status = ?", getOrderTblNameById(order.OrderId))
	params := []interface{}{order.CoinOrderNo, order.ChannelOrderNo, order.PayTime, order.PayPriceCent, order.CoinPayChannel, order.ActiveUid, order.Status, order.OrderId, oldStatus}

	result, err := tx.ExecContext(ctx, query, params...)
	if err != nil {
		return false, err
	}
	rowsAffected, _ := result.RowsAffected()
	return rowsAffected > 0, nil
}

func (s *Store) GetOrder(ctx context.Context, orderId string, tx mysql.Txx) (*Order, error) {
	query := fmt.Sprintf("SELECT %s FROM %s WHERE order_id = ?", queryOrderFields, getOrderTblNameById(orderId))

	order := &Order{}
	var err error
	if tx != nil {
		err = tx.GetContext(ctx, order, query, orderId)
	} else {
		err = s.db.GetContext(ctx, order, query, orderId)
	}

	if err != nil && mysql.IsNoRowsError(err) {
		return nil, nil
	}
	_ = order.DecodePackageInfo()
	return order, err
}

func (s *Store) GetUserYearOrders(ctx context.Context, uid uint32) ([]*Order, error) {
	now := time.Now()
	monthNow := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	queryMonth := 13
	orderListList := make([][]*Order, queryMonth)

	wg := sync.WaitGroup{}
	for i := 0; i < queryMonth; i++ {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()

			tableName := getOrderTblName(monthNow.AddDate(0, -i, 0))
			query := fmt.Sprintf("SELECT %s FROM %s WHERE active_uid = ? AND status in (1,2)", queryOrderFields, tableName)
			orderList := make([]*Order, 0)
			terr := s.readonlyDb.SelectContext(ctx, &orderList, query, uid)
			if terr != nil { // 表不存在，报错也无所谓
				log.WarnWithCtx(ctx, "GetUserYearOrders query tableName: %s, err: %v", tableName, terr)
			}
			for _, order := range orderList {
				_ = order.DecodePackageInfo()
			}
			orderListList[i] = orderList
		}(i)
	}
	wg.Wait()

	// 过滤1年内的
	lastYear := now.AddDate(-1, 0, 0)
	totalOrderList := make([]*Order, 0)
	for _, orderList := range orderListList {
		for _, order := range orderList {
			if order.PayTime.After(lastYear) {
				totalOrderList = append(totalOrderList, order)
			}
		}
	}

	// 按PayTime倒序
	sort.SliceStable(totalOrderList, func(i, j int) bool {
		return totalOrderList[i].PayTime.After(totalOrderList[j].PayTime)
	})
	return totalOrderList, nil
}

func (s *Store) GetCurrentPeriodBuyOrders(ctx context.Context, startTime, endTime time.Time) ([]*Order, error) {
	// 本周期的表
	query := fmt.Sprintf("SELECT %s FROM %s WHERE pay_time >= ? AND pay_time < ? AND status > 0", queryOrderFields, getOrderTblName(startTime))
	params := []interface{}{startTime, endTime}
	orderList1 := make([]*Order, 0)
	err := s.readonlyDb.SelectContext(ctx, &orderList1, query, params...)
	if err != nil {
		if driverErr, ok := err.(*mysql2.MySQLError); ok {
			if driverErr.Number == 1146 { // 忽略表不存在错误
				err = nil
			}
		}
		if err != nil {
			return nil, err
		}
	}

	// 可能也在上周期的表
	prePeriodTime := time.Date(startTime.Year(), startTime.Month()-1, 1, 0, 0, 0, 0, time.Local)
	query = fmt.Sprintf("SELECT %s FROM %s WHERE pay_time >= ? AND pay_time < ? AND status > 0", queryOrderFields, getOrderTblName(prePeriodTime))
	params = []interface{}{startTime, endTime}
	orderList2 := make([]*Order, 0)
	err = s.readonlyDb.SelectContext(ctx, &orderList2, query, params...)
	if err != nil {
		if driverErr, ok := err.(*mysql2.MySQLError); ok {
			if driverErr.Number == 1146 { // 忽略表不存在错误
				err = nil
			}
		}
		if err != nil {
			return nil, err
		}
	}

	// 合并
	orderList := make([]*Order, 0, len(orderList1)+len(orderList2))
	for _, order := range orderList1 {
		_ = order.DecodePackageInfo()
		orderList = append(orderList, order)
	}
	for _, order := range orderList2 {
		_ = order.DecodePackageInfo()
		orderList = append(orderList, order)
	}
	return orderList, nil
}
