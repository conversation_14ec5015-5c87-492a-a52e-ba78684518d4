package main

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"github.com/go-gomail/gomail"
	"github.com/jmoiron/sqlx"
	"runtime"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"io/ioutil"
	"os"
	"sort"
	"strconv"
	"time"

	xlsx "github.com/xuri/excelize/v2"
)

const daySec = 86400

var sc *ServiceConfigT

type Store struct {
	db, readonlyDb *sqlx.DB
}

type ServiceConfigT struct {
	MysqlConfig         *config.MysqlConfig `json:"mysql"`
	MailsTo             []string            `json:"mails_to"`
	MysqlReadOnlyConfig *config.MysqlConfig `json:"readonly_mysql"`
}

type Summary struct {
	CommodityId      uint32 //商品id
	EffectiveDays    uint32
	Price            float64
	TotalPrice       uint32
	BuyTotalPrice    float64
	Count            uint32
	BeginRemainPrice float64 //期初剩余金额
	EndRemainPrice   float64 //本期剩余金额
	UsePrice         float64 //本期核销价值
}

type Detail struct {
	Id               uint32
	OrderId          string
	Uid              uint32
	BuyTime          time.Time
	BeginTime        time.Time // 发放前过期时间
	EndTime          time.Time // 发放后过期时间
	CTime            time.Time
	CommodityId      uint32
	EffectiveDays    uint32
	Count            uint32
	Price            float64 // 单价
	TotalPrice       uint32  // 订单总价格
	BeginDays        float64
	BuyDays          float64
	UseDays          float64
	EndDays          float64
	BuyTotalPrice    float64
	BeginRemainPrice float64 //期初剩余金额
	EndRemainPrice   float64 //本期剩余金额
	UsePrice         float64 //本期核销价值
}

func (sc *ServiceConfigT) Parse(configFile string) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, &sc)
	if err != nil {
		return err
	}

	log.Infof("ServiceConfigT:MysqlConfig:%+v\n", sc.MysqlConfig)
	return
}

func NewMysql(c, rc *config.MysqlConfig) (*Store, error) {
	mysqlDb, err := sqlx.Open("mysql", c.ConnectionString())
	if err != nil {
		log.Errorf("Failed to create mysql %v", err)
		return nil, err
	}

	mysqlReadonlyDb, err := sqlx.Open("mysql", rc.ConnectionString())
	if err != nil {
		log.Errorf("Failed to create mysql %v", err)
		return nil, err
	}

	if c.MaxIdleConns > 0 {
		mysqlDb.SetMaxIdleConns(c.MaxIdleConns)
		mysqlReadonlyDb.SetMaxIdleConns(c.MaxIdleConns)
	}
	if c.MaxOpenConns > 0 {
		mysqlDb.SetMaxOpenConns(c.MaxOpenConns)
		mysqlReadonlyDb.SetMaxOpenConns(c.MaxOpenConns)
	}
	mysqlDb.SetConnMaxLifetime(time.Minute * 5)
	mysqlReadonlyDb.SetConnMaxLifetime(time.Minute * 5)

	st := &Store{
		db:         mysqlDb,
		readonlyDb: mysqlReadonlyDb,
	}

	log.Infof("NewMysql success c%v,rc%v", c, rc)
	return st, nil
}

func main() {
	sc := &ServiceConfigT{}
	err := sc.Parse("/home/<USER>/virtual-image-mall/virtual-image-mall.json")
	if err != nil {
		log.Errorf("Parse fail. err:%v", err)
		return
	}

	now := time.Now()
	if len(os.Args) >= 2 {
		ts, err := strconv.ParseUint(os.Args[1], 10, 64)
		if err == nil {
			now = time.Unix(int64(ts), 0)
		} else {
			log.Errorf("ParseUint fail. %s, err:%v", os.Args[1], err)
		}
	}

	thisMonthBegin := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	genTimeBegin := thisMonthBegin.AddDate(0, -1, 0)
	genTimeEnd := time.Unix(int64(1754560800), 0)
	ctx, cancel := context.WithTimeout(context.Background(), time.Hour)
	defer cancel()
	log.Debugf("生成时间：%s -- %s", genTimeBegin.Format("2006-01-02 15:04:05"), genTimeEnd.Format("2006-01-02 15:04:05"))

	st, err = NewMysql(sc.MysqlConfig, sc.MysqlReadOnlyConfig)
	if err != nil {
		log.Errorf("NewMysql fail. err:%v", err)
		return
	}

	//st.createFinancialStatementsTable()
	//st.DeleteFinancialStatements(ctx)

	var offset uint32
	limit := uint32(1500)
	for {
		dataList, err := st.BatchGetCommodityDataOrdersByTime(ctx, genTimeBegin, offset, limit)
		if err != nil {
			log.Errorf("BatchGetCommodityDataOrdersByTime fail. err:%v", err)
			break
		}

		for _, data := range dataList {
			lastRecord, err := st.GetLastValidFinancialStatementsRecord(ctx, data.Uid, data.CommodityId, data.CreateTime)
			record := &FinancialStatements{
				Uid:          data.Uid,
				CommodityId:  data.CommodityId,
				OrderNo:      data.OrderNo,
				TotalPrice:   data.TotalPrice,
				AvgPrice:     data.AvgPrice,
				EffectiveDay: data.EffectiveDay,
				Count:        data.Count,
				CreateTime:   data.CreateTime,
			}

			if err != nil {
				// 没有记录 时间为当前订单时间
				if err.Error() == "sql: no rows in result set" {
					record.StartTime = data.CreateTime
					record.EndTime = data.CreateTime.Add(time.Duration(data.EffectiveDay*data.Count) * time.Hour * 24)
				} else {
					log.Errorf("GetLastValidFinancialStatementsRecord fail. err:%v", err)
					continue
				}
			} else {
				// 如果有记录 则在上一条订单上叠加时间
				record.StartTime = lastRecord.EndTime
				record.EndTime = lastRecord.EndTime.Add(time.Duration(data.EffectiveDay*data.Count) * time.Hour * 24)
			}

			if record.Uid == 0 {
				continue
			}

			err = st.InsertFinancialStatements(ctx, record)
			if err != nil {
				log.Errorf("InsertFinancialStatements fail. err:%v", err)
			}
		}
		offset += limit
		if len(dataList) < int(limit) {
			break
		}
	}

	subString := fmt.Sprintf("virtual_image_%s_%s", genTimeBegin.Format("2006-01-02"), genTimeEnd.Format("2006-01-02"))

	filePath := fmt.Sprintf("/home/<USER>/tools/%s.xlsx", subString)

	SendReconcileReport(genTimeBegin, genTimeEnd, filePath)

	SendMail(filePath, filePath, sc.MailsTo)
}

// SendReconcileReport 发送对账报表
func SendReconcileReport(begin, end time.Time, filePath string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Hour)
	defer cancel()

	// 查本周期的有效订单表
	orders := make([]*FinancialStatements, 0)
	var err error
	for i := 0; i < 1000; i++ {
		onceOrderList, err := st.GetFinancialStatementsRecordMonthly(ctx, begin, end, uint32(i*1000), 1000)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetEffectOrders fail. err:%v", err)
			break
		}
		if len(onceOrderList) > 0 {
			orders = append(orders, onceOrderList...)
		}

		if len(onceOrderList) < 1000 {
			break
		}
	}

	details := genDetailList(orders, begin, end)

	file := xlsx.NewFile()
	// 生成汇总报表
	err = genSummarySheet(file, details, begin, end, filePath)
	if err != nil {
		log.ErrorWithCtx(ctx, "genSummarySheet fail. err:%v", err)
		return err
	}

	// 生成明细报表
	err = genDetailSheet(file, details, begin, end, filePath)
	if err != nil {
		log.ErrorWithCtx(ctx, "genDetailSheet fail. err:%v", err)
		return err
	}

	log.InfoWithCtx(ctx, "SendReconcileReport success. begin:%v, end:%v", begin, end)
	return nil
}

// genSummarySheet 生成汇总报表
func genSummarySheet(file *xlsx.File, details []*Detail, begin, end time.Time, filePath string) error {

	summaryMap := make(map[string]*Summary)
	for _, detail := range details {
		key := fmt.Sprintf("%d_%d", detail.CommodityId, int32(detail.Price))
		summary, ok := summaryMap[key]
		if !ok {
			summary = &Summary{
				CommodityId:      detail.CommodityId,
				EffectiveDays:    detail.EffectiveDays,
				TotalPrice:       detail.TotalPrice,
				Price:            detail.Price,
				BuyTotalPrice:    detail.BuyTotalPrice,
				Count:            detail.Count,
				BeginRemainPrice: detail.BeginRemainPrice,
				EndRemainPrice:   detail.EndRemainPrice,
				UsePrice:         detail.UsePrice,
			}
			summaryMap[key] = summary
		} else {
			summary.EffectiveDays += detail.EffectiveDays
			summary.TotalPrice += detail.TotalPrice
			summary.BuyTotalPrice += detail.BuyTotalPrice
			summary.Count += detail.Count
			summary.BeginRemainPrice += detail.BeginRemainPrice
			summary.EndRemainPrice += detail.EndRemainPrice
			summary.UsePrice += detail.UsePrice
		}
	}

	// 定义工作表名称
	sheetName := "汇总"

	// 定义标题行
	headers := []interface{}{"月份", "商品id", "有效天数", "单价", "期初剩余金额", "本期购买金额", "本期使用金额", "本期剩余金额"}

	file, streamWriter := NewExcelFileSheetWriter(file, sheetName, headers)
	rowCount := 0
	SheetRowCount := len(summaryMap)

	for _, summary := range summaryMap {
		row := make([]interface{}, 0)
		row = append(row, begin.Format("2006-01"))
		row = append(row, summary.CommodityId)
		row = append(row, summary.EffectiveDays)
		row = append(row, summary.Price)
		row = append(row, summary.BeginRemainPrice)
		row = append(row, summary.BuyTotalPrice)
		row = append(row, summary.UsePrice)
		row = append(row, summary.EndRemainPrice)

		// 写入一行
		// 计算当前行的单元格位置
		rowNumber1 := rowCount%SheetRowCount + 2 // 从第2行开始
		cell1, _ := xlsx.CoordinatesToCellName(1, rowNumber1)
		if err := streamWriter.SetRow(cell1, row); err != nil {
			log.Fatalf("写入数据行 %d 失败: %v", rowNumber1, err)
		}

		rowCount += 1

		if rowCount%SheetRowCount == 0 {
			SaveExcelFile(streamWriter, file, sheetName, begin, end, filePath)
			runtime.GC()
			time.Sleep(5 * time.Second)
		}
	}
	log.InfoWithCtx(context.Background(), "genSummarySheet success. begin:%v, end:%v, summaryMap len:%d", begin, end, len(summaryMap))
	return nil
}

func genDetailList(orders []*FinancialStatements, begin, end time.Time) []*Detail {
	// 根据订单记录创建时间倒序
	sort.SliceStable(orders, func(i, j int) bool {
		return orders[j].Id < orders[i].Id
	})

	details := make([]*Detail, 0)
	for _, order := range orders {
		detail := &Detail{
			Id:            order.Id,
			OrderId:       order.OrderNo,
			Uid:           order.Uid,
			BuyTime:       order.CreateTime,
			BeginTime:     order.StartTime,
			EndTime:       order.EndTime,
			CommodityId:   order.CommodityId,
			Count:         order.Count,
			CTime:         order.CreateTime,
			EffectiveDays: order.EffectiveDay,
			Price:         float64(order.AvgPrice),
			TotalPrice:    order.TotalPrice,
			BuyDays:       0,
			BeginDays:     0,
			BuyTotalPrice: 0,
		}

		if detail.BuyTime.After(begin) && detail.BuyTime.Before(end) {
			detail.BuyTotalPrice = float64(order.TotalPrice)
			detail.BuyDays = float64(order.EffectiveDay * order.Count)
		}

		detail.UseDays += calcUnionDay(order.StartTime, order.EndTime, begin, end)
		// 跨过账期开始时间
		if order.CreateTime.Before(begin) && order.EndTime.After(begin) {
			detail.BeginDays += calcRemainDays(detail.BeginTime, detail.EndTime, begin)
		}
		// 跨过账期结束时间
		if order.CreateTime.Before(end) && order.EndTime.After(end) {
			detail.EndDays += calcRemainDays(detail.BeginTime, detail.EndTime, end)
		}

		detail.BeginRemainPrice = float64(detail.TotalPrice) / float64(detail.EffectiveDays*detail.Count) * (detail.BeginDays)
		detail.EndRemainPrice = float64(detail.TotalPrice) / float64(detail.EffectiveDays*detail.Count) * (detail.EndDays)
		detail.UsePrice = float64(detail.TotalPrice) / float64(detail.EffectiveDays*detail.Count) * (detail.UseDays)
		details = append(details, detail)

	}
	log.InfoWithCtx(context.Background(), "genDetailList success. begin:%v, end:%v, summaryMap len:%d", begin, end, len(orders))
	return details
}

func genDetailSheet(file *xlsx.File, details []*Detail, begin, end time.Time, filePath string) error {

	// 定义工作表名称
	sheetName := "明细"

	// 定义标题行
	headers := []interface{}{"账期", "订单id", "uid", "购买日期", "商品id", "商品有效天数", "购买数量", "形象单价", "起始日期", "结束日期",
		"期初剩余价值", "本期购买价值", "本月核销价值", "本月剩余价值", "期初剩余天数", "本期购买天数", "本月核销天数", "本月剩余天数"}

	file, streamWriter := NewExcelFileSheetWriter(file, sheetName, headers)
	rowCount := 0
	SheetRowCount := len(details)

	for _, detail := range details {
		row := make([]interface{}, 0)
		row = append(row, begin.Format("2006-01"))
		row = append(row, detail.OrderId)
		row = append(row, detail.Uid)
		row = append(row, detail.BuyTime.Format("2006-01-02"))
		row = append(row, detail.CommodityId)
		row = append(row, detail.EffectiveDays) //商品有效天数
		row = append(row, detail.Count)
		row = append(row, detail.Price) // 单价
		row = append(row, detail.BeginTime.Format("2006-01-02"))
		row = append(row, detail.EndTime.Format("2006-01-02"))
		row = append(row, detail.BeginRemainPrice) // 期初剩余价值
		row = append(row, detail.BuyTotalPrice)    // 本期购买价值
		row = append(row, detail.UsePrice)         // 本月核销价值
		row = append(row, detail.EndRemainPrice)   // 本月剩余价值
		row = append(row, detail.BeginDays)        // 期初剩余天数
		row = append(row, detail.BuyDays)          // 本期购买天数
		row = append(row, detail.UseDays)
		row = append(row, detail.EndDays)

		// 写入一行
		// 计算当前行的单元格位置
		rowNumber1 := rowCount%SheetRowCount + 2 // 从第2行开始
		cell1, _ := xlsx.CoordinatesToCellName(1, rowNumber1)
		if err := streamWriter.SetRow(cell1, row); err != nil {
			log.Fatalf("写入数据行 %d 失败: %v", rowNumber1, err)
		}

		rowCount += 1
		if rowCount%SheetRowCount == 0 {
			SaveExcelFile(streamWriter, file, sheetName, begin, end, filePath)
			runtime.GC()
			time.Sleep(5 * time.Second)
		}
	}

	log.InfoWithCtx(context.Background(), "genDetailSheet success. begin:%v, end:%v, summaryMap len:%d", begin, end, len(details))
	return nil
}

// 计算剩余天数
func calcRemainDays(orderBegin, orderEnd, remainTime time.Time) float64 {
	if orderEnd.Before(remainTime) || orderEnd.Before(orderBegin) {
		return 0
	}

	tmpBegin := remainTime
	if orderBegin.After(tmpBegin) {
		tmpBegin = orderBegin
	}
	return float64(orderEnd.Sub(tmpBegin).Seconds()) / float64(daySec)
}

// 计算 orderBegin-orderEnd 和 begin-end 两个时间段的交集天数
func calcUnionDay(orderBegin, orderEnd, begin, end time.Time) float64 {
	if orderBegin.After(end) || orderEnd.Before(begin) {
		return 0
	}

	if orderBegin.Before(begin) {
		orderBegin = begin
	}
	if orderEnd.After(end) {
		orderEnd = end
	}

	if orderBegin.After(orderEnd) {
		return 0
	}

	return orderEnd.Sub(orderBegin).Seconds() / float64(daySec)
}

func SendMail(filePath, subject string, to []string) error {
	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", "<EMAIL>", "<EMAIL>", "<EMAIL>")
	m.SetHeader("Subject", subject)
	m.SetBody("text/html", "数据表在附件")
	m.Attach(filePath) //附件
	d := gomail.NewDialer("mail.52tt.com", 465, "<EMAIL>", "V7.D.sTy@%bDB50t#n.r$A4h+-FnXA")
	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		log.ErrorWithCtx(context.Background(), "SendMail failed. err:%v", err)
		return err
	}
	log.InfoWithCtx(context.Background(), "SendMail success. to:%v", to)
	return nil
}

func SaveExcelFile(streamWriter *xlsx.StreamWriter, file *xlsx.File, sheetName string, beginTs time.Time, endTs time.Time, filePath string) {
	// 关闭流式写入器
	if err := streamWriter.Flush(); err != nil {
		log.Fatalf("刷新写入器失败: %v", err)
	}

	// 设置活动工作表
	index, _ := file.GetSheetIndex(sheetName)
	file.SetActiveSheet(index)

	// 保存文件
	if err := file.SaveAs(filePath); err != nil {
		log.Fatalf("无法保存文件: %v", err)
	}

	log.InfoWithCtx(context.Background(), "保存文件成功: %s, sheetName:%s", filePath, sheetName)
}

func NewExcelFileSheetWriter(file *xlsx.File, sheetName string, headers []interface{}) (*xlsx.File, *xlsx.StreamWriter) {
	// 创建新的工作表
	index, err := file.NewSheet(sheetName)
	if err != nil {
		log.ErrorWithCtx(context.Background(), "无法创建工作表: %v", err)
	}

	err = file.DeleteSheet("sheet1")
	if err != nil {
		log.ErrorWithCtx(context.Background(), "无法删除工作表: %v", err)
	}

	// 创建新的工作表
	streamWriter, err := file.NewStreamWriter(sheetName)
	if err != nil {
		log.Fatalf("无法创建流式写入器: %v", err)
	}

	// 写入标题行
	cell, _ := xlsx.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, headers); err != nil {
		log.Fatalf("写入标题行失败: %v", err)
	}

	// 设置活动工作表
	file.SetActiveSheet(index)
	log.InfoWithCtx(context.Background(), "创建新的工作表成功 %s-%d", sheetName, index)
	return file, streamWriter
}
