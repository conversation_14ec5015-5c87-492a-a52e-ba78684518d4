package resource_cfg

import (
	"context"
	"fmt"
	"github.com/coocood/freecache"
	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
 	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/clients/obsgateway"
 	"golang.52tt.com/pkg/timer"
 	"golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/virtual_image_logic"
	virtual_image_mall "golang.52tt.com/protocol/services/virtual-image-mall"
	virtualImageResourcePb "golang.52tt.com/protocol/services/virtual-image-resource"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/conf"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/model/resource-cfg/store"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"io"
	"os"
	"reflect"
	"sort"
 	"sync"
	"testing"
 
	mocksConf "golang.52tt.com/services/virtual-image/virtual-image-resource/internal/conf/mocks"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/model/resource-cfg/mocks"

	"archive/zip"
)

var (
	testMgr *Mgr

	ctx       = context.Background()
	mockStore *mocks.MockIStore
	mockConf  *mocksConf.MockIBusinessConfManager

	testUid        = uint32(1)
	testVaId       = uint32(1)
	testUserItemId = uint32(1)
)

func initTestMgr(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockStore = mocks.NewMockIStore(ctrl)
	mockConf = mocksConf.NewMockIBusinessConfManager(ctrl)

	testMgr = &Mgr{
		store:    mockStore,
		bc:       mockConf,
		wg:       sync.WaitGroup{},
		shutDown: make(chan struct{}),
	}
}

func TestMgr_GetClientListByPage(t *testing.T) {
	ctx = metadata.AppendToOutgoingContext(ctx, "x-qw-traffic-mark", "tt-dev-mars")
	resourceClient := virtualImageResourcePb.MustNewClientTo(ctx, "*********:80", grpc.WithBlock(), grpc.WithAuthority("virtual-image-resource.52tt.local"))

	var offset uint32

	var resourceList virtualImageResourcePb.GetClientListByPageResponse
	for {
		resp, err := resourceClient.GetClientListByPage(ctx, &virtualImageResourcePb.GetClientListByPageRequest{
			Offset:   offset,
			Limit:    100,
			LatestId: 0,
		})

		if err != nil {
			fmt.Println("Error:", err)
			break
		}
		resourceList.Resources = append(resourceList.Resources, resp.Resources...)
		resourceList.LatestId = resp.LatestId

		if len(resp.Resources) < 100 {
			//return
			break
		}

		offset += uint32(100)
	}

	var resourceLogic virtual_image_logic.GetResourceListResponse
	resourceLogic.BaseResp = &app.BaseResp{
		Ret:          0,
		ErrMsg:       "成功",
		AppId:        0,
		ErrInfo:      nil,
		SuccessMsg:   "",
		Ip:           0,
		RequestId:    nil,
		FaceAuthInfo: nil,
	}
	resourceLogic.LatestVersion = resourceList.LatestId

	for _, data := range resourceList.Resources {
		resourceLogic.Resources = append(resourceLogic.Resources, &virtual_image_logic.VirtualImageResourceInfo{
			Id:               data.GetId(),
			SkinName:         data.GetSkinName(),
			ResourceType:     data.GetResourceType(),
			ResourceName:     data.GetResourceName(),
			ResourceUrl:      data.GetResourceUrl(),
			Version:          data.GetUpdateTime(),
			Essential:        data.GetEssential(),
			ShelfTime:        data.GetShelfTime(),
			ExpireTime:       data.GetExpireTime(),
			EncryptKey:       data.GetEncryptKey(),
			DisplayName:      data.GetDisplayName(),
			IconUrl:          data.GetIconUrl(),
			Category:         data.GetCategory(),
			SubCategory:      data.GetSubCategory(),
			Md5:              data.GetMd5(),
			Level:            data.GetLevel(),
			LevelIcon:        data.GetLevelIcon(),
			Sex:              data.GetSex(),
			LevelWebp:        data.GetLevelWebp(),
			ScaleAble:        data.GetScaleAble(),
			DefaultAnimation: data.GetDefaultAnimation(),
			ResourcePrefix:   data.GetResourcePrefix(),
			CustomMap:        data.GetCustomMap(),
		})
	}

	// 写入ZIP文件
	zipFileName := fmt.Sprintf("resource_list.zip")
	zipFile, err := os.Create(zipFileName)
	if err != nil {
		fmt.Println("Error creating zip file:", err)
		return
	}

	zipWriter := zip.NewWriter(zipFile)

	// 创建一个新的文件条目在ZIP文件中
	fileInZip, err := zipWriter.Create("resource_list")
	if err != nil {
		fmt.Println("Error creating file in zip:", err)
		return
	}

	// 将资源列表的二进制数据写入ZIP文件
	resourceBytes, err := proto.Marshal(&resourceLogic)
	if err != nil {
		fmt.Println("Error marshaling resource list:", err)
		return
	}

	writeSize, err := fileInZip.Write(resourceBytes)
	if err != nil {
		fmt.Println("Error writing to zip file:", err)
		return
	}
	defer zipFile.Close()
	defer zipWriter.Close()
	fmt.Println("ZIP file created successfully.", writeSize)

}

func TestMgr_GetClientListByPage2(t *testing.T) {

	// 读取ZIP文件的内容
	file, err := os.Open("resource_list.zip")
	if err != nil {
		fmt.Println("uploadImage fail to ReadFile.err", err)
		return
	}

	fileBytes, err := io.ReadAll(file)
	fmt.Println("ReadFile size: ", len(fileBytes), err)

	// 读取ZIP文件以验证写入
	reader, err := zip.OpenReader("resource_list.zip")
	if err != nil {
		fmt.Println("Error reading zip file:", err)
		return
	}

	defer reader.Close()

	// 读取ZIP文件的内容
	for _, file := range reader.File {
		fileReader, err := file.Open()
		if err != nil {
			fmt.Println("Error opening file in zip:", err)
			continue
		}
		defer fileReader.Close()

		fileBytes, err := io.ReadAll(fileReader)
		if err != nil {
			fmt.Println("Error reading file in zip:", err)
			continue
		}

		// 验证写入的内容
		fmt.Println("size:  md5 ", len(fileBytes))

		var resourceLogic virtual_image_logic.GetResourceListResponse
		err = proto.Unmarshal(fileBytes, &resourceLogic)
		if err != nil {
			panic(err)
		}
		sort.SliceStable(resourceLogic.Resources, func(i, j int) bool {
			return resourceLogic.Resources[i].GetId() > resourceLogic.Resources[i].GetId()
		})

		fmt.Println("GetClientListByPage2", len(resourceLogic.Resources))
		for _, data := range resourceLogic.Resources {
			if data.Id == 28438 || data.Id == 23088   {
				fmt.Println("GetClientListByPage2", data)
			}
		}
	}

	os.Remove("resource_list.zip")

}

func TestMgr_getResourceId(t *testing.T) {
	initTestMgr(t)
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx          context.Context
		resourceName string
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		want     uint32
		initFunc func()
		wantErr  bool
	}{
		// TODO: Add test cases.
		{
			name: "TestMgr_getResourceId",
			fields: fields{
				store: mockStore,
				bc:    mockConf,
				wg:    sync.WaitGroup{},
			},
			args: args{
				ctx:          ctx,
				resourceName: "test_resource",
			},
			initFunc: func() {
				mockStore.EXPECT().GetVirtualImageResourceIdByResourceName(gomock.Any(), gomock.Any()).Return(uint32(0), nil).AnyTimes()
				mockStore.EXPECT().InsertResourceIdRecord(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil).AnyTimes()
			},
			want:    uint32(1),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt.initFunc()
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			got, err := m.getResourceId(tt.args.ctx, tt.args.resourceName)
			if (err != nil) != tt.wantErr {
				t.Errorf("getResourceId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("getResourceId() got = %v, want %v", got, tt.want)
			}
		})
	}
}


func TestMgr_AddLevelConfig(t *testing.T) {
	initTestMgr(t)

	type fields struct {
		store store.IStore
		bc    conf.IBusinessConfManager
	}
	type args struct {
		ctx     context.Context
		request *virtualImageResourcePb.LevelConfig
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		initFunc func()
		wantErr  bool
	}{
		// TODO: Add test cases.
		{
			name: "TC01 - Success",
			fields: fields{
				store: mockStore,
				bc:    mockConf,
			},
			args: args{
				ctx:     context.Background(),
				request: &virtualImageResourcePb.LevelConfig{},
			},
			wantErr: false,
			initFunc: func() {
				mockStore.EXPECT().AddLevelConfig(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			},
		},
	}
	for _, tt := range tests {
		tt.initFunc()
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store: tt.fields.store,
				bc:    tt.fields.bc,
			}
			if err := m.AddLevelConfig(tt.args.ctx, tt.args.request); (err != nil) != tt.wantErr {
				t.Errorf("AddLevelConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMgr_AddVirtualImageResource(t *testing.T) {
	initTestMgr(t)

	type fields struct {
		store            store.IStore
		bc               conf.IBusinessConfManager
		cacheMapResource sync.Map
	}
	type args struct {
		ctx     context.Context
		request *virtualImageResourcePb.AddVirtualImageResourceRequest
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		want     *virtualImageResourcePb.AddVirtualImageResourceResponse
		wantErr  bool
		initFunc func()
	}{
		// TODO: Add test cases.
		
	}
	for _, tt := range tests {
		tt.initFunc()
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:            tt.fields.store,
				bc:               tt.fields.bc,
				cacheMapResource: tt.fields.cacheMapResource,
			}
			got, err := m.AddVirtualImageResource(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddVirtualImageResource() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddVirtualImageResource() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_BatchUpdateIcon(t *testing.T) {
	initTestMgr(t)
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx     context.Context
		request *virtualImageResourcePb.BatchUpdateIconRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			if err := m.BatchUpdateIcon(tt.args.ctx, tt.args.request); (err != nil) != tt.wantErr {
				t.Errorf("BatchUpdateIcon() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMgr_CloneVirtualImageResource(t *testing.T) {
	initTestMgr(t)
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx     context.Context
		request *virtualImageResourcePb.CloneVirtualImageResourceRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *virtualImageResourcePb.CloneVirtualImageResourceResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			got, err := m.CloneVirtualImageResource(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("CloneVirtualImageResource() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CloneVirtualImageResource() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_DeleteVirtualImageResource(t *testing.T) {
	initTestMgr(t)
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx     context.Context
		request *virtualImageResourcePb.DeleteVirtualImageResourceRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			if err := m.DeleteVirtualImageResource(tt.args.ctx, tt.args.request); (err != nil) != tt.wantErr {
				t.Errorf("DeleteVirtualImageResource() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMgr_EditVirtualImageResource(t *testing.T) {
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
 		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx     context.Context
		request *virtualImageResourcePb.EditVirtualImageResourceRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *virtualImageResourcePb.EditVirtualImageResourceResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			got, err := m.EditVirtualImageResource(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("EditVirtualImageResource() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("EditVirtualImageResource() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_GetActionResourceMap(t *testing.T) {
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *virtualImageResourcePb.GetActionResourceMapResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			got, err := m.GetActionResourceMap(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetActionResourceMap() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetActionResourceMap() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_GetClientListByPage1(t *testing.T) {
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx     context.Context
		request *virtualImageResourcePb.GetClientListByPageRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *virtualImageResourcePb.GetClientListByPageResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			got, err := m.GetClientListByPage(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetClientListByPage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetClientListByPage() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_GetDefaultResourceList(t *testing.T) {
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *virtualImageResourcePb.GetDefaultResourceListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			got, err := m.GetDefaultResourceList(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDefaultResourceList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetDefaultResourceList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_GetVirtualImageResourceCategory(t *testing.T) {
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx context.Context
		req *virtualImageResourcePb.GetVirtualImageResourceCategoryRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *virtualImageResourcePb.GetVirtualImageResourceCategoryResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			got, err := m.GetVirtualImageResourceCategory(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetVirtualImageResourceCategory() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetVirtualImageResourceCategory() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_GetVirtualImageResourcesByIds(t *testing.T) {
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx     context.Context
		request *virtualImageResourcePb.GetVirtualImageResourcesByIdsRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *virtualImageResourcePb.GetVirtualImageResourcesByIdsResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			got, err := m.GetVirtualImageResourcesByIds(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetVirtualImageResourcesByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetVirtualImageResourcesByIds() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_SearchVirtualImageResource(t *testing.T) {
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx     context.Context
		request *virtualImageResourcePb.SearchVirtualImageResourceRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *virtualImageResourcePb.SearchVirtualImageResourceResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			got, err := m.SearchVirtualImageResource(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("SearchVirtualImageResource() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SearchVirtualImageResource() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_SetVirtualImageResourceForSale(t *testing.T) {
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx    context.Context
		idList []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			if err := m.SetVirtualImageResourceForSale(tt.args.ctx, tt.args.idList); (err != nil) != tt.wantErr {
				t.Errorf("SetVirtualImageResourceForSale() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMgr_Stop(t *testing.T) {
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			m.Stop()
		})
	}
}

func TestMgr_UpdateLevelConfig(t *testing.T) {
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx     context.Context
		request *virtualImageResourcePb.LevelConfig
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			if err := m.UpdateLevelConfig(tt.args.ctx, tt.args.request); (err != nil) != tt.wantErr {
				t.Errorf("UpdateLevelConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMgr_UpdateVirtualImageResourceStatus(t *testing.T) {
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx    context.Context
		idList []uint32
		status uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			if err := m.UpdateVirtualImageResourceStatus(tt.args.ctx, tt.args.idList, tt.args.status); (err != nil) != tt.wantErr {
				t.Errorf("UpdateVirtualImageResourceStatus() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMgr_UpdateVirtualImageResourceUrl(t *testing.T) {
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx     context.Context
		request *virtualImageResourcePb.UpdateVirtualImageResourceUrlRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *virtualImageResourcePb.UpdateVirtualImageResourceUrlResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			got, err := m.UpdateVirtualImageResourceUrl(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateVirtualImageResourceUrl() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateVirtualImageResourceUrl() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_getResourceId1(t *testing.T) {
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx          context.Context
		resourceName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			got, err := m.getResourceId(tt.args.ctx, tt.args.resourceName)
			if (err != nil) != tt.wantErr {
				t.Errorf("getResourceId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("getResourceId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_isNewClientVersion(t *testing.T) {
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
 				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			if got := m.isNewClientVersion(tt.args.ctx); got != tt.want {
				t.Errorf("isNewClientVersion() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_transStoreToPB(t *testing.T) {
	type fields struct {
		store             store.IStore
		bc                conf.IBusinessConfManager
		wg                sync.WaitGroup
		shutDown          chan struct{}
		mutex             sync.RWMutex
		latestVersionTs   uint32
		cacheList         []*store.VirtualImageResourceInfo
		cacheVersionList  []*store.VirtualImageResourceInfo
		levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
		ActionResourceMap map[uint32]uint32
		mallClient        virtual_image_mall.VirtualImageMallClient
		cacheMapResource  sync.Map
		obsGatewayCli     *obsgateway.Client
		timerD            *timer.Timer
		cdnResourceRecord store.VirtualImageResourceInfoCDN
		localCache        freecache.Cache
	}
	type args struct {
		ctx context.Context
		v   *store.VirtualImageResourceInfo
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *virtualImageResourcePb.VirtualImageResourceInfo
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Mgr{
				store:             tt.fields.store,
				bc:                tt.fields.bc,
				wg:                tt.fields.wg,
				shutDown:          tt.fields.shutDown,
				mutex:             tt.fields.mutex,
				latestVersionTs:   tt.fields.latestVersionTs,
				cacheList:         tt.fields.cacheList,
				
				levelConfigMap:    tt.fields.levelConfigMap,
				ActionResourceMap: tt.fields.ActionResourceMap,
				mallClient:        tt.fields.mallClient,
				cacheMapResource:  tt.fields.cacheMapResource,
				obsGatewayCli:     tt.fields.obsGatewayCli,
				timerD:            tt.fields.timerD,
				cdnResourceRecord: tt.fields.cdnResourceRecord,
				localCache:        tt.fields.localCache,
			}
			if got := m.transStoreToPB(tt.args.ctx, tt.args.v); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("transStoreToPB() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewMgr(t *testing.T) {
	type args struct {
		ctx context.Context
		s   mysql.DBx
		bc  conf.IBusinessConfManager
	}
	tests := []struct {
		name    string
		args    args
		want    *Mgr
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewMgr(tt.args.ctx, tt.args.s, tt.args.bc)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewMgr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewMgr() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSafeInt64ToUint32(t *testing.T) {
	type args struct {
		val int64
	}
	tests := []struct {
		name string
		args args
		want uint32
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SafeInt64ToUint32(tt.args.val); got != tt.want {
				t.Errorf("SafeInt64ToUint32() = %v, want %v", got, tt.want)
			}
		})
	}
}
