package main

import (
	"context"
	"fmt"
	"github.com/coocood/freecache"
	_ "github.com/joho/godotenv/autoload"
	"github.com/larksuite/oapi-sdk-go/v3"
	"github.com/larksuite/oapi-sdk-go/v3/core"
	"github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/virtual-image-resource"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/model/resource-cfg/store"
	"sort"
	"strings"
)

var (
	AppID     = "cli_a4fefd83137ad00e"
	AppSecret = "4Bm9YuHeqQAAb9z1lPbDVcLnQKQd1Jgt"

	AppToken = "K3PObLjETapgK4stQrLcgsoqnCb"
	TableId  = "tblj74HoTF67OXG9"
	ViewId   = "vewgtDnaZ4"

	localCache freecache.Cache
	larkClient *lark.Client
)

type SuitValue struct {
	Text string `json:"text"`
	Type string `json:"type"`
}

type Suit struct {
	Type  int         `json:"type"`
	Value []SuitValue `json:"value"`
}

var testStore *store.Store

func init() {
	mysqlConfig := &mysqlConnect.MysqlConfig{
		Host:     "**********",
		Port:     3306,
		Database: "appsvr",
		Charset:  "utf8",
		UserName: "godman",
		Password: "thegodofman",
	}

	dbCli, err := mysqlConnect.NewClient(context.Background(), mysqlConfig)
	if err != nil {
		return
	}
	testStore = store.NewStore(dbCli)
}

func main() {
	// 创建 Client
	larkMgr := NewLarkMgr()

	var pageToken string
	var sheetDataList []Response
	for {
		dataList, nextPage, err := larkMgr.GetSheetDataByPageToken(pageToken)
		if err != nil {
			fmt.Println(err)
			return
		}

		sheetDataList = append(sheetDataList, dataList...)
		fmt.Println("GetSheetDataByPageToken", len(dataList), nextPage)
		if nextPage == "" {
			break
		}
		pageToken = nextPage
	}

	for _, item := range sheetDataList {
		var hasPrefix bool //是否侧身资源
		isSuit := true //是否是套装
		var itemId string
		suitName := item.Fields.SuiteIDItemCode.Value[0].Text
		progress := item.Fields.Progress
		if len(item.Fields.ItemIdList) > 0 {
			itemId = item.Fields.ItemIdList[0].Text
		}
		
		fmt.Println("sheetDataList ", item.RecordId, suitName, itemId)
		
		if strings.HasPrefix(suitName, "c") {
			suitName = strings.Replace(suitName, "c", "", 1)
			hasPrefix = true
		}
		
		var resourceList []store.VirtualImageResourceInfo
		var resourceIDList []string
		var idList []uint32
		var err error
		var updateDbStatus bool
		
		resourceList, err = testStore.SearchVirtualImageResource(context.Background(), &pb.SearchVirtualImageResourceRequest{
			Suit:   suitName,
			Offset: 0,
			Limit:  100,
		})
		if err != nil {
			log.ErrorWithCtx(context.Background(), "Error searching virtual image resource: %v", err)
			continue
		}
		
		//按套装没查到数据  查单品
		if len(resourceList) == 0 {
			resourceList, err = testStore.SearchVirtualImageResource(context.Background(), &pb.SearchVirtualImageResourceRequest{
				SkinName: suitName,
				Offset:   0,
				Limit:    100,
			})
			if err != nil {
				log.ErrorWithCtx(context.Background(), "Error searching virtual image resource: %v", err)
				continue
			}
			isSuit = false
		}
		
		sort.Slice(resourceList, func(i, j int) bool {
			return resourceList[i].ID < resourceList[j].ID
		})
		
		for _, r := range resourceList {
			//套装 过滤特姿
			if isSuit && r.SubCategory == uint32(pb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TEZI) {
				continue
			}
			
			//过滤已删除物品
			if r.IsDeleted {
				continue
			}
			
			if hasPrefix {
				if r.ResourcePrefix != "c" {
					continue
				}
			} else {
				if r.ResourcePrefix != "" {
					continue
				}
			}
			
			if r.Status != getStatusIntByString(progress) && progress != "不用测"{
				fmt.Println("getStatusIntByString status not same:",suitName, r.ID, r.Status, getStatusIntByString(progress))
				updateDbStatus = true
				idList = append(idList, r.ID)
			}
			
			if r.DefaultSuit == suitName || r.SkinName == suitName || r.ResourceName == suitName   {
				resourceIDList = append(resourceIDList, fmt.Sprintf("%d", r.ID))
			}
		}

		if len(resourceIDList) > 0 {
			if itemId != strings.Join(resourceIDList, ",") {
				fmt.Println("RecordId not same id list:", suitName, item.RecordId, "itemId:", itemId, "resourceIDList:", resourceIDList)
				larkMgr.UpdateSheetByRecordID(context.Background(), item.RecordId, "物品ID", strings.Join(resourceIDList, ","))
			}

			if updateDbStatus {
				fmt.Println("RecordId not same status:",suitName, "status:", progress, idList,  getStatusIntByString(progress), updateDbStatus)
				testStore.UpdateVirtualImageResourceStatus(context.Background(), idList, getStatusIntByString(progress))
			}
		}
	}

}

// Response 是最外层的响应结构体
type Response struct {
	Fields   Fields `json:"Fields"`
	RecordId string `json:"_"`
}

// Fields 包含记录的字段
type Fields struct {
	SuiteIDItemCode SuiteIDItemCode `json:"套装ID/物品码"`
	Progress        string          `json:"进度"`
	ItemIdList      []SuiteIDItem   `json:"物品ID"`
}

// SuiteIDItemCode 是套装ID/物品码的具体内容
type SuiteIDItemCode struct {
	Type  int           `json:"type"`
	Value []SuiteIDItem `json:"value"`
}

// SuiteIDItem 是套装ID/物品码的具体项
type SuiteIDItem struct {
	Text string `json:"text"`
	Type string `json:"type"`
}

func writeResourceIDList(recordId string, list, userToken string, client *lark.Client) {
	req := larkbitable.NewUpdateAppTableRecordReqBuilder().
		AppToken(`K3PObLjETapgK4stQrLcgsoqnCb`).
		TableId(`tbll8k9HVzOt8LTl`).
		RecordId(recordId).
		AppTableRecord(larkbitable.NewAppTableRecordBuilder().
			Fields(map[string]interface{}{`物品ID`: list}).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Bitable.V1.AppTableRecord.Update(context.Background(), req, larkcore.WithTenantAccessToken(userToken))

	// 处理错误
	if err != nil {
		fmt.Println("writeResourceIDList failed", err)
		return
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Printf("logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return
	}
}

func getStatusIntByString(name string) uint32 {
	switch name {
	case "已上传，待测试":
		return 3
	case "测试中":
		return 4
	case "已重新上传待测试":
		return 5
	case "有问题待修复":
		return 6
	case "原画已输出":
		return 7
	case "暂停测试":
		return 8
	case "动作制作中":
		return 9
	case "原画制作中":
		return 10
	case "待定制作":
		return 11
	case "测试完成":
		return 12
	case "已完成待上传":
		return 13
	default:
		return 0
	}
}
