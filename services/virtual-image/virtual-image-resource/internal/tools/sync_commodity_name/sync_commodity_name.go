package main

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/coocood/freecache"
	_ "github.com/joho/godotenv/autoload"
	"github.com/larksuite/oapi-sdk-go/v3"
	"github.com/larksuite/oapi-sdk-go/v3/core"
	"github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/pkg/foundation/utils"
	pb "golang.52tt.com/protocol/services/virtual-image-resource"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/model/resource-cfg/store"
	"strings"
	"time"
)

const (
	tblCommodityData = "tbl_commodity_data" //商品数据表
)

var (
	AppID      = "cli_a4fefd83137ad00e"
	AppSecret  = "4Bm9YuHeqQAAb9z1lPbDVcLnQKQd1Jgt"
	larkClient *lark.Client
	localCache freecache.Cache
)

type SuitValue struct {
	Text string `json:"text"`
	Type string `json:"type"`
}

type Suit struct {
	Type  int         `json:"type"`
	Value []SuitValue `json:"value"`
}

var resourceStore *store.Store
var mallStore mysql.DBx

func init() {
	resourceMysqlConfig := &mysqlConnect.MysqlConfig{
		Host:         "************",
		Port:         3306,
		Database:     "virtual_image_resource",
		Charset:      "utf8",
		UserName:     "virtual_image_resource_rw",
		Password:     "IaxTKUaIN*XCrVE3",
		MaxIdleConns: 10,
		MaxOpenConns: 10,
	}

	dbCli, err := mysqlConnect.NewClient(context.Background(), resourceMysqlConfig)
	if err != nil {
		return
	}
	resourceStore = store.NewStore(dbCli)

	mallMysqlConfig := &mysqlConnect.MysqlConfig{
		Host:         "***********",
		Port:         3306,
		Database:     "virtual_image_mall",
		Charset:      "utf8",
		UserName:     "virtual_image_mall_rw",
		Password:     "2oOka6tQ1Tdq6fB*",
		MaxIdleConns: 10,
		MaxOpenConns: 10,
	}

	mallStore, err = mysqlConnect.NewClient(context.Background(), mallMysqlConfig)
	if err != nil {
		return
	}
}

func main() {
	// 创建 Client
	larkClient = lark.NewClient(AppID, AppSecret)
	var nextToken string
	for {
		nextToken = refreshCommodityName(nextToken)
		if len(nextToken) > 0 {
			nextToken = refreshCommodityName(nextToken)
			if len(nextToken) > 0 {
				continue
			} else {
				break
			}
		} else {
			break
		}
	}
	fmt.Println("refreshCommodityName Done")
}
func refreshCommodityName(pageToken string) string {
	userToken, err := genAccessToken()
	if err != nil {
		fmt.Println(err)
		return ""
	}

	suitFieldName := "套装ID/物品码"
	start := time.Now()

	// 创建请求对象
	req := larkbitable.NewSearchAppTableRecordReqBuilder().
		AppToken(`K3PObLjETapgK4stQrLcgsoqnCb`).
		TableId(`tblj74HoTF67OXG9`).
		UserIdType(`open_id`).
		PageSize(500).
		PageToken(pageToken).
		Body(larkbitable.NewSearchAppTableRecordReqBodyBuilder().
			ViewId(`vewgtDnaZ4`).
			FieldNames([]string{suitFieldName, `物品ID`}).
			AutomaticFields(false).
			Sort([]*larkbitable.Sort{
				larkbitable.NewSortBuilder().
					FieldName(suitFieldName).
					Desc(true).
					Build(),
			}).
			Build()).
		Build()

	// 发起请求
	resp, err := larkClient.Bitable.V1.AppTableRecord.Search(context.Background(), req, larkcore.WithTenantAccessToken(userToken))

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return ""
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Printf("logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return ""
	}

	for _, item := range resp.Data.Items {
		suit, err := processItem(item.Fields[suitFieldName])
		if err != nil {
			fmt.Printf("Error processing item:%+v - err:%v\n", item, err)
			continue
		}

		var resourceIDList []uint32
		var suitName string
		for _, v := range suit.Value {
			if len(v.Text) == 0 {
				continue
			}

			//先搜索套装
			suitName = v.Text
			var hasPrefix bool
			if strings.HasPrefix(suitName, "c") {
				suitName = strings.Replace(suitName, "c", "", 1)
				hasPrefix = true
			}

			resource, err := resourceStore.SearchVirtualImageResource(context.Background(), &pb.SearchVirtualImageResourceRequest{
				Suit:   suitName,
				Offset: 0,
				Limit:  50,
			})
			if err != nil {
				fmt.Printf("Error searching virtual image resource: %v\n", err)
				continue
			}

			//找不到套装  搜索物品 正面
			if len(resource) == 0 {
				if strings.Contains(suitName, "csingle") {
					suitName = strings.Replace(suitName, "csingle", "single", 1)
					hasPrefix = true
				}
				resource, err = resourceStore.SearchVirtualImageResource(context.Background(), &pb.SearchVirtualImageResourceRequest{
					SkinName: suitName,
					Offset:   0,
					Limit:    10,
				})

				if len(resource) > 0 {
					var resourceSingle []store.VirtualImageResourceInfo
					for _, r := range resource {
						if hasPrefix {
							if r.ResourcePrefix != "c" {
								continue
							}
						} else {
							if r.ResourcePrefix != "" {
								continue
							}
						}

						if r.SkinName == suitName || r.ResourceName == suitName {
							resourceSingle = append(resourceSingle, r)
						}
					}

					resource = resourceSingle
				}
			}

			for _, r := range resource {
				if hasPrefix {
					if r.ResourcePrefix != "c" {
						continue
					}
				} else {
					if r.ResourcePrefix != "" {
						continue
					}
				}
				resourceIDList = append(resourceIDList, r.ID)
			}
		}

		if len(resourceIDList) > 0 {
			//查找商品信息
			commodityData, err := GetCommodityDataList(context.Background(), resourceIDList)
			if err != nil {
				fmt.Println("GetCommodityDataList failed", err)
				continue
			}

			if len(commodityData) == 0 {
				fmt.Printf("Commodity Record Not Found suitName:%s  == %v\n", suitName, resourceIDList)
				continue
			}

			var commodityNames []string
			for _, v := range commodityData {
				commodityNames = append(commodityNames, v.CommodityName)
			}
			fmt.Printf("Commodity Record Success :%v  %s == %v\n", commodityNames, suitName, resourceIDList)
			writeResourceCommodityName(*item.RecordId, strings.Join(commodityNames, ","), userToken, larkClient)

		} else {
			fmt.Printf("Item Record Not found suitName:%s \n", suitName)
		}
	}

	if !*resp.Data.HasMore {
		return ""
	}

	fmt.Println("cost:", time.Since(start))
	return *resp.Data.PageToken
}

func writeResourceCommodityName(recordId string, commodityName, userToken string, client *lark.Client) {
	req := larkbitable.NewUpdateAppTableRecordReqBuilder().
		AppToken(`K3PObLjETapgK4stQrLcgsoqnCb`).
		TableId(`tblj74HoTF67OXG9`).
		RecordId(recordId).
		AppTableRecord(larkbitable.NewAppTableRecordBuilder().
			Fields(map[string]interface{}{`上线名称`: commodityName}).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Bitable.V1.AppTableRecord.Update(context.Background(), req, larkcore.WithTenantAccessToken(userToken))

	// 处理错误
	if err != nil {
		fmt.Println("writeResourceIDList failed", err)
		return
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Printf("logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return
	}
}

// 假设 item.Fields 是一个 map[string]interface{}，需要进行转换
func convertFields(fields interface{}) (Suit, error) {
	suit := Suit{}
	fieldsMap, ok := fields.(map[string]interface{})
	suitType, ok := fieldsMap["type"].(float64)
	if !ok {
		return suit, fmt.Errorf("invalid type field %v", fields)
	}
	suit.Type = int(suitType)

	suitValueInterface, ok := fieldsMap["value"].([]interface{})
	if !ok {
		return suit, fmt.Errorf("invalid value field %v", fields)
	}

	for _, v := range suitValueInterface {
		valueMap, ok := v.(map[string]interface{})
		if !ok {
			return suit, fmt.Errorf("invalid value element")
		}
		text, ok := valueMap["text"].(string)
		if !ok {
			return suit, fmt.Errorf("invalid text field in value element")
		}
		typeField, ok := valueMap["type"].(string)
		if !ok {
			return suit, fmt.Errorf("invalid type field in value element")
		}
		suit.Value = append(suit.Value, SuitValue{Text: text, Type: typeField})
	}

	return suit, nil
}

// 假设 item.Fields 是一个 map[string]interface{}，需要进行转换
func processItem(item interface{}) (Suit, error) {
	suit, err := convertFields(item)
	if err != nil {
		return suit, err
	}

	return suit, nil
}

type CommodityData struct {
	CommodityId         uint32          `db:"commodity_id" json:"commodity_id"`                   //商品id
	CommodityName       string          `db:"commodity_name" json:"commodity_name"`               //商品名称
	Category            uint32          `db:"category" json:"category"`                           //商品品类大类 see CommodityCategory_*
	SubCategory         uint32          `db:"sub_category" json:"sub_category"`                   //商品品类子类
	ResourceIdList      json.RawMessage `db:"resource_id_list" json:"resource_id_list"`           //资源id列表
	CommodityType       uint32          `db:"commodity_type" json:"commodity_type"`               //商品类型 1:单品 2:套装
	ShelfTime           time.Time       `db:"shelf_time" json:"shelf_time"`                       //上架时间
	ExpireTime          time.Time       `db:"expire_time" json:"expire_time"`                     //下架时间
	ExpireTimeShow      bool            `db:"expire_time_show" json:"expire_time_show"`           //下架时间展示
	GainPath            uint32          `db:"gain_path" json:"gain_path"`                         //获取途径 1:商店 2:活动
	Rank                uint32          `db:"c_rank" json:"c_rank"`                               //排序
	CreateTime          time.Time       `db:"create_time" json:"create_time"`                     //创建时间
	UpdateTime          time.Time       `db:"update_time" json:"update_time"`                     //更新时间
	Level               uint32          `db:"c_level" json:"c_level"`                             //等级
	LevelIcon           string          `db:"level_icon" json:"level_icon"`                       //等级图标
	ResourceSex         uint32          `db:"sex" json:"sex"`                                     //资源性别 1:男 2:女 3:通用
	CustomizeLogotype   string          `db:"customize_logotype" json:"customize_logotype"`       //自定义标识
	CustomizeShelfTime  time.Time       `db:"customize_shelf_time" json:"customize_shelf_time"`   //自定义标识上架时间
	CustomizeExpireTime time.Time       `db:"customize_expire_time" json:"customize_expire_time"` //自定义标识下架时间
	CommodityIcon       string          `db:"commodity_icon" json:"commodity_icon"`               //商品图标
	CommodityAnimation  string          `db:"commodity_animation" json:"commodity_animation"`     //商品动画
	ActivityInfo        string          `db:"activity_info" json:"activity_info"`                 //活动信息
	SpineAnimation      string          `db:"spine_animation" json:"spine_animation"`             //spine动画
	RedDotVersion       uint32          `db:"red_dot_version" json:"red_dot_version"`             //红点版本
	ProVideoId          uint32          `db:"pro_video_id" json:"pro_video_id"`                   //宣传片id
}

func (t *CommodityData) String() string {
	return utils.ToJson(t)
}
func (t *CommodityData) SqlString() string {
	return utils.ToSqlStr(t)
}
func (t *CommodityData) TableName() string {
	return tblCommodityData
}

func (t *CommodityData) JoinSqlString() string {
	return utils.ToTblCommodityDataSqlStr(t)
}

func GetCommodityDataList(ctx context.Context, resourceIdList []uint32) ([]*CommodityData, error) {
	list := make([]*CommodityData, 0)
	info := CommodityData{}
	tableName := info.TableName()
	query := " where "

	if len(resourceIdList) != 0 {
		query += "("
		for i, v := range resourceIdList {
			if i == len(resourceIdList)-1 {
				query += fmt.Sprintf("JSON_CONTAINS(`resource_id_list`,'%d')) and ", v)
				break
			} else {
				query += fmt.Sprintf("JSON_CONTAINS(`resource_id_list`,'%d') or ", v)
			}
		}
	}

	if query != " where " {
		query = query[:len(query)-4]
	}

	sql := fmt.Sprintf("select %s from %+v %s", info.SqlString(), tableName, query)

	rows, err := mallStore.QueryxContext(ctx, sql)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataList fail err:%v sql:%v", err, sql)
		return list, err
	}

	defer rows.Close()
	for rows.Next() {
		info := CommodityData{}
		err = rows.StructScan(&info)
		if err == nil {
			list = append(list, &info)
		} else {
			log.Errorf("GetCommodityDataList StructScan fail sql:%v %v", sql, err)
		}
	}
	return list, nil
}
