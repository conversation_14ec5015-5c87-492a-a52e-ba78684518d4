package event

import (
    "context"
    "gitlab.ttyuyin.com/tyr/x/compatible/proto"
    "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    kafka_user_info "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkauserinfo"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
)

func (k *KafkaEvent) HandleUserInfoChange(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
    ev := &kafka_user_info.UserEvent{}
    err := proto.Unmarshal(msg.Value, ev)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleUserInfoChange Failed to proto.Unmarshal %+v", err)
        return err, false
    }

    log.DebugWithCtx(ctx, "HandleUserInfoChange ev:%+v", ev)

    eventType := kafka_user_info.EVENT_TYPE(ev.GetType())
    if eventType != kafka_user_info.EVENT_TYPE_EVENT_SEX_CHG {
        return nil, false
    }

    err, needRetry := k.handleUserSexChangeEv(ctx, ev)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleUserInfoChange handleUserSexChangeEv failed, ev:%+v, err:%v", ev, err)
        return err, needRetry
    }

    return nil, false
}

func (k *KafkaEvent) handleUserSexChangeEv(ctx context.Context, ev *kafka_user_info.UserEvent) (error, bool) {
    sexEvOpt := &kafka_user_info.UserSexEventOpt{}
    err := proto.Unmarshal(ev.GetOptPbInfo(), sexEvOpt)
    if err != nil {
        log.Errorf("handleUserSexChangeEv Failed to proto.Unmarshal. ev:%+v, err:%v", ev, err)
        return err, false
    }

    // 获取正在佩戴的组件
    inuseMap, err := k.userVa.GetUserInUseMap(ctx, ev.GetUid())
    if err != nil {
        log.Errorf("handleUserSexChangeEv GetUserInUseMap failed, ev:%+v, err:%v", ev, err)
        return err, true
    }

    //items := make([]*virtual_image_user.ItemInfo, 0, len(inuseMap))
    //for SubCategory, cfgId := range inuseMap {
    //    if cfgId == 0 {
    //        continue
    //    }
    //    items = append(items, &virtual_image_user.ItemInfo{
    //        SubCategory: SubCategory,
    //        CfgId:       0, // 取消佩戴
    //    })
    //}

    if len(inuseMap) == 0 {
        return nil, false
    }

    // 全部取消佩戴
    err = k.userVa.SetUserVirtualImageInUse(ctx, &virtual_image_user.SetUserVirtualImageInUseReq{
        Uid: ev.GetUid(), IsFullUpdate: true,
    })
    if err != nil {
        log.Errorf("handleUserSexChangeEv SetUserVirtualImageInUse failed, ev:%+v, err:%v", ev, err)
        return err, true
    }

    // 推送通知
    err = k.pushChannelUserVIChange(ctx, 0, ev.GetUid(), true)
    if err != nil {
        log.Errorf("handleUserSexChangeEv pushChannelUserVIChange failed, ev:%+v, err:%v", ev, err)
        return err, true
    }

    log.InfoWithCtx(ctx, "handleUserSexChangeEv ev:%+v, sexEvOpt:%+v", ev, sexEvOpt)
    return nil, false
}
