package user_virtual_avatar

//go:generate quicksilver-cli test interface ../user-virtual-avatar
//go:generate mockgen -destination=./mocks/user_virtual_avatar.go -package=mocks golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/user-virtual-avatar IMgr

import (
    "context"
    randC "crypto/rand"
    "encoding/json"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/pkg/bylink"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app/virtual_image_logic"
    "golang.52tt.com/protocol/common/status"
    kfk_virtual_image_user "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_virtual_image_user"
    virtual_image_mall "golang.52tt.com/protocol/services/virtual-image-mall"
    virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/conf"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/anti-corruption-layer"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/user-virtual-avatar/cache"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/user-virtual-avatar/store"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/producer"
    "google.golang.org/grpc/codes"
    "math/big"
    "sort"
    "sync"
    "time"
)

const (
    SingleItemCnt = 1
    Max_OLD_CFG_ID = 28585

)

type Mgr struct {
    store store.IStore
    cache cache.ICache

    wg       sync.WaitGroup
    shutDown chan struct{}

    acLayer     anti_corruption_layer.IMgr
    kfkProducer *producer.KafkaProducer
    bc          conf.IBusinessConfManager
}

func NewMgr(s, ro mysql.DBx, cacheClient redis.Cmdable, bc conf.IBusinessConfManager, acLayer anti_corruption_layer.IMgr, kfkProducer *producer.KafkaProducer) (*Mgr, error) {
    mysqlStore := store.NewStore(s, ro)
    redisCli := cache.NewCache(cacheClient)

    m := &Mgr{
        store:       mysqlStore,
        cache:       redisCli,
        shutDown:    make(chan struct{}),
        acLayer:     acLayer,
        kfkProducer: kfkProducer,
        bc:          bc,
    }

    return m, nil
}

func (m *Mgr) Stop() {
    _ = m.cache.Close()
    _ = m.store.Close()
    _ = m.kfkProducer.Close()
    m.shutDown <- struct{}{}
    m.wg.Wait()
}

// GetUserVirtualImageList 获取用户拥有的虚拟形象
func (m *Mgr) GetUserVirtualImageList(ctx context.Context, uid uint32) ([]*virtual_image_user.UserItemInfo, error) {
    uid2List, err := m.BatchGetUserVirtualImageList(ctx, []uint32{uid})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserVirtualImageList fail to BatchGetUserVirtualImageList. uid:%d, err:%v", uid, err)
        return nil, err
    }

    list, ok := uid2List[uid]
    if !ok {
        return list, nil
    }

    return list, nil
}

// BatchGetUserVirtualImageList 批量获取用户拥有的虚拟形象
func (m *Mgr) BatchGetUserVirtualImageList(ctx context.Context, uidList []uint32) (map[uint32][]*virtual_image_user.UserItemInfo, error) {
    out := make(map[uint32][]*virtual_image_user.UserItemInfo)
    if len(uidList) == 0 {
        return out, nil
    }
    stopTime := m.bc.GetStopUseOldResourceTimeStamp()
    cacheMap, err := m.cache.BatchGetUserVirtualImageList(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserVirtualImageList fail to BatchGetUserVirtualImageList. uidList:%v, err:%v", uidList, err)
        return out, err
    }

    notExistUidList := make([]uint32, 0)
    for _, uid := range uidList {
        _, ok := cacheMap[uid]
        if !ok {
            notExistUidList = append(notExistUidList, uid)
            continue
        }
    }

    if len(notExistUidList) > 0 {
        reloadCacheMap, err := m.batReloadUserVirtualImageList(ctx, notExistUidList, stopTime)
        if err != nil {
            log.ErrorWithCtx(ctx, "BatchGetUserVirtualImageList fail to batReloadUserVirtualImageList. uidList:%v, err:%v", notExistUidList, err)
            return out, err
        }

        // 合并两个map
        for uid, list := range reloadCacheMap {
            cacheMap[uid] = list
        }
    }

    //now := time.Now()
    for uid, cacheList := range cacheMap {
        list := make([]*virtual_image_user.UserItemInfo, 0)
        for _, info := range cacheList {
            if info.VAId <= Max_OLD_CFG_ID && info.ExpireTime < stopTime {
                // 旧物品-已过期
                continue
            }
            
            if info.VAId > Max_OLD_CFG_ID && info.ExpireTime < time.Now().Unix()  {
                // 新物品-已过期
                continue
            }
            
            list = append(list, fillUserItemInfoFromCache(info))
        }
        out[uid] = list
    }

    return out, nil
}

func fillUserItemInfoFromCache(info *cache.UserVirtualImage) *virtual_image_user.UserItemInfo {
    return &virtual_image_user.UserItemInfo{
        UserItemId:  info.UserItemId,
        CfgId:       info.VAId,
        SubCategory: info.CategoryID,
        ExpireTime:  info.ExpireTime,
        UpdateTime:  info.UpdateTime,
    }
}

func (m *Mgr) batReloadUserVirtualImageList(ctx context.Context, uidList []uint32, stopTime int64) (map[uint32][]*cache.UserVirtualImage, error) {
    infoMap := make(map[uint32][]*cache.UserVirtualImage)
    if len(uidList) == 0 {
        return infoMap, nil
    }
    storeInfoMap, err := m.store.BatchGetUserVirtualImageList(ctx, uidList, stopTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "batReloadUserVirtualImageList fail to BatchGetUserVirtualImageList. uidList:%v, err:%v", uidList, err)
        return infoMap, err
    }

    for _, uid := range uidList {
        storeList, ok := storeInfoMap[uid]
        if !ok {
            infoMap[uid] = make([]*cache.UserVirtualImage, 0)
            continue
        }

        cacheList := make([]*cache.UserVirtualImage, 0)
        for _, info := range storeList {
            cacheList = append(cacheList, &cache.UserVirtualImage{
                UserItemId: info.ID,
                VAId:       info.VAId,
                CategoryID: info.CategoryID,
                AwardTime:  info.AwardTime.Unix(),
                ExpireTime: info.ExpireTime.Unix(),
                UpdateTime: info.Mtime.Unix(),
            })
        }

        infoMap[uid] = cacheList
    }

    err = m.cache.BatchSetUserVirtualImageList(ctx, infoMap)
    if err != nil {
        log.WarnWithCtx(ctx, "batReloadUserVirtualImageList fail to BatchSetUserVirtualImageList. uidList:%v, err:%v", uidList, err)
    }

    log.DebugWithCtx(ctx, "batReloadUserVirtualImageList done. uidList:%v", uidList)
    return infoMap, nil
}

// GetUserInUseMap 获取用户正在使用的虚拟形象
func (m *Mgr) GetUserInUseMap(ctx context.Context, uid uint32) (map[uint32]*cache.UserVAInUse, error) {
    inUseMap := make(map[uint32]*cache.UserVAInUse)

    uid2List, err := m.BatchGetUserInUseList(ctx, []uint32{uid})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserInUseMap fail to BatchGetUserInUseList. uid:%d, err:%v", uid, err)
        return inUseMap, err
    }

    list, ok := uid2List[uid]
    if !ok {
        return inUseMap, nil
    }

    for _, info := range list {
        inUseMap[info.VAId] = info
    }

    return inUseMap, nil
}

// BatchGetUserInUseList 批量获取用户正在使用的虚拟形象
func (m *Mgr) BatchGetUserInUseList(ctx context.Context, uidList []uint32) (map[uint32][]*cache.UserVAInUse, error) {
    out := make(map[uint32][]*cache.UserVAInUse)
    if len(uidList) == 0 {
        return out, nil
    }
    if len(uidList) > 100 {
        log.WarnWithCtx(ctx, "BatchGetUserInUseList uidList too long. len:%v", len(uidList))
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "uidList too long")
    }

    cacheMap, err := m.cache.BatGetUserVirtualImageInUseList(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserInUseList fail to BatGetUserVirtualImageInUseList. uidList:%v, err:%v", uidList, err)
        return out, err
    }

    notExistUidList := make([]uint32, 0)
    for _, uid := range uidList {
        _, ok := cacheMap[uid]
        if !ok {
            notExistUidList = append(notExistUidList, uid)
            continue
        }
    }

    if len(notExistUidList) > 0 {
        reloadCacheMap, err := m.batReloadUserInUseList(ctx, notExistUidList)
        if err != nil {
            log.ErrorWithCtx(ctx, "BatchGetUserInUseList fail to batReloadUserInUseList. uidList:%v, err:%v", notExistUidList, err)
            return out, err
        }

        // 合并两个map
        for uid, list := range reloadCacheMap {
            cacheMap[uid] = list
        }
    }

    now := time.Now()
    for uid, cacheList := range cacheMap {
        var specialPose *cache.UserVAInUse
        list := make([]*cache.UserVAInUse, 0, len(cacheList))
        for _, info := range cacheList {
            if info.ExpireTs < now.Unix() && info.Rights == 0 {
                // 已过期
                continue
            }

            if info.CategoryID == uint32(virtual_image_resource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TEZI) {
                specialPose = info // 记录特姿信息
            } else {
                list = append(list, info)
            }
        }

        // 检查是否可以使用特姿
        if specialPose != nil && m.checkCanUseSpecialPose(specialPose, list) {
            list = append(list, specialPose)
        }
        out[uid] = list
    }

    return out, nil
}

// 检查是否可以使用特姿
// 除了可以和特姿同时外显的品类外，只有使用列表完全匹配特姿套装中的组件时，才能使用
func (m *Mgr) checkCanUseSpecialPose(specialPose *cache.UserVAInUse, inuseList []*cache.UserVAInUse) bool {
    if specialPose == nil {
        return false
    }

    items := specialPose.GetSuitContent()
    if len(items) == 0 {
        return false
    }

    spIdList := make([]uint32, 0)
    for _, id := range items {
        if id == specialPose.VAId || id == 0 {
            continue
        }
        spIdList = append(spIdList, id)
    }
    if len(spIdList) == 0 {
        return false
    }

    showCateMap := make(map[uint32]bool)
    showCateList := m.bc.GetSpecialPoseShowCategoryList()
    for _, cate := range showCateList {
        showCateMap[cate] = true
    }

    inuseIdList := make([]uint32, 0)
    for _, info := range inuseList {
        if info.VAId == 0 {
            continue
        }

        // 过滤掉不需要检查的品类
        if showCateMap[info.CategoryID] {
            continue
        }
        inuseIdList = append(inuseIdList, info.VAId)
    }

    return genUintListKey(spIdList) == genUintListKey(inuseIdList)
}

func genUintListKey(list []uint32) string {
    sort.SliceStable(list, func(i, j int) bool {
        return list[i] < list[j]
    })

    return fmt.Sprintf("%v", list)
}

func (m *Mgr) batReloadUserInUseList(ctx context.Context, uidList []uint32) (map[uint32][]*cache.UserVAInUse, error) {
    infoMap := make(map[uint32][]*cache.UserVAInUse)
    if len(uidList) == 0 {
        return infoMap, nil
    }

    inuseInfoMap, err := m.store.BatchGetUserVirtualImageInUse(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "batReloadUserInUseList fail to BatchGetUserVirtualImageInUse. uidList:%v, err:%v", uidList, err)
        return infoMap, err
    }

    userVaListMap, err := m.BatchGetUserVirtualImageList(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "batReloadUserInUseList fail to BatchGetUserVirtualImageList. uidList:%v, err:%v", uidList, err)
        return infoMap, err
    }

    now := time.Now()
    useRightsUidList := make([]uint32, 0)
    for _, uid := range uidList {
        inuseList, ok := inuseInfoMap[uid]
        if !ok {
            infoMap[uid] = make([]*cache.UserVAInUse, 0)
            continue
        }

        userVaList := userVaListMap[uid]
        userVaMap := make(map[uint32]*virtual_image_user.UserItemInfo)
        for _, info := range userVaList {
            userVaMap[info.CfgId] = info
        }

        var useRights bool
        cacheList := make([]*cache.UserVAInUse, 0)
        for _, info := range inuseList {
            expireTs := userVaMap[info.VAId].GetExpireTime()
            if expireTs < now.Unix() && info.RightsType == 0 {
                // 已过期
                continue
            }

            cacheList = append(cacheList, &cache.UserVAInUse{
                VAId:       info.VAId,
                CategoryID: info.CategoryID,
                ExpireTs:   expireTs,
                Rights:     info.RightsType,
            })

            if info.RightsType > 0 {
                useRights = true
            }
        }

        infoMap[uid] = cacheList
        if useRights {
            useRightsUidList = append(useRightsUidList, uid)
        }
    }

    // 填充特姿套装内容
    err = m.fillSpecialPoseSuitContent(ctx, infoMap)
    if err != nil {
        log.ErrorWithCtx(ctx, "batReloadUserInUseList fail to fillSpecialPoseSuitContent. uidList:%v, err:%v", uidList, err)
        return infoMap, err
    }

    err = m.cache.BatSetUserVirtualImageInUseList(ctx, infoMap)
    if err != nil {
        log.WarnWithCtx(ctx, "batReloadUserInUseList fail to BatchSetUserVirtualImageInUseList. uidList:%v, err:%v", uidList, err)
    }

    go func() {
        newCtx, cancel := grpc.NewContextWithInfoTimeout(ctx, 10*time.Second)
        defer cancel()

        for _, uid := range useRightsUidList {
            // 1/3几率 触发权益类商品下架检查
            randVal, _ := randC.Int(randC.Reader, big.NewInt(int64(3)))
            if randVal.Int64() > 0 {
                continue
            }

            _ = m.CheckUserInuseRightsCommodityValid(newCtx, uid)
        }
    }()

    log.DebugWithCtx(ctx, "batReloadUserInUseList done. uidList:%v", uidList)
    return infoMap, nil
}

// fillSpecialPoseSuitContent 填充特姿套装内容
func (m *Mgr) fillSpecialPoseSuitContent(ctx context.Context, uid2List map[uint32][]*cache.UserVAInUse) error {
    spIdList := make([]uint32, 0)
    for _, list := range uid2List {
        for _, info := range list {
            if info.CategoryID == uint32(virtual_image_resource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TEZI) &&
                info.VAId > 0 {

                spIdList = append(spIdList, info.VAId) // 特姿id
                break
            }
        }
    }

    if len(spIdList) == 0 {
        return nil
    }

    itemCfgList, err := m.acLayer.BatGetVirtualImageCfg(ctx, spIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "fillSpecialPoseSuitContent fail to BatGetVirtualImageCfg. spIdList:%v, err:%v", spIdList, err)
        return err
    }

    id2Cfg := make(map[uint32]*virtual_image_resource.VirtualImageResourceInfo)
    suitIdList := make([]string, 0)
    for _, info := range itemCfgList {
        id2Cfg[info.GetId()] = info
        if info.GetDefaultSuit() == "" {
            continue
        }
        suitIdList = append(suitIdList, info.GetDefaultSuit())
    }

    suitId2Info, err := m.acLayer.GetSuitResourceList(ctx, suitIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "fillSpecialPoseSuitContent fail to GetSuitResourceList. suitIdList:%v, err:%v", suitIdList, err)
        return err
    }

    for _, list := range uid2List {
        for _, info := range list {
            if info.CategoryID != uint32(virtual_image_resource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TEZI) ||
                info.VAId == 0 {
                continue
            }

            cfg := id2Cfg[info.VAId]
            if cfg == nil {
                continue
            }

            suitInfo := suitId2Info[cfg.GetDefaultSuit()]
            if suitInfo == nil {
                continue
            }

            itemIdList := make([]uint32, 0)
            for _, item := range suitInfo.GetResources() {
                itemIdList = append(itemIdList, item.GetId())
            }
            info.SetSuitContent(itemIdList) // 设置特姿套装内容
            break
        }
    }

    return nil
}

type ItemGiveInfo struct {
    CfgId         uint32
    SubCategory   uint32
    SourceOrderId string
    OrderId       string
    DurationSec   int32
    Price         uint32
    SuitPrice     uint32
}

func genGiveItemList(uid, source uint32, sourceDesc string, itemList []*ItemGiveInfo, userVaList []*store.UserVirtualImage) ([]*store.UserVirtualImage, []*store.AwardLog) {
    now := time.Now()
    userVaMap := make(map[uint32]*store.UserVirtualImage)
    for _, info := range userVaList {
        if info.ExpireTime.Before(now) {
            continue
        }
        userVaMap[info.VAId] = info
    }

    itemStoreList := make([]*store.UserVirtualImage, 0)
    logList := make([]*store.AwardLog, 0)
    for _, info := range itemList {
        userVa, ok := userVaMap[info.CfgId]
        if !ok {
            userVa = &store.UserVirtualImage{
                VAId:       info.CfgId,
                CategoryID: info.SubCategory,
                ExpireTime: now,
            }
            userVaMap[info.CfgId] = userVa
        }

        newExpireTime := calcNewVaExpireTime(now, userVa.ExpireTime, info.DurationSec)
        logList = append(logList, &store.AwardLog{
            OrderID:          info.OrderId,
            SourceOrderID:    info.SourceOrderId,
            Uid:              uid,
            VAId:             info.CfgId,
            CategoryID:       info.SubCategory,
            AwardTime:        now,
            ExpireTime:       newExpireTime,
            AwardDurationSec: info.DurationSec,
            AwardSource:      source,
            AwardSourceDesc:  sourceDesc,
            Price:            info.Price,
            SuitPrice:        info.SuitPrice,
        })
        itemStoreList = append(itemStoreList, &store.UserVirtualImage{
            Uid:        uid,
            VAId:       info.CfgId,
            CategoryID: info.SubCategory,
            AwardTime:  now,
            ExpireTime: newExpireTime,
        })
        userVa.ExpireTime = newExpireTime
    }
    return itemStoreList, logList
}

func calcNewVaExpireTime(now, oldExpireTime time.Time, awardDurationSec int32) time.Time {
    if oldExpireTime.Before(now) {
        return now.Add(time.Duration(awardDurationSec) * time.Second)
    }
    return oldExpireTime.Add(time.Duration(awardDurationSec) * time.Second)
}

func genSuitItemOrderId(suitOrderId string, vaId uint32) string {
    return fmt.Sprintf("%s_%d", suitOrderId, vaId)
}

// GiveVirtualImageToUser 发放虚拟形象给用户
func (m *Mgr) GiveVirtualImageToUser(ctx context.Context, req *virtual_image_user.GiveVirtualImageToUserReq) (firstGain bool, err error) {
    uid := req.GetUid()
    outsideTime := time.Now()
    if req.GetOutsideTime() != 0 {
        outsideTime = time.Unix(req.GetOutsideTime(), 0)
    }

    if len(req.GetItems()) == 0 && len(req.GetSuits()) == 0 {
        log.WarnWithCtx(ctx, "GiveVirtualImageToUser fail. req:%+v, giveItemList is empty", req)
        return false, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    itemTmpList := make([]*ItemGiveInfo, 0)
    for _, info := range req.GetItems() {
        if info.GetItem().GetSubCategory() == 0 {
            log.ErrorWithCtx(ctx, "GiveVirtualImageToUser fail. uid:%d, item:%+v, subCategory is 0", uid, info)
            return false, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "subCategory is 0")
        }

        itemTmpList = append(itemTmpList, &ItemGiveInfo{
            CfgId:         info.GetItem().GetCfgId(),
            SubCategory:   info.GetItem().GetSubCategory(),
            SourceOrderId: info.GetOrderId(),
            OrderId:       info.GetOrderId(),
            DurationSec:   info.GetDurationSec(),
            Price:         info.GetItemPrice(),
        })
    }

    suitStoreList := make([]*store.UserOwnSuit, 0)
    for _, suit := range req.GetSuits() {
        if len(suit.GetItems()) == 0 {
            log.WarnWithCtx(ctx, "GiveVirtualImageToUser fail. uid:%d, suit:%+v, suitItemList is empty", uid, suit)
            continue
        }
        suitItems := make([]uint32, 0)
        price := suit.GetSuitPrice() / uint32(len(suit.GetItems()))
        var specialPoseId uint32

        for _, item := range suit.GetItems() {
            itemTmpList = append(itemTmpList, &ItemGiveInfo{
                CfgId:         item.GetCfgId(),
                SubCategory:   item.GetSubCategory(),
                SourceOrderId: suit.GetOrderId(),
                OrderId:       genSuitItemOrderId(suit.GetOrderId(), item.GetCfgId()),
                DurationSec:   suit.GetDurationSec(),
                Price:         price,
                SuitPrice:     suit.GetSuitPrice(),
            })

            if item.GetSubCategory() == uint32(virtual_image_resource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TEZI) {
                specialPoseId = item.GetCfgId() // 记录特姿信息
                continue                        // 套装特姿不记录到suitItems
            }
            suitItems = append(suitItems, item.GetCfgId())
        }

        suitStore := &store.UserOwnSuit{
            Uid:                 uid,
            SuitName:            suit.GetSuitName(),
            LastAddDurationSec:  suit.GetDurationSec(),
            SuitIcon:            suit.GetSuitIcon(),
            LevelIcon:           suit.GetLevelIcon(),
            PromotionResourceId: suit.GetPromotionResourceId(),
            SpecialPoseId:       specialPoseId,
        }
        suitStore.SetSuit(suitItems)
        suitStoreList = append(suitStoreList, suitStore)
    }

    vaIds := make([]uint32, 0)
    itemInitList := make([]*store.UserVirtualImage, 0)
    for _, info := range itemTmpList {
        itemInitList = append(itemInitList, &store.UserVirtualImage{
            Uid:        uid,
            VAId:       info.CfgId,
            CategoryID: info.SubCategory,
        })
        vaIds = append(vaIds, info.CfgId)
    }

    cnt, err := m.store.GetUserHistoryVirtualImageCnt(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GiveVirtualImageToUser fail to GetUserHistoryVirtualImageCnt. uid:%d, err:%v", uid, err)
        return false, err
    }
    firstGain = cnt == 0

    // 提前写入初始数据，防止事务中间隙锁引起死锁
    err = m.store.BatchInsertUserVirtualImage(ctx, uid, itemInitList)
    if err != nil {
        log.WarnWithCtx(ctx, "GiveVirtualImageToUser fail to BatchInsertUserVirtualImage. uid:%d, err:%v", uid, err)
    }

    if len(suitStoreList) > 0 {
        // 提前写入初始数据，防止事务中间隙锁引起死锁
        err = m.store.BatchInsertUserOwnSuit(ctx, uid, suitStoreList)
        if err != nil {
            log.ErrorWithCtx(ctx, "GiveVirtualImageToUser fail to BatchInsertUserOwnSuit. uid:%d, err:%v", uid, err)
            return false, err
        }
    }

    // 开启事务
    err = m.store.Transaction(ctx, func(tx mysql.Txx) error {
        // 查询用户虚拟形象数据
        userVaList, err := m.store.GetUserVirtualImageForUpdate(ctx, tx, uid, vaIds)
        if err != nil {
            log.ErrorWithCtx(ctx, "GiveVirtualImageToUser fail to GetUserVirtualImageForUpdate. uid:%d, err:%v", uid, err)
            return err
        }

        // 生成发放数据和流水
        itemList, logList := genGiveItemList(uid, req.GetSource(), req.GetSourceDesc(), itemTmpList, userVaList)

        // 更新用户虚拟形象数据
        err = m.store.BatchUpsertUserVirtualImage(ctx, tx, uid, itemList)
        if err != nil {
            log.ErrorWithCtx(ctx, "GiveVirtualImageToUser fail to BatchUpsertUserVirtualImage. uid:%d, err:%v", uid, err)
            return err
        }

        // 插入虚拟形象发放流水
        err = m.store.BatchInsertAwardLog(ctx, tx, outsideTime, logList)
        if err != nil {
            log.ErrorWithCtx(ctx, "GiveVirtualImageToUser fail to BatchInsertAwardLog. uid:%d, err:%v", uid, err)
            return err
        }

        if len(suitStoreList) > 0 {
            // 更新用户拥有套装数据
            err = m.store.BatchAddUserOwnSuitDuration(ctx, tx, uid, suitStoreList)
            if err != nil {
                log.ErrorWithCtx(ctx, "GiveVirtualImageToUser fail to BatchAddUserOwnSuitDuration. uid:%d, err:%v", uid, err)
                return err
            }
        }

        return nil
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GiveVirtualImageToUser fail to Transaction. req:%+v, err:%v", req, err)
        return false, err
    }

    // 删除原有物品缓存
    err = m.cache.ClearUserVirtualImageList(ctx, uid)
    if err != nil {
        log.WarnWithCtx(ctx, "GiveVirtualImageToUser fail to cache.ClearUserVirtualImageList req:%+v, err:%v", req, err)
    }

    // 删除原有套装缓存
    err = m.cache.ClearUserOwnSuitList(ctx, uid)
    if err != nil {
        log.WarnWithCtx(ctx, "GiveVirtualImageToUser fail to cache.ClearUserOwnSuitList req:%+v, err:%v", req, err)
    }

    // 删除原有佩戴缓存
    err = m.cache.DelUserVirtualImageInUseList(ctx, uid)
    if err != nil {
        log.WarnWithCtx(ctx, "GiveVirtualImageToUser fail to cache.DelUserVirtualImageInUseList req:%+v, err:%v", req, err)
    }

    log.InfoWithCtx(ctx, "GiveVirtualImageToUser done. req:%+v, firstGain:%v", req, firstGain)
    return firstGain, nil
}

//
//// SaveUserCustomSuit 保存用户自定义套装
//func (m *Mgr) SaveUserCustomSuit(ctx context.Context, req *virtual_image_user.SaveUserSuitReq) error {
//    var err error
//    storeInfo := &store.UserCustomSuit{
//        Uid:    req.GetUid(),
//        SuitId: req.GetSuitId(),
//    }
//    storeInfo.SetCustomSuit(req.GetCfgIdList())
//
//    err = m.store.UpsertUserCustomSuit(ctx, storeInfo)
//    if err != nil {
//        log.ErrorWithCtx(ctx, "SaveUserCustomSuit fail to UpsertUserCustomMatching. req:%+v, err:%v", req, err)
//        return err
//    }
//
//    err = m.cache.ClearUserCustomSuitList(ctx, req.GetUid())
//    if err != nil {
//        log.WarnWithCtx(ctx, "SaveUserCustomSuit fail to ClearUserCustomSuitList. req:%+v, err:%v", req, err)
//    }
//
//    return nil
//}
//
//func (m *Mgr) reloadUserCustomSuitList(ctx context.Context, uid uint32) ([]*cache.UserCustomSuit, error) {
//    storeList, err := m.store.GetUserCustomSuit(ctx, uid)
//    if err != nil {
//        log.ErrorWithCtx(ctx, "reloadUserCustomSuitList fail to GetUserCustomMatching. uid:%d, err:%v", uid, err)
//        return nil, err
//    }
//
//    cacheList := make([]*cache.UserCustomSuit, 0)
//    for _, info := range storeList {
//        cacheList = append(cacheList, &cache.UserCustomSuit{
//            SuitId:    info.SuitId,
//            SuitItems: info.GetCustomSuitItems(),
//        })
//    }
//
//    err = m.cache.SetUserCustomSuitList(ctx, uid, cacheList)
//    if err != nil {
//        log.WarnWithCtx(ctx, "reloadUserCustomSuitList fail to SetUserCustomSuitList. uid:%d, err:%v", uid, err)
//    }
//
//    return cacheList, nil
//}
//
//// GetUserCustomSuitList 获取用户保存的自定义套装
//func (m *Mgr) GetUserCustomSuitList(ctx context.Context, uid uint32) ([]*cache.UserCustomSuit, error) {
//    cacheList, exist, err := m.cache.GetUserCustomSuitList(ctx, uid)
//    if err != nil {
//        log.ErrorWithCtx(ctx, "GetUserCustomSuitList fail to GetUserCustomMatching. uid:%d, err:%v", uid, err)
//        return cacheList, err
//    }
//    if !exist {
//        cacheList, err = m.reloadUserCustomSuitList(ctx, uid)
//        if err != nil {
//            log.ErrorWithCtx(ctx, "GetUserCustomSuitList fail to reloadUserCustomMatching. uid:%d, err:%v", uid, err)
//            return cacheList, err
//        }
//    }
//
//    return cacheList, nil
//}

// SetUserVirtualImageInUse 设置用户正在使用的虚拟形象
func (m *Mgr) SetUserVirtualImageInUse(ctx context.Context, req *virtual_image_user.SetUserVirtualImageInUseReq) error {
    var err error
    uid := req.GetUid()
    if !req.GetIsFullUpdate() && len(req.GetItems()) == 0 {
        log.WarnWithCtx(ctx, "SetUserVirtualImageInUse fail. req:%+v, itemList is empty", req)
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    userVaList, err := m.GetUserVirtualImageList(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserVirtualImageInUse fail to GetUserVirtualImageList. req:%+v, err:%v", req, err)
        return err
    }

    userVaMap := make(map[uint32]*virtual_image_user.UserItemInfo)
    for _, info := range userVaList {
        userVaMap[info.CfgId] = info
    }

    now := time.Now()
    for _, info := range req.GetItems() {
        if info.GetCfgId() == 0 || info.GetUseRightsType() > 0 {
            // 取消佩戴或者使用权益时，无需校验
            continue
        }
        va, ok := userVaMap[info.GetCfgId()]
        if !ok || va.GetExpireTime() < now.Unix() {
            log.ErrorWithCtx(ctx, "SetUserVirtualImageInUse fail. req:%+v, cfgId:%d expired", req, info.GetCfgId())
            return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "虚拟形象组件已过期， 无法使用")
        }
    }

    ty2ItemId := make(map[uint32]uint32)
    if !req.GetIsFullUpdate() {
        ItemId2Info, err := m.GetUserInUseMap(ctx, uid)
        if err != nil {
            log.WarnWithCtx(ctx, "SetUserVirtualImageInUse fail to GetUserInUseMap. uid:%d, err: %+v", uid, err)
        }
        for _, info := range ItemId2Info {
            ty2ItemId[info.CategoryID] = info.VAId
        }
    }

    var useSpecialPose bool
    useList := make([]*store.UserVirtualImageInUse, 0)
    for _, info := range req.GetItems() {
        ty2ItemId[info.GetSubCategory()] = info.GetCfgId()

        if info.GetSubCategory() == uint32(virtual_image_resource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TEZI) &&
            info.GetCfgId() > 0 {
            useSpecialPose = true
        }

        useList = append(useList, &store.UserVirtualImageInUse{
            Uid:        uid,
            CategoryID: info.GetSubCategory(),
            VAId:       info.GetCfgId(),
            RightsType: info.GetUseRightsType(),
        })
    }

    // 没有使用特姿物品
    if !useSpecialPose {
        // 检查是否可以自动使用套装中的特姿
        specialPoseId, err := m.checkAutoUseOwnSuitSpecialPose(ctx, uid, ty2ItemId)
        if err != nil {
            log.WarnWithCtx(ctx, "SetUserVirtualImageInUse fail to checkAutoUseOwnSuitSpecialPose. req:%+v, err:%v", req, err)
        } else {
            log.DebugWithCtx(ctx, "SetUserVirtualImageInUse auto use special pose. uid:%d, specialPoseId:%d", uid, specialPoseId)
        }
        if specialPoseId > 0 {
            // 自动使用套装中的特姿
            useList = append(useList, &store.UserVirtualImageInUse{
                Uid:        uid,
                CategoryID: uint32(virtual_image_resource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TEZI),
                VAId:       specialPoseId,
            })
        }
    }

    err = m.store.Transaction(ctx, func(tx mysql.Txx) error {
        var err error

        if req.GetIsFullUpdate() {
            // 全量覆盖，即先取消佩戴用户已佩戴的所有物品
            err = m.store.ClearUserVirtualImageInUse(ctx, tx, uid)
            if err != nil {
                log.ErrorWithCtx(ctx, "SetUserVirtualImageInUse fail to ClearUserVirtualImageInUse. req:%+v, err:%v", req, err)
                return err
            }
        }

        err = m.store.UpsertUserVirtualImageInUse(ctx, tx, uid, useList)
        if err != nil {
            log.ErrorWithCtx(ctx, "SetUserVirtualImageInUse fail to UpsertUserVirtualImageInUse. req:%+v, err:%v", req, err)
            return err
        }

        return nil
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserVirtualImageInUse fail to Transaction. req:%+v, err:%v", req, err)
        return err
    }

    err = m.cache.DelUserVirtualImageInUseList(ctx, uid)
    if err != nil {
        log.WarnWithCtx(ctx, "SetUserVirtualImageInUse fail to DelUserVirtualImageInUseList. req:%+v, err:%v", req, err)
    }

    log.InfoWithCtx(ctx, "SetUserVirtualImageInUse done. req:%+v", req)

    imageList := make([]*kfk_virtual_image_user.UserVirtualImage, 0, len(ty2ItemId))
    for ty, id := range ty2ItemId {
        imageList = append(imageList, &kfk_virtual_image_user.UserVirtualImage{
            ItemId:      id,
            SubCategory: ty,
        })
    }

    // 发送kafka消息
    m.kfkProducer.ProduceUserVirtualImageChangeEvent(ctx, &kfk_virtual_image_user.UserVirtualImageChangeEvent{
        Uid: uid, UserVirtualImages: imageList,
    })

    go func() {
        newCtx, cancel := grpc.NewContextWithInfoTimeout(ctx, 3*time.Second)
        defer cancel()

        // 数据上报
        m.reportUserVirtualImageUse(newCtx, uid, req.GetItems())
    }()

    return nil
}

// checkAutoUseOwnSuitSpecialPose 检查自动使用拥有套装中的特姿
func (m *Mgr) checkAutoUseOwnSuitSpecialPose(ctx context.Context, uid uint32, ty2ItemId map[uint32]uint32) (uint32, error) {
    inuseMap := make(map[uint32]bool)
    for _, id := range ty2ItemId {
        if id == 0 {
            continue
        }
        inuseMap[id] = true
    }

    if len(inuseMap) == 0 {
        return 0, nil
    }

    suitList, err := m.GetUserOwnSuitList(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkAutoUseOwnSuitSpecialPose fail to GetUserOwnSuitList. uid:%d, err:%v", uid, err)
        return 0, err
    }

    for _, suit := range suitList {
        if suit.ExpireTime <= time.Now().Unix() || suit.SpecialPoseId == 0 || len(suit.SuitItems) == 0 {
            continue // 没有特姿, 不用处理
        }

        suitInuse := true
        for _, itemId := range suit.SuitItems {
            if !inuseMap[itemId] {
                suitInuse = false
                break
            }
        }

        if suitInuse {
            return suit.SpecialPoseId, nil
        }
    }

    return 0, nil
}

// GetUserOwnSuitList 获取用户拥有的套装
func (m *Mgr) GetUserOwnSuitList(ctx context.Context, uid uint32) ([]*cache.UserOwnSuit, error) {
    var err error

    cacheList, exist, err := m.cache.GetUserOwnSuitList(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserOwnSuitList fail to GetUserOwnSuitList. uid:%d, err:%v", uid, err)
        return nil, err
    }

    if !exist {
        cacheList, err = m.reloadUserOwnSuitList(ctx, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUserOwnSuitList fail to reloadUserOwnSuitList. uid:%d, err:%v", uid, err)
            return nil, err
        }
    }

    out := make([]*cache.UserOwnSuit, 0)
    now := time.Now()
    for _, info := range cacheList {
        if info.ExpireTime < now.Unix() {
            // 已过期
            continue
        }
        out = append(out, info)
    }

    return out, nil
}

func (m *Mgr) reloadUserOwnSuitList(ctx context.Context, uid uint32) ([]*cache.UserOwnSuit, error) {
    var err error
    cacheList := make([]*cache.UserOwnSuit, 0)
    storeList, err := m.store.GetUserOwnSuit(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "reloadUserOwnSuitList fail to GetUserOwnSuit. uid:%d, err:%v", uid, err)
        return cacheList, err
    }

    for _, info := range storeList {
        cacheList = append(cacheList, &cache.UserOwnSuit{
            SuitId:        info.Id,
            SuitName:      info.SuitName,
            SuitItems:     info.GetSuitItems(),
            SuitIcon:      info.SuitIcon,
            LevelIcon:     info.LevelIcon,
            PromotionId:   info.PromotionResourceId,
            SpecialPoseId: info.SpecialPoseId,
            ExpireTime:    info.ExpireTime.Unix(),
            UpdateTime:    info.Mtime.Unix(),
        })
    }

    err = m.cache.SetUserOwnSuitList(ctx, uid, cacheList)
    if err != nil {
        log.WarnWithCtx(ctx, "reloadUserOwnSuitList fail to SetUserOwnSuitList. uid:%d, err:%v", uid, err)
    }

    return cacheList, nil
}

// SetUserOrientation 设置用户形象朝向
func (m *Mgr) SetUserOrientation(ctx context.Context, uid, orientation uint32) error {
    err := m.cache.SetUserOrientation(ctx, uid, orientation)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserOrientation fail to SetUserOrientation. uid:%d, orientation:%d, err:%v", uid, orientation, err)
        return err
    }

    log.DebugWithCtx(ctx, "SetUserOrientation done. uid:%d, orientation:%d", uid, orientation)
    return nil
}

// BatchGetUserOrientation 批量获取用户形象朝向
func (m *Mgr) BatchGetUserOrientation(ctx context.Context, uidList []uint32) (map[uint32]uint32, error) {
    out, err := m.cache.BatchGetUserOrientation(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserOrientation fail to BatchGetUserOrientation. uidList:%v, err:%v", uidList, err)
        return out, err
    }

    for _, uid := range uidList {
        _, ok := out[uid]
        if !ok {
            out[uid] = uid%2 + 1 // 没有设置朝向的用户，通过uid确定朝向
        }
    }

    return out, nil
}

// GetUserVirtualImageAwardLogs 获取用户物品获取记录
func (m *Mgr) GetUserVirtualImageAwardLogs(ctx context.Context, req *virtual_image_user.GetUserItemObtainRecordReq) (*virtual_image_user.GetUserItemObtainRecordResp, error) {
    out := &virtual_image_user.GetUserItemObtainRecordResp{}
    recordList := make([]*virtual_image_user.ItemObtainRecord, 0)

    // 只查近6个自然月的数据
    curMonthTime := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Local)
    timeAfter := curMonthTime.AddDate(0, 0, -180)

    gotList, hasMore, err := m.store.GetUserVirtualImageAwardLogs(ctx, req.GetUid(), req.GetLimit(), req.GetOffset(), timeAfter)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserVirtualImageAwardLogs fail to GetUserVirtualImageAwardLogs. req:%+v, err:%v", req, err)
        return out, err
    }

    haveSuit := false
    recordId2vaLogMap := make(map[string][]*store.AwardLog)
    sourceOrderId2RecordIdMap := make(map[string]string)

    for _, record := range gotList {
        recordId, ok := sourceOrderId2RecordIdMap[record.SourceOrderID]
        if !ok {
            recordId = m.store.GenUserRecordOffset(record.AwardTime, record.ID)
            sourceOrderId2RecordIdMap[record.SourceOrderID] = recordId
        }

        if _, ok := recordId2vaLogMap[recordId]; !ok {

            info := &virtual_image_user.ItemObtainRecord{
                RecordId: recordId,
                ItemType: uint32(virtual_image_mall.CommodityType_COMMODITY_TYPE_SINGLE),
                //ItemInfo:     nil,
                //SuitInfo:     nil,
                TotalPrice:   record.Price,
                ObtainSource: record.AwardSource,
                ObtainDesc:   record.AwardSourceDesc,
                ObtainTime:   record.AwardTime.Unix(),
                ExpireTime:   record.ExpireTime.Unix(),
                ValidSec:     int64(record.AwardDurationSec),
            }

            // 比较recordID和SourceOrderID判断记录是否是套装
            if record.OrderID != record.SourceOrderID {
                haveSuit = true
                info.ItemType = uint32(virtual_image_mall.CommodityType_COMMODITY_TYPE_SUIT)
                info.TotalPrice = record.SuitPrice
            }

            recordList = append(recordList, info)
            recordId2vaLogMap[recordId] = make([]*store.AwardLog, 0)
        }

        recordId2vaLogMap[recordId] = append(recordId2vaLogMap[recordId], record)
    }

    suitContent2SuitInfoMap := make(map[string]*store.UserOwnSuit)
    if haveSuit {
        // 获取用户套装
        userSuitList, err := m.store.GetUserOwnSuitByTimeRange(ctx, req.GetUid(), timeAfter, time.Now())
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUserVirtualImageAwardLogs fail to GetUserOwnSuitByTimeRange. req:%+v, err:%v", req, err)
            return out, err
        }

        for _, suit := range userSuitList {
            log.DebugWithCtx(ctx, "suit:%+v", suit)
            suitContent2SuitInfoMap[suit.SuitContent] = suit
        }
    }

    for _, v := range recordList {
        list, _ := recordId2vaLogMap[v.RecordId]

        if len(list) < SingleItemCnt {
            log.WarnWithCtx(ctx, "GetUserVirtualImageAwardLogs fail to find recordId:%s", v.RecordId)
            continue
        }

        // 套装物品信息补充
        if v.ItemType == uint32(virtual_image_mall.CommodityType_COMMODITY_TYPE_SUIT) {
            itemList := make([]uint32, 0, len(list))
            for _, item := range list {
                itemList = append(itemList, item.VAId)
            }
            suitContent := m.store.GetSuitContentByItems(itemList)

            suit := suitContent2SuitInfoMap[suitContent]
            if suit == nil {
                suit = &store.UserOwnSuit{}
            }
            v.SuitInfo = &virtual_image_user.SuitGiveInfo{
                //OrderId:  "",
                SuitName: suit.SuitName,
                //DurationSec: 0,
                Items:     transferItemInfoFromStore2Pb(list),
                SuitIcon:  suit.SuitIcon,
                LevelIcon: suit.LevelIcon,
                SuitPrice: 0,
            }
            continue
        }

        // 单件物品信息补充
        v.ItemInfo = &virtual_image_user.ItemInfo{
            CfgId:       list[0].VAId,
            SubCategory: list[0].CategoryID,
        }
    }

    if hasMore && len(gotList) > 0 {
        out.NextOffset = m.store.GenUserRecordOffset(gotList[len(gotList)-1].AwardTime, gotList[len(gotList)-1].ID)
    }

    out.RecordList = recordList
    return out, nil
}

func transferItemInfoFromStore2Pb(storeList []*store.AwardLog) []*virtual_image_user.ItemInfo {
    out := make([]*virtual_image_user.ItemInfo, 0)
    for _, info := range storeList {
        out = append(out, &virtual_image_user.ItemInfo{
            CfgId:       info.VAId,
            SubCategory: info.CategoryID,
        })
    }

    return out
}

// GetVirtualImageListByExpireTime 根据过期时间获取用户虚拟形象
func (m *Mgr) GetVirtualImageListByExpireTime(ctx context.Context, startTime, endTime time.Time) ([]*store.UserVirtualImage, error) {
    if startTime.After(endTime) || endTime.Sub(startTime) > 24*time.Hour {
        log.ErrorWithCtx(ctx, "GetVirtualImageListByExpireTime fail to GetVirtualImageListByExpireTime. startTime:%s, endTime:%s", startTime, endTime)
        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    out := make([]*store.UserVirtualImage, 0)
    for i := uint32(0); i < store.UserVATblIdx; i++ {
        list, err := m.store.GetVirtualImageListByExpireTime(ctx, i, startTime, endTime)
        if err != nil {
            log.WarnWithCtx(ctx, "GetVirtualImageListByExpireTime fail to GetVirtualImageListByExpireTime. err: %v, startTime: %s, endTime: %s", err, startTime, endTime)
            continue
        }

        out = append(out, list...)
    }

    return out, nil
}

type ReportUseInfo struct {
    Id     string `json:"id"`
    Type   string `json:"type"`
    Rights string `json:"rights"`
}

func (m *Mgr) reportUserVirtualImageUse(ctx context.Context, uid uint32, useList []*virtual_image_user.ItemInfo) {
    changes := make([]*ReportUseInfo, 0)
    for _, info := range useList {
        changes = append(changes, &ReportUseInfo{
            Id:   fmt.Sprint(info.GetCfgId()),
            Type: fmt.Sprint(info.GetSubCategory()),
        })
    }
    changeJson, _ := json.Marshal(changes)

    id2Info, err := m.GetUserInUseMap(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "reportUserVirtualImageUse fail to GetUserInUseMap. uid:%d, err:%v", uid, err)
        return
    }

    details := make([]*ReportUseInfo, 0)
    for id, info := range id2Info {
        details = append(details, &ReportUseInfo{
            Id:     fmt.Sprint(id),
            Type:   fmt.Sprint(info.CategoryID),
            Rights: fmt.Sprint(info.Rights),
        })
    }
    detailJson, _ := json.Marshal(details)

    // 组装上报数据
    data := map[string]interface{}{
        "uid":    uid,
        "change": string(changeJson),
        "detail": string(detailJson),
    }
    // 上报数据
    byErr := bylink.Track(ctx, uint64(uid), "virtual_image_wear_log", data, false)
    if byErr != nil {
        log.ErrorWithCtx(ctx, "reportUserVirtualImageUse fail to bylink.Track. uid:%d, err: %+v", uid, byErr)
        return
    }
    bylink.Flush()
}

// ClearUserInuseByRights 清除用户使用的权益类虚拟形象
func (m *Mgr) ClearUserInuseByRights(ctx context.Context, uid uint32, rightsType uint32) error {
    err := m.store.ClearUserInUseRights(ctx, uid, rightsType)
    if err != nil {
        log.ErrorWithCtx(ctx, "ClearUserInuseByRights fail to ClearUserInUseRights. uid:%d, err:%v", uid, err)
        return err
    }

    err = m.cache.DelUserVirtualImageInUseList(ctx, uid)
    if err != nil {
        log.WarnWithCtx(ctx, "ClearUserInuseByRights fail to DelUserVirtualImageInUseList. uid:%d, err:%v", uid, err)
    }

    // 数据上报
    m.reportUserVirtualImageUse(ctx, uid, nil)
    return nil
}

// CheckUserInuseRightsCommodityValid 检查用户穿戴的权益类商品是否已下架
func (m *Mgr) CheckUserInuseRightsCommodityValid(ctx context.Context, uid uint32) error {
    cfgId2Info, err := m.GetUserInUseMap(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckUserInuseRightsCommodityValid fail to GetUserInUseMap. uid:%d, err:%v", uid, err)
        return err
    }

    rightsCfgIdList := make([]uint32, 0)
    for cfgId, info := range cfgId2Info {
        if info.Rights == uint32(virtual_image_logic.VirtualImageRightsType_VIRTUAL_IMAGE_RIGHTS_TYPE_INFINITE_CHANGE_CARD) {
            // 无限换装卡权益商品
            rightsCfgIdList = append(rightsCfgIdList, cfgId)
        }
    }

    if len(rightsCfgIdList) == 0 {
        // 用户没有穿戴权益类商品
        return nil
    }

    gainTy := uint32(virtual_image_logic.CommodityGainPath_COMMODITY_GAIN_PATH_INFINITE_CHANGE_CARD)
    commodityList, err := m.acLayer.GetCommodityList(ctx, gainTy, rightsCfgIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckUserInuseRightsCommodityValid fail to GetCommodityList. uid:%d, err:%v", uid, err)
        return err
    }

    cfgId2CommodityValid := make(map[uint32]bool)
    for _, commodity := range commodityList {
        for _, cfgId := range commodity.GetResourceIdList() {
            // 对应的商品仍有效
            cfgId2CommodityValid[cfgId] = true
        }
    }

    // 取消佩戴这些权益物品列表
    itemList := make([]*virtual_image_user.ItemInfo, 0)
    for _, cfgId := range rightsCfgIdList {
        if _, ok := cfgId2CommodityValid[cfgId]; ok {
            // 存在有效商品，不处理
            continue
        }

        inuseInfo, ok := cfgId2Info[cfgId]
        if !ok {
            continue
        }

        itemList = append(itemList, &virtual_image_user.ItemInfo{
            SubCategory: inuseInfo.CategoryID,
            CfgId:       0, // 取消佩戴
        })
    }

    if len(itemList) == 0 {
        return nil
    }

    err = m.SetUserVirtualImageInUse(ctx, &virtual_image_user.SetUserVirtualImageInUseReq{
        Uid:   uid,
        Items: itemList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckUserInuseRightsCommodityValid fail to SetUserVirtualImageInUse. uid:%d, err:%v", uid, err)
        return err
    }

    log.InfoWithCtx(ctx, "CheckUserInuseRightsCommodityValid success. uid:%d", uid)
    return nil
}
