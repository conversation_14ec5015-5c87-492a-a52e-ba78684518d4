package anti_corruption_layer

//go:generate quicksilver-cli test interface ../anti-corruption-layer
//go:generate mockgen -destination=./mocks/anti_corruption_layer.go -package=mocks golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/anti-corruption-layer IMgr

import (
    "context"
    "encoding/json"
    "errors"
    "fmt"
    "gitlab.ttyuyin.com/tyr/x/compatible/proto"
    channel_msg_api "gitlab.ttyuyin.com/bizFund/bizFund/pkg/channel-msg-api"
    channelMic "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/clients/account"
    "golang.52tt.com/clients/channel"
    "golang.52tt.com/clients/channelol"
    imApi "golang.52tt.com/clients/im-api"
    push "golang.52tt.com/clients/push-notification/v2"
    sendIm "golang.52tt.com/clients/sendim"
    "golang.52tt.com/clients/seqgen/v2"
    timeline "golang.52tt.com/clients/timeline"
    user_online "golang.52tt.com/clients/user-online"
    userProfile "golang.52tt.com/clients/user-profile-api"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/app"
    channelPB "golang.52tt.com/protocol/app/channel"
    imPB "golang.52tt.com/protocol/app/im"
    gaPush "golang.52tt.com/protocol/app/push"
    syncPB "golang.52tt.com/protocol/app/sync"
    "golang.52tt.com/protocol/app/virtual_image_logic"
    pushPB "golang.52tt.com/protocol/services/push-notification/v2"
    sendim "golang.52tt.com/protocol/services/sendimsvr"
    timelinesvr "golang.52tt.com/protocol/services/timelinesvr"
    userOnlinePb "golang.52tt.com/protocol/services/user-online"
    virtual_image_mall "golang.52tt.com/protocol/services/virtual-image-mall"
    virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
    pb "golang.52tt.com/protocol/services/virtual-image-user"
    "golang.52tt.com/services/helper-from-cpp/immsghelper"
    "golang.52tt.com/services/notify"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/conf"
    "time"
)

const (
    UserPreViewPageJumpUrl = "tt://m.52tt.com/show_virtual_image_shop?show_preview=true"
)

type Mgr struct {
    bc                  conf.IBusinessConfManager
    shutDown            chan struct{}
    sendImCli           sendIm.IClient
    virtualImageCfgCli  virtual_image_resource.VirtualImageResourceClient
    virtualImageMallCli virtual_image_mall.VirtualImageMallClient

    seqgenCli      seqgen.IClient
    accountCli     account.IClient
    userProfileCli userProfile.IClient
    timelineCli    timeline.IClient
    imApiCli       imApi.IClient
    pushCli        push.IClient
    channelMicCli  channelMic.ChannelMicClient
    channelOlCli   channelol.IClient
    userOlCli      user_online.IClient
    channelMsgCli  channel_msg_api.IClient
    channelCli     channel.IClient
}

// NewMgr 活动配置模块
func NewMgr(bc conf.IBusinessConfManager) (*Mgr, error) {
    sendImCli := sendIm.NewClient()
    virtualImageCfgCli, err := virtual_image_resource.NewClient(context.Background())
    if err != nil {
        log.Errorf("NewMgr fail to virtual_image_resource.NewClient, err:%v", err)
        return nil, err
    }

    virtualImageMallCli, err := virtual_image_mall.NewClient(context.Background())
    if err != nil {
        log.Errorf("NewMgr fail to virtual_image_mall.NewClient, err:%v", err)
        return nil, err
    }

    seqGenClient := seqgen.NewIClient()
    accountCli := account.NewIClient()
    timelineCli := timeline.NewClient()
    userProfileCli, _ := userProfile.NewClient()
    imApiCli := imApi.NewIClient()
    pushCli, _ := push.NewClient()
    channelMicCli := channelMic.MustNewClient(context.Background())
    channelOlCli := channelol.NewClient()
    userOlCli := user_online.NewIClient()
    channelMsgCli, _ := channel_msg_api.NewIClient()
    channelCli := channel.NewIClient()

    m := &Mgr{
        bc:                  bc,
        shutDown:            make(chan struct{}),
        sendImCli:           sendImCli,
        seqgenCli:           seqGenClient,
        accountCli:          accountCli,
        userProfileCli:      userProfileCli,
        timelineCli:         timelineCli,
        virtualImageCfgCli:  virtualImageCfgCli,
        imApiCli:            imApiCli,
        pushCli:             pushCli,
        channelMicCli:       channelMicCli,
        channelOlCli:        channelOlCli,
        userOlCli:           userOlCli,
        channelMsgCli:       channelMsgCli,
        channelCli:          channelCli,
        virtualImageMallCli: virtualImageMallCli,
    }

    return m, nil
}

func (m *Mgr) Stop() {
    close(m.shutDown)
}

type UserMsg struct {
    Uid         uint32
    ServerMsgId uint64
    ExtMsg      []byte
    Content     string
}

// SendIMMsgWithHighLight 发送高亮带跳转的TT助手IM消息
// jumpUrl 和 highLight 为空时，发送普通文本消息
func (m *Mgr) SendIMMsgWithHighLight(ctx context.Context, uid uint32, content, highLight, jumpUrl string) error {
    contentInfo := &sendim.Content{
        Type: int32(sendim.Content_TextWithHighlightUrl),
        TextHlUrl: &sendim.ImTextWithHighlightUrl{
            Content:   content,
            Highlight: highLight,
            Url:       jumpUrl,
        },
    }

    if jumpUrl == "" && highLight == "" {
        contentInfo = &sendim.Content{
            Type: int32(sendim.Content_Text),
            TextNormal: &sendim.ImTextNormal{
                Content: content,
            },
        }
    }

    msg := &sendim.SendSyncReq{
        Sender: &sendim.Sender{
            Type: int32(sendim.Sender_User),
            Id:   10000,
        },
        Receiver: &sendim.Receiver{
            Type:   int32(sendim.Receiver_User),
            IdList: []uint32{uid},
        },
        Msg: &sendim.ImMsg{
            Content:     contentInfo,
            AppPlatform: "all",
            AppName:     "",
            ExpiredAt:   uint32(time.Now().Unix()) + 86400,
        },
        WithNotify: true,
    }

    _, err := m.sendImCli.SendSync(ctx, msg)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendIMMsgWithHighLight fail to SendSync. uid:%d, content %s , err: %s", uid, content, err.Error())
        return err
    }

    log.DebugWithCtx(ctx, "SendIMMsgWithHighLight success.uid:%d content %s", uid, content)
    return nil
}

func (m *Mgr) BatGetVirtualImageCfg(ctx context.Context, idList []uint32) ([]*virtual_image_resource.VirtualImageResourceInfo, error) {
    if len(idList) == 0 {
        return nil, nil
    }

    resp, err := m.virtualImageCfgCli.GetVirtualImageResourcesByIds(ctx, &virtual_image_resource.GetVirtualImageResourcesByIdsRequest{
        Ids: idList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "BatGetVirtualImageCfg fail to GetVirtualImageResourcesByIds. idList %v, err:%v", idList, err)
        return nil, err
    }

    return resp.GetResources(), nil
}

// SendBindInviteImMsg 发送绑定邀请IM消息
func (m *Mgr) SendBindInviteImMsg(ctx context.Context, from, to uint32, inviteId, pictureUrl string) (fromMsgId, toMsgId uint64, err error) {
    cmd := uint32(imPB.IM_MSG_TYPE_EXTENDED_MSG)

    contentMap := map[string]interface{}{
        "account":      "",
        "postContent":  "",
        "postType":     "0",
        "shareMsgType": "0",
        "imageUrl":     pictureUrl,
        "link":         fmt.Sprintf("tt://m.52tt.com/show_virtual_image_shop?invite_id=%s", inviteId),
        "shareType":    "2",
        "messageType":  "4",
        "content":      "我想和你绑定虚拟形象双人关系，展示双人形象！>>",
    }

    content, err := json.Marshal(contentMap)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendBindInviteImMsg fail to json.Marshal. content %s, err: %v", content, err)
        return
    }

    fromMsg := &UserMsg{
        Uid:     from,
        Content: string(content),
    }

    toMsg := &UserMsg{
        Uid:     to,
        Content: string(content),
    }

    return m.SendImMsg(ctx, cmd, fromMsg, toMsg)
}

// SendBindSuccessMsg 发送绑定成功助手消息
func (m *Mgr) SendBindSuccessMsg(ctx context.Context, uidA, uidB uint32) error {
    contentFormat := "恭喜！你和你的好友（%s）已成功绑定虚拟形象双人关系，快去搭配你们的专属双人形象吧>>"
    highLight := "快去搭配你们的专属双人形象吧>>"

    // 获取绑定人信息
    userMap, err := m.accountCli.GetUsersMap(ctx, []uint32{uidA, uidB})
    if err != nil {
        log.ErrorWithCtx(ctx, "SendBindSuccessMsg fail to BatGetUserByUid. uidA: %d, uidB: %d, err: %v", uidA, uidB, err)
        return err
    }

    if len(userMap) != 2 {
        log.ErrorWithCtx(ctx, "SendBindSuccessMsg fail to BatGetUserByUid. uidA: %d, uidB: %d, userMap: %v", uidA, uidB, userMap)
        return errors.New("SendBindSuccessMsg fail to BatGetUserByUid")
    }

    // 分别向用户A和用户B发送绑定成功助手消息
    _ = m.SendIMMsgWithHighLight(ctx, uidA, fmt.Sprintf(contentFormat, userMap[uidB].GetNickname()), highLight, UserPreViewPageJumpUrl)
    _ = m.SendIMMsgWithHighLight(ctx, uidB, fmt.Sprintf(contentFormat, userMap[uidA].GetNickname()), highLight, UserPreViewPageJumpUrl)
    return nil
}

// SendBindInviteRejectMsg 发送关系绑定拒绝消息
func (m *Mgr) SendBindInviteRejectMsg(ctx context.Context, uid, targetUid uint32) error {
    contentFormat := "你的好友（%s）未接受与你绑定虚拟形象双人关系，快去绑定其他人佩戴双人形象吧！>>"
    highLight := "快去绑定其他人佩戴双人形象吧！>>"

    // 获取绑定人信息
    userInfo, err := m.accountCli.GetUser(ctx, targetUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendBindInviteRejectMsg fail to GetUser. uid: %d,err: %v", targetUid, err)
        return err
    }

    return m.SendIMMsgWithHighLight(ctx, uid, fmt.Sprintf(contentFormat, userInfo.GetNickname()), highLight, UserPreViewPageJumpUrl)
}

// SendUnbindMsg 发送解绑消息
func (m *Mgr) SendUnbindMsg(ctx context.Context, toUid, bindUid uint32) error {
    contentFormat := "您与%s的虚拟形象双人关系绑定已经解除，快去绑定其他人佩戴双人形象吧！>>"
    highLight := "快去绑定其他人佩戴双人形象吧！>>"

    userMap, err := m.accountCli.GetUsersMap(ctx, []uint32{bindUid})
    if err != nil {
        log.ErrorWithCtx(ctx, "SendUnbindMsg fail to GetUsersMap. uid: %d, err: %v", bindUid, err)
        return err
    }

    if len(userMap) != 1 {
        log.ErrorWithCtx(ctx, "SendUnbindMsg fail to GetUsersMap. uid: %d, err: %v", bindUid, err)
        return errors.New("SendUnbindMsg fail to BatGetUserByUid")
    }

    _ = m.SendIMMsgWithHighLight(ctx, toUid, fmt.Sprintf(contentFormat, userMap[bindUid].GetNickname()), highLight, UserPreViewPageJumpUrl)
    return nil
}

// SendImMsg 发送IM消息
func (m *Mgr) SendImMsg(ctx context.Context, cmd uint32, from, to *UserMsg) (fromMsgId, toMsgId uint64, err error) {
    if from == nil || to == nil {
        return
    }

    if from.ServerMsgId == 0 {
        svrMsgID, err := m.seqgenCli.GenerateSequence(ctx, from.Uid, seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
        if err != nil {
            log.ErrorWithCtx(ctx, "SendImMsg - Failed to GenerateSequence svr msg id: %v", err)
            return 0, 0, err
        }
        from.ServerMsgId = svrMsgID
    }
    if to.ServerMsgId == 0 {
        svrMsgID, err := m.seqgenCli.GenerateSequence(ctx, to.Uid, seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
        if err != nil {
            log.ErrorWithCtx(ctx, "SendImMsg - Failed to GenerateSequence svr msg id: %v", err)
            return 0, 0, err
        }
        to.ServerMsgId = svrMsgID
    }

    userMap, err := m.accountCli.BatGetUserByUid(ctx, from.Uid, to.Uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendImMsg - WriteMsgToUidWithId fail , from: %+v, to:%+v, err: %v", from, to, err)
        return 0, 0, err
    }

    fromUser, toUser := userMap[from.Uid], userMap[to.Uid]

    msg := &timelinesvr.ImMsg{
        FromId:          fromUser.GetUid(),
        ToId:            toUser.GetUid(),
        FromName:        fromUser.GetUsername(),
        ToName:          toUser.GetUsername(),
        FromNick:        fromUser.GetNickname(),
        ToNick:          toUser.GetNickname(),
        Content:         "",
        Type:            cmd,
        ClientMsgTime:   uint32(time.Now().Unix()),
        Status:          uint32(syncPB.NewMessageSync_UN_READ),
        ServerMsgTime:   uint32(time.Now().Unix()),
        HasAttachment:   false,
        MsgSourceType:   uint32(imPB.MsgSourceType_NORMAL_SOURCE),
        Platform:        uint32(timelinesvr.Platform_UNSPECIFIED), // 没有平台限制必须显式指定
        MsgExposureFlag: uint32(syncPB.NewMessageSync_FLAG_ON),
        MsgRedpointFlag: uint32(syncPB.NewMessageSync_FLAG_ON),
    }

    msg.ServerMsgId = uint32(from.ServerMsgId)
    msg.TargetMsgId = uint32(to.ServerMsgId)
    msg.Content = from.Content

    imErr := immsghelper.WriteMsgToUidWithId(ctx, fromUser.GetUid(), msg, m.seqgenCli, m.timelineCli)
    if imErr != nil {
        log.ErrorWithCtx(ctx, "SendImMsg - WriteMsgToUidWithId fail , msg: %v , err: %v", msg, imErr)
        return 0, 0, imErr
    }

    msg.ServerMsgId = uint32(to.ServerMsgId)
    msg.TargetMsgId = uint32(from.ServerMsgId)
    msg.Content = to.Content

    imErr = immsghelper.WriteMsgToUidWithId(ctx, toUser.GetUid(), msg, m.seqgenCli, m.timelineCli)
    if imErr != nil {
        log.ErrorWithCtx(ctx, "SendImMsg - WriteMsgToUidWithId fail , msg: %v , err: %v", msg, imErr)
        return 0, 0, imErr
    }

    imErr = notify.NotifySyncX(ctx, []uint32{fromUser.GetUid(), toUser.GetUid()}, notify.ImMsg)
    if imErr != nil {
        log.ErrorWithCtx(ctx, "SendImMsg - NotifySyncX fail , msg: %v , err: %v", msg, imErr)
    }

    log.InfoWithCtx(ctx, "SendImMsg success, msg:%+v", msg)

    return from.ServerMsgId, to.ServerMsgId, nil
}

// PushChannelUserVIChange 推送房间用户虚拟形象变更
func (m *Mgr) PushChannelUserVIChange(ctx context.Context, cid uint32, info *pb.UserInuseItemInfo, checkUkw bool) error {
    if info == nil || info.GetUid() == 0 {
        return nil
    }

    var err error
    user, err := m.userProfileCli.GetUserProfileV2(ctx, info.GetUid(), true)
    if err != nil {
        log.ErrorWithCtx(ctx, "PushChannelUserVIChange fail to GetUserProfileV2. uid: %d, err: %v", info.GetUid(), err)
        return err
    }

    if cid == 0 {
        channelOlInfo, err := m.channelOlCli.GetUsersChannelId(ctx, info.GetUid(), info.GetUid())
        if err != nil {
            log.ErrorWithCtx(ctx, "PushChannelUserVIChange fail to GetUsersChannelId. uid: %d, err: %v", info.GetUid(), err)
            return err
        }

        cid = channelOlInfo.GetChannelId()
    }
    if cid == 0 {
        log.DebugWithCtx(ctx, "PushChannelUserVIChange uid:%d not in channel", info.GetUid())
        return nil
    }

    channelResp, err := m.channelCli.GetChannelSimpleInfo(ctx, info.GetUid(), cid)
    if err != nil {
        log.ErrorWithCtx(ctx, "PushChannelUserVIChange fail to GetChannelSimpleInfo. cid: %d, err: %v", cid, err)
        return err
    }
    channelTy := channelResp.GetChannelType()

    if checkUkw {
        channelSupportYKW := false
        if channelTy == uint32(channelPB.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) ||
            channelTy == uint32(channelPB.ChannelType_RADIO_LIVE_CHANNEL_TYPE) ||
            channelTy == uint32(channelPB.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {

            channelSupportYKW = true
        }

        if channelSupportYKW && user.GetPrivilege().GetType() == uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) {
            // 神秘人不推送外显数据
            log.DebugWithCtx(ctx, "PushChannelUserVIChange uid:%d is UKW", info.GetUid())
            return nil
        }
    }

    micInfo, err := m.channelMicCli.GetMicrList(ctx, &channelMic.GetMicrListReq{
        ChannelId: cid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "PushChannelUserVIChange fail to GetMicrList. cid: %d, uid: %d, err: %v", cid, info.GetUid(), err)
        return err
    }

    var inMic bool
    for _, mic := range micInfo.GetAllMicList() {
        if mic.GetMicUid() == info.GetUid() {
            inMic = true
            break
        }
    }

    if !inMic {
        log.DebugWithCtx(ctx, "PushChannelUserVIChange uid:%d not in mic", info.GetUid())
        return nil
    }

    var hideSpecialPose bool
    if channelTy == uint32(channelPB.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) &&
        micInfo.GetMicrMode() == uint32(channelPB.EChannelMicMode_DATING_MIC_SPACE_MODE) {
        hideSpecialPose = true
    }

    var micPoseCfgMap map[uint32]uint32
    if len(info.GetItems()) > 0 {
        // 替换姿势组件id
        actCfgMapResp, err := m.virtualImageCfgCli.GetActionResourceMap(ctx, &virtual_image_resource.GetActionResourceMapRequest{})
        if err != nil {
            log.WarnWithCtx(ctx, "PushChannelUserVIChange fail to GetActionResourceMap. err: %v", err)
        }
        micPoseCfgMap = actCfgMapResp.GetActionResourceMap()

        defaultResourceResp, err := m.virtualImageCfgCli.GetDefaultResourceList(ctx, &virtual_image_resource.GetDefaultResourceListRequest{})
        if err != nil {
            log.WarnWithCtx(ctx, "PushChannelUserVIChange fail to GetDefaultResourceList. err:%v", err)
        }

        sitSubCateType := uint32(virtual_image_resource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SITTING_POSE)
        maleDefaultSitCfgId := defaultResourceResp.GetMaleAnimationMap()[sitSubCateType]
        femaleDefaultSitCfgId := defaultResourceResp.GetFemaleAnimationMap()[sitSubCateType]

        hasSitItem := false
        for _, item := range info.GetItems() {
            if item.GetSubCategory() == sitSubCateType && item.GetCfgId() > 0 {
                hasSitItem = true // 已佩戴坐姿动作
            }
        }

        if !hasSitItem {
            var defaultSitCfgId uint32
            if user.GetSex() == uint32(account.Male) {
                defaultSitCfgId = maleDefaultSitCfgId
            } else {
                defaultSitCfgId = femaleDefaultSitCfgId
            }

            // 未佩戴坐姿动作，添加默认坐姿动作
            info.Items = append(info.Items, &pb.InuseItemInfo{
                CfgId: defaultSitCfgId, SubCategory: sitSubCateType,
            })
        }
    }

    items := make([]*virtual_image_logic.UserVirtualImageItem, 0)
    for _, item := range info.GetItems() {
        if item.GetSubCategory() == uint32(virtual_image_resource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TEZI) &&
            hideSpecialPose {
            // 隐藏特姿
            continue
        }

        cfgId := item.GetCfgId()
        if actCfgId, ok := micPoseCfgMap[item.GetCfgId()]; ok {
            cfgId = actCfgId
        }
        if cfgId == 0 {
            continue
        }

        items = append(items, &virtual_image_logic.UserVirtualImageItem{
            ResourceId: cfgId,
            ExpireTs:   item.GetExpireTime(),
            Inuse:      true,
        })
    }

    opt := &virtual_image_logic.UserVirtualImageChangeOpt{
        Uid:         info.GetUid(),
        Cid:         cid,
        Orientation: info.GetOrientation(),
        Items:       items,
    }

    data, e := proto.Marshal(opt)
    if e != nil {
        log.ErrorWithCtx(ctx, "PushChannelUserVIChange marshal err:%v, %+v", e, opt)
        return e
    }

    log.DebugWithCtx(ctx, "PushChannelUserVIChange uid:%d, cid:%d, hideSpecialPose:%v", info.GetUid(), cid, hideSpecialPose)

    // 兼容旧版本
    if m.bc.GetNeedOldChangePush() {
        err := m.pushChannelBroMsgToChannels(ctx, []uint32{cid}, &channelPB.ChannelBroadcastMsg{
            Time:         uint64(time.Now().Unix()),
            ToChannelId:  cid,
            Type:         uint32(channelPB.ChannelMsgType_CHANNEL_USER_MIC_VIRTUAL_IMAGE_CHANGE),
            PbOptContent: data,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "PushChannelUserVIChange pushChannelBroMsgToChannels uid: %d, cid: %d, err: %v", info.GetUid(), cid, err)
        }
    }

    // 可靠房间消息推送
    _, _, sErr := m.channelMsgCli.ReliablePushToChannel(ctx, info.GetUid(), cid, uint32(channelPB.ChannelMsgType_CHANNEL_USER_MIC_VIRTUAL_IMAGE_CHANGE_V2), "", data)
    if sErr != nil {
        log.ErrorWithCtx(ctx, "PushChannelUserVIChange fail to ReliablePushToChannel. uid: %d, cid: %d, err: %v", info.GetUid(), cid, sErr)
        return sErr
    }

    return nil
}

// 房间广播消息
func (m *Mgr) pushChannelBroMsgToChannels(ctx context.Context, channelIds []uint32, msg *channelPB.ChannelBroadcastMsg) error {
    channelMsgBin, e := msg.Marshal()
    if e != nil {
        log.ErrorWithCtx(ctx, "pushChannelBroMsgToChannels marshal err: %v, %+v", e, msg)
        return e
    }

    pushMessage := &gaPush.PushMessage{
        Cmd:     uint32(gaPush.PushMessage_CHANNEL_MSG_BRO),
        Content: channelMsgBin,
        SeqId:   uint32(time.Now().Unix()),
    }
    pushMessageBytes, e := pushMessage.Marshal()
    if e != nil {
        log.ErrorWithCtx(ctx, "pushChannelBroMsgToChannels Marshal channelIds:%v, err: %v", channelIds, e)
        return e
    }

    notification := &pushPB.CompositiveNotification{
        Sequence: uint32(time.Now().Unix()),
        TerminalTypeList: []uint32{
            protocol.MobileAndroidTT,
            protocol.MobileIPhoneTT,
        },
        TerminalTypePolicy: push.DefaultPolicy,
        AppId:              uint32(protocol.TT),
        ProxyNotification: &pushPB.ProxyNotification{
            Type:       uint32(pushPB.ProxyNotification_PUSH),
            Payload:    pushMessageBytes,
            Policy:     pushPB.ProxyNotification_DEFAULT,
            ExpireTime: 60,
        },
    }

    multicastMap := map[uint64]string{}
    for _, channelId := range channelIds {
        if channelId == 0 {
            continue
        }
        multicastMap[uint64(channelId)] = fmt.Sprintf("%d@channel", channelId)
    }

    if len(multicastMap) == 0 {
        return nil
    }

    err := m.pushCli.PushMulticasts(ctx, multicastMap, []uint32{}, notification)
    if err != nil {
        log.ErrorWithCtx(ctx, "pushChannelBroMsgToChannels fail to PushMulticasts channelIds:%v, err: %s", channelIds, err.Error())
        return err
    }

    return nil
}

// PushUserDisplayStatusReminderPop 推送用户外显状态弹窗
func (m *Mgr) PushUserDisplayStatusReminderPop(ctx context.Context, opt *virtual_image_logic.VirtualImageDisplayStatusPop) error {
    if opt == nil || opt.GetUid() == 0 {
        log.WarnWithCtx(ctx, "PushUserDisplayStatusReminderPop opt is nil or uid is 0, opt:%v", opt)
        return nil
    }

    msg, _ := proto.Marshal(opt)
    err := m.PushUserMsg(ctx, msg, uint32(gaPush.PushMessage_VIRTUAL_IMAGE_DISPLAY_STATUS_NOTIFY_POP), []uint32{opt.GetUid()})
    if err != nil {
        log.ErrorWithCtx(ctx, "PushUserDisplayStatusReminderPop err:%v opt:%v", err, opt)
        return err
    }
    return nil
}

// PushUserDisplayStatusReminderChannelIm 推送用户外显状态房间公屏
func (m *Mgr) PushUserDisplayStatusReminderChannelIm(ctx context.Context, opt *virtual_image_logic.VirtualImageDisplayStatusChannelNotify) error {
    if opt == nil || opt.GetUid() == 0 {
        log.WarnWithCtx(ctx, "PushUserDisplayStatusReminderChannelIm opt is nil or uid is 0, opt:%v", opt)
        return nil
    }

    if opt.GetCid() == 0 {
        channelInfo, err := m.channelOlCli.GetUsersChannelId(ctx, opt.GetUid(), opt.GetUid())
        if err != nil {
            log.ErrorWithCtx(ctx, "PushUserDisplayStatusReminderChannelIm fail to GetUsersChannelId. uid: %d, err: %v", opt.GetUid(), err)
            return err
        }

        opt.Cid = channelInfo.GetChannelId()
    }

    msg, _ := proto.Marshal(opt)
    err := m.PushUserMsg(ctx, msg, uint32(gaPush.PushMessage_VIRTUAL_IMAGE_DISPLAY_STATUS_CHANNEL_IM), []uint32{opt.GetUid()})
    if err != nil {
        log.ErrorWithCtx(ctx, "PushUserDisplayStatusReminderChannelIm err:%v opt:%v", err, opt)
        return err
    }
    return nil
}

// PushUserMsg 推送用户消息
func (m *Mgr) PushUserMsg(ctx context.Context, msg []byte, cmd uint32, uidList []uint32) error {
    pushMessage := &gaPush.PushMessage{
        Cmd:     cmd,
        Content: msg,
    }
    pushMessageBytes, _ := proto.Marshal(pushMessage)

    terminalList := []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT, protocol.WindowsTT}

    notification := &pushPB.CompositiveNotification{
        Sequence:           uint32(time.Now().Unix()),
        TerminalTypeList:   terminalList,
        TerminalTypePolicy: push.DefaultPolicy,
        AppId:              0,
        ProxyNotification: &pushPB.ProxyNotification{
            Type:    uint32(pushPB.ProxyNotification_PUSH),
            Payload: pushMessageBytes,
        },
    }

    err := m.pushCli.PushToUsers(ctx, uidList, notification)
    if err != nil {
        log.ErrorWithCtx(ctx, "PushUserMsg err:%v cmd:%v uids:%v", err, cmd, uidList)
    }
    return err
}

func (m *Mgr) CheckUserOnline(ctx context.Context, uid uint32) (bool, error) {
    onlineInfo, err := m.userOlCli.GetLatestOnlineInfo(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckUserOnline fail to GetLatestOnlineInfo. uid: %d, err: %v", uid, err)
        return false, err
    }

    isOnline := onlineInfo.GetOnlineType() == userOnlinePb.OnlineType_ONLINE_TYPE_ONLINE
    return isOnline, nil
}

// GetCommodityList 获取商品信息
func (m *Mgr) GetCommodityList(ctx context.Context, gainTy uint32, resourceIdList []uint32) ([]*virtual_image_mall.CommodityData, error) {
    resp, err := m.virtualImageMallCli.GetCommodityDataList(ctx, &virtual_image_mall.GetCommodityDataListRequest{
        GainPath:       gainTy,
        ResourceIdList: resourceIdList,
        ShelfStatus:    uint32(virtual_image_mall.ShelfStatus_SHELF_STATUS_NOW),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetCommodityList fail to GetCommodityDataList. gainTy: %d, resourceIdList:%v, err: %v", gainTy, resourceIdList, err)
        return nil, err
    }

    return resp.GetCommodityDataList(), nil
}

// GetSuitResourceList 获取套装信息
func (m *Mgr) GetSuitResourceList(ctx context.Context, suitIds []string) (map[string]*virtual_image_resource.VirtualImageResourceSuitInfo, error) {
    suitId2Info := make(map[string]*virtual_image_resource.VirtualImageResourceSuitInfo)
    if len(suitIds) == 0 {
        return suitId2Info, nil
    }

    resp, err := m.virtualImageCfgCli.GetSuitResourceList(ctx, &virtual_image_resource.GetSuitResourceListRequest{
        SuitList: suitIds,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetSuitResourceList fail to GetSuitResourceList. suitIds:%v, err: %v", suitIds, err)
        return suitId2Info, err
    }

    for _, info := range resp.GetResources() {
        suitId2Info[info.GetSuit()] = info
    }

    return suitId2Info, nil
}
