package cache

import (
    "context"
    "encoding/json"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "golang.52tt.com/pkg/log"
    "strconv"
    "time"
)

//go:generate quicksilver-cli test interface ../cache
//go:generate mockgen -destination=../mocks/cache.go -package=mocks golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/reminder/cache ICache

type Cache struct {
    cmder redis.Cmdable
}

func NewCache(client redis.Cmdable) *Cache {
    c := &Cache{
        cmder: client,
    }
    return c
}

func (c *Cache) Close() error {
    return c.cmder.(redis.Client).Close()
}

func (c *Cache) GetRedisClient() redis.Cmdable {
    return c.cmder
}

func genUserLoginDisplayReminderKey(uid uint32) string {
    return fmt.Sprintf("virtual_image_user_login_reminder:%d", uid)
}

func genUserChannelImDisplayReminderKey(uid uint32) string {
    return fmt.Sprintf("virtual_image_user_channel_im_reminder:%d", uid)
}

func genDelayDisplayReminderPoolKey() string {
    return fmt.Sprintf("virtual_image_user_delay_reminder_pool")
}

func genUserInChannelPoolKey() string {
    return fmt.Sprintf("virtual_image_user_in_channel_pool")
}

func (c *Cache) SetUserLoginDisplayReminder(ctx context.Context, uid uint32) error {
    key := genUserLoginDisplayReminderKey(uid)
    err := c.cmder.Set(ctx, key, "1", 30*24*time.Hour).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserLoginDisplayReminder fail to Set. uid:%d, err:%v", uid, err)
        return err
    }

    return nil
}

func (c *Cache) GetUserLoginDisplayReminder(ctx context.Context, uid uint32) (bool, error) {
    key := genUserLoginDisplayReminderKey(uid)
    res, err := c.cmder.Get(ctx, key).Result()
    if err != nil {
        if redis.IsNil(err) {
            return false, nil
        }
        log.ErrorWithCtx(ctx, "GetUserLoginDisplayReminder fail to Get. uid:%d, err:%v", uid, err)
        return false, err
    }

    return len(res) > 0, nil
}

func (c *Cache) RemoveUserLoginDisplayReminder(ctx context.Context, uid uint32) error {
    key := genUserLoginDisplayReminderKey(uid)
    err := c.cmder.Del(ctx, key).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "RemoveUserLoginDisplayReminder fail to Del. uid:%d, err:%v", uid, err)
        return err
    }

    return nil
}

type UserChannelImReminder struct {
    LastTime int64 `json:"LastTime"`
    Count    uint32 `json:"Count"`
    TotalCnt uint32 `json:"TotalCnt"`
}

func (c *Cache) SetUserChannelImDisplayReminder(ctx context.Context, uid uint32, info *UserChannelImReminder) error {
    key := genUserChannelImDisplayReminderKey(uid)
    b, err := json.Marshal(info)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserChannelImDisplayReminder fail to json.Marshal. uid:%d, err:%v", uid, err)
        return err
    }

    err = c.cmder.Set(ctx, key, b, 30*24*time.Hour).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserChannelImDisplayReminder fail to Set. uid:%d, err:%v", uid, err)
        return err
    }

    return nil
}

func (c *Cache) GetUserChannelImDisplayReminder(ctx context.Context, uid uint32) (*UserChannelImReminder, bool, error) {
    var info UserChannelImReminder
    key := genUserChannelImDisplayReminderKey(uid)
    b, err := c.cmder.Get(ctx, key).Bytes()
    if err != nil {
        if redis.IsNil(err) {
            return &info, false, nil
        }
        log.ErrorWithCtx(ctx, "GetUserChannelImDisplayReminder fail to Get. uid:%d, err:%v", uid, err)
        return &info, false, err
    }

    if err := json.Unmarshal(b, &info); err != nil {
        log.ErrorWithCtx(ctx, "GetUserChannelImDisplayReminder fail to json.Unmarshal. uid:%d, err:%v", uid, err)
        return &info, false, err
    }

    return &info, true, nil
}

func (c *Cache) RemoveUserChannelImDisplayReminder(ctx context.Context, uid uint32) error {
    key := genUserChannelImDisplayReminderKey(uid)
    err := c.cmder.Del(ctx, key).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "RemoveUserChannelImDisplayReminder fail to Del. uid:%d, err:%v", uid, err)
        return err
    }

    return nil
}

func (c *Cache) AddUserDelayDisplayReminder(ctx context.Context, uid uint32, expireTs int64) error {
    key := genDelayDisplayReminderPoolKey()
    err := c.cmder.ZAddNX(ctx, key, &redis.Z{Score: float64(expireTs), Member: fmt.Sprint(uid)}).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "AddUserDelayDisplayReminder fail to ZAdd. uid:%d, err:%v", uid, err)
        return err
    }

    return nil
}

func (c *Cache) GetDelayDisplayReminderUidList(ctx context.Context, begin, end int64) ([]uint32, error) {
    uidList := make([]uint32, 0)
    key := genDelayDisplayReminderPoolKey()
    res, err := c.cmder.ZRangeByScore(ctx, key, &redis.ZRangeBy{
        Min:    fmt.Sprintf("%d", begin),
        Max:    fmt.Sprintf("%d", end),
        Offset: 0,
        Count:  1000,
    }).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "GetDelayDisplayReminderUidList fail to ZRangeByScore. err:%v", err)
        return uidList, err
    }

    if len(res) == 0 {
        return uidList, nil
    }

    for _, str := range res {
        uid, _ := strconv.ParseUint(str, 10, 32)
        uidList = append(uidList, uint32(uid))
    }

    return uidList, nil
}

func (c *Cache) RemoveDelayDisplayReminderByTs(ctx context.Context, end int64) error {
    key := genDelayDisplayReminderPoolKey()
    err := c.cmder.ZRemRangeByScore(ctx, key, "0", fmt.Sprintf("%d", end)).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "RemoveDelayDisplayReminderByTs fail to ZRemRangeByScore. err:%v", err)
        return err
    }

    return nil
}

func (c *Cache) RemoveDelayDisplayReminder(ctx context.Context, uid uint32) error {
    key := genDelayDisplayReminderPoolKey()
    err := c.cmder.ZRem(ctx, key, uid).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "RemoveDelayDisplayReminder fail to ZRem. uid:%d, err:%v", uid, err)
        return err
    }

    return nil
}

func (c *Cache) AddUserInChannelPool(ctx context.Context, uid uint32, expireTs int64) error {
    key := genUserInChannelPoolKey()
    err := c.cmder.ZAdd(ctx, key, &redis.Z{Score: float64(expireTs), Member: fmt.Sprint(uid)}).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "AddUserInChannelPool fail to ZAdd. uid:%d, err:%v", uid, err)
        return err
    }

    return nil
}

func (c *Cache) GetUserInChannelPool(ctx context.Context, begin, end int64) ([]uint32, error) {
    uidList := make([]uint32, 0)
    key := genUserInChannelPoolKey()
    res, err := c.cmder.ZRangeByScore(ctx, key, &redis.ZRangeBy{
        Min:    fmt.Sprintf("%d", begin),
        Max:    fmt.Sprintf("%d", end),
        Offset: 0,
        Count:  1000,
    }).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserInChannelPool fail to ZRangeByScore. err:%v", err)
        return uidList, err
    }

    if len(res) == 0 {
        return uidList, nil
    }

    for _, str := range res {
        uid, _ := strconv.ParseUint(str, 10, 32)
        uidList = append(uidList, uint32(uid))
    }

    c.cmder.ZRemRangeByScore(ctx, key, "0", fmt.Sprintf("%d", end))
    return uidList, nil
}

func (c *Cache) RemoveUserInChannelPool(ctx context.Context, uid uint32) error {
    key := genUserInChannelPoolKey()
    err := c.cmder.ZRem(ctx, key, uid).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "RemoveUserInChannelPool fail to ZRem. uid:%d, err:%v", uid, err)
        return err
    }

    return nil
}
