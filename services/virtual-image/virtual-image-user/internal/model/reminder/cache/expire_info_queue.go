package cache

import (
    "context"
    "encoding/json"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
)

const (
    QueueTypeExpired = iota + 1
    QueueTypeSoonExpired
)

type ExpireInfo struct {
    Uid      uint32  `json:"uid"`
    ItemIds  []uint32 `json:"item_ids"`
    ExpireTs int64   `json:"expire_ts"`
}

func getExpireInfoKey(ty uint32) string {
    return fmt.Sprintf("virtual_image_user_expire_info_queue:%d", ty)
}

// LPushExpireInfo 在队列中插入过期信息
func (c *Cache) LPushExpireInfo(ctx context.Context, ty uint32, list []*ExpireInfo) error {
    key := getExpireInfoKey(ty)
    values := make([]interface{}, 0, len(list))
    for _, info := range list {
        b, err := json.Marshal(info)
        if err != nil {
            log.WarnWithCtx(ctx, "LPushExpireInfo fail to json.Marshal. ty:%d, info:%+v, err:%v", ty, info, err)
            continue
        }
        values = append(values, b)
    }

    if len(values) == 0 {
        return nil
    }

    err := c.cmder.LPush(ctx, key, values...).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "LPushExpireInfo fail to LPush. ty:%d, len(list):%d, err:%v", ty, len(list), err)
        return err
    }

    return nil
}

// RPopExpireInfo 从队列中获取过期信息
func (c *Cache) RPopExpireInfo(ctx context.Context, ty uint32) (*ExpireInfo, error) {
    key := getExpireInfoKey(ty)
    b, err := c.cmder.RPop(ctx, key).Bytes()
    if err != nil {
        if redis.IsNil(err) {
            return nil, nil
        }
        log.ErrorWithCtx(ctx, "RPopExpireInfo fail to RPop. ty:%d, err:%v", ty, err)
        return nil, err
    }

    var info ExpireInfo
    if err := json.Unmarshal(b, &info); err != nil {
        log.ErrorWithCtx(ctx, "RPopExpireInfo fail to json.Unmarshal. ty:%d, err:%v", ty, err)
        return nil, err
    }

    return &info, nil
}
