package reminder

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/protocol/app/virtual_image_logic"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/reminder/cache"
    "time"
)

func (m *Mgr) startTimer() error {
    var err error
    m.timerD, err = timer.NewTimerD(context.Background(),
        "virtual-image-user-reminder",
        timer.WithV8RedisCmdable(m.cache.GetRedisClient()))
    if err != nil {
        log.Errorf("startTimer NewTimerD err:%v", err)
        return err
    }

    // 处理过期的虚拟形象
    if err = m.timerD.AddCronTask("0 * * * * *", "handleVAExpired", tasks.FuncTask(func(ctx context.Context) {
        m.handleVAExpired()
    })); err != nil {
        log.Errorf("startTimer AddCronTask handleVAExpired err:%v", err)
        return err
    }

    // 处理1天后过期的虚拟形象
    if err = m.timerD.AddCronTask("0 * * * * *", "handleVAExpireInOneDay", tasks.FuncTask(func(ctx context.Context) {
        m.handleVAExpireInNDays(1)
    })); err != nil {
        log.Errorf("startTimer AddCronTask handleVAExpireInOneDay err:%v", err)
        return err
    }

    // 处理3天后过期的虚拟形象
    if err = m.timerD.AddCronTask("0 * * * * *", "handleVAExpireInThreeDays", tasks.FuncTask(func(ctx context.Context) {
        m.handleVAExpireInNDays(3)
    })); err != nil {
        log.Errorf("startTimer AddCronTask handleVAExpireInThreeDays err:%v", err)
        return err
    }

    // 消费任务-处理过期的虚拟形象
    m.timerD.AddLocalIntervalTask(50*time.Millisecond, tasks.FuncTask(func(ctx context.Context) {
        m.consumeExpireVATask(cache.QueueTypeExpired)
    }))

    // 消费任务-处理即将过期的虚拟形象
    m.timerD.AddLocalIntervalTask(50*time.Millisecond, tasks.FuncTask(func(ctx context.Context) {
        m.consumeExpireVATask(cache.QueueTypeSoonExpired)
    }))

    // 在房间内的用户外显状态提醒
    if err = m.timerD.AddCronTask("* * * * * *", "channelImDisplayReminderTimeout", tasks.FuncTask(func(ctx context.Context) {
        m.channelImDisplayReminderTimeout(ctx)
    })); err != nil {
        log.Errorf("startTimer AddCronTask channelImDisplayReminderTimeout err:%v", err)
        return err
    }

    // 消费任务-处理外显状态房间公屏提醒
    m.timerD.AddLocalIntervalTask(50*time.Millisecond, tasks.FuncTask(func(ctx context.Context) {
        m.consumeChannelImDisplayReminder(ctx)
    }))

    // 3天后提醒用户无法外显状态
    if err = m.timerD.AddCronTask("* * * * * *", "delayDisplayReminderTimeoutInThreeDays", tasks.FuncTask(func(ctx context.Context) {
        var sec int64
        info := m.bc.GetDisplayReminder()
        if info == nil {
            sec = 3 * 24 * 3600
        } else {
            sec = info.ShortDelaySec
        }
        m.delayDisplayReminderTimeout(ctx, sec, false)
    })); err != nil {
        log.Errorf("startTimer AddCronTask delayDisplayReminderTimeoutInThreeDays err:%v", err)
        return err
    }

    // 7天后提醒用户无法外显状态
    if err = m.timerD.AddCronTask("* * * * * *", "delayDisplayReminderTimeoutInSevenDays", tasks.FuncTask(func(ctx context.Context) {
        var sec int64
        info := m.bc.GetDisplayReminder()
        if info == nil {
            sec = 7 * 24 * 3600
        } else {
            sec = info.LongDelaySec
        }
        m.delayDisplayReminderTimeout(ctx, sec, true)
    })); err != nil {
        log.Errorf("startTimer AddCronTask delayDisplayReminderTimeoutInSevenDays err:%v", err)
        return err
    }

    // 消费任务-处理延时外显状态提醒
    m.timerD.AddLocalIntervalTask(50*time.Millisecond, tasks.FuncTask(func(ctx context.Context) {
        m.consumeDelayDisplayReminder(ctx)
    }))

    m.timerD.Start()
    return nil
}

// handleVAExpired 处理已经过期的虚拟形象
func (m *Mgr) handleVAExpired() {
    now := time.Now()
    endTime := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), 0, 0, now.Location())
    startTime := endTime.Add(-time.Minute)

    m.produceExpireVATask(startTime, endTime, cache.QueueTypeExpired)
}

// handleVAExpireInNDays 处理N天后过期的虚拟形象
func (m *Mgr) handleVAExpireInNDays(n uint32) {
    now := time.Now()
    startTime := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), 0, 0, now.Location()).
        AddDate(0, 0, int(n))
    endTime := startTime.Add(time.Minute)

    m.produceExpireVATask(startTime, endTime, cache.QueueTypeSoonExpired)
}

// produceExpireVATask 生成过期的虚拟形象处理任务
func (m *Mgr) produceExpireVATask(startTime, endTime time.Time, taskTy uint32) {
    ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
    defer cancel()

    uid2ts2Info := make(map[uint32]map[int64]*cache.ExpireInfo)
    list, err := m.userVA.GetVirtualImageListByExpireTime(ctx, startTime, endTime)
    if err != nil {
        log.WarnWithCtx(ctx, "produceExpireVATask GetVirtualImageListByExpireTime err: %v, startTime: %s, endTime: %s", err, startTime, endTime)
        return
    }

    for _, info := range list {
        if _, ok := uid2ts2Info[info.Uid]; !ok {
            uid2ts2Info[info.Uid] = make(map[int64]*cache.ExpireInfo)
        }

        if _, ok := uid2ts2Info[info.Uid][info.ExpireTime.Unix()]; !ok {
            uid2ts2Info[info.Uid][info.ExpireTime.Unix()] = &cache.ExpireInfo{
                Uid:      info.Uid,
                ItemIds:  make([]uint32, 0),
                ExpireTs: info.ExpireTime.Unix(),
            }
        }
        v := uid2ts2Info[info.Uid][info.ExpireTime.Unix()]
        v.ItemIds = append(v.ItemIds, info.VAId)
    }

    expireList := make([]*cache.ExpireInfo, 0, len(uid2ts2Info))
    for _, ts2Info := range uid2ts2Info {
        for _, info := range ts2Info {
            expireList = append(expireList, info)
        }
    }

    err = m.cache.LPushExpireInfo(ctx, taskTy, expireList)
    if err != nil {
        log.ErrorWithCtx(ctx, "produceExpireVATask LPushExpireInfo err:%v", err)
    }
}

// 消费处理过期的虚拟形象任务
func (m *Mgr) consumeExpireVATask(ty uint32) {
    ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
    defer cancel()

    info, err := m.cache.RPopExpireInfo(ctx, ty)
    if err != nil {
        log.ErrorWithCtx(ctx, "consumeExpireVATask fail to RPopExpireInfo. ty:%d, err:%v", ty, err)
        return
    }

    if info == nil {
        return
    }

    // tt助手消息
    content, err := m.genExpireIMMsgContent(ctx, info)
    if err != nil {
        log.ErrorWithCtx(ctx, "consumeExpireVATask fail to genExpireIMMsgContent. ty:%d, err:%v", ty, err)
        return
    }

    if content != "" {
        content += " 点击前往＞"
        err = m.acLayer.SendIMMsgWithHighLight(ctx, info.Uid, content, "点击前往＞", "tt://m.52tt.com/show_virtual_image_shop")
        if err != nil {
            log.ErrorWithCtx(ctx, "consumeExpireVATask fail to SendIMMsgWithHighLight. ty:%d, %+v, err:%v", ty, info, err)
        }
    }

    if ty == cache.QueueTypeExpired {
        go func() {
            ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
            defer cancel()

            useMap, err := m.userVA.GetUserInUseMap(ctx, info.Uid)
            if err != nil {
                log.ErrorWithCtx(ctx, "consumeExpireVATask fail to GetUserInUseMap. ty:%d, %+v, err:%v", ty, info, err)
                return
            }

            items := make([]*virtual_image_user.ItemInfo, 0, len(useMap))
            for cfgId, info := range useMap {
                items = append(items, &virtual_image_user.ItemInfo{
                    CfgId:       cfgId,
                    SubCategory: info.CategoryID,
                })
            }

            // 重置一下用户佩戴的组件，避免过期的物品还在佩戴
            err = m.userVA.SetUserVirtualImageInUse(ctx, &virtual_image_user.SetUserVirtualImageInUseReq{
                Uid: info.Uid, Items: items, IsFullUpdate: true,
            })
            if err != nil {
                log.ErrorWithCtx(ctx, "consumeExpireVATask fail to SetUserVirtualImageInUse. ty:%d, %+v, err:%v", ty, info, err)
            }
        }()
        go func() {
            ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
            defer cancel()

            // 外显状态提醒
            err = m.CannotDisplayReminder(ctx, info.Uid)
            if err != nil {
                log.ErrorWithCtx(ctx, "consumeExpireVATask fail to CannotDisplayReminder. ty:%d, %+v, err:%v", ty, info, err)
            }
        }()
        go func() {
            ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
            defer cancel()

            // 外显状态提醒
            err = m.CannotDisplayDelayReminder(ctx, info.Uid)
            if err != nil {
                log.ErrorWithCtx(ctx, "consumeExpireVATask fail to CannotDisplayDelayReminder. ty:%d, %+v, err:%v", ty, info, err)
            }
        }()
    }

    log.DebugWithCtx(ctx, "consumeExpireVATask ty:%d, %+v", ty, info)
}

func (m *Mgr) genExpireIMMsgContent(ctx context.Context, info *cache.ExpireInfo) (string, error) {
    itemNames := ""
    cfgList, err := m.acLayer.BatGetVirtualImageCfg(ctx, info.ItemIds)
    if err != nil {
        log.ErrorWithCtx(ctx, "genExpireIMMsgContent fail to BatGetVirtualImageCfg. info:%+v, err:%v", info, err)
        return "", err
    }

    for i, cfg := range cfgList {
        if cfg.GetDisplayName() == "" || !cfg.GetIsNewResource() {
            continue
        }
        if i > 0 {
            itemNames += "、"
        }

        itemNames += fmt.Sprintf("[%s]", cfg.GetDisplayName())
        if i >= 1 {
            // 最多只展示2个物品名称
            itemNames += fmt.Sprintf("等%d个", len(cfgList))
            break
        }
    }

    if itemNames == "" {
        return "", nil
    }

    content := ""
    now := time.Now()
    expireTime := time.Unix(info.ExpireTs, 0)
    if now.After(expireTime) {
        content = fmt.Sprintf("【虚拟形象物品过期提示】你的%s物品已过期，快去看看其他上新商品吧~", itemNames)
    } else {
        content = fmt.Sprintf("【虚拟形象物品即将过期提示】你的%s物品将于%s过期，快去看看其他上新商品吧~",
            itemNames, expireTime.Format("2006/01/02 15:04:05"))
    }

    return content, nil
}

func (m *Mgr) channelImDisplayReminderTimeout(ctx context.Context) {
    now := time.Now()
    queryTs := now.Add(-time.Minute).Unix()

    uidList, err := m.cache.GetUserInChannelPool(ctx, queryTs, queryTs)
    if err != nil {
        log.ErrorWithCtx(ctx, "channelImReminderTimeout fail to GetUserInChannelPool. queryTs:%d, err:%v", queryTs, err)
        return
    }

    err = m.cache.LPushDisplayRemainderUid(ctx, cache.DisplayRemainderQueueTypeChannel, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "channelImReminderTimeout fail to LPushDisplayRemainderUid. queryTs:%d, err:%v", queryTs, err)
        return
    }

    log.DebugWithCtx(ctx, "channelImDisplayReminderTimeout uidList:%v", uidList)
}

func (m *Mgr) consumeChannelImDisplayReminder(ctx context.Context) {
    uid, err := m.cache.RPopDisplayRemainderUid(ctx, cache.DisplayRemainderQueueTypeChannel)
    if err != nil {
        log.ErrorWithCtx(ctx, "consumeChannelImDisplayReminder fail to RPopDisplayRemainderUid. err:%v", err)
        return
    }

    if uid == 0 {
        return
    }

    reminderCfg := m.bc.GetDisplayReminder()
    if reminderCfg == nil {
        return
    }

    info, exist, err := m.cache.GetUserChannelImDisplayReminder(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "consumeChannelImDisplayReminder fail to GetUserChannelImDisplayReminder. uid:%d, err:%v", uid, err)
        return
    }
    if !exist {
        return
    }

    now := time.Now()
    if now.Unix() < info.LastTime+reminderCfg.ChannelReminderCycleSec {
        // 周期内,已经达到次数,不再提醒
        if info.Count >= reminderCfg.ChannelReminderCycleCnt {
            return
        }
    } else {
        // 到下一周期了，重置次数
        info.Count = 0
        info.LastTime = now.Unix()
    }

    info.Count++
    info.TotalCnt++

    // 房间公屏消息
    err = m.acLayer.PushUserDisplayStatusReminderChannelIm(ctx, &virtual_image_logic.VirtualImageDisplayStatusChannelNotify{
        Uid:              uid,
        Content:          displayDelayReminderContent,
        HighLightContent: "前往搭配＞",
        JumpUrl:          displayReminderJumpUrl,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "consumeChannelImDisplayReminder fail to CannotDisplayReminder. uid:%d, err:%v", uid, err)
    }

    if info.TotalCnt >= reminderCfg.ChannelReminderMaxCnt {
        // 达到最大次数，移除提醒
        err = m.cache.RemoveUserChannelImDisplayReminder(ctx, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "consumeChannelImDisplayReminder fail to RemoveUserChannelImDisplayReminder. uid:%d, err:%v", uid, err)
            return
        }
    } else {
        // 更新提醒信息
        err = m.cache.SetUserChannelImDisplayReminder(ctx, uid, info)
        if err != nil {
            log.ErrorWithCtx(ctx, "consumeChannelImDisplayReminder fail to SetUserChannelImDisplayReminder. uid:%d, err:%v", uid, err)
            return
        }
    }

    log.DebugWithCtx(ctx, "consumeChannelImDisplayReminder uid:%d", uid)
}

func (m *Mgr) delayDisplayReminderTimeout(ctx context.Context, sec int64, clearPool bool) {
    now := time.Now()
    delay := time.Duration(sec) * time.Second
    queryTs := now.Add(-delay).Unix()

    uidList, err := m.cache.GetDelayDisplayReminderUidList(ctx, queryTs, queryTs)
    if err != nil {
        log.ErrorWithCtx(ctx, "delayDisplayReminderTimeout fail to GetUserInChannelPool. queryTs:%d, err:%v", queryTs, err)
        return
    }

    err = m.cache.LPushDisplayRemainderUid(ctx, cache.DisplayRemainderQueueTypeDelay, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "delayDisplayReminderTimeout fail to LPushDisplayRemainderUid. queryTs:%d, err:%v", queryTs, err)
        return
    }

    if clearPool {
        err = m.cache.RemoveDelayDisplayReminderByTs(ctx, queryTs)
        if err != nil {
            log.ErrorWithCtx(ctx, "delayDisplayReminderTimeout fail to RemoveDelayDisplayReminderByTs. queryTs:%d, err:%v", queryTs, err)
            return
        }
    }

    log.DebugWithCtx(ctx, "delayDisplayReminderTimeout uidList:%v", uidList)
}

func (m *Mgr) consumeDelayDisplayReminder(ctx context.Context) {
    uid, err := m.cache.RPopDisplayRemainderUid(ctx, cache.DisplayRemainderQueueTypeDelay)
    if err != nil {
        log.ErrorWithCtx(ctx, "consumeChannelImDisplayReminder fail to RPopDisplayRemainderUid. err:%v", err)
        return
    }

    if uid == 0 {
        return
    }

    err = m.pushUserCannotDisplayReminderMsg(ctx, uid, false, displayDelayReminderContent)
    if err != nil {
        log.ErrorWithCtx(ctx, "consumeDelayDisplayReminder fail to pushUserCannotDisplayReminderMsg. uid:%d, err:%v", uid, err)
    }

    log.DebugWithCtx(ctx, "consumeDelayDisplayReminder uid:%d", uid)
}
