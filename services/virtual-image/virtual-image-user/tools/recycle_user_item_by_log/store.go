package main

import (
    "context"
    "fmt"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "strings"
    "time"
)

type Store struct {
    db mysql.DBx
}

func NewStore(ctx context.Context, cfg *mysqlConnect.MysqlConfig) (*Store, error) {
    mysqlDBCli, err := mysqlConnect.NewClient(ctx, cfg)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewStore fail to mysqlConnect.NewClient, %+v, err:%v", cfg, err)
        return nil, err
    }

    s := &Store{
        db: mysqlDBCli,
    }

    return s, nil
}

func (s *Store) Close() error {
    return s.db.Close()
}

func genParamJoinStr(list []uint32) string {
    strList := make([]string, 0, len(list))
    for _, i := range list {
        strList = append(strList, fmt.Sprint(i))
    }

    return strings.Join(strList, ",")
}

func genAwardLogTblName(t time.Time) string {
    return fmt.Sprintf("virtual_image_award_%04d%02d", t.Year(), t.Month())
}

// GetUidListFromLog 获取uid列表
func (s *Store) GetUidListFromLog(ctx context.Context, offset, limit, beginUid uint32, tblTime time.Time) ([]uint32, error) {
    uidList := make([]uint32, 0)
    if limit == 0 {
        return uidList, nil
    }

    sql := fmt.Sprintf("select distinct(uid) from %s where uid > ? order by uid limit ?,?", genAwardLogTblName(tblTime))
    err := s.db.SelectContext(ctx, &uidList, sql, beginUid, offset, limit)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUidListFromLog fail. err:%v", err)
        return uidList, err
    }

    return uidList, nil
}

/*
var createAwardLogTblSql = `CREATE Table IF NOT EXISTS %s(
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id 自增',
    order_id varchar(128) NOT NULL COMMENT '发放order_id',
    source_order_id varchar(128) NOT NULL COMMENT '来源order_id',
    uid int(10) unsigned NOT NULL COMMENT '发放用户uid',
    va_id int(10) unsigned NOT NULL COMMENT '虚拟形象组件id',
    category_id int(10) unsigned NOT NULL COMMENT '组件分类id',
    price int(10) unsigned NOT NULL default 0 COMMENT '价格,单位T豆',
    suit_price int(10) unsigned NOT NULL default 0 COMMENT '套装总价',
    award_duration_sec int(10) NOT NULL COMMENT '发放时长/秒',
    award_source int(10) unsigned NOT NULL default 0 COMMENT '发放来源',
    award_source_desc varchar(20) NOT NULL default '' COMMENT '发放来源描述',
    award_time datetime NOT NULL default CURRENT_TIMESTAMP COMMENT '发放时间',
    expire_time datetime NOT NULL default CURRENT_TIMESTAMP COMMENT '过期时间',

    ctime datetime NOT NULL default CURRENT_TIMESTAMP COMMENT '创建时间',
    mtime datetime NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    primary key (id),
    index idx_uid(uid),
    index idx_source_order_id(source_order_id),
    unique key uniq_idx_order_id(order_id),
    index idx_award_time(award_time)
)engine=InnoDB default charset=utf8mb4 COMMENT "虚拟形象组件发放流水表";`

*/

type UserAwardLog struct {
    Uid        uint32 `db:"uid"`
    VAId       uint32 `db:"va_id"`
    CategoryID uint32 `db:"category_id"`
    AwardSec   int32  `db:"award_sec"`
}

func (s *Store) BatchGetLogByUid(ctx context.Context, uidList []uint32, awardSource uint32, expireTime, tblTime time.Time) ([]*UserAwardLog, error) {
    list := make([]*UserAwardLog, 0)
    if len(uidList) == 0 {
        return list, nil
    }

    sql := fmt.Sprintf("select uid, va_id, category_id, sum(award_duration_sec) as award_sec from %s where uid in (%s) and award_source=? " +
        "and expire_time>=? and expire_time<'2035-01-01'"+
        "group by uid, va_id, category_id",
        genAwardLogTblName(tblTime), genParamJoinStr(uidList))
    err := s.db.SelectContext(ctx, &list, sql, awardSource, expireTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetLogByUid fail. err:%v", err)
        return list, err
    }

    return list, nil
}
