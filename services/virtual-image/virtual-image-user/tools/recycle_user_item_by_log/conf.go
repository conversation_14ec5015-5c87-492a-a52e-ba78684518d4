package main

import (
    "encoding/json"
    "fmt"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    "os"
)

type ServiceConfigT struct {
	MysqlConfig *mysqlConnect.MysqlConfig `json:"mysql"`
}

func (sc *ServiceConfigT) Parse(path string) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := os.ReadFile(path)
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, &sc)
	if err != nil {
		return err
	}

	return
}
