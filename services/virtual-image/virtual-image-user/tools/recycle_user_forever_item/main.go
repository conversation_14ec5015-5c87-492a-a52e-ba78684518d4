package main

import (
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "os"
    "time"
)

var st *Store

func main() {
    if len(os.Args) < 2 {
        log.Errorf("Usage: %s configFile", os.Args[0])
        return
    }

    sc := &ServiceConfigT{}
    cfgPath := os.Args[1]
    err := sc.Parse(cfgPath)
    if err != nil {
        log.Errorf("Parse fail. err:%v", err)
        return
    }
    log.Infof("cfgPath:%s", cfgPath)

    ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
    defer cancel()

    st, err = NewStore(ctx, sc.MysqlConfig, sc.ReadOnlyDbConfig)
    if err != nil {
        log.Errorf("NewMysql fail. err:%v", err)
        return
    }

    tblCnt := uint32(100)
    limit := uint32(1500)

    for tblIdx := uint32(0); tblIdx < tblCnt; tblIdx++ {
        retry := 0
        for {
            done, err := doRecycleForeverItem(tblIdx, limit)
            if err != nil {
                if retry < 1000 {
                    log.Warnf("Retrying doRecycleForeverItem for tblIdx:%d, limit:%d, retry:%d",
                        tblIdx, limit, retry)
                    time.Sleep(3 * time.Second)
                    retry++
                    continue
                }
                log.Errorf("doRecycleForeverItem fail. tblIdx:%d, limit:%d, err:%v", tblIdx, limit, err)
                return
            }

            retry = 0
            if done {
                log.Infof("Finished processing tblIdx:%d", tblIdx)
                break
            }
            time.Sleep(50*time.Millisecond) // Sleep to avoid tight loop
        }
    }
}

func doRecycleForeverItem( tblIdx, limit uint32) (bool, error) {
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    idList, err := st.GetForeverItemRecordIdList(ctx, tblIdx, limit)
    if err != nil {
        log.Errorf("GetForeverItemRecordIdList fail. tblIdx:%d, limit:%d, err:%v", tblIdx, limit, err)
        return false, err
    }

    if len(idList) == 0 {
        log.Infof("No more records to process for tblIdx:%d", tblIdx)
        return true, nil
    }

    err = st.UpdateForeverItemRecordExpire(ctx, tblIdx, idList)
    if err != nil {
        log.Errorf("UpdateForeverItemRecordExpire fail. tblIdx:%d, limit:%d, err:%v", tblIdx, limit, err)
        return false, err
    }

    tblDone := len(idList) < int(limit)
    return tblDone, nil
}
