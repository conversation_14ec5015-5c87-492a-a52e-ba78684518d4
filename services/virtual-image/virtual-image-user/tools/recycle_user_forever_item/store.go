package main

import (
    "context"
    "fmt"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "strings"
)

type Store struct {
    db, readOnlyDb mysql.DBx
}

func NewStore(ctx context.Context, cfg, readOnlyDbCfg *mysqlConnect.MysqlConfig) (*Store, error) {
    mysqlDBCli, err := mysqlConnect.NewClient(ctx, cfg)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewStore fail to mysqlConnect.NewClient, %+v, err:%v", cfg, err)
        return nil, err
    }

    mysqlReadonlyDb, err := mysqlConnect.NewClient(ctx, readOnlyDbCfg)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewStore fail to mysqlConnect.NewClient, %+v, err:%v", readOnlyDbCfg, err)
        return nil, err
    }

    s := &Store{
        db:         mysqlDBCli,
        readOnlyDb: mysqlReadonlyDb,
    }

    return s, nil
}

func (s *Store) Close() error {
    return s.db.Close()
}

func genParamJoinStr(list []uint32) string {
    strList := make([]string, 0, len(list))
    for _, i := range list {
        strList = append(strList, fmt.Sprint(i))
    }

    return strings.Join(strList, ",")
}

func genUserVirtualImageTblName(uid uint32) string {
    return fmt.Sprintf("virtual_image_user_%02d", uid%100)
}

var createUserVirtualImageTblSql = `CREATE Table IF NOT EXISTS %s(
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id 自增',
    
    uid int(10) unsigned NOT NULL COMMENT '用户uid',
    va_id int(10) unsigned NOT NULL COMMENT '虚拟形象组件id',
    category_id int(10) unsigned NOT NULL COMMENT '虚拟形象组件分类id',
    
    award_time datetime NOT NULL default CURRENT_TIMESTAMP COMMENT '发放时间',
    expire_time datetime NOT NULL default CURRENT_TIMESTAMP COMMENT '过期时间',
  
    ctime datetime NOT NULL default CURRENT_TIMESTAMP COMMENT '创建时间',
    mtime datetime NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    primary key (id),
    index idx_expire_time (expire_time),
    unique key uniq_idx_uid_va_id(uid, va_id)
)engine=InnoDB default charset=utf8mb4 COMMENT "用户虚拟形象数据表";`

func (s *Store) GetForeverItemRecordIdList(ctx context.Context, tblIdx, limit uint32) ([]uint32, error) {
    if limit == 0 {
        return nil, nil
    }

    sql := fmt.Sprintf("SELECT id FROM %s WHERE expire_time > '2035-01-01 00:00:00' LIMIT ?", genUserVirtualImageTblName(tblIdx))
    var idList []uint32

    err := s.readOnlyDb.SelectContext(ctx, &idList, sql, limit)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetForeverItemRecordIdList fail. tblIdx:%d, limit:%d, err:%v", tblIdx, limit, err)
        return nil, err
    }

    return idList, nil
}

func (s *Store) UpdateForeverItemRecordExpire(ctx context.Context, tblIdx uint32, idList []uint32) error {
    if len(idList) == 0 {
        return nil
    }

    sql := fmt.Sprintf("UPDATE %s SET expire_time = '2025-08-07 00:00:00' WHERE id IN (%s) AND expire_time > '2035-01-01 00:00:00'",
        genUserVirtualImageTblName(tblIdx), genParamJoinStr(idList))
    _, err := s.db.ExecContext(ctx, sql)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateForeverItemRecordExpire fail. tblIdx:%d, idList:%v, err:%v", tblIdx, idList, err)
        return err
    }

    return nil
}