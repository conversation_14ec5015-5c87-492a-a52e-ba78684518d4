package mysql

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"strings"
	"time"
)

// 听听直播间随机标题库

var createLiveRandomTitleTable = `CREATE TABLE IF NOT EXISTS tbl_channel_live_random_titles (
  id bigint(10) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  tag_id int(10) unsigned NOT NULL COMMENT '房间品类标签',
  title        varchar(64) NOT NULL COMMENT '随机标题',
  oper         varchar(32) NOT NULL DEFAULT '' COMMENT '操作人',
  deleted      tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0:否 1:是',
  ctime        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  mtime        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY(id),
  UNIQUE  KEY (tag_id, title)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='听听直播间随机房间标题库';`

type LiveRandomTitle struct {
	Id    uint32    `gorm:"column:id" db:"id" json:"id"`             // ID
	TagId uint32    `gorm:"column:tag_id" db:"tag_id" json:"tag_id"` // 房间品类标签
	Title string    `gorm:"column:title" db:"title" json:"title"`    // 随机标题
	Oper  string    `gorm:"column:oper" db:"oper" json:"oper"`       // 操作人
	Ctime time.Time `gorm:"column:ctime" db:"ctime" json:"ctime"`    // 创建时间
	Mtime time.Time `gorm:"column:mtime" db:"mtime" json:"mtime"`    // 更新时间
}

func (l *LiveRandomTitle) TableName() string {
	return "tbl_channel_live_random_titles"
}

func (c *Store) AddLiveRandomTitle(ctx context.Context, tagId uint32, title string, oper string) error {
	_, err := c.db.Exec("insert into tbl_channel_live_random_titles (tag_id,title,oper) values(?,?,?) on duplicate key update deleted=0, oper=values(oper)", tagId, title, oper)
	if nil != err {
		log.ErrorWithCtx(ctx, "AddLiveRandomTitle tag_id:%d, title:%s, oper:%s error:%v", tagId, title, oper, err)
	}
	return err
}

func (c *Store) DelLiveRandomTitle(ctx context.Context, id uint32, oper string) error {
	_, err := c.db.Exec("update tbl_channel_live_random_titles set deleted=1, oper=? where id=?", oper, id)
	if nil != err {
		log.ErrorWithCtx(ctx, "DelLiveRandomTitle id:%d, error:%v", id, err)
	}
	return err
}

func (c *Store) GetLiveRandomTitle(ctx context.Context, id uint32, tagIds []uint32, title string, offset, limit uint32) ([]*LiveRandomTitle, uint32, error) {
	where := "deleted=0"
	args := make([]interface{}, 0)
	if id > 0 {
		where = "id=?"
		args = append(args, id)
	}
	if len(tagIds) > 0 {
		tags := make([]string, 0, len(tagIds))
		for _, tagId := range tagIds {
			tags = append(tags, fmt.Sprintf("%d", tagId))
		}
		where = fmt.Sprintf("%s and tag_id in (%s)", where, strings.Join(tags, ","))
	}
	if title != "" {
		where = fmt.Sprintf("%s and title like ?", where)
		args = append(args, "%"+title+"%")
	}
	sql := fmt.Sprintf("select id, tag_id, title, oper, ctime, mtime from tbl_channel_live_random_titles where %s order by id desc limit %d,%d", where, offset, limit)
	records := make([]*LiveRandomTitle, 0)
	err := c.db.SelectContext(ctx, &records, sql, args...)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetLiveRandomTitle tagIds:%d, error:%v", tagIds, err)
		return records, 0, err
	}

	var count uint32
	countSQL := fmt.Sprintf("select count(*) from tbl_channel_live_random_titles where %s", where)
	err = c.db.QueryRowContext(ctx, countSQL, args...).Scan(&count)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveRandomTitle count error:%v", err)
		return records, 0, err
	}
	return records, count, nil
}
