package server

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/channellivemgr"
	"golang.52tt.com/services/channel-live-mgr/conf"
	mathRand "math/rand"
)

func (s *ChannelLiveMgrServer) GetLiveTagList(ctx context.Context, req *channellivemgr.GetLiveTagListReq) (*channellivemgr.GetLiveTagListResp, error) {
	resp := &channellivemgr.GetLiveTagListResp{}

	_, yuyinTags, err := s.aclExtLogicApi.GetAllTagNameConfig(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveTagList GetChannelTagConfigInfo failed, error:%v", err)
		return resp, err
	}
	for tagId, name := range yuyinTags {
		resp.TagList = append(resp.TagList, &channellivemgr.TagInfo{
			TagId:   tagId,
			TagName: name,
		})
	}
	log.InfoWithCtx(ctx, "GetLiveTagList success, resp:%v", resp.String())
	return resp, nil
}

func (s *ChannelLiveMgrServer) AddLiveRandomTitle(ctx context.Context, req *channellivemgr.AddLiveRandomTitleReq) (*channellivemgr.AddLiveRandomTitleResp, error) {
	resp := &channellivemgr.AddLiveRandomTitleResp{}
	for _, title := range req.TitleList {
		if title == "" {
			log.ErrorWithCtx(ctx, "AddLiveRandomTitle title is empty")
			continue
		}
		for _, tagId := range req.TagIdList {
			if tagId == 0 {
				log.ErrorWithCtx(ctx, "AddLiveRandomTitle tagId is invalid: %d", tagId)
				continue
			}
			err := s.mysqlStore.AddLiveRandomTitle(ctx, tagId, title, req.Oper)
			if err != nil {
				log.ErrorWithCtx(ctx, "AddLiveRandomTitle failed, tagId:%d title:%s oper:%s error:%v", tagId, title, req.Oper, err)
				return resp, err
			}
		}
	}
	log.InfoWithCtx(ctx, "AddLiveRandomTitle success, req :%s", req.String())
	return resp, nil
}

func (s *ChannelLiveMgrServer) QueryLiveRandomTitle(ctx context.Context, req *channellivemgr.QueryLiveRandomTitleReq) (*channellivemgr.QueryLiveRandomTitleResp, error) {
	resp := &channellivemgr.QueryLiveRandomTitleResp{}
	offset := (req.Page - 1) * req.PageSize
	titles, total, err := s.mysqlStore.GetLiveRandomTitle(ctx, req.Id, req.TagIdList, req.Title, offset, req.PageSize)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryLiveRandomTitle failed, tagIds:%v title:%s offset:%d limit:%d error:%v", req.TagIdList, req.Title, offset, req.PageSize, err)
		return resp, err
	}
	resp.TotalNum = total
	for _, title := range titles {
		resp.TitleList = append(resp.TitleList, &channellivemgr.LiveRandomTitle{
			Id:       title.Id,
			TagId:    title.TagId,
			Title:    title.Title,
			Oper:     title.Oper,
			UpdateTs: uint32(title.Mtime.Unix()),
		})
	}
	log.InfoWithCtx(ctx, "QueryLiveRandomTitle success, req:%s, resp:%v", req.String(), resp)
	return resp, nil
}

func (s *ChannelLiveMgrServer) DelLiveRandomTitle(ctx context.Context, req *channellivemgr.DelLiveRandomTitleReq) (*channellivemgr.DelLiveRandomTitleResp, error) {
	resp := &channellivemgr.DelLiveRandomTitleResp{}
	if req.Id == 0 {
		log.ErrorWithCtx(ctx, "DelLiveRandomTitle id is invalid: %d", req.Id)
		return resp, nil
	}
	err := s.mysqlStore.DelLiveRandomTitle(ctx, req.Id, req.Oper)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelLiveRandomTitle failed, id:%d oper:%s error:%v", req.Id, req.Oper, err)
		return resp, err
	}
	return resp, nil
}

func (s *ChannelLiveMgrServer) GetLiveReadyGuideImg(ctx context.Context, req *channellivemgr.GetLiveReadyGuideImgReq) (*channellivemgr.GetLiveReadyGuideImgResp, error) {
	resp := &channellivemgr.GetLiveReadyGuideImgResp{}
	tagId, err := s.aclExtLogicApi.GetChannelTag(ctx, req.Uid, req.Cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveReadyGuideImg GetChannelLiveInfo failed, uid:%d cid:%d err:%v", req.Uid, req.Cid, err)
		return resp, err
	}
	resp.ImgUrl = conf.GetLiveReadyGuideImg(tagId)
	log.InfoWithCtx(ctx, "GetLiveReadyGuideImg success, uid:%d cid:%d, tagId:%d, imgUrl:%s", req.Uid, req.Cid, tagId, resp.ImgUrl)
	return resp, nil
}

func (s *ChannelLiveMgrServer) GetChannelLiveRandomTitle(ctx context.Context, req *channellivemgr.GetChannelLiveRandomTitleReq) (*channellivemgr.GetChannelLiveRandomTitleResp, error) {
	resp := &channellivemgr.GetChannelLiveRandomTitleResp{}
	if req.TagId == 0 {
		log.ErrorWithCtx(ctx, "GetChannelLiveRandomTitle tagId is invalid: %d", req.TagId)
		return resp, nil
	}
	titles, total, err := s.mysqlStore.GetLiveRandomTitle(ctx, 0, []uint32{req.TagId}, "", 0, 5000)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveRandomTitle failed, tagId:%d error:%v", req.TagId, err)
		return resp, err
	}
	if len(titles) == 0 {

		user, err := s.aclExtLogicApi.GetUserInfo(ctx, req.Uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelLiveRandomTitle GetUser failed, uid:%d error:%v", req.Uid, err)
			return resp, err
		}
		title := fmt.Sprintf("%s陪你聊天", user.Nickname)
		runes := []rune(title)
		if len(runes) > 15 {
			title = string(runes[:15]) // 限制标题长度为15个字符
		}
		resp.Title = title
		log.WarnWithCtx(ctx, "GetChannelLiveRandomTitle no titles found for tagId:%d, default title:%s", req.TagId, title)
		return resp, nil
	}
	if total > 5000 {
		log.WarnWithCtx(ctx, "GetChannelLiveRandomTitle found too many titles for tagId:%d, total:%d", req.TagId, total)
	}

	idx := mathRand.Intn(len(titles))
	resp.Title = titles[idx].Title
	log.InfoWithCtx(ctx, "GetChannelLiveRandomTitle success, cid:%d, tagId:%d title:%v", req.ChannelId, req.TagId, resp.Title)
	return resp, nil
}
