package conf

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"sync"
	"time"

	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channellivemgr"
)

const (
	configFile = "/data/oss/conf-center/tt/channel-live-mgr.json"
)

var mapWhiteChannelID = make(map[uint32]int8)
var lastMd5 [16]byte
var gMutex sync.RWMutex

var gActivityConf *ActivityConf

type MatchTypeTimeRange struct {
	BeginTs uint32 `json:"begin_time"`
	EndTs   uint32 `json:"end_time"`
	MatchTy uint32 `json:"match_type"`
}

type ExtraPkTimeStu struct {
	Rule          string  `json:"rule"`            //客户端展示加时玩法文本
	SingleVal     uint32  `json:"single_val"`      //分差多少之内触发
	LeftTs        int64   `json:"left_time"`       //离结束多少秒触发
	TotalScore    int64   `json:"total_score"`     //总分多少触发
	FixTs         int64   `json:"fix_ts"`          //小于这个时间不再触发加时
	OpenTimeRange []int64 `json:"open_time_range"` //开启PK加时时间段
	Switch        bool    `json:"switch"`          //当前时间段是否开启
}

// PkCntLimit PK次数限制配置结构体
// 用于配置特定时间段内用户的PK次数上限，实现时间段限流功能
type PkCntLimit struct {
	BeginTime int `json:"begin_time"` // 限制开始时间，从当日零点开始计算的秒数(如14:00对应50400秒)
	EndTime   int `json:"end_time"`   // 限制结束时间，从当日零点开始计算的秒数(如20:00对应72000秒)
	Cnt       int `json:"cnt"`        // 在限制时间段内允许的最大PK次数
}

type ActivityConf struct {
	MatchTypeTimeRangeList []*MatchTypeTimeRange `json:"time_range_list"` //区间端使用哪种匹配模式

	NormalMatchTimeRange []uint32 `json:"normal_match_time_range"` //普通匹配池时间区间
	RankMatchTimeRange   []uint32 `json:"rank_match_time_range"`   //PK赛匹配池时间区间
	NormalMatchScores    []uint32 `json:"normal_scores"`           //普通匹配匹配分差距
	RankMatchScores      []uint32 `json:"rank_scores"`             //PK赛匹配分之差

	NormalDayMatchCnt uint32          `json:"normal_day_match_cnt"`
	RankDayMatchCnt   uint32          `json:"rank_day_match_cnt"`
	ExtraPkTime       *ExtraPkTimeStu `json:"extra_time"`
	ExtraPkWhiteList  []uint32        `json:"extra_time_white_list"` //PK加时开关开启前的，可以触发加时的白名单channelID列表
	LiveCacheTs       int64           `json:"live_cache_ts"`         //直播状态内存缓存秒数

	StateSecList []*StateSec `json:"states"` //每个阶段对应的时长，秒为单位

	AppointPkCountDownTs uint32 `json:"appoint_pk_count_down_ts"` // 指定pk应战邀约倒计时时间
	AppointPkTs          uint32 `json:"appoint_pk_ts"`            // 指定pk每场PK时间

	WebUrl string `json:"web_url"` // 请求前端组的接口url

	PkCntLimit *PkCntLimit `json:"pk_cnt_limit"`

	PkSameMatchCntLimit uint32 `json:"pk_same_match_cnt_limit"` // 相同对手近n场不能匹配

	VirtualLiveSecretExpire    uint32   `json:"virtual_live_secret_expire"`     //虚拟主播房间密钥过期时间(秒)
	VirtualLiveSecretSwitch    bool     `json:"virtual_live_secret_switch"`     //虚拟主播房间密钥开启开关
	VirtualLiveSecretWhiteCids []uint32 `json:"virtual_live_secret_white_cids"` //虚拟主播房间密钥开启房间白名单

	LiveReadyGuideImgs map[uint32]string `json:"live_ready_guide_imgs"` // 直播准备页引导图片配置 tag_id -> img
}

func init() {

	Parse()

	go func() {
		for {
			select {
			case <-time.After(time.Minute):
				Parse()
			}
		}
	}()

}

func Parse() error {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("channel_live_mgr activity_conf Parse r:%v", r)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		log.Errorf("ReadFile fail %v", err)
		return err
	}

	tmpMd5 := md5.Sum(data)
	if lastMd5 == tmpMd5 {
		return nil
	}

	log.Infof("channel_live_mgr_activity_conf Parse Unmarshal data:%v", data)

	tmpConf := &ActivityConf{}

	err = json.Unmarshal(data, &tmpConf)
	if err != nil {
		log.Errorf("channel_live_mgr_activity_conf Parse Unmarshal err:%v ", err)
		return err
	}

	gMutex.Lock()

	gActivityConf = tmpConf
	lastMd5 = tmpMd5

	if gActivityConf.ExtraPkWhiteList != nil {
		mapWhiteChannelID = make(map[uint32]int8)
		for _, cid := range gActivityConf.ExtraPkWhiteList {
			mapWhiteChannelID[cid] = 1
		}
	}

	for _, v := range gActivityConf.StateSecList {
		Sec2State[v.State] = v.Sec
	}

	gMutex.Unlock()

	log.Infof("dyconf=%s", utils.ToJson(gActivityConf))
	log.Infof("channel_live_mgr_activity_conf conf:%+v extra:%+v white:%+v", *gActivityConf, gActivityConf.ExtraPkTime, mapWhiteChannelID)

	return nil
}

func GetMatchType(ts uint32) pb.ChannelLivePKMatchType {

	gMutex.RLock()
	defer gMutex.RUnlock()

	if gActivityConf == nil {
		return pb.ChannelLivePKMatchType_CPK_Match_Nornal
	}

	for _, m := range gActivityConf.MatchTypeTimeRangeList {
		if ts >= m.BeginTs && ts <= m.EndTs {
			return pb.ChannelLivePKMatchType(m.MatchTy)
		}
	}

	return pb.ChannelLivePKMatchType_CPK_Match_Nornal
}

// GetPkCntLimitItem 获取PK次数限制配置项
// 该函数用于获取当前配置的PK次数限制信息，支持时间范围检查
//
// 参数说明:
//   - hit: 是否检查当前时间是否在限制时间范围内
//   - true: 只有当前时间在限制时间段内时才返回配置，否则返回nil
//   - false: 直接返回配置，不检查当前时间
//
// 返回值:
//   - *PkCntLimit: PK次数限制配置结构体，包含以下字段：
//   - BeginTime: 限制开始时间(从当日零点开始的秒数)
//   - EndTime: 限制结束时间(从当日零点开始的秒数)
//   - Cnt: 限制时间段内允许的最大PK次数
//   - 如果未配置或不在限制时间内(当hit=true时)，返回nil
//
// 比如{"begin_time":72000,"end_time":79200,"cnt":2},
// 即 72000/3600 = 20  79200/3600 = 22
// 表示 20:00～22:00期间只能连麦2次
//
// 使用场景:
//   - hit=true: 用于检查当前是否需要进行PK次数限制(CheckIsPkCntLimit、AddPkCnt)
//   - hit=false: 用于获取配置信息展示给客户端(GetPkLimitInfo)
//
// 时间计算逻辑:
//   - 获取当前时间距离当日零点的秒数
//   - 判断该秒数是否在[BeginTime, EndTime]范围内
func GetPkCntLimitItem(hit bool) *PkCntLimit {
	if gActivityConf == nil {
		return nil
	}

	cntLimit := gActivityConf.PkCntLimit
	if false == hit {
		return cntLimit
	}

	nowTs := time.Now()
	durationTime := nowTs.Sub(time.Date(nowTs.Year(), nowTs.Month(), nowTs.Day(), 0, 0, 0, 0, time.Local))
	durationSec := int(durationTime.Seconds())
	if cntLimit.BeginTime <= durationSec && durationSec <= cntLimit.EndTime {
		return cntLimit
	}

	return nil
}

func GetMatchIndexTime(index uint32, matchTy pb.ChannelLivePKMatchType) ([]uint32, uint32, uint32, uint32) {

	gMutex.RLock()
	defer gMutex.RUnlock()

	timeRange := []uint32{0, 600}
	var scores, dayCntLimit, sameMatchCntLimit uint32 = 999999999, 3, 0

	if gActivityConf == nil {
		return timeRange, scores, dayCntLimit, sameMatchCntLimit
	}

	if matchTy == pb.ChannelLivePKMatchType_CPK_Match_Nornal {
		if int(index) < len(gActivityConf.NormalMatchScores) {
			scores = gActivityConf.NormalMatchScores[index]
			dayCntLimit = gActivityConf.NormalDayMatchCnt
			timeRange = []uint32{gActivityConf.NormalMatchTimeRange[index], gActivityConf.NormalMatchTimeRange[index+1]}
			sameMatchCntLimit = gActivityConf.PkSameMatchCntLimit
		}
	} else if matchTy == pb.ChannelLivePKMatchType_CPK_Match_Rank {
		if int(index) < len(gActivityConf.RankMatchScores) {
			scores = gActivityConf.RankMatchScores[index]
			dayCntLimit = gActivityConf.RankDayMatchCnt
			timeRange = []uint32{gActivityConf.RankMatchTimeRange[index], gActivityConf.RankMatchTimeRange[index+1]}
		}
	}

	log.Debugf("GetMatchIndexTime index:%v match:%v %v %v %v", index, matchTy, timeRange, scores, dayCntLimit)

	return timeRange, scores, dayCntLimit, sameMatchCntLimit
}

func GetMatchTimeSlot(matchTy pb.ChannelLivePKMatchType) uint32 {
	gMutex.RLock()
	defer gMutex.RUnlock()

	if gActivityConf == nil {
		return 1
	}

	sz := len(gActivityConf.NormalMatchScores)
	if matchTy == pb.ChannelLivePKMatchType_CPK_Match_Rank {
		sz = len(gActivityConf.RankMatchScores)
	}

	if sz == 0 {
		sz = 1
	}

	return uint32(sz)
}

func GetExtraTimeConf() *ExtraPkTimeStu {
	gMutex.RLock()
	defer gMutex.RUnlock()

	extra := &ExtraPkTimeStu{
		SingleVal:  10000,
		LeftTs:     10,
		TotalScore: 10000,
		FixTs:      2,
		Rule:       "最后10S前双方PK值之和>=10000，则最后10S内落后方单笔送礼>=10000T豆反超领先方，PK时间将延长",
		Switch:     false,
	}

	if gActivityConf != nil && gActivityConf.ExtraPkTime != nil {
		extra = gActivityConf.ExtraPkTime
	}

	rule := fmt.Sprintf("双方PK值之和>=%v，则最后%vS内落后方单笔送礼>=%vT豆反超领先方，PK时间将延长", extra.TotalScore, extra.LeftTs, extra.SingleVal)

	extra.Rule = rule
	extra.Switch = GetExtraTimeSwitch(extra)

	return extra
}

func GetLiveCacheTs() int64 {
	gMutex.RLock()
	defer gMutex.RUnlock()

	if gActivityConf != nil {
		return gActivityConf.LiveCacheTs
	}

	return 1
}

func GetExtraTimeSwitch(cxtraPkTime *ExtraPkTimeStu) bool {
	if cxtraPkTime == nil || cxtraPkTime.OpenTimeRange == nil {
		return false
	}

	timeRange := cxtraPkTime.OpenTimeRange
	if timeRange == nil {
		return false
	}
	nowTs := time.Now().Unix()
	for i := 0; i < len(timeRange)-1; {
		if nowTs >= timeRange[i] && nowTs <= timeRange[i+1] {
			return true
		}
		i = i + 2
	}

	return false
}

func GetAppointPkTsConf() (uint32, uint32) {
	gMutex.RLock()
	defer gMutex.RUnlock()

	if gActivityConf != nil {
		return gActivityConf.AppointPkCountDownTs, gActivityConf.AppointPkTs
	}

	return 0, 0
}

func GetWebUrl() string {
	gMutex.RLock()
	defer gMutex.RUnlock()

	if gActivityConf != nil {
		return gActivityConf.WebUrl
	}

	return ""
}

func GetVirtualLiveSecretExpire() uint32 {
	gMutex.RLock()
	defer gMutex.RUnlock()
	if gActivityConf == nil || gActivityConf.VirtualLiveSecretExpire == 0 {
		return 7 * 24 * 3600
	}
	return gActivityConf.VirtualLiveSecretExpire
}

func IsShowVirtualLiveSecret(cid uint32) bool {
	gMutex.RLock()
	defer gMutex.RUnlock()
	if gActivityConf == nil {
		return false
	}
	if gActivityConf.VirtualLiveSecretSwitch {
		return true
	}
	for _, c := range gActivityConf.VirtualLiveSecretWhiteCids {
		if cid == c {
			return true
		}
	}
	return false
}

func GetLiveReadyGuideImg(tagID uint32) string {
	gMutex.RLock()
	defer gMutex.RUnlock()

	if gActivityConf != nil {
		if img, ok := gActivityConf.LiveReadyGuideImgs[tagID]; ok {
			return img
		}
	}

	return ""
}
