package main

import (
    "fmt"
    "net/http"
    _ "net/http/pprof"
    "os"
    "time"

    show_list "golang.52tt.com/services/channel-live/channel-live-http-logic/control/show-list"

    "github.com/gorilla/mux"
    "github.com/urfave/cli"
    "golang.52tt.com/pkg/admin"
    "golang.52tt.com/pkg/config"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/versioning/svn" // code revision
    "golang.52tt.com/pkg/web"
    "golang.52tt.com/services/channel-live/channel-live-http-logic/client"
    "golang.52tt.com/services/channel-live/channel-live-http-logic/conf"
    "golang.52tt.com/services/channel-live/channel-live-http-logic/control/anchor_cert"
    official_channel "golang.52tt.com/services/channel-live/channel-live-http-logic/control/official-channel"
)

func main() {
    app := cli.NewApp()
    app.Name = "channel-live-http-logic"
    app.Version = svn.CodeRevision
    app.Compiled = time.Now()
    app.Copyright = "(c) 2023 TT"
    app.Usage = "tt revenue-base http service."
    app.Flags = []cli.Flag{
        cli.StringFlag{
            Name:  "config",
            Value: "",
            Usage: "config path",
        },
    }

    cli.VersionPrinter = func(c *cli.Context) {
        fmt.Fprintf(os.Stdout, "%s\n%s\n", c.App.Name, c.App.Version)
    }

    app.Action = func(c *cli.Context) error {
        log.SetLevel(log.DebugLevel)
        configPath := c.String("config")

        cfg, err := config.NewConfig("json", configPath)
        if err != nil {
            log.Fatalln("Failed to NewConfig: ", err)
            panic(err)
        }

        if err = conf.GetServerConfig().Parse(cfg); err != nil {
            log.Fatalln("Parse config fail: %v ", err)
            panic(err)
        }

        client.Setup()

        if err = conf.SetupDynamicConfig(); err != nil {
            log.Fatalln("Setup dynamic config fail: %v ", err)
            panic(err)
        }

        r := mux.NewRouter()

        auth := web.NewAuth(&web.UidAuthVerify{}, conf.GetServerConfig().GetValidateToken())
        handler := web.NewHandler(auth, conf.GetServerConfig().GetCors(), true)
        //noAuthHandler := web.NewHandler(nil, models.GetModelServer().GetServerConfig().GetCors(), true)

        // 外网接口
        p := r.PathPrefix("/channel-live-http-logic").Subrouter()
        anchorCertRouter := p.PathPrefix("/anchor-cert").Subrouter()
        anchorCertRouter.Handle("/anchorCertInit", handler.SetHandler(anchor_cert.AnchorCertInit))
        anchorCertRouter.Handle("/getAnchorCheckInfo", handler.SetHandler(anchor_cert.GetAnchorCheckInfo))
        anchorCertRouter.Handle("/getAnchorCertInfo", handler.SetHandler(anchor_cert.GetAnchorCertInfo))
        anchorCertRouter.Handle("/applyAnchorCheckUpgrade", handler.SetHandler(anchor_cert.ApplyAnchorCheckUpgrade))
        anchorCertRouter.Handle("/getAnchorLevelMonthTask", handler.SetHandler(anchor_cert.GetAnchorLevelMonthTask))

        offcialChannelRouter := p.PathPrefix("/official-channel").Subrouter()
        offcialChannelRouter.Handle("/CheckUserRegisterEntry", handler.SetHandler(official_channel.CheckUserRegisterEntry))
        offcialChannelRouter.Handle("/GetRegisterOfficialChList", handler.SetHandler(official_channel.GetRegisterOfficialChList))
        offcialChannelRouter.Handle("/GetOfficialChMatchInfo", handler.SetHandler(official_channel.GetOfficialChMatchInfo))
        offcialChannelRouter.Handle("/RegisterOfficialChannel", handler.SetHandler(official_channel.RegisterOfficialChannel))
        offcialChannelRouter.Handle("/CancelRegisterOfficialCh", handler.SetHandler(official_channel.CancelRegisterOfficialCh))

        channelLiveShowListRouter := p.PathPrefix("/channel-live-show-list").Subrouter()
        channelLiveShowListRouter.Handle("/GetShowTime", handler.SetHandler(show_list.GetShowTime))
        channelLiveShowListRouter.Handle("/GetShowTag", handler.SetHandler(show_list.GetShowTag))
        channelLiveShowListRouter.Handle("/DeclareShow", handler.SetHandler(show_list.DeclareShow))
        channelLiveShowListRouter.Handle("/GetShowList", handler.SetHandler(show_list.GetShowList))
        channelLiveShowListRouter.Handle("/GetShowListUserBaseInfo", handler.SetHandler(show_list.GetShowListUserBaseInfo))
        channelLiveShowListRouter.Handle("/UnfoldShowList", handler.SetHandler(show_list.UnfoldShowList))

        addr := conf.GetServerConfig().GetAddr()
        log.Infof("server run at %s\n", addr)
        http.Handle("/", r)

        closer := admin.ListenAndServe()
        defer closer()
        return http.ListenAndServe(addr, nil)
    }

    _ = app.Run(os.Args)
}
