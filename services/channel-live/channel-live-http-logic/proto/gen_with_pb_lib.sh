#!/usr/bin/env sh
pb_path=${QUICKSILVER_PATH}/third-party/tt-protocol/service/quicksilver
pb_app_path=${QUICKSILVER_PATH}/third-party/tt-protocol/app

protoc --proto_path=${pb_path} --proto_path=${pb_app_path} --go_out=.  tt/quicksilver/channel-live-http-logic/channel-live-http-logic.proto
ls golang.52tt.com/protocol/services/channel-live-http-logic/channel-live-http-logic.pb.go | xargs -n1 -IX bash -c 'sed s/,omitempty// X > X.tmp && mv X{.tmp,}'
mv golang.52tt.com/protocol/services/channel-live-http-logic/channel-live-http-logic.pb.go ../../../../protocol/services/channel-live-http-logic/channel-live-http-logic.pb.go
rm -rf golang.52tt.com

