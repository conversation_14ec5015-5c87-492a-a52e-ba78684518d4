package client

import (
    "context"
    "golang.52tt.com/clients/account"
    anchor_check "golang.52tt.com/clients/anchor-check"
    anchor_level "golang.52tt.com/clients/anchor-level"
    anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
    "golang.52tt.com/clients/channel"
    channellivemanagement "golang.52tt.com/clients/channel-live-management"
    channellivemgr "golang.52tt.com/clients/channel-live-mgr"
    channellivestats "golang.52tt.com/clients/channel-live-stats"
    headImageCli "golang.52tt.com/clients/headimage"
    official_live_channel "golang.52tt.com/clients/official-live-channel"
    channel_live_show_list "golang.52tt.com/protocol/services/channel-live-show-list"
    "google.golang.org/grpc"
)

var (
    AnchorContractClient   anchorcontract_go.IClient
    LiveStatsCli           channellivestats.IClient
    LiveManagementCli      channellivemanagement.IClient
    OfficialLiveChannelCli official_live_channel.IClient
    ChannelCli             channel.IClient
    LiveMgrCli             channellivemgr.IClient
    AccountCli             account.IClient
    AnchorCheckCli         anchor_check.IClient
    HeadImageCli           headImageCli.IClient
    AnchorLevelCli         anchor_level.IClient
    ChannelLiveShowList    channel_live_show_list.ChannelLiveShowListClient
)

func Setup() error {

    opts := []grpc.DialOption{
        grpc.WithBlock(),
        grpc.WithChainUnaryInterceptor(
            //tests.UnaryClientInterceptor(),                // 测试拦截器
            //sentinel_interceptor.UnaryClientInterceptor(), // 熔断拦截器
        ),
    }

    AnchorContractClient, _ = anchorcontract_go.NewClient(opts...)
    LiveStatsCli, _ = channellivestats.NewClient(opts...)
    LiveManagementCli, _ = channellivemanagement.NewClient(opts...)
    OfficialLiveChannelCli, _ = official_live_channel.NewClient(opts...)
    ChannelCli = channel.NewClient(opts...)
    LiveMgrCli, _ = channellivemgr.NewClient(opts...)
    AccountCli = account.NewIClient(opts...)
    AnchorCheckCli, _ = anchor_check.NewClient(opts...)
    AnchorLevelCli = anchor_level.NewIClient(opts...)
    ChannelLiveShowList, _ = channel_live_show_list.NewClient(context.Background(), opts...)
    HeadImageCli = headImageCli.NewIClient(opts...)

    return nil
}
