package anchor_cert

import (
	"net/http"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	mockcheck "golang.52tt.com/clients/mocks/anchor-check"
	mockanchorlevel "golang.52tt.com/clients/mocks/anchor-level"
	mockanchorcontract "golang.52tt.com/clients/mocks/anchorcontract-go"
	mockChannelLiveMgr "golang.52tt.com/clients/mocks/channel-live-mgr"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web"
	AnchorCheckpb "golang.52tt.com/protocol/services/anchor-check"
	anchorlevelpb "golang.52tt.com/protocol/services/anchor-level"
	anchorcontractpb "golang.52tt.com/protocol/services/anchorcontract-go"
	"golang.52tt.com/protocol/services/channellivemgr"
	"golang.52tt.com/services/channel-live/channel-live-http-logic/client"
)

func init() {
	log.SetLevel(log.DebugLevel)
}

// go test -timeout 30s -run ^TestTrafficMark$ golang.52tt.com/services/channel-live/channel-live-http-logic/control/anchor_cert -v -count=1
func TestTrafficMark(t *testing.T) {

	return

	/*
		ctx := protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{
			UserID:    uint32(2404178),
			RequestID: "requestIdStr",
		})

		ctx = metadata.AppendToOutgoingContext(ctx,
			"x-request-id", "requestIdStr",
			"x-qw-traffic-mark", "quicksilver-main-subenv", // 填充子环境流量标识
		)

		//client.Setup()
		anchorContractClient, err := anchorcontract_go.NewTargetClient("127.0.0.1:80", grpc.WithTimeout(time.Second*3))
		if err != nil {
			t.Log(err)
			return
		}
		resp, err := anchorContractClient.GetUserContractCacheInfo(ctx, 0, 2404178)
		t.Log(resp, err)
	*/
}

// go test -timeout 30s -run ^TestXX$ golang.52tt.com/services/channel-live/channel-live-http-logic/control/anchor_cert -v -count=1
func TestXX(t *testing.T) {
	now := time.Now()
	todayZero := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)

	lastApplyTm := time.Date(2023, 1, 31, 0, 0, 0, 0, time.Local)
	next3monthTm := time.Date(lastApplyTm.Year(), lastApplyTm.Month()+3, lastApplyTm.Day(), 0, 0, 0, 0, time.Local)

	t.Log(now)
	t.Log(next3monthTm)
	t.Log(lastApplyTm)

	lessDay := next3monthTm.Unix() - todayZero.Unix()
	t.Log(lessDay / 86400)
}

func init() {
	log.SetLevel(log.DebugLevel)
}

type indexHandler struct {
}

func (ih *indexHandler) Header() http.Header {
	return http.Header{}
}

func (ih *indexHandler) Write([]byte) (int, error) {
	return 0, nil
}

func (ih *indexHandler) WriteHeader(statusCode int) {
	return
}

// go test -timeout 30s -run ^TestGetAnchorCheckInfo$ golang.52tt.com/services/channel-live/channel-live-http-logic/control/anchor_cert -v
func TestGetAnchorCheckInfo(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockcheckcli := mockcheck.NewMockIClient(ctl)
	mockcontractcli := mockanchorcontract.NewMockIClient(ctl)
	mockanchorlevelcli := mockanchorlevel.NewMockIClient(ctl)
	checkresp := &AnchorCheckpb.GetAnchorCheckHistoryResp{
		List: []*AnchorCheckpb.AnchorCheckData{{Uid: 1, Level: "S"}},
	}
	certresp := &anchorcontractpb.ContractCacheInfo{
		Contract: &anchorcontractpb.ContractInfo{
			GuildId:  1,
			SignTime: uint32(time.Date(2022, 1, 1, 1, 0, 0, 0, time.Local).Unix()),
		},
	}
	level := anchorlevelpb.ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_A
	gomock.InOrder(
		mockcheckcli.EXPECT().GetAnchorCheckHistory(gomock.Any(), gomock.Any()).Return(checkresp, nil),
		mockcontractcli.EXPECT().GetUserContractCacheInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(certresp, nil),
		mockanchorlevelcli.EXPECT().GetAnchorLevel(gomock.Any(), gomock.Any()).Return(level, nil),
		// GetAnchorLevel
	)

	type args struct {
		authInfo *web.AuthInfo
		w        http.ResponseWriter
		r        *http.Request
	}
	tests := []struct {
		name string
		args args
	}{

		{
			args: args{
				authInfo: &web.AuthInfo{},
				w:        &indexHandler{},
				r:        &http.Request{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client.AnchorCheckCli = mockcheckcli
			client.AnchorContractClient = mockcontractcli
			client.AnchorLevelCli = mockanchorlevelcli
			GetAnchorCheckInfo(tt.args.authInfo, tt.args.w, tt.args.r)
		})
	}
}

// go test -timeout 30s -run ^TestAnchorCertInit$ golang.52tt.com/services/channel-live/channel-live-http-logic/control/anchor_cert -v
func TestAnchorCertInit(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockcheckcli := mockcheck.NewMockIClient(ctl)
	mockcontractcli := mockanchorcontract.NewMockIClient(ctl)
	mocklivemgrcli := mockChannelLiveMgr.NewMockIClient(ctl)

	liveresp := &channellivemgr.GetChannelLiveInfoResp{
		ChannelLiveInfo: &channellivemgr.ChannelLiveInfo{TagId: 3001},
	}
	checkresp := &AnchorCheckpb.GetAnchorCheckHistoryResp{
		List: []*AnchorCheckpb.AnchorCheckData{{Uid: 1, Level: "S"}},
	}
	certresp := &anchorcontractpb.GetAnchorCertTaskInfoResp{
		OutCert: &anchorcontractpb.ExamineCertMsg{
			ItemName: "X",
		},
		CenterCert: &anchorcontractpb.ExamineCertMsg{
			ItemName: "X1",
		},
	}
	gomock.InOrder(
		mockcheckcli.EXPECT().GetAnchorCheckHistory(gomock.Any(), gomock.Any()).Return(checkresp, nil),
		mocklivemgrcli.EXPECT().GetChannelLiveInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(liveresp, nil),
		mockcontractcli.EXPECT().GetAnchorCertTaskInfo(gomock.Any(), gomock.Any()).Return(certresp, nil),
	)

	type args struct {
		authInfo *web.AuthInfo
		w        http.ResponseWriter
		r        *http.Request
	}
	tests := []struct {
		name string
		args args
	}{

		{
			args: args{
				authInfo: &web.AuthInfo{},
				w:        &indexHandler{},
				r:        &http.Request{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client.AnchorCheckCli = mockcheckcli
			client.AnchorContractClient = mockcontractcli
			client.LiveMgrCli = mocklivemgrcli
			AnchorCertInit(tt.args.authInfo, tt.args.w, tt.args.r)
		})
	}
}

// go test -timeout 30s -run ^TestGetAnchorCert$ golang.52tt.com/services/channel-live/channel-live-http-logic/control/anchor_cert -v
//func TestGetAnchorCert(t *testing.T) {
//	ctl := gomock.NewController(t)
//	defer ctl.Finish()
//	mocklivestatscli := mockChannelLiveStats.NewMockIClient(ctl)
//	mocklivemgrcli := mockChannelLiveMgr.NewMockIClient(ctl)
//	statsresp := &channellivestatspb.BatchGetAnchorMonthlyStatsByUidResp{}
//	liveresp := &channellivemgr.GetChannelLiveInfoResp{}
//	gomock.InOrder(
//		mocklivemgrcli.EXPECT().GetChannelLiveInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(liveresp, nil),
//		mocklivestatscli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(statsresp, nil),
//	)
//
//	type args struct {
//		authInfo *web.AuthInfo
//		w        http.ResponseWriter
//		r        *http.Request
//	}
//	tests := []struct {
//		name string
//		args args
//	}{
//
//		{
//			args: args{
//				authInfo: &web.AuthInfo{},
//				w:        &indexHandler{},
//				r:        &http.Request{},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			client.LiveStatsCli = mocklivestatscli
//			client.LiveMgrCli = mocklivemgrcli
//			GetAnchorCertInfo(tt.args.authInfo, tt.args.w, tt.args.r)
//		})
//	}
//}
