package show_list

import (
    "context"
    "encoding/json"
    "net/http"
    "time"

    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/pkg/web"
    pb "golang.52tt.com/protocol/services/channel-live-http-logic"
    channel_live_show_list "golang.52tt.com/protocol/services/channel-live-show-list"
    "golang.52tt.com/protocol/services/channellivemgr"
    "golang.52tt.com/services/channel-live/channel-live-http-logic/client"
)

func GetShowTime(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
    defer cancel()

    uid := authInfo.UserID

    req := &pb.GetShowTimeRequest{}
    err := json.Unmarshal(authInfo.Body, &req)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetShowTime Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
        web.ServeBadReq(w)
        return
    }
    log.DebugWithCtx(ctx, "GetShowTime begin authInfo:%v req:%v", authInfo, req)

    showTimeResp, err := client.ChannelLiveShowList.GetShowTime(ctx, &channel_live_show_list.GetShowTimeRequest{
        Datetime: req.GetDatetime(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetShowTime failed uid:%d err:%v", uid, err)
        sErr := protocol.ToServerError(err)
        _ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
        return
    }

    httpResp := &pb.GetShowTimeResponse{
        EarliestSelectableTime: showTimeResp.GetEarliestSelectableTime(),
    }
    for _, item := range showTimeResp.GetShowTimeList() {
        httpResp.ShowTimeList = append(httpResp.ShowTimeList, &pb.GetShowTimeResponse_ShowTime{
            ShowStartTime: item.GetShowStartTime(),
            ShowEndTime:   item.GetShowEndTime(),
            RemainCnt:     item.GetRemainCnt(),
        })
    }

    _ = web.ServeAPIJson(w, httpResp)
    log.DebugWithCtx(ctx, "GetShowTime uid %d resp:%v", uid, httpResp)
}
func GetShowTag(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
    defer cancel()

    uid := authInfo.UserID

    req := &pb.GetShowTagRequest{}
    err := json.Unmarshal(authInfo.Body, &req)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetShowTag Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
        web.ServeBadReq(w)
        return
    }
    log.DebugWithCtx(ctx, "GetShowTag begin authInfo:%v req:%v", authInfo, req)

    showTagResp, err := client.ChannelLiveShowList.GetShowTag(ctx, &channel_live_show_list.GetShowTagRequest{})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetShowTag failed uid:%d err:%v", uid, err)
        sErr := protocol.ToServerError(err)
        _ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
        return
    }

    httpResp := &pb.GetShowTagResponse{
        VoiceTagList:    make([]*pb.GetShowTagResponse_TagNode, 0),
        ContentTagList:  make([]*pb.GetShowTagResponse_TagNode, 0),
        ServerTimestamp: uint32(time.Now().Unix()),
    }
    // dfs 复制树
    dfsCopyTree(showTagResp.GetVoiceTagList(), &httpResp.VoiceTagList)
    dfsCopyTree(showTagResp.GetContentTagList(), &httpResp.ContentTagList)

    log.DebugWithCtx(ctx, "GetShowTag uid %d resp:%v", uid, httpResp)
    _ = web.ServeAPIJson(w, httpResp)
}

func dfsCopyTree(src []*channel_live_show_list.TagNode, dst *[]*pb.GetShowTagResponse_TagNode) {
    for _, item := range src {
        newNode := &pb.GetShowTagResponse_TagNode{
            TagId:   item.GetTagId(),
            TagName: item.GetTagName(),
        }
        *dst = append(*dst, newNode)

        dfsCopyTree(item.GetChildList(), &newNode.ChildList)
    }
    log.Debugf("dfsCopyTree src:%v dst:%v", src, dst)
}

func DeclareShow(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
    defer cancel()
    uid := authInfo.UserID

    req := &pb.DeclareShowRequest{}
    err := json.Unmarshal(authInfo.Body, &req)
    if err != nil {
        log.ErrorWithCtx(ctx, "DeclareShow Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
        web.ServeBadReq(w)
        return
    }

    ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: authInfo.UserID})

    log.DebugWithCtx(ctx, "DeclareShow begin authInfo:%v req:%v", authInfo, req)
    _, err = client.ChannelLiveShowList.DeclareShow(ctx, &channel_live_show_list.DeclareShowRequest{
        ShowName:              req.GetShowName(),
        ShowStartTime:         req.GetShowStartTime(),
        ShowEndTime:           req.GetShowEndTime(),
        VoiceTagId:            req.GetVoiceTagId(),
        ContentTagId:          req.GetContentTagId(),
        ShowDescAudio:         req.GetShowDescAudio(),
        ShowDescAudioDuration: req.ShowDescAudioDuration,
        ShowCoverImg:          req.GetShowCoverImg(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "DeclareShow failed uid:%d err:%v", uid, err)
        sErr := protocol.ToServerError(err)
        _ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
        return
    }

    _ = web.ServeAPIJson(w, nil)
    log.DebugWithCtx(ctx, "DeclareShow uid %d req:%v", uid, req)
}

func GetShowList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
    defer cancel()

    uid := authInfo.UserID

    req := &pb.GetShowListRequest{}
    err := json.Unmarshal(authInfo.Body, &req)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetShowList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
        web.ServeBadReq(w)
        return
    }
    log.DebugWithCtx(ctx, "GetShowList begin authInfo:%v req:%v", authInfo, req)

    showListResp, err := client.ChannelLiveShowList.GetShowList(ctx, &channel_live_show_list.GetShowListRequest{
        Datetime:         req.GetDatetime(),
        VoiceTagIdList:   req.GetVoiceTagId(),
        ContentTagIdList: req.GetContentTagId(),
        Uid:              authInfo.UserID,
    })
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "GetShowList failed uid:%d err:%v", uid, err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    httpResp := &pb.GetShowListResponse{
        ShowList: make([]*pb.GetShowListResponse_ShowGroupItem, 0),
    }
    uidList := make([]uint32, 0, 50)
    cidList := make([]uint32, 0, 50)
    for _, group := range showListResp.GetShowList() {
        for _, item := range group.GetShowItem() {
            uidList = append(uidList, item.GetUid())
            cidList = append(cidList, item.GetChannelId())
        }
    }

    userMap, err := client.AccountCli.GetUsersMap(ctx, uidList)
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "GetShowList GetUsersMap failed uid:%d err:%v", uid, err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    // channelLiveStatusMap
    channelLiveStatusMap := make(map[uint32]uint32)

    var isCache bool
    // 首页时走缓存，需实时查询房间直播状态
    if len(req.GetVoiceTagId()) == 0 && len(req.GetContentTagId()) == 0 {
        isCache = true
        // channelLiveStatus
        channelLiveResp, err := client.LiveMgrCli.BatchGetChannelLiveStatusSimple(ctx, channellivemgr.BatchGetChannelLiveStatusSimpleReq{
            ChannelList: cidList,
        })
        if err != nil {
            e := protocol.ToServerError(err)
            log.ErrorWithCtx(ctx, "GetShowList BatchGetChannelLiveStatusSimple failed cidList:%d err:%v", len(cidList), err)
            _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
            return
        }

        for _, channelLiveStatus := range channelLiveResp.GetChannelLiveStatusList() {
            channelLiveStatusMap[channelLiveStatus.GetChannelId()] = channelLiveStatus.GetStatus()
        }
    }

    // accountList
    accountList := make([]string, 0)
    for _, userInfo := range userMap {
        accountList = append(accountList, userInfo.GetUsername())
    }

    imageMap, err := client.HeadImageCli.BatchGetHeadImageMd5(ctx, 1024, accountList)
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "GetShowList BatchGetHeadImageMd5 failed uid:%d err:%v", uid, err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    for _, group := range showListResp.GetShowList() {
        itemList := make([]*pb.ShowItem, 0, len(group.GetShowItem()))
        for _, item := range group.GetShowItem() {
            var liveStatus uint32
            if isCache {
                liveStatus = channelLiveStatusMap[item.GetChannelId()]
            } else {
                liveStatus = item.GetLiveStatus()
            }
            itemList = append(itemList, &pb.ShowItem{
                ShowId:        item.GetShowId(),
                ShowName:      item.GetShowName(),
                ShowDescAudio: item.GetShowDescAudio(),
                ShowCoverImg:  item.GetShowCoverImg(),
                LiveStatus:    liveStatus,
                Account:       userMap[item.GetUid()].GetUsername(),
                Nickname:      userMap[item.GetUid()].GetNickname(),
                Avatar:        imageMap[userMap[item.GetUid()].GetUsername()],
                ChannelId:     item.GetChannelId(),
                AudioDuration: item.GetAudioDuration(),
                ContentId:     item.GetContentId(),
                VoiceId:       item.GetVoiceId(),
                HotValue:      item.GetHotValue(),
            })
        }
        httpResp.ShowList = append(httpResp.ShowList, &pb.GetShowListResponse_ShowGroupItem{
            TimeRange: group.GetTimeRange(),
            ShowList:  itemList,
            HasMore:   group.GetHasMore(),
            IsEnd:     group.GetIsEnd(),
            BeginTime: group.GetBeginTime(),
            EndTime:   group.GetEndTime(),
        })
    }

    _ = web.ServeAPIJson(w, httpResp)
    log.DebugWithCtx(ctx, "GetShowList uid %d resp:%v", uid, httpResp)
}

func GetShowListUserBaseInfo(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
    defer cancel()
    uid := authInfo.UserID

    log.DebugWithCtx(ctx, "GetShowListUserBaseInfo begin authInfo:%v", authInfo)
    svrResp, err := client.ChannelLiveShowList.GetShowListUserBaseInfo(ctx, &channel_live_show_list.GetShowListUserBaseInfoRequest{
        Uid: authInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetShowListUserBaseInfo failed uid:%d err:%v", uid, err)
        sErr := protocol.ToServerError(err)
        _ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
        return
    }

    resp := &pb.GetShowListUserBaseInfoResponse{
        RemainDeclareCnt: svrResp.GetRemainDeclareCnt(),
        ShowApplyEntry:   svrResp.GetShowApplyEntry(),
        UnableReason:     svrResp.GetUnableReason(),
    }

    _ = web.ServeAPIJson(w, resp)
    log.DebugWithCtx(ctx, "GetShowListUserBaseInfo uid %d", uid)
}

func UnfoldShowList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
    defer cancel()

    uid := authInfo.UserID

    req := &pb.UnfoldShowListRequest{}
    err := json.Unmarshal(authInfo.Body, &req)
    if err != nil {
        log.ErrorWithCtx(ctx, "UnfoldShowList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
        web.ServeBadReq(w)
        return
    }
    log.DebugWithCtx(ctx, "UnfoldShowList begin authInfo:%v req:%v", authInfo, req)

    unfoldResp, err := client.ChannelLiveShowList.UnfoldShowList(ctx, &channel_live_show_list.UnfoldShowListRequest{
        BeginTime:        req.GetBeginTime(),
        EndTime:          req.GetEndTime(),
        VoiceTagIdList:   req.GetVoiceTagId(),
        ContentTagIdList: req.GetContentTagId(),
    })
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "UnfoldShowList failed uid:%d err:%v", uid, err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    uidList := make([]uint32, 0, len(unfoldResp.GetUnfoldShowList()))
    cidList := make([]uint32, 0, len(unfoldResp.GetUnfoldShowList()))
    for _, item := range unfoldResp.GetUnfoldShowList() {
        uidList = append(uidList, item.GetUid())
        cidList = append(cidList, item.GetChannelId())
    }

    userMap, err := client.AccountCli.GetUsersMap(ctx, uidList)
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "UnfoldShowList GetUsersMap failed uid:%d err:%v", uid, err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    // accountList
    accountList := make([]string, 0)
    for _, userInfo := range userMap {
        accountList = append(accountList, userInfo.GetUsername())
    }

    imageMap, err := client.HeadImageCli.BatchGetHeadImageMd5(ctx, 1024, accountList)
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "UnfoldShowList BatchGetHeadImageMd5 failed uid:%d err:%v", uid, err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    resp := &pb.UnfoldShowListResponse{}
    for _, show := range unfoldResp.GetUnfoldShowList() {
        resp.ShowList = append(resp.ShowList, &pb.ShowItem{
            ShowId:        show.GetShowId(),
            ShowName:      show.GetShowName(),
            ShowDescAudio: show.GetShowDescAudio(),
            ShowCoverImg:  show.GetShowCoverImg(),
            LiveStatus:    show.GetLiveStatus(),
            Account:       userMap[show.GetUid()].GetUsername(),
            Nickname:      userMap[show.GetUid()].GetNickname(),
            Avatar:        imageMap[userMap[show.GetUid()].GetUsername()],
            ChannelId:     show.GetChannelId(),
            AudioDuration: show.GetAudioDuration(),
            ContentId:     show.GetContentId(),
            VoiceId:       show.GetVoiceId(),
            HotValue:      show.GetHotValue(),
        })
    }

    _ = web.ServeAPIJson(w, resp)
    log.DebugWithCtx(ctx, "UnfoldShowList uid %d req:%+v, resp:%+v", uid, req, resp)
}
