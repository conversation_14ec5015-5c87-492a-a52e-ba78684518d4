package show_list

//func TestIsShowEnd(t *testing.T) {
//    type args struct {
//        now          time.Time
//        timeRangeStr string
//    }
//    tests := []struct {
//        name string
//        args args
//        want bool
//    }{
//        {
//            name: "test1",
//            args: args{
//                now:          time.Now(),
//                timeRangeStr: "09:00-10:00",
//            },
//            want: true,
//        },
//        {
//            name: "test2",
//            args: args{
//                now:          time.Now(),
//                timeRangeStr: "16:00-17:00",
//            },
//            want: false,
//        },
//    }
//    for _, tt := range tests {
//        t.Run(tt.name, func(t *testing.T) {
//            if got := IsShowEnd(tt.args.now, tt.args.timeRangeStr); got != tt.want {
//                t.Errorf("IsShowEnd() = %v, want %v", got, tt.want)
//            }
//        })
//    }
//}
