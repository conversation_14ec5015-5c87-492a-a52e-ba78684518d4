package main

import (
    "context"
    "gitlab.ttyuyin.com/tyr/tt-ecosystem/startup/suit/grpc/server" // server startup
    "gitlab.ttyuyin.com/tyr/x/log"
    "google.golang.org/grpc"

    pb "golang.52tt.com/protocol/services/channel-live-show-list"

    "golang.52tt.com/services/channel-live-show-list/internal"

    _ "golang.52tt.com/pkg/hub/tyr/compatible/server"
    "golang.52tt.com/services/channel-live-show-list/internal/conf"
)

func main() {
    var (
        svr *internal.Server
        cfg = &conf.StartConfig{}
        err error
    )

    // config file support yaml & json, default channel-live-show-list.json/yaml
    if err := grpcserver.NewServer("channel-live-show-list", cfg).
        AddGrpcServer(grpcserver.NewPlugin().
            WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
                if svr, err = internal.NewServer(ctx, cfg); err != nil {
                    return err
                }

                // register custom grpc server
                pb.RegisterChannelLiveShowListServer(s, svr)
                return nil
            }).
            WithEventLinkSubscription(),
        ).
        WithCloseFunc(func(ctx context.Context) {
            // do something when server terminating
            svr.ShutDown()
        }).
        Start(); err != nil {
        log.Errorf("server start fail, err: %v", err)
    }
}
