// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-live-show-list/internal/model/anti-corruption-layer (interfaces: IACLayer)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	app "golang.52tt.com/protocol/app"
	channel "golang.52tt.com/protocol/app/channel"
	Account "golang.52tt.com/protocol/services/accountsvr"
	channel_go "golang.52tt.com/protocol/services/channel-go"
	channel_live_show_list "golang.52tt.com/protocol/services/channel-live-show-list"
	v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	im_api "golang.52tt.com/protocol/services/im-api"
)

// MockIACLayer is a mock of IACLayer interface.
type MockIACLayer struct {
	ctrl     *gomock.Controller
	recorder *MockIACLayerMockRecorder
}

// MockIACLayerMockRecorder is the mock recorder for MockIACLayer.
type MockIACLayerMockRecorder struct {
	mock *MockIACLayer
}

// NewMockIACLayer creates a new mock instance.
func NewMockIACLayer(ctrl *gomock.Controller) *MockIACLayer {
	mock := &MockIACLayer{ctrl: ctrl}
	mock.recorder = &MockIACLayerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIACLayer) EXPECT() *MockIACLayerMockRecorder {
	return m.recorder
}

// AsyncScanAudio mocks base method.
func (m *MockIACLayer) AsyncScanAudio(arg0 context.Context, arg1 *v2.ScanAudioReq) (*v2.ScanAudioResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AsyncScanAudio", arg0, arg1)
	ret0, _ := ret[0].(*v2.ScanAudioResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AsyncScanAudio indicates an expected call of AsyncScanAudio.
func (mr *MockIACLayerMockRecorder) AsyncScanAudio(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncScanAudio", reflect.TypeOf((*MockIACLayer)(nil).AsyncScanAudio), arg0, arg1)
}

// AsyncScanImage mocks base method.
func (m *MockIACLayer) AsyncScanImage(arg0 context.Context, arg1 *v2.ScanImageReq) (*v2.ScanImageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AsyncScanImage", arg0, arg1)
	ret0, _ := ret[0].(*v2.ScanImageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AsyncScanImage indicates an expected call of AsyncScanImage.
func (mr *MockIACLayerMockRecorder) AsyncScanImage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncScanImage", reflect.TypeOf((*MockIACLayer)(nil).AsyncScanImage), arg0, arg1)
}

// BatGetChannelLiveStatus mocks base method.
func (m *MockIACLayer) BatGetChannelLiveStatus(arg0 context.Context, arg1 []uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetChannelLiveStatus", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetChannelLiveStatus indicates an expected call of BatGetChannelLiveStatus.
func (mr *MockIACLayerMockRecorder) BatGetChannelLiveStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetChannelLiveStatus", reflect.TypeOf((*MockIACLayer)(nil).BatGetChannelLiveStatus), arg0, arg1)
}

// BatchGetChannelHot mocks base method.
func (m *MockIACLayer) BatchGetChannelHot(arg0 context.Context, arg1 []uint32) (map[uint32]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelHot", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelHot indicates an expected call of BatchGetChannelHot.
func (mr *MockIACLayerMockRecorder) BatchGetChannelHot(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelHot", reflect.TypeOf((*MockIACLayer)(nil).BatchGetChannelHot), arg0, arg1)
}

// BatchGetUserProfile mocks base method.
func (m *MockIACLayer) BatchGetUserProfile(arg0 context.Context, arg1 []uint32) (map[uint32]*app.UserProfile, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserProfile", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*app.UserProfile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserProfile indicates an expected call of BatchGetUserProfile.
func (mr *MockIACLayerMockRecorder) BatchGetUserProfile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserProfile", reflect.TypeOf((*MockIACLayer)(nil).BatchGetUserProfile), arg0, arg1)
}

// GetAccountUser mocks base method.
func (m *MockIACLayer) GetAccountUser(arg0 context.Context, arg1 uint32) (*Account.UserResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountUser", arg0, arg1)
	ret0, _ := ret[0].(*Account.UserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountUser indicates an expected call of GetAccountUser.
func (mr *MockIACLayerMockRecorder) GetAccountUser(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountUser", reflect.TypeOf((*MockIACLayer)(nil).GetAccountUser), arg0, arg1)
}

// GetAnchorChannelId mocks base method.
func (m *MockIACLayer) GetAnchorChannelId(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorChannelId", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorChannelId indicates an expected call of GetAnchorChannelId.
func (mr *MockIACLayerMockRecorder) GetAnchorChannelId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorChannelId", reflect.TypeOf((*MockIACLayer)(nil).GetAnchorChannelId), arg0, arg1)
}

// GetSimpleChannelInfo mocks base method.
func (m *MockIACLayer) GetSimpleChannelInfo(arg0 context.Context, arg1 uint32) (*channel_go.ChannelSimpleInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSimpleChannelInfo", arg0, arg1)
	ret0, _ := ret[0].(*channel_go.ChannelSimpleInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSimpleChannelInfo indicates an expected call of GetSimpleChannelInfo.
func (mr *MockIACLayerMockRecorder) GetSimpleChannelInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSimpleChannelInfo", reflect.TypeOf((*MockIACLayer)(nil).GetSimpleChannelInfo), arg0, arg1)
}

// GetUidByAlias mocks base method.
func (m *MockIACLayer) GetUidByAlias(arg0 context.Context, arg1 string) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUidByAlias", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUidByAlias indicates an expected call of GetUidByAlias.
func (mr *MockIACLayerMockRecorder) GetUidByAlias(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUidByAlias", reflect.TypeOf((*MockIACLayer)(nil).GetUidByAlias), arg0, arg1)
}

// GetUidByName mocks base method.
func (m *MockIACLayer) GetUidByName(arg0 context.Context, arg1 string) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUidByName", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUidByName indicates an expected call of GetUidByName.
func (mr *MockIACLayerMockRecorder) GetUidByName(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUidByName", reflect.TypeOf((*MockIACLayer)(nil).GetUidByName), arg0, arg1)
}

// GetUserProfile mocks base method.
func (m *MockIACLayer) GetUserProfile(arg0 context.Context, arg1 uint32) (*app.UserProfile, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProfile", arg0, arg1)
	ret0, _ := ret[0].(*app.UserProfile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserProfile indicates an expected call of GetUserProfile.
func (mr *MockIACLayerMockRecorder) GetUserProfile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfile", reflect.TypeOf((*MockIACLayer)(nil).GetUserProfile), arg0, arg1)
}

// GetUserProfileMap mocks base method.
func (m *MockIACLayer) GetUserProfileMap(arg0 context.Context, arg1 []uint32, arg2 bool) (map[uint32]*app.UserProfile, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProfileMap", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]*app.UserProfile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserProfileMap indicates an expected call of GetUserProfileMap.
func (mr *MockIACLayerMockRecorder) GetUserProfileMap(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfileMap", reflect.TypeOf((*MockIACLayer)(nil).GetUserProfileMap), arg0, arg1, arg2)
}

// IsUserInApplyGroup mocks base method.
func (m *MockIACLayer) IsUserInApplyGroup(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsUserInApplyGroup", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsUserInApplyGroup indicates an expected call of IsUserInApplyGroup.
func (mr *MockIACLayerMockRecorder) IsUserInApplyGroup(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUserInApplyGroup", reflect.TypeOf((*MockIACLayer)(nil).IsUserInApplyGroup), arg0, arg1)
}

// PushShowBeginToChannel mocks base method.
func (m *MockIACLayer) PushShowBeginToChannel(arg0 context.Context, arg1 uint32, arg2 *channel_live_show_list.LiveShowInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushShowBeginToChannel", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushShowBeginToChannel indicates an expected call of PushShowBeginToChannel.
func (mr *MockIACLayerMockRecorder) PushShowBeginToChannel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushShowBeginToChannel", reflect.TypeOf((*MockIACLayer)(nil).PushShowBeginToChannel), arg0, arg1, arg2)
}

// SendChannelCommonPublicMsg mocks base method.
func (m *MockIACLayer) SendChannelCommonPublicMsg(arg0 context.Context, arg1, arg2 uint32, arg3 *channel.CommonHighLightChannelIm) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendChannelCommonPublicMsg", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendChannelCommonPublicMsg indicates an expected call of SendChannelCommonPublicMsg.
func (mr *MockIACLayerMockRecorder) SendChannelCommonPublicMsg(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendChannelCommonPublicMsg", reflect.TypeOf((*MockIACLayer)(nil).SendChannelCommonPublicMsg), arg0, arg1, arg2, arg3)
}

// SendOfficialAccountMsg mocks base method.
func (m *MockIACLayer) SendOfficialAccountMsg(arg0 context.Context, arg1 string, arg2 uint32, arg3 *im_api.Text) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendOfficialAccountMsg", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendOfficialAccountMsg indicates an expected call of SendOfficialAccountMsg.
func (mr *MockIACLayerMockRecorder) SendOfficialAccountMsg(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOfficialAccountMsg", reflect.TypeOf((*MockIACLayer)(nil).SendOfficialAccountMsg), arg0, arg1, arg2, arg3)
}

// SimplePushToChannel mocks base method.
func (m *MockIACLayer) SimplePushToChannel(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 string, arg5 []byte) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimplePushToChannel", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimplePushToChannel indicates an expected call of SimplePushToChannel.
func (mr *MockIACLayerMockRecorder) SimplePushToChannel(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimplePushToChannel", reflect.TypeOf((*MockIACLayer)(nil).SimplePushToChannel), arg0, arg1, arg2, arg3, arg4, arg5)
}

// Stop mocks base method.
func (m *MockIACLayer) Stop() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Stop")
}

// Stop indicates an expected call of Stop.
func (mr *MockIACLayerMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockIACLayer)(nil).Stop))
}

// SyncScanText mocks base method.
func (m *MockIACLayer) SyncScanText(arg0 context.Context, arg1 *v2.SyncTextCheckReq) (*v2.SyncTextCheckResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncScanText", arg0, arg1)
	ret0, _ := ret[0].(*v2.SyncTextCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncScanText indicates an expected call of SyncScanText.
func (mr *MockIACLayerMockRecorder) SyncScanText(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncScanText", reflect.TypeOf((*MockIACLayer)(nil).SyncScanText), arg0, arg1)
}
