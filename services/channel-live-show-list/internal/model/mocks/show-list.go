// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-live-show-list/internal/model/show-list (interfaces: IShowListMgr)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	channel_live_show_list "golang.52tt.com/protocol/services/channel-live-show-list"
	entity "golang.52tt.com/services/channel-live-show-list/internal/entity"
	store "golang.52tt.com/services/channel-live-show-list/internal/model/show-list/store"
)

// MockIShowListMgr is a mock of IShowListMgr interface.
type MockIShowListMgr struct {
	ctrl     *gomock.Controller
	recorder *MockIShowListMgrMockRecorder
}

// MockIShowListMgrMockRecorder is the mock recorder for MockIShowListMgr.
type MockIShowListMgrMockRecorder struct {
	mock *MockIShowListMgr
}

// NewMockIShowListMgr creates a new mock instance.
func NewMockIShowListMgr(ctrl *gomock.Controller) *MockIShowListMgr {
	mock := &MockIShowListMgr{ctrl: ctrl}
	mock.recorder = &MockIShowListMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIShowListMgr) EXPECT() *MockIShowListMgrMockRecorder {
	return m.recorder
}

// AddChannelImNotify mocks base method.
func (m *MockIShowListMgr) AddChannelImNotify(arg0 context.Context, arg1 *entity.ChannelImNotify) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChannelImNotify", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddChannelImNotify indicates an expected call of AddChannelImNotify.
func (mr *MockIShowListMgrMockRecorder) AddChannelImNotify(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelImNotify", reflect.TypeOf((*MockIShowListMgr)(nil).AddChannelImNotify), arg0, arg1)
}

// AddChannelLiveShowTag mocks base method.
func (m *MockIShowListMgr) AddChannelLiveShowTag(arg0 context.Context, arg1 uint32, arg2 string, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChannelLiveShowTag", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddChannelLiveShowTag indicates an expected call of AddChannelLiveShowTag.
func (mr *MockIShowListMgrMockRecorder) AddChannelLiveShowTag(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelLiveShowTag", reflect.TypeOf((*MockIShowListMgr)(nil).AddChannelLiveShowTag), arg0, arg1, arg2, arg3)
}

// AddShowTag mocks base method.
func (m *MockIShowListMgr) AddShowTag(arg0 context.Context, arg1 *channel_live_show_list.AddShowTagRequest) (*channel_live_show_list.AddShowTagResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddShowTag", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_show_list.AddShowTagResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddShowTag indicates an expected call of AddShowTag.
func (mr *MockIShowListMgrMockRecorder) AddShowTag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddShowTag", reflect.TypeOf((*MockIShowListMgr)(nil).AddShowTag), arg0, arg1)
}

// DeclareShow mocks base method.
func (m *MockIShowListMgr) DeclareShow(arg0 context.Context, arg1 *channel_live_show_list.DeclareShowRequest) (*channel_live_show_list.DeclareShowResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeclareShow", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_show_list.DeclareShowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeclareShow indicates an expected call of DeclareShow.
func (mr *MockIShowListMgrMockRecorder) DeclareShow(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeclareShow", reflect.TypeOf((*MockIShowListMgr)(nil).DeclareShow), arg0, arg1)
}

// DeleteChannelLiveShowTag mocks base method.
func (m *MockIShowListMgr) DeleteChannelLiveShowTag(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteChannelLiveShowTag", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteChannelLiveShowTag indicates an expected call of DeleteChannelLiveShowTag.
func (mr *MockIShowListMgrMockRecorder) DeleteChannelLiveShowTag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteChannelLiveShowTag", reflect.TypeOf((*MockIShowListMgr)(nil).DeleteChannelLiveShowTag), arg0, arg1)
}

// DeleteShowTag mocks base method.
func (m *MockIShowListMgr) DeleteShowTag(arg0 context.Context, arg1 *channel_live_show_list.DeleteShowTagRequest) (*channel_live_show_list.DeleteShowTagResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteShowTag", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_show_list.DeleteShowTagResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteShowTag indicates an expected call of DeleteShowTag.
func (mr *MockIShowListMgrMockRecorder) DeleteShowTag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteShowTag", reflect.TypeOf((*MockIShowListMgr)(nil).DeleteShowTag), arg0, arg1)
}

// GetChannelLiveShowApprovalList mocks base method.
func (m *MockIShowListMgr) GetChannelLiveShowApprovalList(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 uint32) (*channel_live_show_list.GetChannelLiveShowApprovalListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveShowApprovalList", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*channel_live_show_list.GetChannelLiveShowApprovalListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveShowApprovalList indicates an expected call of GetChannelLiveShowApprovalList.
func (mr *MockIShowListMgrMockRecorder) GetChannelLiveShowApprovalList(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowApprovalList", reflect.TypeOf((*MockIShowListMgr)(nil).GetChannelLiveShowApprovalList), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetChannelLiveShowByApprovalId mocks base method.
func (m *MockIShowListMgr) GetChannelLiveShowByApprovalId(arg0 context.Context, arg1 uint32) (*store.ChannelLiveShow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveShowByApprovalId", arg0, arg1)
	ret0, _ := ret[0].(*store.ChannelLiveShow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveShowByApprovalId indicates an expected call of GetChannelLiveShowByApprovalId.
func (mr *MockIShowListMgrMockRecorder) GetChannelLiveShowByApprovalId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowByApprovalId", reflect.TypeOf((*MockIShowListMgr)(nil).GetChannelLiveShowByApprovalId), arg0, arg1)
}

// GetChannelLiveShowInfoById mocks base method.
func (m *MockIShowListMgr) GetChannelLiveShowInfoById(arg0 context.Context, arg1 uint32) (*channel_live_show_list.ShowItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveShowInfoById", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_show_list.ShowItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveShowInfoById indicates an expected call of GetChannelLiveShowInfoById.
func (mr *MockIShowListMgrMockRecorder) GetChannelLiveShowInfoById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowInfoById", reflect.TypeOf((*MockIShowListMgr)(nil).GetChannelLiveShowInfoById), arg0, arg1)
}

// GetChannelLiveShowListEnd mocks base method.
func (m *MockIShowListMgr) GetChannelLiveShowListEnd(arg0 context.Context, arg1, arg2 uint32) ([]*store.ChannelLiveShow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveShowListEnd", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.ChannelLiveShow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveShowListEnd indicates an expected call of GetChannelLiveShowListEnd.
func (mr *MockIShowListMgrMockRecorder) GetChannelLiveShowListEnd(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowListEnd", reflect.TypeOf((*MockIShowListMgr)(nil).GetChannelLiveShowListEnd), arg0, arg1, arg2)
}

// GetChannelLiveTagTree mocks base method.
func (m *MockIShowListMgr) GetChannelLiveTagTree(arg0 context.Context) (*channel_live_show_list.TagNode, map[uint32]*channel_live_show_list.TagNode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveTagTree", arg0)
	ret0, _ := ret[0].(*channel_live_show_list.TagNode)
	ret1, _ := ret[1].(map[uint32]*channel_live_show_list.TagNode)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetChannelLiveTagTree indicates an expected call of GetChannelLiveTagTree.
func (mr *MockIShowListMgrMockRecorder) GetChannelLiveTagTree(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveTagTree", reflect.TypeOf((*MockIShowListMgr)(nil).GetChannelLiveTagTree), arg0)
}

// GetLiveShowEntryInfo mocks base method.
func (m *MockIShowListMgr) GetLiveShowEntryInfo(arg0 context.Context, arg1 *channel_live_show_list.GetLiveShowEntryInfoRequest) (*channel_live_show_list.GetLiveShowEntryInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiveShowEntryInfo", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_show_list.GetLiveShowEntryInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLiveShowEntryInfo indicates an expected call of GetLiveShowEntryInfo.
func (mr *MockIShowListMgrMockRecorder) GetLiveShowEntryInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveShowEntryInfo", reflect.TypeOf((*MockIShowListMgr)(nil).GetLiveShowEntryInfo), arg0, arg1)
}

// GetNearlyChannelLiveShowInfo mocks base method.
func (m *MockIShowListMgr) GetNearlyChannelLiveShowInfo(arg0 context.Context, arg1 uint32) (*channel_live_show_list.ShowItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNearlyChannelLiveShowInfo", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_show_list.ShowItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNearlyChannelLiveShowInfo indicates an expected call of GetNearlyChannelLiveShowInfo.
func (mr *MockIShowListMgrMockRecorder) GetNearlyChannelLiveShowInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNearlyChannelLiveShowInfo", reflect.TypeOf((*MockIShowListMgr)(nil).GetNearlyChannelLiveShowInfo), arg0, arg1)
}

// GetRelatedTagNodeId mocks base method.
func (m *MockIShowListMgr) GetRelatedTagNodeId(arg0 context.Context, arg1 []uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelatedTagNodeId", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelatedTagNodeId indicates an expected call of GetRelatedTagNodeId.
func (mr *MockIShowListMgrMockRecorder) GetRelatedTagNodeId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelatedTagNodeId", reflect.TypeOf((*MockIShowListMgr)(nil).GetRelatedTagNodeId), arg0, arg1)
}

// GetShowList mocks base method.
func (m *MockIShowListMgr) GetShowList(arg0 context.Context, arg1 *channel_live_show_list.GetShowListRequest) (*channel_live_show_list.GetShowListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowList", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_show_list.GetShowListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowList indicates an expected call of GetShowList.
func (mr *MockIShowListMgrMockRecorder) GetShowList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowList", reflect.TypeOf((*MockIShowListMgr)(nil).GetShowList), arg0, arg1)
}

// GetShowTag mocks base method.
func (m *MockIShowListMgr) GetShowTag(arg0 context.Context) (*channel_live_show_list.GetShowTagResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowTag", arg0)
	ret0, _ := ret[0].(*channel_live_show_list.GetShowTagResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowTag indicates an expected call of GetShowTag.
func (mr *MockIShowListMgrMockRecorder) GetShowTag(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowTag", reflect.TypeOf((*MockIShowListMgr)(nil).GetShowTag), arg0)
}

// GetShowTime mocks base method.
func (m *MockIShowListMgr) GetShowTime(arg0 context.Context, arg1 uint32) (*channel_live_show_list.GetShowTimeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowTime", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_show_list.GetShowTimeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowTime indicates an expected call of GetShowTime.
func (mr *MockIShowListMgrMockRecorder) GetShowTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowTime", reflect.TypeOf((*MockIShowListMgr)(nil).GetShowTime), arg0, arg1)
}

// GetUserRemainDeclareCnt mocks base method.
func (m *MockIShowListMgr) GetUserRemainDeclareCnt(arg0 context.Context, arg1 uint32) (uint32, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRemainDeclareCnt", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserRemainDeclareCnt indicates an expected call of GetUserRemainDeclareCnt.
func (mr *MockIShowListMgrMockRecorder) GetUserRemainDeclareCnt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRemainDeclareCnt", reflect.TypeOf((*MockIShowListMgr)(nil).GetUserRemainDeclareCnt), arg0, arg1)
}

// HandleShowApproval mocks base method.
func (m *MockIShowListMgr) HandleShowApproval(arg0 context.Context, arg1 *channel_live_show_list.HandleShowApprovalRequest) (*channel_live_show_list.HandelShowApprovalResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleShowApproval", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_show_list.HandelShowApprovalResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleShowApproval indicates an expected call of HandleShowApproval.
func (mr *MockIShowListMgrMockRecorder) HandleShowApproval(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleShowApproval", reflect.TypeOf((*MockIShowListMgr)(nil).HandleShowApproval), arg0, arg1)
}

// InsertChannelLiveShow mocks base method.
func (m *MockIShowListMgr) InsertChannelLiveShow(arg0 context.Context, arg1 *store.ChannelLiveShowApproval) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertChannelLiveShow", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertChannelLiveShow indicates an expected call of InsertChannelLiveShow.
func (mr *MockIShowListMgrMockRecorder) InsertChannelLiveShow(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertChannelLiveShow", reflect.TypeOf((*MockIShowListMgr)(nil).InsertChannelLiveShow), arg0, arg1)
}

// ManualAddShow mocks base method.
func (m *MockIShowListMgr) ManualAddShow(arg0 context.Context, arg1 *channel_live_show_list.TestToolRequest_TestShowBeginPush) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ManualAddShow", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ManualAddShow indicates an expected call of ManualAddShow.
func (mr *MockIShowListMgrMockRecorder) ManualAddShow(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ManualAddShow", reflect.TypeOf((*MockIShowListMgr)(nil).ManualAddShow), arg0, arg1)
}

// MarkChannelLiveShowEndNotify mocks base method.
func (m *MockIShowListMgr) MarkChannelLiveShowEndNotify(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkChannelLiveShowEndNotify", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// MarkChannelLiveShowEndNotify indicates an expected call of MarkChannelLiveShowEndNotify.
func (mr *MockIShowListMgrMockRecorder) MarkChannelLiveShowEndNotify(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkChannelLiveShowEndNotify", reflect.TypeOf((*MockIShowListMgr)(nil).MarkChannelLiveShowEndNotify), arg0, arg1)
}

// SetApprovalAuditType mocks base method.
func (m *MockIShowListMgr) SetApprovalAuditType(arg0 context.Context, arg1 *channel_live_show_list.SetApprovalAuditTypeRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetApprovalAuditType", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetApprovalAuditType indicates an expected call of SetApprovalAuditType.
func (mr *MockIShowListMgrMockRecorder) SetApprovalAuditType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetApprovalAuditType", reflect.TypeOf((*MockIShowListMgr)(nil).SetApprovalAuditType), arg0, arg1)
}

// Stop mocks base method.
func (m *MockIShowListMgr) Stop() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Stop")
}

// Stop indicates an expected call of Stop.
func (mr *MockIShowListMgrMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockIShowListMgr)(nil).Stop))
}

// UnfoldShowList mocks base method.
func (m *MockIShowListMgr) UnfoldShowList(arg0 context.Context, arg1 *channel_live_show_list.UnfoldShowListRequest) ([]*channel_live_show_list.ShowItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnfoldShowList", arg0, arg1)
	ret0, _ := ret[0].([]*channel_live_show_list.ShowItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnfoldShowList indicates an expected call of UnfoldShowList.
func (mr *MockIShowListMgrMockRecorder) UnfoldShowList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfoldShowList", reflect.TypeOf((*MockIShowListMgr)(nil).UnfoldShowList), arg0, arg1)
}

// UpdateShowApprovalTag mocks base method.
func (m *MockIShowListMgr) UpdateShowApprovalTag(arg0 context.Context, arg1 *channel_live_show_list.ModifyShowApprovalTagRequest) (*channel_live_show_list.ModifyShowApprovalTagResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateShowApprovalTag", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_show_list.ModifyShowApprovalTagResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateShowApprovalTag indicates an expected call of UpdateShowApprovalTag.
func (mr *MockIShowListMgrMockRecorder) UpdateShowApprovalTag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateShowApprovalTag", reflect.TypeOf((*MockIShowListMgr)(nil).UpdateShowApprovalTag), arg0, arg1)
}
