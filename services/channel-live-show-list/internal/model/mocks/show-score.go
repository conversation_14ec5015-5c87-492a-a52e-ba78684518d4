// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-live-show-list/internal/model/show-score (interfaces: IShowScore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	entity "golang.52tt.com/services/channel-live-show-list/internal/entity"
)

// MockIShowScore is a mock of IShowScore interface.
type MockIShowScore struct {
	ctrl     *gomock.Controller
	recorder *MockIShowScoreMockRecorder
}

// MockIShowScoreMockRecorder is the mock recorder for MockIShowScore.
type MockIShowScoreMockRecorder struct {
	mock *MockIShowScore
}

// NewMockIShowScore creates a new mock instance.
func NewMockIShowScore(ctrl *gomock.Controller) *MockIShowScore {
	mock := &MockIShowScore{ctrl: ctrl}
	mock.recorder = &MockIShowScoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIShowScore) EXPECT() *MockIShowScoreMockRecorder {
	return m.recorder
}

// AddToShowScoreSyncQueue mocks base method.
func (m *MockIShowScore) AddToShowScoreSyncQueue(arg0 context.Context, arg1, arg2 uint32, arg3 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddToShowScoreSyncQueue", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddToShowScoreSyncQueue indicates an expected call of AddToShowScoreSyncQueue.
func (mr *MockIShowScoreMockRecorder) AddToShowScoreSyncQueue(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddToShowScoreSyncQueue", reflect.TypeOf((*MockIShowScore)(nil).AddToShowScoreSyncQueue), arg0, arg1, arg2, arg3)
}

// GetAnchorScoreByUidList mocks base method.
func (m *MockIShowScore) GetAnchorScoreByUidList(arg0 context.Context, arg1 []uint32) (map[uint32]*entity.AnchorScore, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorScoreByUidList", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*entity.AnchorScore)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorScoreByUidList indicates an expected call of GetAnchorScoreByUidList.
func (mr *MockIShowScoreMockRecorder) GetAnchorScoreByUidList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorScoreByUidList", reflect.TypeOf((*MockIShowScore)(nil).GetAnchorScoreByUidList), arg0, arg1)
}

// GetScoreStByAnchorUidShowId mocks base method.
func (m *MockIShowScore) GetScoreStByAnchorUidShowId(arg0 context.Context, arg1, arg2 uint32) (entity.AnchorScore, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScoreStByAnchorUidShowId", arg0, arg1, arg2)
	ret0, _ := ret[0].(entity.AnchorScore)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScoreStByAnchorUidShowId indicates an expected call of GetScoreStByAnchorUidShowId.
func (mr *MockIShowScoreMockRecorder) GetScoreStByAnchorUidShowId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScoreStByAnchorUidShowId", reflect.TypeOf((*MockIShowScore)(nil).GetScoreStByAnchorUidShowId), arg0, arg1, arg2)
}

// LiveShowRating mocks base method.
func (m *MockIShowScore) LiveShowRating(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LiveShowRating", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LiveShowRating indicates an expected call of LiveShowRating.
func (mr *MockIShowScoreMockRecorder) LiveShowRating(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LiveShowRating", reflect.TypeOf((*MockIShowScore)(nil).LiveShowRating), arg0, arg1, arg2, arg3, arg4)
}

// Stop mocks base method.
func (m *MockIShowScore) Stop() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Stop")
}

// Stop indicates an expected call of Stop.
func (mr *MockIShowScoreMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockIShowScore)(nil).Stop))
}
