package anti_corruption_layer

import(
	context "context"
	account "golang.52tt.com/clients/account"
	app "golang.52tt.com/protocol/app"
	channelApp "golang.52tt.com/protocol/app/channel"
	channel_go "golang.52tt.com/protocol/services/channel-go"
	showListPb "golang.52tt.com/protocol/services/channel-live-show-list"
	v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	impb "golang.52tt.com/protocol/services/im-api"
)

type IACLayer interface {
	AsyncScanAudio(ctx context.Context, req *v2.ScanAudioReq) (*v2.ScanAudioResp,error)
	AsyncScanImage(ctx context.Context, req *v2.ScanImageReq) (*v2.ScanImageResp,error)
	BatGetChannelLiveStatus(ctx context.Context, channelIdList []uint32) (map[uint32]uint32,error)
	BatchGetChannelHot(ctx context.Context, channelIdList []uint32) (map[uint32]int64,error)
	BatchGetUserProfile(ctx context.Context, uidList []uint32) (map[uint32]*app.UserProfile,error)
	GetAccountUser(ctx context.Context, uid uint32) (*account.User,error)
	GetAnchorChannelId(ctx context.Context, anchorUid uint32) (uint32,error)
	GetSimpleChannelInfo(ctx context.Context, cid uint32) (*channel_go.ChannelSimpleInfo,error)
	GetUidByAlias(ctx context.Context, alias string) (uint32,error)
	GetUidByName(ctx context.Context, name string) (uint32,error)
	GetUserProfile(ctx context.Context, uid uint32) (*app.UserProfile,error)
	GetUserProfileMap(ctx context.Context, uidList []uint32, replace bool) (map[uint32]*app.UserProfile,error)
	IsUserInApplyGroup(ctx context.Context, uid uint32) (bool,error)
	PushShowBeginToChannel(ctx context.Context, channelId uint32, showInfo *showListPb.LiveShowInfo) error
	SendChannelCommonPublicMsg(ctx context.Context, uid, cid uint32, opt *channelApp.CommonHighLightChannelIm) error
	SendOfficialAccountMsg(ctx context.Context, namespace string, toUid uint32, text *impb.Text) error
	SimplePushToChannel(ctx context.Context, fromUid, channelId, channelMsgType uint32, content string, pbOptData []byte) (string,error)
	Stop() 
	SyncScanText(ctx context.Context, req *v2.SyncTextCheckReq) (*v2.SyncTextCheckResp,error)
}

