package anti_corruption_layer

//go:generate quicksilver-cli test interface ../anti-corruption-layer
//go:generate mockgen -destination=../mocks/anti_corruption_layer.go -package=mocks golang.52tt.com/services/channel-live-show-list/internal/model/anti-corruption-layer IACLayer

import (
    "context"

    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    channel_msg_api "gitlab.ttyuyin.com/bizFund/bizFund/pkg/channel-msg-api"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/clients/account"
    censoringProxy "golang.52tt.com/clients/censoring-proxy"
    channel_live_mgr "golang.52tt.com/clients/channel-live-mgr"
    channelstats "golang.52tt.com/clients/channel-stats"
    im_api "golang.52tt.com/clients/im-api"
    push "golang.52tt.com/clients/push-notification/v2"
    userProfile "golang.52tt.com/clients/user-profile-api"
    "golang.52tt.com/pkg/protocol"
    pbApp "golang.52tt.com/protocol/app"
    channelApp "golang.52tt.com/protocol/app/channel"
    channel_live_logic "golang.52tt.com/protocol/app/channel-live-logic"
    "golang.52tt.com/protocol/common/status"
    channel_go "golang.52tt.com/protocol/services/channel-go"
    showListPb "golang.52tt.com/protocol/services/channel-live-show-list"
    impb "golang.52tt.com/protocol/services/im-api"
    "golang.52tt.com/services/channel-live-show-list/internal/conf"
    "golang.52tt.com/services/tt-rev/esport/common/user_group"
)

const (
    imAnchorBindId = 90004 // 达人服务号
)

type ACLayer struct {
    shutDown chan struct{}
    bc       conf.IBusinessConfManager

    userProfileCli    userProfile.IClient
    accountCli        account.IClient
    channelGoCli      channel_go.ChannelGoClient
    pushCli           push.IClient
    channelMsgApi     channel_msg_api.IClient
    censoringProxyCli censoringProxy.IClient
    imApiCli          im_api.IClient
    userGroupCli      *user_group.UserGroupCli
    channelLiveMgrCli channel_live_mgr.IClient
    channelstatsCli   channelstats.IClient
}

// NewMgr 活动配置模块
func NewMgr(ctx context.Context, bc conf.IBusinessConfManager) (*ACLayer, error) {
    userProfileCli, _ := userProfile.NewClient()
    pushCli, _ := push.NewClient()
    accountCli := account.NewIClient()
    channelGoCli, err := channel_go.NewClient(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewClient err:%v", err)
    }

    channelMsgApi, _ := channel_msg_api.NewIClient()
    censoringProxyCli := censoringProxy.NewClient()
    imApiCli, _ := im_api.NewClient()

    userGroupCfg := bc.GetUserGroupApi()
    if userGroupCfg == nil {
        log.ErrorWithCtx(ctx, "NewMgr user_group_api is nil")
    }

    userGroupCli := user_group.NewUserGroupCli(
        bc.GetUserGroupApi().DspLpmAdminHost,
        bc.GetUserGroupApi().DspLpmOfflineGroupHost,
        bc.GetUserGroupApi().DspLpmApiserverHost)

    channelStatsCli, _ := channelstats.NewClient()

    channelLiveMgrCli := channel_live_mgr.NewIClient()

    m := &ACLayer{
        shutDown:          make(chan struct{}),
        bc:                bc,
        userGroupCli:      userGroupCli,
        channelstatsCli:   channelStatsCli,
        userProfileCli:    userProfileCli,
        accountCli:        accountCli,
        pushCli:           pushCli,
        channelMsgApi:     channelMsgApi,
        channelGoCli:      channelGoCli,
        censoringProxyCli: censoringProxyCli,
        imApiCli:          imApiCli,
        channelLiveMgrCli: channelLiveMgrCli,
    }

    return m, nil
}

func (m *ACLayer) Stop() {
    close(m.shutDown)
}

// SendChannelCommonPublicMsg 推送用户外显状态房间公屏
func (m *ACLayer) SendChannelCommonPublicMsg(ctx context.Context, uid, cid uint32, opt *channelApp.CommonHighLightChannelIm) error {
    if uid == 0 || opt == nil {
        log.WarnWithCtx(ctx, "SendChannelPublicMsg fail to GetUsersChannelId. uid: %d, opt:%v", uid, opt)
        return nil
    }

    opt.Cid = cid

    marshalMsg, err := proto.Marshal(opt)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendChannelPublicMsg proto.Marshal failed opt:%v, err:%v", opt, err)
        return err
    }

    reqId, err := m.channelMsgApi.SimplePushToUsers(ctx, []uint32{uid}, 0, cid,
        uint32(channelApp.ChannelMsgType_COMMON_HIGHLIGHT_CHANNEL_IM), "", marshalMsg, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "pushFansInfoChangeMsg failed opt:%v err:%v", opt, err)
        return err
    }

    log.InfoWithCtx(ctx, "SendChannelPublicMsg success.reqId:%s opt:%+v", reqId, opt)
    return nil
}

// GetSimpleChannelInfo 根据cid获取房间简单信息
func (m *ACLayer) GetSimpleChannelInfo(ctx context.Context, cid uint32) (*channel_go.ChannelSimpleInfo, error) {
    infoResp, err := m.channelGoCli.GetChannelSimpleInfo(ctx, &channel_go.GetChannelSimpleInfoReq{
        ChannelId: cid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetSimpleChannelInfo failed cid:%d err:%v", cid, err)
        return nil, err
    }

    return infoResp.GetChannelSimple(), nil
}

func (m *ACLayer) GetUidByName(ctx context.Context, name string) (uint32, error) {
    uid, _, err := m.accountCli.GetUidByName(ctx, name)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUidByName failed name:%s err:%v", name, err)
        return 0, err
    }
    return uid, nil
}

func (m *ACLayer) GetUidByAlias(ctx context.Context, alias string) (uint32, error) {
    uid, _, err := m.accountCli.GetUidByAlias(ctx, alias)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUidByAlias failed alias:%s err:%v", alias, err)
        return 0, err
    }
    return uid, nil
}

func (m *ACLayer) BatchGetUserProfile(ctx context.Context, uidList []uint32) (map[uint32]*pbApp.UserProfile, error) {
    return m.userProfileCli.BatchGetUserProfileV2(ctx, uidList, false)
}

// PushShowBeginToChannel 推送节目开始
func (m *ACLayer) PushShowBeginToChannel(ctx context.Context, channelId uint32, showInfo *showListPb.LiveShowInfo) error {
    opt := &channel_live_logic.LiveShowStartPushOpt{
        ChannelId: channelId,
        ShowInfo: &channel_live_logic.LiveShowInfo{
            ShowId:   showInfo.GetShowId(),
            ShowName: showInfo.GetShowName(),
            BeginTs:  showInfo.GetBeginTs(),
            EndTs:    showInfo.GetEndTs(),
            RatingFloat: &channel_live_logic.ShowRatingFloat{
                IsShow:       showInfo.GetRatingFloatConf().GetIsShow(),
                ListenSec:    showInfo.GetRatingFloatConf().GetListenSec(),
                FloatStaySec: showInfo.GetRatingFloatConf().GetFloatStaySec(),
            },
        },
    }

    data, e := proto.Marshal(opt)
    if e != nil {
        log.ErrorWithCtx(ctx, "PushShowBeginToChannel marshal err:%v, %+v", e, opt)
        return e
    }

    _, err := m.SimplePushToChannel(ctx, 0, channelId, uint32(channelApp.ChannelMsgType_CHANNEL_LIVE_SHOW_START_NOTIFY), "直播间节目开始推送", data)
    if err != nil {
        log.ErrorWithCtx(ctx, "PushShowBeginToChannel failed to SimplePushToChannel. err:%v, %+v", err, opt)
        return err
    }
    return err
}

//SimplePushToChannel 简单房间推送
func (m *ACLayer) SimplePushToChannel(ctx context.Context, fromUid, channelId, channelMsgType uint32, content string, pbOptData []byte) (string, error) {
    reqId, err := m.channelMsgApi.SimplePushToChannel(ctx, fromUid, channelId, channelMsgType, content, pbOptData)
    return reqId, err
}

// SendOfficialAccountMsg 发送公众号消息 ps: hightlight字段内容要在content中出现才会有高亮效果
func (m *ACLayer) SendOfficialAccountMsg(ctx context.Context, namespace string, toUid uint32, text *impb.Text) error {
    sendReq := &impb.SendPublicAccountTextReq{
        Namespace: namespace,
        PublicAccount: &impb.PublicAccount{
            PublicType: impb.PublicAccount_SYSTEM,
            BindedId:   imAnchorBindId,
        },
        ToUid: toUid,
        Text:  text,
    }
    accountText, err := m.imApiCli.SendPublicAccountText(ctx, sendReq)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendOfficialAccountMsg - SendPublicAccountText fail , err: %v, namespace: %v, toUid: %v, text: %v", err, namespace, toUid, text)
        return err
    }
    log.InfoWithCtx(ctx, "SendOfficialAccountMsg - SendPublicAccountText success , accountText: %v, namespace: %v, toUid: %v, text: %v", accountText, namespace, toUid, text)
    return err
}

func (m *ACLayer) GetAnchorChannelId(ctx context.Context, anchorUid uint32) (uint32, error) {
    channelLiveInfo, err := m.channelLiveMgrCli.GetChannelLiveInfo(ctx, anchorUid, false)
    if err != nil {
        if e := protocol.ToServerError(err); e.Code() == status.ErrChannelLiveNotAuthority {
            log.ErrorWithCtx(ctx, "GetAnchorChannelId - GetChannelLiveInfo error, err: %v, anchorUid: %v", err, anchorUid)
            return 0, nil
        }
        log.ErrorWithCtx(ctx, "GetAnchorChannelId failed anchorUid:%d err:%v", anchorUid, err)
        return 0, err
    }
    return channelLiveInfo.GetChannelLiveInfo().GetChannelId(), nil
}
