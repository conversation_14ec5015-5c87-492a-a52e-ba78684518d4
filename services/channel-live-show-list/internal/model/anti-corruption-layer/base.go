package anti_corruption_layer

import (
    "context"

    "golang.52tt.com/clients/account"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/protocol/app"
    serPb "golang.52tt.com/protocol/services/channellivemgr"
    v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
)

func (m *ACLayer) GetUserProfile(ctx context.Context, uid uint32) (*app.UserProfile, error) {
    return m.userProfileCli.GetUserProfileV2(ctx, uid, true)
}

func (m *ACLayer) GetUserProfileMap(ctx context.Context, uidList []uint32, replace bool) (map[uint32]*app.UserProfile, error) {
    if len(uidList) == 0 {
        return nil, nil
    }
    return m.userProfileCli.BatchGetUserProfileV2(ctx, uidList, replace)
}

func (m *ACLayer) GetAccountUser(ctx context.Context, uid uint32) (*account.User, error) {
    return m.accountCli.GetUser(ctx, uid)
}

func (m *ACLayer) SyncScanText(ctx context.Context, req *v2.SyncTextCheckReq) (*v2.SyncTextCheckResp, error) {
    return m.censoringProxyCli.Text().SyncScanText(ctx, req)
}

func (m *ACLayer) AsyncScanImage(ctx context.Context, req *v2.ScanImageReq) (*v2.ScanImageResp, error) {
    return m.censoringProxyCli.Image().AsyncScanImage(ctx, req)
}

func (m *ACLayer) AsyncScanAudio(ctx context.Context, req *v2.ScanAudioReq) (*v2.ScanAudioResp, error) {
    return m.censoringProxyCli.Audio().AsyncScanAudio(ctx, req)
}

//
func (m *ACLayer) IsUserInApplyGroup(ctx context.Context, uid uint32) (bool, error) {
    res, err := m.userGroupCli.GetUserHasGroup(ctx, uid, []string{m.bc.GetApplyGroupId()})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserHasGroup err: %v, uid:%d, groupId: %s", err, uid, m.bc.GetApplyGroupId())
        return false, err
    }
    return len(res) > 0, nil
}

func (m *ACLayer) BatchGetChannelHot(ctx context.Context, channelIdList []uint32) (map[uint32]int64, error) {
    return m.channelstatsCli.BatchGetChannelHotValue(ctx, channelIdList)
}

func (m *ACLayer) BatGetChannelLiveStatus(ctx context.Context, channelIdList []uint32) (map[uint32]uint32, error) {
    retMap := make(map[uint32]uint32)
    batchResp, err := m.channelLiveMgrCli.BatchGetChannelLiveStatusSimple(ctx, serPb.BatchGetChannelLiveStatusSimpleReq{
        ChannelList: channelIdList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "BatGetChannelLiveStatus BatchGetChannelLiveStatusSimple err:%v", err)
        return retMap, err
    }

    for _, sim := range batchResp.ChannelLiveStatusList {
        retMap[sim.ChannelId] = sim.GetStatus()
    }
    return retMap, nil
}
