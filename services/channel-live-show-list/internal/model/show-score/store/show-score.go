package store

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "time"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "strings"
)

const (
    LiveShowScoreRecordTblName = "channel_live_show_score_record" // 节目评分统计表
)

// 节目评分统计表建表SQL
var createLiveShowScoreTblSql = `CREATE TABLE IF NOT EXISTS %s (
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id 自增',
    show_id int(10) unsigned NOT NULL COMMENT '节目id',
    anchor_uid int(10) unsigned NOT NULL COMMENT '主播uid',
    score_1_cnt int(10) unsigned NOT NULL DEFAULT 0 COMMENT '评分为1的次数',
    score_2_cnt int(10) unsigned NOT NULL DEFAULT 0 COMMENT '评分为2的次数',
    score_3_cnt int(10) unsigned NOT NULL DEFAULT 0 COMMENT '评分为3的次数',
    score_4_cnt int(10) unsigned NOT NULL DEFAULT 0 COMMENT '评分为4的次数',
    score_5_cnt int(10) unsigned NOT NULL DEFAULT 0 COMMENT '评分为5的次数',
    total_score int(10) unsigned NOT NULL DEFAULT 0 COMMENT '总评分',
    total_count int(10) unsigned NOT NULL DEFAULT 0 COMMENT '总评分人数',
    create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uniq_show_id (show_id),
    KEY idx_anchor_uid(anchor_uid),
    KEY idx_update_time(update_time)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='节目评分统计表'`

func (s *Store) createLiveShowScoreTbl(ctx context.Context) error {
    _, err := s.db.ExecContext(ctx, fmt.Sprintf(createLiveShowScoreTblSql, LiveShowScoreRecordTblName))
    if err != nil {
        log.ErrorWithCtx(ctx, "CreateLiveShowScoreSummaryTbl failed. createLiveShowScoreSummaryTblSql:%s tblName:%s err:%v",
            createLiveShowScoreTblSql, LiveShowScoreRecordTblName, err)
        return err
    }
    return nil
}

type LiveShowScore struct {
    Id         uint32    `db:"id"`
    ShowId     uint32    `db:"show_id"`
    AnchorUid  uint32    `db:"anchor_uid"`
    Score1Cnt  uint32    `db:"score_1_cnt"`
    Score2Cnt  uint32    `db:"score_2_cnt"`
    Score3Cnt  uint32    `db:"score_3_cnt"`
    Score4Cnt  uint32    `db:"score_4_cnt"`
    Score5Cnt  uint32    `db:"score_5_cnt"`
    TotalScore uint32    `db:"total_score"`
    TotalCount uint32    `db:"total_count"`
    CreateTime time.Time `db:"create_time"`
    UpdateTime time.Time `db:"update_time"`
}

// UpsertShowScore 插入或更新节目评分统计
func (s *Store) UpsertShowScore(ctx context.Context, summary *LiveShowScore) (bool, error) {
    if summary == nil {
        log.ErrorWithCtx(ctx, "UpsertShowScore failed. summary is nil")
        return false, fmt.Errorf("UpsertShowScore failed. summary is nil")
    }

    query := fmt.Sprintf(`INSERT INTO %s 
        (show_id,anchor_uid, score_1_cnt, score_2_cnt, score_3_cnt, score_4_cnt, score_5_cnt, total_score, total_count) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`, LiveShowScoreRecordTblName)
    _, err := s.db.ExecContext(ctx, query, summary.ShowId, summary.AnchorUid, summary.Score1Cnt, summary.Score2Cnt, summary.Score3Cnt,
        summary.Score4Cnt, summary.Score5Cnt, summary.TotalScore, summary.TotalCount)
    if err != nil {
        // 如果是主键重复错误，则忽略
        if mysql.IsMySQLError(err, 1062) {
            log.WarnWithCtx(ctx, "UpsertShowScore err1062. summary:%+v err:%v", summary, err)
            return false, nil
        }
        log.ErrorWithCtx(ctx, "UpsertShowScore failed. summary:%+v err:%v", summary, err)
        return false, err
    }

    log.InfoWithCtx(ctx, "UpsertShowScore success. summary:%+v", summary)
    return true, nil
}

// GetShowScoreByShowId 根据节目ID获取评分统计
func (s *Store) GetShowScoreByShowId(ctx context.Context, showId uint32) (*LiveShowScore, error) {
    query := fmt.Sprintf("SELECT id, show_id, anchor_uid,total_score,total_count FROM %s WHERE show_id=?", LiveShowScoreRecordTblName)
    var summary LiveShowScore
    err := s.db.GetContext(ctx, &summary, query, showId)
    if err != nil {
        // 记录不存在
        if mysql.IsNoRowsError(err) {
            return &LiveShowScore{}, nil
        }
        log.ErrorWithCtx(ctx, "GetShowScoreSummaryByShowId failed. query:%s err:%v", query, err)
        return &LiveShowScore{}, err
    }

    return &summary, nil
}

// GetDistinctAnchorUidByUpdateTime 获取昨日新增/更新记录的去重anchorUid列表
func (s *Store) GetDistinctAnchorUidByUpdateTime(ctx context.Context, startTime, endTime time.Time) ([]uint32, error) {
    query := fmt.Sprintf("SELECT DISTINCT anchor_uid FROM %s WHERE update_time >= ? AND update_time < ?", LiveShowScoreRecordTblName)

    uidList := make([]uint32, 0)
    err := s.db.SelectContext(ctx, &uidList, query, startTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetDistinctAnchorUidByUpdateTime failed. startTime:%d endTime:%d query:%s err:%v", startTime, endTime, query, err)
        return nil, err
    }
    return uidList, nil
}

type ScoreSt struct {
    AnchorUid  uint32 `db:"anchor_uid"`
    TotalScore uint32 `db:"total_score"`
    TotalCount uint32 `db:"total_count"`
}

// GetAnchorTotalScoreByAnchorUid 统计给定anchorUid列表的总节目评分
func (s *Store) GetAnchorTotalScoreByAnchorUid(ctx context.Context, anchorUidList []uint32) ([]*ScoreSt, error) {
    // 处理空列表情况
    if len(anchorUidList) == 0 {
        return []*ScoreSt{}, nil
    }

    // 使用参数化查询构建占位符
    placeholders := strings.Repeat("?,", len(anchorUidList)-1) + "?"

    query := fmt.Sprintf("SELECT anchor_uid, IFNULL(SUM(total_score), 0) AS total_score, IFNULL(SUM(total_count), 0) AS total_count "+
        "FROM %s WHERE anchor_uid IN (%s) GROUP BY anchor_uid", LiveShowScoreRecordTblName, placeholders)

    // 构造查询参数
    args := make([]interface{}, len(anchorUidList))
    for i, uid := range anchorUidList {
        args[i] = uid
    }

    scoreList := make([]*ScoreSt, 0)
    err := s.db.SelectContext(ctx, &scoreList, query, args...)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetAnchorTotalScoreByAnchorUid failed. anchorUidList:[%+v] query:%s err:%v", anchorUidList, query, err)
        return nil, err
    }

    return scoreList, nil
}
