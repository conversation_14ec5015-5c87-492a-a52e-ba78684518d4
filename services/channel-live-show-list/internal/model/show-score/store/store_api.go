package store

import(
	context "context"
	mysql "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	time "time"
)

type IStore interface {
	BatUpsertAnchorScore(ctx context.Context, recordList []*AnchorScore) error
	Close() error
	CreateAnchorScoreTbl(ctx context.Context) error
	GetAnchorScoreByUidList(ctx context.Context, uidList []uint32) ([]*AnchorScore,error)
	GetAnchorTotalScoreByAnchorUid(ctx context.Context, anchorUidList []uint32) ([]*ScoreSt,error)
	GetDistinctAnchorUidByUpdateTime(ctx context.Context, startTime, endTime time.Time) ([]uint32,error)
	GetShowScoreByShowId(ctx context.Context, showId uint32) (*LiveShowScore,error)
	Transaction(ctx context.Context, f func(tx mysql.Txx) error) error
	UpsertOrIncrAnchorScore(ctx context.Context, uid, incrScore, incrCnt uint32) error
	UpsertShowScore(ctx context.Context, summary *LiveShowScore) (bool,error)
}

