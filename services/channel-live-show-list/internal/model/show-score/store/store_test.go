package store

import (
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    "context"
    "testing"
    "time"
)

var testStore *Store

var (
    testUid       = uint32(1)
    testAnchorUid = uint32(2)
    ctx           = context.Background()
)

func init() {
    mysqlConfig := &mysqlConnect.MysqlConfig{
        Host:     "*************", //"*************"
        Port:     3306,
        Database: "appsvr",
        Charset:  "utf8",
        UserName: "godman",
        Password: "thegodofman",
    }

    dbCli, err := mysqlConnect.NewClient(context.Background(), mysqlConfig)
    if err != nil {
        return
    }

    testStore = NewStore(dbCli)
}

func TestStore_UpsertOrIncrAnchorScore(t *testing.T) {
    err := testStore.UpsertOrIncrAnchorScore(ctx, testUid, 1, 1)
    if err != nil {
        t.Errorf("UpsertOrIncrAnchorScore failed. err:%v", err)
    }

    err = testStore.UpsertOrIncrAnchorScore(ctx, testUid, 1, 1)
    if err != nil {
        t.Errorf("UpsertOrIncrAnchorScore failed. err:%v", err)
    }

    // get
    anchorScoreList, err := testStore.GetAnchorScoreByUidList(ctx, []uint32{testUid})
    if err != nil {
        t.Errorf("GetAnchorScoreByUidList failed. err:%v", err)
    }
    for _, anchorScore := range anchorScoreList {
        t.Logf("GetAnchorScoreByUidList. anchorScore:%+v", anchorScore)
    }

    err = testStore.BatUpsertAnchorScore(ctx, []*AnchorScore{
        &AnchorScore{
            Uid:            testUid,
            TotalScore:     10,
            TotalRatingCnt: 3,
        },
        &AnchorScore{
            Uid:            testUid + 1,
            TotalScore:     15,
            TotalRatingCnt: 3,
        },
    })
    if err != nil {
        t.Errorf("BatUpsertAnchorScore failed. err:%v", err)
    }

    anchorScoreList, err = testStore.GetAnchorScoreByUidList(ctx, []uint32{testUid, testUid + 1})
    if err != nil {
        t.Errorf("GetAnchorScoreByUidList failed. err:%v", err)
    }
    for _, anchorScore := range anchorScoreList {
        t.Logf("GetAnchorScoreByUidList. anchorScore:%+v", anchorScore)
    }
}

func TestStore_UpsertShowScore(t *testing.T) {
    testShowId := uint32(1)
    showScore := &LiveShowScore{
        ShowId:     testShowId,
        AnchorUid:  testAnchorUid,
        TotalScore: 100,
        TotalCount: 10,
        Score1Cnt:  1,
        Score2Cnt:  2,
        Score3Cnt:  3,
        Score4Cnt:  4,
        Score5Cnt:  5,
    }

    ok, err := testStore.UpsertShowScore(ctx, showScore)
    if err != nil {
        t.Errorf("UpsertShowScore failed. err: %v", err)
    }
    t.Logf("ok:%v", ok)

    showScore = &LiveShowScore{
        ShowId:     testShowId + 1,
        AnchorUid:  testAnchorUid + 1,
        TotalScore: 100,
        TotalCount: 10,
        Score1Cnt:  1,
        Score2Cnt:  2,
        Score3Cnt:  3,
        Score4Cnt:  4,
        Score5Cnt:  5,
    }

    ok, err = testStore.UpsertShowScore(ctx, showScore)
    if err != nil {
        t.Errorf("UpsertShowScore failed. err: %v", err)
    }
    t.Logf("UpsertShowScore ok: %v", ok)

    gotByShowId, err := testStore.GetShowScoreByShowId(ctx, testShowId)
    if err != nil {
        t.Errorf("GetShowScoreByShowId failed. err: %v", err)
    }
    t.Logf("showId:%d gotByShowId:%+v", testShowId, gotByShowId)

    showScore = &LiveShowScore{
        ShowId:     testShowId + 2,
        AnchorUid:  testAnchorUid + 2,
        TotalScore: 100,
        TotalCount: 10,
        Score1Cnt:  1,
        Score2Cnt:  2,
        Score3Cnt:  3,
        Score4Cnt:  4,
        Score5Cnt:  5,
    }

    ok, err = testStore.UpsertShowScore(ctx, showScore)
    if err != nil {
        t.Errorf("UpsertShowScore failed. err: %v", err)
    }

    scoreList, err := testStore.GetAnchorTotalScoreByAnchorUid(ctx, []uint32{testAnchorUid, testAnchorUid + 1})
    if err != nil {
        t.Errorf("GetAnchorScoreByUidList failed. err: %v", err)
    }
    for _, score := range scoreList {
        t.Logf("score: %+v", score)
    }

    anchorList, err := testStore.GetDistinctAnchorUidByUpdateTime(ctx, time.Now().Add(-time.Hour*24), time.Now())
    if err != nil {
        t.Errorf("GetDistinctAnchorUidByUpdateTime failed. err: %v", err)
    }
    t.Logf("GetDistinctAnchorUidByUpdateTime: %v", anchorList)
}
