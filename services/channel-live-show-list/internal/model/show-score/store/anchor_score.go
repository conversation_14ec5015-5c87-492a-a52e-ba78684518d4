package store

import (
    "fmt"
    "time"
    "strings"
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "github.com/pkg/errors"
)

const (
    AnchorScoreTblName = "channel_live_show_anchor_score"
)

var createAnchorScoreTblSql = `CREATE Table IF NOT EXISTS %s(
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id 自增',
    uid int(10) unsigned NOT NULL COMMENT '主播uid',
    total_score int(10) unsigned NOT NULL DEFAULT 0 COMMENT '总评分',
    total_rating_cnt int(10) unsigned NOT NULL DEFAULT 0 COMMENT '总评分次数',
    
    create_time datetime NOT NULL default CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time datetime NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', 
    
    primary key (id),
    unique key uniq_idx_uid(uid)
    
)engine=InnoDB default charset=utf8mb4 COMMENT "主播评分表";`

func (s *Store) CreateAnchorScoreTbl(ctx context.Context) error {
    _, err := s.db.ExecContext(ctx, fmt.Sprintf(createAnchorScoreTblSql, AnchorScoreTblName))
    if err != nil {
        log.ErrorWithCtx(ctx, "CreateAnchorScoreTbl failed.createAnchorScoreTblSql:%s tblName:%s err:%v", createAnchorScoreTblSql, AnchorScoreTblName, err)
        return err
    }
    return nil
}

type AnchorScore struct {
    Id             uint32    `db:"id"`
    Uid            uint32    `db:"uid"`
    TotalScore     uint32    `db:"total_score"`
    TotalRatingCnt uint32    `db:"total_rating_cnt"`
    CreateTime     time.Time `db:"create_time"`
    UpdateTime     time.Time `db:"update_time"`
}

// BatUpsertAnchorScore 批量插入或更新主播评分记录
func (s *Store) BatUpsertAnchorScore(ctx context.Context, recordList []*AnchorScore) error {
    if len(recordList) == 0 {
        log.InfoWithCtx(ctx, "BatUpsertAnchorScore: recordList is empty")
        return nil
    }

    // 构建VALUES部分
    values := make([]interface{}, 0, len(recordList)*3) // 每条记录3个参数: uid, total_score, total_rating_cnt
    placeholders := make([]string, 0, len(recordList))

    for _, record := range recordList {
        placeholders = append(placeholders, "(?,?,?)")
        values = append(values, record.Uid, record.TotalScore, record.TotalRatingCnt)
    }

    query := fmt.Sprintf("INSERT INTO %s (uid,total_score,total_rating_cnt) VALUES %s ON DUPLICATE KEY UPDATE total_score=VALUES(total_score),total_rating_cnt=VALUES(total_rating_cnt)",
        AnchorScoreTblName,
        strings.Join(placeholders, ","))

    _, err := s.db.ExecContext(ctx, query, values...)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatUpsertAnchorScore failed. query:%s err:%v", query, err)
        return err
    }

    log.InfoWithCtx(ctx, "BatUpsertAnchorScore success. record count:%d", len(recordList))
    return nil
}

//UpsertOrIncrAnchorScore 增量更新主播评分，不存在则新增
func (s *Store) UpsertOrIncrAnchorScore(ctx context.Context, uid, incrScore, incrCnt uint32) error {
    // 参数校验
    if uid == 0 {
        return errors.New("invalid uid")
    }

    query := fmt.Sprintf("INSERT INTO %s (uid,total_score,total_rating_cnt) VALUES (?,?,?) ON DUPLICATE KEY UPDATE total_score=total_score+?,total_rating_cnt=total_rating_cnt+?", AnchorScoreTblName)
    _, err := s.db.ExecContext(ctx, query, uid, incrScore, incrCnt, incrScore, incrCnt)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpsertOrIncrAnchorScore failed. uid:%d, incrScore:%d, incrCnt:%d, err:%v", uid, incrScore, incrCnt, err)
        return err
    }

    log.InfoWithCtx(ctx, "UpsertOrIncrAnchorScore success. uid:%d, incrScore:%d, incrCnt:%d", uid, incrScore, incrCnt)
    return nil
}

// GetAnchorScoreByUidList 根据uidList获取主播评分
func (s *Store) GetAnchorScoreByUidList(ctx context.Context, uidList []uint32) ([]*AnchorScore, error) {
    if len(uidList) == 0 {
        log.WarnWithCtx(ctx, "GetAnchorScoreByUidList. uidList is empty")
        return nil, nil
    }

    query := fmt.Sprintf("SELECT uid,total_score,total_rating_cnt,create_time,update_time FROM %s WHERE uid IN (%s)", AnchorScoreTblName, genParamJoinStr(uidList))
    scoreList := make([]*AnchorScore, 0)
    err := s.db.SelectContext(ctx, &scoreList, query)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetAnchorScoreByUidList failed. uidList:[%+v] query:%s err:%v", uidList, query, err)
        return nil, err
    }
    return scoreList, nil
}
