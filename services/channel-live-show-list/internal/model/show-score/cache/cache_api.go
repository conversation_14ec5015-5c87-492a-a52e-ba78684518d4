package cache

import(
	redis "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	context "context"
)

type ICache interface {
	AddShowScore(ctx context.Context, showId, uid, anchorUid, score uint32) (bool,error)
	AddToShowScoreSyncQueue(ctx context.Context, showId, anchorUid uint32, endTime int64) error
	Close() error
	GetExpiredShowsFromSyncQueue(ctx context.Context, beginTs, endTs int64) ([]string,error)
	GetRedisClient() redis.Cmdable
	GetShowScoreStats(ctx context.Context, showId, anchorUid uint32) (*ShowScoreStats,error)
	RemoveFromSyncQueue(ctx context.Context, members []string) error
}

