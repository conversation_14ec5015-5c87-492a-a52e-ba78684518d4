// file: cache/show_score_test.go

package cache

import (
    "context"
    "testing"
    "time"

    "github.com/alicebob/miniredis/v2"
    "github.com/stretchr/testify/assert"
    "strconv"
    redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
)

var (
    testCache *Cache
    testRedis *miniredis.Miniredis
    ctx       = context.Background()
)

func init() {
    var err error
    testRedis, err = miniredis.Run()
    if err != nil {
        panic(err)
    }
    port, _ := strconv.ParseInt(testRedis.Port(), 10, 32)
    redisClient, _ := redisConnect.NewClient(context.Background(), &redisConnect.RedisConfig{
        Host: testRedis.Host(),
        Port: uint32(port),
    })

    testCache = NewCache(redisClient)
}

func TestAddShowScore(t *testing.T) {
    // 清理之前的数据
    testRedis.FlushAll()

    showId := uint32(1001)
    anchorUid := uint32(2001)
    uid := uint32(12345)
    score := uint32(5)

    // 第一次添加评分
    added, err := testCache.AddShowScore(ctx, showId, uid, anchorUid, score)
    assert.NoError(t, err)
    assert.True(t, added)

    // 重复添加同一用户评分，应该返回false
    added, err = testCache.AddShowScore(ctx, showId, uid, anchorUid, score)
    assert.NoError(t, err)
    assert.False(t, added)

    // 添加另一个用户的评分
    added, err = testCache.AddShowScore(ctx, showId, uid+1, anchorUid, score-1)
    assert.NoError(t, err)
    assert.True(t, added)
}

func TestGetShowScoreStats(t *testing.T) {
    // 清理之前的数据
    testRedis.FlushAll()

    showId := uint32(1002)
    anchorUid := uint32(2002)

    // 添加多个用户的评分
    scores := []struct {
        uid   uint32
        score uint32
    }{
        {12345, 5},
        {12346, 4},
        {12347, 5},
        {12348, 3},
        {12349, 4},
    }

    for _, s := range scores {
        added, err := testCache.AddShowScore(ctx, showId, s.uid, anchorUid, s.score)
        assert.NoError(t, err)
        assert.True(t, added)
    }

    // 获取评分统计
    stats, err := testCache.GetShowScoreStats(ctx, showId, anchorUid)
    assert.NoError(t, err)
    assert.NotNil(t, stats)
    assert.Equal(t, uint32(21), stats.TotalScore) // 5+4+5+3+4 = 21
    assert.Equal(t, uint32(5), stats.TotalCount)
    assert.Equal(t, uint32(2), stats.ScoreCount[5])
    assert.Equal(t, uint32(2), stats.ScoreCount[4])
    assert.Equal(t, uint32(1), stats.ScoreCount[3])
    assert.Equal(t, uint32(0), stats.ScoreCount[1])
    assert.Equal(t, uint32(0), stats.ScoreCount[2])

    // 测试不存在的节目ID
    nonExistStats, err := testCache.GetShowScoreStats(ctx, 9999, anchorUid)
    assert.NoError(t, err)
    assert.NotNil(t, nonExistStats)
    assert.Equal(t, uint32(0), nonExistStats.TotalScore)
    assert.Equal(t, uint32(0), nonExistStats.TotalCount)
    assert.Empty(t, nonExistStats.ScoreCount)
}

func TestAddToShowScoreSyncQueue(t *testing.T) {
    // 清理之前的数据
    testRedis.FlushAll()

    showId := uint32(1003)
    anchorUid := uint32(2003)
    endTime := time.Now().Add(1 * time.Hour).Unix()

    // 添加节目到同步队列
    err := testCache.AddToShowScoreSyncQueue(ctx, showId, anchorUid, endTime)
    assert.NoError(t, err)

    // 验证添加成功
    members, err := testRedis.ZMembers(ShowScoreSyncQueueKey)
    assert.NoError(t, err)
    assert.Contains(t, members, "1003_2003")
}

func TestGetExpiredShowsFromSyncQueue(t *testing.T) {
    // 清理之前的数据
    testRedis.FlushAll()

    currentTime := time.Now().Unix()
    expiredTime := currentTime - 1000
    futureTime := currentTime + 1000

    // 添加已过期的节目
    err := testCache.AddToShowScoreSyncQueue(ctx, 1001, 2001, expiredTime)
    assert.NoError(t, err)

    // 添加未过期的节目
    err = testCache.AddToShowScoreSyncQueue(ctx, 1002, 2002, futureTime)
    assert.NoError(t, err)

    // 获取已过期的节目
    expiredShows, err := testCache.GetExpiredShowsFromSyncQueue(ctx, currentTime-3600, currentTime)
    assert.NoError(t, err)
    assert.Len(t, expiredShows, 1)
    assert.Contains(t, expiredShows, "1001_2001")
    assert.NotContains(t, expiredShows, "1002_2002")
}

func TestRemoveFromSyncQueue(t *testing.T) {
    // 清理之前的数据
    testRedis.FlushAll()

    // 添加几个节目到队列
    shows := []struct {
        showId    uint32
        anchorUid uint32
    }{
        {1001, 2001},
        {1002, 2002},
        {1003, 2003},
        {1004, 2004},
    }

    for _, show := range shows {
        err := testCache.AddToShowScoreSyncQueue(ctx, show.showId, show.anchorUid, time.Now().Unix())
        assert.NoError(t, err)
    }

    // 删除部分节目
    toRemove := []string{"1001_2001", "1003_2003"}
    err := testCache.RemoveFromSyncQueue(ctx, toRemove)
    assert.NoError(t, err)

    // 验证删除结果
    remainingMembers, err := testRedis.ZMembers(ShowScoreSyncQueueKey)
    assert.NoError(t, err)
    assert.Len(t, remainingMembers, 2)
    assert.Contains(t, remainingMembers, "1002_2002")
    assert.Contains(t, remainingMembers, "1004_2004")
    assert.NotContains(t, remainingMembers, "1001_2001")
    assert.NotContains(t, remainingMembers, "1003_2003")

    // 测试删除空列表
    err = testCache.RemoveFromSyncQueue(ctx, []string{})
    assert.NoError(t, err)
}

func TestCacheIntegration(t *testing.T) {
    // 清理之前的数据
    testRedis.FlushAll()

    showId := uint32(2001)
    anchorUid := uint32(3001)
    endTime := time.Now().Add(2 * time.Hour).Unix()

    // 添加评分数据
    users := map[uint32]uint32{
        1001: 5,
        1002: 4,
        1003: 3,
        1004: 5,
        1005: 2,
    }

    for uid, score := range users {
        added, err := testCache.AddShowScore(ctx, showId, uid, anchorUid, score)
        assert.NoError(t, err)
        assert.True(t, added)
    }

    // 获取统计信息
    stats, err := testCache.GetShowScoreStats(ctx, showId, anchorUid)
    assert.NoError(t, err)
    assert.Equal(t, uint32(19), stats.TotalScore) // 5+4+3+5+2 = 19
    assert.Equal(t, uint32(5), stats.TotalCount)
    assert.Equal(t, uint32(2), stats.ScoreCount[5])
    assert.Equal(t, uint32(1), stats.ScoreCount[4])
    assert.Equal(t, uint32(1), stats.ScoreCount[3])
    assert.Equal(t, uint32(1), stats.ScoreCount[2])

    // 添加到同步队列
    err = testCache.AddToShowScoreSyncQueue(ctx, showId, anchorUid, endTime)
    assert.NoError(t, err)

    // 验证在队列中
    queueShows, err := testCache.GetExpiredShowsFromSyncQueue(ctx, endTime-3600, endTime+1)
    assert.NoError(t, err)
    assert.Contains(t, queueShows, "2001_3001")

    // 从队列中移除
    err = testCache.RemoveFromSyncQueue(ctx, []string{"2001_3001"})
    assert.NoError(t, err)

    // 验证已移除
    queueShows, err = testCache.GetExpiredShowsFromSyncQueue(ctx, endTime-3600, endTime+1)
    assert.NoError(t, err)
    assert.NotContains(t, queueShows, "2001_3001")
}
