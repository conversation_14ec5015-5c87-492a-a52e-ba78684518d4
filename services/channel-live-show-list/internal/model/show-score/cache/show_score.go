package cache

import (
    "time"
    "fmt"
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "strconv"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "strings"
)

/*
使用hash记录某个节目的评分
key: show_id exp: 2*time.hour(或者做成可配置的)
field: uid 值: 评分

zset 记录待同步的节目评分信息
key: show_score_sync_queue
mem: show_id 值: 节目的结束时间
-- 定时器定期扫描该队列，将到期的节目进行同步
*/

const (
    ShowScoreHashKey      = "channel_live_show_score:c%d_u%d"    // 节目评分Hash key模板
    ShowScoreSyncQueueKey = "channel_live_show_score_sync_queue" //待同步队列ZSet key
    DefaultExpireTime     = 1 * time.Hour                        // 默认过期时间2小时
)

// AddShowScore 添加用户对节目的评分
func (c *Cache) AddShowScore(ctx context.Context, showId, uid, anchorUid, score uint32) (bool, error) {
    key := fmt.Sprintf(ShowScoreHashKey, showId, anchorUid)

    // 检查用户是否已评分
    exists := c.cmder.HExists(ctx, key, fmt.Sprintf("%d", uid))
    if exists.Err() != nil {
        log.ErrorWithCtx(ctx, "AddShowScore fail. showId:%d, uid:%d, err:%v", showId, uid, exists.Err())
        return false, exists.Err()
    }

    // 如果已存在，返回错误（不允许重复评分）
    if exists.Val() {
        log.WarnWithCtx(ctx, "AddShowScore,already exist showId:%d, uid:%d", showId, uid)
        return false, nil
    }

    // 添加评分
    result := c.cmder.HSet(ctx, key, fmt.Sprintf("%d", uid), fmt.Sprintf("%d", score))
    if result.Err() != nil {
        log.ErrorWithCtx(ctx, "AddShowScore fail. showId:%d, uid:%d, err:%v", showId, uid, result.Err())
        return false, result.Err()
    }

    // 设置过期时间
    c.cmder.Expire(ctx, key, DefaultExpireTime)
    return true, nil
}

// ShowScoreStats 节目评分统计信息
type ShowScoreStats struct {
    TotalScore uint32            // 总评分
    TotalCount uint32            // 总评分人数
    ScoreCount map[uint32]uint32 // 各分数的评分人数统计，key为分数(1-5)，value为人数
}

// GetShowScoreStats 获取节目评分统计信息
func (c *Cache) GetShowScoreStats(ctx context.Context, showId, anchorUid uint32) (*ShowScoreStats, error) {
    key := fmt.Sprintf(ShowScoreHashKey, showId, anchorUid)
    result := c.cmder.HGetAll(ctx, key)
    if result.Err() != nil {
        return nil, result.Err()
    }

    scores := result.Val()
    if len(scores) == 0 {
        return &ShowScoreStats{
            ScoreCount: make(map[uint32]uint32),
        }, nil
    }

    stats := &ShowScoreStats{
        ScoreCount: make(map[uint32]uint32),
    }

    for _, scoreStr := range scores {
        score, _ := strconv.ParseUint(scoreStr, 10, 32)
        stats.TotalCount++
        stats.TotalScore += uint32(score)
        stats.ScoreCount[uint32(score)]++
    }

    return stats, nil
}

// AddToShowScoreSyncQueue 添加节目到同步队列
func (c *Cache) AddToShowScoreSyncQueue(ctx context.Context, showId, anchorUid uint32, endTime int64) error {
    // 使用ZAdd命令将节目ID和结束时间添加到ZSet中
    result := c.cmder.ZAdd(ctx, ShowScoreSyncQueueKey, &redis.Z{
        Score:  float64(endTime),
        Member: fmt.Sprintf("%d_%d", showId, anchorUid),
    })
    return result.Err()
}

// GetExpiredShowsFromSyncQueue 获取已到期的节目列表
func (c *Cache) GetExpiredShowsFromSyncQueue(ctx context.Context, beginTs, endTs int64) ([]string, error) {
    // 使用ZRANGEBYSCORE获取所有结束时间小于等于当前时间的节目
    result := c.cmder.ZRangeByScore(ctx, ShowScoreSyncQueueKey, &redis.ZRangeBy{
        Min: fmt.Sprintf("%d", beginTs),
        Max: fmt.Sprintf("%d", endTs),
    })
    if result.Err() != nil {
        return nil, result.Err()
    }

    members := result.Val()
    return members, nil
}

func SplitSyncMem(mem string) (showId, anchorUid uint32, err error) {
    if mem == "" {
        return 0, 0, fmt.Errorf("empty mem string")
    }

    mems := strings.Split(mem, "_")
    if len(mems) != 2 {
        return 0, 0, fmt.Errorf("invalid mem format: expected showId_anchorUid, got %s", mem)
    }

    showId64, err := strconv.ParseUint(mems[0], 10, 32)
    if err != nil {
        return 0, 0, fmt.Errorf("failed to parse showId from %s: %w", mems[0], err)
    }

    anchorUid64, err := strconv.ParseUint(mems[1], 10, 32)
    if err != nil {
        return 0, 0, fmt.Errorf("failed to parse anchorUid from %s: %w", mems[1], err)
    }

    return uint32(showId64), uint32(anchorUid64), nil
}

// RemoveFromSyncQueue 从同步队列中移除节目
func (c *Cache) RemoveFromSyncQueue(ctx context.Context, members []string) error {
    if len(members) == 0 {
        return nil
    }

    result, err := c.cmder.ZRem(ctx, ShowScoreSyncQueueKey, members).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "RemoveFromSyncQueue fail. members:%v, err:%v", members, err)
        return err
    }
    log.InfoWithCtx(ctx, "RemoveFromSyncQueue,members:%v,result:%v", members, result)
    return nil
}
