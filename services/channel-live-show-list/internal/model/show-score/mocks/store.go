// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-live-show-list/internal/model/show-score/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	mysql "gitlab.ttyuyin.com/tyr/x/middleware/mysql"
	store "golang.52tt.com/services/channel-live-show-list/internal/model/show-score/store"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// BatUpsertAnchorScore mocks base method.
func (m *MockIStore) BatUpsertAnchorScore(arg0 context.Context, arg1 []*store.AnchorScore) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatUpsertAnchorScore", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatUpsertAnchorScore indicates an expected call of BatUpsertAnchorScore.
func (mr *MockIStoreMockRecorder) BatUpsertAnchorScore(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatUpsertAnchorScore", reflect.TypeOf((*MockIStore)(nil).BatUpsertAnchorScore), arg0, arg1)
}

// Close mocks base method.
func (m *MockIStore) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close))
}

// CreateAnchorScoreTbl mocks base method.
func (m *MockIStore) CreateAnchorScoreTbl(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAnchorScoreTbl", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAnchorScoreTbl indicates an expected call of CreateAnchorScoreTbl.
func (mr *MockIStoreMockRecorder) CreateAnchorScoreTbl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAnchorScoreTbl", reflect.TypeOf((*MockIStore)(nil).CreateAnchorScoreTbl), arg0)
}

// GetAnchorScoreByUidList mocks base method.
func (m *MockIStore) GetAnchorScoreByUidList(arg0 context.Context, arg1 []uint32) ([]*store.AnchorScore, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorScoreByUidList", arg0, arg1)
	ret0, _ := ret[0].([]*store.AnchorScore)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorScoreByUidList indicates an expected call of GetAnchorScoreByUidList.
func (mr *MockIStoreMockRecorder) GetAnchorScoreByUidList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorScoreByUidList", reflect.TypeOf((*MockIStore)(nil).GetAnchorScoreByUidList), arg0, arg1)
}

// GetAnchorTotalScoreByAnchorUid mocks base method.
func (m *MockIStore) GetAnchorTotalScoreByAnchorUid(arg0 context.Context, arg1 []uint32) ([]*store.ScoreSt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorTotalScoreByAnchorUid", arg0, arg1)
	ret0, _ := ret[0].([]*store.ScoreSt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorTotalScoreByAnchorUid indicates an expected call of GetAnchorTotalScoreByAnchorUid.
func (mr *MockIStoreMockRecorder) GetAnchorTotalScoreByAnchorUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorTotalScoreByAnchorUid", reflect.TypeOf((*MockIStore)(nil).GetAnchorTotalScoreByAnchorUid), arg0, arg1)
}

// GetDistinctAnchorUidByUpdateTime mocks base method.
func (m *MockIStore) GetDistinctAnchorUidByUpdateTime(arg0 context.Context, arg1, arg2 time.Time) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDistinctAnchorUidByUpdateTime", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDistinctAnchorUidByUpdateTime indicates an expected call of GetDistinctAnchorUidByUpdateTime.
func (mr *MockIStoreMockRecorder) GetDistinctAnchorUidByUpdateTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDistinctAnchorUidByUpdateTime", reflect.TypeOf((*MockIStore)(nil).GetDistinctAnchorUidByUpdateTime), arg0, arg1, arg2)
}

// GetShowScoreByShowId mocks base method.
func (m *MockIStore) GetShowScoreByShowId(arg0 context.Context, arg1 uint32) (*store.LiveShowScore, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowScoreByShowId", arg0, arg1)
	ret0, _ := ret[0].(*store.LiveShowScore)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowScoreByShowId indicates an expected call of GetShowScoreByShowId.
func (mr *MockIStoreMockRecorder) GetShowScoreByShowId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowScoreByShowId", reflect.TypeOf((*MockIStore)(nil).GetShowScoreByShowId), arg0, arg1)
}

// Transaction mocks base method.
func (m *MockIStore) Transaction(arg0 context.Context, arg1 func(mysql.Txx) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transaction", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Transaction indicates an expected call of Transaction.
func (mr *MockIStoreMockRecorder) Transaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transaction", reflect.TypeOf((*MockIStore)(nil).Transaction), arg0, arg1)
}

// UpsertOrIncrAnchorScore mocks base method.
func (m *MockIStore) UpsertOrIncrAnchorScore(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertOrIncrAnchorScore", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertOrIncrAnchorScore indicates an expected call of UpsertOrIncrAnchorScore.
func (mr *MockIStoreMockRecorder) UpsertOrIncrAnchorScore(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertOrIncrAnchorScore", reflect.TypeOf((*MockIStore)(nil).UpsertOrIncrAnchorScore), arg0, arg1, arg2, arg3)
}

// UpsertShowScore mocks base method.
func (m *MockIStore) UpsertShowScore(arg0 context.Context, arg1 *store.LiveShowScore) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertShowScore", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertShowScore indicates an expected call of UpsertShowScore.
func (mr *MockIStoreMockRecorder) UpsertShowScore(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertShowScore", reflect.TypeOf((*MockIStore)(nil).UpsertShowScore), arg0, arg1)
}
