// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-live-show-list/internal/model/show-score/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	cache "golang.52tt.com/services/channel-live-show-list/internal/model/show-score/cache"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// AddShowScore mocks base method.
func (m *MockICache) AddShowScore(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddShowScore", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddShowScore indicates an expected call of AddShowScore.
func (mr *MockICacheMockRecorder) AddShowScore(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddShowScore", reflect.TypeOf((*MockICache)(nil).AddShowScore), arg0, arg1, arg2, arg3, arg4)
}

// AddToShowScoreSyncQueue mocks base method.
func (m *MockICache) AddToShowScoreSyncQueue(arg0 context.Context, arg1, arg2 uint32, arg3 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddToShowScoreSyncQueue", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddToShowScoreSyncQueue indicates an expected call of AddToShowScoreSyncQueue.
func (mr *MockICacheMockRecorder) AddToShowScoreSyncQueue(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddToShowScoreSyncQueue", reflect.TypeOf((*MockICache)(nil).AddToShowScoreSyncQueue), arg0, arg1, arg2, arg3)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// GetExpiredShowsFromSyncQueue mocks base method.
func (m *MockICache) GetExpiredShowsFromSyncQueue(arg0 context.Context, arg1, arg2 int64) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExpiredShowsFromSyncQueue", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExpiredShowsFromSyncQueue indicates an expected call of GetExpiredShowsFromSyncQueue.
func (mr *MockICacheMockRecorder) GetExpiredShowsFromSyncQueue(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExpiredShowsFromSyncQueue", reflect.TypeOf((*MockICache)(nil).GetExpiredShowsFromSyncQueue), arg0, arg1, arg2)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetShowScoreStats mocks base method.
func (m *MockICache) GetShowScoreStats(arg0 context.Context, arg1, arg2 uint32) (*cache.ShowScoreStats, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowScoreStats", arg0, arg1, arg2)
	ret0, _ := ret[0].(*cache.ShowScoreStats)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowScoreStats indicates an expected call of GetShowScoreStats.
func (mr *MockICacheMockRecorder) GetShowScoreStats(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowScoreStats", reflect.TypeOf((*MockICache)(nil).GetShowScoreStats), arg0, arg1, arg2)
}

// RemoveFromSyncQueue mocks base method.
func (m *MockICache) RemoveFromSyncQueue(arg0 context.Context, arg1 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveFromSyncQueue", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveFromSyncQueue indicates an expected call of RemoveFromSyncQueue.
func (mr *MockICacheMockRecorder) RemoveFromSyncQueue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveFromSyncQueue", reflect.TypeOf((*MockICache)(nil).RemoveFromSyncQueue), arg0, arg1)
}
