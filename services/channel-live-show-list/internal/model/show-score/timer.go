package show_score

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "time"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
    "golang.52tt.com/services/channel-live-show-list/internal/model/show-score/cache"
    "golang.52tt.com/services/channel-live-show-list/internal/model/show-score/store"
)

func (m *ShowScore) startTimer() error {
    var err error
    m.timerD, err = timer.NewTimerD(context.Background(),
        "channel-live-show-score",
        timer.WithV8RedisCmdable(m.cache.GetRedisClient()))
    if err != nil {
        log.Errorf("startTimer NewTimerD err:%v", err)
        return err
    }

    // 定期全量更新计算主播评分 每日凌晨4点结算 "0 0 4 * * * "
    if err = m.timerD.AddCronTask("0 0 4 * * *", "settleAnchorShowScore",
        tasks.NewTracingTask(tasks.FuncTask(m.settleAnchorShowScore), "settleAnchorShowScore", 0)); err != nil {
        log.Errorf("startTimer AddCronTask settleAnchorShowScore err:%v", err)
        return err
    }
    //测试定时器逻辑
    //m.timerD.AddLocalIntervalTask(time.Minute,
    //    tasks.NewTracingTask(tasks.FuncTask(m.settleAnchorShowScore), "settleAnchorShowScore2", 0))

    // 同步数据定时器，每分钟执行
    m.timerD.AddIntervalTask("checkShowScoreSyncHandle", time.Minute, tasks.NewTracingTask(tasks.FuncTask(m.checkShowScoreSyncHandle), "checkShowScoreSyncHandle", 0))

    m.timerD.Start()
    return nil
}

func (m *ShowScore) settleAnchorShowScore(ctx context.Context) {
    ctx, cancel := context.WithTimeout(ctx, 20*time.Minute)
    defer cancel()

    now := time.Now()
    // beginTime: 昨日0点 endTime: 今日0点
    beginTime := time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, time.Local)
    endTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)

    // 查出可能需要更新的主播uid
    anchorList, err := m.store.GetDistinctAnchorUidByUpdateTime(ctx, beginTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "settleAnchorShowScore GetDistinctAnchorUidByUpdateTime error: %v", err)
        return
    }

    log.DebugWithCtx(ctx, "settleAnchorShowScore anchorList: %v", anchorList)

    scoreList := make([]*store.ScoreSt, 0)
    // 分批查询主播总分，每次最多50个
    batchSize := 50
    for i := 0; i < len(anchorList); i += batchSize {
        end := i + batchSize
        if end > len(anchorList) {
            end = len(anchorList)
        }
        list, err := m.store.GetAnchorTotalScoreByAnchorUid(ctx, anchorList[i:end])
        if err != nil {
            log.ErrorWithCtx(ctx, "settleAnchorShowScore GetAnchorTotalScoreByAnchorUid error: %v", err)
            return
        }
        scoreList = append(scoreList, list...)
    }

    // 构造主播评分列表
    anchorScoreList := make([]*store.AnchorScore, len(scoreList))
    for i, score := range scoreList {
        anchorScoreList[i] = &store.AnchorScore{
            Uid:            score.AnchorUid,
            TotalScore:     score.TotalScore,
            TotalRatingCnt: score.TotalCount,
        }
    }

    // 分批更新主播评分表，每次最多50条
    updateBatchSize := 50
    for i := 0; i < len(anchorScoreList); i += updateBatchSize {
        end := i + updateBatchSize
        if end > len(anchorScoreList) {
            end = len(anchorScoreList)
        }

        err = m.store.BatUpsertAnchorScore(ctx, anchorScoreList[i:end])
        if err != nil {
            log.ErrorWithCtx(ctx, "BatUpsertAnchorScore: failed to update anchor score, err: %v", err)
            return
        }
    }

    log.InfoWithCtx(ctx, "settleAnchorShowScore done, update cnt: %d", len(anchorScoreList))
}

func (m *ShowScore) checkShowScoreSyncHandle(ctx context.Context) {
    ctx, cancel := context.WithTimeout(ctx, 1*time.Minute)
    defer cancel()

    log.DebugWithCtx(ctx, "checkShowScoreSyncHandle start")

    now := time.Now()
    expList, err := m.cache.GetExpiredShowsFromSyncQueue(ctx, now.Add(-time.Hour).Unix(), now.Unix())
    if err != nil {
        log.ErrorWithCtx(ctx, "checkShowScoreSyncHandle GetExpiredShowsFromSyncQueue error: %v", err)
        return
    }

    for _, v := range expList {
        // 拆分字符串
        showId, anchorUid, err := cache.SplitSyncMem(v)
        if err != nil {
            log.ErrorWithCtx(ctx, "checkShowScoreSyncHandle SplitSyncMem error: %v", err)
            continue
        }

        log.DebugWithCtx(ctx, "checkShowScoreSyncHandle showId: %d, anchorUid: %d", showId, anchorUid)
        // 查询缓存中的节目评分
        scoreStats, err := m.cache.GetShowScoreStats(ctx, showId, anchorUid)
        if err != nil {
            log.ErrorWithCtx(ctx, "checkShowScoreSyncHandle GetShowScoreStats error: %v", err)
            continue
        }

        if scoreStats.TotalScore > 0 {
            // 节目评分落库
            ok, err := m.store.UpsertShowScore(ctx, &store.LiveShowScore{
                ShowId:     showId,
                AnchorUid:  anchorUid,
                Score1Cnt:  scoreStats.ScoreCount[1],
                Score2Cnt:  scoreStats.ScoreCount[2],
                Score3Cnt:  scoreStats.ScoreCount[3],
                Score4Cnt:  scoreStats.ScoreCount[4],
                Score5Cnt:  scoreStats.ScoreCount[5],
                TotalScore: scoreStats.TotalScore,
                TotalCount: scoreStats.TotalCount,
            })
            if err != nil {
                log.ErrorWithCtx(ctx, "checkShowScoreSyncHandle setShowScore failed. err: %v", err)
                continue
            }

            if ok {
                err = m.store.UpsertOrIncrAnchorScore(ctx, anchorUid, scoreStats.TotalScore, scoreStats.TotalCount)
                if err != nil {
                    log.ErrorWithCtx(ctx, "checkShowScoreSyncHandle upsertOrIncrAnchorScore failed. err: %v", err)
                    //continue
                }
            }
        }

        err = m.cache.RemoveFromSyncQueue(ctx, []string{v})
        if err != nil {
            log.ErrorWithCtx(ctx, "checkShowScoreSyncHandle RemoveFromSyncQueue failed. err: %v", err)
            continue
        }

        log.InfoWithCtx(ctx, "checkShowScoreSyncHandle success. showId: %d, anchorUid: %d, totalScore: %d, totalCount: %d",
            showId, anchorUid, scoreStats.TotalScore, scoreStats.TotalCount)
    }

}
