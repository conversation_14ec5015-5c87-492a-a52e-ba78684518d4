package show_score

import(
	entity "golang.52tt.com/services/channel-live-show-list/internal/entity"
	context "context"
)

type IShowScore interface {
	AddToShowScoreSyncQueue(ctx context.Context, showId, anchorUid uint32, endTime int64) error
	GetAnchorScoreByUidList(ctx context.Context, uidList []uint32) (map[uint32]*entity.AnchorScore,error)
	GetScoreStByAnchorUidShowId(ctx context.Context, anchorUid, showId uint32) (entity.AnchorScore,error)
	LiveShowRating(ctx context.Context, uid, anchorUid, showId, score uint32) (bool,error)
	Stop() 
}

