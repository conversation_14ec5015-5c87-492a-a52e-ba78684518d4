package show_score

//go:generate quicksilver-cli test interface ../show-score
//go:generate mockgen -destination=../mocks/show-score.go -package=mocks golang.52tt.com/services/channel-live-show-list/internal/model/show-score IShowScore

import (
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    anti_corruption_layer "golang.52tt.com/services/channel-live-show-list/internal/model/anti-corruption-layer"
    "sync"
    "golang.52tt.com/services/channel-live-show-list/internal/model/show-score/store"
    "golang.52tt.com/services/channel-live-show-list/internal/model/show-score/cache"
    "golang.52tt.com/services/channel-live-show-list/internal/conf"
    "golang.52tt.com/services/channel-live-show-list/internal/entity"
    "context"
)

type ShowScore struct {
    store      store.IStore
    cache      cache.ICache
    bc         conf.IBusinessConfManager
    acLayerMgr anti_corruption_layer.IACLayer

    timerD   *timer.Timer
    wg       sync.WaitGroup
    shutDown chan struct{}
}

func NewMgr(s mysql.DBx, cacheClient redis.Cmdable, bc conf.IBusinessConfManager,
    acLayerMgr anti_corruption_layer.IACLayer) (*ShowScore, error) {
    mysqlStore := store.NewStore(s)
    redisCli := cache.NewCache(cacheClient)

    m := &ShowScore{
        store:      mysqlStore,
        cache:      redisCli,
        bc:         bc,
        shutDown:   make(chan struct{}),
        acLayerMgr: acLayerMgr,
    }

    err := m.startTimer()
    if err != nil {
        log.Errorf("NewMgr startTimer err:%v", err)
        return m, err
    }

    return m, nil
}

func (m *ShowScore) Stop() {
    m.timerD.Stop()
    close(m.shutDown)
    m.wg.Wait()
    _ = m.cache.Close()
    _ = m.store.Close()
}

// GetAnchorScoreByUidList 获取主播评分
func (m *ShowScore) GetAnchorScoreByUidList(ctx context.Context, uidList []uint32) (map[uint32]*entity.AnchorScore, error) {
    if len(uidList) == 0 {
        return nil, nil
    }

    scoreList, err := m.store.GetAnchorScoreByUidList(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetAnchorScoreByUidList failed. uidList:[%+v] err:%v", uidList, err)
        return nil, err
    }

    retMap := make(map[uint32]*entity.AnchorScore, len(scoreList))
    for _, score := range scoreList {
        retMap[score.Uid] = &entity.AnchorScore{
            Uid:            score.Uid,
            TotalScore:     score.TotalScore,
            TotalRatingCnt: score.TotalRatingCnt,
        }
    }
    return retMap, nil
}

// LiveShowRating 直播打分
func (m *ShowScore) LiveShowRating(ctx context.Context, uid, anchorUid, showId, score uint32) (bool, error) {
    return m.cache.AddShowScore(ctx, showId, uid, anchorUid, score)
}

func (m *ShowScore) AddToShowScoreSyncQueue(ctx context.Context, showId, anchorUid uint32, endTime int64) error {
    if endTime == 0 {
        return nil
    }
    err := m.cache.AddToShowScoreSyncQueue(ctx, showId, anchorUid, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "AddToShowScoreSyncQueue failed. showId:%d err:%v", showId, err)
        return err
    }

    log.InfoWithCtx(ctx, "AddToShowScoreSyncQueue success. showId:%d anchorUid:%d endTime:%d", showId, anchorUid, endTime)
    return nil
}

// GetScoreStByAnchorUidShowId 根据主播id和showId 查询数据
func (m *ShowScore) GetScoreStByAnchorUidShowId(ctx context.Context, anchorUid, showId uint32) (entity.AnchorScore, error) {
    scoreSt, err := m.store.GetShowScoreByShowId(ctx, showId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetScoreStByAnchorUidShowId failed. anchorUid:%d showId:%d err:%v",
            anchorUid, showId, err)
        return entity.AnchorScore{}, err
    }

    if scoreSt == nil {
        return entity.AnchorScore{}, nil
    }

    return entity.AnchorScore{
        Uid:            scoreSt.AnchorUid,
        TotalScore:     scoreSt.TotalScore,
        TotalRatingCnt: scoreSt.TotalCount,
    }, nil
}
