package show_score

import (
    "context"
    "testing"
    "github.com/golang/mock/gomock"
    "golang.52tt.com/services/channel-live-show-list/internal/model/show-score/mocks"
    mocksModel "golang.52tt.com/services/channel-live-show-list/internal/model/mocks"
    mocksConf "golang.52tt.com/services/channel-live-show-list/internal/conf/mocks"
    "golang.52tt.com/services/channel-live-show-list/internal/model/show-score/store"
    "golang.52tt.com/services/channel-live-show-list/internal/entity"
    "time"
    "github.com/pkg/errors"
    "github.com/stretchr/testify/assert"
)

var (
    testMgr *ShowScore

    ctx       = context.Background()
    mockCache *mocks.MockICache
    mockStore *mocks.MockIStore
    mockConf  *mocksConf.MockIBusinessConfManager
    mockACL   *mocksModel.MockIACLayer

    testUid = uint32(1)
)

func initTestMgr(t *testing.T) *gomock.Controller {
    ctrl := gomock.NewController(t)

    mockCache = mocks.NewMockICache(ctrl)
    mockStore = mocks.NewMockIStore(ctrl)
    mockConf = mocksConf.NewMockIBusinessConfManager(ctrl)
    mockACL = mocksModel.NewMockIACLayer(ctrl)

    testMgr = &ShowScore{
        cache:      mockCache,
        store:      mockStore,
        bc:         mockConf,
        acLayerMgr: mockACL,
    }

    return ctrl
}

func TestShowScore_GetAnchorScoreByUidList(t *testing.T) {
    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    uidList := []uint32{1, 2, 3}
    anchorScores := []*store.AnchorScore{
        {
            Uid:            1,
            TotalScore:     100,
            TotalRatingCnt: 10,
        },
        {
            Uid:            2,
            TotalScore:     200,
            TotalRatingCnt: 20,
        },
    }

    tests := []struct {
        name    string
        uidList []uint32
        setup   func()
        want    map[uint32]*entity.AnchorScore
        wantErr bool
    }{
        {
            name:    "success",
            uidList: uidList,
            setup: func() {
                mockStore.EXPECT().GetAnchorScoreByUidList(ctx, uidList).Return(anchorScores, nil)
            },
            want: map[uint32]*entity.AnchorScore{
                1: {
                    Uid:            1,
                    TotalScore:     100,
                    TotalRatingCnt: 10,
                },
                2: {
                    Uid:            2,
                    TotalScore:     200,
                    TotalRatingCnt: 20,
                },
            },
            wantErr: false,
        },
        {
            name:    "empty uid list",
            uidList: []uint32{},
            setup:   func() {},
            want:    nil,
            wantErr: false,
        },
        {
            name:    "store error",
            uidList: uidList,
            setup: func() {
                mockStore.EXPECT().GetAnchorScoreByUidList(ctx, uidList).Return(nil, errors.New("store error"))
            },
            want:    nil,
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.setup()
            got, err := testMgr.GetAnchorScoreByUidList(ctx, tt.uidList)
            if (err != nil) != tt.wantErr {
                t.Errorf("ShowScore.GetAnchorScoreByUidList() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            assert.Equal(t, tt.want, got)
        })
    }
}

func TestShowScore_LiveShowRating(t *testing.T) {
    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    uid := uint32(1)
    anchorUid := uint32(2)
    showId := uint32(3)
    score := uint32(5)

    tests := []struct {
        name      string
        uid       uint32
        anchorUid uint32
        showId    uint32
        score     uint32
        setup     func()
        want      bool
        wantErr   bool
    }{
        {
            name:      "success",
            uid:       uid,
            anchorUid: anchorUid,
            showId:    showId,
            score:     score,
            setup: func() {
                mockCache.EXPECT().AddShowScore(ctx, showId, uid, anchorUid, score).Return(true, nil)
            },
            want:    true,
            wantErr: false,
        },
        {
            name:      "cache error",
            uid:       uid,
            anchorUid: anchorUid,
            showId:    showId,
            score:     score,
            setup: func() {
                mockCache.EXPECT().AddShowScore(ctx, showId, uid, anchorUid, score).Return(false, errors.New("cache error"))
            },
            want:    false,
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.setup()
            got, err := testMgr.LiveShowRating(ctx, tt.uid, tt.anchorUid, tt.showId, tt.score)
            if (err != nil) != tt.wantErr {
                t.Errorf("ShowScore.LiveShowRating() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if got != tt.want {
                t.Errorf("ShowScore.LiveShowRating() = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestShowScore_AddToShowScoreSyncQueue(t *testing.T) {
    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    showId := uint32(1)
    anchorUid := uint32(2)
    endTime := time.Now().Add(time.Hour).Unix()

    tests := []struct {
        name      string
        showId    uint32
        anchorUid uint32
        endTime   int64
        setup     func()
        wantErr   bool
    }{
        {
            name:      "success",
            showId:    showId,
            anchorUid: anchorUid,
            endTime:   endTime,
            setup: func() {
                mockCache.EXPECT().AddToShowScoreSyncQueue(ctx, showId, anchorUid, endTime).Return(nil)
            },
            wantErr: false,
        },
        {
            name:      "zero end time",
            showId:    showId,
            anchorUid: anchorUid,
            endTime:   0,
            setup:     func() {},
            wantErr:   false,
        },
        {
            name:      "cache error",
            showId:    showId,
            anchorUid: anchorUid,
            endTime:   endTime,
            setup: func() {
                mockCache.EXPECT().AddToShowScoreSyncQueue(ctx, showId, anchorUid, endTime).Return(errors.New("cache error"))
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.setup()
            err := testMgr.AddToShowScoreSyncQueue(ctx, tt.showId, tt.anchorUid, tt.endTime)
            if (err != nil) != tt.wantErr {
                t.Errorf("ShowScore.AddToShowScoreSyncQueue() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestShowScore_GetScoreStByAnchorUidShowId(t *testing.T) {
    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    anchorUid := uint32(1)
    showId := uint32(2)
    liveShowScore := &store.LiveShowScore{
        AnchorUid:  anchorUid,
        TotalScore: 100,
        TotalCount: 10,
    }

    tests := []struct {
        name      string
        anchorUid uint32
        showId    uint32
        setup     func()
        want      entity.AnchorScore
    }{
        {
            name:      "success",
            anchorUid: anchorUid,
            showId:    showId,
            setup: func() {
                mockStore.EXPECT().GetShowScoreByShowId(ctx, showId).Return(liveShowScore, nil)
            },
            want: entity.AnchorScore{
                Uid:            anchorUid,
                TotalScore:     100,
                TotalRatingCnt: 10,
            },
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.setup()
            got, err := testMgr.GetScoreStByAnchorUidShowId(ctx, tt.anchorUid, tt.showId)
            assert.NoError(t, err)
            assert.Equal(t, tt.want, got)
        })
    }
}
