// timer_test.go 修复版

package show_score

import (
    "context"
    "errors"
    "testing"
    "time"

    "github.com/golang/mock/gomock"
    "golang.52tt.com/services/channel-live-show-list/internal/model/show-score/cache"
    "golang.52tt.com/services/channel-live-show-list/internal/model/show-score/mocks"
    mocksModel "golang.52tt.com/services/channel-live-show-list/internal/model/mocks"
    mocksConf "golang.52tt.com/services/channel-live-show-list/internal/conf/mocks"
    "golang.52tt.com/services/channel-live-show-list/internal/model/show-score/store"
)

func initTestMgrWithCtrl(ctrl *gomock.Controller) (*ShowScore, *mocks.MockICache, *mocks.MockIStore, *mocksConf.MockIBusinessConfManager, *mocksModel.MockIACLayer) {
    mockCache := mocks.NewMockICache(ctrl)
    mockStore := mocks.NewMockIStore(ctrl)
    mockConf := mocksConf.NewMockIBusinessConfManager(ctrl)
    mockACL := mocksModel.NewMockIACLayer(ctrl)

    testMgr := &ShowScore{
        cache:      mockCache,
        store:      mockStore,
        bc:         mockConf,
        acLayerMgr: mockACL,
    }

    return testMgr, mockCache, mockStore, mockConf, mockACL
}

func TestShowScore_settleAnchorShowScore(t *testing.T) {
    now := time.Now()
    beginTime := time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, time.Local)
    endTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)

    anchorUids := []uint32{1, 2, 3, 4, 5}
    scoreSts := []*store.ScoreSt{
        {AnchorUid: 1, TotalScore: 100, TotalCount: 10},
        {AnchorUid: 2, TotalScore: 200, TotalCount: 20},
        {AnchorUid: 3, TotalScore: 300, TotalCount: 30},
        {AnchorUid: 4, TotalScore: 400, TotalCount: 40},
        {AnchorUid: 5, TotalScore: 500, TotalCount: 50},
    }

    anchorScores := []*store.AnchorScore{
        {Uid: 1, TotalScore: 100, TotalRatingCnt: 10},
        {Uid: 2, TotalScore: 200, TotalRatingCnt: 20},
        {Uid: 3, TotalScore: 300, TotalRatingCnt: 30},
        {Uid: 4, TotalScore: 400, TotalRatingCnt: 40},
        {Uid: 5, TotalScore: 500, TotalRatingCnt: 50},
    }

    tests := []struct {
        name  string
        setup func(*ShowScore, *mocks.MockICache, *mocks.MockIStore)
    }{
        {
            name: "success",
            setup: func(mgr *ShowScore, mockCache *mocks.MockICache, mockStore *mocks.MockIStore) {
                mockStore.EXPECT().GetDistinctAnchorUidByUpdateTime(gomock.Any(), beginTime, endTime).Return(anchorUids, nil)
                mockStore.EXPECT().GetAnchorTotalScoreByAnchorUid(gomock.Any(), []uint32{1, 2, 3, 4, 5}).Return(scoreSts, nil)
                mockStore.EXPECT().BatUpsertAnchorScore(gomock.Any(), anchorScores).Return(nil)
            },
        },
        {
            name: "get anchor uid error",
            setup: func(mgr *ShowScore, mockCache *mocks.MockICache, mockStore *mocks.MockIStore) {
                mockStore.EXPECT().GetDistinctAnchorUidByUpdateTime(gomock.Any(), beginTime, endTime).Return(nil, errors.New("db error"))
            },
        },
        {
            name: "get anchor score error",
            setup: func(mgr *ShowScore, mockCache *mocks.MockICache, mockStore *mocks.MockIStore) {
                mockStore.EXPECT().GetDistinctAnchorUidByUpdateTime(gomock.Any(), beginTime, endTime).Return(anchorUids, nil)
                mockStore.EXPECT().GetAnchorTotalScoreByAnchorUid(gomock.Any(), []uint32{1, 2, 3, 4, 5}).Return(nil, errors.New("db error"))
            },
        },
        {
            name: "upsert anchor score error",
            setup: func(mgr *ShowScore, mockCache *mocks.MockICache, mockStore *mocks.MockIStore) {
                mockStore.EXPECT().GetDistinctAnchorUidByUpdateTime(gomock.Any(), beginTime, endTime).Return(anchorUids, nil)
                mockStore.EXPECT().GetAnchorTotalScoreByAnchorUid(gomock.Any(), []uint32{1, 2, 3, 4, 5}).Return(scoreSts, nil).AnyTimes()
                mockStore.EXPECT().BatUpsertAnchorScore(gomock.Any(), anchorScores).Return(errors.New("db error"))
            },
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            ctrl := gomock.NewController(t)
            defer ctrl.Finish()

            mgr, _, mockStore, _, _ := initTestMgrWithCtrl(ctrl)
            tt.setup(mgr, nil, mockStore)
            mgr.settleAnchorShowScore(context.Background())
        })
    }
}

func TestShowScore_checkShowScoreSyncHandle(t *testing.T) {
    expList := []string{"1_10", "2_20"}
    scoreStats := &cache.ShowScoreStats{
        TotalScore: 100,
        TotalCount: 10,
        ScoreCount: map[uint32]uint32{
            1: 1,
            2: 2,
            3: 3,
            4: 4,
            5: 5,
        },
    }

    tests := []struct {
        name  string
        setup func(*ShowScore, *mocks.MockICache, *mocks.MockIStore)
    }{
        {
            name: "success",
            setup: func(mgr *ShowScore, mockCache *mocks.MockICache, mockStore *mocks.MockIStore) {
                mockCache.EXPECT().GetExpiredShowsFromSyncQueue(gomock.Any(), gomock.Any(), gomock.Any()).Return(expList, nil)
                mockCache.EXPECT().GetShowScoreStats(gomock.Any(), uint32(1), uint32(10)).Return(scoreStats, nil).Times(1)
                mockCache.EXPECT().GetShowScoreStats(gomock.Any(), uint32(2), uint32(20)).Return(scoreStats, nil).Times(1)
                mockStore.EXPECT().UpsertShowScore(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
                mockStore.EXPECT().UpsertOrIncrAnchorScore(gomock.Any(), uint32(10), uint32(100), uint32(10)).Return(nil).Times(1)
                mockStore.EXPECT().UpsertOrIncrAnchorScore(gomock.Any(), uint32(20), uint32(100), uint32(10)).Return(nil).Times(1)
                mockCache.EXPECT().RemoveFromSyncQueue(gomock.Any(), []string{"1_10"}).Return(nil).Times(1)
                mockCache.EXPECT().RemoveFromSyncQueue(gomock.Any(), []string{"2_20"}).Return(nil).Times(1)
            },
        },
        {
            name: "get expired shows error",
            setup: func(mgr *ShowScore, mockCache *mocks.MockICache, mockStore *mocks.MockIStore) {
                mockCache.EXPECT().GetExpiredShowsFromSyncQueue(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("cache error"))
            },
        },
        {
            name: "split sync mem error",
            setup: func(mgr *ShowScore, mockCache *mocks.MockICache, mockStore *mocks.MockIStore) {
                invalidList := []string{"invalid_format"}
                mockCache.EXPECT().GetExpiredShowsFromSyncQueue(gomock.Any(), gomock.Any(), gomock.Any()).Return(invalidList, nil)
            },
        },
        {
            name: "get show score stats error",
            setup: func(mgr *ShowScore, mockCache *mocks.MockICache, mockStore *mocks.MockIStore) {
                mockCache.EXPECT().GetExpiredShowsFromSyncQueue(gomock.Any(), gomock.Any(), gomock.Any()).Return(expList, nil)
                mockCache.EXPECT().GetShowScoreStats(gomock.Any(), uint32(1), uint32(10)).Return(nil, errors.New("cache error")).Times(1)
                mockCache.EXPECT().GetShowScoreStats(gomock.Any(), uint32(2), uint32(20)).Return(nil, errors.New("cache error")).Times(1)
            },
        },
        {
            name: "upsert show score error",
            setup: func(mgr *ShowScore, mockCache *mocks.MockICache, mockStore *mocks.MockIStore) {
                mockCache.EXPECT().GetExpiredShowsFromSyncQueue(gomock.Any(), gomock.Any(), gomock.Any()).Return(expList, nil)
                mockCache.EXPECT().GetShowScoreStats(gomock.Any(), uint32(1), uint32(10)).Return(scoreStats, nil).Times(1)
                mockCache.EXPECT().GetShowScoreStats(gomock.Any(), uint32(2), uint32(20)).Return(scoreStats, nil).Times(1)
                mockStore.EXPECT().UpsertShowScore(gomock.Any(), gomock.Any()).Return(false, errors.New("db error")).AnyTimes()
            },
        },
        {
            name: "upsert anchor score error",
            setup: func(mgr *ShowScore, mockCache *mocks.MockICache, mockStore *mocks.MockIStore) {
                mockCache.EXPECT().GetExpiredShowsFromSyncQueue(gomock.Any(), gomock.Any(), gomock.Any()).Return(expList, nil)
                mockCache.EXPECT().GetShowScoreStats(gomock.Any(), uint32(1), uint32(10)).Return(scoreStats, nil).Times(1)
                mockCache.EXPECT().GetShowScoreStats(gomock.Any(), uint32(2), uint32(20)).Return(scoreStats, nil).Times(1)
                mockStore.EXPECT().UpsertShowScore(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
                mockStore.EXPECT().UpsertOrIncrAnchorScore(gomock.Any(), uint32(10), uint32(100), uint32(10)).Return(errors.New("db error")).Times(1)
                mockStore.EXPECT().UpsertOrIncrAnchorScore(gomock.Any(), uint32(20), uint32(100), uint32(10)).Return(errors.New("db error")).Times(1)
                mockCache.EXPECT().RemoveFromSyncQueue(gomock.Any(), []string{"1_10"}).Return(nil).Times(1)
                mockCache.EXPECT().RemoveFromSyncQueue(gomock.Any(), []string{"2_20"}).Return(nil).Times(1)
            },
        },
        {
            name: "remove from sync queue error",
            setup: func(mgr *ShowScore, mockCache *mocks.MockICache, mockStore *mocks.MockIStore) {
                mockCache.EXPECT().GetExpiredShowsFromSyncQueue(gomock.Any(), gomock.Any(), gomock.Any()).Return(expList, nil)
                mockCache.EXPECT().GetShowScoreStats(gomock.Any(), uint32(1), uint32(10)).Return(scoreStats, nil).Times(1)
                mockCache.EXPECT().GetShowScoreStats(gomock.Any(), uint32(2), uint32(20)).Return(scoreStats, nil).Times(1)
                mockStore.EXPECT().UpsertShowScore(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
                mockStore.EXPECT().UpsertOrIncrAnchorScore(gomock.Any(), uint32(10), uint32(100), uint32(10)).Return(nil).Times(1)
                mockStore.EXPECT().UpsertOrIncrAnchorScore(gomock.Any(), uint32(20), uint32(100), uint32(10)).Return(nil).Times(1)
                mockCache.EXPECT().RemoveFromSyncQueue(gomock.Any(), []string{"1_10"}).Return(errors.New("cache error")).Times(1)
                mockCache.EXPECT().RemoveFromSyncQueue(gomock.Any(), []string{"2_20"}).Return(errors.New("cache error")).Times(1)
            },
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            ctrl := gomock.NewController(t)
            defer ctrl.Finish()

            mgr, mockCache, mockStore, _, _ := initTestMgrWithCtrl(ctrl)
            tt.setup(mgr, mockCache, mockStore)
            mgr.checkShowScoreSyncHandle(context.Background())
        })
    }
}

// 添加SplitSyncMem函数的测试
func TestSplitSyncMem(t *testing.T) {
    tests := []struct {
        name          string
        mem           string
        wantShowId    uint32
        wantAnchorUid uint32
        wantErr       bool
    }{
        {
            name:          "valid format",
            mem:           "123_456",
            wantShowId:    123,
            wantAnchorUid: 456,
            wantErr:       false,
        },
        {
            name:          "empty string",
            mem:           "",
            wantShowId:    0,
            wantAnchorUid: 0,
            wantErr:       true,
        },
        {
            name:          "invalid format - no underscore",
            mem:           "123456",
            wantShowId:    0,
            wantAnchorUid: 0,
            wantErr:       true,
        },
        {
            name:          "invalid format - too many underscores",
            mem:           "123_456_789",
            wantShowId:    0,
            wantAnchorUid: 0,
            wantErr:       true,
        },
        {
            name:          "invalid showId",
            mem:           "abc_123",
            wantShowId:    0,
            wantAnchorUid: 0,
            wantErr:       true,
        },
        {
            name:          "invalid anchorUid",
            mem:           "123_def",
            wantShowId:    0,
            wantAnchorUid: 0,
            wantErr:       true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            gotShowId, gotAnchorUid, err := cache.SplitSyncMem(tt.mem)
            if (err != nil) != tt.wantErr {
                t.Errorf("SplitSyncMem() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if gotShowId != tt.wantShowId {
                t.Errorf("SplitSyncMem() gotShowId = %v, want %v", gotShowId, tt.wantShowId)
            }
            if gotAnchorUid != tt.wantAnchorUid {
                t.Errorf("SplitSyncMem() gotAnchorUid = %v, want %v", gotAnchorUid, tt.wantAnchorUid)
            }
        })
    }
}
