// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-live-show-list/internal/model/show-list/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	store "golang.52tt.com/services/channel-live-show-list/internal/model/show-list/store"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockIStore) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close))
}

// CountChannelLiveShowTimeRange mocks base method.
func (m *MockIStore) CountChannelLiveShowTimeRange(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountChannelLiveShowTimeRange", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountChannelLiveShowTimeRange indicates an expected call of CountChannelLiveShowTimeRange.
func (mr *MockIStoreMockRecorder) CountChannelLiveShowTimeRange(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountChannelLiveShowTimeRange", reflect.TypeOf((*MockIStore)(nil).CountChannelLiveShowTimeRange), arg0, arg1, arg2)
}

// CountMachineAuditDoneChannelLiveShowApproval mocks base method.
func (m *MockIStore) CountMachineAuditDoneChannelLiveShowApproval(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountMachineAuditDoneChannelLiveShowApproval", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountMachineAuditDoneChannelLiveShowApproval indicates an expected call of CountMachineAuditDoneChannelLiveShowApproval.
func (mr *MockIStoreMockRecorder) CountMachineAuditDoneChannelLiveShowApproval(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountMachineAuditDoneChannelLiveShowApproval", reflect.TypeOf((*MockIStore)(nil).CountMachineAuditDoneChannelLiveShowApproval), arg0, arg1, arg2, arg3)
}

// CreateChannelLiveShowListTable mocks base method.
func (m *MockIStore) CreateChannelLiveShowListTable(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChannelLiveShowListTable", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateChannelLiveShowListTable indicates an expected call of CreateChannelLiveShowListTable.
func (mr *MockIStoreMockRecorder) CreateChannelLiveShowListTable(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChannelLiveShowListTable", reflect.TypeOf((*MockIStore)(nil).CreateChannelLiveShowListTable), arg0)
}

// DeleteChannelLiveShowTag mocks base method.
func (m *MockIStore) DeleteChannelLiveShowTag(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteChannelLiveShowTag", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteChannelLiveShowTag indicates an expected call of DeleteChannelLiveShowTag.
func (mr *MockIStoreMockRecorder) DeleteChannelLiveShowTag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteChannelLiveShowTag", reflect.TypeOf((*MockIStore)(nil).DeleteChannelLiveShowTag), arg0, arg1)
}

// ExistsPendingPassApproval mocks base method.
func (m *MockIStore) ExistsPendingPassApproval(arg0 context.Context, arg1, arg2 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExistsPendingPassApproval", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExistsPendingPassApproval indicates an expected call of ExistsPendingPassApproval.
func (mr *MockIStoreMockRecorder) ExistsPendingPassApproval(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExistsPendingPassApproval", reflect.TypeOf((*MockIStore)(nil).ExistsPendingPassApproval), arg0, arg1, arg2)
}

// GetChannelLiveShowApprovalById mocks base method.
func (m *MockIStore) GetChannelLiveShowApprovalById(arg0 context.Context, arg1 uint32) (*store.ChannelLiveShowApproval, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveShowApprovalById", arg0, arg1)
	ret0, _ := ret[0].(*store.ChannelLiveShowApproval)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveShowApprovalById indicates an expected call of GetChannelLiveShowApprovalById.
func (mr *MockIStoreMockRecorder) GetChannelLiveShowApprovalById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowApprovalById", reflect.TypeOf((*MockIStore)(nil).GetChannelLiveShowApprovalById), arg0, arg1)
}

// GetChannelLiveShowByAnchorUid mocks base method.
func (m *MockIStore) GetChannelLiveShowByAnchorUid(arg0 context.Context, arg1 uint32) ([]*store.ChannelLiveShow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveShowByAnchorUid", arg0, arg1)
	ret0, _ := ret[0].([]*store.ChannelLiveShow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveShowByAnchorUid indicates an expected call of GetChannelLiveShowByAnchorUid.
func (mr *MockIStoreMockRecorder) GetChannelLiveShowByAnchorUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowByAnchorUid", reflect.TypeOf((*MockIStore)(nil).GetChannelLiveShowByAnchorUid), arg0, arg1)
}

// GetChannelLiveShowByApprovalId mocks base method.
func (m *MockIStore) GetChannelLiveShowByApprovalId(arg0 context.Context, arg1 uint32) (*store.ChannelLiveShow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveShowByApprovalId", arg0, arg1)
	ret0, _ := ret[0].(*store.ChannelLiveShow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveShowByApprovalId indicates an expected call of GetChannelLiveShowByApprovalId.
func (mr *MockIStoreMockRecorder) GetChannelLiveShowByApprovalId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowByApprovalId", reflect.TypeOf((*MockIStore)(nil).GetChannelLiveShowByApprovalId), arg0, arg1)
}

// GetChannelLiveShowByShowId mocks base method.
func (m *MockIStore) GetChannelLiveShowByShowId(arg0 context.Context, arg1 uint32) (*store.ChannelLiveShow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveShowByShowId", arg0, arg1)
	ret0, _ := ret[0].(*store.ChannelLiveShow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveShowByShowId indicates an expected call of GetChannelLiveShowByShowId.
func (mr *MockIStoreMockRecorder) GetChannelLiveShowByShowId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowByShowId", reflect.TypeOf((*MockIStore)(nil).GetChannelLiveShowByShowId), arg0, arg1)
}

// GetChannelLiveShowCntByStartTimeTag mocks base method.
func (m *MockIStore) GetChannelLiveShowCntByStartTimeTag(arg0 context.Context, arg1, arg2 uint32, arg3, arg4 []uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveShowCntByStartTimeTag", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveShowCntByStartTimeTag indicates an expected call of GetChannelLiveShowCntByStartTimeTag.
func (mr *MockIStoreMockRecorder) GetChannelLiveShowCntByStartTimeTag(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowCntByStartTimeTag", reflect.TypeOf((*MockIStore)(nil).GetChannelLiveShowCntByStartTimeTag), arg0, arg1, arg2, arg3, arg4)
}

// GetChannelLiveShowListBegin mocks base method.
func (m *MockIStore) GetChannelLiveShowListBegin(arg0 context.Context, arg1, arg2 uint32) ([]*store.ChannelLiveShow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveShowListBegin", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.ChannelLiveShow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveShowListBegin indicates an expected call of GetChannelLiveShowListBegin.
func (mr *MockIStoreMockRecorder) GetChannelLiveShowListBegin(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowListBegin", reflect.TypeOf((*MockIStore)(nil).GetChannelLiveShowListBegin), arg0, arg1, arg2)
}

// GetChannelLiveShowListEnd mocks base method.
func (m *MockIStore) GetChannelLiveShowListEnd(arg0 context.Context, arg1, arg2 uint32) ([]*store.ChannelLiveShow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveShowListEnd", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.ChannelLiveShow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveShowListEnd indicates an expected call of GetChannelLiveShowListEnd.
func (mr *MockIStoreMockRecorder) GetChannelLiveShowListEnd(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowListEnd", reflect.TypeOf((*MockIStore)(nil).GetChannelLiveShowListEnd), arg0, arg1, arg2)
}

// InsertChannelLiveShow mocks base method.
func (m *MockIStore) InsertChannelLiveShow(arg0 context.Context, arg1 *store.ChannelLiveShow) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertChannelLiveShow", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertChannelLiveShow indicates an expected call of InsertChannelLiveShow.
func (mr *MockIStoreMockRecorder) InsertChannelLiveShow(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertChannelLiveShow", reflect.TypeOf((*MockIStore)(nil).InsertChannelLiveShow), arg0, arg1)
}

// InsertChannelLiveShowApproval mocks base method.
func (m *MockIStore) InsertChannelLiveShowApproval(arg0 context.Context, arg1 *store.ChannelLiveShowApproval) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertChannelLiveShowApproval", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertChannelLiveShowApproval indicates an expected call of InsertChannelLiveShowApproval.
func (mr *MockIStoreMockRecorder) InsertChannelLiveShowApproval(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertChannelLiveShowApproval", reflect.TypeOf((*MockIStore)(nil).InsertChannelLiveShowApproval), arg0, arg1)
}

// InsertChannelLiveShowTag mocks base method.
func (m *MockIStore) InsertChannelLiveShowTag(arg0 context.Context, arg1 *store.ChannelLiveShowTag) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertChannelLiveShowTag", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertChannelLiveShowTag indicates an expected call of InsertChannelLiveShowTag.
func (mr *MockIStoreMockRecorder) InsertChannelLiveShowTag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertChannelLiveShowTag", reflect.TypeOf((*MockIStore)(nil).InsertChannelLiveShowTag), arg0, arg1)
}

// PageChannelLiveShowByStartTimeTag mocks base method.
func (m *MockIStore) PageChannelLiveShowByStartTimeTag(arg0 context.Context, arg1, arg2 uint32, arg3, arg4 []uint32, arg5, arg6 uint32) ([]*store.ChannelLiveShow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PageChannelLiveShowByStartTimeTag", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].([]*store.ChannelLiveShow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PageChannelLiveShowByStartTimeTag indicates an expected call of PageChannelLiveShowByStartTimeTag.
func (mr *MockIStoreMockRecorder) PageChannelLiveShowByStartTimeTag(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PageChannelLiveShowByStartTimeTag", reflect.TypeOf((*MockIStore)(nil).PageChannelLiveShowByStartTimeTag), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// PageMachineAuditDoneChannelLiveShowApproval mocks base method.
func (m *MockIStore) PageMachineAuditDoneChannelLiveShowApproval(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32, arg4, arg5, arg6 uint32) ([]*store.ChannelLiveShowApproval, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PageMachineAuditDoneChannelLiveShowApproval", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].([]*store.ChannelLiveShowApproval)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PageMachineAuditDoneChannelLiveShowApproval indicates an expected call of PageMachineAuditDoneChannelLiveShowApproval.
func (mr *MockIStoreMockRecorder) PageMachineAuditDoneChannelLiveShowApproval(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PageMachineAuditDoneChannelLiveShowApproval", reflect.TypeOf((*MockIStore)(nil).PageMachineAuditDoneChannelLiveShowApproval), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// SelectAllChannelLiveShowTag mocks base method.
func (m *MockIStore) SelectAllChannelLiveShowTag(arg0 context.Context) ([]*store.ChannelLiveShowTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SelectAllChannelLiveShowTag", arg0)
	ret0, _ := ret[0].([]*store.ChannelLiveShowTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SelectAllChannelLiveShowTag indicates an expected call of SelectAllChannelLiveShowTag.
func (mr *MockIStoreMockRecorder) SelectAllChannelLiveShowTag(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SelectAllChannelLiveShowTag", reflect.TypeOf((*MockIStore)(nil).SelectAllChannelLiveShowTag), arg0)
}

// UpdateAudioMachineAuditType mocks base method.
func (m *MockIStore) UpdateAudioMachineAuditType(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAudioMachineAuditType", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAudioMachineAuditType indicates an expected call of UpdateAudioMachineAuditType.
func (mr *MockIStoreMockRecorder) UpdateAudioMachineAuditType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAudioMachineAuditType", reflect.TypeOf((*MockIStore)(nil).UpdateAudioMachineAuditType), arg0, arg1, arg2)
}

// UpdateChannelLiveShowApprovalTagId mocks base method.
func (m *MockIStore) UpdateChannelLiveShowApprovalTagId(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChannelLiveShowApprovalTagId", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateChannelLiveShowApprovalTagId indicates an expected call of UpdateChannelLiveShowApprovalTagId.
func (mr *MockIStoreMockRecorder) UpdateChannelLiveShowApprovalTagId(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChannelLiveShowApprovalTagId", reflect.TypeOf((*MockIStore)(nil).UpdateChannelLiveShowApprovalTagId), arg0, arg1, arg2, arg3)
}

// UpdateImgMachineAuditType mocks base method.
func (m *MockIStore) UpdateImgMachineAuditType(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateImgMachineAuditType", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateImgMachineAuditType indicates an expected call of UpdateImgMachineAuditType.
func (mr *MockIStoreMockRecorder) UpdateImgMachineAuditType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateImgMachineAuditType", reflect.TypeOf((*MockIStore)(nil).UpdateImgMachineAuditType), arg0, arg1, arg2)
}

// UpdateManualAuditType mocks base method.
func (m *MockIStore) UpdateManualAuditType(arg0 context.Context, arg1, arg2 uint32, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateManualAuditType", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateManualAuditType indicates an expected call of UpdateManualAuditType.
func (mr *MockIStoreMockRecorder) UpdateManualAuditType(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateManualAuditType", reflect.TypeOf((*MockIStore)(nil).UpdateManualAuditType), arg0, arg1, arg2, arg3)
}

// UpdateTextMachineAuditType mocks base method.
func (m *MockIStore) UpdateTextMachineAuditType(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTextMachineAuditType", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTextMachineAuditType indicates an expected call of UpdateTextMachineAuditType.
func (mr *MockIStoreMockRecorder) UpdateTextMachineAuditType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTextMachineAuditType", reflect.TypeOf((*MockIStore)(nil).UpdateTextMachineAuditType), arg0, arg1, arg2)
}
