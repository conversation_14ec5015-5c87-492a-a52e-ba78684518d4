// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-live-show-list/internal/model/show-list/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	channel_live_show_list "golang.52tt.com/protocol/services/channel-live-show-list"
	entity "golang.52tt.com/services/channel-live-show-list/internal/entity"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// AddChannelImNotify mocks base method.
func (m *MockICache) AddChannelImNotify(arg0 context.Context, arg1 *entity.ChannelImNotify, arg2 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChannelImNotify", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddChannelImNotify indicates an expected call of AddChannelImNotify.
func (mr *MockICacheMockRecorder) AddChannelImNotify(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelImNotify", reflect.TypeOf((*MockICache)(nil).AddChannelImNotify), arg0, arg1, arg2)
}

// AddChannelLiveShowStartSendOfficalAccount mocks base method.
func (m *MockICache) AddChannelLiveShowStartSendOfficalAccount(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChannelLiveShowStartSendOfficalAccount", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddChannelLiveShowStartSendOfficalAccount indicates an expected call of AddChannelLiveShowStartSendOfficalAccount.
func (mr *MockICacheMockRecorder) AddChannelLiveShowStartSendOfficalAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelLiveShowStartSendOfficalAccount", reflect.TypeOf((*MockICache)(nil).AddChannelLiveShowStartSendOfficalAccount), arg0, arg1)
}

// BatchGetTimeSectionLimit mocks base method.
func (m *MockICache) BatchGetTimeSectionLimit(arg0 context.Context, arg1, arg2 []uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTimeSectionLimit", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTimeSectionLimit indicates an expected call of BatchGetTimeSectionLimit.
func (mr *MockICacheMockRecorder) BatchGetTimeSectionLimit(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTimeSectionLimit", reflect.TypeOf((*MockICache)(nil).BatchGetTimeSectionLimit), arg0, arg1, arg2)
}

// CheckChannelLiveShowEndNotify mocks base method.
func (m *MockICache) CheckChannelLiveShowEndNotify(arg0 context.Context, arg1 []uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckChannelLiveShowEndNotify", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckChannelLiveShowEndNotify indicates an expected call of CheckChannelLiveShowEndNotify.
func (mr *MockICacheMockRecorder) CheckChannelLiveShowEndNotify(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckChannelLiveShowEndNotify", reflect.TypeOf((*MockICache)(nil).CheckChannelLiveShowEndNotify), arg0, arg1)
}

// CheckTimeSectionLimit mocks base method.
func (m *MockICache) CheckTimeSectionLimit(arg0 context.Context, arg1, arg2, arg3 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckTimeSectionLimit", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckTimeSectionLimit indicates an expected call of CheckTimeSectionLimit.
func (mr *MockICacheMockRecorder) CheckTimeSectionLimit(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckTimeSectionLimit", reflect.TypeOf((*MockICache)(nil).CheckTimeSectionLimit), arg0, arg1, arg2, arg3)
}

// CheckUserDeclareDailyLimit mocks base method.
func (m *MockICache) CheckUserDeclareDailyLimit(arg0 context.Context, arg1, arg2 uint32, arg3 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserDeclareDailyLimit", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserDeclareDailyLimit indicates an expected call of CheckUserDeclareDailyLimit.
func (mr *MockICacheMockRecorder) CheckUserDeclareDailyLimit(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserDeclareDailyLimit", reflect.TypeOf((*MockICache)(nil).CheckUserDeclareDailyLimit), arg0, arg1, arg2, arg3)
}

// CheckUserDeclareWeeklyLimit mocks base method.
func (m *MockICache) CheckUserDeclareWeeklyLimit(arg0 context.Context, arg1, arg2 uint32, arg3 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserDeclareWeeklyLimit", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserDeclareWeeklyLimit indicates an expected call of CheckUserDeclareWeeklyLimit.
func (mr *MockICacheMockRecorder) CheckUserDeclareWeeklyLimit(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserDeclareWeeklyLimit", reflect.TypeOf((*MockICache)(nil).CheckUserDeclareWeeklyLimit), arg0, arg1, arg2, arg3)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// DecrTimeSectionLimit mocks base method.
func (m *MockICache) DecrTimeSectionLimit(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecrTimeSectionLimit", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DecrTimeSectionLimit indicates an expected call of DecrTimeSectionLimit.
func (mr *MockICacheMockRecorder) DecrTimeSectionLimit(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecrTimeSectionLimit", reflect.TypeOf((*MockICache)(nil).DecrTimeSectionLimit), arg0, arg1, arg2)
}

// DecrUserDeclareDailyLimit mocks base method.
func (m *MockICache) DecrUserDeclareDailyLimit(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecrUserDeclareDailyLimit", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DecrUserDeclareDailyLimit indicates an expected call of DecrUserDeclareDailyLimit.
func (mr *MockICacheMockRecorder) DecrUserDeclareDailyLimit(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecrUserDeclareDailyLimit", reflect.TypeOf((*MockICache)(nil).DecrUserDeclareDailyLimit), arg0, arg1, arg2)
}

// DecrUserDeclareWeeklyLimit mocks base method.
func (m *MockICache) DecrUserDeclareWeeklyLimit(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecrUserDeclareWeeklyLimit", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DecrUserDeclareWeeklyLimit indicates an expected call of DecrUserDeclareWeeklyLimit.
func (mr *MockICacheMockRecorder) DecrUserDeclareWeeklyLimit(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecrUserDeclareWeeklyLimit", reflect.TypeOf((*MockICache)(nil).DecrUserDeclareWeeklyLimit), arg0, arg1, arg2)
}

// DelNearlyChannelLiveShow mocks base method.
func (m *MockICache) DelNearlyChannelLiveShow(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelNearlyChannelLiveShow", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelNearlyChannelLiveShow indicates an expected call of DelNearlyChannelLiveShow.
func (mr *MockICacheMockRecorder) DelNearlyChannelLiveShow(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelNearlyChannelLiveShow", reflect.TypeOf((*MockICache)(nil).DelNearlyChannelLiveShow), arg0, arg1)
}

// GetChannelLiveShowCntCache mocks base method.
func (m *MockICache) GetChannelLiveShowCntCache(arg0 context.Context, arg1 uint32) (uint32, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveShowCntCache", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetChannelLiveShowCntCache indicates an expected call of GetChannelLiveShowCntCache.
func (mr *MockICacheMockRecorder) GetChannelLiveShowCntCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveShowCntCache", reflect.TypeOf((*MockICache)(nil).GetChannelLiveShowCntCache), arg0, arg1)
}

// GetLiveShowEntryInfoCache mocks base method.
func (m *MockICache) GetLiveShowEntryInfoCache(arg0 context.Context, arg1 uint32) (*channel_live_show_list.GetLiveShowEntryInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiveShowEntryInfoCache", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_show_list.GetLiveShowEntryInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLiveShowEntryInfoCache indicates an expected call of GetLiveShowEntryInfoCache.
func (mr *MockICacheMockRecorder) GetLiveShowEntryInfoCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveShowEntryInfoCache", reflect.TypeOf((*MockICache)(nil).GetLiveShowEntryInfoCache), arg0, arg1)
}

// GetNearlyChannelLiveShow mocks base method.
func (m *MockICache) GetNearlyChannelLiveShow(arg0 context.Context, arg1 uint32) (*channel_live_show_list.ShowItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNearlyChannelLiveShow", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_show_list.ShowItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNearlyChannelLiveShow indicates an expected call of GetNearlyChannelLiveShow.
func (mr *MockICacheMockRecorder) GetNearlyChannelLiveShow(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNearlyChannelLiveShow", reflect.TypeOf((*MockICache)(nil).GetNearlyChannelLiveShow), arg0, arg1)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetShowListCache mocks base method.
func (m *MockICache) GetShowListCache(arg0 context.Context, arg1 uint32) (*channel_live_show_list.GetShowListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowListCache", arg0, arg1)
	ret0, _ := ret[0].(*channel_live_show_list.GetShowListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowListCache indicates an expected call of GetShowListCache.
func (mr *MockICacheMockRecorder) GetShowListCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowListCache", reflect.TypeOf((*MockICache)(nil).GetShowListCache), arg0, arg1)
}

// GetUserDeclareDailyLimitCnt mocks base method.
func (m *MockICache) GetUserDeclareDailyLimitCnt(arg0 context.Context, arg1 uint32, arg2 string) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDeclareDailyLimitCnt", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDeclareDailyLimitCnt indicates an expected call of GetUserDeclareDailyLimitCnt.
func (mr *MockICacheMockRecorder) GetUserDeclareDailyLimitCnt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDeclareDailyLimitCnt", reflect.TypeOf((*MockICache)(nil).GetUserDeclareDailyLimitCnt), arg0, arg1, arg2)
}

// GetUserDeclareWeeklyLimit mocks base method.
func (m *MockICache) GetUserDeclareWeeklyLimit(arg0 context.Context, arg1 uint32, arg2 string) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDeclareWeeklyLimit", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDeclareWeeklyLimit indicates an expected call of GetUserDeclareWeeklyLimit.
func (mr *MockICacheMockRecorder) GetUserDeclareWeeklyLimit(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDeclareWeeklyLimit", reflect.TypeOf((*MockICache)(nil).GetUserDeclareWeeklyLimit), arg0, arg1, arg2)
}

// IsShowAlreadyNotified mocks base method.
func (m *MockICache) IsShowAlreadyNotified(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsShowAlreadyNotified", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsShowAlreadyNotified indicates an expected call of IsShowAlreadyNotified.
func (mr *MockICacheMockRecorder) IsShowAlreadyNotified(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsShowAlreadyNotified", reflect.TypeOf((*MockICache)(nil).IsShowAlreadyNotified), arg0, arg1)
}

// MarkChannelLiveShowEndNotify mocks base method.
func (m *MockICache) MarkChannelLiveShowEndNotify(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkChannelLiveShowEndNotify", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// MarkChannelLiveShowEndNotify indicates an expected call of MarkChannelLiveShowEndNotify.
func (mr *MockICacheMockRecorder) MarkChannelLiveShowEndNotify(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkChannelLiveShowEndNotify", reflect.TypeOf((*MockICache)(nil).MarkChannelLiveShowEndNotify), arg0, arg1)
}

// PopExpireChannelImNotify mocks base method.
func (m *MockICache) PopExpireChannelImNotify(arg0 context.Context) (*entity.ChannelImNotify, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PopExpireChannelImNotify", arg0)
	ret0, _ := ret[0].(*entity.ChannelImNotify)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// PopExpireChannelImNotify indicates an expected call of PopExpireChannelImNotify.
func (mr *MockICacheMockRecorder) PopExpireChannelImNotify(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PopExpireChannelImNotify", reflect.TypeOf((*MockICache)(nil).PopExpireChannelImNotify), arg0)
}

// ScriptLoad mocks base method.
func (m *MockICache) ScriptLoad(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScriptLoad", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ScriptLoad indicates an expected call of ScriptLoad.
func (mr *MockICacheMockRecorder) ScriptLoad(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScriptLoad", reflect.TypeOf((*MockICache)(nil).ScriptLoad), arg0)
}

// SetChannelLiveShowCntCache mocks base method.
func (m *MockICache) SetChannelLiveShowCntCache(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelLiveShowCntCache", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetChannelLiveShowCntCache indicates an expected call of SetChannelLiveShowCntCache.
func (mr *MockICacheMockRecorder) SetChannelLiveShowCntCache(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveShowCntCache", reflect.TypeOf((*MockICache)(nil).SetChannelLiveShowCntCache), arg0, arg1, arg2)
}

// SetLiveShowEntryInfoCache mocks base method.
func (m *MockICache) SetLiveShowEntryInfoCache(arg0 context.Context, arg1 uint32, arg2 *channel_live_show_list.GetLiveShowEntryInfoResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLiveShowEntryInfoCache", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetLiveShowEntryInfoCache indicates an expected call of SetLiveShowEntryInfoCache.
func (mr *MockICacheMockRecorder) SetLiveShowEntryInfoCache(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLiveShowEntryInfoCache", reflect.TypeOf((*MockICache)(nil).SetLiveShowEntryInfoCache), arg0, arg1, arg2)
}

// SetNearlyChannelLiveShow mocks base method.
func (m *MockICache) SetNearlyChannelLiveShow(arg0 context.Context, arg1 uint32, arg2 *channel_live_show_list.ShowItem) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNearlyChannelLiveShow", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetNearlyChannelLiveShow indicates an expected call of SetNearlyChannelLiveShow.
func (mr *MockICacheMockRecorder) SetNearlyChannelLiveShow(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNearlyChannelLiveShow", reflect.TypeOf((*MockICache)(nil).SetNearlyChannelLiveShow), arg0, arg1, arg2)
}

// SetShowListCache mocks base method.
func (m *MockICache) SetShowListCache(arg0 context.Context, arg1 uint32, arg2 *channel_live_show_list.GetShowListResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetShowListCache", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetShowListCache indicates an expected call of SetShowListCache.
func (mr *MockICacheMockRecorder) SetShowListCache(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetShowListCache", reflect.TypeOf((*MockICache)(nil).SetShowListCache), arg0, arg1, arg2)
}
