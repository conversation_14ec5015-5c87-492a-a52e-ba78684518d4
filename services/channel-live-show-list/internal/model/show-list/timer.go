package show_score

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    channelApp "golang.52tt.com/protocol/app/channel"
    showListPb "golang.52tt.com/protocol/services/channel-live-show-list"
    impb "golang.52tt.com/protocol/services/im-api"
    "time"
)

func (m *ShowListMgr) startTimer() error {
    var err error
    m.timerD, err = timer.NewTimerD(context.Background(),
        "channel-live-show-list",
        timer.WithV8RedisCmdable(m.cache.GetRedisClient()))
    if err != nil {
        log.Errorf("startTimer NewTimerD err:%v", err)
        return err
    }

    m.timerD.AddLocalIntervalTask(time.Second,
        tasks.NewTracingTask(tasks.FuncTask(m.handleChannelImNotify), "handleChannelImNotify", 0))

    m.timerD.AddLocalIntervalTask(time.Second,
        tasks.NewTracingTask(tasks.FuncTask(m.handleShowBeginNotifyChannel), "handleShowBeginNotifyChannel", 0))

    m.timerD.AddLocalIntervalTask(time.Second,
        tasks.NewTracingTask(tasks.FuncTask(m.handleShowBeginNotifyOfficalAccount), "handleShowBeginNotifyOfficalAccount", 0))

    m.timerD.Start()
    return nil
}

func (m *ShowListMgr) handleChannelImNotify(ctx context.Context) {

    ctx, cancel := context.WithTimeout(ctx, 2*time.Second)
    defer cancel()

    notifyInfo, ok, err := m.cache.PopExpireChannelImNotify(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "handleChannelImNotify PopExpireChannelImNotify err:%v", err)
        return
    }

    if !ok {
        return
    }

    log.DebugWithCtx(ctx, "handleChannelImNotify notifyInfo:%+v", notifyInfo)

    _ = m.acLayerMgr.SendChannelCommonPublicMsg(ctx, notifyInfo.ToUid, notifyInfo.ChannelId, &channelApp.CommonHighLightChannelIm{
        Uid:              notifyInfo.ToUid,
        Cid:              notifyInfo.ChannelId,
        Content:          notifyInfo.Content,
        HighLightContent: notifyInfo.HighLight,
        FontColor:        "#FFFFFF",
        HighLightColor:   "#FFB638",
    })
}

func (m *ShowListMgr) handleShowBeginNotifyChannel(ctx context.Context) {
    ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
    defer cancel()

    now := time.Now()
    showList, err := m.store.GetChannelLiveShowListBegin(ctx, uint32(now.Unix()), uint32(now.Unix()-60)) // 查近一分钟内开始的节目列表
    if err != nil {
        log.Errorf("handleShowBeginNotifyChannel GetChannelLiveShowListBegin fail %v", err)
        return
    }
    for _, show := range showList {
        // 判断是否有推送过
        ok, err := m.cache.IsShowAlreadyNotified(ctx, show.ID)
        if err != nil {
            log.Errorf("handleShowBeginNotifyChannel AddChannelLiveShowStartPushChannel fail %v", err)
            return
        }
        if !ok {
            continue
        }

        ratingFloat := m.bc.GetRatingFloatConf()
        opt := &showListPb.LiveShowInfo{
            ShowId:    show.ID,
            ShowName:  show.ShowName,
            BeginTs:   int64(show.ShowStartTime),
            EndTs:     int64(show.ShowEndTime),
            AnchorUid: show.AnchorUid,
            RatingFloatConf: &showListPb.ShowRatingFloat{
                IsShow:       ratingFloat.IsShow,
                ListenSec:    ratingFloat.ListenSec,
                FloatStaySec: ratingFloat.StaySec,
            },
        }
        err = m.acLayerMgr.PushShowBeginToChannel(ctx, show.ChannelId, opt)
        if err != nil {
            log.ErrorWithCtx(ctx, "PushShowBeginToChannel err:%v", err)
            continue
        }

        log.InfoWithCtx(ctx, "handleShowBeginNotifyChannel PushShowBeginToChannel opt:%+v", opt)
    }
}

func (m *ShowListMgr) handleShowBeginNotifyOfficalAccount(ctx context.Context) {
    ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
    defer cancel()

    notifySt := time.Now().Add(9 * time.Minute)
    notifyEt := notifySt.Add(time.Minute)
    showList, err := m.store.GetChannelLiveShowListBegin(ctx, uint32(notifyEt.Unix()), uint32(notifySt.Unix())) // 检索10分钟后的数据
    if err != nil {
        log.ErrorWithCtx(ctx, "handleShowBeginNotifyOfficalAccount fail to GetChannelLiveShowListBegin. notifySt:%d, notifyEt:%d, err:%v", notifySt, notifyEt, err)
        return
    }
    log.InfoWithCtx(ctx, "handleShowBeginNotifyOfficalAccount GetChannelLiveShowListBegin. showList len: %d", len(showList))
    for _, show := range showList {
        ok, err := m.cache.AddChannelLiveShowStartSendOfficalAccount(ctx, show.ID)
        if err != nil {
            log.ErrorWithCtx(ctx, "handleShowBeginNotifyOfficalAccount fail to AddChannelLiveShowStartSendOfficalAccount. showId:%d, err:%v", show.ID, err)
            continue
        }
        if !ok {
            continue
        }

        showStartTime := time.Unix(int64(show.ShowStartTime), 0)
        err = m.acLayerMgr.SendOfficialAccountMsg(ctx, "channel-live-show-list-handleShowBeginNotifyOfficalAccount", show.AnchorUid, &impb.Text{
            Content: fmt.Sprintf("亲爱的达人，您申请在【%s】演出的节目【%s】即将开始，请在节目时间准时表演才艺哦", showStartTime.Format("2006-01-02 15:04"), show.ShowName),
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "handleShowBeginNotifyOfficalAccount fail to SendOfficialAccountMsg. showId:%d, err:%v", show.ID, err)
            continue
        }
        log.InfoWithCtx(ctx, "handleShowBeginNotifyOfficalAccount SendOfficialAccountMsg success. show: %+v", show)
    }
}
