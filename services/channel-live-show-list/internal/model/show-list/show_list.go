package show_score

import (
    "context"
    "fmt"
    "sort"
    "time"

    "golang.52tt.com/services/tt-rev/common/util"

    "golang.52tt.com/pkg/log"
    pb "golang.52tt.com/protocol/services/channel-live-show-list"
)

func (m *ShowListMgr) GetLiveShowEntryInfo(ctx context.Context, req *pb.GetLiveShowEntryInfoRequest) (*pb.GetLiveShowEntryInfoResponse, error) {
    out := &pb.GetLiveShowEntryInfoResponse{}
    if !m.bc.IsNeedShowEntry() {
        return out, nil
    }

    // 获取今天本地零点的时间戳
    now := time.Now()
    todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
    todayStartTime := uint32(todayStart.Unix())
    
    // 尝试从缓存中获取节目数量
    cnt, exist, err := m.cache.GetChannelLiveShowCntCache(ctx, todayStartTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLiveShowEntryInfo failed to get cached cnt: %v", err)
    }
    
    // 如果缓存不存在，则从数据库获取
    if !exist {
        cnt, err = m.store.GetChannelLiveShowCntByStartTimeTag(ctx, todayStartTime, todayStartTime+86400, []uint32{}, []uint32{})
        if err != nil {
            log.ErrorWithCtx(ctx, "GetLiveShowEntryInfo fail to GetChannelLiveShowCntByStartTimeTag. todayStartTime:%d, err:%v", todayStartTime, err)
            return out, err
        }
        
        // 将结果存入缓存
        err = m.cache.SetChannelLiveShowCntCache(ctx, todayStartTime, cnt)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetLiveShowEntryInfo failed to set cnt cache: %v", err)
        }
    }
    
    if cnt > 0 {
        out.ShowEntryTextList = append(out.ShowEntryTextList, fmt.Sprintf("今天有%d个节目", cnt))
        out.ShowEntryTextList = append(out.ShowEntryTextList, m.bc.GetFakeEntryTextList()...)
    }
    
    return out, nil
}

type TimeInfo struct {
    StartTime uint32
    EndTime   uint32
}

type ShowWithHotValue struct {
    ShowItem *pb.ShowItem
    HotValue int64
}

func (m *ShowListMgr) GetShowList(ctx context.Context, req *pb.GetShowListRequest) (*pb.GetShowListResponse, error) {
    // 检查是否需要使用缓存（过滤参数为空）
    useCache := len(req.GetVoiceTagIdList()) == 0 && len(req.GetContentTagIdList()) == 0

    if useCache {
        // 尝试从缓存中获取结果
        cachedResp, err := m.cache.GetShowListCache(ctx, req.GetDatetime())
        if err != nil {
            log.ErrorWithCtx(ctx, "GetShowList failed to get cached result: %v", err)
        }
        if cachedResp != nil {
            return cachedResp, nil
        }
    }

    out := &pb.GetShowListResponse{}

    showList, err := m.store.PageChannelLiveShowByStartTimeTag(ctx, req.GetDatetime(), req.GetDatetime()+86400, req.GetVoiceTagIdList(), req.GetContentTagIdList(), 0, 9999)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetShowList fail to PageChannelLiveShowByStartTimeTag. err:%v, req:%+v", err, req)
        return out, err
    }

    if len(showList) == 0 {
        // 如果结果为空且使用缓存，将空结果缓存
        if useCache {
            err = m.cache.SetShowListCache(ctx, req.GetDatetime(), out)
            if err != nil {
                log.ErrorWithCtx(ctx, "GetShowList failed to set empty cache: %v", err)
            }
        }
        return out, nil
    }
    cidList := make([]uint32, 0, len(showList))
    for _, show := range showList {
        cidList = append(cidList, show.ChannelId)
    }

    var mergeHotValueMap = make(map[uint32]int64)
    var mergeLiveStatusMap = make(map[uint32]uint32)
    // 每次100个cid
    for i := 0; i < len(cidList); i += 100 {
        end := i + 100
        if end > len(cidList) {
            end = len(cidList)
        }
        hotValueMap, err := m.acLayerMgr.BatchGetChannelHot(ctx, cidList[i:end])
        if err != nil {
            log.ErrorWithCtx(ctx, "GetShowList failed to get channel hot: %v", err)
            continue
        }
        for cid, hotValue := range hotValueMap {
            mergeHotValueMap[cid] = hotValue
        }

        liveStatusMap, err := m.acLayerMgr.BatGetChannelLiveStatus(ctx, cidList[i:end])
        if err != nil {
            log.ErrorWithCtx(ctx, "GetShowList failed to get channel live status: %v", err)
            continue
        }
        for cid, liveStatus := range liveStatusMap {
            mergeLiveStatusMap[cid] = liveStatus
        }
    }
    groupMap := make(map[string][]*pb.ShowItem)

    timeMap := make(map[string]*TimeInfo)
    for _, item := range showList {
        timeRangeStr := fmt.Sprintf("%s-%s", time.Unix(int64(item.ShowStartTime), 0).Format("15:04"),
            time.Unix(int64(item.ShowEndTime), 0).Format("15:04"))
        timeMap[timeRangeStr] = &TimeInfo{
            EndTime:   item.ShowEndTime,
            StartTime: item.ShowStartTime,
        }
        groupMap[timeRangeStr] = append(groupMap[timeRangeStr], &pb.ShowItem{
            ShowId:        item.ID,
            ShowName:      item.ShowName,
            ShowDescAudio: item.ShowDescAudio,
            ShowCoverImg:  item.ShowCoverImg,
            Uid:           item.AnchorUid,
            ChannelId:     item.ChannelId,
            AudioDuration: item.ShowDescAudioDuration,
            VoiceId:       item.VoiceTagId,
            ContentId:     item.ContentTagId,
            HotValue:      mergeHotValueMap[item.ChannelId],
            LiveStatus:    mergeLiveStatusMap[item.ChannelId],
        })
    }
    for timeRangeStr, itemList := range groupMap {
        var hasMore bool
        sort.Slice(itemList, func(i, j int) bool {
            if itemList[i].LiveStatus != itemList[j].LiveStatus {
                return itemList[i].LiveStatus > itemList[j].LiveStatus
            }
            // 热度值大的排在前
            return itemList[i].HotValue > itemList[j].HotValue
        })
        if len(itemList) > 4 {
            hasMore = true
            itemList = itemList[:4]
        }

        out.ShowList = append(out.ShowList, &pb.GetShowListResponse_ShowGroupItem{
            TimeRange: timeRangeStr,
            ShowItem:  itemList,
            BeginTime: timeMap[timeRangeStr].StartTime,
            EndTime:   timeMap[timeRangeStr].EndTime,
            HasMore:   hasMore,
        })
    }

    // sort
    sort.Slice(out.ShowList, func(i, j int) bool {
        return out.ShowList[i].TimeRange < out.ShowList[j].TimeRange
    })

    // 如果使用缓存，将结果存入缓存
    if useCache {
        err = m.cache.SetShowListCache(ctx, req.GetDatetime(), out)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetShowList failed to set cache: %v", err)
        }
    }

    return out, nil
}

func (m *ShowListMgr) GetUserRemainDeclareCnt(ctx context.Context, uid uint32) (uint32, uint32, error) {
    dailyLimitCnt, err := m.cache.GetUserDeclareDailyLimitCnt(ctx, uid, time.Now().Format("20060102"))
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserDeclareDailyLimit fail to GetUserDeclareDailyLimitCnt. uid:%d, err:%v", uid, err)
        return 0, 0, err
    }

    weekStart := util.GetNatureWeekStart()
    weeklyLimitCnt, err := m.cache.GetUserDeclareWeeklyLimit(ctx, uid, weekStart.Format("20060102"))
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserDeclareDailyLimit fail to GetUserDeclareWeeklyLimit. uid:%d, err:%v", uid, err)
        return 0, 0, err
    }
    return dailyLimitCnt, weeklyLimitCnt, nil
}

func (m *ShowListMgr) UnfoldShowList(ctx context.Context, req *pb.UnfoldShowListRequest) ([]*pb.ShowItem, error) {

    list, err := m.store.PageChannelLiveShowByStartTimeTag(ctx, req.GetBeginTime(), req.GetEndTime(), req.GetVoiceTagIdList(), req.GetContentTagIdList(), 0, 50)
    if err != nil {
        log.ErrorWithCtx(ctx, "PageChannelLiveShowByStartTimeTag failed, err: %v, req: %+v", err, req)
        return nil, err
    }
    cidList := make([]uint32, 0, len(list))
    out := make([]*pb.ShowItem, 0, len(list))
    for _, v := range list {
        cidList = append(cidList, v.ChannelId)
        out = append(out, &pb.ShowItem{
            ShowId:        v.ID,
            ShowName:      v.ShowName,
            ShowStartTime: v.ShowStartTime,
            ShowEndTime:   v.ShowEndTime,
            ShowDescAudio: v.ShowDescAudio,
            ShowCoverImg:  v.ShowCoverImg,
            Uid:           v.AnchorUid,
            ChannelId:     v.ChannelId,
            AudioDuration: v.ShowDescAudioDuration,
            ContentId:     v.ContentTagId,
            VoiceId:       v.VoiceTagId,
        })
    }

    // 获取热度值
    hotMap, err := m.acLayerMgr.BatchGetChannelHot(ctx, cidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowList BatchGetChannelHot err: %v, cidList: %v", err, cidList)
        return out, err
    }

    for _, v := range out {
        v.HotValue = hotMap[v.ChannelId]
    }

    channelLiveStatusMap, err := m.acLayerMgr.BatGetChannelLiveStatus(ctx, cidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowList BatGetChannelLiveStatus err: %v, cidList: %v", err, cidList)
        return out, err
    }

    for _, v := range out {
        v.LiveStatus = channelLiveStatusMap[v.ChannelId]
    }

    // sort
    sort.Slice(out, func(i, j int) bool {
        if out[i].LiveStatus != out[j].LiveStatus {
            return out[i].LiveStatus > out[j].LiveStatus
        }
        return out[i].HotValue > out[j].HotValue
    })
    return out, nil
}
