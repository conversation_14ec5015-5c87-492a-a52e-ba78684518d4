package cache

import (
    "context"
    "fmt"
    json "github.com/json-iterator/go"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/services/channel-live-show-list/internal/entity"
    "time"
)

const (
    ChannelImNotifyQueueKey        = "show_list_channel_im_notify_queue"
    ChannelLiveShowStartPushPrefix = "show_list_start_push_"
)

const popMember = `
local ts = redis.call("TIME")
redis.replicate_commands()
local list = redis.call('ZRANGEBYSCORE', KEYS[1], '-INF', ts[1], 'LIMIT', 0, 1)
local ret = {}

for ix = 1, #list do
    local removed = redis.call("ZREM", KEYS[1], list[ix])
    if removed > 0 then
        table.insert(ret, list[ix])
    end
end
return ret
`

func (c *Cache) ScriptLoad(ctx context.Context) (err error) {
    c.popQueueSha, err = c.cmder.ScriptLoad(ctx, popMember).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "ScriptLoad fail to ScriptLoad. error:%v", err)
        return err
    }

    log.Debugf("ScriptLoad sha:%s", c.popQueueSha)
    return nil
}

func (c *Cache) PopExpireChannelImNotify(ctx context.Context) (*entity.ChannelImNotify, bool, error) {
    info := &entity.ChannelImNotify{}

    res, err := c.cmder.EvalSha(ctx, c.popQueueSha, []string{ChannelImNotifyQueueKey}).Result()
    if err != nil && err != redis.Nil {
        log.ErrorWithCtx(ctx, "PopExpireChannelImNotify fail to EvalSha. err:%v", err)
        return info, false, err
    }

    if rs, ok := res.([]interface{}); ok && len(rs) > 0 {
        tmp, ok := rs[0].(string)
        if !ok {
            log.ErrorWithCtx(ctx, "PopExpireChannelImNotify fail to rs[0].(string). rs:%+v", rs)
            return info, false, nil
        }

        err = json.Unmarshal([]byte(tmp), info)
        if err != nil {
            log.ErrorWithCtx(ctx, "PopExpireChannelImNotify fail to Unmarshal. rs:%+v", rs)
            return info, false, nil
        }

        return info, true, nil
    }

    return info, false, nil
}

func (c *Cache) AddChannelImNotify(ctx context.Context, info *entity.ChannelImNotify, expire time.Time) error {
    if info == nil {
        return nil
    }

    tmp, err := json.Marshal(info)
    if err != nil {
        log.ErrorWithCtx(ctx, "AddChannelImNotify fail to Marshal. uid:%v, info:%+v, err:%v", info, err)
        return err
    }

    mem := redis.Z{
        Member: tmp,
        Score:  float64(expire.Unix()),
    }

    err = c.cmder.ZAdd(ctx, ChannelImNotifyQueueKey, &mem).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "AddChannelImNotify fail to ZAdd. uid:%v, info:%+v, err:%v", info, err)
        return err
    }

    return nil
}

// IsShowAlreadyNotified 节目开始推送标记
func (c *Cache) IsShowAlreadyNotified(ctx context.Context, showId uint32) (bool, error) {
    key := ChannelLiveShowStartPushPrefix + fmt.Sprintf("%d", showId)
    ok, err := c.cmder.SetNX(ctx, key, 1, time.Hour).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "IsShowAlreadyNotified fail to SetNX. showId:%v, err:%v", showId, err)
        return false, err
    }

    if ok {
        log.InfoWithCtx(ctx, "IsShowAlreadyNotified success. showId:%v", showId)
    }
    return ok, nil
}

func genShowStartSendOfficalAccountKey(showId uint32) string {
    return fmt.Sprintf("show_start_send_offical_account:%v", showId)
}
func (c *Cache) AddChannelLiveShowStartSendOfficalAccount(ctx context.Context, showId uint32) (bool, error) {
    ok, err := c.cmder.SetNX(ctx, genShowStartSendOfficalAccountKey(showId), 1, time.Hour).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "AddChannelLiveShowStartSendOfficalAccount fail to SetNX. showId:%v, err:%v", showId, err)
        return false, err
    }

    return ok, nil
}

func genShowEndNotifyFlagKey(showId uint32) string {
    return fmt.Sprintf("show_end_notify_flag:%v", showId)
}

func (c *Cache) MarkChannelLiveShowEndNotify(ctx context.Context, showIdList []uint32) error {
    keys := make([]string, len(showIdList))
    for i, showId := range showIdList {
        keys[i] = genShowEndNotifyFlagKey(showId)
    }

    c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
        for _, key := range keys {
            pipe.Set(ctx, key, 1, 24*time.Hour)
        }

        return nil
    })

    return nil
}

func (c *Cache) CheckChannelLiveShowEndNotify(ctx context.Context, showIdList []uint32) ([]uint32, error) {
    keys := make([]string, len(showIdList))
    for i, showId := range showIdList {
        keys[i] = genShowEndNotifyFlagKey(showId)
    }

    cmdList := make([]*redis.IntCmd, len(showIdList))
    c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
        for i, key := range keys {
            cmdList[i] = pipe.Exists(ctx, key)
        }
        return nil
    })
    log.DebugWithCtx(ctx, "CheckChannelLiveShowEndNotify, cmdList len:%v", len(cmdList))
    notNotifyShowIdList := make([]uint32, 0, len(showIdList))
    for i, cmd := range cmdList {
        log.DebugWithCtx(ctx, "CheckChannelLiveShowEndNotify, showId:%v, cmd.Val():%v", showIdList[i], cmd.Val())
        if redis.IsNil(cmd.Err()) {
            notNotifyShowIdList = append(notNotifyShowIdList, showIdList[i])
        }else if cmd.Val() == 0 {
            notNotifyShowIdList = append(notNotifyShowIdList, showIdList[i])
        }
    }

    return notNotifyShowIdList, nil
}
