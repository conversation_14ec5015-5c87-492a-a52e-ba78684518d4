package cache

import (
    "context"
    "encoding/json"
    "fmt"
    "strconv"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "golang.52tt.com/pkg/log"
    pb "golang.52tt.com/protocol/services/channel-live-show-list"
    "time"
)

//go:generate quicksilver-cli test interface ../cache
//go:generate mockgen -destination=../mocks/cache.go -package=mocks golang.52tt.com/services/channel-live-show-list/internal/model/show-list/cache ICache

type Cache struct {
    cmder       redis.Cmdable
    popQueueSha string
}

func NewCache(client redis.Cmdable) *Cache {
    c := &Cache{
        cmder: client,
    }
    err := c.ScriptLoad(context.Background())
    if err != nil {
        log.ErrorWithCtx(context.Background(), "ScriptLoad error: %v", err)
        panic(err)
    }
    return c
}

func (c *Cache) Close() error { return nil }

func (c *Cache) GetRedisClient() redis.Cmdable {
    return c.cmder
}

func genUserDeclareDailyLimitKey(uid uint32, timeStr string) string {
    return fmt.Sprintf("user_declare_daily_limit:%d:%s", uid, timeStr)
}

func genUserDeclareWeeklyLimitKey(uid uint32, timeStr  string) string {
    return fmt.Sprintf("user_declare_weekly_limit:%d:%s", uid, timeStr)
}

func genGetShowListCacheKey(datetime uint32) string {
    return fmt.Sprintf("show_list:%d", datetime)
}

func genGetLiveShowEntryInfoCacheKey(todayStartTime uint32) string {
    return fmt.Sprintf("live_show_entry_info:%d", todayStartTime)
}

func genChannelLiveShowCntCacheKey(todayStartTime uint32) string {
    return fmt.Sprintf("channel_live_show_cnt:%d", todayStartTime)
}

func (c *Cache) GetShowListCache(ctx context.Context, datetime uint32) (*pb.GetShowListResponse, error) {
    key := genGetShowListCacheKey(datetime)
    data, err := c.cmder.Get(ctx, key).Result()
    if redis.IsNil(err) {
        return nil, nil
    }
    if err != nil {
        log.ErrorWithCtx(ctx, "GetShowListCache err: %v, datetime: %d", err, datetime)
        return nil, err
    }
    
    resp := &pb.GetShowListResponse{}
    err = json.Unmarshal([]byte(data), resp)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetShowListCache Unmarshal err: %v, datetime: %d", err, datetime)
        return nil, err
    }
    
    return resp, nil
}

func (c *Cache) SetShowListCache(ctx context.Context, datetime uint32, resp *pb.GetShowListResponse) error {
    key := genGetShowListCacheKey(datetime)
    data, err := json.Marshal(resp)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetShowListCache Marshal err: %v, datetime: %d", err, datetime)
        return err
    }
    
    err = c.cmder.Set(ctx, key, string(data), 5*time.Minute).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SetShowListCache Set err: %v, datetime: %d", err, datetime)
        return err
    }
    
    return nil
}

func (c *Cache) GetLiveShowEntryInfoCache(ctx context.Context, todayStartTime uint32) (*pb.GetLiveShowEntryInfoResponse, error) {
    key := genGetLiveShowEntryInfoCacheKey(todayStartTime)
    data, err := c.cmder.Get(ctx, key).Result()
    if redis.IsNil(err) {
        return nil, nil
    }
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLiveShowEntryInfoCache err: %v, todayStartTime: %d", err, todayStartTime)
        return nil, err
    }
    
    resp := &pb.GetLiveShowEntryInfoResponse{}
    err = json.Unmarshal([]byte(data), resp)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLiveShowEntryInfoCache Unmarshal err: %v, todayStartTime: %d", err, todayStartTime)
        return nil, err
    }
    
    return resp, nil
}

func (c *Cache) SetLiveShowEntryInfoCache(ctx context.Context, todayStartTime uint32, resp *pb.GetLiveShowEntryInfoResponse) error {
    key := genGetLiveShowEntryInfoCacheKey(todayStartTime)
    data, err := json.Marshal(resp)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetLiveShowEntryInfoCache Marshal err: %v, todayStartTime: %d", err, todayStartTime)
        return err
    }
    
    err = c.cmder.Set(ctx, key, string(data), 2*time.Minute).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SetLiveShowEntryInfoCache Set err: %v, todayStartTime: %d", err, todayStartTime)
        return err
    }
    
    return nil
}

func (c *Cache) GetChannelLiveShowCntCache(ctx context.Context, todayStartTime uint32) (uint32, bool, error) {
    key := genChannelLiveShowCntCacheKey(todayStartTime)
    data, err := c.cmder.Get(ctx, key).Result()
    if redis.IsNil(err) {
        return 0, false, nil
    }
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowCntCache err: %v, todayStartTime: %d", err, todayStartTime)
        return 0, false, err
    }
    
    cnt, err := strconv.ParseUint(data, 10, 32)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowCntCache ParseUint err: %v, todayStartTime: %d", err, todayStartTime)
        return 0, false, err
    }
    
    return uint32(cnt), true, nil
}

func (c *Cache) SetChannelLiveShowCntCache(ctx context.Context, todayStartTime uint32, cnt uint32) error {
    key := genChannelLiveShowCntCacheKey(todayStartTime)
    
    err := c.cmder.Set(ctx, key, fmt.Sprintf("%d", cnt), 2*time.Minute).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SetChannelLiveShowCntCache Set err: %v, todayStartTime: %d", err, todayStartTime)
        return err
    }
    
    return nil
}

func (c *Cache) CheckUserDeclareDailyLimit(ctx context.Context, uid, limit uint32, timeStr string) (bool, error) {
    key := genUserDeclareDailyLimitKey(uid, timeStr)
    nowCnt, err := c.cmder.Incr(ctx, key).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "IncrUserDeclareDailyLimit err: %v, uid: %d", err, uid)
        return false, err
    }
    if nowCnt > int64(limit) { // 如果已经超过上限了, 减回去, 不影响配置放大之后继续申请
        err = c.cmder.Decr(ctx, key).Err()
        if err != nil {
            log.ErrorWithCtx(ctx, "DecrUserDeclareDailyLimit err: %v, uid: %d", err, uid)
            return false, err
        }

        return false, nil
    }
    if nowCnt == 1 { // 首次记录, 设置过期时间
        err = c.cmder.Expire(ctx, key, 24*time.Hour).Err()
        if err != nil {
            log.ErrorWithCtx(ctx, "ExpireUserDeclareDailyLimit err: %v, uid: %d", err, uid)
        }
    }

    return true, nil
}

func (c *Cache) DecrUserDeclareDailyLimit(ctx context.Context, uid uint32, timeStr string) error {
    key := genUserDeclareDailyLimitKey(uid, timeStr)
    _, err := c.cmder.Decr(ctx, key).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "DecrUserDeclareDailyLimit err: %v, uid: %d", err, uid)
        return err
    }
    return nil
}

func genTimeSectionLimitKey(startTime, endTime uint32) string {
    return fmt.Sprintf("time_section_limit:%d:%d", startTime, endTime)
}
func (c *Cache) CheckTimeSectionLimit(ctx context.Context, startTime, endTime, limit uint32) (bool, error) {
    key := genTimeSectionLimitKey(startTime, endTime)
    nowCnt, err := c.cmder.Incr(ctx, key).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "IncrTimeSectionLimit err: %v, startTime: %d, endTime: %d", err, startTime, endTime)
        return false, err
    }
    if nowCnt > int64(limit) { // 如果已经超过上限了, 减回去, 不影响配置放大之后继续申请
        err = c.cmder.Decr(ctx, key).Err()
        if err != nil {
            log.ErrorWithCtx(ctx, "DecrTimeSectionLimit err: %v, startTime: %d, endTime: %d", err, startTime, endTime)
            return false, err
        }

        return false, nil
    }
    if nowCnt == 1 { // 首次记录, 设置过期时间
        err = c.cmder.Expire(ctx, key, 24*time.Hour).Err()
        if err != nil {
            log.ErrorWithCtx(ctx, "ExpireTimeSectionLimit err: %v, startTime: %d, endTime: %d", err, startTime, endTime)
        }
    }

    return true, nil
}

func (c *Cache) DecrTimeSectionLimit(ctx context.Context, startTime, endTime uint32) error {
    key := genTimeSectionLimitKey(startTime, endTime)
    _, err := c.cmder.Decr(ctx, key).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "DecrTimeSectionLimit err: %v, startTime: %d, endTime: %d", err, startTime, endTime)
        return err
    }
    return nil
}

func (c *Cache) CheckUserDeclareWeeklyLimit(ctx context.Context, uid, limit uint32, timeStr string) (bool, error) {
    key := genUserDeclareWeeklyLimitKey(uid, timeStr)
    nowCnt, err := c.cmder.Incr(ctx, key).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "IncrUserDeclareWeeklyLimit err: %v, uid: %d", err, uid)
        return false, err
    }
    if nowCnt > int64(limit) { // 如果已经超过上限了, 减回去, 不影响配置放大之后继续申请
        err = c.cmder.Decr(ctx, key).Err()
        if err != nil {
            log.ErrorWithCtx(ctx, "DecrUserDeclareWeeklyLimit err: %v, uid: %d", err, uid)
            return false, err
        }

        return false, nil
    }

    if nowCnt == 1 { // 首次记录, 设置过期时间
        err = c.cmder.Expire(ctx, key, 7*24*time.Hour).Err()
        if err != nil {
            log.ErrorWithCtx(ctx, "ExpireUserDeclareWeeklyLimit err: %v, uid: %d", err, uid)
        }
    }

    return true, nil
}

func (c *Cache) DecrUserDeclareWeeklyLimit(ctx context.Context, uid uint32, timeStr string) error {
    key := genUserDeclareWeeklyLimitKey(uid, timeStr)
    _, err := c.cmder.Decr(ctx, key).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "DecrUserDeclareWeeklyLimit err: %v, uid: %d", err, uid)
        return err
    }
    return nil
}

func (c *Cache) GetUserDeclareDailyLimitCnt(ctx context.Context, uid uint32, timeStr string) (uint32, error) {
    key := genUserDeclareDailyLimitKey(uid, timeStr)
    cnt, err := c.cmder.Get(ctx, key).Int()
    if redis.IsNil(err) {
        return 0, nil
    }
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserDeclareDailyLimitCnt err: %v, uid: %d", err, uid)
        return 0, err
    }
    return uint32(cnt), nil
}

func (c *Cache) GetUserDeclareWeeklyLimit(ctx context.Context, uid uint32, timeStr string) (uint32, error) {
    key := genUserDeclareWeeklyLimitKey(uid, timeStr)
    cnt, err := c.cmder.Get(ctx, key).Int()
    if redis.IsNil(err) {
        return 0, nil
    }
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserDeclareWeeklyLimit err: %v, uid: %d", err, uid)
        return 0, err
    }

    return uint32(cnt), nil
}

func genNearlyChannelLiveShowKey(anchorUid uint32) string {
    return fmt.Sprintf("nearly_channel_live_show:%d", anchorUid)
}
func (c *Cache) GetNearlyChannelLiveShow(ctx context.Context, anchorUid uint32) (*pb.ShowItem, error) {
    key := genNearlyChannelLiveShowKey(anchorUid)
    showItem, err := c.cmder.Get(ctx, key).Result()
    if redis.IsNil(err) {
        return &pb.ShowItem{}, nil
    }
    if err != nil {
        log.ErrorWithCtx(ctx, "GetNearlyChannelLiveShow err: %v, anchorUid: %d", err, anchorUid)
        return nil, err
    }
    rs := &pb.ShowItem{}
    err = json.Unmarshal([]byte(showItem), rs)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetNearlyChannelLiveShow Unmarshal err: %v, anchorUid: %d", err, anchorUid)
        return nil, err
    }

    return rs, nil
}

func (c *Cache) DelNearlyChannelLiveShow(ctx context.Context, anchorUid uint32) error {
    key := genNearlyChannelLiveShowKey(anchorUid)
    err := c.cmder.Del(ctx, key).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "DelNearlyChannelLiveShow err: %v, anchorUid: %d", err, anchorUid)
        return err
    }
    return nil
}

func (c *Cache) SetNearlyChannelLiveShow(ctx context.Context, anchorUid uint32, showItem *pb.ShowItem) error {
    key := genNearlyChannelLiveShowKey(anchorUid)
    showItemJson, err := json.Marshal(showItem)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetNearlyChannelLiveShow Marshal err: %v, anchorUid: %d", err, anchorUid)
        return err
    }
    err = c.cmder.Set(ctx, key, string(showItemJson), 2*time.Hour).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SetNearlyChannelLiveShow Set err: %v, anchorUid: %d", err, anchorUid)
        return err
    }
    return nil
}

func (c *Cache) BatchGetTimeSectionLimit(ctx context.Context, startTimeList, endTimeList []uint32) (map[uint32]uint32, error) {
    if len(startTimeList) != len(endTimeList) {
        return nil, fmt.Errorf("startTimeList and endTimeList length not equal")
    }
    keyList := make([]string, 0, len(startTimeList))
    for i := 0; i < len(startTimeList); i++ {
        keyList = append(keyList, genTimeSectionLimitKey(startTimeList[i], endTimeList[i]))
    }
    resList, err := c.cmder.MGet(ctx, keyList...).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetTimeSectionLimit MGet err: %v", err)
        return nil, err
    }
    mapStartTime2Cnt := make(map[uint32]uint32, 0)
    for i, res := range resList {
        if res == nil {
            continue
        }
        cnt, err := strconv.Atoi(res.(string))
        if err != nil {
            log.ErrorWithCtx(ctx, "BatchGetTimeSectionLimit Atoi err: %v", err)
            continue
        }
        mapStartTime2Cnt[startTimeList[i]] = uint32(cnt)
    }
    return mapStartTime2Cnt, nil
}