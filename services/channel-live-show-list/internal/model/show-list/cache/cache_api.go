package cache

import(
	context "context"
	redis "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	pb "golang.52tt.com/protocol/services/channel-live-show-list"
	time "time"
	entity "golang.52tt.com/services/channel-live-show-list/internal/entity"
)

type ICache interface {
	AddChannelImNotify(ctx context.Context, info *entity.ChannelImNotify, expire time.Time) error
	AddChannelLiveShowStartSendOfficalAccount(ctx context.Context, showId uint32) (bool,error)
	BatchGetTimeSectionLimit(ctx context.Context, startTimeList, endTimeList []uint32) (map[uint32]uint32,error)
	CheckChannelLiveShowEndNotify(ctx context.Context, showIdList []uint32) ([]uint32,error)
	CheckTimeSectionLimit(ctx context.Context, startTime, endTime, limit uint32) (bool,error)
	CheckUserDeclareDailyLimit(ctx context.Context, uid, limit uint32, timeStr string) (bool,error)
	CheckUserDeclareWeeklyLimit(ctx context.Context, uid, limit uint32, timeStr string) (bool,error)
	Close() error
	DecrTimeSectionLimit(ctx context.Context, startTime, endTime uint32) error
	DecrUserDeclareDailyLimit(ctx context.Context, uid uint32, timeStr string) error
	DecrUserDeclareWeeklyLimit(ctx context.Context, uid uint32, timeStr string) error
	DelNearlyChannelLiveShow(ctx context.Context, anchorUid uint32) error
	GetChannelLiveShowCntCache(ctx context.Context, todayStartTime uint32) (uint32,bool,error)
	GetLiveShowEntryInfoCache(ctx context.Context, todayStartTime uint32) (*pb.GetLiveShowEntryInfoResponse,error)
	GetNearlyChannelLiveShow(ctx context.Context, anchorUid uint32) (*pb.ShowItem,error)
	GetRedisClient() redis.Cmdable
	GetShowListCache(ctx context.Context, datetime uint32) (*pb.GetShowListResponse,error)
	GetUserDeclareDailyLimitCnt(ctx context.Context, uid uint32, timeStr string) (uint32,error)
	GetUserDeclareWeeklyLimit(ctx context.Context, uid uint32, timeStr string) (uint32,error)
	IsShowAlreadyNotified(ctx context.Context, showId uint32) (bool,error)
	MarkChannelLiveShowEndNotify(ctx context.Context, showIdList []uint32) error
	PopExpireChannelImNotify(ctx context.Context) (*entity.ChannelImNotify,bool,error)
	ScriptLoad(ctx context.Context) (err error)
	SetChannelLiveShowCntCache(ctx context.Context, todayStartTime uint32, cnt uint32) error
	SetLiveShowEntryInfoCache(ctx context.Context, todayStartTime uint32, resp *pb.GetLiveShowEntryInfoResponse) error
	SetNearlyChannelLiveShow(ctx context.Context, anchorUid uint32, showItem *pb.ShowItem) error
	SetShowListCache(ctx context.Context, datetime uint32, resp *pb.GetShowListResponse) error
}

