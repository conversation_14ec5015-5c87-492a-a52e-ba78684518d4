package show_score

//go:generate quicksilver-cli test interface ../show-list
//go:generate mockgen -destination=../mocks/show-list.go -package=mocks golang.52tt.com/services/channel-live-show-list/internal/model/show-list IShowListMgr

import (
    "context"
    "encoding/hex"
    "fmt"
    comctx "golang.52tt.com/services/tt-rev/common/ctx"
    "sort"
    "sync"
    "time"

    "gitlab.ttyuyin.com/bizFund/bizFund/pkg/audit"
    v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
    impb "golang.52tt.com/protocol/services/im-api"
    "golang.52tt.com/services/tt-rev/common/goroutineex"
    "golang.52tt.com/services/tt-rev/common/util"

    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/channel-live-show-list"
    "golang.52tt.com/services/channel-live-show-list/internal/conf"
    "golang.52tt.com/services/channel-live-show-list/internal/entity"
    anti_corruption_layer "golang.52tt.com/services/channel-live-show-list/internal/model/anti-corruption-layer"
    "golang.52tt.com/services/channel-live-show-list/internal/model/show-list/cache"
    "golang.52tt.com/services/channel-live-show-list/internal/model/show-list/store"
    "google.golang.org/grpc/codes"
)

type ShowListMgr struct {
    store      store.IStore
    cache      cache.ICache
    bc         conf.IBusinessConfManager
    acLayerMgr anti_corruption_layer.IACLayer

    timerD   *timer.Timer
    wg       sync.WaitGroup
    shutDown chan struct{}
}

func NewShowListMgr(s mysql.DBx, cacheClient redis.Cmdable, bc conf.IBusinessConfManager,
    acLayerMgr anti_corruption_layer.IACLayer) (*ShowListMgr, error) {
    mysqlStore := store.NewStore(s)
    redisCli := cache.NewCache(cacheClient)

    m := &ShowListMgr{
        store:      mysqlStore,
        cache:      redisCli,
        bc:         bc,
        shutDown:   make(chan struct{}),
        acLayerMgr: acLayerMgr,
    }

    err := m.startTimer()
    if err != nil {
        log.Errorf("NewMgr startTimer err:%v", err)
        return m, err
    }

    return m, nil
}

func (m *ShowListMgr) Stop() {
    m.timerD.Stop()
    close(m.shutDown)
    m.wg.Wait()
    _ = m.cache.Close()
    _ = m.store.Close()
}

func (m *ShowListMgr) AddChannelLiveShowTag(ctx context.Context, id uint32, tagName string, parentId uint32) error {
    return m.store.InsertChannelLiveShowTag(ctx, &store.ChannelLiveShowTag{
        ID:       id,
        TagName:  tagName,
        ParentId: parentId,
    })
}

func (m *ShowListMgr) DeleteChannelLiveShowTag(ctx context.Context, id uint32) error {
    return m.store.DeleteChannelLiveShowTag(ctx, id)
}

func (m *ShowListMgr) GetChannelLiveTagTree(ctx context.Context) (*pb.TagNode, map[uint32]*pb.TagNode, error) {
    tagList, err := m.store.SelectAllChannelLiveShowTag(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveTagTree SelectAllChannelLiveShowTag err:%v", err)
        return nil, nil, err
    }

    nodeRefMap := make(map[uint32]*pb.TagNode)
    root := &pb.TagNode{}
    nodeRefMap[0] = root // 虚拟根

    // 确保子节点比父节点id大, tagList从小到大处理, 把树组装起来
    for _, tag := range tagList {
        parentNode, ok := nodeRefMap[tag.ParentId]
        if !ok {
            continue
        }
        node := &pb.TagNode{
            TagId:   tag.ID,
            TagName: tag.TagName,
        }
        parentNode.ChildList = append(parentNode.ChildList, node)
        nodeRefMap[tag.ID] = node
    }

    return root, nodeRefMap, nil
}

func (m *ShowListMgr) GetRelatedTagNodeId(ctx context.Context, idList []uint32) ([]uint32, error) {
    _, nodeRefMap, err := m.GetChannelLiveTagTree(ctx)
    if err != nil {
        return nil, err
    }

    // bfs搜索所有子节点
    bfsQueue := make([]*pb.TagNode, 0, len(idList))
    leafIdList := make([]uint32, 0, len(idList))
    for _, id := range idList {
        if node, ok := nodeRefMap[id]; ok {
            bfsQueue = append(bfsQueue, node)
        }
    }

    for len(bfsQueue) > 0 {
        node := bfsQueue[0]
        bfsQueue = bfsQueue[1:]
        if len(node.ChildList) == 0 {
            leafIdList = append(leafIdList, node.TagId)
        } else {
            bfsQueue = append(bfsQueue, node.ChildList...)
        }
    }

    return leafIdList, nil
}

const (
    voiceTagRootId   = 1
    contentTagRootId = 2
)

func (m *ShowListMgr) GetShowTag(ctx context.Context) (*pb.GetShowTagResponse, error) {
    _, nodeRefMap, err := m.GetChannelLiveTagTree(ctx)
    if err != nil {
        return nil, err
    }
    resp := &pb.GetShowTagResponse{
        VoiceTagList:   nodeRefMap[voiceTagRootId].ChildList,
        ContentTagList: nodeRefMap[contentTagRootId].ChildList,
    }
    return resp, nil
}

func (m *ShowListMgr) GetShowTime(ctx context.Context, datetime uint32) (*pb.GetShowTimeResponse, error) {
    resp := &pb.GetShowTimeResponse{}

    earliestTimeTmp := time.Now().AddDate(0, 0, int(m.bc.GetAvailableDeclareAfterDay()))
    earliestTime := uint32(time.Date(earliestTimeTmp.Year(), earliestTimeTmp.Month(), earliestTimeTmp.Day(), 0, 0, 0, 0, earliestTimeTmp.Location()).Unix())
    resp.EarliestSelectableTime = earliestTime
    if datetime == 0 {
        datetime = earliestTime
    }
    if datetime < earliestTime {
        datetime = earliestTime
    }

    targetTime := time.Unix(int64(datetime), 0)
    log.DebugWithCtx(ctx, "GetShowTime targetTime:%s", targetTime)
    targetTimeStart := time.Date(targetTime.Year(), targetTime.Month(), targetTime.Day(), 0, 0, 0, 0, targetTime.Location())
    // 只能预约n天后的
    targetTimeEnd := targetTimeStart.AddDate(0, 0, 1)

    startTimeList, endTimeList := make([]uint32, 0), make([]uint32, 0)
    // 拼装时间段
    timeSectionDuration := m.bc.GetShowTimeDuration(targetTimeStart)
    for targetTimeStart.Before(targetTimeEnd) {
        respItem := &pb.GetShowTimeResponse_ShowTime{
            ShowStartTime: uint32(targetTimeStart.Unix()),
            ShowEndTime:   uint32(targetTimeStart.Add(time.Duration(timeSectionDuration) * time.Second).Unix()),
        }
        resp.ShowTimeList = append(resp.ShowTimeList, respItem)

        targetTimeStart = targetTimeStart.Add(time.Duration(timeSectionDuration) * time.Second)
        startTimeList = append(startTimeList, respItem.ShowStartTime)
        endTimeList = append(endTimeList, respItem.ShowEndTime)
    }

    mapStartTime2Cnt, err := m.cache.BatchGetTimeSectionLimit(ctx, startTimeList, endTimeList)
    mapStartTimeRemainCnt := make(map[uint32]uint32, len(startTimeList))
    for _, startTime := range startTimeList {
        if m.bc.GetShowApplyLimitCnt() > mapStartTime2Cnt[startTime] {
            mapStartTimeRemainCnt[startTime] = m.bc.GetShowApplyLimitCnt() - mapStartTime2Cnt[startTime]
        }
    }
    for _, item := range resp.ShowTimeList {
        item.RemainCnt = mapStartTimeRemainCnt[item.ShowStartTime]
    }

    return resp, err
}

func (m *ShowListMgr) GetChannelLiveShowApprovalList(ctx context.Context, datetime, ttid, approvalFilterStatus, pageNum, pageSize uint32) (*pb.GetChannelLiveShowApprovalListResp, error) {
    resp := &pb.GetChannelLiveShowApprovalListResp{}
    uid := uint32(0)
    if ttid > 0 {
        var err error
        uid, err = m.acLayerMgr.GetUidByAlias(ctx, fmt.Sprint(ttid))
        if err != nil {
            log.ErrorWithCtx(ctx, "GetChannelLiveShowApprovalList fail to GetUidByName. ttid:%d, err:%v", ttid, err)
            return resp, err
        }
    }

    queryAuditStatus := []uint32{}
    sortType := store.ApprovalListSortTypeShowStartTime
    if approvalFilterStatus == uint32(pb.GetChannelLiveShowApprovalListRequest_Pending) {
        queryAuditStatus = append(queryAuditStatus, store.ChannelLiveShowApprovalAuditTypePending)
    }
    if approvalFilterStatus == uint32(pb.GetChannelLiveShowApprovalListRequest_Approved) {
        queryAuditStatus = append(queryAuditStatus, store.ChannelLiveShowApprovalAuditTypePass)
        queryAuditStatus = append(queryAuditStatus, store.ChannelLiveShowApprovalAuditTypeReject)
        sortType = store.ApprovalListSortTypeApprovalTime
    }

    // 计算总数
    count, err := m.store.CountMachineAuditDoneChannelLiveShowApproval(ctx, datetime, uid, queryAuditStatus)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowApprovalList fail to CountMachineAuditDoneChannelLiveShowApproval. datetime:%d, uid:%d, err:%v", datetime, uid, err)
        return resp, err
    }
    resp.Total = count

    // 获取分页数据
    pageData, err := m.store.PageMachineAuditDoneChannelLiveShowApproval(ctx, datetime, uid, queryAuditStatus, pageNum, pageSize, uint32(sortType))
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowApprovalList fail to PageMachineAuditDoneChannelLiveShowApproval. datetime:%d, uid:%d, err:%v", datetime, uid, err)
        return resp, err
    }

    // 获取用户信息
    uidList := make([]uint32, 0)
    for _, item := range pageData {
        uidList = append(uidList, item.AnchorUid)
    }
    userMap, err := m.acLayerMgr.BatchGetUserProfile(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowApprovalList fail to BatchGetUserProfile. uidList:%v, err:%v", uidList, err)
        return resp, err
    }

    // 填充数据
    for _, item := range pageData {
        approvalListItem := &pb.ChannelLiveShowApprovalItem{
            ShowApprovalId:   item.ID,
            AnchorNickname:   userMap[item.AnchorUid].GetNickname(),
            AnchorTtid:       userMap[item.AnchorUid].GetAccountAlias(),
            ShowStartTime:    item.ShowStartTime,
            ShowEndTime:      item.ShowEndTime,
            AnchorScore:      0,
            ShowName:         item.ShowName,
            ShowCoverImg:     m.bc.GetShowListResourceObsPrefix() + item.ShowCoverImg,
            ShowDescAudio:    m.bc.GetShowListResourceObsPrefix() + item.ShowDescAudio,
            VoiceTagId:       item.VoiceTagId,
            ContentTagId:     item.ContentTagId,
            ApprovalStatus:   item.ManualAuditType,
            ApprovalOperator: item.ApprovalOperator,
            ApprovalTime:     uint32(item.ApprovalTime.Unix()),
            AnchorUid:        item.AnchorUid,
        }
        resp.ApprovalList = append(resp.ApprovalList, approvalListItem)
    }

    return resp, nil
}

func (m *ShowListMgr) HandleShowApproval(ctx context.Context, req *pb.HandleShowApprovalRequest) (*pb.HandelShowApprovalResponse, error) {
    resp := &pb.HandelShowApprovalResponse{}

    approval, err := m.store.GetChannelLiveShowApprovalById(ctx, req.GetShowApprovalId())
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleShowApproval fail to GetChannelLiveShowApprovalById. showApprovalId:%d, err:%v", req.GetShowApprovalId(), err)
        return resp, err
    }
    // 机审未完成/不通过, 返回
    if approval.ImgMachineAuditType != store.ChannelLiveShowApprovalAuditTypePass || approval.AudioMachineAuditType != store.ChannelLiveShowApprovalAuditTypePass ||
        approval.TextMachineAuditType != store.ChannelLiveShowApprovalAuditTypePass {
        return resp, nil
    }

    manualAuditType := uint32(store.ChannelLiveShowApprovalAuditTypePass)
    if !req.GetIsPass() {
        manualAuditType = uint32(store.ChannelLiveShowApprovalAuditTypeReject)
    }
    err = m.store.UpdateManualAuditType(ctx, req.GetShowApprovalId(), manualAuditType, req.GetOperator())
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleShowApproval fail to UpdateManualAuditType. showApprovalId:%d, err:%v", req.GetShowApprovalId(), err)
        return resp, err
    }

    if req.GetIsPass() {
        err = m.InsertChannelLiveShow(ctx, approval)
        if err != nil {
            log.ErrorWithCtx(ctx, "HandleShowApproval fail to InsertChannelLiveShow. showApprovalId:%d, err:%v", req.GetShowApprovalId(), err)
            return resp, err
        }
    } else { // 回退申报次数
        m.rollbackUserDeclareLimit(ctx, approval)
    }

    // 推送
    goroutineex.GoroutineWithTimeoutCtx(ctx, 5*time.Second, func(ctx context.Context) {
        showStartTime := time.Unix(int64(approval.ShowStartTime), 0)
        msg := &impb.Text{
            Content: fmt.Sprintf("亲爱的达人，很抱歉，您申请在【%s】演出的节目【%s】未通过审核，请调整节目信息后重新申请【申请链接】",
                showStartTime.Format("2006-01-02 15:04"), approval.ShowName),
            Highlight: "【申请链接】",
            Url:       m.bc.GetDeclarePageUrl(),
        }

        if req.GetIsPass() {
            msg = &impb.Text{
                Content: fmt.Sprintf("亲爱的达人，您申请在【%s】演出的节目【%s】通过审核，请在节目时间准时表演才艺哦", showStartTime.Format("2006-01-02 15:04"), approval.ShowName),
            }
        }

        err = m.acLayerMgr.SendOfficialAccountMsg(ctx, "channel-live-show-list-HandleShowApproval", approval.AnchorUid, msg)
        if err != nil {
            log.ErrorWithCtx(ctx, "HandleShowApproval fail to SendOfficialAccountMsg. showApprovalId:%d, err:%v", req.GetShowApprovalId(), err)
        }
    })

    return resp, nil
}

func (m *ShowListMgr) rollbackUserDeclareLimit(ctx context.Context, approval *store.ChannelLiveShowApproval) {
    err := m.cache.DecrUserDeclareDailyLimit(ctx, approval.AnchorUid, approval.CreateTime.Format("********"))
    if err != nil {
        log.ErrorWithCtx(ctx, "rollbackUserDeclareLimit fail to DecrUserDeclareDailyLimit. showApprovalId:%d, err:%v", approval.ID, err)
    }
    err = m.cache.DecrUserDeclareWeeklyLimit(ctx, approval.AnchorUid, util.GetOneNatureWeekStart(approval.CreateTime).Format("********"))
    if err != nil {
        log.ErrorWithCtx(ctx, "rollbackUserDeclareLimit fail to DecrUserDeclareWeeklyLimit. showApprovalId:%d, err:%v", approval.ID, err)
    }
    err = m.cache.DecrTimeSectionLimit(ctx, approval.ShowStartTime, approval.ShowEndTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "rollbackUserDeclareLimit fail to DecrTimeSectionLimit. showApprovalId:%d, err:%v", approval.ID, err)
    }
}

func (m *ShowListMgr) UpdateShowApprovalTag(ctx context.Context, req *pb.ModifyShowApprovalTagRequest) (*pb.ModifyShowApprovalTagResponse, error) {
    err := m.store.UpdateChannelLiveShowApprovalTagId(ctx, req.GetShowApprovalId(), req.GetVoiceTagId(), req.GetContentTagId())
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateShowApprovalTag fail to UpdateChannelLiveShowApprovalTagId. showApprovalId:%d, err:%v", req.GetShowApprovalId(), err)
        return nil, err
    }
    return &pb.ModifyShowApprovalTagResponse{}, nil
}

// GetChannelLiveShowInfo 获取直播间节目信息
func (m *ShowListMgr) GetNearlyChannelLiveShowInfo(ctx context.Context, anchorUid uint32) (*pb.ShowItem, error) {
    out := &pb.ShowItem{}

    // 先从缓存取
    showItem, err := m.cache.GetNearlyChannelLiveShow(ctx, anchorUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetNearlyChannelLiveShow cache, anchorUid: %d, error: %v", anchorUid, err)
        return out, err
    }
    nowTs := uint32(time.Now().Unix())
    if showItem.GetShowEndTime() >= nowTs { // 节目未结束
        log.DebugWithCtx(ctx, "GetNearlyChannelLiveShowInfo, from cache: %+v", out)
        return showItem, nil
    }

    showList, err := m.store.GetChannelLiveShowByAnchorUid(ctx, anchorUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetNearlyChannelLiveShowInfo fail to GetNearlyChannelLiveShowByAnchorUid. anchorUid:%d, err:%v", anchorUid, err)
        return out, err
    }

    if len(showList) == 0 {
        log.DebugWithCtx(ctx, "GetNearlyChannelLiveShowInfo showList empty, anchorUid: %d", anchorUid)
        return out, nil
    }

    // 根据开始时间升序排序
    sort.SliceStable(showList, func(i, j int) bool {
        return showList[i].ShowStartTime < showList[j].ShowStartTime
    })
    out = &pb.ShowItem{
        ShowId:        showList[0].ID,
        ShowName:      showList[0].ShowName,
        ShowStartTime: showList[0].ShowStartTime,
        ShowEndTime:   showList[0].ShowEndTime,
        ShowDescAudio: showList[0].ShowDescAudio,
        ShowCoverImg:  showList[0].ShowCoverImg,
        Uid:           showList[0].AnchorUid,
        ChannelId:     showList[0].ChannelId,
    }

    err = m.cache.SetNearlyChannelLiveShow(ctx, anchorUid, out)
    if err != nil {
        log.WarnWithCtx(ctx, "GetNearlyChannelLiveShowInfo fail to SetNearlyChannelLiveShow. anchorUid:%d, err:%v", anchorUid, err)
    }

    return out, nil
}

// GetChannelLiveShowInfoById 根据节目id获取节目信息
func (m *ShowListMgr) GetChannelLiveShowInfoById(ctx context.Context, showId uint32) (*pb.ShowItem, error) {
    out := &pb.ShowItem{}

    show, err := m.store.GetChannelLiveShowByShowId(ctx, showId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowInfoById fail to GetChannelLiveShowByShowId. showId:%d, err:%v", showId, err)
        return out, err
    }

    if show == nil || show.ID == 0 {
        return out, nil
    }

    out = &pb.ShowItem{
        ShowId:        show.ID,
        ShowName:      show.ShowName,
        ShowStartTime: uint32(show.ShowStartTime),
        ShowEndTime:   uint32(show.ShowEndTime),
        ShowDescAudio: show.ShowDescAudio,
        ShowCoverImg:  show.ShowCoverImg,
        Uid:           show.AnchorUid,
        //ChannelId:     0,
    }

    return out, nil
}

func (m *ShowListMgr) DeclareShow(ctx context.Context, req *pb.DeclareShowRequest) (*pb.DeclareShowResponse, error) {
    resp := &pb.DeclareShowResponse{}

    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        return resp, fmt.Errorf("DeclareShow fail to get serviceInfo")
    }

    // 判断是否人群包用户
    yes, err := m.acLayerMgr.IsUserInApplyGroup(ctx, serviceInfo.UserID)
    if err != nil {
        log.ErrorWithCtx(ctx, "DeclareShow fail to IsUserInApplyGroup, uid: %d, err: %v", serviceInfo, err)
        return resp, err
    }
    if !yes {
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "您不具备提报资格")
    }

    // 检查自己是否已经申请过
    existsApprovaled, err := m.store.ExistsPendingPassApproval(ctx, serviceInfo.UserID, req.GetShowStartTime())
    if err != nil {
        log.ErrorWithCtx(ctx, "DeclareShow fail to ExistsPendingPassApproval")
        return resp, err
    }
    if existsApprovaled {
        err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该时间段已存在提报节目")
        return resp, err
    }

    rollBackList := make([]func(), 0)
    // 检查日申请
    ok, err = m.cache.CheckUserDeclareDailyLimit(ctx, serviceInfo.UserID, m.bc.GetUserDeclareDailyLimitCnt(), time.Now().Format("********"))
    if err != nil {
        log.ErrorWithCtx(ctx, "DeclareShow fail to CheckUserDeclareDailyLimit, uid: %d, err: %v", serviceInfo, err)
        return resp, err
    }
    if !ok {
        log.DebugWithCtx(ctx, "DeclareShow fail to CheckUserDeclareDailyLimit")
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "已达到当日/当周申报节目上限")
    }
    rollBackList = append(rollBackList, func() {
        ctx, cancel:= comctx.WithTimeout(5*time.Second)
        defer cancel()
        err = m.cache.DecrUserDeclareDailyLimit(ctx, serviceInfo.UserID, time.Now().Format("********"))
        if err != nil {
            log.ErrorWithCtx(ctx, "DeclareShow fail to DecrUserDeclareDailyLimit, uid: %d, err: %v", serviceInfo, err)
        }
    })

    // 检查周申请限制
    weekStart := util.GetNatureWeekStart()
    ok, err = m.cache.CheckUserDeclareWeeklyLimit(ctx, serviceInfo.UserID, m.bc.GetUserDeclareWeeklyLimitCnt(), weekStart.Format("********"))
    if err != nil {
        log.ErrorWithCtx(ctx, "DeclareShow fail to CheckUserDeclareWeeklyLimit, uid: %d, err: %v", serviceInfo, err)
        return resp, err
    }
    if !ok {
        log.DebugWithCtx(ctx, "DeclareShow fail to CheckUserDeclareWeeklyLimit")
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "已达到当日/当周申报节目上限")
    }
    rollBackList = append(rollBackList, func() {
        ctx, cancel:= comctx.WithTimeout(5*time.Second)
        defer cancel()
        err = m.cache.DecrUserDeclareWeeklyLimit(ctx, serviceInfo.UserID, weekStart.Format("********"))
        if err != nil {
            log.ErrorWithCtx(ctx, "DeclareShow fail to DecrUserDeclareWeeklyLimit, uid: %d, err: %v", serviceInfo, err)
        }
    })
    defer func() {
        if err != nil {
            for _, rollBack := range rollBackList {
                rollBack()
            }
        }
    }()

    // 检查该时段当前已通过+申请中的节目数,申请时候占用,不通过回退
    ok, err = m.cache.CheckTimeSectionLimit(ctx, req.GetShowStartTime(), req.GetShowEndTime(), m.bc.GetShowApplyLimitCnt())
    if err != nil {
        log.ErrorWithCtx(ctx, "DeclareShow fail to CheckTimeSectionLimit, startTime: %d, endTime: %d, err: %v", req.GetShowStartTime(), req.GetShowEndTime(), err)
        return resp, err
    }
    if !ok {
        log.DebugWithCtx(ctx, "DeclareShow fail to CheckTimeSectionLimit")
        err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该时段已达到申报节目上限")
        return resp, err
    }
    rollBackList = append(rollBackList, func() {
        ctx, cancel:= comctx.WithTimeout(5*time.Second)
        defer cancel()
        err = m.cache.DecrTimeSectionLimit(ctx, req.GetShowStartTime(), req.GetShowEndTime())
        if err != nil {
            log.ErrorWithCtx(ctx, "DeclareShow fail to DecrTimeSectionLimit, startTime: %d, endTime: %d, err: %v", req.GetShowStartTime(), req.GetShowEndTime(), err)
        }
    })

    approval := &store.ChannelLiveShowApproval{
        ShowName:              req.GetShowName(),
        ShowStartTime:         req.GetShowStartTime(),
        ShowEndTime:           req.GetShowEndTime(),
        ShowCoverImg:          req.GetShowCoverImg(),
        ShowDescAudio:         req.GetShowDescAudio(),
        ShowDescAudioDuration: req.GetShowDescAudioDuration(),
        VoiceTagId:            req.GetVoiceTagId(),
        ContentTagId:          req.GetContentTagId(),
        AnchorUid:             serviceInfo.UserID,
        ImgMachineAuditType:   store.ChannelLiveShowApprovalAuditTypePending,
        TextMachineAuditType:  store.ChannelLiveShowApprovalAuditTypePending,
        AudioMachineAuditType: store.ChannelLiveShowApprovalAuditTypePending,
        ManualAuditType:       store.ChannelLiveShowApprovalAuditTypePending,
    }
    approvalId, err := m.store.InsertChannelLiveShowApproval(ctx, approval)
    if err != nil {
        log.ErrorWithCtx(ctx, "DeclareShow fail to InsertChannelLiveShowApproval. req:%+v, err:%v", req, err)
        return resp, err
    }
    approval.ID = approvalId

    if !m.bc.GetAuditTestModel() {
        err = m.sendAudit(ctx, approvalId, req.GetShowName(), m.bc.GetShowListResourceObsPrefix()+req.GetShowCoverImg(), m.bc.GetShowListResourceObsPrefix()+req.GetShowDescAudio())
        if err != nil {
            log.ErrorWithCtx(ctx, "DeclareShow fail to sendAudit. req:%+v, err:%v", req, err)
            return resp, err
        }

    } else {
        // 先不管审批 直接插入节目表
        err = m.InsertChannelLiveShow(ctx, approval)
        if err != nil {
            log.ErrorWithCtx(ctx, "DeclareShow fail to InsertChannelLiveShow. req:%+v, err:%v", req, err)
            return resp, err
        }
    }

    return resp, nil
}

func (m *ShowListMgr) InsertChannelLiveShow(ctx context.Context, showApproval *store.ChannelLiveShowApproval) error {
    channelId, err := m.acLayerMgr.GetAnchorChannelId(ctx, showApproval.AnchorUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "InsertChannelLiveShow fail to GetAnchorChannelId. anchorUid:%d, err:%v", showApproval.AnchorUid, err)
        return err
    }

    // 主播节目信息有变更, 删除最近节目缓存
    err = m.cache.DelNearlyChannelLiveShow(ctx, showApproval.AnchorUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "InsertChannelLiveShow fail to DelNearlyChannelLiveShow. anchorUid:%d, err:%v", showApproval.AnchorUid, err)
    }

    return m.store.InsertChannelLiveShow(ctx, &store.ChannelLiveShow{
        ApprovalId:            showApproval.ID,
        ShowName:              showApproval.ShowName,
        ShowStartTime:         showApproval.ShowStartTime,
        ShowEndTime:           showApproval.ShowEndTime,
        ShowCoverImg:          showApproval.ShowCoverImg,
        ShowDescAudio:         showApproval.ShowDescAudio,
        ShowDescAudioDuration: showApproval.ShowDescAudioDuration,
        VoiceTagId:            showApproval.VoiceTagId,
        ContentTagId:          showApproval.ContentTagId,
        AnchorUid:             showApproval.AnchorUid,
        ChannelId:             channelId,
        CreateTime:            time.Now(),
    })
}

func (m *ShowListMgr) sendAudit(ctx context.Context, approvalId uint32, showName, coverImg, showAudioDesc string) error {
    log.DebugWithCtx(ctx, "sendAudit, approvalId:%d, showName:%s, coverImg:%s, showAudioDesc:%s", approvalId, showName, coverImg, showAudioDesc)

    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    uid := serviceInfo.UserID

    userInfo, err := m.acLayerMgr.GetAccountUser(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "DeclareShow fail to GetUserProfile. uid:%d, err:%v", uid, err)
        return err
    }

    taskContext := &v2.TaskContext{
        SceneCode: string(audit.SCENE_CODE_CHANNEL_LIVE_SHOW_LIST_COVER_NAME),
        AppId:     string(audit.APP_ID_QUICKSILVER),
        Scenes:    []v2.Scene{v2.Scene_SCENE_DEFAULT},
        UserInfo: &v2.User{
            Id:       uint64(uid),
            Alias:    userInfo.GetAlias(),
            Nickname: userInfo.GetNickname(),
        },
        DeviceInfo: &v2.Device{
            Id: hex.EncodeToString(serviceInfo.DeviceID),
            Ip: serviceInfo.ClientIPAddr().String(),
        },
        BelongObjId: userInfo.GetAlias(),
    }

    callBack := &v2.Callback{
        Callback: &v2.Callback_KafkaCallback_{KafkaCallback: &v2.Callback_KafkaCallback{}},
        Params: map[string]string{
            "uid":         fmt.Sprint(uid),
            "approval_id": fmt.Sprint(approvalId),
        },
    }

    //1.文字送审 先同步非Reject后异步
    if len(showName) > 0 {
        callBack.Params = map[string]string{
            "uid":         fmt.Sprint(uid),
            "approval_id": fmt.Sprint(approvalId),
            "text":        showName,
            "scene":       fmt.Sprint(pb.ChannelLiveShowApprovalAuditScene_ChannelLiveShowApprovalAuditSceneName),
        }
        verifyRes, serr := m.acLayerMgr.SyncScanText(ctx, &v2.SyncTextCheckReq{
            Context:  taskContext,
            Text:     showName,
            Callback: callBack,
            Async:    true,
        })
        if serr != nil {
            log.ErrorWithCtx(ctx, "sendAudit SyncScanText err uid:%d, err:%+v", uid, serr)
            return serr
        }
        log.InfoWithCtx(ctx, "sendAudit SyncScanText. content:%s uid:%d, verifyRes:%+v", showName, uid, verifyRes)

        // 机审结果 REJECT 不保存记录，直接返回异常
        if v2.Suggestion_REJECT == v2.Suggestion(verifyRes.GetResult()) {
            // 更新状态
            err = m.store.UpdateTextMachineAuditType(ctx, approvalId, store.ChannelLiveShowApprovalAuditTypeReject)
            if err != nil {
                log.ErrorWithCtx(ctx, "sendAudit UpdateTextMachineAuditType err:%v", err)
            }
            return protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillReject)
        } else if v2.Suggestion_PASS == v2.Suggestion(verifyRes.GetResult()) {
            // 机审结果 PASS
            err = m.store.UpdateTextMachineAuditType(ctx, approvalId, store.ChannelLiveShowApprovalAuditTypePass)
            if err != nil {
                log.ErrorWithCtx(ctx, "AddUserAuditSkill UpdateTextMachineAuditType err:%v", err)
            }
        } else {
            log.WarnWithCtx(ctx, "sendAudit SyncScanText review uid:%d, text:%s", uid, showName)
        }
    }

    //2. 技能图
    if len(coverImg) > 0 {
        callBack.Params = map[string]string{
            "uid":         fmt.Sprint(uid),
            "approval_id": fmt.Sprint(approvalId),
            "img":         coverImg,
            "scene":       fmt.Sprint(pb.ChannelLiveShowApprovalAuditScene_ChannelLiveShowApprovalAuditSceneCover),
        }
        taskContext.SceneCode = string(audit.SCENE_CODE_CHANNEL_LIVE_SHOW_LIST_COVER_NAME)
        _, err := m.acLayerMgr.AsyncScanImage(ctx, &v2.ScanImageReq{
            Context: taskContext,
            ImageDatas: []*v2.ImageData{
                {
                    Metadata: &v2.Metadata{
                        DataId: fmt.Sprint(approvalId),
                    },
                    ImageData: &v2.ImageData_Url{
                        Url: coverImg,
                    },
                },
            },
            Callback: callBack,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "sendAudit, AsyncScanImage err, uid: %d, err:%v", uid, err)
            return err
        }
    }

    //3. 语音送审
    if len(showAudioDesc) > 0 {
        callBack.Params = map[string]string{
            "uid":         fmt.Sprint(uid),
            "approval_id": fmt.Sprint(approvalId),
            "audio":       showAudioDesc,
            "scene":       fmt.Sprint(pb.ChannelLiveShowApprovalAuditScene_ChannelLiveShowApprovalAuditSceneAudio),
        }
        taskContext.SceneCode = string(audit.SCENE_CODE_CHANNEL_LIVE_SHOW_LIST_AUDIO_DESC)
        scanAudioReq := &v2.ScanAudioReq{
            Context: taskContext,
            AudioData: &v2.AudioData{
                Metadata: &v2.Metadata{
                    DataId: fmt.Sprint(approvalId),
                },
                AudioData: &v2.AudioData_Url{
                    Url: showAudioDesc, // 送审音频链接
                },
            },
            Callback: callBack,
        }
        _, serr := m.acLayerMgr.AsyncScanAudio(ctx, scanAudioReq)
        if serr != nil {
            log.ErrorWithCtx(ctx, "censoring-proxy.AsyncScanAudio failed, uid: %d, approvalId %d, err:%v, ", uid, approvalId, serr)
            return serr
        }
    }

    log.DebugWithCtx(ctx, "sendAudit, AsyncScanAudio success, uid: %d, approvalId %d", uid, approvalId)
    return nil
}

func (m *ShowListMgr) AddShowTag(ctx context.Context, req *pb.AddShowTagRequest) (*pb.AddShowTagResponse, error) {
    for _, tag := range req.GetTagList() {
        err := m.AddChannelLiveShowTag(ctx, tag.GetTagId(), tag.GetTagName(), tag.GetParentId())
        if err != nil {
            log.ErrorWithCtx(ctx, "AddShowTag fail to AddChannelLiveShowTag. tag:%+v, err:%v", tag, err)
            return nil, err
        }
    }
    return &pb.AddShowTagResponse{}, nil
}

func (m *ShowListMgr) DeleteShowTag(ctx context.Context, req *pb.DeleteShowTagRequest) (*pb.DeleteShowTagResponse, error) {
    for _, id := range req.GetTagIdList() {
        err := m.DeleteChannelLiveShowTag(ctx, id)
        if err != nil {
            log.ErrorWithCtx(ctx, "DeleteShowTag fail to DeleteChannelLiveShowTag. id:%d, err:%v", id, err)
            return nil, err
        }
    }
    return &pb.DeleteShowTagResponse{}, nil
}

// AddChannelImNotify 添加房间公屏通知
func (m *ShowListMgr) AddChannelImNotify(ctx context.Context, notifyInfo *entity.ChannelImNotify) error {
    return m.cache.AddChannelImNotify(ctx, notifyInfo, time.Now().Add(5*time.Second))
}

func (m *ShowListMgr) ManualAddShow(ctx context.Context, req *pb.TestToolRequest_TestShowBeginPush) error {
    // 先获取当前频道已存在的未开始或者进行中的节目信息
    showList, err := m.store.GetChannelLiveShowByAnchorUid(ctx, req.GetAnchorUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetNearlyChannelLiveShowByAnchorUid req:%+v error:%+v", req, err)
        return err
    }
    // 判断待插入的节目时间是否和已存在的节目时间冲突
    for _, show := range showList {
        if show.ShowStartTime < uint32(req.GetEndTs()) && show.ShowEndTime > uint32(req.GetBeginTs()) {
            log.ErrorWithCtx(ctx, "GetNearlyChannelLiveShowByAnchorUid req:%+v error:%+v", req, err)
            return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "当前时间段有节目正在执行，请勿重复添加")
        }
    }

    //approvalId, err := m.store.InsertChannelLiveShowApproval(ctx, &store.ChannelLiveShowApproval{
    //    ShowName:                req.GetShowName(),
    //    ShowStartTime:           uint32(req.GetBeginTs()),
    //    ShowEndTime:             uint32(req.GetEndTs()),
    //    ShowCoverImg:            "https://ga-album-cdnqn.52tt.com/tt-server/20250814115700_84039617.png",
    //    ShowDescAudio:           "http://192.168.9.227:2335/testing/channel-live-show-list-tt-dev-mars",
    //    ShowDescAudioDuration:   50,
    //    VoiceTagId:              5,
    //    ContentTagId:            18,
    //    AnchorUid:               req.GetAnchorUid(),
    //    ImgTextMachineAuditType: store.ChannelLiveShowApprovalAuditTypePass,
    //    AudioMachineAuditType:   store.ChannelLiveShowApprovalAuditTypePass,
    //    ManualAuditType:         store.ChannelLiveShowApprovalAuditTypePass,
    //    ApprovalOperator:        "manual add",
    //})
    //if err != nil {
    //    log.ErrorWithCtx(ctx, "ManualAddShow fail to InsertChannelLiveShowApproval. req:%+v, err:%v", req, err)
    //    return err
    //}

    // 直接插入节目表
    err = m.store.InsertChannelLiveShow(ctx, &store.ChannelLiveShow{
        ApprovalId:            1,
        ShowName:              req.GetShowName(),
        ShowStartTime:         uint32(req.GetBeginTs()),
        ShowEndTime:           uint32(req.GetEndTs()),
        ShowCoverImg:          "https://ga-album-cdnqn.52tt.com/tt-server/20250814115700_84039617.png",
        ShowDescAudio:         "http://192.168.9.227:2335/testing/channel-live-show-list-tt-dev-mars",
        ShowDescAudioDuration: 50,
        VoiceTagId:            5,
        ContentTagId:          18,
        AnchorUid:             req.GetAnchorUid(),
        ChannelId:             req.GetChannelId(),
        CreateTime:            time.Now(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "ManualAddShow fail to InsertChannelLiveShow. req:%+v, err:%v", req, err)
        return err
    }

    return nil
}

func (m *ShowListMgr) GetChannelLiveShowListEnd(ctx context.Context, startTsBefore, startTsNotBefore uint32) ([]*store.ChannelLiveShow, error) {
    showList, err := m.store.GetChannelLiveShowListEnd(ctx, startTsBefore, startTsNotBefore)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowListEnd fail to GetChannelLiveShowListEnd. startTsBefore:%d, startTsNotBefore:%d, err:%v", startTsBefore, startTsNotBefore, err)
        return nil, err
    }
    if len(showList) == 0 {
        return make([]*store.ChannelLiveShow, 0), nil
    }
        showIdList := make([]uint32, 0, len(showList))
    for _, show := range showList {
        showIdList = append(showIdList, show.ID)
    }
    log.DebugWithCtx(ctx, "GetChannelLiveShowListEnd, showIdList: %+v", showIdList)
    notNotifyShowIdList, err := m.cache.CheckChannelLiveShowEndNotify(ctx, showIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowListEnd fail to CheckChannelLiveShowEndNotify. showIdList:%+v, err:%v")
        return nil, err
    }
    log.DebugWithCtx(ctx, "GetChannelLiveShowListEnd, notNotifyShowIdList: %+v", notNotifyShowIdList)

    notNotifyShowIdMap := make(map[uint32]struct{})
    for _, showId := range notNotifyShowIdList {
        notNotifyShowIdMap[showId] = struct{}{}
    }

    rs := make([]*store.ChannelLiveShow, 0)
    for _, show := range showList {
        if _, ok := notNotifyShowIdMap[show.ID]; ok {
            rs = append(rs, show)
        }
    }
    return rs, nil
}

func (m *ShowListMgr) MarkChannelLiveShowEndNotify(ctx context.Context, showIdList []uint32) error {
    return m.cache.MarkChannelLiveShowEndNotify(ctx, showIdList)
}

func (m *ShowListMgr) SetApprovalAuditType(ctx context.Context, req *pb.SetApprovalAuditTypeRequest) error {
    approval, err := m.store.GetChannelLiveShowApprovalById(ctx, req.GetApprovalId())
    if err != nil {
        log.ErrorWithCtx(ctx, "SetApprovalAuditType fail to GetChannelLiveShowApprovalById. showApprovalId:%d, err:%v", req.GetApprovalId(), err)
        return err
    }

    auditType := uint32(store.ChannelLiveShowApprovalAuditTypeReject)
    if req.GetChannelLiveShowApprovalAuditType() == uint32(pb.ChannelLiveShowApprovalAuditType_ChannelLiveShowApprovalAuditTypePass) {
        auditType = store.ChannelLiveShowApprovalAuditTypePass
    }

    switch req.GetChannelLiveShowApprovalAuditScene() {
    case uint32(pb.ChannelLiveShowApprovalAuditScene_ChannelLiveShowApprovalAuditSceneName):
        err := m.store.UpdateTextMachineAuditType(ctx, req.GetApprovalId(), auditType)
        if err != nil {
            log.ErrorWithCtx(ctx, "SetApprovalAuditType fail to UpdateTextMachineAuditType. req:%+v, err:%v", req, err)
            return err
        }
        approval.TextMachineAuditType = auditType
    case uint32(pb.ChannelLiveShowApprovalAuditScene_ChannelLiveShowApprovalAuditSceneCover):
        err := m.store.UpdateImgMachineAuditType(ctx, req.GetApprovalId(), auditType)
        if err != nil {
            log.ErrorWithCtx(ctx, "SetApprovalAuditType fail to UpdateImgMachineAuditType. req:%+v, err:%v", req, err)
        }
        approval.ImgMachineAuditType = auditType
    case uint32(pb.ChannelLiveShowApprovalAuditScene_ChannelLiveShowApprovalAuditSceneAudio):
        err := m.store.UpdateAudioMachineAuditType(ctx, req.GetApprovalId(), auditType)
        if err != nil {
            log.ErrorWithCtx(ctx, "SetApprovalAuditType fail to UpdateAudioMachineAuditType. req:%+v, err:%v", req, err)
            return err
        }
        approval.AudioMachineAuditType = auditType
    }
    // 机审不通过回退
    if auditType == store.ChannelLiveShowApprovalAuditTypeReject {
        m.rollbackUserDeclareLimit(ctx, approval)
    }
    return nil
}

func (m *ShowListMgr) GetChannelLiveShowByApprovalId(ctx context.Context, approvalId uint32) (*store.ChannelLiveShow, error) {
    return m.store.GetChannelLiveShowByApprovalId(ctx, approvalId)
}
