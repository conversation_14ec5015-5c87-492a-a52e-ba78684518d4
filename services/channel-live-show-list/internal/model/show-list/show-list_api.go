package show_score

import(
	context "context"
	pb "golang.52tt.com/protocol/services/channel-live-show-list"
	entity "golang.52tt.com/services/channel-live-show-list/internal/entity"
	store "golang.52tt.com/services/channel-live-show-list/internal/model/show-list/store"
)

type IShowListMgr interface {
	AddChannelImNotify(ctx context.Context, notifyInfo *entity.ChannelImNotify) error
	AddChannelLiveShowTag(ctx context.Context, id uint32, tagName string, parentId uint32) error
	AddShowTag(ctx context.Context, req *pb.AddShowTagRequest) (*pb.AddShowTagResponse,error)
	DeclareShow(ctx context.Context, req *pb.DeclareShowRequest) (*pb.DeclareShowResponse,error)
	DeleteChannelLiveShowTag(ctx context.Context, id uint32) error
	DeleteShowTag(ctx context.Context, req *pb.DeleteShowTagRequest) (*pb.DeleteShowTagResponse,error)
	GetChannelLiveShowApprovalList(ctx context.Context, datetime, ttid, approvalFilterStatus, pageNum, pageSize uint32) (*pb.GetChannelLiveShowApprovalListResp,error)
	GetChannelLiveShowByApprovalId(ctx context.Context, approvalId uint32) (*store.ChannelLiveShow,error)
	GetChannelLiveShowInfoById(ctx context.Context, showId uint32) (*pb.ShowItem,error)
	GetChannelLiveShowListEnd(ctx context.Context, startTsBefore, startTsNotBefore uint32) ([]*store.ChannelLiveShow,error)
	GetChannelLiveTagTree(ctx context.Context) (*pb.TagNode,map[uint32]*pb.TagNode,error)
	GetLiveShowEntryInfo(ctx context.Context, req *pb.GetLiveShowEntryInfoRequest) (*pb.GetLiveShowEntryInfoResponse,error)
	GetNearlyChannelLiveShowInfo(ctx context.Context, anchorUid uint32) (*pb.ShowItem,error)
	GetRelatedTagNodeId(ctx context.Context, idList []uint32) ([]uint32,error)
	GetShowList(ctx context.Context, req *pb.GetShowListRequest) (*pb.GetShowListResponse,error)
	GetShowTag(ctx context.Context) (*pb.GetShowTagResponse,error)
	GetShowTime(ctx context.Context, datetime uint32) (*pb.GetShowTimeResponse,error)
	GetUserRemainDeclareCnt(ctx context.Context, uid uint32) (uint32,uint32,error)
	HandleShowApproval(ctx context.Context, req *pb.HandleShowApprovalRequest) (*pb.HandelShowApprovalResponse,error)
	InsertChannelLiveShow(ctx context.Context, showApproval *store.ChannelLiveShowApproval) error
	ManualAddShow(ctx context.Context, req *pb.TestToolRequest_TestShowBeginPush) error
	MarkChannelLiveShowEndNotify(ctx context.Context, showIdList []uint32) error
	SetApprovalAuditType(ctx context.Context, req *pb.SetApprovalAuditTypeRequest) error
	Stop() 
	UnfoldShowList(ctx context.Context, req *pb.UnfoldShowListRequest) ([]*pb.ShowItem,error)
	UpdateShowApprovalTag(ctx context.Context, req *pb.ModifyShowApprovalTagRequest) (*pb.ModifyShowApprovalTagResponse,error)
}

