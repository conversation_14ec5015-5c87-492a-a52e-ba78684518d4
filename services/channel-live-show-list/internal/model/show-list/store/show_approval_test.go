package store

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

// TestStore_InsertChannelLiveShowApproval 测试插入审批记录
func TestStore_InsertChannelLiveShowApproval(t *testing.T) {
	now := time.Now()
	tests := []struct {
		name     string
		approval *ChannelLiveShowApproval
		mockFunc func(sqlmock.Sqlmock)
		wantErr  bool
	}{
		{
			name: "success - insert approval",
			approval: &ChannelLiveShowApproval{
				ShowName:                "测试节目",
				ShowStartTime:           1704070800,
				ShowEndTime:             1704074400,
				ShowCoverImg:            "cover.jpg",
				ShowDescAudio:           "desc.mp3",
				ShowDescAudioDuration:   60,
				VoiceTagId:              10,
				ContentTagId:            20,
				AnchorUid:               12345,
				TextMachineAuditType: ChannelLiveShowApprovalAuditTypePending,
				ImgMachineAuditType: ChannelLiveShowApprovalAuditTypePending,
				AudioMachineAuditType:   ChannelLiveShowApprovalAuditTypePending,
				ManualAuditType:         ChannelLiveShowApprovalAuditTypePending,
				ApprovalOperator:        "111",
				ApprovalTime:            now,
			},
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec("INSERT INTO channel_live_show_approval").
					WithArgs("测试节目", uint32(1704070800), uint32(1704074400), "cover.jpg", "desc.mp3",
						uint32(60), uint32(10), uint32(20), uint32(12345),
						uint32(ChannelLiveShowApprovalAuditTypePending),
						uint32(ChannelLiveShowApprovalAuditTypePending),
						uint32(ChannelLiveShowApprovalAuditTypePending),
						uint32(ChannelLiveShowApprovalAuditTypePending),
						"111", now).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantErr: false,
		},
		{
			name: "failure - database error",
			approval: &ChannelLiveShowApproval{
				ShowName:  "测试节目",
				AnchorUid: 12345,
			},
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec("INSERT INTO channel_live_show_approval").
					WillReturnError(sql.ErrConnDone)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store, mock, cleanup := setupTestStore(t)
			defer cleanup()

			tt.mockFunc(mock)

			_, err := store.InsertChannelLiveShowApproval(context.Background(), tt.approval)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

// TestStore_SelectChannelLiveShowApprovalById 测试根据ID查询审批记录
func TestStore_SelectChannelLiveShowApprovalById(t *testing.T) {
	tests := []struct {
		name         string
		id           uint32
		mockFunc     func(sqlmock.Sqlmock)
		wantErr      bool
		wantApproval *ChannelLiveShowApproval
	}{
		{
			name: "success - get approval by id",
			id:   1,
			mockFunc: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"id", "show_name", "show_start_time", "show_end_time",
					"show_cover_img", "show_desc_audio", "show_desc_audio_duration", "voice_tag_id",
					"content_tag_id", "anchor_uid", "text_machine_audit_type","img_machine_audit_type", "audio_machine_audit_type",
					"manual_audit_type", "approval_operator", "approval_time", "create_time", "update_time"}).
					AddRow(1, "测试节目", 1704070800, 1704074400, "cover.jpg", "desc.mp3", 60, 10, 20, 12345,
						ChannelLiveShowApprovalAuditTypePass, ChannelLiveShowApprovalAuditTypePass, ChannelLiveShowApprovalAuditTypePass,
						ChannelLiveShowApprovalAuditTypePass, "admin", time.Now(), time.Now(), time.Now())

				mock.ExpectQuery("SELECT .* FROM channel_live_show_approval WHERE id = \\?").
					WithArgs(1).
					WillReturnRows(rows)
			},
			wantErr: false,
			wantApproval: &ChannelLiveShowApproval{
				ID:                      1,
				ShowName:                "测试节目",
				ShowStartTime:           1704070800,
				ShowEndTime:             1704074400,
				ShowCoverImg:            "cover.jpg",
				ShowDescAudio:           "desc.mp3",
				ShowDescAudioDuration:   60,
				VoiceTagId:              10,
				ContentTagId:            20,
				AnchorUid:               12345,
				TextMachineAuditType:   ChannelLiveShowApprovalAuditTypePass,
				ImgMachineAuditType: ChannelLiveShowApprovalAuditTypePass,
				AudioMachineAuditType:   ChannelLiveShowApprovalAuditTypePass,
				ManualAuditType:         ChannelLiveShowApprovalAuditTypePass,
				ApprovalOperator:        "admin",
			},
		},
		{
			name: "failure - database error",
			id:   1,
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery("SELECT .* FROM channel_live_show_approval WHERE id = \\?").
					WithArgs(1).
					WillReturnError(sql.ErrConnDone)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store, mock, cleanup := setupTestStore(t)
			defer cleanup()

			tt.mockFunc(mock)

			approval, err := store.GetChannelLiveShowApprovalById(context.Background(), tt.id)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, approval)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, approval)
				if tt.wantApproval != nil {
					assert.Equal(t, tt.wantApproval.ID, approval.ID)
					assert.Equal(t, tt.wantApproval.ShowName, approval.ShowName)
					assert.Equal(t, tt.wantApproval.AnchorUid, approval.AnchorUid)
					assert.Equal(t, tt.wantApproval.TextMachineAuditType, approval.TextMachineAuditType)
					assert.Equal(t, tt.wantApproval.ImgMachineAuditType, approval.ImgMachineAuditType)
					assert.Equal(t, tt.wantApproval.AudioMachineAuditType, approval.AudioMachineAuditType)
					assert.Equal(t, tt.wantApproval.ManualAuditType, approval.ManualAuditType)
				}
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

// TestStore_PageMachineAuditDoneChannelLiveShowApproval 测试分页查询机审完成的审批记录
func TestStore_PageMachineAuditDoneChannelLiveShowApproval(t *testing.T) {
	tests := []struct {
		name          string
		datetime      uint32
		uid           uint32
		auditTypeList []uint32
		pageNum       uint32
		pageSize      uint32
		mockFunc      func(sqlmock.Sqlmock)
		wantErr       bool
		wantCount     int
	}{
		{
			name:          "success - page with datetime and uid",
			datetime:      1704067200, // 2024-01-01
			uid:           12345,
			auditTypeList: []uint32{ChannelLiveShowApprovalAuditTypePending},
			pageNum:       1,
			pageSize:      10,
			mockFunc: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"id", "show_name", "show_start_time", "show_end_time",
					"show_cover_img", "show_desc_audio", "show_desc_audio_duration", "voice_tag_id",
					"content_tag_id", "anchor_uid", "text_machine_audit_type", "img_machine_audit_type", "audio_machine_audit_type",
					"manual_audit_type", "approval_operator", "approval_time", "create_time", "update_time"}).
					AddRow(1, "测试节目1", 1704070800, 1704074400, "cover1.jpg", "desc1.mp3", 60, 10, 20, 12345,
						ChannelLiveShowApprovalAuditTypePass,ChannelLiveShowApprovalAuditTypePass, ChannelLiveShowApprovalAuditTypePass,
						ChannelLiveShowApprovalAuditTypePending, "", time.Now(), time.Now(), time.Now()).
					AddRow(2, "测试节目2", 1704078000, 1704081600, "cover2.jpg", "desc2.mp3", 90, 11, 21, 12345,
						ChannelLiveShowApprovalAuditTypePass, ChannelLiveShowApprovalAuditTypePass, ChannelLiveShowApprovalAuditTypePass,
						ChannelLiveShowApprovalAuditTypePending, "", time.Now(), time.Now(), time.Now())

				mock.ExpectQuery("SELECT .* FROM channel_live_show_approval WHERE .* LIMIT \\?, \\?").
					WithArgs(0, 10).
					WillReturnRows(rows)
			},
			wantErr:   false,
			wantCount: 2,
		},
		{
			name:          "success - page without datetime",
			datetime:      0,
			uid:           12345,
			auditTypeList: []uint32{},
			pageNum:       1,
			pageSize:      5,
			mockFunc: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"id", "show_name", "show_start_time", "show_end_time",
					"show_cover_img", "show_desc_audio", "show_desc_audio_duration", "voice_tag_id",
					"content_tag_id", "anchor_uid", "text_machine_audit_type","img_machine_audit_type", "audio_machine_audit_type",
					"manual_audit_type", "approval_operator", "approval_time", "create_time", "update_time"}).
					AddRow(1, "测试节目", 1704070800, 1704074400, "cover.jpg", "desc.mp3", 60, 10, 20, 12345,
						ChannelLiveShowApprovalAuditTypePass,ChannelLiveShowApprovalAuditTypePass, ChannelLiveShowApprovalAuditTypePass,
						ChannelLiveShowApprovalAuditTypePending, "", time.Now(), time.Now(), time.Now())

				mock.ExpectQuery("SELECT .* FROM channel_live_show_approval WHERE .* LIMIT \\?, \\?").
					WithArgs(0, 5).
					WillReturnRows(rows)
			},
			wantErr:   false,
			wantCount: 1,
		},
		{
			name:     "failure - database error",
			datetime: 1704067200,
			uid:      12345,
			pageNum:  1,
			pageSize: 10,
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery("SELECT .* FROM channel_live_show_approval WHERE .* LIMIT \\?, \\?").
					WillReturnError(sql.ErrConnDone)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store, mock, cleanup := setupTestStore(t)
			defer cleanup()

			tt.mockFunc(mock)

			approvals, err := store.PageMachineAuditDoneChannelLiveShowApproval(
				context.Background(), tt.datetime, tt.uid, tt.auditTypeList, tt.pageNum, tt.pageSize, 1)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, approvals)
			} else {
				assert.NoError(t, err)
				assert.Len(t, approvals, tt.wantCount)
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

// TestStore_UpdateManualAuditType 测试更新人工审核状态
func TestStore_UpdateManualAuditType(t *testing.T) {
	tests := []struct {
		name      string
		id        uint32
		auditType uint32
		operator  string
		mockFunc  func(sqlmock.Sqlmock)
		wantErr   bool
	}{
		{
			name:      "success - update to pass",
			id:        1,
			auditType: ChannelLiveShowApprovalAuditTypePass,
			operator:  "admin",
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec("UPDATE channel_live_show_approval SET manual_audit_type = \\?, approval_operator = \\?, approval_time = NOW\\(\\) WHERE id = \\?").
					WithArgs(ChannelLiveShowApprovalAuditTypePass, "admin", 1).
					WillReturnResult(sqlmock.NewResult(0, 1))
			},
			wantErr: false,
		},
		{
			name:      "success - update to reject",
			id:        2,
			auditType: ChannelLiveShowApprovalAuditTypeReject,
			operator:  "admin2",
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec("UPDATE channel_live_show_approval SET manual_audit_type = \\?, approval_operator = \\?, approval_time = NOW\\(\\) WHERE id = \\?").
					WithArgs(ChannelLiveShowApprovalAuditTypeReject, "admin2", 2).
					WillReturnResult(sqlmock.NewResult(0, 1))
			},
			wantErr: false,
		},
		{
			name:      "failure - database error",
			id:        1,
			auditType: ChannelLiveShowApprovalAuditTypePass,
			operator:  "admin",
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec("UPDATE channel_live_show_approval SET manual_audit_type = \\?, approval_operator = \\?, approval_time = NOW\\(\\) WHERE id = \\?").
					WithArgs(ChannelLiveShowApprovalAuditTypePass, "admin", 1).
					WillReturnError(sql.ErrConnDone)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store, mock, cleanup := setupTestStore(t)
			defer cleanup()

			tt.mockFunc(mock)

			err := store.UpdateManualAuditType(context.Background(), tt.id, tt.auditType, tt.operator)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

// TestStore_CountMachineAuditDoneChannelLiveShowApproval 测试统计机审完成的审批记录数量
func TestStore_CountMachineAuditDoneChannelLiveShowApproval(t *testing.T) {
	tests := []struct {
		name          string
		datetime      uint32
		uid           uint32
		auditTypeList []uint32
		mockFunc      func(sqlmock.Sqlmock)
		wantErr       bool
		wantCount     uint32
	}{
		{
			name:          "success - count with datetime and uid",
			datetime:      1704067200,
			uid:           12345,
			auditTypeList: []uint32{ChannelLiveShowApprovalAuditTypePending},
			mockFunc: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"COUNT(1)"}).AddRow(5)
				mock.ExpectQuery("SELECT COUNT\\(1\\) FROM channel_live_show_approval WHERE .*").
					WillReturnRows(rows)
			},
			wantErr:   false,
			wantCount: 5,
		},
		{
			name:          "success - count without datetime",
			datetime:      0,
			uid:           12345,
			auditTypeList: []uint32{},
			mockFunc: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"COUNT(1)"}).AddRow(10)
				mock.ExpectQuery("SELECT COUNT\\(1\\) FROM channel_live_show_approval WHERE .*").
					WillReturnRows(rows)
			},
			wantErr:   false,
			wantCount: 10,
		},
		{
			name:     "failure - database error",
			datetime: 1704067200,
			uid:      12345,
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery("SELECT COUNT\\(1\\) FROM channel_live_show_approval WHERE .*").
					WillReturnError(sql.ErrConnDone)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store, mock, cleanup := setupTestStore(t)
			defer cleanup()

			tt.mockFunc(mock)

			count, err := store.CountMachineAuditDoneChannelLiveShowApproval(
				context.Background(), tt.datetime, tt.uid, tt.auditTypeList)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, uint32(0), count)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantCount, count)
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

// TestStore_UpdateChannelLiveShowApprovalTagId 测试更新审批记录的标签ID
func TestStore_UpdateChannelLiveShowApprovalTagId(t *testing.T) {
	tests := []struct {
		name         string
		id           uint32
		voiceTagId   uint32
		contentTagId uint32
		mockFunc     func(sqlmock.Sqlmock)
		wantErr      bool
	}{
		{
			name:         "success - update tag ids",
			id:           1,
			voiceTagId:   10,
			contentTagId: 20,
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec("UPDATE channel_live_show_approval SET voice_tag_id = \\?, content_tag_id = \\? WHERE id = \\?").
					WithArgs(10, 20, 1).
					WillReturnResult(sqlmock.NewResult(0, 1))
			},
			wantErr: false,
		},
		{
			name:         "failure - database error",
			id:           1,
			voiceTagId:   10,
			contentTagId: 20,
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec("UPDATE channel_live_show_approval SET voice_tag_id = \\?, content_tag_id = \\? WHERE id = \\?").
					WithArgs(10, 20, 1).
					WillReturnError(sql.ErrConnDone)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store, mock, cleanup := setupTestStore(t)
			defer cleanup()

			tt.mockFunc(mock)

			err := store.UpdateChannelLiveShowApprovalTagId(context.Background(), tt.id, tt.voiceTagId, tt.contentTagId)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}
