package store

import (
    "context"
    "fmt"
    "strings"
    "time"

    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
)

type ChannelLiveShow struct {
    ID                    uint32    `db:"id"`
    ApprovalId            uint32    `db:"approval_id"`
    ShowName              string    `db:"show_name"`
    ShowStartTime         uint32    `db:"show_start_time"`
    ShowEndTime           uint32    `db:"show_end_time"`
    ShowCoverImg          string    `db:"show_cover_img"`
    ShowDescAudio         string    `db:"show_desc_audio"`
    ShowDescAudioDuration uint32    `db:"show_desc_audio_duration"`
    VoiceTagId            uint32    `db:"voice_tag_id"`
    ContentTagId          uint32    `db:"content_tag_id"`
    AnchorUid             uint32    `db:"anchor_uid"`
    ChannelId             uint32    `db:"channel_id"`
    CreateTime            time.Time `db:"create_time"`
}

func (cls *ChannelLiveShow) allFiledNoId() []interface{} {
    return []interface{}{cls.ApprovalId, cls.ShowName, cls.ShowStartTime, cls.ShowEndTime, cls.ShowCoverImg, cls.ShowDescAudio, cls.ShowDescAudioDuration, cls.VoiceTagId, cls.ContentTagId, cls.AnchorUid, cls.ChannelId}
}

func (cls *ChannelLiveShow) allFiledNoIdName() string {
    return "approval_id, show_name, show_start_time, show_end_time, show_cover_img, show_desc_audio, show_desc_audio_duration, voice_tag_id, content_tag_id, anchor_uid, channel_id"
}

func (cls *ChannelLiveShow) allFiledName() string {
    return "id, " + cls.allFiledNoIdName()
}

func (cls *ChannelLiveShow) allFiledWithCreateTime() []interface{} {
    return append(cls.allFiledNoId(), time.Now())
}

var createChannelLiveShowListTbl = `CREATE TABLE IF NOT EXISTS channel_live_show_list (
  id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '审批id',
  approval_id int(10) unsigned NOT NULL COMMENT '自增id',
  show_name varchar(255) NOT NULL DEFAULT '' COMMENT '节目名称',
  show_start_time int(10) unsigned NOT NULL DEFAULT 0 COMMENT '节目开始时间',
  show_end_time int(10) unsigned NOT NULL DEFAULT 0 COMMENT '节目结束时间',
  show_cover_img varchar(255) NOT NULL DEFAULT '' COMMENT '节目封面图',
  show_desc_audio varchar(255) NOT NULL DEFAULT '' COMMENT '节目描述音频',
  show_desc_audio_duration int(10) unsigned NOT NULL DEFAULT 0 COMMENT '节目描述音频时长',
  voice_tag_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '语音标签id',
  content_tag_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '内容标签id',
  anchor_uid int(10) unsigned NOT NULL DEFAULT 0 COMMENT '主播uid',
  channel_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '直播间cid',
  create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      PRIMARY KEY (id),
      INDEX idx_tag_start_time(show_start_time, voice_tag_id, content_tag_id),
      INDEX idx_show_start_time_anchor_uid(show_start_time, anchor_uid),
      INDEX idx_show_end_time(show_end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '语音直播节目表';`

func (s *Store) CreateChannelLiveShowListTable(ctx context.Context) error {
    _, err := s.db.ExecContext(ctx, createChannelLiveShowListTbl)
    if err != nil {
        log.ErrorWithCtx(ctx, "CreateChannelLiveShowListTable fail to create table. err:%v", err)
        return err
    }
    log.DebugWithCtx(ctx, "CreateChannelLiveShowListTable success")
    return nil
}

// InsertChannelLiveShow 插入节目表记录
func (s *Store) InsertChannelLiveShow(ctx context.Context, show *ChannelLiveShow) error {
    _, err := s.db.ExecContext(ctx, "INSERT INTO channel_live_show_list ("+show.allFiledNoIdName()+") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", show.allFiledNoId()...)
    if err != nil {
        log.ErrorWithCtx(ctx, "InsertChannelLiveShow fail to insert. %+v, err:%v", show, err)
        return err
    }
    log.DebugWithCtx(ctx, "InsertChannelLiveShow success, show:%+v", show)
    return nil
}

// PageChannelLiveShowByStartTimeTag 分页获取节目表记录
func (s *Store) PageChannelLiveShowByStartTimeTag(ctx context.Context, startTime, endTime uint32, voiceTagId, contentTagId []uint32, offset, limit uint32) ([]*ChannelLiveShow, error) {
    voiceTagIdStr := genParamJoinStr(voiceTagId)
    contentTagIdStr := genParamJoinStr(contentTagId)
    voiceTagQuery := ""
    if voiceTagIdStr != "" {
        voiceTagQuery = " AND voice_tag_id IN (" + voiceTagIdStr + ")"
    }
    contentTagQuery := ""
    if contentTagIdStr != "" {
        contentTagQuery = " AND content_tag_id IN (" + contentTagIdStr + ")"
    }
    query := "SELECT " + new(ChannelLiveShow).allFiledName() + " FROM channel_live_show_list WHERE show_start_time >= ? AND show_start_time < ? " + voiceTagQuery + contentTagQuery + " order by show_start_time LIMIT ?, ?"
    log.DebugWithCtx(ctx, "PageChannelLiveShowByStartTimeTag query: %s", query)
    shows := make([]*ChannelLiveShow, 0)
    err := s.db.SelectContext(ctx, &shows, query, startTime, endTime, offset, limit)
    if err != nil {
        log.ErrorWithCtx(ctx, "PageChannelLiveShowByStartTimeTag fail to select. startTime:%d, endTime:%d, voiceTagId:%v, contentTagId:%v, offset:%d, limit:%d, err:%v", startTime, endTime, voiceTagId, contentTagId, offset, limit, err)
        return nil, err
    }

    log.DebugWithCtx(ctx, "PageChannelLiveShowByStartTimeTag success, startTime:%d, endTime:%d, voiceTagId:%v, contentTagId:%v, offset:%d, limit:%d, shows:%+v", startTime, endTime, voiceTagId, contentTagId, offset, limit, shows)
    return shows, nil
}

func (s *Store) GetChannelLiveShowCntByStartTimeTag(ctx context.Context, startTime, endTime uint32, voiceTagId, contentTagId []uint32) (uint32, error) {
    voiceTagIdStr := genParamJoinStr(voiceTagId)
    contentTagIdStr := genParamJoinStr(contentTagId)
    voiceTagQuery := ""
    if voiceTagIdStr != "" {
        voiceTagQuery = " AND voice_tag_id IN (" + voiceTagIdStr + ")"
    }
    contentTagQuery := ""
    if contentTagIdStr != "" {
        contentTagQuery = " AND content_tag_id IN (" + contentTagIdStr + ")"
    }
    query := "SELECT COUNT(*) FROM channel_live_show_list WHERE show_start_time >= ? AND show_start_time < ? " + voiceTagQuery + contentTagQuery
    var count uint32
    err := s.db.GetContext(ctx, &count, query, startTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowCntByStartTimeTag fail to select. startTime:%d, endTime:%d, voiceTagId:%v, contentTagId:%v, err:%v", startTime, endTime, voiceTagId, contentTagId, err)
        return 0, err
    }

    log.DebugWithCtx(ctx, "GetChannelLiveShowCntByStartTimeTag success, startTime:%d, endTime:%d, voiceTagId:%v, contentTagId:%v, count:%d", startTime, endTime, voiceTagId, contentTagId, count)
    return count, nil
}

func genParamJoinStr(list []uint32) string {
    strList := make([]string, 0, len(list))
    for _, i := range list {
        strList = append(strList, fmt.Sprint(i))
    }

    return strings.Join(strList, ",")
}

// GetChannelLiveShowByAnchorUid 根据anchorUid获取 进行中+未开始 的节目表记录
func (s *Store) GetChannelLiveShowByAnchorUid(ctx context.Context, anchorUid uint32) ([]*ChannelLiveShow, error) {
    now := time.Now()
    shows := make([]*ChannelLiveShow, 0)

    //根据anchorUid获取 进行中+未开始 的节目表记录
    query := "SELECT " + new(ChannelLiveShow).allFiledName() + " FROM channel_live_show_list WHERE (show_start_time >= ? OR show_end_time >= ?) AND anchor_uid = ?"

    err := s.db.SelectContext(ctx, &shows, query, now.Unix(), now.Unix(), anchorUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowByAnchorUid fail to select. anchorUid:%d, err:%v", anchorUid, err)
        return nil, err
    }

    log.DebugWithCtx(ctx, "GetChannelLiveShowByAnchorUid success, anchorUid:%d, shows:%+v", anchorUid, shows)
    return shows, nil
}

// GetChannelLiveShowByShowId 根据showId获取节目表记录
func (s *Store) GetChannelLiveShowByShowId(ctx context.Context, showId uint32) (*ChannelLiveShow, error) {
    show := new(ChannelLiveShow)
    query := "SELECT " + show.allFiledName() + " FROM channel_live_show_list WHERE id = ?"
    err := s.db.GetContext(ctx, show, query, showId)
    if err != nil {
        // 记录不存在
        if mysql.IsNoRowsError(err) {
            return show, nil
        }
        log.ErrorWithCtx(ctx, "GetChannelLiveShowByShowId fail to select. showId:%d, err:%v", showId, err)
        return show, err
    }

    return show, nil
}

//GetChannelLiveShowListBegin 获取一开始的节目记录列表
func (s *Store) GetChannelLiveShowListBegin(ctx context.Context, startTsBefore, startTsNotBefore uint32) ([]*ChannelLiveShow, error) {
    shows := make([]*ChannelLiveShow, 0)

    query := "SELECT " + new(ChannelLiveShow).allFiledName() + " FROM channel_live_show_list WHERE show_start_time < ? AND show_start_time >= ? ORDER BY show_start_time ASC"
    err := s.db.SelectContext(ctx, &shows, query, startTsBefore, startTsNotBefore)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowListBegin fail to select. startTsBefore:%d, startTsNotBefore:%d, err:%v", startTsBefore, startTsNotBefore, err)
        return nil, err
    }

    return shows, nil
}

func (s *Store) GetChannelLiveShowListEnd(ctx context.Context, startTsBefore, startTsNotBefore uint32) ([]*ChannelLiveShow, error) {
    shows := make([]*ChannelLiveShow, 0)
    query := "SELECT " + new(ChannelLiveShow).allFiledName() + " FROM channel_live_show_list WHERE show_end_time < ? AND show_end_time >= ? ORDER BY show_start_time DESC"
    err := s.db.SelectContext(ctx, &shows, query, startTsBefore, startTsNotBefore)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowListEnd fail to select. startTsBefore:%d, startTsNotBefore:%d, err:%v", startTsBefore, startTsNotBefore, err)
        return nil, err
    }

    return shows, nil
}

func (s *Store) CountChannelLiveShowTimeRange(ctx context.Context, startTime, endTime uint32) (uint32, error) {
    count := uint32(0)
    query := "SELECT COUNT(1) FROM channel_live_show_list WHERE show_start_time >= ? AND show_end_time <= ?"
    err := s.db.GetContext(ctx, &count, query, startTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "CountChannelLiveShowTimeRange fail to select. startTime:%d, endTime:%d, err:%v", startTime, endTime, err)
        return 0, err
    }

    return count, nil
}

func (s *Store) GetChannelLiveShowByApprovalId(ctx context.Context, approvalId uint32) (*ChannelLiveShow, error) {
    show := &ChannelLiveShow{}
    query := "SELECT " + show.allFiledName() + " FROM channel_live_show_list WHERE approval_id = ?"
    err := s.db.GetContext(ctx, show, query, approvalId)
    if err != nil {
        // 记录不存在
        if mysql.IsNoRowsError(err) {
            return show, nil
        }
        log.ErrorWithCtx(ctx, "GetChannelLiveShowByApprovalId fail to select. approvalId:%d, err:%v", approvalId, err)
        return show, err
    }

    return show, nil
}