package store

import(
	context "context"
)

type IStore interface {
	Close() error
	CountChannelLiveShowTimeRange(ctx context.Context, startTime, endTime uint32) (uint32,error)
	CountMachineAuditDoneChannelLiveShowApproval(ctx context.Context, datetime, uid uint32, auditTypeList []uint32) (uint32,error)
	CreateChannelLiveShowListTable(ctx context.Context) error
	DeleteChannelLiveShowTag(ctx context.Context, id uint32) error
	ExistsPendingPassApproval(ctx context.Context, anchorUid, startTme uint32) (bool,error)
	GetChannelLiveShowApprovalById(ctx context.Context, id uint32) (*ChannelLiveShowApproval,error)
	GetChannelLiveShowByAnchorUid(ctx context.Context, anchorUid uint32) ([]*ChannelLiveShow,error)
	GetChannelLiveShowByApprovalId(ctx context.Context, approvalId uint32) (*ChannelLiveShow,error)
	GetChannelLiveShowByShowId(ctx context.Context, showId uint32) (*ChannelLiveShow,error)
	GetChannelLiveShowCntByStartTimeTag(ctx context.Context, startTime, endTime uint32, voiceTagId, contentTagId []uint32) (uint32,error)
	GetChannelLiveShowListBegin(ctx context.Context, startTsBefore, startTsNotBefore uint32) ([]*ChannelLiveShow,error)
	GetChannelLiveShowListEnd(ctx context.Context, startTsBefore, startTsNotBefore uint32) ([]*ChannelLiveShow,error)
	InsertChannelLiveShow(ctx context.Context, show *ChannelLiveShow) error
	InsertChannelLiveShowApproval(ctx context.Context, show *ChannelLiveShowApproval) (uint32,error)
	InsertChannelLiveShowTag(ctx context.Context, tag *ChannelLiveShowTag) error
	PageChannelLiveShowByStartTimeTag(ctx context.Context, startTime, endTime uint32, voiceTagId, contentTagId []uint32, offset, limit uint32) ([]*ChannelLiveShow,error)
	PageMachineAuditDoneChannelLiveShowApproval(ctx context.Context, datetime, uid uint32, auditTypeList []uint32, pageNum, pageSize, sortType uint32) ([]*ChannelLiveShowApproval,error)
	SelectAllChannelLiveShowTag(ctx context.Context) ([]*ChannelLiveShowTag,error)
	UpdateAudioMachineAuditType(ctx context.Context, id, auditType uint32) error
	UpdateChannelLiveShowApprovalTagId(ctx context.Context, id, voiceTagId, contentTagId uint32) error
	UpdateImgMachineAuditType(ctx context.Context, id, auditType uint32) error
	UpdateManualAuditType(ctx context.Context, id, auditType uint32, auditOperator string) error
	UpdateTextMachineAuditType(ctx context.Context, id, auditType uint32) error
}

