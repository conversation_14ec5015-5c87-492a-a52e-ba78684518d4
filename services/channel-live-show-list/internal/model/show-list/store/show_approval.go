package store

import (
	"context"
	"fmt"
	"strings"
	"time"

	"golang.52tt.com/pkg/log"
)

const (
	ChannelLiveShowApprovalAuditTypeInvalid = iota
	ChannelLiveShowApprovalAuditTypePending
	ChannelLiveShowApprovalAuditTypePass
	ChannelLiveShowApprovalAuditTypeReject
)

type ChannelLiveShowApproval struct {
	ID                    uint32    `db:"id"`
	ShowName              string    `db:"show_name"`
	ShowStartTime         uint32    `db:"show_start_time"`
	ShowEndTime           uint32    `db:"show_end_time"`
	ShowCoverImg          string    `db:"show_cover_img"`
	ShowDescAudio         string    `db:"show_desc_audio"`
	ShowDescAudioDuration uint32    `db:"show_desc_audio_duration"`
	VoiceTagId            uint32    `db:"voice_tag_id"`
	ContentTagId          uint32    `db:"content_tag_id"`
	AnchorUid             uint32    `db:"anchor_uid"`
	TextMachineAuditType  uint32    `db:"text_machine_audit_type"`
	ImgMachineAuditType   uint32    `db:"img_machine_audit_type"`   // 图文机审类型
	AudioMachineAuditType uint32    `db:"audio_machine_audit_type"` // 音频机审类型
	ManualAuditType       uint32    `db:"manual_audit_type"`        // 人审类型
	ApprovalOperator      string    `db:"approval_operator"`        // 审批人
	ApprovalTime          time.Time `db:"approval_time"`            // 审批时间
	CreateTime            time.Time `db:"create_time"`
	UpdateTime            time.Time `db:"update_time"`
}

func (clsa *ChannelLiveShowApproval) allFieldNoId() []interface{} {
	return []interface{}{clsa.ShowName, clsa.ShowStartTime, clsa.ShowEndTime, clsa.ShowCoverImg, clsa.ShowDescAudio, clsa.ShowDescAudioDuration, clsa.VoiceTagId, clsa.ContentTagId, clsa.AnchorUid, clsa.TextMachineAuditType, clsa.ImgMachineAuditType, clsa.AudioMachineAuditType, clsa.ManualAuditType, clsa.ApprovalOperator, clsa.ApprovalTime}
}


func (clsa *ChannelLiveShowApproval) allFieldNameNoId() string {
	return "show_name, show_start_time, show_end_time, show_cover_img, show_desc_audio, show_desc_audio_duration, voice_tag_id, content_tag_id, anchor_uid, text_machine_audit_type, img_machine_audit_type, audio_machine_audit_type, manual_audit_type, approval_operator, approval_time"
}

func (clsa *ChannelLiveShowApproval) allFieldName() string {
	return "id, " + clsa.allFieldNameNoId() + ", create_time, update_time"
}

var createChannelLiveShowApprovalTblSql = `CREATE Table IF NOT EXISTS channel_live_show_approval(
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id 自增',
    show_name varchar(255) NOT NULL DEFAULT '' COMMENT '节目名称', 
    show_start_time int(10) unsigned NOT NULL DEFAULT 0 COMMENT '节目开始时间',
    show_end_time int(10) unsigned NOT NULL DEFAULT 0 COMMENT '节目结束时间',
    show_cover_img varchar(255) NOT NULL DEFAULT '' COMMENT '节目封面图',
    show_desc_audio varchar(255) NOT NULL DEFAULT '' COMMENT '节目描述音频',
    show_desc_audio_duration int(10) unsigned NOT NULL DEFAULT 0 COMMENT '节目描述音频时长',
    voice_tag_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '语音标签id',
    content_tag_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '内容标签id',
    anchor_uid int(10) unsigned NOT NULL DEFAULT 0 COMMENT '主播uid',
    text_machine_audit_type int(10) unsigned NOT NULL DEFAULT 0 COMMENT '文字机审类型',
    img_machine_audit_type int(10) unsigned NOT NULL DEFAULT 0 COMMENT '图片机审类型',
    audio_machine_audit_type int(10) unsigned NOT NULL DEFAULT 0 COMMENT '音频机审类型',
    manual_audit_type int(10) unsigned NOT NULL DEFAULT 0 COMMENT '人审类型',
    approval_operator varchar(255) NOT NULL DEFAULT '' COMMENT '审批人',
    approval_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '审批时间',
    create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    INDEX idx_show_start_time_anchor_uid(show_start_time, anchor_uid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '频道直播节目审核表';`


func (s *Store) InsertChannelLiveShowApproval(ctx context.Context, show *ChannelLiveShowApproval) (uint32, error) {
	rs, err := s.db.ExecContext(ctx, "INSERT INTO channel_live_show_approval ("+show.allFieldNameNoId()+") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", show.allFieldNoId()...)
	if err != nil {
		log.ErrorWithCtx(ctx, "InsertChannelLiveShowApproval fail to insert. %+v, err:%v", show, err)
		return 0, err
	}
	id, err := rs.LastInsertId()
	if err != nil {
		log.ErrorWithCtx(ctx, "InsertChannelLiveShowApproval fail to get last insert id. %+v, err:%v", show, err)
		return 0, err
	}
	log.DebugWithCtx(ctx, "InsertChannelLiveShowApproval success, id: %d, show:%+v", id, show)
	return uint32(id), nil
}

func (s *Store) GetChannelLiveShowApprovalById(ctx context.Context, id uint32) (*ChannelLiveShowApproval, error) {
	show := &ChannelLiveShowApproval{}
	err := s.db.GetContext(ctx, show, "SELECT "+show.allFieldName()+" FROM channel_live_show_approval WHERE id = ?", id)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveShowApprovalById fail to select. id:%d, err:%v", id, err)
		return nil, err
	}
	log.DebugWithCtx(ctx, "GetChannelLiveShowApprovalById success, id:%d, show:%+v", id, show)
	return show, nil
}

const (
	ApprovalListSortTypeShowStartTime = 1
	ApprovalListSortTypeApprovalTime = 2
)

func (s *Store) PageMachineAuditDoneChannelLiveShowApproval(ctx context.Context, datetime, uid uint32, auditTypeList []uint32, pageNum, pageSize, sortType uint32) ([]*ChannelLiveShowApproval, error) {
	var shows []*ChannelLiveShowApproval
	queryByDateTime := ""
	if datetime > 0 {
		queryByDateTime = "show_start_time >= " + fmt.Sprint(datetime) + " AND show_start_time < " + fmt.Sprint(datetime+86400) + " AND "
	}

	sortSql := " ORDER BY show_start_time ASC LIMIT ?, ?"
	if sortType == ApprovalListSortTypeApprovalTime {
		sortSql = " ORDER BY approval_time DESC LIMIT ?, ?"
	}

	queryByUid := ""
	if uid > 0 {
		queryByUid = " anchor_uid = " + fmt.Sprint(uid) + " AND  "
	}

	queryByManualAuditType := ""
	if len(auditTypeList) > 0 {
		queryByManualAuditType += " AND manual_audit_type IN (" + genParamJoinStr(auditTypeList) + ") "
	}

	query := "SELECT " + new(ChannelLiveShowApproval).allFieldName() + " FROM channel_live_show_approval " +
		"WHERE "+ queryByDateTime + queryByUid +
		" text_machine_audit_type = "+fmt.Sprint(ChannelLiveShowApprovalAuditTypePass)+
		" AND img_machine_audit_type = "+fmt.Sprint(ChannelLiveShowApprovalAuditTypePass)+
		" AND audio_machine_audit_type = "+fmt.Sprint(ChannelLiveShowApprovalAuditTypePass)+
		queryByManualAuditType + sortSql

	err := s.db.SelectContext(ctx, &shows, query, (pageNum-1)*pageSize, pageSize)
	if err != nil {
		log.ErrorWithCtx(ctx, "PageMachineAuditDoneChannelLiveShowApproval fail to select. pageNum:%d, pageSize:%d, err:%v", pageNum, pageSize, err)
		return nil, err
	}
	log.DebugWithCtx(ctx, "PageMachineAuditDoneChannelLiveShowApproval success, pageNum:%d, pageSize:%d, shows:%+v", pageNum, pageSize, shows)
	return shows, nil
}

func (s *Store) CountMachineAuditDoneChannelLiveShowApproval(ctx context.Context, datetime, uid uint32, auditTypeList []uint32) (uint32, error) {
	queryByDateTime := ""
	if datetime > 0 {
		queryByDateTime = "show_start_time >= " + fmt.Sprint(datetime) + " AND show_start_time < " + fmt.Sprint(datetime+86400) + " AND "
	}
	queryByUid := ""
	if uid > 0 {
		queryByUid = " anchor_uid = " + fmt.Sprint(uid) + " AND "
	}
	queryByManualAuditType := ""
	if len(auditTypeList) > 0 {
		queryByManualAuditType += " AND manual_audit_type IN (" + genParamJoinStr(auditTypeList) + ")"
	}

	query := "SELECT COUNT(1) FROM channel_live_show_approval " +
			"WHERE "+ queryByDateTime + queryByUid +
		" text_machine_audit_type = "+fmt.Sprint(ChannelLiveShowApprovalAuditTypePass)+
		" AND img_machine_audit_type = "+fmt.Sprint(ChannelLiveShowApprovalAuditTypePass)+
		" AND audio_machine_audit_type = "+fmt.Sprint(ChannelLiveShowApprovalAuditTypePass)+
		queryByManualAuditType
	var count uint32
	err := s.db.GetContext(ctx, &count, query)
	if err != nil {
		log.ErrorWithCtx(ctx, "CountMachineAuditDoneChannelLiveShowApproval fail to select. err:%v", err)
		return 0, err
	}
	log.DebugWithCtx(ctx, "CountMachineAuditDoneChannelLiveShowApproval success, count:%d", count)
	return count, nil
}


func (s *Store) UpdateChannelLiveShowApprovalTagId(ctx context.Context, id, voiceTagId, contentTagId uint32) error {
	modifyList := []string{}
	argList := []interface{}{}
	if voiceTagId > 0 {
		modifyList = append(modifyList, "voice_tag_id = ?")
		argList = append(argList, voiceTagId)
	}

	if contentTagId > 0 {
		modifyList = append(modifyList, "content_tag_id = ?")
		argList = append(argList, contentTagId)
	}
	argList = append(argList, id)
	modifyStr := strings.Join(modifyList, ", ")

	_, err := s.db.ExecContext(ctx, "UPDATE channel_live_show_approval SET "+modifyStr+" WHERE id = ?", argList...)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateChannelLiveShowApprovalTagId fail to update. id:%d, voiceTagId:%d, contentTagId:%d, err:%v", id, voiceTagId, contentTagId, err)
		return err
	}
	log.DebugWithCtx(ctx, "UpdateChannelLiveShowApprovalTagId success, id:%d, voiceTagId:%d, contentTagId:%d", id, voiceTagId, contentTagId)
	return nil
}

func (s *Store) UpdateTextMachineAuditType(ctx context.Context, id, auditType uint32) error {
	_, err := s.db.ExecContext(ctx, "UPDATE channel_live_show_approval SET text_machine_audit_type = ? WHERE id = ?", auditType, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateTextMachineAuditType fail to update. id:%d, auditType:%d, err:%v", id, auditType, err)
		return err
	}
	log.DebugWithCtx(ctx, "UpdateTextMachineAuditType success, id:%d, auditType:%d", id, auditType)
	 return nil
}

func (s *Store) UpdateImgMachineAuditType(ctx context.Context, id, auditType uint32) error {
	_, err := s.db.ExecContext(ctx, "UPDATE channel_live_show_approval SET img_machine_audit_type = ? WHERE id = ?", auditType, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateImgMachineAuditType fail to update. id:%d, auditType:%d, err:%v", id, auditType, err)
		return err
	}
	log.DebugWithCtx(ctx, "UpdateImgMachineAuditType success, id:%d, auditType:%d", id, auditType)
	return nil
}

func (s *Store) UpdateAudioMachineAuditType(ctx context.Context, id, auditType uint32) error {
	_, err := s.db.ExecContext(ctx, "UPDATE channel_live_show_approval SET audio_machine_audit_type = ? WHERE id = ?", auditType, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAudioMachineAuditType fail to update. id:%d, auditType:%d, err:%v", id, auditType, err)
		return err
	}
	log.DebugWithCtx(ctx, "UpdateAudioMachineAuditType success, id:%d, auditType:%d", id, auditType)
	return nil
}

func (s *Store) UpdateManualAuditType(ctx context.Context, id, auditType uint32, auditOperator string) error {
	_, err := s.db.ExecContext(ctx, "UPDATE channel_live_show_approval SET manual_audit_type = ?, approval_operator = ?, approval_time = NOW() WHERE id = ?", auditType, auditOperator, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateManualAuditType fail to update. id:%d, auditType:%d, err:%v", id, auditType, err)
		return err
	}
	log.DebugWithCtx(ctx, "UpdateManualAuditType success, id:%d, auditType:%d", id, auditType)
	return nil
}

func (s *Store) ExistsPendingPassApproval(ctx context.Context, anchorUid, startTme uint32) (bool, error) {
	var count uint32
	sql := "SELECT COUNT(1) FROM channel_live_show_approval WHERE show_start_time = ? AND anchor_uid = ? " +
		"AND text_machine_audit_type != ? " +
		"AND img_machine_audit_type != ? " +
		"AND audio_machine_audit_type != ? " +
		"AND manual_audit_type != ?"
	err := s.db.GetContext(ctx, &count, sql, startTme,anchorUid,
		ChannelLiveShowApprovalAuditTypeReject,
		ChannelLiveShowApprovalAuditTypeReject,
		ChannelLiveShowApprovalAuditTypeReject,
		ChannelLiveShowApprovalAuditTypeReject )
	if err != nil {
		log.ErrorWithCtx(ctx, "ExistsPendingPassApproval, anchorUid: %d, startTime: %d, err: %v", anchorUid, startTme, err)
		return false, err
	}
	return count > 0, nil
}
