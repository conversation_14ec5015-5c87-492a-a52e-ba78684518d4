package store

import (
	"context"
	"golang.52tt.com/pkg/log"
	"time"
)

type ChannelLiveShowTag struct {
	ID uint32 `db:"id"`
	TagName string `db:"tag_name"`
	ParentId uint32 `db:"parent_id"`

	CreateTime time.Time `db:"create_time"`
}

func (clst *ChannelLiveShowTag) allFieldNoId() []interface{} {
	return []interface{}{clst.TagName, clst.ParentId}
}

func (clst *ChannelLiveShowTag) allFieldNoIdName() string {
	return "tag_name, parent_id, create_time"
}

func (clst *ChannelLiveShowTag) allFieldName() string {
	return "id, tag_name, parent_id, create_time"
}

func (clst *ChannelLiveShowTag) allFieldWithCreateTime() []interface{} {
	return append(clst.allFieldNoId(), time.Now())
}

var createChannelLiveShowTagTbl = `CREATE TABLE IF NOT EXISTS channel_live_show_tag (
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    tag_name varchar(255) NOT NULL DEFAULT '' COMMENT '标签名称',
    parent_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '父标签id',
    create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '节目单标签表';`

func (s *Store) InsertChannelLiveShowTag(ctx context.Context, tag *ChannelLiveShowTag) error {
	_, err := s.db.ExecContext(ctx, "INSERT INTO channel_live_show_tag ("+tag.allFieldNoIdName()+") VALUES (?, ?, ?)", tag.allFieldWithCreateTime()...)
	if err != nil {
		log.ErrorWithCtx(ctx, "InsertChannelLiveShowTag fail to insert. %+v, err:%v", tag, err)
		return err
	}
	log.DebugWithCtx(ctx, "InsertChannelLiveShowTag success, tag:%+v", tag)
	return nil
}

func (s *Store) DeleteChannelLiveShowTag(ctx context.Context, id uint32) error {
	_, err := s.db.ExecContext(ctx, "DELETE FROM channel_live_show_tag WHERE id = ?", id)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteChannelLiveShowTag fail to delete. id:%d, err:%v", id, err)
		return err
	}
	log.DebugWithCtx(ctx, "DeleteChannelLiveShowTag success, id:%d", id)
	return nil
}

func (s *Store) SelectAllChannelLiveShowTag(ctx context.Context) ([]*ChannelLiveShowTag, error) {
	tags := make([]*ChannelLiveShowTag, 0)
	err := s.db.SelectContext(ctx, &tags, "SELECT "+new(ChannelLiveShowTag).allFieldName()+" FROM channel_live_show_tag ORDER BY id ASC")
	if err != nil {
		log.ErrorWithCtx(ctx, "SelectAllChannelLiveShowTag fail to select. err:%v", err)
		return nil, err
	}
	log.DebugWithCtx(ctx, "SelectAllChannelLiveShowTag success, tags:%+v", tags)
	return tags, nil
}


