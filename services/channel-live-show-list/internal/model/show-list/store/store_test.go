package store

import (
    "context"
    "database/sql"
    "testing"
    "time"

    "github.com/DATA-DOG/go-sqlmock"
    "github.com/jmoiron/sqlx"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    x_mysql "gitlab.ttyuyin.com/tyr/x/middleware/mysql"
)

// 云测db
func getTestStore() *Store {
    db, err := mysqlConnect.NewClient(context.Background(), &mysqlConnect.MysqlConfig{
        Host:     "**********",
        Port:     3306,
        Database: "appsvr",
        UserName: "godman",
        Password: "thegodofman",
    })
    if err != nil {
        panic(err)
    }
    return &Store{
        db: db,
    }
}

// TestDBx 实现 mysql.DBx 接口用于测试
type TestDBx struct {
    *sqlx.DB
    mock sqlmock.Sqlmock
}

// 实现 mysql.DBx 接口的必要方法
func (t *TestDBx) Close() error {
    return t.DB.Close()
}

func (t *TestDBx) Beginx() (x_mysql.Txx, error) {
    return nil, nil
}

func (t *TestDBx) Begin() (x_mysql.Tx, error) {
    return nil, nil
}

func (t *TestDBx) ExecContext(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
    return t.DB.ExecContext(ctx, query, args...)
}

func (t *TestDBx) SelectContext(ctx context.Context, dest interface{}, query string, args ...interface{}) error {
    return t.DB.SelectContext(ctx, dest, query, args...)
}

func (t *TestDBx) GetContext(ctx context.Context, dest interface{}, query string, args ...interface{}) error {
    return t.DB.GetContext(ctx, dest, query, args...)
}

func (t *TestDBx) Queryx(query string, args ...interface{}) (x_mysql.Rowsx, error) {
    return t.DB.Queryx(query, args...)
}

func (t *TestDBx) QueryRowx(query string, args ...interface{}) x_mysql.Rowx {
    return t.DB.QueryRowx(query, args...)
}

func (t *TestDBx) QueryRowxContext(ctx context.Context, query string, args ...interface{}) x_mysql.Rowx {
    return t.DB.QueryRowxContext(ctx, query, args...)
}

func (t *TestDBx) QueryxContext(ctx context.Context, query string, args ...interface{}) (x_mysql.Rowsx, error) {
    return t.DB.QueryxContext(ctx, query, args...)
}

func (t *TestDBx) Query(query string, args ...interface{}) (x_mysql.Rows, error) {
    return t.DB.Query(query, args...)
}

func (t *TestDBx) QueryRowContext(ctx context.Context, query string, args ...interface{}) x_mysql.Row {
    return t.DB.QueryRowContext(ctx, query, args...)
}

func (t *TestDBx) QueryRow(query string, args ...interface{}) x_mysql.Row {
    return t.DB.QueryRow(query, args...)
}

func (t *TestDBx) QueryContext(ctx context.Context, query string, args ...interface{}) (x_mysql.Rows, error) {
    return t.DB.QueryContext(ctx, query, args...)
}

// TestTxx 实现 mysql.Txx 接口用于测试
type TestTxx struct {
    *sqlx.Tx
}

func (t *TestTxx) Commit() error {
    return t.Tx.Commit()
}

func (t *TestTxx) Rollback() error {
    return t.Tx.Rollback()
}

func (t *TestTxx) ExecContext(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
    return t.Tx.ExecContext(ctx, query, args...)
}

func (t *TestTxx) SelectContext(ctx context.Context, dest interface{}, query string, args ...interface{}) error {
    return t.Tx.SelectContext(ctx, dest, query, args...)
}

func (t *TestTxx) GetContext(ctx context.Context, dest interface{}, query string, args ...interface{}) error {
    return t.Tx.GetContext(ctx, dest, query, args...)
}

// setupTestStore 创建测试用的Store实例
func setupTestStore(t *testing.T) (*Store, sqlmock.Sqlmock, func()) {
    db, mock, err := sqlmock.New()
    require.NoError(t, err)

    sqlxDB := sqlx.NewDb(db, "mysql")
    testDBx := &TestDBx{DB: sqlxDB, mock: mock}
    store := &Store{db: testDBx}

    cleanup := func() {
        db.Close()
    }

    return store, mock, cleanup
}

func TestStore_InsertChannelLiveShow(t *testing.T) {
    tests := []struct {
        name     string
        show     *ChannelLiveShow
        mockFunc func(sqlmock.Sqlmock)
        wantErr  bool
    }{
        {
            name: "success - insert show",
            show: &ChannelLiveShow{
                ApprovalId:            1,
                ShowName:              "测试节目",
                ShowStartTime:         uint32(time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC).Unix()),
                ShowEndTime:           uint32(time.Date(2024, 1, 1, 11, 0, 0, 0, time.UTC).Unix()),
                ShowCoverImg:          "http://example.com/cover.jpg",
                ShowDescAudio:         "http://example.com/desc.mp3",
                ShowDescAudioDuration: 60,
                VoiceTagId:            10,
                ContentTagId:          20,
                AnchorUid:             12345,
                ChannelId:             1,
            },
            mockFunc: func(mock sqlmock.Sqlmock) {
                mock.ExpectExec("INSERT INTO channel_live_show_list").
                    WithArgs(1, "测试节目", sqlmock.AnyArg(), sqlmock.AnyArg(),
                        "http://example.com/cover.jpg", "http://example.com/desc.mp3",
                        60, 10, 20, 12345, 1).
                    WillReturnResult(sqlmock.NewResult(1, 1))
            },
            wantErr: false,
        },
        {
            name: "failure - database error",
            show: &ChannelLiveShow{
                ApprovalId: 1,
                ShowName:   "测试节目",
            },
            mockFunc: func(mock sqlmock.Sqlmock) {
                mock.ExpectExec("INSERT INTO channel_live_show_list").
                    WillReturnError(sql.ErrConnDone)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            store, mock, cleanup := setupTestStore(t)
            defer cleanup()

            tt.mockFunc(mock)

            err := store.InsertChannelLiveShow(context.Background(), tt.show)

            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
            }

            assert.NoError(t, mock.ExpectationsWereMet())
        })
    }
}

func TestStore_PageChannelLiveShowByStartTimeTag(t *testing.T) {
    tests := []struct {
        name         string
        startTime    uint32
        endTime      uint32
        voiceTagId   []uint32
        contentTagId []uint32
        offset       uint32
        limit        uint32
        mockFunc     func(sqlmock.Sqlmock)
        wantErr      bool
        wantCount    int
    }{
        {
            name:         "success - query with tags",
            startTime:    1704067200, // 2024-01-01 00:00:00
            endTime:      1704153600, // 2024-01-02 00:00:00
            voiceTagId:   []uint32{10, 11},
            contentTagId: []uint32{20, 21},
            offset:       0,
            limit:        10,
            mockFunc: func(mock sqlmock.Sqlmock) {
                rows := sqlmock.NewRows([]string{"approval_id", "show_name", "show_start_time", "show_end_time",
                    "show_cover_img", "show_desc_audio", "show_desc_audio_duration", "voice_tag_id", "content_tag_id", "anchor_uid"}).
                    AddRow(1, "测试节目1", 1704070800, 1704074400, "cover1.jpg", "desc1.mp3", 60, 10, 20, 12345).
                    AddRow(2, "测试节目2", 1704078000, 1704081600, "cover2.jpg", "desc2.mp3", 90, 11, 21, 12346)

                mock.ExpectQuery("SELECT .* FROM channel_live_show_list WHERE show_start_time >= \\? AND show_start_time < \\? AND voice_tag_id IN \\(10,11\\) AND content_tag_id IN \\(20,21\\) order by show_start_time LIMIT \\?, \\?").
                    WithArgs(1704067200, 1704153600, 0, 10).
                    WillReturnRows(rows)
            },
            wantErr:   false,
            wantCount: 2,
        },
        {
            name:         "success - query without tags",
            startTime:    1704067200,
            endTime:      1704153600,
            voiceTagId:   []uint32{},
            contentTagId: []uint32{},
            offset:       0,
            limit:        10,
            mockFunc: func(mock sqlmock.Sqlmock) {
                rows := sqlmock.NewRows([]string{"approval_id", "show_name", "show_start_time", "show_end_time",
                    "show_cover_img", "show_desc_audio", "show_desc_audio_duration", "voice_tag_id", "content_tag_id", "anchor_uid"}).
                    AddRow(1, "测试节目1", 1704070800, 1704074400, "cover1.jpg", "desc1.mp3", 60, 10, 20, 12345)

                mock.ExpectQuery("SELECT .* FROM channel_live_show_list WHERE show_start_time >= \\? AND show_start_time < \\? order by show_start_time LIMIT \\?, \\?").
                    WithArgs(1704067200, 1704153600, 0, 10).
                    WillReturnRows(rows)
            },
            wantErr:   false,
            wantCount: 1,
        },
        {
            name:      "failure - database error",
            startTime: 1704067200,
            endTime:   1704153600,
            mockFunc: func(mock sqlmock.Sqlmock) {
                mock.ExpectQuery("SELECT .* FROM channel_live_show_list").
                    WillReturnError(sql.ErrConnDone)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            store, mock, cleanup := setupTestStore(t)
            defer cleanup()

            tt.mockFunc(mock)

            shows, err := store.PageChannelLiveShowByStartTimeTag(context.Background(),
                tt.startTime, tt.endTime, tt.voiceTagId, tt.contentTagId, tt.offset, tt.limit)

            if tt.wantErr {
                assert.Error(t, err)
                assert.Nil(t, shows)
            } else {
                assert.NoError(t, err)
                assert.Len(t, shows, tt.wantCount)
            }

            assert.NoError(t, mock.ExpectationsWereMet())
        })
    }
}

func TestStore_GetChannelLiveShowByAnchorUid(t *testing.T) {
    tests := []struct {
        name      string
        anchorUid uint32
        mockFunc  func(sqlmock.Sqlmock)
        wantErr   bool
        wantCount int
    }{
        {
            name:      "success - get shows by anchor uid",
            anchorUid: 12345,
            mockFunc: func(mock sqlmock.Sqlmock) {
                rows := sqlmock.NewRows([]string{"approval_id", "show_name", "show_start_time", "show_end_time",
                    "show_cover_img", "show_desc_audio", "show_desc_audio_duration", "voice_tag_id", "content_tag_id", "anchor_uid"}).
                    AddRow(1, "测试节目1", time.Now().Add(time.Hour).Unix(), time.Now().Add(2*time.Hour).Unix(),
                        "cover1.jpg", "desc1.mp3", 60, 10, 20, 12345).
                    AddRow(2, "测试节目2", time.Now().Add(-time.Hour).Unix(), time.Now().Add(time.Hour).Unix(),
                        "cover2.jpg", "desc2.mp3", 90, 11, 21, 12345)

                mock.ExpectQuery("SELECT .* FROM channel_live_show_list WHERE \\(show_start_time >= \\? OR show_end_time >= \\?\\) AND anchor_uid = \\?").
                    WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 12345).
                    WillReturnRows(rows)
            },
            wantErr:   false,
            wantCount: 2,
        },
        {
            name:      "success - no shows found",
            anchorUid: 99999,
            mockFunc: func(mock sqlmock.Sqlmock) {
                rows := sqlmock.NewRows([]string{"approval_id", "show_name", "show_start_time", "show_end_time",
                    "show_cover_img", "show_desc_audio", "show_desc_audio_duration", "voice_tag_id", "content_tag_id", "anchor_uid"})

                mock.ExpectQuery("SELECT .* FROM channel_live_show_list WHERE \\(show_start_time >= \\? OR show_end_time >= \\?\\) AND anchor_uid = \\?").
                    WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 99999).
                    WillReturnRows(rows)
            },
            wantErr:   false,
            wantCount: 0,
        },
        {
            name:      "failure - database error",
            anchorUid: 12345,
            mockFunc: func(mock sqlmock.Sqlmock) {
                mock.ExpectQuery("SELECT .* FROM channel_live_show_list").
                    WillReturnError(sql.ErrConnDone)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            store, mock, cleanup := setupTestStore(t)
            defer cleanup()

            tt.mockFunc(mock)

            shows, err := store.GetChannelLiveShowByAnchorUid(context.Background(), tt.anchorUid)

            if tt.wantErr {
                assert.Error(t, err)
                assert.Nil(t, shows)
            } else {
                assert.NoError(t, err)
                assert.Len(t, shows, tt.wantCount)
                if tt.wantCount > 0 {
                    for _, show := range shows {
                        assert.Equal(t, tt.anchorUid, show.AnchorUid)
                    }
                }
            }

            assert.NoError(t, mock.ExpectationsWereMet())
        })
    }
}

func TestGenParamJoinStr(t *testing.T) {
    tests := []struct {
        name   string
        input  []uint32
        expect string
    }{
        {
            name:   "empty slice",
            input:  []uint32{},
            expect: "",
        },
        {
            name:   "single element",
            input:  []uint32{1},
            expect: "1",
        },
        {
            name:   "multiple elements",
            input:  []uint32{1, 2, 3, 4, 5},
            expect: "1,2,3,4,5",
        },
        {
            name:   "large numbers",
            input:  []uint32{4294967295, 1000000, 999},
            expect: "4294967295,1000000,999",
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := genParamJoinStr(tt.input)
            assert.Equal(t, tt.expect, result)
        })
    }
}

func TestChannelLiveShow_Methods(t *testing.T) {
    show := &ChannelLiveShow{
        ApprovalId:            1,
        ShowName:              "测试节目",
        ShowStartTime:         1704070800,
        ShowEndTime:           1704074400,
        ShowCoverImg:          "cover.jpg",
        ShowDescAudio:         "desc.mp3",
        ShowDescAudioDuration: 60,
        VoiceTagId:            10,
        ContentTagId:          20,
        AnchorUid:             12345,
        ChannelId:             1,
    }

    t.Run("allFiledNoId", func(t *testing.T) {
        fields := show.allFiledNoId()
        expected := []interface{}{uint32(1), "测试节目", uint32(1704070800), uint32(1704074400),
            "cover.jpg", "desc.mp3", uint32(60), uint32(10), uint32(20), uint32(12345), uint32(1)}
        assert.Equal(t, expected, fields)
    })

    t.Run("allFiledName", func(t *testing.T) {
        fieldNames := show.allFiledName()
        expected := "id, approval_id, show_name, show_start_time, show_end_time, show_cover_img, show_desc_audio, show_desc_audio_duration, voice_tag_id, content_tag_id, anchor_uid, channel_id"
        assert.Equal(t, expected, fieldNames)
    })

}

// TestStore_CreateChannelLiveShowListTable 测试创建节目表
func TestStore_CreateChannelLiveShowListTable(t *testing.T) {
    tests := []struct {
        name     string
        mockFunc func(sqlmock.Sqlmock)
        wantErr  bool
    }{
        {
            name: "success - create table",
            mockFunc: func(mock sqlmock.Sqlmock) {
                mock.ExpectExec("CREATE TABLE IF NOT EXISTS channel_live_show_list").
                    WillReturnResult(sqlmock.NewResult(0, 0))
            },
            wantErr: false,
        },
        {
            name: "failure - database error",
            mockFunc: func(mock sqlmock.Sqlmock) {
                mock.ExpectExec("CREATE TABLE IF NOT EXISTS channel_live_show_list").
                    WillReturnError(sql.ErrConnDone)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            store, mock, cleanup := setupTestStore(t)
            defer cleanup()

            tt.mockFunc(mock)

            err := store.CreateChannelLiveShowListTable(context.Background())

            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
            }

            assert.NoError(t, mock.ExpectationsWereMet())
        })
    }
}

// TestStore_GetChannelLiveShowByShowId 测试根据ShowId获取节目
func TestStore_GetChannelLiveShowByShowId(t *testing.T) {
    tests := []struct {
        name     string
        showId   uint32
        mockFunc func(sqlmock.Sqlmock)
        wantErr  bool
        wantShow *ChannelLiveShow
    }{
        {
            name:   "success - get show by id",
            showId: 1,
            mockFunc: func(mock sqlmock.Sqlmock) {
                rows := sqlmock.NewRows([]string{"id", "approval_id", "show_name", "show_start_time", "show_end_time",
                    "show_cover_img", "show_desc_audio", "show_desc_audio_duration", "voice_tag_id", "content_tag_id", "anchor_uid"}).
                    AddRow(1, 1, "测试节目", 1704070800, 1704074400, "cover.jpg", "desc.mp3", 60, 10, 20, 12345)

                mock.ExpectQuery("SELECT .* FROM channel_live_show_list WHERE id = \\?").
                    WithArgs(1).
                    WillReturnRows(rows)
            },
            wantErr: false,
            wantShow: &ChannelLiveShow{
                ID:                    1,
                ApprovalId:            1,
                ShowName:              "测试节目",
                ShowStartTime:         1704070800,
                ShowEndTime:           1704074400,
                ShowCoverImg:          "cover.jpg",
                ShowDescAudio:         "desc.mp3",
                ShowDescAudioDuration: 60,
                VoiceTagId:            10,
                ContentTagId:          20,
                AnchorUid:             12345,
            },
        },
        {
            name:   "success - no rows found",
            showId: 999,
            mockFunc: func(mock sqlmock.Sqlmock) {
                mock.ExpectQuery("SELECT .* FROM channel_live_show_list WHERE id = \\?").
                    WithArgs(999).
                    WillReturnError(sql.ErrNoRows)
            },
            wantErr:  false,
            wantShow: &ChannelLiveShow{}, // 空对象
        },
        {
            name:   "failure - database error",
            showId: 1,
            mockFunc: func(mock sqlmock.Sqlmock) {
                mock.ExpectQuery("SELECT .* FROM channel_live_show_list WHERE id = \\?").
                    WithArgs(1).
                    WillReturnError(sql.ErrConnDone)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            store, mock, cleanup := setupTestStore(t)
            defer cleanup()

            tt.mockFunc(mock)

            show, err := store.GetChannelLiveShowByShowId(context.Background(), tt.showId)

            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
                if tt.wantShow != nil {
                    assert.Equal(t, tt.wantShow.ID, show.ID)
                    assert.Equal(t, tt.wantShow.ShowName, show.ShowName)
                    assert.Equal(t, tt.wantShow.AnchorUid, show.AnchorUid)
                }
            }

            assert.NoError(t, mock.ExpectationsWereMet())
        })
    }
}
