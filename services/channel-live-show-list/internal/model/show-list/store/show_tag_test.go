package store

import (
    "context"
    "database/sql"
    "testing"
    "time"

    "github.com/DATA-DOG/go-sqlmock"
    "github.com/stretchr/testify/assert"
    "golang.52tt.com/pkg/log"
)

func Test_PageChannelLiveShowByStartTimeTag2(t *testing.T) {
    db := getTestStore()
    now := time.Now()
    // 0 点
    timeStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
    ret, err := db.PageChannelLiveShowByStartTimeTag(context.Background(), uint32(timeStart.Unix()), uint32(timeStart.Add(time.Hour*24).Unix()), []uint32{}, []uint32{23}, 0, 10)
    if err != nil {
        log.Errorf("PageChannelLiveShowByStartTimeTag err: %v", err)
        return
    }
    for _, v := range ret {
        log.Infof("PageChannelLiveShowByStartTimeTag ret: %+v", v)
    }

}

// TestStore_InsertChannelLiveShowTag 测试插入标签
func TestStore_InsertChannelLiveShowTag(t *testing.T) {
    tests := []struct {
        name     string
        tag      *ChannelLiveShowTag
        mockFunc func(sqlmock.Sqlmock)
        wantErr  bool
    }{
        {
            name: "success - insert tag",
            tag: &ChannelLiveShowTag{
                TagName:  "测试标签",
                ParentId: 0,
            },
            mockFunc: func(mock sqlmock.Sqlmock) {
                mock.ExpectExec("INSERT INTO channel_live_show_tag").
                    WithArgs("测试标签", uint32(0), sqlmock.AnyArg()).
                    WillReturnResult(sqlmock.NewResult(1, 1))
            },
            wantErr: false,
        },
        {
            name: "success - insert child tag",
            tag: &ChannelLiveShowTag{
                TagName:  "子标签",
                ParentId: 1,
            },
            mockFunc: func(mock sqlmock.Sqlmock) {
                mock.ExpectExec("INSERT INTO channel_live_show_tag").
                    WithArgs("子标签", uint32(1), sqlmock.AnyArg()).
                    WillReturnResult(sqlmock.NewResult(2, 1))
            },
            wantErr: false,
        },
        {
            name: "failure - database error",
            tag: &ChannelLiveShowTag{
                TagName:  "测试标签",
                ParentId: 0,
            },
            mockFunc: func(mock sqlmock.Sqlmock) {
                mock.ExpectExec("INSERT INTO channel_live_show_tag").
                    WillReturnError(sql.ErrConnDone)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            store, mock, cleanup := setupTestStore(t)
            defer cleanup()

            tt.mockFunc(mock)

            err := store.InsertChannelLiveShowTag(context.Background(), tt.tag)

            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
            }

            assert.NoError(t, mock.ExpectationsWereMet())
        })
    }
}

// TestStore_DeleteChannelLiveShowTag 测试删除标签
func TestStore_DeleteChannelLiveShowTag(t *testing.T) {
    tests := []struct {
        name     string
        id       uint32
        mockFunc func(sqlmock.Sqlmock)
        wantErr  bool
    }{
        {
            name: "success - delete tag",
            id:   1,
            mockFunc: func(mock sqlmock.Sqlmock) {
                mock.ExpectExec("DELETE FROM channel_live_show_tag WHERE id = \\?").
                    WithArgs(1).
                    WillReturnResult(sqlmock.NewResult(0, 1))
            },
            wantErr: false,
        },
        {
            name: "success - delete non-existent tag",
            id:   999,
            mockFunc: func(mock sqlmock.Sqlmock) {
                mock.ExpectExec("DELETE FROM channel_live_show_tag WHERE id = \\?").
                    WithArgs(999).
                    WillReturnResult(sqlmock.NewResult(0, 0))
            },
            wantErr: false,
        },
        {
            name: "failure - database error",
            id:   1,
            mockFunc: func(mock sqlmock.Sqlmock) {
                mock.ExpectExec("DELETE FROM channel_live_show_tag WHERE id = \\?").
                    WithArgs(1).
                    WillReturnError(sql.ErrConnDone)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            store, mock, cleanup := setupTestStore(t)
            defer cleanup()

            tt.mockFunc(mock)

            err := store.DeleteChannelLiveShowTag(context.Background(), tt.id)

            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
            }

            assert.NoError(t, mock.ExpectationsWereMet())
        })
    }
}

// TestStore_SelectAllChannelLiveShowTag 测试查询所有标签
func TestStore_SelectAllChannelLiveShowTag(t *testing.T) {
    tests := []struct {
        name      string
        mockFunc  func(sqlmock.Sqlmock)
        wantErr   bool
        wantCount int
        wantTags  []*ChannelLiveShowTag
    }{
        {
            name: "success - get all tags",
            mockFunc: func(mock sqlmock.Sqlmock) {
                rows := sqlmock.NewRows([]string{"id", "tag_name", "parent_id", "create_time"}).
                    AddRow(1, "声色标签", 0, time.Now()).
                    AddRow(2, "内容分类", 0, time.Now()).
                    AddRow(10, "男声色", 1, time.Now()).
                    AddRow(20, "虚拟主播", 2, time.Now())

                mock.ExpectQuery("SELECT .* FROM channel_live_show_tag ORDER BY id ASC").
                    WillReturnRows(rows)
            },
            wantErr:   false,
            wantCount: 4,
            wantTags: []*ChannelLiveShowTag{
                {ID: 1, TagName: "声色标签", ParentId: 0},
                {ID: 2, TagName: "内容分类", ParentId: 0},
                {ID: 10, TagName: "男声色", ParentId: 1},
                {ID: 20, TagName: "虚拟主播", ParentId: 2},
            },
        },
        {
            name: "success - no tags found",
            mockFunc: func(mock sqlmock.Sqlmock) {
                rows := sqlmock.NewRows([]string{"id", "tag_name", "parent_id", "create_time"})

                mock.ExpectQuery("SELECT .* FROM channel_live_show_tag ORDER BY id ASC").
                    WillReturnRows(rows)
            },
            wantErr:   false,
            wantCount: 0,
            wantTags:  []*ChannelLiveShowTag{},
        },
        {
            name: "failure - database error",
            mockFunc: func(mock sqlmock.Sqlmock) {
                mock.ExpectQuery("SELECT .* FROM channel_live_show_tag ORDER BY id ASC").
                    WillReturnError(sql.ErrConnDone)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            store, mock, cleanup := setupTestStore(t)
            defer cleanup()

            tt.mockFunc(mock)

            tags, err := store.SelectAllChannelLiveShowTag(context.Background())

            if tt.wantErr {
                assert.Error(t, err)
                assert.Nil(t, tags)
            } else {
                assert.NoError(t, err)
                assert.Len(t, tags, tt.wantCount)

                if tt.wantCount > 0 {
                    for i, expectedTag := range tt.wantTags {
                        assert.Equal(t, expectedTag.ID, tags[i].ID)
                        assert.Equal(t, expectedTag.TagName, tags[i].TagName)
                        assert.Equal(t, expectedTag.ParentId, tags[i].ParentId)
                    }
                }
            }

            assert.NoError(t, mock.ExpectationsWereMet())
        })
    }
}

// TestChannelLiveShowTag_Methods 测试标签实体方法
func TestChannelLiveShowTag_Methods(t *testing.T) {
    tag := &ChannelLiveShowTag{
        ID:       1,
        TagName:  "测试标签",
        ParentId: 0,
    }

    t.Run("allFieldNoId", func(t *testing.T) {
        fields := tag.allFieldNoId()
        expected := []interface{}{"测试标签", uint32(0)}
        assert.Equal(t, expected, fields)
        assert.Len(t, fields, 2)
    })

    t.Run("allFieldWithCreateTime", func(t *testing.T) {
        fieldsWithTime := tag.allFieldWithCreateTime()
        assert.Len(t, fieldsWithTime, 3) // 2个原字段 + 1个时间字段

        // 验证前2个字段与allField()相同
        originalFields := tag.allFieldNoId()
        for i, field := range originalFields {
            assert.Equal(t, field, fieldsWithTime[i])
        }

        // 验证最后一个字段是时间类型
        lastField := fieldsWithTime[len(fieldsWithTime)-1]
        _, ok := lastField.(time.Time)
        assert.True(t, ok, "Last field should be time.Time")
    })
}

// TestChannelLiveShowTag_EdgeCases 测试标签边界条件
func TestChannelLiveShowTag_EdgeCases(t *testing.T) {
    t.Run("empty tag name", func(t *testing.T) {
        tag := &ChannelLiveShowTag{
            TagName:  "",
            ParentId: 0,
        }
        fields := tag.allFieldNoId()
        assert.Equal(t, "", fields[0])
    })

    t.Run("max parent id", func(t *testing.T) {
        tag := &ChannelLiveShowTag{
            TagName:  "测试",
            ParentId: 4294967295, // max uint32
        }
        fields := tag.allFieldNoId()
        assert.Equal(t, uint32(4294967295), fields[1])
    })

    t.Run("unicode tag name", func(t *testing.T) {
        tag := &ChannelLiveShowTag{
            TagName:  "测试标签🎵",
            ParentId: 1,
        }
        fields := tag.allFieldNoId()
        assert.Equal(t, "测试标签🎵", fields[0])
    })
}
