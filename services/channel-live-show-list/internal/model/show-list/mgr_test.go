package show_score

import (
    "context"
    "errors"
    "testing"

    "golang.52tt.com/protocol/app"
    "golang.52tt.com/services/channel-live-show-list/internal/model/show-list/store"

    "github.com/golang/mock/gomock"
    "github.com/stretchr/testify/assert"

    pb "golang.52tt.com/protocol/services/channel-live-show-list"
    dyConfMocks "golang.52tt.com/services/channel-live-show-list/internal/conf/mocks"
    modelMocks "golang.52tt.com/services/channel-live-show-list/internal/model/mocks"
    dataLayerMocks "golang.52tt.com/services/channel-live-show-list/internal/model/show-list/mocks"
)

// MockStore 创建store的mock
type MockStore struct {
    ctrl *gomock.Controller
    mock *dataLayerMocks.MockIStore
}

// MockCache 创建cache的mock
type MockCache struct {
    ctrl *gomock.Controller
    mock *dataLayerMocks.MockICache
}

// MockBusinessConf 创建业务配置的mock
type MockBusinessConf struct {
    ctrl *gomock.Controller
    mock *dyConfMocks.MockIBusinessConfManager
}

// MockACLayer 创建防腐层的mock
type MockACLayer struct {
    ctrl *gomock.Controller
    mock *modelMocks.MockIACLayer
}

// TestSetup 测试设置
type TestSetup struct {
    mgr       *ShowListMgr
    mockStore *MockStore
    mockCache *MockCache
    mockBC    *MockBusinessConf
    mockAC    *MockACLayer
}

func setupTest(t *testing.T) *TestSetup {
    ctrl := gomock.NewController(t)

    mockStore := &MockStore{
        ctrl: ctrl,
        mock: dataLayerMocks.NewMockIStore(ctrl),
    }
    mockCache := &MockCache{
        ctrl: ctrl,
        mock: dataLayerMocks.NewMockICache(ctrl),
    }
    mockBC := &MockBusinessConf{
        ctrl: ctrl,
        mock: dyConfMocks.NewMockIBusinessConfManager(ctrl),
    }
    mockAC := &MockACLayer{
        ctrl: ctrl,
        mock: modelMocks.NewMockIACLayer(ctrl),
    }

    mgr := &ShowListMgr{
        store:      mockStore.mock,
        cache:      mockCache.mock,
        bc:         mockBC.mock,
        acLayerMgr: mockAC.mock,
        shutDown:   make(chan struct{}),
    }

    return &TestSetup{
        mgr:       mgr,
        mockStore: mockStore,
        mockCache: mockCache,
        mockBC:    mockBC,
        mockAC:    mockAC,
    }
}

func (ts *TestSetup) tearDown() {
    ts.mockStore.ctrl.Finish()
    ts.mockCache.ctrl.Finish()
    ts.mockBC.ctrl.Finish()
    ts.mockAC.ctrl.Finish()
}

func TestShowListMgr_AddChannelLiveShowTag(t *testing.T) {
    tests := []struct {
        name     string
        id       uint32
        tagName  string
        parentId uint32
        mockFunc func(*TestSetup)
        wantErr  bool
    }{
        {
            name:     "success - add tag",
            id:       1,
            tagName:  "测试标签",
            parentId: 0,
            mockFunc: func(ts *TestSetup) {
                ts.mockStore.mock.EXPECT().
                    InsertChannelLiveShowTag(gomock.Any(), gomock.Any()).
                    DoAndReturn(func(ctx context.Context, tag *store.ChannelLiveShowTag) error {
                        assert.Equal(t, uint32(1), tag.ID)
                        assert.Equal(t, "测试标签", tag.TagName)
                        assert.Equal(t, uint32(0), tag.ParentId)
                        return nil
                    }).
                    Times(1)
            },
            wantErr: false,
        },
        {
            name:     "failure - store error",
            id:       1,
            tagName:  "测试标签",
            parentId: 0,
            mockFunc: func(ts *TestSetup) {
                ts.mockStore.mock.EXPECT().
                    InsertChannelLiveShowTag(gomock.Any(), gomock.Any()).
                    Return(errors.New("database error")).
                    Times(1)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            ts := setupTest(t)
            defer ts.tearDown()

            tt.mockFunc(ts)

            err := ts.mgr.AddChannelLiveShowTag(context.Background(), tt.id, tt.tagName, tt.parentId)

            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
            }
        })
    }
}

func TestShowListMgr_DeleteChannelLiveShowTag(t *testing.T) {
    tests := []struct {
        name     string
        id       uint32
        mockFunc func(*TestSetup)
        wantErr  bool
    }{
        {
            name: "success - delete tag",
            id:   1,
            mockFunc: func(ts *TestSetup) {
                ts.mockStore.mock.EXPECT().
                    DeleteChannelLiveShowTag(gomock.Any(), uint32(1)).
                    Return(nil).
                    Times(1)
            },
            wantErr: false,
        },
        {
            name: "failure - store error",
            id:   1,
            mockFunc: func(ts *TestSetup) {
                ts.mockStore.mock.EXPECT().
                    DeleteChannelLiveShowTag(gomock.Any(), uint32(1)).
                    Return(errors.New("database error")).
                    Times(1)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            ts := setupTest(t)
            defer ts.tearDown()

            tt.mockFunc(ts)

            err := ts.mgr.DeleteChannelLiveShowTag(context.Background(), tt.id)

            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
            }
        })
    }
}

func TestShowListMgr_GetChannelLiveTagTree(t *testing.T) {
    tests := []struct {
        name     string
        mockFunc func(*TestSetup)
        wantErr  bool
        validate func(*pb.TagNode, map[uint32]*pb.TagNode)
    }{
        {
            name: "success - build tag tree",
            mockFunc: func(ts *TestSetup) {
                tagList := []*store.ChannelLiveShowTag{
                    {ID: 1, TagName: "声色标签", ParentId: 0},
                    {ID: 2, TagName: "内容分类", ParentId: 0},
                    {ID: 10, TagName: "男声色", ParentId: 1},
                    {ID: 11, TagName: "女声色", ParentId: 1},
                    {ID: 100, TagName: "正太音", ParentId: 10},
                }
                ts.mockStore.mock.EXPECT().
                    SelectAllChannelLiveShowTag(gomock.Any()).
                    Return(tagList, nil).
                    Times(1)
            },
            wantErr: false,
            validate: func(root *pb.TagNode, nodeMap map[uint32]*pb.TagNode) {
                assert.NotNil(t, root)
                assert.Equal(t, 2, len(root.ChildList))
                assert.Contains(t, nodeMap, uint32(1))
                assert.Contains(t, nodeMap, uint32(2))
                assert.Contains(t, nodeMap, uint32(10))
                assert.Contains(t, nodeMap, uint32(11))
                assert.Contains(t, nodeMap, uint32(100))

                // 验证树结构
                voiceTag := nodeMap[1]
                assert.Equal(t, "声色标签", voiceTag.TagName)
                assert.Equal(t, 2, len(voiceTag.ChildList))

                maleVoice := nodeMap[10]
                assert.Equal(t, "男声色", maleVoice.TagName)
                assert.Equal(t, 1, len(maleVoice.ChildList))
            },
        },
        {
            name: "failure - store error",
            mockFunc: func(ts *TestSetup) {
                ts.mockStore.mock.EXPECT().
                    SelectAllChannelLiveShowTag(gomock.Any()).
                    Return(nil, errors.New("database error")).
                    Times(1)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            ts := setupTest(t)
            defer ts.tearDown()

            tt.mockFunc(ts)

            root, nodeMap, err := ts.mgr.GetChannelLiveTagTree(context.Background())

            if tt.wantErr {
                assert.Error(t, err)
                assert.Nil(t, root)
                assert.Nil(t, nodeMap)
            } else {
                assert.NoError(t, err)
                if tt.validate != nil {
                    tt.validate(root, nodeMap)
                }
            }
        })
    }
}

func TestShowListMgr_GetRelatedTagNodeId(t *testing.T) {
    tests := []struct {
        name     string
        idList   []uint32
        mockFunc func(*TestSetup)
        want     []uint32
        wantErr  bool
    }{
        {
            name:   "success - get leaf nodes",
            idList: []uint32{1, 2},
            mockFunc: func(ts *TestSetup) {
                tagList := []*store.ChannelLiveShowTag{
                    {ID: 1, TagName: "声色标签", ParentId: 0},
                    {ID: 2, TagName: "内容分类", ParentId: 0},
                    {ID: 10, TagName: "男声色", ParentId: 1},
                    {ID: 100, TagName: "正太音", ParentId: 10},
                    {ID: 200, TagName: "虚拟主播", ParentId: 2},
                }
                ts.mockStore.mock.EXPECT().
                    SelectAllChannelLiveShowTag(gomock.Any()).
                    Return(tagList, nil).
                    Times(1)
            },
            want:    []uint32{100, 200},
            wantErr: false,
        },
        {
            name:   "success - direct leaf nodes",
            idList: []uint32{100, 200},
            mockFunc: func(ts *TestSetup) {
                tagList := []*store.ChannelLiveShowTag{
                    {ID: 1, TagName: "声色标签", ParentId: 0},
                    {ID: 100, TagName: "正太音", ParentId: 1},
                    {ID: 200, TagName: "虚拟主播", ParentId: 1},
                }
                ts.mockStore.mock.EXPECT().
                    SelectAllChannelLiveShowTag(gomock.Any()).
                    Return(tagList, nil).
                    Times(1)
            },
            want:    []uint32{100, 200},
            wantErr: false,
        },
        {
            name:   "failure - store error",
            idList: []uint32{1},
            mockFunc: func(ts *TestSetup) {
                ts.mockStore.mock.EXPECT().
                    SelectAllChannelLiveShowTag(gomock.Any()).
                    Return(nil, errors.New("database error")).
                    Times(1)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            ts := setupTest(t)
            defer ts.tearDown()

            tt.mockFunc(ts)

            result, err := ts.mgr.GetRelatedTagNodeId(context.Background(), tt.idList)

            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
                assert.ElementsMatch(t, tt.want, result)
            }
        })
    }
}

func TestShowListMgr_GetShowTag(t *testing.T) {
    tests := []struct {
        name     string
        mockFunc func(*TestSetup)
        wantErr  bool
        validate func(*pb.GetShowTagResponse)
    }{
        {
            name: "success - get show tags",
            mockFunc: func(ts *TestSetup) {
                tagList := []*store.ChannelLiveShowTag{
                    {ID: 1, TagName: "声色标签", ParentId: 0},
                    {ID: 2, TagName: "内容分类", ParentId: 0},
                    {ID: 10, TagName: "男声色", ParentId: 1},
                    {ID: 11, TagName: "女声色", ParentId: 1},
                    {ID: 20, TagName: "虚拟主播", ParentId: 2},
                    {ID: 21, TagName: "情感类", ParentId: 2},
                }
                ts.mockStore.mock.EXPECT().
                    SelectAllChannelLiveShowTag(gomock.Any()).
                    Return(tagList, nil).
                    Times(1)
            },
            wantErr: false,
            validate: func(resp *pb.GetShowTagResponse) {
                assert.NotNil(t, resp)
                assert.Equal(t, 2, len(resp.VoiceTagList))
                assert.Equal(t, 2, len(resp.ContentTagList))

                // 验证声色标签
                assert.Equal(t, "男声色", resp.VoiceTagList[0].TagName)
                assert.Equal(t, "女声色", resp.VoiceTagList[1].TagName)

                // 验证内容标签
                assert.Equal(t, "虚拟主播", resp.ContentTagList[0].TagName)
                assert.Equal(t, "情感类", resp.ContentTagList[1].TagName)
            },
        },
        {
            name: "failure - store error",
            mockFunc: func(ts *TestSetup) {
                ts.mockStore.mock.EXPECT().
                    SelectAllChannelLiveShowTag(gomock.Any()).
                    Return(nil, errors.New("database error")).
                    Times(1)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            ts := setupTest(t)
            defer ts.tearDown()

            tt.mockFunc(ts)

            resp, err := ts.mgr.GetShowTag(context.Background())

            if tt.wantErr {
                assert.Error(t, err)
                assert.Nil(t, resp)
            } else {
                assert.NoError(t, err)
                if tt.validate != nil {
                    tt.validate(resp)
                }
            }
        })
    }
}

//func TestShowListMgr_HandleShowApproval(t *testing.T) {
//	tests := []struct {
//		name     string
//		req      *pb.HandleShowApprovalRequest
//		mockFunc func(*TestSetup)
//		wantErr  bool
//		validate func(*pb.HandelShowApprovalResponse)
//	}{
//		{
//			name: "success - approve show",
//			req: &pb.HandleShowApprovalRequest{
//				ShowApprovalId: 1,
//				IsPass:         true,
//				Operator:       "admin",
//			},
//			mockFunc: func(ts *TestSetup) {
//				approval := &store.ChannelLiveShowApproval{
//					ID:                      1,
//					ImgTextMachineAuditType: store.ChannelLiveShowApprovalAuditTypePass,
//					AudioMachineAuditType:   store.ChannelLiveShowApprovalAuditTypePass,
//				}
//				ts.mockStore.mock.EXPECT().
//					GetChannelLiveShowApprovalById(gomock.Any(), uint32(1)).
//					Return(approval, nil).
//					Times(1)
//
//				ts.mockStore.mock.EXPECT().
//					UpdateManualAuditType(gomock.Any(), uint32(1), uint32(store.ChannelLiveShowApprovalAuditTypePass), "admin").
//					Return(nil).
//					Times(1)
//			},
//			wantErr: false,
//			validate: func(resp *pb.HandelShowApprovalResponse) {
//				assert.NotNil(t, resp)
//			},
//		},
//		{
//			name: "success - reject show",
//			req: &pb.HandleShowApprovalRequest{
//				ShowApprovalId: 1,
//				IsPass:         false,
//				Operator:       "admin",
//			},
//			mockFunc: func(ts *TestSetup) {
//				approval := &store.ChannelLiveShowApproval{
//					ID:                      1,
//					ImgTextMachineAuditType: store.ChannelLiveShowApprovalAuditTypePass,
//					AudioMachineAuditType:   store.ChannelLiveShowApprovalAuditTypePass,
//				}
//				ts.mockStore.mock.EXPECT().
//					GetChannelLiveShowApprovalById(gomock.Any(), uint32(1)).
//					Return(approval, nil).
//					Times(1)
//
//				ts.mockStore.mock.EXPECT().
//					UpdateManualAuditType(gomock.Any(), uint32(1), uint32(store.ChannelLiveShowApprovalAuditTypeReject), "admin").
//					Return(nil).
//					Times(1)
//			},
//			wantErr: false,
//		},
//		{
//			name: "skip - machine audit not passed",
//			req: &pb.HandleShowApprovalRequest{
//				ShowApprovalId: 1,
//				IsPass:         true,
//				Operator:       "admin",
//			},
//			mockFunc: func(ts *TestSetup) {
//				approval := &store.ChannelLiveShowApproval{
//					ID:                      1,
//					ImgTextMachineAuditType: store.ChannelLiveShowApprovalAuditTypePending,
//					AudioMachineAuditType:   store.ChannelLiveShowApprovalAuditTypePass,
//				}
//				ts.mockStore.mock.EXPECT().
//					GetChannelLiveShowApprovalById(gomock.Any(), uint32(1)).
//					Return(approval, nil).
//					Times(1)
//				// 不应该调用UpdateManualAuditType
//			},
//			wantErr: false,
//		},
//		{
//			name: "failure - approval not found",
//			req: &pb.HandleShowApprovalRequest{
//				ShowApprovalId: 999,
//				IsPass:         true,
//				Operator:       "admin",
//			},
//			mockFunc: func(ts *TestSetup) {
//				ts.mockStore.mock.EXPECT().
//					GetChannelLiveShowApprovalById(gomock.Any(), uint32(999)).
//					Return(nil, errors.New("not found")).
//					Times(1)
//			},
//			wantErr: true,
//		},
//	}
//
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			ts := setupTest(t)
//			defer ts.tearDown()
//
//			tt.mockFunc(ts)
//
//			resp, err := ts.mgr.HandleShowApproval(context.Background(), tt.req)
//
//			if tt.wantErr {
//				assert.Error(t, err)
//			} else {
//				assert.NoError(t, err)
//				if tt.validate != nil {
//					tt.validate(resp)
//				}
//			}
//		})
//	}
//}

// TestShowListMgr_GetChannelLiveShowApprovalList 测试获取审批列表
func TestShowListMgr_GetChannelLiveShowApprovalList(t *testing.T) {
    tests := []struct {
        name                 string
        datetime             uint32
        ttid                 uint32
        approvalFilterStatus uint32
        pageNum              uint32
        pageSize             uint32
        mockFunc             func(*TestSetup)
        wantErr              bool
        validate             func(*pb.GetChannelLiveShowApprovalListResp)
    }{
        {
            name:                 "success - get approval list with ttid",
            datetime:             **********,
            ttid:                 12345,
            approvalFilterStatus: uint32(pb.GetChannelLiveShowApprovalListRequest_Pending),
            pageNum:              1,
            pageSize:             10,
            mockFunc: func(ts *TestSetup) {
                // Mock GetUidByName
                ts.mockAC.mock.EXPECT().
                    GetUidByAlias(gomock.Any(), "12345").
                    Return(uint32(12345), nil).
                    Times(1)

                // Mock CountMachineAuditDoneChannelLiveShowApproval
                ts.mockStore.mock.EXPECT().
                    CountMachineAuditDoneChannelLiveShowApproval(gomock.Any(), uint32(**********), uint32(12345), []uint32{store.ChannelLiveShowApprovalAuditTypePending}).
                    Return(uint32(5), nil).
                    Times(1)

                ts.mockBC.mock.EXPECT().GetShowListResourceObsPrefix().Return("obsPrefix").AnyTimes()
                // Mock PageMachineAuditDoneChannelLiveShowApproval
                approvalList := []*store.ChannelLiveShowApproval{
                    {
                        ID:                    1,
                        ShowName:              "测试节目1",
                        ShowStartTime:         1704070800,
                        ShowEndTime:           1704074400,
                        AnchorUid:             12345,
                        ImgMachineAuditType:   store.ChannelLiveShowApprovalAuditTypePass,
                        TextMachineAuditType:  store.ChannelLiveShowApprovalAuditTypePass,
                        AudioMachineAuditType: store.ChannelLiveShowApprovalAuditTypePass,
                        ManualAuditType:       store.ChannelLiveShowApprovalAuditTypePending,
                    },
                }
                ts.mockStore.mock.EXPECT().
                    PageMachineAuditDoneChannelLiveShowApproval(gomock.Any(), uint32(**********), uint32(12345), []uint32{store.ChannelLiveShowApprovalAuditTypePending}, uint32(1), uint32(10), uint32(1)).
                    Return(approvalList, nil).
                    Times(1)

                // Mock BatchGetUserProfile
                userMap := map[uint32]*app.UserProfile{
                    12345: {
                        Uid:      12345,
                        Nickname: "测试用户",
                        Account:  "tt1234",
                    },
                }
                ts.mockAC.mock.EXPECT().
                    BatchGetUserProfile(gomock.Any(), []uint32{12345}).
                    Return(userMap, nil).
                    Times(1)
            },
            wantErr: false,
            validate: func(resp *pb.GetChannelLiveShowApprovalListResp) {
                assert.NotNil(t, resp)
                assert.Equal(t, uint32(5), resp.Total)
                assert.Len(t, resp.ApprovalList, 1)
                assert.Equal(t, "测试节目1", resp.ApprovalList[0].ShowName)
                assert.Equal(t, "测试用户", resp.ApprovalList[0].AnchorNickname)
            },
        },
        {
            name:                 "success - get approval list without ttid",
            datetime:             **********,
            ttid:                 0,
            approvalFilterStatus: 0,
            pageNum:              1,
            pageSize:             10,
            mockFunc: func(ts *TestSetup) {
                // Mock CountMachineAuditDoneChannelLiveShowApproval
                ts.mockStore.mock.EXPECT().
                    CountMachineAuditDoneChannelLiveShowApproval(gomock.Any(), uint32(**********), uint32(0), []uint32{}).
                    Return(uint32(3), nil).
                    Times(1)

                // Mock PageMachineAuditDoneChannelLiveShowApproval
                ts.mockStore.mock.EXPECT().
                    PageMachineAuditDoneChannelLiveShowApproval(gomock.Any(), uint32(**********), uint32(0), []uint32{}, uint32(1), uint32(10), uint32(1)).
                    Return([]*store.ChannelLiveShowApproval{}, nil).
                    Times(1)

                // Mock BatchGetUserProfile
                ts.mockAC.mock.EXPECT().
                    BatchGetUserProfile(gomock.Any(), []uint32{}).
                    Return(map[uint32]*app.UserProfile{}, nil).
                    Times(1)
            },
            wantErr: false,
            validate: func(resp *pb.GetChannelLiveShowApprovalListResp) {
                assert.NotNil(t, resp)
                assert.Equal(t, uint32(3), resp.Total)
                assert.Len(t, resp.ApprovalList, 0)
            },
        },
        {
            name:     "failure - GetUidByName error",
            datetime: **********,
            ttid:     12345,
            pageNum:  1,
            pageSize: 10,
            mockFunc: func(ts *TestSetup) {
                ts.mockAC.mock.EXPECT().
                    GetUidByAlias(gomock.Any(), "12345").
                    Return(uint32(0), errors.New("user not found")).
                    Times(1)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            ts := setupTest(t)
            defer ts.tearDown()

            tt.mockFunc(ts)

            resp, err := ts.mgr.GetChannelLiveShowApprovalList(context.Background(), tt.datetime, tt.ttid, tt.approvalFilterStatus, tt.pageNum, tt.pageSize)

            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
                if tt.validate != nil {
                    tt.validate(resp)
                }
            }
        })
    }
}

// TestShowListMgr_GetChannelLiveShowInfoById 测试根据ID获取节目信息
func TestShowListMgr_GetChannelLiveShowInfoById(t *testing.T) {
    tests := []struct {
        name     string
        showId   uint32
        mockFunc func(*TestSetup)
        wantErr  bool
        validate func(item *pb.ShowItem)
    }{
        {
            name:   "success - get show info by id",
            showId: 1,
            mockFunc: func(ts *TestSetup) {
                show := &store.ChannelLiveShow{
                    ID:                    1,
                    ApprovalId:            1,
                    ShowName:              "测试节目",
                    ShowStartTime:         1704070800,
                    ShowEndTime:           1704074400,
                    ShowCoverImg:          "cover.jpg",
                    ShowDescAudio:         "desc.mp3",
                    ShowDescAudioDuration: 60,
                    VoiceTagId:            10,
                    ContentTagId:          20,
                    AnchorUid:             12345,
                }
                ts.mockStore.mock.EXPECT().
                    GetChannelLiveShowByShowId(gomock.Any(), uint32(1)).
                    Return(show, nil).
                    Times(1)
            },
            wantErr: false,
            validate: func(item *pb.ShowItem) {
                assert.NotNil(t, item)
                assert.Equal(t, "测试节目", item.ShowName)
                assert.Equal(t, uint32(1704070800), item.ShowStartTime)
                assert.Equal(t, uint32(1704074400), item.ShowEndTime)
                assert.Equal(t, uint32(12345), item.Uid)
            },
        },
        {
            name:   "success - show not found",
            showId: 999,
            mockFunc: func(ts *TestSetup) {
                ts.mockStore.mock.EXPECT().
                    GetChannelLiveShowByShowId(gomock.Any(), uint32(999)).
                    Return(&store.ChannelLiveShow{}, nil).
                    Times(1)
            },
            wantErr: false,
            validate: func(item *pb.ShowItem) {
                assert.NotNil(t, item)
                assert.Equal(t, "", item.ShowName)
            },
        },
        {
            name:   "failure - store error",
            showId: 1,
            mockFunc: func(ts *TestSetup) {
                ts.mockStore.mock.EXPECT().
                    GetChannelLiveShowByShowId(gomock.Any(), uint32(1)).
                    Return(nil, errors.New("database error")).
                    Times(1)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            ts := setupTest(t)
            defer ts.tearDown()

            tt.mockFunc(ts)

            item, err := ts.mgr.GetChannelLiveShowInfoById(context.Background(), tt.showId)

            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
                if tt.validate != nil {
                    tt.validate(item)
                }
            }
        })
    }
}
