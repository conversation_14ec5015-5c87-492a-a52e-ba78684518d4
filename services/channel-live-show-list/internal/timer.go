package internal

import (
    "context"
    "fmt"
    "time"

    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    impb "golang.52tt.com/protocol/services/im-api"
)

func (s *Server) startTimer() error {
    var err error
    s.timerD, err = timer.NewTimerD(context.Background(),
        "channel-live-show-list-svr",
        timer.WithV8RedisCmdable(s.redisCli))
    if err != nil {
        log.Errorf("startTimer NewTimerD err:%v", err)
        return err
    }

    s.timerD.AddIntervalTask("ChannelLiveShowEndScoreNotify", 10*time.Second,
        tasks.NewTracingTask(tasks.FuncTask(s.ChannelLiveShowEndScoreNotify), "ChannelLiveShowEndScoreNotify", 0))

    s.timerD.Start()
    return nil
}

func (s *Server) ChannelLiveShowEndScoreNotify(ctx context.Context) {
    notifySt := time.Now().Add(-10 * time.Minute)
    notifyEt := notifySt.Add(time.Minute) // 检索10分钟前1分钟区间内的数据
    showList, err := s.showListMgr.GetChannelLiveShowListEnd(ctx, uint32(notifyEt.Unix()), uint32(notifySt.Unix()))
    if err != nil {
        log.ErrorWithCtx(ctx, "ChannelLiveShowEndScoreNotify fail to GetChannelLiveShowListEnd. notifySt:%d, notifyEt:%d, err:%v", notifySt, notifyEt, err)
        return
    }
    log.DebugWithCtx(ctx, "ChannelLiveShowEndScoreNotify, showList len: %d, notifySt:%d, notifyEt:%d", len(showList), notifySt, notifyEt)
    if len(showList) == 0 {
        return
    }

    notifiedShowIdList := make([]uint32, 0, len(showList))
    for _, item := range showList {
        score, err := s.showScoreMgr.GetScoreStByAnchorUidShowId(ctx, item.AnchorUid, item.ID)
        if err != nil {
            log.ErrorWithCtx(ctx, "ChannelLiveShowEndScoreNotify fail to showScoreMgr.GetScoreStByAnchorUidShowId, anchorUid:%d, showId:%d, err:%v", item.AnchorUid, item.ID, err)
            continue
        }
        msg := "亲爱的达人，刚刚的演出不错哦，希望你继续努力~"
        if score.TotalRatingCnt > 0 {
            msg = fmt.Sprintf("亲爱的达人，刚刚的演出有%d个用户为你打分，观众的满意度是%.2f分，希望你继续努力哦", score.TotalRatingCnt, float32(score.TotalScore)/float32(score.TotalRatingCnt))
        }
        err = s.acLayer.SendOfficialAccountMsg(ctx, "channel-live-show-list-ChannelLiveShowEndScoreNotify", item.AnchorUid, &impb.Text{
            Content: msg,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "ChannelLiveShowEndScoreNotify fail to acLayer.SendOfficialAccountMsg, anchorUid:%d, showId:%d, err:%v", item.AnchorUid, item.ID, err)
            continue
        }
        notifiedShowIdList = append(notifiedShowIdList, item.ID)
        log.DebugWithCtx(ctx, "ChannelLiveShowEndScoreNotify send msg success, anchorUid:%d, showId:%d, msg:%s", item.AnchorUid, item.ID, msg)
    }

    log.InfoWithCtx(ctx, "ChannelLiveShowEndScoreNotify notifiedShowIdList len: %d", len(notifiedShowIdList))
    err = s.showListMgr.MarkChannelLiveShowEndNotify(ctx, notifiedShowIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "ChannelLiveShowEndScoreNotify fail to MarkChannelLiveShowEndNotify, notifiedShowIdList:%+v, err:%v", notifiedShowIdList, err)
    }
}
