package event

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    channelPb "golang.52tt.com/protocol/app/channel"
    channelgoevent "golang.52tt.com/protocol/services/channelol-go/event"
    "golang.52tt.com/services/channel-live-show-list/internal/entity"
    "time"
)

func (s *KafkaEvent) HandleChannelEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
    event := &channelgoevent.ChannelOLEvent{}
    err := proto.Unmarshal(msg.Value, event)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleChannelEvent Failed to proto.Unmarshal %+v", err)
        return err, false
    }

    // 只处理直播间事件
    if event.GetChannelType() != uint32(channelPb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
        log.DebugWithCtx(ctx, "handlerChannelEvent no handler ev:%+v", event)
        return nil, false
    }

    switch event.Event.(type) {
    case *channelgoevent.ChannelOLEvent_EnterEvent:
        log.DebugWithCtx(ctx, "HandlerEnterChannelEvent ev:%+v", event)
        if err := s.HandlerEnterChannelEvent(ctx, event); err != nil {
            log.ErrorWithCtx(ctx, "handlerChannelEvent HandlerChannelEvent err:%v", err)
            return nil, true // 重试
        }
    case *channelgoevent.ChannelOLEvent_LeaveEvent:
        log.DebugWithCtx(ctx, "handlerChannelEvent no handler ev:%+v", event)
    }

    return nil, false
}

func (s *KafkaEvent) HandlerEnterChannelEvent(ctx context.Context, event *channelgoevent.ChannelOLEvent) error {
    // 获取直播间主播信息
    var anchorUid uint32
    channelInfo, err := s.acLayerMgr.GetSimpleChannelInfo(ctx, event.GetCid())
    if err != nil || channelInfo.GetChannelId() == 0 {
        log.ErrorWithCtx(ctx, "handlerChannelEvent GetSimpleChannelInfo channelInfo:%+v err:%v", channelInfo, err)
        return err
    }

    anchorUid = channelInfo.GetCreaterUid() // 直播间的房主即主播
    if event.GetUid() == anchorUid {
        // 主播进房不需要推送
        return nil
    }

    // 获取房间当前节目信息
    showInfo, err := s.showListMgr.GetNearlyChannelLiveShowInfo(ctx, anchorUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "handlerChannelEvent GetNearlyChannelLiveShowInfo err:%v", err)
        return err
    }

    if showInfo.GetShowId() == 0 {
        return nil
    }

    now := time.Now()
    nextDayBegin := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, time.Local)

    var highLightPart string
    var content string
    if int64(showInfo.GetShowStartTime()) >= now.Unix() && int64(showInfo.GetShowStartTime()) < nextDayBegin.Unix() {
        // 当天有节目，且节目未开始
        showStartTime := time.Unix(int64(showInfo.GetShowStartTime()), 0)
        highLightPart = fmt.Sprintf("%s 播出 %s", showStartTime.Format("15:04"), showInfo.GetShowName())
        content = fmt.Sprintf("%s，留下来听听吧", highLightPart)

    } else if int64(showInfo.GetShowStartTime()) < now.Unix() && int64(showInfo.GetShowEndTime()) > now.Unix() {
        // 当前有进行中的节目
        highLightPart = fmt.Sprintf("主播正在播出 %s", showInfo.GetShowName())
        content = fmt.Sprintf("%s，留下来听听吧", highLightPart)

    } else {
        // 其他情况不需要推送
        log.DebugWithCtx(ctx, "handlerChannelEvent no handler ev:%+v showInfo:%+v", event, showInfo)
        return nil
    }

    // 延迟推送，先加入队列
    err = s.showListMgr.AddChannelImNotify(ctx, &entity.ChannelImNotify{
        ToUid:     event.GetUid(),
        ChannelId: event.GetCid(),
        AnchorUid: showInfo.GetUid(),
        Content:   content,
        HighLight: highLightPart,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "handlerChannelEvent AddChannelImNotify err:%v", err)
        return err
    }

    return nil
}
