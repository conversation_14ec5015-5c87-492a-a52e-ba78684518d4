package event

import (
    "context"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_go"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_scheme"
    "gitlab.ttyuyin.com/tt-infra/middleware/kafka"
    "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
    "golang.52tt.com/clients/channelol"
    "golang.52tt.com/pkg/config"
    "golang.52tt.com/pkg/log"
    anti_corruption_layer "golang.52tt.com/services/channel-live-show-list/internal/model/anti-corruption-layer"
    "golang.52tt.com/services/channel-live-show-list/internal/conf"
    show_list "golang.52tt.com/services/channel-live-show-list/internal/model/show-list"
)

type KafkaEvent struct {
    channelEventSub subscriber.Subscriber

    acLayerMgr  anti_corruption_layer.IACLayer
    showListMgr show_list.IShowListMgr

    channelOlCli     channelol.IClient
    channelSchemeCli channel_scheme.ChannelSchemeClient
    channelCli       channel_go.ChannelGoClient
}

func (s *KafkaEvent) Close() {
    _ = s.channelEventSub.Stop()
}

// NewKafkaEvent .
func NewKafkaEvent(sc *conf.StartConfig,
    acLayerMgr anti_corruption_layer.IACLayer, showListMgr show_list.IShowListMgr) (ev *KafkaEvent, err error) {

    ev = &KafkaEvent{
        acLayerMgr:  acLayerMgr,
        showListMgr: showListMgr,
    }

    ev.channelOlCli = channelol.NewClient()
    ev.channelSchemeCli = channel_scheme.MustNewClient(context.Background())
    ev.channelCli = channel_go.MustNewClient(context.Background())

    ev.channelEventSub, err = NewEventLinkSubscriber(sc.ChannelOLKafkaConfig, subscriber.ProcessorContextFunc(ev.HandleChannelEvent))
    if err != nil {
        log.Errorf("NewKafkaEvent failed,config:%+v,err:%v", sc, err)
        return nil, err
    }

    log.Infof("NewKafkaEvent success,config:%+v,", sc)
    return ev, nil
}

func NewEventLinkSubscriber(kfkCfg *config.KafkaConfig, processor subscriber.ProcessorContext) (subscriber.Subscriber, error) {
    cfg := kafka.DefaultConfig()
    cfg.ClientID = kfkCfg.ClientID
    cfg.Consumer.Offsets.Initial = kafka.OffsetNewest
    cfg.Consumer.Return.Errors = true

    kafkaSub, err := kafka.NewSubscriber(kfkCfg.BrokerList(), cfg, subscriber.WithMaxRetryTimes(3))
    if err != nil {
        log.ErrorWithCtx(nil, "NewEventLinkSubscriber failed,config:%+v,err:%v", kfkCfg, err)
        return nil, err
    }

    err = kafkaSub.SubscribeContext(kfkCfg.GroupID, []string{kfkCfg.Topics}, processor)
    if err != nil {
        log.ErrorWithCtx(nil, "NewEventLinkSubscriber failed,config:%+v,err:%v", kfkCfg, err)
        return nil, err
    }

    return kafkaSub, nil
}
