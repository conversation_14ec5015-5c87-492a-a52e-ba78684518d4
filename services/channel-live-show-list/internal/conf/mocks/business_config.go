// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-live-show-list/internal/conf (interfaces: IBusinessConfManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	conf "golang.52tt.com/services/channel-live-show-list/internal/conf"
)

// MockIBusinessConfManager is a mock of IBusinessConfManager interface.
type MockIBusinessConfManager struct {
	ctrl     *gomock.Controller
	recorder *MockIBusinessConfManagerMockRecorder
}

// MockIBusinessConfManagerMockRecorder is the mock recorder for MockIBusinessConfManager.
type MockIBusinessConfManagerMockRecorder struct {
	mock *MockIBusinessConfManager
}

// NewMockIBusinessConfManager creates a new mock instance.
func NewMockIBusinessConfManager(ctrl *gomock.Controller) *MockIBusinessConfManager {
	mock := &MockIBusinessConfManager{ctrl: ctrl}
	mock.recorder = &MockIBusinessConfManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBusinessConfManager) EXPECT() *MockIBusinessConfManagerMockRecorder {
	return m.recorder
}

// GetApplyGroupId mocks base method.
func (m *MockIBusinessConfManager) GetApplyGroupId() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApplyGroupId")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetApplyGroupId indicates an expected call of GetApplyGroupId.
func (mr *MockIBusinessConfManagerMockRecorder) GetApplyGroupId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplyGroupId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetApplyGroupId))
}

// GetAuditTestModel mocks base method.
func (m *MockIBusinessConfManager) GetAuditTestModel() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuditTestModel")
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetAuditTestModel indicates an expected call of GetAuditTestModel.
func (mr *MockIBusinessConfManagerMockRecorder) GetAuditTestModel() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuditTestModel", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetAuditTestModel))
}

// GetAvailableDeclareAfterDay mocks base method.
func (m *MockIBusinessConfManager) GetAvailableDeclareAfterDay() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvailableDeclareAfterDay")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetAvailableDeclareAfterDay indicates an expected call of GetAvailableDeclareAfterDay.
func (mr *MockIBusinessConfManagerMockRecorder) GetAvailableDeclareAfterDay() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailableDeclareAfterDay", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetAvailableDeclareAfterDay))
}

// GetDeclarePageUrl mocks base method.
func (m *MockIBusinessConfManager) GetDeclarePageUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeclarePageUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetDeclarePageUrl indicates an expected call of GetDeclarePageUrl.
func (mr *MockIBusinessConfManagerMockRecorder) GetDeclarePageUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeclarePageUrl", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetDeclarePageUrl))
}

// GetFakeEntryTextList mocks base method.
func (m *MockIBusinessConfManager) GetFakeEntryTextList() []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFakeEntryTextList")
	ret0, _ := ret[0].([]string)
	return ret0
}

// GetFakeEntryTextList indicates an expected call of GetFakeEntryTextList.
func (mr *MockIBusinessConfManagerMockRecorder) GetFakeEntryTextList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFakeEntryTextList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetFakeEntryTextList))
}

// GetRatingFloatConf mocks base method.
func (m *MockIBusinessConfManager) GetRatingFloatConf() *conf.ShowRatingFloat {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRatingFloatConf")
	ret0, _ := ret[0].(*conf.ShowRatingFloat)
	return ret0
}

// GetRatingFloatConf indicates an expected call of GetRatingFloatConf.
func (mr *MockIBusinessConfManagerMockRecorder) GetRatingFloatConf() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRatingFloatConf", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetRatingFloatConf))
}

// GetShowApplyLimitCnt mocks base method.
func (m *MockIBusinessConfManager) GetShowApplyLimitCnt() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowApplyLimitCnt")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetShowApplyLimitCnt indicates an expected call of GetShowApplyLimitCnt.
func (mr *MockIBusinessConfManagerMockRecorder) GetShowApplyLimitCnt() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowApplyLimitCnt", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetShowApplyLimitCnt))
}

// GetShowListResourceObsPrefix mocks base method.
func (m *MockIBusinessConfManager) GetShowListResourceObsPrefix() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowListResourceObsPrefix")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetShowListResourceObsPrefix indicates an expected call of GetShowListResourceObsPrefix.
func (mr *MockIBusinessConfManagerMockRecorder) GetShowListResourceObsPrefix() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowListResourceObsPrefix", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetShowListResourceObsPrefix))
}

// GetShowTimeDuration mocks base method.
func (m *MockIBusinessConfManager) GetShowTimeDuration(arg0 time.Time) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowTimeDuration", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetShowTimeDuration indicates an expected call of GetShowTimeDuration.
func (mr *MockIBusinessConfManagerMockRecorder) GetShowTimeDuration(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowTimeDuration", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetShowTimeDuration), arg0)
}

// GetUserDeclareDailyLimitCnt mocks base method.
func (m *MockIBusinessConfManager) GetUserDeclareDailyLimitCnt() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDeclareDailyLimitCnt")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetUserDeclareDailyLimitCnt indicates an expected call of GetUserDeclareDailyLimitCnt.
func (mr *MockIBusinessConfManagerMockRecorder) GetUserDeclareDailyLimitCnt() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDeclareDailyLimitCnt", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetUserDeclareDailyLimitCnt))
}

// GetUserDeclareWeeklyLimitCnt mocks base method.
func (m *MockIBusinessConfManager) GetUserDeclareWeeklyLimitCnt() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDeclareWeeklyLimitCnt")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetUserDeclareWeeklyLimitCnt indicates an expected call of GetUserDeclareWeeklyLimitCnt.
func (mr *MockIBusinessConfManagerMockRecorder) GetUserDeclareWeeklyLimitCnt() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDeclareWeeklyLimitCnt", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetUserDeclareWeeklyLimitCnt))
}

// GetUserGroupApi mocks base method.
func (m *MockIBusinessConfManager) GetUserGroupApi() *conf.UserGroupApi {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserGroupApi")
	ret0, _ := ret[0].(*conf.UserGroupApi)
	return ret0
}

// GetUserGroupApi indicates an expected call of GetUserGroupApi.
func (mr *MockIBusinessConfManagerMockRecorder) GetUserGroupApi() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserGroupApi", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetUserGroupApi))
}

// IsNeedShowEntry mocks base method.
func (m *MockIBusinessConfManager) IsNeedShowEntry() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsNeedShowEntry")
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsNeedShowEntry indicates an expected call of IsNeedShowEntry.
func (mr *MockIBusinessConfManagerMockRecorder) IsNeedShowEntry() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsNeedShowEntry", reflect.TypeOf((*MockIBusinessConfManager)(nil).IsNeedShowEntry))
}

// Reload mocks base method.
func (m *MockIBusinessConfManager) Reload(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reload", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Reload indicates an expected call of Reload.
func (mr *MockIBusinessConfManagerMockRecorder) Reload(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reload", reflect.TypeOf((*MockIBusinessConfManager)(nil).Reload), arg0)
}

// Watch mocks base method.
func (m *MockIBusinessConfManager) Watch(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Watch", arg0)
}

// Watch indicates an expected call of Watch.
func (mr *MockIBusinessConfManagerMockRecorder) Watch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockIBusinessConfManager)(nil).Watch), arg0)
}
