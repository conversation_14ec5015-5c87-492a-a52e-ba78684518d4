package conf

//go:generate quicksilver-cli test interface ../conf
//go:generate mockgen -destination=./mocks/business_config.go -package=mocks golang.52tt.com/services/channel-live-show-list/internal/conf IBusinessConfManager

import (
    redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
    "gitlab.ttyuyin.com/tyr/x/middleware/mysql"
    "golang.52tt.com/pkg/config"
)

type StartConfig struct {
    // from config file
    RedisConfig          *redisConnect.RedisConfig `json:"redis"`
    MysqlConfig          *mysql.Config             `json:"mysql"`
    MysqlReadOnlyConfig  *mysql.Config             `json:"readonly_mysql"`
    ChannelOLKafkaConfig *config.KafkaConfig       `json:"channelol_kfk"`
}
