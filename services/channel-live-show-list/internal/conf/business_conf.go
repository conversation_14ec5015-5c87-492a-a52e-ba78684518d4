package conf

import (
    "crypto/md5"
    "encoding/json"
    "fmt"
    "io/ioutil"
    "time"

    "golang.52tt.com/pkg/log"
)

const BusinessConfFile = "/data/cicd-dy-conf/ser/channel-live-show-list.json"

var LastConfMd5Sum [md5.Size]byte

type UserGroupApi struct {
    DspLpmAdminHost        string `json:"dsp_lpm_admin_host"`
    DspLpmOfflineGroupHost string `json:"dsp_lpm_offline_group_host"`
    DspLpmApiserverHost    string `json:"dsp_lpm_apiserver_host"`
}

type BusinessConf struct {
    ShowTimeDuration          map[string]uint32 `json:"show_time_duration"`            // 节目时长配置, 单位秒, key为起效时间格式YYYYMMDD
    ShowApplyLimitCnt         uint32            `json:"show_apply_limit_cnt"`          // 节目申请数量上限
    AvailableDeclareAfterDay  uint32            `json:"available_declare_after_day"`   // 可以申报多少天后的
    UserDeclareDailyLimitCnt  uint32            `json:"user_declare_daily_limit_cnt"`  // 用户每天申请数量上限
    UserDeclareWeeklyLimitCnt uint32            `json:"user_declare_weekly_limit_cnt"` // 用户每周申请数量上限
    UserGroupApi              *UserGroupApi     `json:"user_group_api"`                // 人群包配置
    ApplyGroupId              string            `json:"apply_group_id"`                // 申请人群包ID
    ShowListResourceObsPrefix string            `json:"show_list_resource_obs_prefix"` // 节目列表资源obs前缀
    NeedShowEntry             bool              `json:"need_show_entry"`               // 是否需要节目入口
    FakeEntryTextList         []string          `json:"fake_entry_text_list"`          // 假节目入口文本列表
    AuditTestModel            bool              `json:"audit_test_model"`              // 审核测试模式
    RatingFloatConf *ShowRatingFloat `json:"rating_float_conf"`
    DeclarePageUrl            string            `json:"declare_page_url"`              // 申报页面url
}

type ShowRatingFloat struct {
    IsShow    bool  `json:"is_show"`
    ListenSec int64 `json:"listen_sec"`
    StaySec   int64 `json:"stay_sec"`
}

func (c *BusinessConf) Parse(configFile string) (isChange bool, err error) {
    defer func() {
        if e := recover(); e != nil {
            err = fmt.Errorf("Failed to parse config: %v \n", e)
        }
    }()

    data, err := ioutil.ReadFile(configFile)
    if err != nil {
        return false, err
    }

    md5Sum := md5.Sum(data)
    if md5Sum == LastConfMd5Sum {
        isChange = false
        return
    }

    err = json.Unmarshal(data, &c)
    if err != nil {
        return false, err
    }

    err = c.CheckConf()
    if err != nil {
        return false, err
    }

    LastConfMd5Sum = md5Sum

    log.Infof("BusinessConf : %+v", c)
    return true, nil
}

func (c *BusinessConf) CheckConf() error {

    return nil
}

type BusinessConfManager struct {
    Done chan interface{}
    //mutex sync.RWMutex
    conf *BusinessConf
}

func NewBusinessConfManager() (*BusinessConfManager, error) {
    businessConf := &BusinessConf{}

    _, err := businessConf.Parse(BusinessConfFile)
    if err != nil {
        log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
        return nil, err
    }

    confMgr := &BusinessConfManager{
        conf: businessConf,
        Done: make(chan interface{}),
    }

    go confMgr.Watch(BusinessConfFile)

    return confMgr, nil
}

func (bm *BusinessConfManager) Reload(file string) error {
    businessConf := &BusinessConf{}

    isChange, err := businessConf.Parse(file)
    if err != nil {
        log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
        return err
    }

    if isChange {
        //bm.mutex.Lock()
        bm.conf = businessConf
        //bm.mutex.Unlock()

        log.Infof("Reload %+v", businessConf)
    }

    return nil
}

func (bm *BusinessConfManager) Watch(file string) {
    log.Infof("Watch start. file:%s", file)

    for {
        select {
        case _, ok := <-bm.Done:
            if !ok {
                log.Infof("Watch done")
                return
            }

        case <-time.After(30 * time.Second):
            err := bm.Reload(file)
            if err != nil {
                log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
            }
        }
    }
}

func (bm *BusinessConfManager) GetShowApplyLimitCnt() uint32 {
    if bm.conf == nil || bm.conf.ShowApplyLimitCnt == 0 {
        return 50
    }
    return bm.conf.ShowApplyLimitCnt
}

func (bm *BusinessConfManager) GetShowTimeDuration(t time.Time) uint32 {
    timeStr := t.Format("20060102")
    if conf, ok := bm.conf.ShowTimeDuration[timeStr]; ok {
        return conf
    }
    if bm.conf.ShowTimeDuration["default"] > 0 {
        return bm.conf.ShowTimeDuration["default"]
    }
    return 1800
}

func (bm *BusinessConfManager) GetAvailableDeclareAfterDay() uint32 {
    if bm.conf == nil {
        return 1
    }
    return bm.conf.AvailableDeclareAfterDay
}

func (bm *BusinessConfManager) GetUserDeclareDailyLimitCnt() uint32 {
    if bm.conf == nil || bm.conf.UserDeclareDailyLimitCnt == 0 {
        return 3
    }
    return bm.conf.UserDeclareDailyLimitCnt
}

func (bm *BusinessConfManager) GetUserDeclareWeeklyLimitCnt() uint32 {
    if bm.conf == nil || bm.conf.UserDeclareWeeklyLimitCnt == 0 {
        return 21
    }
    return bm.conf.UserDeclareWeeklyLimitCnt
}

func (bm *BusinessConfManager) GetUserGroupApi() *UserGroupApi {
    if bm.conf == nil || bm.conf.UserGroupApi == nil {
        log.Warnf("GetUserGroupApi conf is nil or UserGroupApi is nil")
        return nil
    }
    return bm.conf.UserGroupApi
}

func (bm *BusinessConfManager) GetApplyGroupId() string {
    if bm.conf == nil || bm.conf.ApplyGroupId == "" {
        log.Warnf("GetApplyGroupId conf is nil or ApplyGroupId is empty")
        return ""
    }
    return bm.conf.ApplyGroupId
}

func (bm *BusinessConfManager) GetShowListResourceObsPrefix() string {
    if bm.conf == nil || bm.conf.ShowListResourceObsPrefix == "" {
        return "https://testing-go-api.ttyuyin.com/obs-cdn/tt/liveroom-prog/"
    }
    return bm.conf.ShowListResourceObsPrefix
}

func (bm *BusinessConfManager) IsNeedShowEntry() bool {
    if bm.conf == nil {
        return false
    }
    return bm.conf.NeedShowEntry
}

func (bm *BusinessConfManager) GetFakeEntryTextList() []string {
    if bm.conf == nil || len(bm.conf.FakeEntryTextList) == 0 {
        return []string{}
    }
    return bm.conf.FakeEntryTextList
}

func (bm *BusinessConfManager) GetRatingFloatConf() *ShowRatingFloat {
    if bm.conf == nil || bm.conf.RatingFloatConf == nil {
        bm.conf.RatingFloatConf = &ShowRatingFloat{
            IsShow:    true,
            ListenSec: 60,
            StaySec:   10,
        }
    }
    return bm.conf.RatingFloatConf
}

func (bm *BusinessConfManager) GetAuditTestModel() bool {
    if bm.conf == nil {
        return false
    }
    return bm.conf.AuditTestModel
}

func (bm *BusinessConfManager) GetDeclarePageUrl() string {
    if bm.conf == nil || bm.conf.DeclarePageUrl == "" {
        return "https://app.52tt.com/testing/frontend-web-assist-voice-room-schedule/index.html#!/apply"
    }
    return bm.conf.DeclarePageUrl
}
