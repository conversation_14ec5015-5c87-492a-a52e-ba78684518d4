package conf

import(
	time "time"
)

type IBusinessConfManager interface {
	GetApplyGroupId() string
	GetAuditTestModel() bool
	GetAvailableDeclareAfterDay() uint32
	GetDeclarePageUrl() string
	GetFakeEntryTextList() []string
	GetRatingFloatConf() *ShowRatingFloat
	GetShowApplyLimitCnt() uint32
	GetShowListResourceObsPrefix() string
	GetShowTimeDuration(t time.Time) uint32
	GetUserDeclareDailyLimitCnt() uint32
	GetUserDeclareWeeklyLimitCnt() uint32
	GetUserGroupApi() *UserGroupApi
	IsNeedShowEntry() bool
	Reload(file string) error
	Watch(file string) 
}


type IBusinessConf interface {
	CheckConf() error
	Parse(configFile string) (isChange bool,err error)
}

