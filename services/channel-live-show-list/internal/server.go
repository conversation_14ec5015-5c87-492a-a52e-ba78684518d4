package internal

import (
    "context"
    "math"

    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "gitlab.ttyuyin.com/tyr/x/log"
    context0 "golang.org/x/net/context"
    "google.golang.org/grpc/codes"

    "time"

    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/channel-live-show-list"
    "golang.52tt.com/services/channel-live-show-list/internal/conf"
    "golang.52tt.com/services/channel-live-show-list/internal/event"
    anti_corruption_layer "golang.52tt.com/services/channel-live-show-list/internal/model/anti-corruption-layer"
    show_list "golang.52tt.com/services/channel-live-show-list/internal/model/show-list"
    show_score "golang.52tt.com/services/channel-live-show-list/internal/model/show-score"
)

var (
    errUnimplemented = protocol.NewExactServerError(codes.OK, status.ErrGrpcUnimplemented)
)

func NewServer(ctx context.Context, cfg *conf.StartConfig) (*Server, error) {
    log.Infof("server startup with cfg: %+v", *cfg)

    redisClient, err := redisConnect.NewClient(ctx, cfg.RedisConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to redisConnect.NewClient, %+v, err:%v", cfg.RedisConfig, err)
        return nil, err
    }

    mysqlDBCli, err := mysqlConnect.NewClient(ctx, cfg.MysqlConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to mysqlConnect.NewClient, %+v, err:%v", cfg.MysqlConfig, err)
        return nil, err
    }

    bc, err := conf.NewBusinessConfManager()
    if err != nil {
        return nil, err
    }

    acl, err := anti_corruption_layer.NewMgr(ctx, bc)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to anti_corruption_layer.NewShowListMgr, err:%v", err)
        return nil, err
    }

    scoreMgr, err := show_score.NewMgr(mysqlDBCli, redisClient, bc, acl)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to show_score.NewShowListMgr, err:%v", err)
        return nil, err
    }

    showListMgr, err := show_list.NewShowListMgr(mysqlDBCli, redisClient, bc, acl)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to show_list.NewShowListMgr, err:%v", err)
        return nil, err
    }

    ev, err := event.NewKafkaEvent(cfg, acl, showListMgr)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to event.NewKafkaEvent, err:%v", err)
        return nil, err
    }

    s := &Server{
        acLayer:      acl,
        bc:           bc,
        KafkaEvent:   ev,
        showScoreMgr: scoreMgr,
        showListMgr:  showListMgr,
        redisCli:     redisClient,
    }

    err = s.startTimer()
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to startTimer, err:%v", err)
        return nil, err
    }

    return s, nil
}

type Server struct {
    bc           *conf.BusinessConfManager
    KafkaEvent   *event.KafkaEvent
    acLayer      anti_corruption_layer.IACLayer
    showScoreMgr show_score.IShowScore
    showListMgr  show_list.IShowListMgr
    timerD       *timer.Timer
    redisCli     redis.Client
}

func (s *Server) UnfoldShowList(c context0.Context, request *pb.UnfoldShowListRequest) (*pb.UnfoldShowListResponse, error) {
    out := &pb.UnfoldShowListResponse{}
    defer func() {
        log.DebugWithCtx(c, "UnfoldShowList, req: %+v, out: %+v", request, out)
    }()
    list, err := s.showListMgr.UnfoldShowList(c, request)
    if err != nil {
        log.ErrorWithCtx(c, "UnfoldShowList, unfold show list error: %+v, req: %+v", err, request)
        return out, err
    }
    out.UnfoldShowList = list
    return out, nil
}

func (s *Server) SetApprovalAuditType(c context0.Context, request *pb.SetApprovalAuditTypeRequest) (*pb.SetApprovalAuditTypeResponse, error) {
    resp := &pb.SetApprovalAuditTypeResponse{}
    err := s.showListMgr.SetApprovalAuditType(c, request)
    if err != nil {
        log.ErrorWithCtx(c, "SetApprovalAuditType fail to SetApprovalAuditType. req:%+v, err:%v", request, err)
        return resp, err
    }
    return resp, nil
}

func (s *Server) GetShowListUserBaseInfo(ctx context.Context, request *pb.GetShowListUserBaseInfoRequest) (*pb.GetShowListUserBaseInfoResponse, error) {
    resp := &pb.GetShowListUserBaseInfoResponse{}
    defer func() {
        log.DebugWithCtx(ctx, "GetShowListUserBaseInfo resp:%+v,  req:%+v", resp, request)
    }()

    cid, err := s.acLayer.GetAnchorChannelId(ctx, request.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetShowListUserBaseInfo get anchor channel id failed, err: %v, req:%+v", err, request)
        return resp, err
    }
    if cid == 0 {
        resp.UnableReason = "主播才可以申请节目哦~"
        log.ErrorWithCtx(ctx, "GetShowListUserBaseInfo get anchor channel id failed, err: %v, req:%+v", err, request)
        return resp, nil
    }

    isValid, err := s.acLayer.IsUserInApplyGroup(ctx, request.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetShowListUserBaseInfo fail to IsUserInApplyGroup. uid :%d, err:%v", request.GetUid(), err)
        return resp, err
    }
    if !isValid {
        log.ErrorWithCtx(ctx, "GetShowListUserBaseInfo user is not in apply group. uid :%d", request.GetUid())
        resp.UnableReason = "当前没有申请节目权限哦~"
        return resp, nil
    }
    resp.ShowApplyEntry = isValid

    dailyLimitCnt, weeklyLimitCnt, err := s.showListMgr.GetUserRemainDeclareCnt(ctx, request.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetShowListUserBaseInfo fail to GetUserDeclareDailyLimit. uid:%d, err:%v", request.GetUid(), err)
        return resp, err
    }

    if s.bc.GetUserDeclareDailyLimitCnt() >= dailyLimitCnt {
        resp.RemainDeclareCnt = s.bc.GetUserDeclareDailyLimitCnt() - dailyLimitCnt
    }
    if dailyLimitCnt >= s.bc.GetUserDeclareDailyLimitCnt() || weeklyLimitCnt >= s.bc.GetUserDeclareWeeklyLimitCnt() {
        resp.UnableReason = "已达到当日/当周申报节目上限"
    }

    return resp, nil
}
func (s *Server) AddShowTag(ctx context.Context, req *pb.AddShowTagRequest) (*pb.AddShowTagResponse, error) {
    return s.showListMgr.AddShowTag(ctx, req)
}

func (s *Server) DeleteShowTag(ctx context.Context, req *pb.DeleteShowTagRequest) (*pb.DeleteShowTagResponse, error) {
    return s.showListMgr.DeleteShowTag(ctx, req)
}

func (s *Server) DeclareShow(ctx context.Context, request *pb.DeclareShowRequest) (*pb.DeclareShowResponse, error) {
    return s.showListMgr.DeclareShow(ctx, request)
}

func (s *Server) GetLiveShowEntryInfo(ctx context.Context, req *pb.GetLiveShowEntryInfoRequest) (*pb.GetLiveShowEntryInfoResponse, error) {
    out := &pb.GetLiveShowEntryInfoResponse{}
    defer func() {
        log.DebugWithCtx(ctx, "GetLiveShowEntryInfo out:%+v, req:%+v", out, req)
    }()
    // 获取今天0点的时间戳
    todayStartTime := uint32(time.Now().Unix() / 86400 * 86400)

    out, err := s.showListMgr.GetLiveShowEntryInfo(ctx, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLiveShowEntryInfo fail to GetChannelLiveShowCntByStartTimeTag. todayStartTime:%d, err:%v", todayStartTime, err)
        return out, err
    }
    return out, nil
}

func (s *Server) GetShowList(ctx context.Context, req *pb.GetShowListRequest) (*pb.GetShowListResponse, error) {
    out := &pb.GetShowListResponse{}
    defer func() {
        log.DebugWithCtx(ctx, "GetShowList out:%+v, req:%+v", out, req)
    }()
    if req.GetDatetime() == 0 {
        now := time.Now()
        req.Datetime = uint32(time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).Unix())
    }
    out, err := s.showListMgr.GetShowList(ctx, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetShowList fail to showListMgr.GetShowList, req:%+v, err:%v", req, err)
        return out, err
    }

    // isShowEnd
    now := time.Now()
    for _, itemGroup := range out.ShowList {
        itemGroup.IsEnd = IsShowEnd(now, int64(itemGroup.GetEndTime()))
    }
    return out, nil
}

func IsShowEnd(now time.Time, endTime int64) bool {
    return now.Unix() > endTime
}

func (s *Server) GetChannelLiveShowInfo(ctx context.Context, request *pb.GetChannelLiveShowInfoRequest) (*pb.GetChannelLiveShowInfoResponse, error) {
    out := &pb.GetChannelLiveShowInfoResponse{}

    show, err := s.showListMgr.GetNearlyChannelLiveShowInfo(ctx, request.GetAnchorUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetNearlyChannelLiveShowInfo fail to showListMgr.GetNearlyChannelLiveShowInfo, req:%+v, err:%v", request, err)
        return out, err
    }

    // 如果节目还没开始
    if int64(show.GetShowStartTime()) > time.Now().Unix() {
        log.DebugWithCtx(ctx, "GetNearlyChannelLiveShowInfo show not start, req:%+v", request)
        return out, nil
    }

    ratingFloat := s.bc.GetRatingFloatConf()

    out.LiveShowInfo = &pb.LiveShowInfo{
        ShowId:    show.GetShowId(),
        ShowName:  show.GetShowName(),
        BeginTs:   int64(show.GetShowStartTime()),
        EndTs:     int64(show.GetShowEndTime()),
        AnchorUid: show.GetUid(),
        RatingFloatConf: &pb.ShowRatingFloat{
            IsShow:       ratingFloat.IsShow,
            ListenSec:    ratingFloat.ListenSec,
            FloatStaySec: ratingFloat.StaySec,
        },
    }

    return out, nil
}

func (s *Server) RateChannelLiveShow(ctx context.Context, request *pb.RateChannelLiveShowRequest) (*pb.RateChannelLiveShowResponse, error) {
    out := &pb.RateChannelLiveShowResponse{}

    // 获取节目信息
    showInfo, err := s.showListMgr.GetChannelLiveShowInfoById(ctx, request.GetShowId())
    if err != nil {
        log.ErrorWithCtx(ctx, "RateChannelLiveShow fail to showListMgr.GetChannelLiveShowInfoById, req:%+v, err:%v", request, err)
        return out, err
    }

    nowUnix := uint32(time.Now().Unix())
    if showInfo.GetShowId() == 0 ||
        showInfo.GetShowStartTime() > nowUnix ||
        showInfo.GetShowEndTime() < nowUnix {
        // 不存在的节目信息 or 不在节目时间
        log.ErrorWithCtx(ctx, "RateChannelLiveShow fail to showListMgr.GetChannelLiveShowInfoById,showInfo：%+v req:%+v, err:%v", showInfo, request, err)
        return out, nil
    }

    // 记录用户评分
    ok, err := s.showScoreMgr.LiveShowRating(ctx, request.GetUid(), showInfo.GetUid(), request.GetShowId(), request.GetScore())
    if err != nil {
        log.ErrorWithCtx(ctx, "RateChannelLiveShow fail to showScoreMgr.LiveShowRating, req:%+v, err:%v", request, err)
        return out, err
    }

    if ok {
        _ = s.showScoreMgr.AddToShowScoreSyncQueue(ctx, request.GetShowId(), showInfo.GetUid(), int64(showInfo.GetShowEndTime()))
        log.InfoWithCtx(ctx, "RateChannelLiveShow success, req:%+v", request)
    }

    return out, nil
}

func (s *Server) TestTool(ctx context.Context, request *pb.TestToolRequest) (*pb.TestToolResponse, error) {
    out := &pb.TestToolResponse{}

    err := s.showListMgr.ManualAddShow(ctx, request.GetShowInfo())
    if err != nil {
        log.ErrorWithCtx(ctx, "TestTool failed, req:%+v, err:%+v", request, err)
        return out, err
    }

    log.InfoWithCtx(ctx, "TestTool success, req:%+v", request)
    return out, nil
}

func (s *Server) HandleShowApproval(ctx context.Context, req *pb.HandleShowApprovalRequest) (*pb.HandelShowApprovalResponse, error) {
    return s.showListMgr.HandleShowApproval(ctx, req)
}

func (s *Server) GetShowTime(ctx context.Context, req *pb.GetShowTimeRequest) (*pb.GetShowTimeResponse, error) {
    resp, err := s.showListMgr.GetShowTime(ctx, req.GetDatetime())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetShowTime fail to showListMgr.GetShowTime, req:%+v, err:%v", req, err)
        return resp, err
    }
    log.DebugWithCtx(ctx, "GetShowTime success, resp:%+v", resp)
    return resp, nil
}

func (s *Server) ShutDown() {

}

// GetChannelLiveShowApprovalList 获取审批记录
func (s *Server) GetChannelLiveShowApprovalList(ctx context.Context, req *pb.GetChannelLiveShowApprovalListRequest) (*pb.GetChannelLiveShowApprovalListResp, error) {
    resp := &pb.GetChannelLiveShowApprovalListResp{}
    approvalList, err := s.showListMgr.GetChannelLiveShowApprovalList(ctx, req.GetDatetime(), req.GetTtid(), req.GetApprovalFilterStatus(), req.GetPageNum(), req.GetPageSize())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowApprovalList fail to showListMgr.GetChannelLiveShowApprovalList, req:%+v, err:%v", req, err)
        return resp, err
    }
    log.DebugWithCtx(ctx, "GetChannelLiveShowApprovalList success, resp:%+v", approvalList)

    resp = approvalList

    // 补充主播评分
    uidList := make([]uint32, 0, len(approvalList.GetApprovalList()))
    for _, item := range approvalList.GetApprovalList() {
        uidList = append(uidList, item.GetAnchorUid())
    }
    userScoreMap, err := s.showScoreMgr.GetAnchorScoreByUidList(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelLiveShowApprovalList fail to showScoreMgr.GetAnchorScoreByUidList, req:%+v, err:%v", req, err)
        return resp, err
    }
    log.DebugWithCtx(ctx, "GetChannelLiveShowApprovalList showScoreMgr.GetAnchorScoreByUidList, req:%+v, userScoreMap:%+v", req, userScoreMap)
    for _, item := range approvalList.GetApprovalList() {
        scoreInfo, ok := userScoreMap[item.GetAnchorUid()]
        if !ok {
            continue
        }
        item.AnchorScore = math.Round(float64(scoreInfo.TotalScore)/float64(scoreInfo.TotalRatingCnt)*100) / 100
    }

    // 补充本场评分
    for _, item := range approvalList.GetApprovalList() {
        if item.GetShowEndTime() > uint32(time.Now().Unix()) {
            continue
        }
        // 查询showId
        show, err := s.showListMgr.GetChannelLiveShowByApprovalId(ctx, item.GetShowApprovalId())
        if err != nil {
            log.ErrorWithCtx(ctx, "GetChannelLiveShowApprovalList fail to showListMgr.GetChannelLiveShowByApprovalId, req:%+v, err:%v", req, err)
            continue
        }

        scoreInfo, err := s.showScoreMgr.GetScoreStByAnchorUidShowId(ctx, item.GetAnchorUid(), show.ID)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetChannelLiveShowApprovalList fail to showScoreMgr.GetScoreStByAnchorUidShowId, req:%+v, err:%v", req, err)
            continue
        }
        if scoreInfo.TotalRatingCnt > 0 {
            item.ThisShowScore = math.Round(float64(scoreInfo.TotalScore) / float64(scoreInfo.TotalRatingCnt)*100) / 100
        }
    }

    return resp, nil
}

// GetShowTag 获取标签配置
func (s *Server) GetShowTag(ctx context.Context, req *pb.GetShowTagRequest) (*pb.GetShowTagResponse, error) {
    resp, err := s.showListMgr.GetShowTag(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetShowTag fail to showListMgr.GetShowTag, req:%+v, err:%v", req, err)
        return resp, err
    }
    log.DebugWithCtx(ctx, "GetShowTag success, resp:%+v", resp)
    return resp, nil
}

// ModifyShowApprovalTag 修改标签
func (s *Server) ModifyShowApprovalTag(ctx context.Context, req *pb.ModifyShowApprovalTagRequest) (*pb.ModifyShowApprovalTagResponse, error) {
    return s.showListMgr.UpdateShowApprovalTag(ctx, req)
}
