package entity

type AnchorScore struct {
    Uid            uint32 `json:"anchor_uid"`
    TotalScore     uint32 `json:"total_score"`
    TotalRatingCnt uint32 `json:"total_rating_cnt"`
}

type ChannelImNotify struct {
    ToUid     uint32 `json:"to_uid"`
    ChannelId uint32 `json:"channel_id"`
    AnchorUid uint32 `json:"anchor_uid"`
    Content   string `json:"content"`
    HighLight string `json:"high_light"`
}
