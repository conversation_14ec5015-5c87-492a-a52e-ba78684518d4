package handler

import (
	"github.com/gin-gonic/gin"
	ttv1 "gitlab.ttyuyin.com/gengo/t-bank/quwan/centralbank/legacy/tt/v1"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/unified_pay/cb"
	"google.golang.org/protobuf/encoding/protojson"
	"net/http"
)

func CheckFreezeOrder(ctx *gin.Context) {
	log.InfoWithCtx(ctx, "CheckFreezeOrder")
	resp := &ttv1.CheckFreezeOrderResponse{
		OrderStatus: ttv1.CheckFreezeOrderResponse_ORDER_STATUS_FINISH,
	}
	if ctx.ContentType() != jsonContentType {
		//ReturnErr(ctx, errParams, "content type invalid, get "+ctx.ContentType())
		log.ErrorWithCtx(ctx, "content type invalid, get "+ctx.ContentType())
		ctx.AbortWithStatus(http.StatusBadRequest)
		return
	}

	data, err := ctx.GetRawData()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRawData failed %v", err)
		//ReturnErr(ctx, errParams, "params json invalid")
		//log.ErrorWithCtx(ctx, "params json invalid")
		ctx.AbortWithStatus(http.StatusBadRequest)
		return
	}

	log.InfoWithCtx(ctx, "CheckFreezeOrder data=%s", string(data))

	reqBody := &ttv1.CheckFreezeOrderRequest{}

	if err := protojson.Unmarshal(data, reqBody); err != nil {
		log.ErrorWithCtx(ctx, "CheckFreezeOrder protojson.Unmarshal err %+v", err)
		//ReturnErr(ctx, errParams, "params json invalid")
		ctx.AbortWithStatus(http.StatusBadRequest)
		return
	}

	log.InfoWithCtx(ctx, "CheckFreezeOrder reqBody %+v", reqBody)
	target := getServiceTargetByAppId(reqBody.AppId)
	if len(target) == 0 {
		log.ErrorWithCtx(ctx, "CheckFreezeOrder not found target service:%s", reqBody.AppId)
		//ReturnErr(ctx, errParams, "not found target service")
		ctx.AbortWithStatus(http.StatusBadRequest)
		return
	}

	cbCli, err := getCallbackClientNew(ctx, target)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create callback client, AppId: %s, err: %v", reqBody.AppId, err)
		//ReturnErr(ctx, errSystem, "Failed to create callback client")
		ctx.AbortWithStatus(http.StatusBadRequest)
		return
	}

	_, _, err = cbCli.Notify(ctx, &cb.PayNotify{
		AppId:      reqBody.AppId,
		OutTradeNo: reqBody.OutTradeNo,
		CreateAt:   uint32(reqBody.GetNotifiedAt().AsTime().Unix()),
		Uid: uint32(reqBody.GetUid()),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "Notify failed, AppId: %s, err: %v", reqBody.AppId, err)
		//ReturnErr(ctx, errSystem, "call server notify failed")
		//ctx.AbortWithStatus(http.StatusBadRequest)
		resp.OrderStatus = ttv1.CheckFreezeOrderResponse_ORDER_STATUS_UNSPECIFIED
		ctx.JSON(http.StatusOK, resp)
		return
	}
	log.InfoWithCtx(ctx, "CheckFreezeOrder Notify reqBody:%v", reqBody)
	ctx.JSON(http.StatusOK, resp)
}
