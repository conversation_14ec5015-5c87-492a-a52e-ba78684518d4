package server

import (
	"context"
	"fmt"
	ttv1 "gitlab.ttyuyin.com/gengo/t-bank/quwan/centralbank/legacy/tt/v1"
	"gitlab.ttyuyin.com/gengo/t-bank/quwan/centralbank/tbank"
	"gitlab.ttyuyin.com/gengo/t-bank/quwan/centralbank/tbank/types/errortype"
	im_api "golang.52tt.com/clients/im-api"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/marketid_helper"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/tbean"
	grpcclient "golang.52tt.com/pkg/tbean/grpc-client"
	pb "golang.52tt.com/protocol/services/unified_pay"
	"google.golang.org/protobuf/types/known/timestamppb"
	"strconv"
	"time"
)


type FreezeV2 struct {
	secretKey string
	//tbeanClient tbean.Client
	coinClient ttv1.TTLegacyClient
	notifyHost string
	imClient im_api.IClient
}

func NewFreezeV2(postUrl, secretKey, notifyHost string) *FreezeV2 {
	fz :=  &FreezeV2{
		//tbeanClient: tbean.NewClient(postUrl),
		secretKey:  secretKey,
		coinClient: grpcclient.NewTBeanClient(),
		notifyHost: notifyHost,
		imClient: im_api.NewIClient(),
	}
	//fz.tbeanClient.SetSecretKey(secretKey)
	return  fz
}

func (t *FreezeV2) PresetFreeze(ctx context.Context,  in *pb.PresetFreezeReq) (uint32, protocol.ServerError) {
	/*req := &tbean.PresetFreezeReqData{
		AppId:         in.GetAppId(),
		OutTradeNo:    in.GetOutTradeNo(),
		BuyerId:       in.GetUid(),
		FreezeBalance: in.GetFreezeBalance(),
		OutOrderTime:  in.GetOutOrderTime(),
		FreezeTitle:   in.GetFreezeReason(),
		SubId:         in.GetSubId(),
	}

	resp, err := t.tbeanClient.PresetFreeze(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "tbeanClient.PresetFreeze ctx %v in %v err %v", ctx, req, err)
		return 0,  err
	}
 	log.InfoWithCtx(ctx, "PresetFreeze success in:%v resp:%+v", in, resp )
																																							return resp.GetRemainBalance(), nil*/
	outOrderTime, err := time.ParseInLocation("2006-01-02 15:04:05", in.GetOutOrderTime(), time.Local)
	if err != nil {
		log.ErrorWithCtx(ctx, "time.ParseInLocation=%s, err=%v", in.GetOutOrderTime(), err)
		return 0, tbean.ErrParamError
	}
	req := ttv1.FreezeRequest{
		AppId:               in.GetAppId(),
		Uid:                 uint64(in.GetUid()),
		FreezeAmount: in.GetFreezeBalance(),
		OutTradeNo:          in.GetOutTradeNo(),
		FreezeTitle:         in.GetFreezeReason(),
		OutOrderTime:        timestamppb.New(outOrderTime),
		SubScope:            in.GetSubId(),
		MaxFreezeTimeSecond: 0, //默认5min
		NotifyHost:          t.notifyHost,
	}
	resp, err := t.coinClient.Freeze(ctx, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "FreezeV2.FreezeErr=%v", err)

		errDetail, b := tbank.ErrorDetail(err)
		if b {
			log.ErrorWithCtx(ctx, "tbank.ErrorDetailErr=%+v", errDetail)
			return 0, t.TransAPIErrorV2(ctx, in.GetUid(), errDetail.GetCode())
		}
		return 0, tbean.ErrSystemError
	}

	return uint32(resp.GetRemainBalance()), nil
}

func (t *FreezeV2) UnFreezeAndRefund(ctx context.Context,  in *pb.UnFreezeAndRefundReq) (*pb.UnFreezeAndRefundResp, protocol.ServerError) {
	out := &pb.UnFreezeAndRefundResp{}
	/*req := &tbean.UnFreezeAndRefundReqData{
		AppId:         in.GetAppId(),
		OutTradeNo:    in.GetOutTradeNo(),
		BuyerId:       in.GetUid(),
	}

	r, err := t.tbeanClient.UnFreezeAndRefund(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "tbeanClient.PresetFreeze ctx %v in %v err %v", ctx, req, err)
		return out,  err
	}
	log.InfoWithCtx(ctx, "UnFreezeAndRefund success in:%v remain:%d", in, r )
	return out , nil*/
	req := &ttv1.UnFreezeAndRefundRequest{
		AppId:      in.GetAppId(),
		Uid:        uint64(in.GetUid()),
		OutTradeNo: in.GetOutTradeNo(),
	}
	refund, err := t.coinClient.UnFreezeAndRefund(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "UnFreezeAndRefundErr=%v", err)
		errDetail, b := tbank.ErrorDetail(err)
		if b {
			log.ErrorWithCtx(ctx, "tbank.ErrorDetailErr=%+v", errDetail)
			return out, t.TransAPIErrorV2(ctx, in.GetUid(), errDetail.GetCode())
		}
		return out, tbean.ErrSystemError
	}
	log.InfoWithCtx(ctx, "UnFreezeAndRefund=%+v", refund)
	return out, nil
}

func (t *FreezeV2) UnfreezeAndConsume(ctx context.Context,  in *pb.UnfreezeAndConsumeReq) (*pb.UnfreezeAndConsumeResp, protocol.ServerError) {
	out := &pb.UnfreezeAndConsumeResp{}
	/*req := &tbean.UnfreezeAndConsumeReqData{
		AppId:         in.GetAppId(),
		OutTradeNo:    in.GetOutTradeNo(),
		BuyerId:       in.GetUid(),
		BuyerName:     in.GetUserName(),
		CommodityId:   fmt.Sprintf("%d", in.GetItemId()),
		CommodityName: in.GetItemName(),
		Num:           in.GetItemNum(),
		UnitPrice:     in.GetItemPrice(),
		Price:         in.GetTotalPrice(),
		Platform:      in.GetPlatform(),
		Notes:         in.GetNotes(),
		OutOrderTime:  in.GetOutOrderTime(),
		RefundPrice:   in.GetRefundPrice(),
	}

	r, err := t.tbeanClient.UnfreezeAndConsume(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "tbeanClient.PresetFreeze ctx %v in %v err %v", ctx, req, err)
		return out,  err
	}

	dealToken, err2 := handlerDealToken(r.GetDealToken(), r.GetOutTradeNo())
	if err2 != nil {
		log.ErrorWithCtx(ctx, "handlerDealToken err=%+v", err2)
		return out, protocol.NewServerError(-2, err2.Error())
	}

	log.InfoWithCtx(ctx, "deal_token=%s", dealToken)

	out.Ctime = r.GetCtime()
	out.DealToken = dealToken
	log.InfoWithCtx(ctx, "UnfreezeAndConsume success in:%v remain:%v", in, r)
	return out , nil*/
	platform, err := strconv.Atoi(in.GetPlatform())
	if err != nil {
		log.ErrorWithCtx(ctx, "strconv.Atoi=%v", err)
		return out, tbean.ErrParamError
	}

	outOrderTime := time.Now()
	if in.GetOutOrderTime() != "" {
		outOrderTime, err = time.ParseInLocation("2006-01-02 15:04:05", in.GetOutOrderTime(), time.Local)
		if err != nil {
			log.ErrorWithCtx(ctx, "time.ParseInLocation=%s, err=%v", in.GetOutOrderTime(), err)
			errDetail, b := tbank.ErrorDetail(err)
			if b {
				log.ErrorWithCtx(ctx, "tbank.ErrorDetailErr=%+v", errDetail)
				return out, t.TransAPIErrorV2(ctx, in.GetUid(), errDetail.GetCode())
			}
			return out, tbean.ErrSystemError
		}
	}

	req := &ttv1.UnFreezeAndConsumeRequest{
		AppId:         in.GetAppId(),
		Uid:           uint64(in.GetUid()),
		Username:      in.GetUserName(),
		CommodityId:   fmt.Sprintf("%d", in.GetItemId()),
		CommodityName: in.GetItemName(),
		CommodityNum:         in.GetItemNum(),
		CommodityUnitAmount:  in.GetItemPrice(),
		CommodityTotalAmount: in.GetTotalPrice(),
		Platform:      ttv1.Platform(platform),
		OutTradeNo:    in.GetOutTradeNo(),
		Notes:         in.GetNotes(),
		OutOrderTime:  timestamppb.New(outOrderTime),
		ReturnAmount:         in.GetRefundPrice(),
	}
	r, err := t.coinClient.UnFreezeAndConsume(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "UnFreezeAndConsume Err=%v", err)
		errDetail, b := tbank.ErrorDetail(err)
		if b {
			log.ErrorWithCtx(ctx, "tbank.ErrorDetailErr=%+v", errDetail)
			return out, t.TransAPIErrorV2(ctx, in.GetUid(), errDetail.GetCode())
		}
		return out, tbean.ErrSystemError
	}

	log.InfoWithCtx(ctx, "coinClient.UnFreezeAndConsume resp=%+v", r)

	dealToken, err2 := handlerDealToken(r.GetDealToken(), r.GetConsumeOrder().GetOutTradeNo())
	if err2 != nil {
		log.ErrorWithCtx(ctx, "handlerDealToken err=%+v", err2)
		return out, tbean.ErrSystemError
	}

	log.InfoWithCtx(ctx, "deal_token=%s", dealToken)
	log.InfoWithCtx(ctx, "ctime=%v", r.GetConsumeOrder().GetCreateTime())

	out.Ctime = r.GetConsumeOrder().GetCreateTime().AsTime().Local().Format("2006-01-02 15:04:05")
	out.DealToken = dealToken
	log.InfoWithCtx(ctx, "UnfreezeAndConsume success in:%v remain:%v", in, r)
	return out , nil
}

func (t *FreezeV2) TransAPIErrorV2(ctx context.Context, uid uint32, code errortype.ErrorCode) protocol.ServerError {
	retErr := tbean.ErrSystemError
	switch code {
	case 5000001:
		retErr = tbean.ErrIllegalArgument
	case 5000028:
		retErr = tbean.ErrDuplicateOrder
	case errortype.ErrorCode_ERROR_CODE_BALANCE_NOT_ENOUGH:
		retErr = tbean.ErrInsufficientFunds
	case errortype.ErrorCode_ERROR_CODE_USER_NOT_EXISTS:
		retErr = tbean.ErrAccountNotExists
	case 5000053:
		retErr = tbean.ErrInsufficientFunds
	case errortype.ErrorCode_ERROR_CODE_CONSUME_BLACK:
		retErr = tbean.ErrFreezeBlackList
	case errortype.ErrorCode_ERROR_CODE_AVAILABLE_BALANCE_NOT_ENOUGH:
		retErr = tbean.ErrorNoEnoughBalance
	case errortype.ErrorCode_ERROR_CODE_CONSUME_ABNORMAL_RECHARGE:
		retErr = tbean.ErrorAbnormalProtect
	case errortype.ErrorCode_ERROR_CODE_CONSUME_UNDER_RECHARGE:
		retErr = tbean.ErrorMinorRecharge

	default:
		retErr = tbean.ErrSystemError
	}

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.Errorf("Failed to get service info from context: %d", uid)
		serviceInfo = &grpc.ServiceInfo{}
	}
	if retErr == tbean.ErrorAbnormalProtect {
		content := "尊敬的用户你好，系统检测到您的账号存在异常风险，已为您启用平台消费保护机制，如需解除限制请点击【功能申诉】发起申诉解除"
		highlight := "【功能申诉】"
		jumpUrl := marketid_helper.Get("tbean_appeal_url", serviceInfo.MarketID, serviceInfo.TerminalType)
		_, err := t.imClient.SimpleSendTTAssistantText(ctx, uid, content, highlight, jumpUrl)
		if err != nil {
			log.ErrorWithCtx(ctx, "SimpleSendTTAssistantText err=%v", err)
		}

		content = "请根据【消费保护机制申诉材料说明】页面提示提供相关信息进行申诉解限~"
		highlight = "【消费保护机制申诉材料说明】"
		jumpUrl = marketid_helper.Get("tbean_consume_url", serviceInfo.MarketID, serviceInfo.TerminalType)
		_, err = t.imClient.SimpleSendTTAssistantText(ctx, uid, content, highlight, jumpUrl)
		if err != nil {
			log.ErrorWithCtx(ctx, "SimpleSendTTAssistantText err=%v", err)
		}
	}

	if retErr == tbean.ErrorMinorRecharge {
		content := "尊敬的用户你好，系统检测到您的账号存在异常风险，已为您启用平台充值消费保护机制，如需解除限制请点击【功能申诉】发起申诉解除。"
		highlight := "【功能申诉】"
		jumpUrl := marketid_helper.Get("tbean_appeal_url", serviceInfo.MarketID, serviceInfo.TerminalType)
		_, err := t.imClient.SimpleSendTTAssistantText(ctx, uid, content, highlight, jumpUrl)
		if err != nil {
			log.ErrorWithCtx(ctx, "SimpleSendTTAssistantText err=%v", err)
		}

		content = "请根据【充值消费保护机制申诉材料说明】页面提示提供相关信息进行申诉解限~"
		highlight = "【充值消费保护机制申诉材料说明】"
		jumpUrl = marketid_helper.Get("tbean_recharge_url", serviceInfo.MarketID, serviceInfo.TerminalType)
		_, err = t.imClient.SimpleSendTTAssistantText(ctx, uid, content, highlight, jumpUrl)
		if err != nil {
			log.ErrorWithCtx(ctx, "SimpleSendTTAssistantText err=%v", err)
		}
	}

	return retErr
}


