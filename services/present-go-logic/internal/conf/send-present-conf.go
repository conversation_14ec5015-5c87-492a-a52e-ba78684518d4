package conf

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/log"
	"io/ioutil"
	"sync"
	"time"
)

const SuperStarConfFile = "/data/oss/conf-center/tt/super_star_protector.json"
const NobilityConfFile = "/data/oss/conf-center/tt/nobility.json"
const PresentGoLogicConfFile = "/data/oss/conf-center/tt/present-go-logic.json"

// SendPresentConf 送礼过程中可能会用到的一系列相关配置
type SendPresentConf struct {
	NobilityConf       NobilityConf
	SuperStarConf      SuperStarConf
	PresentGoLogicConf PresentGoLogicConf
}

// NobilityLevel 贵族特权配置
type NobilityLevel struct {
	Level         uint32   `json:"level"`
	LevelName     string   `json:"lvName"`
	PrivilegeList []uint32 `json:"privilegeList"`
}

type NobilityConf struct {
	NLevel []*NobilityLevel `json:"nobilityLevel"`
}

// StarItem 大房限制

type StarItem struct {
	BusinessType uint32   `json:"business_type"`
	UidList      []uint32 `json:"uid_list"`
	CidList      []uint32 `json:"cid_list"`
}

type SuperStarConf struct {
	SuperStarList []*StarItem `json:"super_star_list"`
}

type PresentGoLogicConf struct {
	CheckTimeOut            uint32             `json:"check_time_out"`
	GiftReorderConf         *GiftReorderConfig `json:"gift_reorder_conf"`
	LabelSystemClientCaller string             `json:"label_system_client_caller"`
	LabelSystemClientSecret string             `json:"label_system_client_secret"`
	LabelSystemServerHost   string             `json:"label_system_server_host"`
}

type GiftReorderConfig struct {
	FreqUseGiftLabelName                 string              `json:"freq_use_gift_label_name"`                   // 常用礼物标签名
	AllConsumeMoneyLabelName             string              `json:"all_consume_money_label_name"`               // 累计消费金额标签名
	RecentConsumeMoneyLabelName          string              `json:"recent_consume_money_label_name"`            // 最近消费金额标签名
	RecentUseLuckyGiftTimeLabelName      string              `json:"recent_use_lucky_gift_time_label_name"`      // 最近使用幸运礼物次数标签名
	FreqUseGiftShowNum                   uint32              `json:"freq_use_gift_show_num"`                     // 常用礼物展示个数
	FreqUseGiftStartPos                  uint32              `json:"freq_use_gift_start_pos"`                    // 常用礼物开始展示的位置，第1位传1而不是0
	FreqUseGiftMinTime                   uint32              `json:"freq_use_gift_min_time"`                     // 常用礼物需要满足的最小使用次数
	ShowFreqUseGiftUserTypes             []string            `json:"show_freq_use_gift_user_types"`              // 哪些用户类型展示常用礼物
	RecentUseLuckyGiftMinTime            uint32              `json:"recent_use_lucky_gift_min_time"`             // 最近使用幸运礼物要满足的最小使用次数，超过才算幸运礼物用户
	AllConsumeLow2LowMid                 uint32              `json:"all_consume_low_2_low_mid"`                  // 累计消费金额，低消用户 到 中低价值用户 的分割值
	AllConsumeLowMid2MidHigh             uint32              `json:"all_consume_low_mid_2_mid_high"`             // 累计消费金额，中低价值用户 到 高价值用户 的分割值
	AllConsumeMidHigh2High               uint32              `json:"all_consume_mid_high_2_high"`                // 累计消费金额，中高价值用户 到 高价值用户 的分割值
	LowMidConsumeUserRecentConsumeSplit  uint32              `json:"low_mid_consume_user_recent_consume_split"`  // 中低价值用户，最近消费金额分割点
	MidHighConsumeUserRecentConsumeSplit uint32              `json:"mid_high_consume_user_recent_consume_split"` // 中高价值用户，最近消费金额分割点
	UserTypeRcmdGifts                    map[string][]uint32 `json:"user_type_rcmd_gifts"`                       // 不同用户类型推荐的礼物列表
}

func (g *GiftReorderConfig) GetAllLabelNames() []string {
	return []string{g.FreqUseGiftLabelName, g.AllConsumeMoneyLabelName, g.RecentConsumeMoneyLabelName, g.RecentUseLuckyGiftTimeLabelName}
}

func (g *GiftReorderConfig) ShowFreqUseGift(userType string) bool {
	for _, v := range g.ShowFreqUseGiftUserTypes {
		if v == userType {
			return true
		}
	}
	return false
}

func (g *GiftReorderConfig) GetRcmdGifts(userType string) []uint32 {
	return g.UserTypeRcmdGifts[userType]
}

type SendPresentConfManager struct {
	Done  chan interface{}
	mutex sync.RWMutex
	conf  *SendPresentConf
}

func NewSendPresentConf(ctx context.Context) *SendPresentConfManager {
	sendPresentConf := &SendPresentConf{}

	configMap := make(map[string]interface{})
	configMap[SuperStarConfFile] = &sendPresentConf.SuperStarConf
	configMap[NobilityConfFile] = &sendPresentConf.NobilityConf
	configMap[PresentGoLogicConfFile] = &sendPresentConf.PresentGoLogicConf

	err := sendPresentConf.Parse(ctx, configMap)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewSendPresentConf fail to Parse BusinessConf err:%v", err)
	}

	confMgr := &SendPresentConfManager{
		conf: sendPresentConf,
		Done: make(chan interface{}),
	}

	go confMgr.Watch(ctx, configMap)

	return confMgr
}

func (c *SendPresentConf) Parse(ctx context.Context, configMap map[string]interface{}) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()
	for path, config := range configMap {
		data, err := ioutil.ReadFile(path)
		if err != nil {
			return err
		}

		err = json.Unmarshal(data, &config)
		if err != nil {
			return err
		}

		fmt.Println(config)
	}

	log.InfoWithCtx(ctx, "BusinessConf : %+v", c)
	return nil
}

func (s *SendPresentConfManager) Reload(ctx context.Context, configMap map[string]interface{}) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	err := s.conf.Parse(ctx, configMap)
	if err != nil {
		log.ErrorWithCtx(ctx, "fail to Reload SendPresentConf err:%v", err)
	}

	return nil
}

func (s *SendPresentConfManager) Watch(ctx context.Context, configMap map[string]interface{}) {
	log.InfoWithCtx(ctx, "Watch start. file:%v", configMap)

	for {
		select {
		case _, ok := <-s.Done:
			if !ok {
				log.InfoWithCtx(ctx, "Watch done")
				return
			}

		case <-time.After(10 * time.Second):
			log.DebugWithCtx(ctx, "Watch check change")

			err := s.Reload(ctx, configMap)
			if err != nil {
				log.ErrorWithCtx(ctx, "Watch Reload fail. file:%v, err:%v", configMap, err)
			}
		}
	}
}

func (s *SendPresentConfManager) GetConf() *SendPresentConf {
	if s.conf == nil {
		return &SendPresentConf{}
	}
	return s.conf
}

func (s *SendPresentConfManager) GetGiftReorderConf() *GiftReorderConfig {
	if s.conf == nil || s.conf.PresentGoLogicConf.GiftReorderConf == nil {
		return &GiftReorderConfig{}
	}
	return s.conf.PresentGoLogicConf.GiftReorderConf
}
