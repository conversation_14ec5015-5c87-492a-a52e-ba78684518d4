package comm_send

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	ga "golang.52tt.com/protocol/app"
	presentPB2 "golang.52tt.com/protocol/app/userpresent"
	riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
	user_online "golang.52tt.com/protocol/services/user-online"
	presentPB "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/present-go-logic/internal/model/info"
	"golang.52tt.com/services/present-go-logic/internal/model/interceptor"
	client "golang.52tt.com/services/present-go-logic/internal/rpc"
	"google.golang.org/grpc/codes"
	"strconv"
	"strings"
	"time"
)

type iopInterceptor struct {
}

var IopInterceptor *iopInterceptor

func WithIopInterceptor() interceptor.Interceptor {
	return IopInterceptor
}

func (s *iopInterceptor) Handle(info *info.RequestBaseInfo) error {

	// 短超时
	ctx := info.GetCtx()

	timeOut := info.SendPresentConf.PresentGoLogicConf.CheckTimeOut
	if timeOut == 0 {
		timeOut = 1000
	}
	tmpCtx, cancel := context.WithTimeout(ctx, time.Millisecond*time.Duration(timeOut))
	defer cancel()

	//log.DebugWithCtx(ctx, "IopInterceptor begin info %+v ", info)

	// 红钻礼物不管
	if info.GetPresentConfig().GetPriceType() != uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) {
		log.DebugWithCtx(ctx, "IopInterceptor CheckIsChanceItem is diamond , id %d", info.CommonSendPresentReq.ItemId)
		return nil
	}

	baseReq := info.GetBaseReq()
	op := &riskMngApiPb.FaceAuthInfo{}
	if len(baseReq.GetFaceAuthInfo().GetToken()) > 0 {
		op.Token = baseReq.GetFaceAuthInfo().GetToken()
		op.ProviderCode = baseReq.GetFaceAuthInfo().GetProviderCode()
		op.ProviderResultData = baseReq.GetFaceAuthInfo().GetProviderResultData()
	}

	if len(baseReq.GetFaceAuthInfo().GetResultToken()) > 0 {
		op.ResultToken = baseReq.GetFaceAuthInfo().GetResultToken()
	}

	// 先填充二级房间类型
	channelSchemeInfo, err := client.ChannelSchemeCli.GetCurChannelSchemeInfo(tmpCtx, info.SendChannelInfo.GetChannelId(), info.SendChannelInfo.GetChannelType())
	if err != nil {
		log.ErrorWithCtx(tmpCtx, "FillUserProfileWithCommonSendReq fail , req %v ,err: %v", info.CommonSendPresentInfo.CommonSendPresentReq, err)
		return nil
	}

	// 还得填在线信息
	uidList := make([]uint32, 0)
	for _, item := range info.CommonSendPresentInfo.TargetUserMap {
		uidList = append(uidList, item.GetUid())
	}
	userOlList, err := client.UserOlCli.BatchGetLastMultiOnlineInfo(tmpCtx, uidList)
	if err != nil {
		log.ErrorWithCtx(tmpCtx, "FillUserProfileWithCommonSendReq fail , req %v ,err: %v", info.BatchSendPresentInfo)
		return nil
	}

	userOlMap := make(map[uint32]*user_online.MultiOnlineInfo)
	for _, item := range userOlList {
		userOlMap[item.GetUid()] = item
		log.DebugWithCtx(tmpCtx, "userOlList %+v", item)
	}

	deviceListStr := ""
	ipListStr := ""

	for _, uid := range uidList {
		deviceId := ""
		nowIp := ""
		lastLoginTs := int64(0)
		if userOlMap[uid] == nil {
			deviceListStr += deviceId + ","
			ipListStr += nowIp + ","
			continue
		}
		for _, olInfo := range userOlMap[uid].GetOnlineInfoList() {
			if olInfo.GetOnlineType() == user_online.OnlineType_ONLINE_TYPE_ONLINE && olInfo.GetOnlineAt() > lastLoginTs {
				deviceId = olInfo.GetDeviceIdHex()
				nowIp = olInfo.GetClientIp()
			}
		}
		deviceListStr += deviceId + ","
		ipListStr += nowIp + ","
	}

	deviceListStr = strings.ToLower(deviceListStr)
	// 去掉最后一个字符
	if len(deviceListStr) > 0 {
		deviceListStr = deviceListStr[:len(deviceListStr)-1]
	}

	if len(ipListStr) > 0 {
		ipListStr = ipListStr[:len(ipListStr)-1]
	}

	// 总价值
	price := float64(info.GetPresentConfig().GetPrice()) * float64(info.CommonSendPresentReq.Count) * float64(len(info.CommonSendPresentInfo.TargetUserMap)) / 100

	uidListStr := ""
	for _, item := range info.CommonSendPresentInfo.TargetUserMap {
		uidListStr += strconv.Itoa(int(item.GetUid())) + ","
	}
	uidListStr = strings.TrimRight(uidListStr, ",")

	// 区分参数，正常送礼和背包送礼是不一样的
	customType := 1
	sceneId := 7

	if info.CommonSendPresentReq.ItemSource == uint32(presentPB2.PresentSourceType_PRESENT_SOURCE_PACKAGE) {
		customType = 2
		sceneId = 8
	}

	checkReq := &riskMngApiPb.CheckReq{
		Scene: "ORDINARY_GIFT_CONSUME",
		SourceEntity: &riskMngApiPb.Entity{
			FaceAuthInfo:  op,
			Uid:           info.SendPresentUser.GetUid(),
			Phone:         info.SendPresentUser.GetPhone(),
			ChannelId:     info.SendChannelInfo.GetChannelId(),
			ChannelViewId: info.SendChannelInfo.GetChannelViewId(),
		},
		// 通用参数传递
		CustomParams: map[string]string{
			"consume_type":       strconv.Itoa(customType),
			"scene_id":           strconv.Itoa(sceneId),
			"amount":             strconv.FormatFloat(price, 'f', -1, 64),
			"rcv_uid":            uidListStr,
			"rcv_deviceId":       deviceListStr,
			"rcv_ip":             ipListStr,
			"room_id":            strconv.Itoa(int(info.SendChannelInfo.GetChannelId())),
			"scheme_id":          strconv.Itoa(int(channelSchemeInfo.GetSchemeInfo().GetSchemeId())),
			"schema_detail_type": strconv.Itoa(int(channelSchemeInfo.GetSchemeInfo().GetSchemeDetailType())),
		},
	}

	checkResp, err := client.RiskMngCli.CheckHelper(tmpCtx, checkReq, info.GetBaseReq())
	if err != nil {
		// 系统错误，风控非关键路径，可忽略系统错误
		log.ErrorWithCtx(tmpCtx, "risk-mng-api.Check failed, err:%v, req:%+v", err, checkResp)
		return nil
	}

	if info.BaseResp == nil {
		info.BaseResp = &ga.BaseResp{}
	}

	if len(checkResp.GetErrInfo()) != 0 && len(info.BaseResp.ErrInfo) == 0 {
		log.DebugWithCtx(tmpCtx, "risk-mng-api.Check errInfo:%+v", checkResp.GetErrInfo())
		info.BaseResp.ErrInfo = checkResp.GetErrInfo()
	}

	log.DebugWithCtx(tmpCtx, "risk-mng-api.Check resp:%+v", checkResp)

	if checkResp.GetErrCode() != 0 {
		info.BaseResp.ErrInfo = checkResp.GetErrInfo()
		return protocol.NewExactServerError(codes.OK, int(checkResp.GetErrCode()), checkResp.GetErrMsg())
	}

	return nil
}
