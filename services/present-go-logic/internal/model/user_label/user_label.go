package user_label

import (
	"bytes"
	"context"
	"crypto/md5" //#nosec
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/log"
	"io"
	"net"
	"net/http"
	"strconv"
	"strings"
	"time"
)

type CheckTagIdReq struct {
	Client *Client  `json:"client"`
	Data   *ReqData `json:"data"`
	Id     int64    `json:"id"`
	Sign   string   `json:"sign"`
}

func (s *CheckTagIdReq) GenSign(secretKey string) {
	dataUrlParams := fmt.Sprintf("appId=%s&entityDomain=%s&entityId=%s&labelNames=%s", s.Data.AppId, s.Data.EntityDomain,
		s.Data.EntityId, strings.Join(s.Data.Labels, ","))
	str := fmt.Sprintf("%d%s%s%s", s.Id, s.Client.Caller, dataUrlParams, secretKey)
	hash := md5.New() //#nosec
	hash.Write([]byte(str))
	sign := hash.Sum(nil)
	s.Sign = fmt.Sprintf("%x", sign)
}

type Client struct {
	Caller string `json:"caller"`
	Ex     string `json:"ex"`
}

type ReqData struct {
	AppId        string   `json:"appId"`
	EntityDomain string   `json:"entityDomain"`
	EntityId     string   `json:"entityId"`
	Labels       []string `json:"labelNames"`
}

type CheckTagIdResp struct {
	Id      int64             `json:"id"`
	Message string            `json:"message"`
	Status  int32             `json:"status"`
	Data    map[string]string `json:"data"`
}

func postWithContext(ctx context.Context, httpCli *http.Client, addr string, req interface{}, resp interface{}) (out []byte, err error) {
	data, err := marshalReq(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to Marshal err(%+v)", err)
		return
	}

	log.Infof("http POST %+v -> %+v", addr, string(data))

	request, err := http.NewRequestWithContext(ctx,"POST", addr, bytes.NewReader(data))
	if nil != err {
		log.ErrorWithCtx(ctx, "Failed to NewRequest err(%+v)", err)
		return
	}
	request.Header.Set("Content-Type", "application/json")

	response, err := httpCli.Do(request)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to s.do err(%+v)", err)
		return
	}
	defer response.Body.Close()

	if http.StatusOK != response.StatusCode {
		err = fmt.Errorf("post status code(%+v)", response.Status)
		log.ErrorWithCtx(ctx, "Failed to do code(%+v)", response.Status)
		return
	}

	body, err := io.ReadAll(response.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to ReadAll err(%+v)", err)
		return
	}

	if nil == resp {
		out = data
	} else {
		err = json.Unmarshal(body, &resp)
		if nil != err {
			log.ErrorWithCtx(ctx, "Data Unmarshal fail %v", err)
			return
		}
	}
	return
}

func marshalReq(req interface{}) (data []byte, err error) {
	if _, ok := req.([]byte); ok {
		data = req.([]byte)
	} else {
		data, err = json.Marshal(req)
		if err != nil {
			return
		}
	}
	return
}

type UserLabelMgr struct {
	httpCli      *http.Client
	clientCaller string
	clientSecret string
	serverHost   string
}

func NewUserLabelMgr(ctx context.Context, clientCaller string, clientSecret string, serverHost string) (*UserLabelMgr, error) {
	httpCli := &http.Client{
		Transport: &http.Transport{
			DialContext: func(ctx context.Context, network, addr string) (conn net.Conn, e error) {
				host, _, err := net.SplitHostPort(addr)
				if err != nil {
					return nil, err
				}
				addrs, err := net.LookupHost(host)
				if err != nil {
					return nil, err
				}
				fmt.Printf("resolved %s to %v\n", host, addrs)
				return net.Dial(network, addr)
			},
			MaxIdleConns:          500,              // 最大空闲连接
			MaxConnsPerHost:       500,              // 每个pod最多多少链接
			IdleConnTimeout:       60 * time.Second, // 空闲连接的超时时间
			ExpectContinueTimeout: 10 * time.Second, // 等待服务第一个响应的超时时间
			MaxIdleConnsPerHost:   100,              // 每个host保持的空闲连接数
		},
	}
	return &UserLabelMgr{
		httpCli:      httpCli,
		clientCaller: clientCaller,
		clientSecret: clientSecret,
		serverHost:   serverHost,
	}, nil
}

func (m *UserLabelMgr) GetUserLabelValue(ctx context.Context, uid uint32, labels []string) (map[string]string, error) {
	req := &CheckTagIdReq{
		Client: &Client{
			Caller: m.clientCaller,
			Ex:     "tt",
		},
		Data: &ReqData{
			AppId:        "ttvoice",
			EntityDomain: "user",
			EntityId:     strconv.Itoa(int(uid)),
			Labels:       labels,
		},
		Id: time.Now().UnixNano() / 1e6,
	}
	req.GenSign(m.clientSecret)
	log.DebugWithCtx(ctx, "GetUserLabelValue req: %+v", *req)

	resp := &CheckTagIdResp{}
	addr := m.serverHost + "/lpm-api/portrait/v1/singleQuery"
	_, err := postWithContext(ctx, m.httpCli, addr, req, resp)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserLabelValue err: %v", err)
		return nil, err
	}
	log.DebugWithCtx(ctx, "GetUserLabelValue resp: %+v", *resp)

	labelMap := make(map[string]string)
	if resp.Status == 200 && resp.Data != nil {
		for k, v := range resp.Data {
			labelMap[k] = v
		}
	}
	return labelMap, nil
}
