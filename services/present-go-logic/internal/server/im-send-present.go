package server

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app"
	presentPB_ "golang.52tt.com/protocol/app/userpresent"
	present_limit "golang.52tt.com/protocol/services/present-limit"
	present_middleware "golang.52tt.com/protocol/services/present-middleware"
	"golang.52tt.com/services/present-go-logic/internal/model/info"
	"golang.52tt.com/services/present-go-logic/internal/model/interceptor"
	common2 "golang.52tt.com/services/present-go-logic/internal/model/interceptor/common"
	"golang.52tt.com/services/present-go-logic/internal/model/interceptor/im"
	"golang.52tt.com/services/present-go-logic/internal/model/interceptor/send"
	"golang.52tt.com/services/present-go-logic/internal/rpc"
	"strconv"
	"sync"
)

// PresentSendByIM im送礼
func (s *PresentGoLogic_) PresentSendByIM(ctx context.Context, in *presentPB_.IMSendPresentReq) (out *presentPB_.IMSendPresentResp, err error) {
	out = &presentPB_.IMSendPresentResp{BaseResp: &app.BaseResp{}}
	// 先获取送礼所需的一些相关info
	log.DebugWithCtx(ctx, "BaseReq %v", in.GetBaseReq())

	sendInfo, err := info.InitRequestBaseInfoWithImSendReq(ctx, s.sendPresentConf.GetConf(), in, out)
	if err != nil {
		log.ErrorWithCtx(ctx, "IMSendPresent InitRequestBaseInfoWithImSendReq fail , req: %v , err: %v", in, err)
		return out, err
	}

	log.DebugWithCtx(ctx, "InitRequestBaseInfoWithImSendReq BaseReq %v", in.GetBaseReq())

	// 拦截器
	err = interceptor.WithInterceptors(sendInfo, im.SelfInterceptor, im.AiRoleCheckInterceptor, im.IopInterceptor, common2.UsualDeviceInterceptor,
		common2.NobilityInterceptor, common2.PresentTypeInterceptor, send.SinglePriceInterceptor,
		common2.OptValidInterceptor, common2.PresentSetInterceptor, im.BirthdayPresentInterceptor, im.NobilityStrangerInterceptor, im.InternalInterceptor,
		im.BackpackPresentLimitInterceptor)
	if err != nil {

		log.ErrorWithCtx(ctx, "PresentSendByIM WithInterceptors fail , req: %v , err: %v", in, err)

		tmp := &present_limit.ErrContent{}
		sErr := json.Unmarshal(out.BaseResp.ErrInfo, tmp)

		log.ErrorWithCtx(ctx, "PresentSendByIM WithInterceptors fail , error content: %v err:%v", tmp.PopupContent, sErr)

		return out, err
	}

	log.DebugWithCtx(ctx, "WithInterceptors BaseReq %v", in.GetBaseReq())

	if in.TargetType == uint32(presentPB_.SendPresentTargetType_PRESENT_TARGET_TYPE_AI_ROLE){
		// AI角色IM送礼，调用present-middleware的AiSendPresent
		req := &present_middleware.AiSendPresentReq{
			SendUid:    sendInfo.SendPresentUser.GetUid(),
			BatchType:  0, // 仅支持单目标
			TargetInfo: &present_middleware.AiPresentTargetInfo{Target: &present_middleware.AiPresentTargetInfo_SingleTarget{SingleTarget: &present_middleware.SingleTargetAi{
				RoleId:   in.GetRoleId(),
				PartnerId: in.GetPartnerId(),
				ItemId:   in.GetItemId(),
				Count:    in.GetCount(),
			}}},
			ChannelId:  0,
			SendSource: in.GetSendSource(),
			ItemSource: in.GetItemSource(),
			SendMethod: uint32(present_middleware.PresentSendMethodType_PRESENT_TYPE_IM),
			WithPay:    true, // IM路径默认需要扣费/消耗
			BackpackItemId: in.GetSourceId(),
			WithPush:   false, // AI暂不推送
			ClientInfo: &present_middleware.PresentClientInfo{
				AppId:    sendInfo.GetBaseReq().GetAppId(),
				MarketId: sendInfo.GetBaseReq().GetMarketId(),
				ServiceInfo: &present_middleware.ServiceCtrlInfo{
					ClientIp:      strconv.Itoa(int(sendInfo.GetServiceInfo().ClientIP)),
					ClientType:    uint32(sendInfo.GetServiceInfo().ClientType),
					ClientVersion: sendInfo.GetServiceInfo().ClientVersion,
					DeviceId:      sendInfo.GetServiceInfo().DeviceID,
					TerminalType:  sendInfo.GetServiceInfo().TerminalType,
				},
			},
			SendTime:     0, // 使用服务端时间
			IsMulti:      false,
			BusinessType: in.GetBusinessType(),
		}

		// 通过底层Stub直接调用AiSendPresent，避免改动IClient接口
		pmStub := client.PresentMiddlewareCli.Stub().(present_middleware.PresentMiddlewareClient)
		resp, err := pmStub.AiSendPresent(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "AiSendPresent fail , req: %v , err: %v", in, err)
			return out, err
		}
		out = fillImSendRespFromAi(in, resp)
	}else if len(in.GetTargetUidList()) == 0 {
		// 送礼
		req := &present_middleware.ImSendPresentReq{
			SendUid:    sendInfo.SendPresentUser.GetUid(),
			TargetUid:  in.GetTargetUid(),
			SendSource: in.GetSendSource(),
			ItemSource: in.GetItemSource(),
			SendMethod: uint32(present_middleware.PresentSendMethodType_PRESENT_TYPE_IM),
			SendType:   in.GetSendType(),
			IsOptValid: sendInfo.IsOpValid, // 这里需要判断
			ItemId:     in.GetItemId(),
			Count:      in.GetCount(),
			SourceId:   in.GetSourceId(),
			AppId:      sendInfo.GetBaseReq().GetAppId(),
			MarketId:   sendInfo.GetBaseReq().GetMarketId(),
			ServiceInfo: &present_middleware.ServiceCtrlInfo{
				ClientIp:      strconv.Itoa(int(sendInfo.GetServiceInfo().ClientIP)),
				ClientType:    uint32(sendInfo.GetServiceInfo().ClientType),
				ClientVersion: sendInfo.GetServiceInfo().ClientVersion,
				DeviceId:      sendInfo.GetServiceInfo().DeviceID,
				TerminalType:  sendInfo.GetServiceInfo().TerminalType,
			},
			PresentTextType: in.GetPresentTextType(),
			ShowImPreEffect: sendInfo.ImSendPresentInfo.IsBirthDayPresent,
			PreEffectText:   in.GetPreEffectText(),
		}

		resp, err := client.PresentMiddlewareCli.ImSendPresent(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelPresentSend SendPresent fail , req: %v , err: %v", in, err)
			return out, err
		}

		out = fillImSendResp(in, resp)
	} else {
		wg := sync.WaitGroup{}

		respMap := make(map[uint32]*present_middleware.ImSendPresentResp)

		for _, item := range in.GetTargetUidList() {
			wg.Add(1)
			// 送礼
			req := &present_middleware.ImSendPresentReq{
				SendUid:    sendInfo.SendPresentUser.GetUid(),
				TargetUid:  item,
				SendSource: in.GetSendSource(),
				ItemSource: in.GetItemSource(),
				SendMethod: uint32(present_middleware.PresentSendMethodType_PRESENT_TYPE_IM),
				SendType:   in.GetSendType(),
				IsOptValid: sendInfo.IsOpValid, // 这里需要判断
				ItemId:     in.GetItemId(),
				Count:      in.GetCount(),
				SourceId:   in.GetSourceId(),
				AppId:      sendInfo.GetBaseReq().GetAppId(),
				MarketId:   sendInfo.GetBaseReq().GetMarketId(),
				ServiceInfo: &present_middleware.ServiceCtrlInfo{
					ClientIp:      strconv.Itoa(int(sendInfo.GetServiceInfo().ClientIP)),
					ClientType:    uint32(sendInfo.GetServiceInfo().ClientType),
					ClientVersion: sendInfo.GetServiceInfo().ClientVersion,
					DeviceId:      sendInfo.GetServiceInfo().DeviceID,
					TerminalType:  sendInfo.GetServiceInfo().TerminalType,
				},
				PresentTextType: in.GetPresentTextType(),
				ShowImPreEffect: sendInfo.ImSendPresentInfo.IsBirthDayPresent,
				PreEffectText:   in.GetPreEffectText(),
			}
			item := item
			go func() {
				defer wg.Done()
				resp, sErr := client.PresentMiddlewareCli.ImSendPresent(ctx, req)
				if sErr != nil {
					err = sErr
					log.ErrorWithCtx(ctx, "ChannelPresentSend SendPresent fail , req: %v , err: %v", in, err)
					return
				}
				respMap[item] = resp
			}()
		}

		wg.Wait()

		if len(respMap) == 0 {
			log.ErrorWithCtx(ctx, "resp map is nil")
			return out, err
		}

		out = fillImSendRespByMap(in, respMap)
	}

	return out, err
}

func fillImSendResp(in *presentPB_.IMSendPresentReq, resp *present_middleware.ImSendPresentResp) *presentPB_.IMSendPresentResp {
	// 填充回包
	out := &presentPB_.IMSendPresentResp{
		ItemId:                  in.GetItemId(),
		MemberContributionAdded: resp.GetMemberContributionAdded(),
		Count:                   in.GetCount(),
		CurTbeans:               resp.GetCurTbeans(),
		ItemSource:              resp.GetItemSource(),
		SourceId:                resp.GetSourceId(),
		SourceRemain:            resp.GetSourceRemain(),
	}

	msgInfo := &presentPB_.PresentSendMsg{
		ItemInfo: &presentPB_.PresentSendItemInfo{
			ItemId:            resp.MsgInfo.GetItemInfo().GetItemId(),
			Count:             resp.MsgInfo.GetItemInfo().GetCount(),
			ShowEffect:        resp.MsgInfo.GetItemInfo().GetShowEffect(),
			ShowEffectV2:      resp.MsgInfo.GetItemInfo().GetShowEffectV2(),
			FlowId:            resp.MsgInfo.GetItemInfo().GetFlowId(),
			IsBatch:           resp.MsgInfo.GetItemInfo().GetIsBatch(),
			ShowBatchEffect:   resp.MsgInfo.GetItemInfo().GetShowBatchEffect(),
			SendType:          resp.MsgInfo.GetItemInfo().GetSendType(),
			DynamicTemplateId: resp.MsgInfo.GetItemInfo().GetDynamicTemplateId(),
			IsVisibleToSender: resp.MsgInfo.GetItemInfo().IsVisibleToSender,
			CustomTextJson:    resp.MsgInfo.GetItemInfo().GetCustomTextJson(),
		},
		SendTime:       resp.MsgInfo.GetSendTime(),
		ChannelId:      resp.MsgInfo.GetChannelId(),
		SendUid:        resp.MsgInfo.GetSendUid(),
		SendAccount:    resp.MsgInfo.GetSendAccount(),
		SendNickname:   resp.MsgInfo.GetSendNickname(),
		TargetUid:      resp.MsgInfo.GetTargetUid(),
		TargetAccount:  resp.MsgInfo.GetTargetAccount(),
		TargetNickname: resp.MsgInfo.GetTargetNickname(),
		ExtendJson:     resp.MsgInfo.GetExtendJson(),
	}

	out.MsgInfo = msgInfo
	return out
}

func fillImSendRespByMap(in *presentPB_.IMSendPresentReq, respMap map[uint32]*present_middleware.ImSendPresentResp) *presentPB_.IMSendPresentResp {
	// 填充回包
	resp := &present_middleware.ImSendPresentResp{}
	for _, item := range in.GetTargetUidList() {
		if respMap[item] != nil {
			resp = respMap[item]
			break
		}
	}

	out := &presentPB_.IMSendPresentResp{
		ItemId:                  in.GetItemId(),
		MemberContributionAdded: resp.GetMemberContributionAdded(),
		Count:                   in.GetCount(),
		ItemSource:              resp.GetItemSource(),
		SourceId:                resp.GetSourceId(),
	}

	msgInfo := &presentPB_.PresentSendMsg{
		ItemInfo: &presentPB_.PresentSendItemInfo{
			ItemId:            resp.MsgInfo.GetItemInfo().GetItemId(),
			Count:             resp.MsgInfo.GetItemInfo().GetCount(),
			ShowEffect:        resp.MsgInfo.GetItemInfo().GetShowEffect(),
			ShowEffectV2:      resp.MsgInfo.GetItemInfo().GetShowEffectV2(),
			FlowId:            resp.MsgInfo.GetItemInfo().GetFlowId(),
			IsBatch:           resp.MsgInfo.GetItemInfo().GetIsBatch(),
			ShowBatchEffect:   resp.MsgInfo.GetItemInfo().GetShowBatchEffect(),
			SendType:          resp.MsgInfo.GetItemInfo().GetSendType(),
			DynamicTemplateId: resp.MsgInfo.GetItemInfo().GetDynamicTemplateId(),
			IsVisibleToSender: resp.MsgInfo.GetItemInfo().IsVisibleToSender,
			CustomTextJson:    resp.MsgInfo.GetItemInfo().GetCustomTextJson(),
			ShowImPreEffect:   resp.MsgInfo.GetItemInfo().GetShowImPreEffect(),
			PreEffectText:     resp.MsgInfo.GetItemInfo().GetPreEffectText(),
		},
		SendTime:       resp.MsgInfo.GetSendTime(),
		ChannelId:      resp.MsgInfo.GetChannelId(),
		SendUid:        resp.MsgInfo.GetSendUid(),
		SendAccount:    resp.MsgInfo.GetSendAccount(),
		SendNickname:   resp.MsgInfo.GetSendNickname(),
		TargetUid:      resp.MsgInfo.GetTargetUid(),
		TargetAccount:  resp.MsgInfo.GetTargetAccount(),
		TargetNickname: resp.MsgInfo.GetTargetNickname(),
		ExtendJson:     resp.MsgInfo.GetExtendJson(),
	}

	curTbean := resp.GetCurTbeans()
	sourceRemain := resp.GetSourceRemain()

	for _, item := range respMap {
		if item.GetCurTbeans() < curTbean {
			curTbean = item.GetCurTbeans()
		}

		if item.GetSourceRemain() < sourceRemain {
			sourceRemain = item.GetSourceRemain()
		}
	}

	out.CurTbeans = curTbean
	out.SourceRemain = sourceRemain

	out.MsgInfo = msgInfo
	return out
}

// 将AiSendPresentResp 转为 IMSendPresentResp（取第一个msg_info用于IM回包）
func fillImSendRespFromAi(in *presentPB_.IMSendPresentReq, resp *present_middleware.AiSendPresentResp) *presentPB_.IMSendPresentResp {
	out := &presentPB_.IMSendPresentResp{
		ItemId:       in.GetItemId(),
		Count:        resp.GetCount(),
		CurTbeans:    uint64(resp.GetCurTbeans()),
		ItemSource:   resp.GetItemSource(),
		SourceId:     resp.GetSourceId(),
		SourceRemain: resp.GetSourceRemain(),
	}
	if len(resp.GetMsgInfo()) > 0 && resp.GetMsgInfo()[0] != nil {
		mi := resp.GetMsgInfo()[0]
		msgInfo := &presentPB_.PresentSendMsg{
			ItemInfo: &presentPB_.PresentSendItemInfo{
				ItemId:            mi.GetItemInfo().GetItemId(),
				Count:             mi.GetItemInfo().GetCount(),
				ShowEffect:        mi.GetItemInfo().GetShowEffect(),
				ShowEffectV2:      mi.GetItemInfo().GetShowEffectV2(),
				FlowId:            mi.GetItemInfo().GetFlowId(),
				IsBatch:           mi.GetItemInfo().GetIsBatch(),
				ShowBatchEffect:   mi.GetItemInfo().GetShowBatchEffect(),
				SendType:          mi.GetItemInfo().GetSendType(),
				DynamicTemplateId: mi.GetItemInfo().GetDynamicTemplateId(),
				IsVisibleToSender: mi.GetItemInfo().GetIsVisibleToSender(),
				CustomTextJson:    mi.GetItemInfo().GetCustomTextJson(),
			},
			SendTime:       mi.GetSendTime(),
			ChannelId:      mi.GetChannelId(),
			SendUid:        mi.GetSendUid(),
			SendAccount:    mi.GetSendAccount(),
			SendNickname:   mi.GetSendNickname(),
			TargetUid:      mi.GetTargetUid(),
			TargetAccount:  mi.GetTargetAccount(),
			TargetNickname: mi.GetTargetNickname(),
			ExtendJson:     mi.GetExtendJson(),
		}
		out.MsgInfo = msgInfo
	}
	return out
}
