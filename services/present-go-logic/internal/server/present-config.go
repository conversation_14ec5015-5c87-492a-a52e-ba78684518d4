package server

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/ttversion"
	ga "golang.52tt.com/protocol/app"
	pb "golang.52tt.com/protocol/app/present-go-logic"
	"golang.52tt.com/protocol/app/sync"
	presentPB_ "golang.52tt.com/protocol/app/userpresent"
	"golang.52tt.com/protocol/common/status"
	present_set "golang.52tt.com/protocol/services/present-set"
	"golang.52tt.com/protocol/services/userpresent"
	userpresent_go "golang.52tt.com/protocol/services/userpresent-go"
	client "golang.52tt.com/services/present-go-logic/internal/rpc"
	"sort"
	"strings"
	"time"
)

const PresentEffectExpiredSec = 3600

func (s *PresentGoLogic_) GetPresentConfigList(ctx context.Context, req *presentPB_.GetPresentConfigListReq) (*presentPB_.GetPresentConfigListResp, error) {
	out := &presentPB_.GetPresentConfigListResp{}

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetPresentConfigList ServiceInfoFromContext fail. ctx:%+v", ctx)
		return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	uid := serviceInfo.UserID

	updateTime, presentMap := s.presentConfigMemCache.GetConfigList()

	testType := checkIsTestUid(uid)

	out.LastUpdateTime = updateTime
	out.ConfigList = make([]*ga.PresentItemConfig, 0)
	for _, item := range presentMap {
		if item.GetExtend().GetIsTest() && testType == 0 {
			continue
		}
		out.ConfigList = append(out.ConfigList, tranConfigToLogic(item, uint32(serviceInfo.ClientType), s.emperorSetCache.GetEmperorPresentList()))
	}

	sort.Slice(out.ConfigList, func(i, j int) bool {
		if out.ConfigList[i].GetRankFloat() == out.ConfigList[j].GetRankFloat() {
			return out.ConfigList[i].GetItemId() < out.ConfigList[j].GetItemId()
		}
		return out.ConfigList[i].GetRankFloat() < out.ConfigList[j].GetRankFloat()
	})

	return out, nil
}

func (s *PresentGoLogic_) GetPresentConfigById(ctx context.Context, req *presentPB_.GetPresentConfigByIdReq) (*presentPB_.GetPresentConfigByIdResp, error) {
	out := &presentPB_.GetPresentConfigByIdResp{}

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetPresentConfigById ServiceInfoFromContext fail. ctx:%+v", ctx)
		return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	uid := serviceInfo.UserID

	presentCfg := s.presentConfigMemCache.GetConfigById(req.GetItemId())
	testType := checkIsTestUid(uid)

	// 不是测试礼物，或者是测试账号
	if !presentCfg.GetExtend().GetIsTest() || testType != 0 {
		out.ItemConfig = tranConfigToLogic(presentCfg, uint32(serviceInfo.ClientType), s.emperorSetCache.GetEmperorPresentList())
	}

	return out, nil
}

func (s *PresentGoLogic_) PresentGetFlowConfigList(ctx context.Context, req *presentPB_.GetPresentFlowConfigListReq) (*presentPB_.GetPresentFlowConfigListResp, error) {
	out := &presentPB_.GetPresentFlowConfigListResp{}

	updateTime, presentMap := s.presentConfigMemCache.GetFlowConfigList()

	out.LastUpdateTime = updateTime
	if updateTime == req.GetLastUpdateTime() {
		return out, nil
	}

	out.ConfigList = make([]*ga.PresentFlowConfig, 0)
	for _, item := range presentMap {
		out.ConfigList = append(out.ConfigList, tranFlowConfigToLogic(item))
	}

	log.DebugWithCtx(ctx, "PresentGetFlowConfigList resp len %v", len(out.GetConfigList()))

	return out, nil
}

func (s *PresentGoLogic_) GetPresentFlowConfigById(ctx context.Context, req *presentPB_.GetPresentFlowConfigByIdReq) (*presentPB_.GetPresentFlowConfigByIdResp, error) {
	out := &presentPB_.GetPresentFlowConfigByIdResp{}

	presentCfg := s.presentConfigMemCache.GetFlowConfigById(req.GetFlowId())

	out.FlowConfig = tranFlowConfigToLogic(presentCfg)

	log.DebugWithCtx(ctx, "GetPresentFlowConfigById resp %v", out.GetFlowConfig())

	return out, nil
}

func (s *PresentGoLogic_) GetPresentDynamicTemplateConfig(ctx context.Context, req *presentPB_.GetPresentDynamicTemplateConfigReq) (
	*presentPB_.GetPresentDynamicTemplateConfigResp, error) {
	resp := s.presentConfigMemCache.GetDynamicEffectConfig()

	out := tranDynamicConfigToLogic(resp)

	return out, nil

}

func (s *PresentGoLogic_) GetDrawPresentPara(ctx context.Context, req *presentPB_.GetDrawPresentParaReq) (*presentPB_.GetDrawPresentParaResp, error) {
	out := &presentPB_.GetDrawPresentParaResp{}

	out.ParaList = make([]*presentPB_.DrawPresentPara, 0)
	for _, item := range s.drawGameConf.CongfigList {
		out.ParaList = append(out.ParaList, &presentPB_.DrawPresentPara{
			ItemId: item.ItemId,
			ImgUrl: item.ImgUrl,
		})
	}

	return out, nil
}

func (s *PresentGoLogic_) GetNamingPresentConfigList(ctx context.Context, req *presentPB_.GetNamingPresentConfigListReq) (*presentPB_.GetNamingPresentConfigListResp, error) {
	out := &presentPB_.GetNamingPresentConfigListResp{}
	out.ConfigList = make([]*presentPB_.NamingPresentConfig, 0)

	namingConfig := s.presentConfigMemCache.GetNamingPresentConfigList()
	for _, item := range namingConfig {
		out.ConfigList = append(out.ConfigList, &presentPB_.NamingPresentConfig{
			Uid:           item.GetUid(),
			GiftId:        item.GetGiftId(),
			NamingContent: item.GetNamingContent(),
			Account:       item.GetAccount(),
		})
	}

	return out, nil
}

func (s *PresentGoLogic_) GetImPresentItemIdList(ctx context.Context, req *presentPB_.GetImPresentItemIdListReq) (*presentPB_.GetImPresentItemIdListResp, error) {
	out := &presentPB_.GetImPresentItemIdListResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetImPresentItemIdList ServiceInfoFromContext fail. ctx:%+v", ctx)
		return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	//因为ios误发了一个包，需要过滤一下旧版本
	if serviceInfo.ClientType == protocol.ClientTypeIOS {
		if !ttversion.ImPresentFeature.Atleast(serviceInfo.ClientType, serviceInfo.ClientVersion) {
			log.ErrorWithCtx(ctx, "GetImPresentItemIdList version check fail. ctx:%+v", ctx)
			return out, protocol.NewExactServerError(nil, status.ErrCircleAnnouncementVersion)
		}
	}

	out.ItemIdList = s.imPresentConf.ImItemId
	return out, nil
}

func (s *PresentGoLogic_) GetStangerImItemIdList(ctx context.Context, req *presentPB_.GetStangerImItemIdListReq) (*presentPB_.GetStangerImItemIdListResp, error) {
	out := &presentPB_.GetStangerImItemIdListResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetStangerImItemIdList ServiceInfoFromContext fail. ctx:%+v", ctx)
		return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	//因为ios误发了一个包，需要过滤一下旧版本
	if serviceInfo.ClientType == protocol.ClientTypeIOS {
		if !ttversion.ImPresentFeature.Atleast(serviceInfo.ClientType, serviceInfo.ClientVersion) {
			log.ErrorWithCtx(ctx, "GetStangerImItemIdList version check fail. ctx:%+v", ctx)
			return out, protocol.NewExactServerError(nil, status.ErrCircleAnnouncementVersion)
		}
	}

	out.ItemIdList = s.imPresentConf.StrangeItemId
	return out, nil
}

type effectUrlJson struct {
	Url  string `json:"url"`
	Md5  string `json:"md5"`
	VUrl string `json:"v_url"`
	VMd5 string `json:"v_md5"`
}

func tranConfigToLogic(config *userpresent.StPresentItemConfig, clientType uint32, emperorPresentMap map[uint32]*present_set.EmperorSetPresentItem) *ga.PresentItemConfig {
	// 填充基础信息
	newConfig := &ga.PresentItemConfig{
		ItemId:             config.GetItemId(),
		Name:               config.GetName(),
		IconUrl:            config.GetIconUrl(),
		Price:              config.GetPrice(),
		Score:              config.GetScore(),
		Charm:              config.GetCharm(),
		Rank:               config.GetRank(),
		EffectBegin:        config.GetEffectBegin(),
		EffectEnd:          config.GetEffectEnd(),
		MsgIconUrl:         toPresentMsgIconUrl(config.GetIconUrl()),
		PriceType:          config.GetPriceType(),
		RichValue:          config.GetRichValue(),
		IsDel:              config.GetIsDel(),
		ShowBatchOption:    !config.GetExtend().GetUnshowBatchOption(),
		ItemTag:            config.GetExtend().GetTag(),
		NobilityLevel:      config.GetExtend().GetNobilityLevel(),
		UnshowPresentShelf: config.GetExtend().GetUnshowPresentShelf(),
		ShowEffectEnd:      config.GetExtend().GetShowEffectEnd(),
		EffectEndDelay:     config.GetExtend().GetEffectEndDelay(),
		RankFloat:          config.GetRankFloat(),
		IsBoxBreaking:      config.GetExtend().GetIsBoxBreaking(),
		FansLevel:          config.GetExtend().GetFansLevel(),
	}

	// 特效信息，礼物架上的或者配置了强制赠送属性的礼物才会有

	if clientType == protocol.ClientTypeIOS && config.GetUpdateTime() >= 1504195200 {
		newConfig.VideoEffectUrl = config.GetExtend().GetIosExtend().GetVideoEffectUrl()
	} else {
		newConfig.VideoEffectUrl = config.GetExtend().GetVideoEffectUrl()
	}

	newConfig.EffectUrlList = make([]*ga.PresentEffectInfo, 0)
	// 新的特效map

	// 麦位延展特效
	if config.GetExtend().GetMicEffectUrl() != "" {
		newConfig.EffectUrlList = append(newConfig.EffectUrlList, &ga.PresentEffectInfo{
			Url:            config.GetExtend().GetMicEffectUrl(),
			Md5:            config.GetExtend().GetMicEffectMd5(),
			EffectType:     uint32(ga.PRESENT_EFFECT_TYPE_PRESENT_EFFECT_TYPE_MIC),
			EffectFileType: uint32(ga.PRESENT_EFFECT_FILE_TYPE_PRESENT_EFFECT_FILE_TYPE_FRAME),
		})
	}

	// 普通特效
	effect := &effectUrlJson{}
	_ = json.Unmarshal(config.GetExtend().GetVideoEffectUrl(), effect)

	mainEffect := &ga.PresentEffectInfo{
		EffectType: uint32(ga.PRESENT_EFFECT_TYPE_PRESENT_EFFECT_TYPE_MAIN),
	}
	if effect.VUrl != "" {
		mainEffect.Url = effect.VUrl
		mainEffect.Md5 = effect.VMd5
		mainEffect.EffectFileType = uint32(ga.PRESENT_EFFECT_FILE_TYPE_PRESENT_EFFECT_FILE_TYPE_VAP)
	} else {
		mainEffect.Url = effect.Url
		mainEffect.Md5 = effect.Md5
		mainEffect.EffectFileType = uint32(ga.PRESENT_EFFECT_FILE_TYPE_PRESENT_EFFECT_FILE_TYPE_LOTTIE)
	}

	// 如果有小礼物vap特效，优先使用该特效
	if config.GetExtend().GetSmallVapUrl() != "" {
		mainEffect.Url = config.GetExtend().GetSmallVapUrl()
		mainEffect.Md5 = config.GetExtend().GetSmallVapMd5()
		mainEffect.EffectFileType = uint32(ga.PRESENT_EFFECT_FILE_TYPE_PRESENT_EFFECT_FILE_TYPE_VAP)
	}

	if mainEffect.GetUrl() != "" {
		newConfig.EffectUrlList = append(newConfig.EffectUrlList, mainEffect)
	}

	return newConfig
}

func toPresentMsgIconUrl(iconUrl string) string {
	if len(iconUrl) == 0 {
		return ""
	}
	var dest string
	dest += iconUrl
	if strings.Contains(iconUrl, "?") {
		dest += "&imageView2/0/w/24/h/24"
	} else {
		dest += "?imageView2/0/w/24/h/24"
	}
	return dest
}

func checkIsTestUid(uid uint32) uint32 {
	presentFreeUid := []uint32{2456324, 1023278, 3552970, 5414063, 2363704}
	presentPayUid := []uint32{12278487}

	for _, item := range presentFreeUid {
		if uid == item {
			// 送礼免费账号
			return 1
		}
	}

	for _, item := range presentPayUid {
		if uid == item {
			// 支付账号
			return 2
		}
	}

	return 0

}

func tranFlowConfigToLogic(flowConfig *userpresent.StPresentFlowConfig) *ga.PresentFlowConfig {
	return &ga.PresentFlowConfig{
		FlowId: flowConfig.GetFlowId(),
		Url:    flowConfig.GetUrl(),
		Md5:    flowConfig.GetMd5(),
	}
}

func tranDynamicConfigToLogic(flowConfig *userpresent.GetPresentDynaminEffectTemplateConfigResp) *presentPB_.GetPresentDynamicTemplateConfigResp {
	resp := &presentPB_.GetPresentDynamicTemplateConfigResp{Configs: &ga.PresentTemplateConfigs{}}

	templateMap := make(map[uint32]*userpresent.DynamicEffectTemplate)
	for _, item := range flowConfig.GetTemplateList() {
		templateMap[item.GetId()] = item
	}
	effectMap := make(map[uint32][]*userpresent.PresentEffectTemplateConfig)
	for _, item := range flowConfig.GetPresentEffectList() {
		effectMap[item.GetPresentId()] = append(effectMap[item.GetPresentId()], item)
	}

	resp.Configs.EffectTemplateList = make([]*ga.DynamicEffectTemplate, 0)
	for _, item := range templateMap {
		resp.Configs.EffectTemplateList = append(resp.Configs.EffectTemplateList, &ga.DynamicEffectTemplate{
			Id:      item.GetId(),
			Icon:    item.GetIcon(),
			Url:     item.GetUrl(),
			Md5:     item.GetMd5(),
			BgColor: item.GetBgColor(),
		})
	}

	for id, item := range effectMap {
		tmp := &ga.PresentDynamicTemplate{PresentId: id}
		tmp.TemConfigs = make([]*ga.TemplateParaConfig, 0)
		for _, cfg := range item {
			tmp.TemConfigs = append(tmp.TemConfigs, &ga.TemplateParaConfig{
				PresentCnt: cfg.GetPresentCnt(),
				TemplateId: cfg.GetTemplateId(),
			})
		}
		resp.GetConfigs().ConfigList = append(resp.GetConfigs().ConfigList, tmp)
	}

	resp.GetConfigs().ConfigVersion = flowConfig.GetInfoVersion()

	return resp
}

func (s *PresentGoLogic_) PresentConfigSync(ctx context.Context, req *pb.PresentConfigSyncReq) (*pb.PresentConfigSyncResp, error) {
	out := &pb.PresentConfigSyncResp{}
	out.ConfigList = make([]*ga.PresentItemConfig, 0)
	out.EnterBlackList = make([]*pb.PresentEnterBlacklist, 0)

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetPresentConfigById ServiceInfoFromContext fail. ctx:%+v", ctx)
		return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	resp, err := client.PresentGoCli.GetPresentConfigListV3(ctx, &userpresent_go.GetPresentConfigListV3Req{
		UpdateTime: req.GetLastUpdateTime(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PresentConfigSync GetPresentConfigList fail. err:%+v", err)
		return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	testType := checkIsTestUid(serviceInfo.UserID)
	emperorPresentMap := s.emperorSetCache.GetEmperorPresentList()

	for _, item := range resp.GetItemList() {
		if item.GetBaseConfig().GetIsTest() && testType == 0 {
			continue
		}
		out.ConfigList = append(out.ConfigList, tranPresentGoConfigToLogic(item, serviceInfo.ClientType, emperorPresentMap))
	}

	for _, item := range resp.GetEnterBlackList() {
		itemList := make([]uint32, 0)
		itemList = append(itemList, item.GetGiftItemList()...)
		out.EnterBlackList = append(out.EnterBlackList, &pb.PresentEnterBlacklist{
			EnterType:    pb.PresentEnterType(item.GetEnterType()),
			GiftItemList: itemList,
		})
	}

	out.LastUpdateTime = resp.GetLastUpdateTime()

	out.FlowSync = &sync.AdvancedConfigPresentFlowSync{}
	out.FlowSync.ConfigUpdateTime, _ = s.presentConfigMemCache.GetFlowConfigList()
	if req.GetFlowUpdateTime() < s.presentConfigMemCache.GetFlowConfigUpdateTime() && !req.GetOnlyPresent() {
		_, flowCfg := s.presentConfigMemCache.GetFlowConfigList()
		out.FlowSync.FlowList = make([]*ga.PresentFlowConfig, 0)
		for _, item := range flowCfg {
			out.FlowSync.FlowList = append(out.FlowSync.FlowList, tranFlowConfigToLogic(item))
		}
	}

	out.DynamicTemplateSync = &sync.AdvancedConfigPresentDynamicTemplateSync{}
	out.DynamicTemplateSync.ConfigUpdateTime = s.presentConfigMemCache.GetDynamicEffectConfig().GetInfoVersion()
	if req.GetDynamicUpdateTime() < s.presentConfigMemCache.GetDynamicEffectConfig().GetInfoVersion() && !req.GetOnlyPresent() {
		tmpDynamicCfg := tranDynamicConfigToLogic(s.presentConfigMemCache.GetDynamicEffectConfig())
		out.DynamicTemplateSync.Configs = tmpDynamicCfg.GetConfigs()
	}

	if req.WithEmperorSet {
		lastUpdate, emperorCfg := s.emperorSetCache.GetConfigList()
		out.EmperorSetConfig = &pb.EmperorSetConfigSync{
			EmperorSetConfig: make([]*pb.EmperorSetConfig, 0),
			LastUpdateTime:   lastUpdate,
		}

		for _, item := range emperorCfg {
			presentIdList := make([]uint32, 0)
			for _, present := range item.GetPresentList() {
				presentIdList = append(presentIdList, present.GetPresentId())
			}

			if item.GetUpdateTime() > req.GetEmperorSetUpdateTime() {
				out.EmperorSetConfig.EmperorSetConfig = append(out.EmperorSetConfig.EmperorSetConfig, &pb.EmperorSetConfig{
					EmperorSetId:            item.GetSetId(),
					EmperorSetName:          item.GetSetName(),
					EmperorSetIcon:          item.GetIconUrl(),
					EffectBegin:             item.GetStartTime(),
					EffectEnd:               item.GetEndTime(),
					IsShow:                  item.GetShowPresentShelf(),
					ShowEffectEnd:           item.GetShowEffectEnd(),
					Rank:                    float32(item.GetRank()),
					FloatJumpType:           pb.EmperorSetConfig_EmperorSetJumpType(item.GetBannerJumpType()),
					FloatImageUrl:           item.GetBannerResUrl(),
					FloatJumpUrl:            item.GetBannerJumpUrl(),
					TotalPrice:              item.GetPresentsTotalPrice(),
					EmperorEffectUrl:        item.GetEffect().GetAndroidEffectUrl(),
					EmperorEffectMd5:        item.GetEffect().GetAndroidEffectMd5(),
					PresentIdList:           presentIdList,
					UpdateTime:              item.GetUpdateTime(),
					IsDelete:                item.GetIsDel(),
					IsBoxBreaking:           item.GetEffect().GetIsBreakingBox(),
					EmperorViewingSourceUrl: item.GetEffect().GetEmperorViewingSourceUrl(),
					EmperorViewingSourceMd5: item.GetEffect().GetEmperorViewingSourceMd5(),
					EmperorFlowSourceUrl:    item.GetEffect().GetEmperorFlowSourceUrl(),
					EmperorFlowSourceMd5:    item.GetEffect().GetEmperorFlowSourceMd5(),
				})
			}
		}
	}

	log.DebugWithCtx(ctx, "PresentConfigSync success. req:%+v, resp:%+v", req, len(out.ConfigList))

	return out, nil
}

func tranPresentGoConfigToLogic(config *userpresent_go.PresentConfigNew, clientType uint16, emperorPresentMap map[uint32]*present_set.EmperorSetPresentItem) *ga.PresentItemConfig {
	// 填充基础信息
	newConfig := &ga.PresentItemConfig{
		ItemId:             config.GetBaseConfig().GetItemId(),
		Name:               config.GetBaseConfig().GetName(),
		IconUrl:            config.GetBaseConfig().GetIconUrl(),
		Price:              config.GetBaseConfig().GetPrice(),
		Score:              config.GetBaseConfig().GetScore(),
		Charm:              config.GetBaseConfig().GetCharm(),
		Rank:               uint32(config.GetEnterConfig().GetRank()),
		EffectBegin:        config.GetEnterConfig().GetEffectBegin(),
		EffectEnd:          config.GetEnterConfig().GetEffectEnd(),
		MsgIconUrl:         toPresentMsgIconUrl(config.GetBaseConfig().GetIconUrl()),
		PriceType:          config.GetBaseConfig().GetPriceType(),
		RichValue:          config.GetBaseConfig().GetRichValue(),
		IsDel:              config.GetBaseConfig().GetIsDel(),
		ShowBatchOption:    !config.GetEnterConfig().GetUnshowBatchOption(),
		ItemTag:            config.GetEnterConfig().GetTag(),
		NobilityLevel:      config.GetEnterConfig().GetNobilityLevel(),
		UnshowPresentShelf: config.GetEnterConfig().GetUnshowPresentShelf(),
		ShowEffectEnd:      config.GetEnterConfig().GetShowEffectEnd(),
		EffectEndDelay:     config.GetEnterConfig().GetEffectEndDelay(),
		RankFloat:          config.GetEnterConfig().GetRank(),
		IsBoxBreaking:      config.GetEffectConfig().GetIsBoxBreaking(),
		FansLevel:          config.GetEnterConfig().GetFansLevel(),
	}

	// 特效信息，礼物架上的或者配置了强制赠送属性的礼物才会有
	// 20240716 新增套装礼物不过滤
	// 20240801 新增帝王套礼物不过滤
	now := uint32(time.Now().Unix())
	if now <= newConfig.GetEffectEnd()+PresentEffectExpiredSec || config.GetBaseConfig().GetForceSendable() ||
		config.EnterConfig.GetTag() == uint32(ga.PresentTagType_PRESENT_TAG_SET) || emperorPresentMap[newConfig.GetItemId()] != nil {

		if clientType == protocol.ClientTypeIOS && config.GetBaseConfig().GetUpdateTime() >= 1504195200 {
			newConfig.VideoEffectUrl = config.GetEffectConfig().GetIosVideoEffectUrl()
		} else {
			newConfig.VideoEffectUrl = config.GetEffectConfig().GetVideoEffectUrl()
		}

		newConfig.VideoEffectUrl = config.GetEffectConfig().GetVideoEffectUrl()
		newConfig.EffectUrlList = make([]*ga.PresentEffectInfo, 0)

		// 麦位延展特效
		if config.GetEffectConfig().GetMicEffectUrl() != "" {
			newConfig.EffectUrlList = append(newConfig.EffectUrlList, &ga.PresentEffectInfo{
				Url:            config.GetEffectConfig().GetMicEffectUrl(),
				Md5:            config.GetEffectConfig().GetMicEffectMd5(),
				EffectType:     uint32(ga.PRESENT_EFFECT_TYPE_PRESENT_EFFECT_TYPE_MIC),
				EffectFileType: uint32(ga.PRESENT_EFFECT_FILE_TYPE_PRESENT_EFFECT_FILE_TYPE_FRAME),
			})
		}

		// 普通特效
		effect := &effectUrlJson{}
		_ = json.Unmarshal(config.GetEffectConfig().GetVideoEffectUrl(), effect)

		mainEffect := &ga.PresentEffectInfo{
			EffectType: uint32(ga.PRESENT_EFFECT_TYPE_PRESENT_EFFECT_TYPE_MAIN),
		}
		if effect.VUrl != "" {
			mainEffect.Url = effect.VUrl
			mainEffect.Md5 = effect.VMd5
			mainEffect.EffectFileType = uint32(ga.PRESENT_EFFECT_FILE_TYPE_PRESENT_EFFECT_FILE_TYPE_VAP)
		} else {
			mainEffect.Url = effect.Url
			mainEffect.Md5 = effect.Md5
			mainEffect.EffectFileType = uint32(ga.PRESENT_EFFECT_FILE_TYPE_PRESENT_EFFECT_FILE_TYPE_LOTTIE)
		}

		// 如果有小礼物vap特效，优先使用该特效
		if config.GetEffectConfig().GetSmallVapUrl() != "" {
			mainEffect.Url = config.GetEffectConfig().GetSmallVapUrl()
			mainEffect.Md5 = config.GetEffectConfig().GetSmallVapMd5()
			mainEffect.EffectFileType = uint32(ga.PRESENT_EFFECT_FILE_TYPE_PRESENT_EFFECT_FILE_TYPE_VAP)
		}

		if mainEffect.GetUrl() != "" {
			newConfig.EffectUrlList = append(newConfig.EffectUrlList, mainEffect)
		}
	}

	return newConfig
}
