package server

import (
	"context"
	"github.com/golang/mock/gomock"
	User "golang.52tt.com/clients/account-go"
	account2 "golang.52tt.com/clients/mocks/account-go"
	headimage "golang.52tt.com/clients/mocks/headimage"
	userPresentMock "golang.52tt.com/clients/mocks/userpresent"
	present_extra_conf "golang.52tt.com/clients/present-extra-conf"
	userpresent_go "golang.52tt.com/clients/userpresent-go"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/present-go-logic"
	presentPB_ "golang.52tt.com/protocol/app/userpresent"
	"golang.52tt.com/protocol/services/userpresent"
	userpresent_go2 "golang.52tt.com/protocol/services/userpresent-go"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	"golang.52tt.com/services/present-go-logic/internal/conf"
	"golang.52tt.com/services/present-go-logic/internal/model/present_config_cache"
	"golang.52tt.com/services/present-go-logic/internal/rpc"
	"sync"
	"testing"
	"time"
)

func SetupBasePresentCli(ctl *gomock.Controller) *userPresentMock.MockIClient {
	presentCli := userPresentMock.NewMockIClient(ctl)
	presentCli.EXPECT().GetPresentConfigUpdateTime(gomock.Any(), gomock.Any()).Return(&userpresent.GetPresentConfigUpdateTimeResp{
		UpdateTime: 10000,
	}, nil)
	presentCli.EXPECT().GetPresentConfigListV2ByUpdateTime(gomock.Any(), gomock.Any()).Return(&userpresent.GetPresentConfigListV2Resp{
		UpdateTime: 10000,
		ItemList:   []*userpresent.StPresentItemConfig{{ItemId: 1, Price: 10, Extend: &userpresent.StPresentItemConfigExtend{ForceSendable: true}}},
	}, nil)
	presentCli.EXPECT().GetPresentFlowConfigUpdateTime(gomock.Any(), gomock.Any()).Return(&userpresent.GetPresentFlowConfigUpdateTimeResp{
		UpdateTime: 10000,
	}, nil)
	presentCli.EXPECT().GetPresentFlowConfigList(gomock.Any(), gomock.Any()).Return(&userpresent.GetPresentFlowConfigListResp{
		FlowList: []*userpresent.StPresentFlowConfig{{
			FlowId:     1,
			Url:        "aaa",
			Md5:        "bbb",
			UpdateTime: 10000,
			CreateTime: 0,
			Desc:       "ccc",
		}},
	}, nil)
	presentCli.EXPECT().GetValidNamingPresentInfos(gomock.Any(), gomock.Any()).Return(&userpresent.GetValidNamingPresentInfosResp{
		InfoList: []*userpresent.NamingPresentInfo{{
			Id:            1,
			Uid:           2202538,
			GiftId:        1,
			NamingContent: "aaa",
			BeginTs:       0,
			EndTs:         0,
			Account:       "ccc",
		}},
	}, nil)
	presentCli.EXPECT().GetPresentDynaminEffectTemplateConfig(gomock.Any(), gomock.Any()).Return(&userpresent.GetPresentDynaminEffectTemplateConfigResp{
		TemplateList:      []*userpresent.DynamicEffectTemplate{{Id: 1}},
		PresentEffectList: []*userpresent.PresentEffectTemplateConfig{{Id: 1, TemplateId: 1, PresentId: 1}},
		InfoVersion:       10000,
	}, nil)
	return presentCli
}

func SetupForDetail(ctl *gomock.Controller) (err error) {
	presentCli := SetupBasePresentCli(ctl)
	presentCli.EXPECT().GetUserPresentDetailListNew(gomock.Any(), gomock.Any()).Return(&userpresent.GetUserPresentDetailListResp{
		DetailList: []*userpresent.StUserPresentDetail{{
			FromUid:     2402972,
			TargetUid:   2202538,
			ItemConfig:  &userpresent.StPresentItemConfig{ItemId: 1},
			ReceiveTime: uint32(time.Now().Unix()),
			ItemCount:   1,
			AddCharm:    10,
			OrderId:     "test",
			ItemId:      1,
			Score:       0,
			SendSource:  0,
			SendMethod:  0,
			AddRich:     10,
			IsUkw:       true,
		}},
	}, nil)

	presentCli.EXPECT().GetUserPresentSendDetailList(gomock.Any(), gomock.Any()).Return(&userpresent.GetUserPresentSendDetailListResp{
		DetailList: []*userpresent.StUserPresentDetail{{
			FromUid:     2202538,
			TargetUid:   2402972,
			ItemConfig:  &userpresent.StPresentItemConfig{ItemId: 1},
			ReceiveTime: uint32(time.Now().Unix()),
			ItemCount:   1,
			AddCharm:    10,
			OrderId:     "test",
			ItemId:      1,
			Score:       0,
			SendSource:  0,
			SendMethod:  0,
			AddRich:     10,
			IsUkw:       true,
		}},
	}, nil)
	account := account2.NewMockIClient(ctl)
	uid := uint32(2202538)
	name := "liang"
	to := uint32(2402972)
	toName := "liang2"

	account.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(map[uint32]*User.User{2202538: &User.User{Uid: uid, Username: name}, 2402972: &User.User{Uid: to, Username: toName}}, nil)
	client.AccountCli = account

	headMock := headimage.NewMockIClient(ctl)
	headMock.EXPECT().BatchGetHeadImageMd5(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[string]string{"liang": "test", "liang1": "test1"}, nil)
	client.HeadImageCli = headMock

	client.PresentCli = presentCli

	presentGoMockClient := userpresent_go2.NewMockUserPresentGOClient(ctl)
	presentGoMockClient.EXPECT().GetAiPresentDetail(gomock.Any(), gomock.Any()).Return(&userpresent_go2.GetAiPresentDetailResp{AiPresentDetailList: []*userpresent_go2.AiPresentDetail{}}, nil).AnyTimes()
	client.PresentGoProtoCli = presentGoMockClient

	// Mock AI soulmate client to avoid nil pointer when roleIdList is empty but code still calls BatchGetAIPartner
	aisClient := aigc_soulmate.NewMockAigcSoulmateClient(ctl)
 aisClient.EXPECT().BatchGetAIPartner(gomock.Any(), gomock.Any()).Return(&aigc_soulmate.BatchGetAIPartnerResp{PartnerMap: map[uint32]*aigc_soulmate.AIPartner{}}, nil).AnyTimes()
	client.AiSoulmateCli = aisClient
	return nil
}

func TestPresentGoLogic__GetUserPresentDetailList(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	_ = SetupForDetail(ctl)

	presentCache := present_config_cache.NewPresentMemCache()

	ctx := context.Background()
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: 2202538})

	type fields struct {
		presentCli            userpresent_go.PresentClientWrapper
		businessConf          *conf.BusinessConfManager
		sendPresentConf       *conf.SendPresentConfManager
		drawGameConf          *conf.DrawGameConf
		imPresentConf         *conf.ImPresentConf
		constellationUrl      string
		presentExtraConfCli   present_extra_conf.IClient
		presentFloatCache     []*pb.PresentFloatLayer
		presentFlashCache     []*pb.PresentFlashEffect
		flashConfigCache      []*pb.FlashEffectConfig
		presentConfigCache    map[uint32]*userpresent.StPresentItemConfig
		mapLock               sync.RWMutex
		presentConfigMemCache *present_config_cache.PresentMemCache
		lastUpdateTime        uint32
	}
	type args struct {
		ctx context.Context
		req *presentPB_.GetUserPresentDetailListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *presentPB_.GetUserPresentDetailListResp
		wantErr bool
	}{
		{name: "GetUserPresentDetailList",
			fields: fields{
				presentCli:            client.PresentCli,
				businessConf:          businessConfig,
				sendPresentConf:       sendPresentConfig,
				constellationUrl:      "",
				presentExtraConfCli:   presentExtraConfCli,
				presentFloatCache:     presentFloatCache,
				presentFlashCache:     presentFlashCache,
				flashConfigCache:      flashConfigCache,
				lastUpdateTime:        0,
				presentConfigMemCache: presentCache,
			}, args: args{
				ctx: ctx,
				req: &presentPB_.GetUserPresentDetailListReq{
					BaseReq:    nil,
					DetailType: 2,
				},
			}, wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentGoLogic_{
				businessConf:        tt.fields.businessConf,
				sendPresentConf:     tt.fields.sendPresentConf,
				drawGameConf:        tt.fields.drawGameConf,
				imPresentConf:       tt.fields.imPresentConf,
				constellationUrl:    tt.fields.constellationUrl,
				presentExtraConfCli: tt.fields.presentExtraConfCli,

				presentFlashCache:     tt.fields.presentFlashCache,
				flashConfigCache:      tt.fields.flashConfigCache,
				presentConfigCache:    tt.fields.presentConfigCache,
				mapLock:               tt.fields.mapLock,
				presentConfigMemCache: tt.fields.presentConfigMemCache,
				lastUpdateTime:        tt.fields.lastUpdateTime,
			}
			_, err := s.GetUserPresentDetailList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserPresentDetailList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
