package server

import (
	"context"
	account "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/ttversion"
	"golang.52tt.com/pkg/ttversion/android"
	"golang.52tt.com/pkg/ttversion/car"
	"golang.52tt.com/pkg/ttversion/iphone"
	"golang.52tt.com/pkg/ttversion/pc"
	"golang.52tt.com/pkg/ttversion/tx_mini"
	ga "golang.52tt.com/protocol/app"
	presentPB_ "golang.52tt.com/protocol/app/userpresent"
	"golang.52tt.com/protocol/common/status"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	"golang.52tt.com/protocol/services/userpresent"
	userpresent_go_proto "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/present-go-logic/internal/rpc"
	"sort"
)

const RecordList = 100

func (s *PresentGoLogic_) GetUserPresentDetailList(ctx context.Context, req *presentPB_.GetUserPresentDetailListReq) (*presentPB_.GetUserPresentDetailListResp, error) {
	out := &presentPB_.GetUserPresentDetailListResp{}
	out.PresentDetailList = make([]*presentPB_.UserPresentDetail, 0)
	out.PresentSendDetailList = make([]*presentPB_.UserPresentSendDetail, 0)

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetPresentConfigList ServiceInfoFromContext fail. ctx:%+v", ctx)
		return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	uid := serviceInfo.UserID
	receivePresentList := &userpresent.GetUserPresentDetailListResp{}
	sendPresentList := &userpresent.GetUserPresentSendDetailListResp{}
	var err error

	if req.GetDetailType() == 0 || req.GetDetailType() == 2 {
		receivePresentList, err = client.PresentCli.GetUserPresentDetailListNew(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserPresentDetailList GetUserPresentSendDetailList fail.err :%v", err)
			return out, err
		}
	}

	if req.GetDetailType() == 1 || req.GetDetailType() == 2 {
		sendPresentList, err = client.PresentCli.GetUserPresentSendDetailList(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserPresentDetailList GetUserPresentSendDetailList fail.err :%v", err)
			return out, err
		}

	}

	//判断是否需要返回语音匹配送出的匿名礼物
	maskCallVerion := ttversion.New("蒙面通话", android.V5_4_2, iphone.V5_4_2, car.None, pc.V1_3_0, tx_mini.V0_0_1)
	atLeastMask := maskCallVerion.Atleast(serviceInfo.ClientType, serviceInfo.ClientVersion)

	recordCount := uint32(0)

	userMap := make(map[uint32]*account.User)
	for _, item := range receivePresentList.GetDetailList() {
		userMap[item.GetFromUid()] = &account.User{}
		userMap[item.GetTargetUid()] = &account.User{}
	}
	for _, item := range sendPresentList.GetDetailList() {
		userMap[item.GetFromUid()] = &account.User{}
		userMap[item.GetTargetUid()] = &account.User{}
	}

	userList := make([]uint32, 0)
	for item := range userMap {
		userList = append(userList, item)
	}

	if len(userList) <= 100 {
		userMap, err = client.AccountCli.GetUsersMap(ctx, userList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserPresentDetailList GetUsersMap fail.err :%v", err)
			return out, err
		}
	} else {
		userMap, err = client.AccountCli.GetUsersMap(ctx, userList[:100])
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserPresentDetailList GetUsersMap fail.err :%v", err)
			return out, err
		}

		tmpMap, err := client.AccountCli.GetUsersMap(ctx, userList[100:])
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserPresentDetailList GetUsersMap fail.err :%v", err)
			return out, err
		}

		for _, item := range tmpMap {
			userMap[item.GetUid()] = item
		}
	}

	accountList := make([]string, 0)
	for _, user := range userMap {
		accountList = append(accountList, user.GetUsername())
	}
	headMap, err := client.HeadImageCli.BatchGetHeadImageMd5(ctx, uid, accountList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserPresentDetailList BatchGetHeadImageMd5 fail.err :%v", err)
		return out, err
	}

	presentDetailVersion := ttversion.New("用户礼物明细V2", android.V3_1_2, iphone.V3_0_1, car.V3_4_0, pc.V1_0_0, tx_mini.V0_0_1)
	splitDetail := !presentDetailVersion.Atleast(serviceInfo.ClientType, serviceInfo.ClientVersion)

	//收礼的详情
	for _, item := range receivePresentList.GetDetailList() {
		isBreak, isContinue := s.DetailCheck(item, uint32(serviceInfo.ClientType), recordCount, atLeastMask)
		if isBreak {
			break
		} else if isContinue {
			continue
		}

		s.addPresentDetail(uid, &recordCount, item, userMap[item.GetFromUid()], headMap[userMap[item.GetFromUid()].GetUsername()],
			splitDetail, out)
	}

	recordCount = uint32(0)

	// 送礼的详情
	for _, item := range sendPresentList.GetDetailList() {
		isBreak, isContinue := s.DetailCheck(item, uint32(serviceInfo.ClientType), recordCount, atLeastMask)
		if isBreak {
			break
		} else if isContinue {
			continue
		}

		s.addSendPresentDetail(uid, &recordCount, item, userMap[item.GetTargetUid()], headMap[userMap[item.GetTargetUid()].GetUsername()],
			splitDetail, out)
	}

	// AI送礼详情（追加到送礼列表中）
	if req.GetDetailType() == 1 || req.GetDetailType() == 2 {
		aiResp, aiErr := client.PresentGoProtoCli.GetAiPresentDetail(ctx, &userpresent_go_proto.GetAiPresentDetailReq{FromUid: uid})
		if aiErr != nil {
			log.ErrorWithCtx(ctx, "GetUserPresentDetailList GetAiPresentDetail fail.err :%v", aiErr)
			return out, aiErr
		}

		// 收集包含的的role_id
		partnerIdList := make([]uint32, 0)
		for _, it := range aiResp.GetAiPresentDetailList() {
			cfg := s.presentConfigMemCache.GetConfigById(it.GetItemId())
			if cfg == nil {
				continue
			}
			detail := &presentPB_.UserPresentSendDetail{
				Uid:             uid,
				ItemBriefConfig: tranBriefConfig(cfg),
				ItemCount:       it.GetItemCount(),
				ItemId:          it.GetItemId(),
				Rich:            it.GetAddRich(),
				SendMethod:      int32(presentPB_.PresentSendMethodType_PRESENT_TYPE_IM),
				TargetType:      uint32(presentPB_.SendPresentTargetType_PRESENT_TARGET_TYPE_AI_ROLE),
				ToUid:           it.GetTargetPartnerId(),
				SendTime:        it.GetSendTime(),
				BusinessType:    it.GetBusinessType(),
			}
			// 分数补充
			detail.ItemBriefConfig.Score = cfg.GetScore() * it.GetItemCount()
			detail.ItemBriefConfig.Rich = it.GetAddRich()
			out.PresentSendDetailList = append(out.PresentSendDetailList, detail)

			// 如果roleIdList 不包含對應的role_id，则添加
			if isUint32InSlice(it.GetTargetPartnerId(), partnerIdList) == false {
				partnerIdList = append(partnerIdList, it.GetTargetPartnerId())
			}
		}

		// 查role
		roleMap, err := client.AiSoulmateCli.BatchGetAIPartner(ctx, &aigc_soulmate.BatchGetAIPartnerReq{
			Ids: partnerIdList,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserPresentDetailList BatchGetAIPartner fail.err :%v", err)
			//return out, err
		}

		for _, item := range out.PresentSendDetailList {
			if item.GetTargetType() == uint32(presentPB_.SendPresentTargetType_PRESENT_TARGET_TYPE_AI_ROLE) {
				item.ToName = roleMap.GetPartnerMap()[item.ToUid].GetRole().GetName()
				item.ToHeadImageUrl = roleMap.GetPartnerMap()[item.ToUid].GetRole().GetAvatar()
			}
		}

		sort.Slice(out.PresentSendDetailList, func(i, j int) bool {
			return out.PresentSendDetailList[i].SendTime > out.PresentSendDetailList[j].SendTime
		})
	}

	return out, nil
}

func isUint32InSlice(a uint32, list []uint32) bool {
	for _, b := range list {
		if b == a {
			return true
		}
	}
	return false
}

func tranBriefConfig(cfg *userpresent.StPresentItemConfig) *ga.PresentItemBriefConfig {
	return &ga.PresentItemBriefConfig{
		ItemId:     cfg.GetItemId(),
		Name:       cfg.GetName(),
		IconUrl:    cfg.GetIconUrl(),
		Score:      cfg.GetScore(),
		Charm:      cfg.GetCharm(),
		MsgIconUrl: toPresentMsgIconUrl(cfg.GetIconUrl()),
		Rich:       cfg.GetRichValue(),
	}
}

func tranDetailToUkw(detail *presentPB_.UserPresentDetail) {
	detail.UserProfile = &ga.UserProfile{
		Uid:      0,
		Account:  "ukw0000",
		Nickname: "神秘人",
	}
	ukwInfo := &ga.UserUKWInfo{
		Level: 1,
	}
	options, _ := ukwInfo.Marshal()

	privilege := &ga.UserPrivilege{
		Account:  "ukw0000",
		Nickname: "神秘人",
		Type:     uint32(ga.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW),
		Options:  options,
	}

	detail.UserProfile.Privilege = privilege
	detail.UserUkwInfo = ukwInfo

	detail.FromUid = 0
	detail.FromAccount = "ukw0000"
	detail.FromName = "神秘人"
}

func tranSendDetailToUkw(detail *presentPB_.UserPresentSendDetail) {
	detail.UserProfile = &ga.UserProfile{
		Uid:      0,
		Account:  "ukw0000",
		Nickname: "神秘人",
	}
	ukwInfo := &ga.UserUKWInfo{
		Level: 1,
	}
	options, _ := ukwInfo.Marshal()

	privilege := &ga.UserPrivilege{
		Account:  "ukw0000",
		Nickname: "神秘人",
		Type:     uint32(ga.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW),
		Options:  options,
	}

	detail.UserProfile.Privilege = privilege
	detail.UserUkwInfo = ukwInfo

	detail.ToUid = 0
	detail.ToAccount = "ukw0000"
	detail.ToName = "神秘人"
}

func (s *PresentGoLogic_) DetailCheck(item *userpresent.StUserPresentDetail, clientType, recordCount uint32,
	atLeastMask bool) (isBreak, isContinue bool) {
	if recordCount >= RecordList {
		// 批量礼物的记录，旧版中的显示会拆成多条；保证最多返回RECORDS_LMT条
		return true, false
	}

	if item.GetSendSource() == uint32(presentPB_.PresentSendSourceType_E_SEND_SOURCE_MASKED_CALL) &&
		(!atLeastMask && clientType == protocol.PC_TT) {
		return false, true
	}

	return false, false
}

func (s *PresentGoLogic_) addPresentDetail(uid uint32, recordCount *uint32, item *userpresent.StUserPresentDetail,
	fromUser *account.User, fromHead string, splitDetail bool, out *presentPB_.GetUserPresentDetailListResp) {

	cfg := s.presentConfigMemCache.GetConfigById(item.GetItemId())
	if cfg == nil {
		return
	}

	charm := item.GetAddCharm()

	if !splitDetail || item.GetItemCount() == 1 {
		*recordCount++

		detail := &presentPB_.UserPresentDetail{
			Uid:             uid,
			FromUid:         item.GetFromUid(),
			FromAccount:     fromUser.GetUsername(),
			FromName:        fromUser.GetNickname(),
			FromFaceMd5:     fromHead,
			ItemBriefConfig: tranBriefConfig(s.presentConfigMemCache.GetConfigById(item.GetItemId())),
			SendTime:        item.GetReceiveTime(),
			ItemCount:       item.GetItemCount(),
			ItemId:          item.GetItemId(),
			Charm:           item.GetAddCharm(),
			SendSource:      item.GetSendSource(),
			FromSex:         fromUser.GetSex(),
			SendMethod:      int32(item.GetSendMethod()),
			BusinessType:    item.GetBusinessType(),
		}

		// 真实增加的，魅力
		detail.ItemBriefConfig.Charm = item.GetAddCharm()
		detail.ItemBriefConfig.Score = cfg.GetScore() * item.GetItemCount()

		if item.GetIsUkw() {
			tranDetailToUkw(detail)
		}
		out.PresentDetailList = append(out.PresentDetailList, detail)
	} else {
		for i := uint32(0); i < item.GetItemCount() && *recordCount < RecordList; i++ {
			*recordCount++
			detail := &presentPB_.UserPresentDetail{
				Uid:             uid,
				FromUid:         item.GetFromUid(),
				FromAccount:     fromUser.GetUsername(),
				FromName:        fromUser.GetNickname(),
				FromFaceMd5:     fromHead,
				SendTime:        item.GetReceiveTime(),
				ItemCount:       1,
				ItemBriefConfig: tranBriefConfig(s.presentConfigMemCache.GetConfigById(item.GetItemId())),
			}

			if charm > cfg.GetCharm() {
				charm = charm - cfg.GetCharm()
			} else {
				charm = 0
			}
			detail.ItemBriefConfig.Charm = charm

			if item.GetIsUkw() {
				tranDetailToUkw(detail)
			}

			out.PresentDetailList = append(out.PresentDetailList, detail)
		}
	}
}

func (s *PresentGoLogic_) addSendPresentDetail(uid uint32, recordCount *uint32, item *userpresent.StUserPresentDetail,
	toUser *account.User, toHead string, splitDetail bool, out *presentPB_.GetUserPresentDetailListResp) {

	cfg := s.presentConfigMemCache.GetConfigById(item.GetItemId())
	if cfg == nil {
		return
	}

	if !splitDetail || item.GetItemCount() == 1 {
		*recordCount++

		detail := &presentPB_.UserPresentSendDetail{
			Uid:             uid,
			ToUid:           item.GetTargetUid(),
			ToAccount:       toUser.GetUsername(),
			ToName:          toUser.GetNickname(),
			ToFaceMd5:       toHead,
			ItemBriefConfig: tranBriefConfig(s.presentConfigMemCache.GetConfigById(item.GetItemId())),
			SendTime:        item.GetReceiveTime(),
			ItemCount:       item.GetItemCount(),
			ItemId:          item.GetItemId(),
			Rich:            item.GetAddRich(),
			SendSource:      item.GetSendSource(),
			ToSex:           toUser.GetSex(),
			SendMethod:      int32(item.GetSendMethod()),
			BusinessType:    item.GetBusinessType(),
		}
		// 真实增加的财富
		detail.ItemBriefConfig.Rich = item.GetAddRich()
		detail.ItemBriefConfig.Score = cfg.GetScore() * item.GetItemCount()

		if item.GetIsUkw() {
			tranSendDetailToUkw(detail)
		}
		out.PresentSendDetailList = append(out.PresentSendDetailList, detail)
	}
}
