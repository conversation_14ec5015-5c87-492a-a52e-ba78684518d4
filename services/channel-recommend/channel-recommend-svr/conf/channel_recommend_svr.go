package conf

import "encoding/json"

const ChannelRecommendSvrConfigFiles = "/data/oss/conf-center/tt/channel-recommend-svr.json"

const (
	AbTestSceneQualityUser           = 1 //优质pgc用户
	AbTestSceneHighUser              = 2 // 壕用户
	AbTestSceneRecListPrecisePgcUser = 3 // 推荐列表-精准PGC用户
	AbTestSceneRecTopWinA            = 4 // 顶部浮窗-人群包A
	AbTestSceneRecTopWinB            = 5 // 顶部浮窗-人群包B
	AbTestSceneRecTopWinC            = 6 // 顶部浮窗-人群包C
)

// ab测试配置
type AbTest struct {
	IsSwitch  bool   `json:"is_switch"`
	AppId     uint32 `json:"app_id"`
	Url       string `json:"url"`
	LayId     string `json:"lay_id"`
	ExptId    string `json:"expt_id"`
	ExptVal   string `json:"expt_val"`
	TimeOutMs uint32 `json:"time_out_ms"` // 超时时间 毫秒
}

type QualityUser struct {
	HighCertUrl     string `json:"high_cert_url"`      // 壕用户认证url
	HighPopUrl      string `json:"high_pop_url"`       // 壕用户弹窗url
	FlagExpireTs    uint32 `json:"flag_expire_ts"`     // 标记过期时间
	PopFlagExpireTs uint32 `json:"pop_flag_expire_ts"` // 弹窗标记过期时间
}

type FeiShuConfig struct {
	AppId                     string `json:"app_id"`                       // 飞书应用ID
	AppSecret                 string `json:"app_secret"`                   // 飞书应用密钥
	ShortLinkSpreadsheetToken string `json:"short_link_spreadsheet_token"` // 短链表格token
	ShortLinkMainSheetId      string `json:"short_link_main_sheet_id"`     // 短链主表格ID
}

type ChannelRecommendSvrDyConfig struct {
	PgcRecommendListConf      *PGCRecommendListConf `json:"pgc_recommend_list_conf"`
	TopWinRecommend           *TopWinRecommendConf  `json:"top_win_recommend_conf"`
	ConsumeUserLable          string                `json:"consume_user_lable"`             // 付费用户标签
	EnterNoConsumeUserLable   string                `json:"enter_no_consume_user_lable"`    // 进房未付费用户标签
	TestConsumeTagUids        map[uint32]string     `json:"test_consume_tag_uids"`          // 测试-付费uid标签
	TestEnterNoConsumeTagUids map[uint32]string     `json:"test_enter_no_consume_tag_uids"` // 测试-进入未付费uid标签
	MapId2AbTest              map[uint32]AbTest     `json:"map_ab_test"`                    // ab测试配置 type see AbTestScene xxxx
	QualityUserConf           QualityUser           `json:"quality_user"`                   // 优质用户配置
	IopTimeOutMs              uint32                `json:"iop_time_out_ms"`                // iop超时时间ms
	FeedbackConf              Feedback              `json:"feedback_conf"`                  //推荐列表反馈配置
	WeddingIopProxyGroupId    uint32                ` json:"wedding_iop_proxy_group_id"`    // 婚礼房人群包id
	LpmProxyTimeout           uint32                `json:"get_lpm_proxy_timeout"`          // 获取lpm超时时间ms
	FeiShuConf                FeiShuConfig          `json:"feishu_conf"`                    // 飞书配置
	ShortLinkRefreshTs        uint32                `json:"short_link_refresh_ts"`          // 短链刷新时间 单位秒
}

// PGCRecommendListConf 针对PGC付费用户的推荐列表优化配置
type PGCRecommendListConf struct {
	Enabled                 bool `json:"enabled"`                      // 是否开启
	IsPriorityTop           bool `json:"is_priority_top"`              // 是否开启优先置顶
	AbTestHitKeepTime       int  `json:"ab_test_hit_keep_time"`        // AB测试命中保留时间
	ConsumeTagCidNum        int  `json:"consume_tag_cid_num"`          // 付费标签房间数量
	EnterNoConsumeTagCidNum int  `json:"enter_no_consume_tag_cid_num"` // 进入未付费标签房间数量
}

// TopWinRecommendConf 顶部浮窗房间推荐配置
type TopWinRecommendConf struct {
	Enabled                 bool   `json:"enabled"`                     // 是否开启
	ConsumeTagRate          int    `json:"consume_tag_rate"`            // 付费标签房间概率[0-100)
	EnterNoConsumeTagRate   int    `json:"enter_no_consume_tag_rate"`   // 进入未付费标签房间概率[0-100)
	PriorityTagForB         uint32 `json:"priority_tag_for_b"`          // 人群包B-优先标签
	TodayNumLimit           int    `json:"today_num_limit"`             // 今日推荐数量限制
	ChannelRecCooldown      uint32 `json:"channel_rec_cooldown"`        // 单个房间被推荐冷却时间s
	ContinueNoEnterNumLimit int    `json:"continue_no_enter_num_limit"` // 连续未进入房间次数限制
	IsDebug                 bool   `json:"is_debug"`                    // 是否开启debug
}

// 反馈配置
type Feedback struct {
	NegativeEffectTs   uint32   `json:"negative_effect_ts"`   // 负反馈生效时间
	NegativeReasonList []string `json:"negative_reason_list"` // 负反馈原因列表
}

func (r *ChannelRecommendSvrDyConfig) UnmarshalBinary(data []byte) error {
	err := json.Unmarshal(data, r)
	if err != nil {
		return err
	}
	return nil
}

func (r *ChannelRecommendSvrDyConfig) IsTestUidTags(uid uint32) map[uint32]string {
	testTags := make(map[uint32]string)
	if tags, ok := r.TestConsumeTagUids[uid]; ok {
		testTags[1] = tags
	}
	if tags, ok := r.TestEnterNoConsumeTagUids[uid]; ok {
		testTags[2] = tags
	}
	return testTags
}

func (r *ChannelRecommendSvrDyConfig) GetAbTestConf() map[uint32]AbTest {
	return r.MapId2AbTest
}
