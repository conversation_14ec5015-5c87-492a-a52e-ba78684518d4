package manager

import (
	"context"
	"fmt"
	larksheets "github.com/larksuite/oapi-sdk-go/v3/service/sheets/v3"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/pkg/coroutine"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/marketid_helper"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	gaChannel "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channel-recommend-svr"
	channelmic "golang.52tt.com/protocol/services/channelmicsvr"
	iopRoomSitePb "golang.52tt.com/protocol/services/iop-room-site"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/client"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/conf"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/feishu"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/model"
	"math/rand" //#nosec
	"strconv"
	"strings"
	"sync"
	"time"
)

type QuickRecChannelInfo struct {
	ChannelId    uint32
	MicMaleNum   uint32 //麦上男性数量
	MicFemaleNum uint32 //麦上女性数量
	HasLocker    bool   //是否上锁
	MicNum       uint32 //麦位人数
	ZeroMicSex   int32  //0麦性别
}
type QuickRecMicSexNTagInfo struct {
	TagId      uint32
	MaleCids   []uint32
	FemaleCids []uint32
	LockerCids map[uint32]struct{} //上锁的房间，随机时排除
	NoLiveCids map[uint32]struct{} //未开播的房间
	AllCidList []uint32            // 房间列表
}
type QuickRecChannelMgr struct {
	dyCfg      conf.IConfDynamic
	clientPool *model.SClientPool

	RecMicSexNTags map[uint32]*QuickRecMicSexNTagInfo
	rwLocker       sync.RWMutex

	feiShuSheet            *feishu.Sheet // 飞书表格
	mapFeiShuRwLocker      sync.RWMutex
	mapFeiShuTagId2CidList map[uint32][]uint32 // 飞书表格tagid对应的房间列表
}

func NewQuickRecChannelMgr(ctx context.Context, cliPool *model.SClientPool, dyConf conf.IConfDynamic) (*QuickRecChannelMgr, error) {
	mgr := &QuickRecChannelMgr{
		dyCfg:                  dyConf,
		clientPool:             cliPool,
		RecMicSexNTags:         make(map[uint32]*QuickRecMicSexNTagInfo),
		mapFeiShuTagId2CidList: make(map[uint32][]uint32),
		mapFeiShuRwLocker:      sync.RWMutex{},
	}

	mgr.feiShuSheet = feishu.NewSheet(feishu.NewSheetParam{
		AppId:            dyConf.GetSvrDyConf().FeiShuConf.AppId,
		AppSecret:        dyConf.GetSvrDyConf().FeiShuConf.AppSecret,
		SpreadsheetToken: dyConf.GetSvrDyConf().FeiShuConf.ShortLinkSpreadsheetToken,
		MainRangeStr:     fmt.Sprintf("%s!A2:E200", dyConf.GetSvrDyConf().FeiShuConf.ShortLinkMainSheetId), // 主表格范围字符串
		SubRangeStr:      "%s!A%d:A%d",
		CheckSheetIsSync: mgr.CheckSheetIsSync,
		ParseSheetData:   mgr.ParseSheetData,
	})

	rand.Seed(time.Now().UnixNano()) //#nosec

	go mgr.TimerRefreshQuickRecMicSexNInfo()
	coroutine.FixIntervalExec(mgr.TimerRefreshQuickRecMicSexNInfo, time.Duration(dyConf.GetSvrDyConf().ShortLinkRefreshTs)*time.Second)

	log.InfoWithCtx(ctx, "NewQuickRecChannelMgr ok")
	return mgr, nil
}

// 排除上锁的房间再随机
func (m *QuickRecMicSexNTagInfo) RandomCid(ctx context.Context, uid uint32, cf *conf.TagidChannelConf) uint32 {
	allowCids := []uint32{}
	for _, cid := range m.AllCidList {
		if _, ok := m.LockerCids[cid]; ok { //检查是否上锁
			continue
		}
		if cf.ChannelType == uint32(gaChannel.ChannelType_RADIO_LIVE_CHANNEL_TYPE) { //类型7 的房间.要检查是否开播
			if _, ok := m.NoLiveCids[cid]; ok {
				continue
			}
		}
		allowCids = append(allowCids, cid)
	}
	if len(allowCids) == 0 {
		return cf.RandomCid()
	}
	idx := rand.Uint32() % uint32(len(allowCids)) //#nosec
	return allowCids[idx]
}

func (m *QuickRecChannelMgr) CheckSheetIsSync(ctx context.Context, table []interface{}) (string, bool) {
	if len(table) < 5 {
		log.ErrorWithCtx(ctx, "CheckSheetIsSync table len < 5, table: %v", table)
		return "", false
	}
	sheetName, ok := table[0].(string)
	if !ok || sheetName == "" {
		log.ErrorWithCtx(ctx, "CheckSheetIsSync sheetName is not string or empty, table: %v", table[0])
		return "", false
	}
	sheetStatus, ok := table[4].(string)
	if !ok || sheetStatus != "sync(同步到线上)" {
		log.DebugWithCtx(ctx, "CheckSheetIsSync sheetStatus is not string or not sync, table: %v sheetStatus:%s", table[4], sheetStatus)
		return "", false
	}

	log.InfoWithCtx(ctx, "CheckSheetIsSync sheetName: %s, sheetStatus: %s", sheetName, sheetStatus)
	return sheetName, true
}

func (m *QuickRecChannelMgr) ParseSheetData(ctx context.Context, sheetInfo *larksheets.Sheet, tableList [][]interface{}) error {
	strList := strings.Split(*sheetInfo.Title, "-") // 获取表格名称中的tagid
	if len(strList) != 2 {
		log.ErrorWithCtx(ctx, "ParseSheetData sheetInfo.Title:%s not contain tagid", *sheetInfo.Title)
		return nil
	}

	tagid, err := strconv.ParseUint(strList[1], 10, 32)
	if err != nil {
		log.ErrorWithCtx(ctx, "ParseSheetData ParseUint err:%v, sheetInfo.Title:%s", err, *sheetInfo.Title)
		return nil
	}

	cidList := make([]uint32, 0)
	for _, row := range tableList {
		if len(row) < 1 {
			continue
		}
		cid, ok := row[0].(float64)
		if ok && cid != 0 {
			cidList = append(cidList, uint32(cid))
			continue
		}
	}

	m.mapFeiShuRwLocker.Lock()
	defer m.mapFeiShuRwLocker.Unlock()

	if _, ok := m.mapFeiShuTagId2CidList[uint32(tagid)]; !ok {
		m.mapFeiShuTagId2CidList[uint32(tagid)] = []uint32{}
	}

	m.mapFeiShuTagId2CidList[uint32(tagid)] = cidList

	log.InfoWithCtx(ctx, "ParseSheetData tagid:%d, cidList:%v", tagid, cidList)
	return nil
}

func (m *QuickRecChannelMgr) GetMicSexNTagInfo(tagid uint32) *QuickRecMicSexNTagInfo {
	m.rwLocker.RLock()
	defer m.rwLocker.RUnlock()
	info, ok := m.RecMicSexNTags[tagid]
	if !ok {
		return nil
	}
	return info
}

func (m *QuickRecChannelMgr) TimerRefreshQuickRecMicSexNInfo() {
	dycf := m.dyCfg.Get()
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*10)
	defer cancel()

	now := time.Now()
	log.InfoWithCtx(ctx, "TimerRefreshQuickRecMicSexNInfo now %v", now)
	defer func() {
		log.InfoWithCtx(ctx, "TimerRefreshQuickRecMicSexNInfo cost %v", time.Since(now))
	}()

	// 获取飞书表格数据
	err := m.feiShuSheet.SyncSheetData(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerRefreshQuickRecMicSexNInfo SyncSheetData err:%v", err)
	}

	channelIs2Tags := make(map[uint32][]uint32)
	channelIds := []uint32{}
	for _, cf := range dycf.AppointedTagidChList {
		if !cf.IsNewGo {
			continue
		}
		//只处理房间类型为:4和7 这两种
		if cf.ChannelType != uint32(gaChannel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) && cf.ChannelType != uint32(gaChannel.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
			continue
		}
		if cf.IsEnd() {
			continue
		}

		cidList := cf.ChList
		if cf.ChannelSource == conf.ChannelSourceFeiShuSheet { // 飞书表格来源
			m.mapFeiShuRwLocker.RLock()
			if list, ok := m.mapFeiShuTagId2CidList[cf.Tagid]; ok {
				cidList = list
			}
			m.mapFeiShuRwLocker.RUnlock()
		}

		for _, cid := range cidList {
			if _, ok := channelIs2Tags[cid]; !ok {
				channelIs2Tags[cid] = []uint32{}
			}
			if len(channelIs2Tags[cid]) == 0 {
				channelIds = append(channelIds, cid)
			}
			channelIs2Tags[cid] = append(channelIs2Tags[cid], cf.Tagid)
		}
	}
	log.InfoWithCtx(ctx, "TimerRefreshQuickRecMicSexNInfo channelSize:%d", len(channelIds))
	channelInfos := make(map[uint32]*QuickRecChannelInfo)
	reqCids := []uint32{}
	for _, cid := range channelIds {
		reqCids = append(reqCids, cid)
		if len(reqCids) == 20 {
			err := m.batchGetChannelMicInfo(reqCids, channelInfos)
			if err != nil {
				log.ErrorWithCtx(ctx, "TimerRefreshLocalInfo batchGetChannelMicInfo err:%v", err)
				return
			}
			reqCids = []uint32{}
		}
	}
	if len(reqCids) > 0 {
		err := m.batchGetChannelMicInfo(reqCids, channelInfos)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerRefreshLocalInfo batchGetChannelMicInfo err:%v", err)
			return
		}
	}

	recMicSexNTags := map[uint32]*QuickRecMicSexNTagInfo{}
	for cid, info := range channelInfos {
		tagList, ok := channelIs2Tags[cid]
		if !ok {
			continue
		}
		for _, tagid := range tagList {
			cf := dycf.AppointedTagidToConf[tagid]
			if _, ok := recMicSexNTags[tagid]; !ok {
				recMicSexNTags[tagid] = &QuickRecMicSexNTagInfo{
					TagId:      tagid,
					MaleCids:   []uint32{},
					FemaleCids: []uint32{},
					LockerCids: make(map[uint32]struct{}),
					NoLiveCids: make(map[uint32]struct{}),
					AllCidList: make([]uint32, 0),
				}
			}

			recMicSexNTags[tagid].AllCidList = append(recMicSexNTags[tagid].AllCidList, cid)

			if info.HasLocker {
				recMicSexNTags[tagid].LockerCids[cid] = struct{}{}
				continue
			}
			if info.MicNum == 0 { //未开播
				recMicSexNTags[tagid].NoLiveCids[cid] = struct{}{}
				continue
			}

			if cf.ChannelType == uint32(gaChannel.ChannelType_RADIO_LIVE_CHANNEL_TYPE) { //语音直播房
				if info.ZeroMicSex == account.Male { //主播是0麦
					recMicSexNTags[tagid].MaleCids = append(recMicSexNTags[tagid].MaleCids, cid)
				} else {
					recMicSexNTags[tagid].FemaleCids = append(recMicSexNTags[tagid].FemaleCids, cid)
				}
				continue
			}
			if info.MicMaleNum >= cf.RecParam {
				recMicSexNTags[tagid].MaleCids = append(recMicSexNTags[tagid].MaleCids, cid)
			}
			if info.MicFemaleNum >= cf.RecParam {
				recMicSexNTags[tagid].FemaleCids = append(recMicSexNTags[tagid].FemaleCids, cid)
			}
		}
	}
	for _, info := range recMicSexNTags {
		log.InfoWithCtx(ctx, "TimerRefreshQuickRecMicSexNInfo: %+v", info)
	}
	m.rwLocker.Lock()
	defer m.rwLocker.Unlock()
	m.RecMicSexNTags = recMicSexNTags
}

func (m *QuickRecChannelMgr) batchGetChannelMicInfo(cids []uint32, channelInfos map[uint32]*QuickRecChannelInfo) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	channelInfoMap, err := m.clientPool.ChannelCli.BatchGetChannelSimpleInfo(ctx, 0, cids)
	if err != nil {
		log.ErrorWithCtx(ctx, "batchGetChannelMicInfo BatchGetChannelSimpleInfo cids:%v, err:%v", cids, err)
		return err
	}
	rsp, err := m.clientPool.ChannelMicCli.BatGetMicrList(ctx, 0, &channelmic.BatGetMicrListReq{
		ChannelIdList: cids,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "batchGetChannelMicInfo BatGetMicrList cids:%v, err:%v", cids, err)
		return err
	}

	uids := []uint32{}
	uid2info := map[uint32]*QuickRecChannelInfo{}
	uid2MicId := map[uint32]uint32{}
	for _, mic := range rsp.MicDataList {
		info := &QuickRecChannelInfo{
			ChannelId: mic.ChannelId,
		}
		channelInfos[mic.ChannelId] = info
		if channelInfo, ok := channelInfoMap[mic.ChannelId]; ok {
			if channelInfo.GetEnterControlType() == uint32(gaChannel.EnterControlType_EnterControlType_PASSWD) {
				info.HasLocker = true
			}
		}
		for _, mi := range mic.AllMicList {
			if mi.MicUid > 0 {
				uids = append(uids, mi.MicUid)
				uid2info[mi.MicUid] = info
				uid2MicId[mi.MicUid] = mi.MicId
			}
		}
	}

	if len(uids) == 0 {
		return nil
	}

	//获取性别
	userMap, err := m.clientPool.AccountCli.GetUsersMap(ctx, uids)
	if err != nil {
		log.ErrorWithCtx(ctx, "batchGetChannelMicInfo GetUserMap err uids:%v, err:%v", uids, err)
		return err
	}

	for uid, userInfo := range userMap {
		if userInfo.Sex == account.Male {
			uid2info[uid].MicMaleNum++
		} else if userInfo.Sex == account.Female {
			uid2info[uid].MicFemaleNum++
		} else {
			log.WarnWithCtx(ctx, "batchGetChannelMicInfo sex unknow uid:%d", uid)
		}
		uid2info[uid].MicNum++
		if micId, ok := uid2MicId[uid]; ok {
			if micId == 1 {
				uid2info[uid].ZeroMicSex = userInfo.Sex
			}
		}
	}
	return nil
}

// 优先随机跳转当前麦上异性人数≥N人的房间, 如不存在，则纯随机
func (m *QuickRecChannelMgr) RecommendMicSexN(ctx context.Context, uid uint32, cf *conf.TagidChannelConf) uint32 {
	tagInfo := m.GetMicSexNTagInfo(cf.Tagid)
	if tagInfo == nil {
		log.ErrorWithCtx(ctx, "RecommendMicSexN cf.Tagid:%d, uid:%d", cf.Tagid, uid)
		return cf.RandomCid()
	}
	if len(tagInfo.MaleCids) == 0 && len(tagInfo.FemaleCids) == 0 {
		log.ErrorWithCtx(ctx, "RecommendMicSexN err cf.Tagid:%d, uid:%d", cf.Tagid, uid)
		return tagInfo.RandomCid(ctx, uid, cf)
	}
	userInfo, err := m.clientPool.AccountCli.GetUserByUid(ctx, uid)
	if err != nil { //如果错误，不返回，走兜底策略
		log.ErrorWithCtx(ctx, "RecommendMicSexN GetUserByUid err uid:%v, err:%v", uid, err)
		return tagInfo.RandomCid(ctx, uid, cf)
	}
	if userInfo.Sex == account.Male {
		if len(tagInfo.FemaleCids) == 0 {
			return tagInfo.RandomCid(ctx, uid, cf)
		}
		idx := rand.Uint32() % uint32(len(tagInfo.FemaleCids)) //#nosec
		return tagInfo.FemaleCids[idx]
	}

	if userInfo.Sex == account.Female {
		if len(tagInfo.MaleCids) == 0 {
			return tagInfo.RandomCid(ctx, uid, cf)
		}
		idx := rand.Uint32() % uint32(len(tagInfo.MaleCids)) //#nosec
		return tagInfo.MaleCids[idx]
	}

	return tagInfo.RandomCid(ctx, uid, cf)
}

// 随机：公会公开房 上锁过滤
// 语音直播房   上锁和开播过滤
func (m *QuickRecChannelMgr) RecommendRand(ctx context.Context, uid uint32, cf *conf.TagidChannelConf) uint32 {
	tagInfo := m.GetMicSexNTagInfo(cf.Tagid)
	if tagInfo == nil {
		log.ErrorWithCtx(ctx, "RecommendMicSexN cf.Tagid:%d, uid:%d", cf.Tagid, uid)
		return cf.RandomCid()
	}
	return tagInfo.RandomCid(ctx, uid, cf)
}

func (m *QuickRecChannelMgr) GetQuickRecChannelByTagId(ctx context.Context, uid uint32, tagid uint32) (*pb.GetQuickRecChannelByTagIdResp, error) {
	rsp := &pb.GetQuickRecChannelByTagIdResp{}
	dycf := m.dyCfg.Get()
	cf, ok := dycf.AppointedTagidToConf[tagid]
	if !ok {
		return rsp, nil
	}
	if len(cf.ChList) == 0 && cf.ChannelSource != conf.ChannelSourceFeiShuSheet {
		return rsp, nil
	}
	if cf.IsEnd() {
		return rsp, nil
	}
	recCid := uint32(0)
	switch cf.RecType {
	case uint32(pb.QuickRecChannelType_Normal):
		recCid = m.RecommendRand(ctx, uid, cf)
	case uint32(pb.QuickRecChannelType_MicSex):
		recCid = m.RecommendMicSexN(ctx, uid, cf)
	default:
	}
	rsp.ChannelId = recCid
	log.InfoWithCtx(ctx, "GetQuickRecChannelByTagId uid:%d, tagid:%d, cid:%d", uid, tagid, recCid)
	return rsp, nil
}

func (m *QuickRecChannelMgr) GetQuickWeightConfList(ctx context.Context, req *pb.GetQuickWeightConfListReq) (*pb.GetQuickWeightConfListResp, error) {
	resp := &pb.GetQuickWeightConfListResp{}

	for _, conf := range m.dyCfg.Get().QuickWeightConfList {
		resp.ConfList = append(resp.ConfList, &pb.QuickWeightConf{
			TagId:       conf.TagId,
			MapLvWeight: conf.MapLv2Weight,
		})
	}

	log.DebugWithCtx(ctx, "GetQuickWeightConfList req:%+v resp:%+v", req, resp)
	return resp, nil
}

func (m *QuickRecChannelMgr) GetQuickRecChannelList(ctx context.Context, req *pb.GetQuickRecChannelListReq) (*pb.GetQuickRecChannelListResp, error) {
	resp := &pb.GetQuickRecChannelListResp{}
	if req.GetPage() == 0 || req.GetPageSize() == 0 {
		log.ErrorWithCtx(ctx, "GetQuickRecChannelList invalid param req:%+v resp:%+v", req, resp)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	cidList, err := m.clientPool.Cache.GetTagLevelChannelList(uint32(pb.UserCategory_QuickEnterUser_Type), req.GetTagId(), req.GetLevel(),
		(req.GetPage()-1)*req.GetPageSize(), req.GetPage()*req.GetPageSize()-1)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetQuickRecChannelList GetTagLevelChannelList req:%v err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	resp.CidList = cidList

	log.DebugWithCtx(ctx, "GetQuickRecChannelList req:%+v resp:%+v", req, resp)
	return resp, nil
}

func (m *QuickRecChannelMgr) GetQuickRecChannelFromIop(ctx context.Context, uid uint32, tagId uint32) uint32 {
	var channelId uint32

	subCtx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Millisecond*time.Duration(m.dyCfg.GetSvrDyConf().IopTimeOutMs))
	defer cancel()

	sv, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetQuickRecChannelFromIop no service info context uid:%d tagId:%d", uid, tagId)
		return 0
	}

	appName := marketid_helper.GetAppName(sv.MarketID)

	resp, err := client.CliInstance.IopRoomSiteCli.GetRecommendRoomByTagId(subCtx, &iopRoomSitePb.GetRecommendRoomByTagIdReq{
		AppName:   "ttyuyin",
		AppType:   appName,
		BizSiteId: fmt.Sprintf("%d", tagId),
		Uid:       fmt.Sprintf("%d", uid),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetQuickRecChannelFromIop GetRecommendRoomByTagId uid:%d tagId:%d err:%v", uid, tagId, err)
		return 0
	}

	if resp.GetCode() != 200 {
		log.ErrorWithCtx(ctx, "GetQuickRecChannelFromIop GetRecommendRoomByTagId uid:%d tagId:%d code:%d", uid, tagId, resp.GetCode())
		return 0
	}

	if resp.GetRoomInfo().GetRoomId() != "" {
		result, parseErr := strconv.ParseUint(resp.GetRoomInfo().GetRoomId(), 10, 32)
		if parseErr != nil {
			log.ErrorWithCtx(ctx, "GetQuickRecChannelFromIop ParseUint uid:%d tagId:%d err:%v", uid, tagId, parseErr)
		} else {
			channelId = uint32(result)
		}
	}

	log.InfoWithCtx(ctx, "GetQuickRecChannelFromIop uid:%d, tagId:%d, appName:%s, cid:%d", uid, tagId, appName, channelId)
	return channelId
}
