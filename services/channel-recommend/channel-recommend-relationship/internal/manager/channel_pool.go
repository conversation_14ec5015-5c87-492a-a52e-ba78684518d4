package manager

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	appChannelPB "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channel-recommend-relationship"
	entertainmentRecommendBack "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	"golang.52tt.com/services/channel-recommend/channel-recommend-relationship/internal/rpc"
	"golang.52tt.com/services/channel-recommend/channel-recommend-relationship/internal/store"
	"strconv"
	"strings"
	"time"
)

const (
	// TagWeightMin 标签权重最小值（即 0.001）
	TagWeightMin = 1
	// TagWeightMax 标签权重最大值（即 1.0）
	TagWeightMax = 1000
)

// GetRecommendPoolChannelTags 运营后台 - 获取推荐池标签（包含虚拟标签）
func (m *Manager) GetRecommendPoolChannelTags(ctx context.Context) (*pb.GetRecommendPoolChannelTagsResp, error) {
	resp := &pb.GetRecommendPoolChannelTagsResp{}
	resp.List = make([]*pb.RecommendPoolChannelTag, 0)
	channelTagResp, sErr := rpc.EntertainmentRecommendBackCli.GetChannelTagConfigInfo(ctx, 0, &entertainmentRecommendBack.GetChannelTagConfigInfoReq{})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetRecommendPoolChannelTags GetChannelTagConfigInfo err:%v", sErr)
		return nil, sErr
	}

	weightList, err := m.GetFullChannelTagWeightList(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendPoolChannelTags GetFullChannelTagWeightList err:%v", err)
		return nil, err
	}
	weightMap := make(map[uint32]uint32)
	for _, w := range weightList {
		weightMap[w.TagId] = w.Weight
	}

	virtualTagList := m.dyconf.GetRelationshipConf().GetVirtualTagList()
	for _, tag := range virtualTagList {
		resp.List = append(resp.List, &pb.RecommendPoolChannelTag{
			TagId:   tag.TagID,
			TagName: tag.TagName,
			TagType: pb.RecommendPoolChannelTag_RECOMMEND_POOL_CHANNEL_TAG_TYPE_VIRTUAL,
			Weight:  weightMap[tag.TagID],
		})
	}
	for _, tag := range channelTagResp.GetChannelTagList() {
		resp.List = append(resp.List, &pb.RecommendPoolChannelTag{
			TagId:   tag.GetTagId(),
			TagName: tag.GetName(),
			TagType: pb.RecommendPoolChannelTag_RECOMMEND_POOL_CHANNEL_TAG_TYPE_DEFAULT,
			Weight:  weightMap[tag.GetTagId()],
		})
	}
	for _, multi := range channelTagResp.GetChannelMultiTagList() {
		for _, tag := range multi.GetSubTagList() {
			resp.List = append(resp.List, &pb.RecommendPoolChannelTag{
				TagId:   tag.GetTagId(),
				TagName: tag.GetName(),
				TagType: pb.RecommendPoolChannelTag_RECOMMEND_POOL_CHANNEL_TAG_TYPE_DEFAULT,
				Weight:  weightMap[tag.GetTagId()],
			})
		}
	}
	return resp, nil
}

// CreateRecommendPoolChannel 运营后台 - 创建推荐池房间
func (m *Manager) CreateRecommendPoolChannel(ctx context.Context, channels []*pb.CreateRecommendPoolChannelReq_Channel, loc pb.RecommendChannelLocation) error {
	if len(channels) == 0 {
		return nil
	}
	maxCount := 100
	if len(channels) > maxCount {
		channels = channels[:maxCount]
		log.WarnWithCtx(ctx, "CreateRecommendPoolChannel too many channels, only process %d", maxCount)
	}
	log.InfoWithCtx(ctx, "CreateRecommendPoolChannel start channels:%v", channels)

	channelIds := make([]uint32, 0)
	for _, c := range channels {
		channelIds = append(channelIds, c.GetChannelId())
	}

	// get channels info
	channelInfoMap, sErr := rpc.ChannelCli.BatchGetChannelSimpleInfo(ctx, 0, channelIds)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "CreateRecommendPoolChannel BatchGetChannelSimpleInfo err:%v", sErr)
		return sErr
	}

	// get channels tag info
	channelTagMap, err := rpc.EntertainmentRecommendBackCli.BatchGetChannelTagMap(ctx, channelIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateRecommendPoolChannel BatchGetChannelTagMap err:%v", err)
		return err
	}

	// get recommend pool existed channels
	existedChannels, err := m.store.GetRecommendChannelListByCIds(ctx, channelIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateRecommendPoolChannel GetRecommendChannelListByCIds err:%v", err)
		return err
	}
	existedChannelsMap := make(map[uint32]*store.RecommendPoolChannel)
	for _, c := range existedChannels {
		existedChannelsMap[c.ChannelID] = c
	}

	recommendPoolChannels := make([]*store.RecommendPoolChannel, 0)
	for _, c := range channels {
		cid := c.ChannelId
		channel, ok := channelInfoMap[cid]
		if !ok {
			log.ErrorWithCtx(ctx, "CreateRecommendPoolChannel channel not exist, cid:%v", cid)
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("房间 %s cid %d 不存在", m.unreliableGetChannelViewId(ctx, cid), cid))
		}
		if channel.GetChannelType() != uint32(appChannelPB.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) &&
			channel.GetChannelType() != uint32(appChannelPB.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
			log.ErrorWithCtx(ctx, "CreateRecommendPoolChannel channel type not pgc, cid:%v", cid)
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("房间 %s 不是PGC房", m.unreliableGetChannelViewId(ctx, cid)))
		}

		var isVirtualTag bool
		var tagId uint32
		tag, ok := channelTagMap[cid]
		if ok && tag.GetTagId() > 0 {
			// 检查传入标签与实际标签一致
			if c.GetTagId() != tag.GetTagId() {
				log.ErrorWithCtx(ctx, "CreateRecommendPoolChannel tag not match, cid:%v, reqTagId:%v, realTagId:%v", cid, c.GetTagId(), tag.GetTagId())
				return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("房间 %s 标签与实际不符（%s）", m.unreliableGetChannelViewId(ctx, cid), tag.GetName()))
			}
			tagId = tag.GetTagId()
		} else {
			if c.GetTagId() == 0 {
				log.ErrorWithCtx(ctx, "CreateRecommendPoolChannel channel tag not exist, cid:%v", cid)
				return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("房间 %s 未设置标签", m.unreliableGetChannelViewId(ctx, cid)))
			}
			isVirtualTag = true
			virtualTag := m.dyconf.GetRelationshipConf().GetVirtualTag(c.GetTagId())
			if virtualTag.TagID == 0 {
				log.ErrorWithCtx(ctx, "CreateRecommendPoolChannel virtual tag not exist, tag:%v", c.GetTagId())
				return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("房间 %s 的品类ID %d 不存在", m.unreliableGetChannelViewId(ctx, cid), c.GetTagId()))
			}
			tagId = virtualTag.TagID
		}

		if existedChannel, ok := existedChannelsMap[cid]; ok {
			// 同一个房间，只允许设置同一个的虚拟标签的不同位置
			if existedChannel.IsVirtualTag && tagId != existedChannel.TagID {
				log.ErrorWithCtx(ctx, "CreateRecommendPoolChannel tag not match, cid:%v, oldTagId:%v, newTagId:%v", cid, existedChannel.TagID, tagId)
				return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("房间ID %s 已存在标签:%d", m.unreliableGetChannelViewId(ctx, cid), existedChannel.TagID))
			}
		}

		log.InfoWithCtx(ctx, "CreateRecommendPoolChannel channel:%v, tagId:%v, isVirtualTag:%v, loc:%v", cid, tagId, isVirtualTag, loc)

		recommendPoolChannels = append(recommendPoolChannels, &store.RecommendPoolChannel{
			ChannelID:    cid,
			IsVirtualTag: isVirtualTag,
			TagID:        tagId,
			Location:     loc,
			CreateTime:   time.Now(),
		})
	}

	if err = m.store.BatchCreateRecommendChannel(ctx, recommendPoolChannels); err != nil {
		log.ErrorWithCtx(ctx, "CreateRecommendPoolChannel BatchCreateRecommendChannel err:%v", err)
		return err
	}
	return nil
}

// DeleteRecommendPoolChannel 运营后台 - 修改推荐池房间
func (m *Manager) DeleteRecommendPoolChannel(ctx context.Context, ids []uint32) error {
	if len(ids) == 0 {
		return nil
	}
	log.InfoWithCtx(ctx, "DeleteRecommendPoolChannel start ids:%v", ids)
	if err := m.store.DeleteRecommendChannelById(ctx, ids); err != nil {
		log.ErrorWithCtx(ctx, "DeleteRecommendPoolChannel DeleteRecommendChannelById err:%v, ids:%v", err, ids)
		return err
	}
	return nil
}

// GetRecommendPoolChannelList 运营后台 - 获取推荐池房间列表
func (m *Manager) GetRecommendPoolChannelList(ctx context.Context, req *pb.GetRecommendPoolChannelListReq) (*pb.GetRecommendPoolChannelListResp, error) {
	resp := &pb.GetRecommendPoolChannelListResp{}
	resp.List = make([]*pb.RecommendPoolChannel, 0)
	if req.GetLimit() == 0 {
		req.Limit = 10
	}

	viewId := strings.TrimSpace(req.GetChannelViewId())
	if req.GetChannelViewId() != "" {
		cInfo, sErr := rpc.ChannelCli.GetChannelSimpleInfoByViewId(ctx, 0, viewId)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "GetRecommendPoolChannelList GetChannelSimpleInfoByViewId err:%v", sErr)
			return nil, sErr
		}
		req.ChannelId = cInfo.GetChannelId()
	}

	channels, count, err := m.store.GetRecommendChannelList(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendPoolChannelList GetRecommendChannelList err:%v", err)
		return nil, err
	}

	channelIds := make([]uint32, 0)
	tagIds := make([]uint32, 0)
	for _, channel := range channels {
		channelIds = append(channelIds, channel.ChannelID)
		tagIds = append(tagIds, channel.TagID)
	}

	channelInfoMap, err := rpc.ChannelCli.BatchGetChannelSimpleInfo(ctx, 0, channelIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendPoolChannelList BatchGetChannelSimpleInfo err:%v", err)
	}
	tagMap, err := m.GetChannelTagInfoMap(ctx, tagIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendPoolChannelList GetChannelTagInfoMap err:%v", err)
	}

	resp.Total = uint32(count)
	for _, channel := range channels {
		tmpC := &pb.RecommendPoolChannel{
			Id:           channel.ID,
			ChannelId:    channel.ChannelID,
			TagId:        channel.TagID,
			Location:     uint32(channel.Location),
			ActiveStatus: 1, // 这个没用，文档上有，先占位
		}
		if v, ok := tagMap[channel.TagID]; ok {
			tmpC.TagName = v.TagName
		}
		if v, ok := channelInfoMap[channel.ChannelID]; ok {
			tmpC.ChannelName = v.GetName()
			tmpC.ChannelViewId = v.GetChannelViewId()
		}
		resp.List = append(resp.List, tmpC)
	}
	return resp, nil
}

// CreateRecommendPoolTagWeight 运营后台 - 创建推荐池标签权重
func (m *Manager) CreateRecommendPoolTagWeight(ctx context.Context, req *pb.CreateRecommendPoolTagWeightReq) error {
	weight := req.GetWeight()
	if weight < TagWeightMin || weight > TagWeightMax {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("权重范围错误"))
	}

	weightList, err := m.GetChannelTagWeightListByLocation(ctx, req.GetLocation())
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateRecommendPoolTagWeight GetFullChannelTagWeightList err:%v", err)
		return err
	}
	weightSum := uint32(0)
	for _, w := range weightList {
		if w.TagId == req.GetTagId() {
			log.ErrorWithCtx(ctx, "CreateRecommendPoolTagWeight tag already exist, tagId:%v", req.GetTagId())
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("标签 %d 已存在", req.GetTagId()))
		}
		weightSum += w.Weight
	}
	if weightSum+weight > TagWeightMax {
		log.ErrorWithCtx(ctx, "CreateRecommendPoolTagWeight over max weight. weightSum:%v + weight:%v", weightSum, weight)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("权重总和不能超过 %d", 1))
	}

	err = m.store.CreateRecommendChannelTagWeight(ctx, req.GetTagId(), req.GetWeight(), req.GetLocation())
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateRecommendPoolTagWeight CreateRecommendChannelTagWeight err:%v", err)
		return err
	}
	return nil
}

// ModifyRecommendPoolTagWeight 运营后台 - 修改推荐池标签权重
func (m *Manager) ModifyRecommendPoolTagWeight(ctx context.Context, req *pb.CreateRecommendPoolTagWeightReq) error {
	weight := req.GetWeight()
	if weight < TagWeightMin || weight > TagWeightMax {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "权重范围错误")
	}

	weightList, err := m.GetChannelTagWeightListByLocation(ctx, req.GetLocation())
	if err != nil {
		log.ErrorWithCtx(ctx, "ModifyRecommendPoolTagWeight GetFullChannelTagWeightList err:%v", err)
		return err
	}
	weightSum := uint32(0)
	for _, w := range weightList {
		if w.TagId != req.GetTagId() { // 不包括自己
			weightSum += w.Weight
		}
	}
	if weightSum+weight > TagWeightMax {
		log.ErrorWithCtx(ctx, "ModifyRecommendPoolTagWeight over max weight. weightSum:%v + weight:%v", weightSum, weight)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("权重总和不能超过 %d", 1))
	}

	err = m.store.UpdateRecommendChannelTagWeight(ctx, req.GetTagId(), req.GetWeight(), req.GetLocation())
	if err != nil {
		log.ErrorWithCtx(ctx, "ModifyRecommendPoolTagWeight UpdateRecommendChannelTagWeight err:%v", err)
		return err
	}
	return nil
}

// DeleteRecommendPoolTagWeight 运营后台 - 删除推荐池标签权重
func (m *Manager) DeleteRecommendPoolTagWeight(ctx context.Context, ids []uint32) error {
	if len(ids) == 0 {
		return nil
	}
	if err := m.store.DeleteRecommendChannelTagWeight(ctx, ids); err != nil {
		log.ErrorWithCtx(ctx, "DeleteRecommendPoolTagWeight DeleteRecommendChannelTagWeight err:%v, ids:%v", err, ids)
		return err
	}
	return nil
}

// GetRecommendPoolTagWeightList 运营后台 - 获取推荐池标签权重列表
func (m *Manager) GetRecommendPoolTagWeightList(ctx context.Context, req *pb.GetRecommendPoolTagWeightListReq) (*pb.GetRecommendPoolTagWeightListResp, error) {
	resp := &pb.GetRecommendPoolTagWeightListResp{}
	resp.List = make([]*pb.RecommendPoolTagWeight, 0)
	if req.GetLimit() == 0 {
		req.Limit = 999
	}
	weights, count, err := m.store.GetRecommendChannelTagWeightList(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendPoolTagWeightList GetRecommendChannelTagWeightList err:%v", err)
		return nil, err
	}

	tagIds := make([]uint32, 0)
	for _, w := range weights {
		tagIds = append(tagIds, w.TagID)
	}
	tagMap, err := m.GetChannelTagInfoMap(ctx, tagIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendPoolTagWeightList GetChannelTagInfoMap err:%v", err)
	}

	for _, weight := range weights {
		tempW := &pb.RecommendPoolTagWeight{
			Id:       weight.ID,
			TagId:    weight.TagID,
			Weight:   weight.Weight,
			Location: uint32(weight.Location),
		}
		if v, ok := tagMap[weight.TagID]; ok {
			tempW.TagNane = v.TagName
		}
		resp.List = append(resp.List, tempW)
	}
	resp.Total = uint32(count)
	return resp, nil
}

// syncChannelTag 更新房间标签
func (m *Manager) syncChannelTag() {
	ctx := context.Background()
	// get channel id list
	channelList, err := m.GetFullPoolChannelList(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "syncChannelTag GetFullPoolChannelList err:%v", err)
		return
	}
	if len(channelList) == 0 {
		return
	}
	channelIdList := make([]uint32, 0)
	for _, channel := range channelList {
		channelIdList = append(channelIdList, channel.GetChannelId())
	}
	// in batches
	channel2TagMap := make(map[uint32]uint32)
	for i := 0; i < len(channelIdList); i += 100 {
		end := i + 100
		if end > len(channelIdList) {
			end = len(channelIdList)
		}
		// get channel tag
		tempMap, sErr := rpc.EntertainmentRecommendBackCli.BatchGetChannelTagMap(ctx, channelIdList[i:end])
		if sErr != nil {
			log.ErrorWithCtx(ctx, "syncChannelTag BatchGetChannelTagMap err:%v", sErr)
			return // 失败不处理，避免缺失误删
		}
		for cid, tagInfo := range tempMap {
			channel2TagMap[cid] = tagInfo.GetTagId()
		}
	}
	dyc := m.dyconf.GetRelationshipConf()
	// update
	for _, channel := range channelList {
		cid := channel.GetChannelId()
		tagId, ok := channel2TagMap[cid]
		if !ok || tagId == 0 { // 无标签信息
			if dyc.IsVirtualTag(channel.GetTagId()) {
				// 不删除虚拟标签
				continue
			}
			log.InfoWithCtx(ctx, "syncChannelTag channel tag not exist, cid:%v", cid)
			if err = m.store.DeleteRecommendChannelByCid(ctx, []uint32{cid}); err != nil {
				log.ErrorWithCtx(ctx, "syncChannelTag DeleteRecommendChannelByCid err:%v, cid:%v", err, cid)
			}
			continue
		}
		if tagId != channel.GetTagId() {
			// 修改了标签
			log.InfoWithCtx(ctx, "syncChannelTag channel tag not match, cid:%v, oldTagId:%v, newTagId:%v", cid, channel.GetTagId(), tagId)
			if err = m.store.UpdateRecommendChannelTag(ctx, cid, tagId, false); err != nil {
				log.ErrorWithCtx(ctx, "syncChannelTag DeleteRecommendChannel err:%v, cid:%v", err, cid)
			}
			continue
		}
	}
}

// GetFullPoolChannelList 获取全量推荐池房间列表
func (m *Manager) GetFullPoolChannelList(ctx context.Context) ([]*pb.RecommendPoolChannel, error) {
	list := make([]*pb.RecommendPoolChannel, 0)
	channels, _, err := m.store.GetRecommendChannelList(ctx, &pb.GetRecommendPoolChannelListReq{Limit: 99999})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFullPoolChannelList GetRecommendChannelList err:%v", err)
		return nil, err
	}
	for _, channel := range channels {
		list = append(list, &pb.RecommendPoolChannel{
			Id:           channel.ID,
			ChannelId:    channel.ChannelID,
			TagId:        channel.TagID,
			Location:     uint32(channel.Location),
			ActiveStatus: 1, // 这个没用，文档上有，先占位
		})
	}
	return list, nil
}

// GetFullChannelTagWeightList 获取全量推荐池标签权重列表
func (m *Manager) GetFullChannelTagWeightList(ctx context.Context) ([]*pb.RecommendPoolTagWeight, error) {
	list := make([]*pb.RecommendPoolTagWeight, 0)
	weights, _, err := m.store.GetRecommendChannelTagWeightList(ctx, &pb.GetRecommendPoolTagWeightListReq{
		Limit: 999,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFullChannelTagWeightList GetRecommendChannelTagWeightList err:%v", err)
		return nil, err
	}
	for _, w := range weights {
		list = append(list, &pb.RecommendPoolTagWeight{
			TagId:  w.TagID,
			Weight: w.Weight,
		})
	}
	return list, nil
}

// GetChannelTagWeightListByLocation 根据应用位置获取推荐池标签权重列表
func (m *Manager) GetChannelTagWeightListByLocation(ctx context.Context, location uint32) ([]*pb.RecommendPoolTagWeight, error) {
	list := make([]*pb.RecommendPoolTagWeight, 0)
	weights, _, err := m.store.GetRecommendChannelTagWeightList(ctx, &pb.GetRecommendPoolTagWeightListReq{
		Limit:     999,
		Locations: []uint32{location}})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFullChannelTagWeightList GetRecommendChannelTagWeightList err:%v", err)
		return nil, err
	}
	for _, w := range weights {
		list = append(list, &pb.RecommendPoolTagWeight{
			TagId:  w.TagID,
			Weight: w.Weight,
		})
	}
	return list, nil
}

type ChannelTag struct {
	TagID        uint32
	TagName      string
	IsVirtualTag bool
}

// GetChannelTagInfoMap 获取房间标签，包含虚拟标签
func (m *Manager) GetChannelTagInfoMap(ctx context.Context, tagIds []uint32) (map[uint32]ChannelTag, error) {
	tagMap := make(map[uint32]ChannelTag)
	notVirtualTagIds := make([]uint32, 0)
	dyc := m.dyconf.GetRelationshipConf()
	for _, tagId := range tagIds {
		if tagId == 0 {
			continue
		}
		if tag := dyc.GetVirtualTag(tagId); tag.TagID == 0 {
			notVirtualTagIds = append(notVirtualTagIds, tagId)
		} else {
			tagMap[tagId] = ChannelTag{
				TagID:        tag.TagID,
				TagName:      tag.TagName,
				IsVirtualTag: true,
			}
		}
	}
	if len(notVirtualTagIds) == 0 {
		return tagMap, nil
	}

	channelTagMap := make(map[uint32]*entertainmentRecommendBack.ChannelTagConfigInfo)
	// get tag map
	{
		channelTagResp, sErr := rpc.EntertainmentRecommendBackCli.GetChannelTagConfigInfo(ctx, 0, &entertainmentRecommendBack.GetChannelTagConfigInfoReq{})
		if sErr != nil {
			log.ErrorWithCtx(ctx, "GetChannelTagInfoMap GetChannelTagConfigInfo err:%v", sErr)
			return nil, sErr
		}
		for _, tag := range channelTagResp.GetChannelTagList() {
			channelTagMap[tag.GetTagId()] = tag
		}
		for _, multi := range channelTagResp.GetChannelMultiTagList() {
			for _, tag := range multi.GetSubTagList() {
				channelTagMap[tag.GetTagId()] = tag
			}
		}
	}

	for _, tagId := range notVirtualTagIds {
		if tag, ok := channelTagMap[tagId]; ok {
			tagMap[tagId] = ChannelTag{
				TagID:        tag.GetTagId(),
				TagName:      tag.GetName(),
				IsVirtualTag: false,
			}
		}
	}
	return tagMap, nil
}

// unreliableGetChannelViewId 获取房间viewId用于错误提示（不可靠）
func (m *Manager) unreliableGetChannelViewId(ctx context.Context, cid uint32) string {
	cInfo, sErr := rpc.ChannelCli.GetChannelSimpleInfo(ctx, 0, cid)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "unreliableGetChannelViewId GetChannelSimpleInfo err:%v", sErr)
		return strconv.Itoa(int(cid))
	}
	return cInfo.GetChannelViewId()
}
