package store

import (
	"context"
	"errors"
	"github.com/go-sql-driver/mysql"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channel-recommend-relationship"
	"gorm.io/gorm/clause"
	"time"
)

type RecommendChannelTagWeight struct {
	ID         uint32
	TagID      uint32
	Weight     uint32
	Location   pb.RecommendChannelLocation
	CreateTime time.Time
}

func (r *RecommendChannelTagWeight) TableName() string {
	return "recommend_channel_tag_weight"
}

type RecommendPoolChannel struct {
	ID           uint32
	ChannelID    uint32
	IsVirtualTag bool
	TagID        uint32
	Location     pb.RecommendChannelLocation
	CreateTime   time.Time
}

func (r *RecommendPoolChannel) TableName() string {
	return "recommend_pool_channel"
}

func (s *Store) BatchCreateRecommendChannel(ctx context.Context, channels []*RecommendPoolChannel) error {
	if len(channels) == 0 {
		return nil
	}
	tx := s.db.WithContext(ctx).Begin()
	for _, channel := range channels {
		if err := tx.Clauses(clause.OnConflict{
			//Columns:   []clause.Column{{Name: "tag_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"is_virtual_tag", "tag_id", "location"}),
		}).Create(channel).Error; err != nil {
			var mysqlErr *mysql.MySQLError
			if errors.As(err, &mysqlErr) && mysqlErr.Number == 1062 {
				continue
			}
			log.ErrorWithCtx(ctx, "BatchCreateRecommendChannel Create err: %+v", err)
			tx.Rollback()
			return err
		}
	}
	return tx.Commit().Error
}

func (s *Store) UpdateRecommendChannelTag(ctx context.Context, channelId, tagId uint32, isVirtual bool) error {
	if channelId == 0 || tagId == 0 {
		return nil
	}
	return s.db.WithContext(ctx).Model(&RecommendPoolChannel{}).
		Where("channel_id = ?", channelId).
		Updates(map[string]interface{}{
			"tag_id":         tagId,
			"is_virtual_tag": isVirtual,
		}).Error
}

func (s *Store) DeleteRecommendChannelById(ctx context.Context, ids []uint32) error {
	if len(ids) == 0 {
		return nil
	}
	return s.db.WithContext(ctx).Delete(&RecommendPoolChannel{}, "id in (?)", ids).Error
}

func (s *Store) DeleteRecommendChannelByCid(ctx context.Context, cidList []uint32) error {
	if len(cidList) == 0 {
		return nil
	}
	return s.db.WithContext(ctx).Delete(&RecommendPoolChannel{}, "channel_id in (?)", cidList).Error
}

func (s *Store) GetRecommendChannelList(ctx context.Context, req *pb.GetRecommendPoolChannelListReq) ([]*RecommendPoolChannel, int64, error) {
	var count int64
	channels := make([]*RecommendPoolChannel, 0)
	q := s.db.WithContext(ctx).Model(&RecommendPoolChannel{})
	if req.GetChannelId() > 0 {
		q = q.Where("channel_id = ?", req.GetChannelId())
	}
	if req.GetTagId() > 0 {
		q = q.Where("tag_id = ?", req.GetTagId())
	}
	if len(req.GetLocations()) > 0 {
		q = q.Where("location in (?)", req.GetLocations())
	}
	if err := q.Count(&count).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendChannelList Count err: %+v", err)
		return nil, count, err
	}
	if count == 0 {
		return channels, count, nil
	}
	if err := q.
		Offset(int(req.GetOffset())).
		Limit(int(req.GetLimit())).
		Order("create_time desc").
		Find(&channels).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendChannelList Find err: %+v", err)
		return nil, 0, err
	}
	return channels, count, nil
}

func (s *Store) GetRecommendChannelListByCIds(ctx context.Context, cidList []uint32) ([]*RecommendPoolChannel, error) {
	var channels []*RecommendPoolChannel
	if len(cidList) == 0 {
		return channels, nil
	}
	if err := s.db.WithContext(ctx).Model(&RecommendPoolChannel{}).
		Where("channel_id in (?)", cidList).
		Find(&channels).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendChannelListByCid Find err: %+v", err)
		return nil, err
	}
	return channels, nil
}

func (s *Store) CreateRecommendChannelTagWeight(ctx context.Context, TagID, weight, location uint32) error {
	if TagID == 0 {
		return nil
	}
	return s.db.WithContext(ctx).Clauses(clause.OnConflict{
		//Columns:   []clause.Column{{Name: "tag_id"}},
		//DoUpdates: clause.AssignmentColumns([]string{"weight"}),
	}).Create(&RecommendChannelTagWeight{
		TagID:      TagID,
		Weight:     weight,
		Location:   pb.RecommendChannelLocation(location),
		CreateTime: time.Now(),
	}).Error
}

func (s *Store) UpdateRecommendChannelTagWeight(ctx context.Context, TagID, weight, location uint32) error {
	if TagID == 0 {
		return nil
	}
	return s.db.WithContext(ctx).Model(&RecommendChannelTagWeight{}).
		Where("tag_id = ? and location=?", TagID, location).
		Update("weight", weight).Error
}

func (s *Store) DeleteRecommendChannelTagWeight(ctx context.Context, ids []uint32) error {
	if len(ids) == 0 {
		return nil
	}
	return s.db.WithContext(ctx).Delete(&RecommendChannelTagWeight{}, "id in (?)", ids).Error
}

func (s *Store) GetRecommendChannelTagWeightList(ctx context.Context, req *pb.GetRecommendPoolTagWeightListReq) ([]*RecommendChannelTagWeight, int64, error) {
	var weights []*RecommendChannelTagWeight
	count := int64(0)
	q := s.db.WithContext(ctx).Model(&RecommendChannelTagWeight{})
	if len(req.GetLocations()) > 0 {
		q = q.Where("location in (?)", req.GetLocations())
	}
	if err := q.Count(&count).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendChannelTagWeightList Count err: %+v", err)
		return nil, count, err
	}
	if err := q.
		Offset(int(req.GetOffset())).
		Limit(int(req.GetLimit())).
		Order("create_time desc").
		Find(&weights).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetRecommendChannelTagWeightList Find err: %+v", err)
		return nil, 0, err
	}
	return weights, count, nil
}
