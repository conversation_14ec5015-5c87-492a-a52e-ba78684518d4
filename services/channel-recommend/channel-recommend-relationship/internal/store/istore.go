package store

import (
	"context"
	pb "golang.52tt.com/protocol/services/channel-recommend-relationship"
)

type IStore interface {
	BatchCreateRecommendChannel(ctx context.Context, channels []*RecommendPoolChannel) error
	UpdateRecommendChannelTag(ctx context.Context, channelId, tagId uint32, isVirtual bool) error
	DeleteRecommendChannelByCid(ctx context.Context, cidList []uint32) error
	DeleteRecommendChannelById(ctx context.Context, ids []uint32) error
	GetRecommendChannelList(ctx context.Context, req *pb.GetRecommendPoolChannelListReq) ([]*RecommendPoolChannel, int64, error)
	GetRecommendChannelListByCIds(ctx context.Context, cidList []uint32) ([]*RecommendPoolChannel, error)
	CreateRecommendChannelTagWeight(ctx context.Context, TagID, weight, location uint32) error
	UpdateRecommendChannelTagWeight(ctx context.Context, TagID, weight, location uint32) error
	DeleteRecommendChannelTagWeight(ctx context.Context, ids []uint32) error
	GetRecommendChannelTagWeightList(ctx context.Context, req *pb.GetRecommendPoolTagWeightListReq) ([]*RecommendChannelTagWeight, int64, error)
}
