package utils

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"github.com/go-gomail/gomail"
	"golang.52tt.com/pkg/log"
	"math/rand" //#nosec
	"net/http"
	"os"
	"strings"
	"time"
)

const (
	SvrName      = "backpack-rick-control"
	SecretKeyLen = 16 //密钥长度
)

func GenTableName(temp string, ts time.Time) string {
	return fmt.Sprintf(temp, ts.Year(), int(ts.Month()))
}

func GetTimeStr(ts time.Time) string {
	return ts.Format("2006-01-02 15:04:05")
}

func GetMinStr(ts time.Time) string {
	return ts.Format("2006-01-02-15-04")
}

func GetHourStr(ts time.Time) string {
	return ts.Format("2006-01-02-15")
}

func GetMonStr(ts time.Time) string {
	return ts.Format("2006-01")
}

func GetDayStr(ts time.Time) string {
	return ts.Format("2006-01-02")
}

var httpCli = &http.Client{}

type ContextT struct {
	Text string `json:"text"`
}

type ReportMsg struct {
	Title   string    `json:"title"`
	MsgType string    `json:"msg_type"`
	Context *ContextT `json:"content"`
}

func CheckRatioLimit(currValue, limitValue uint64, ratio float64) bool {
	if currValue <= 0 {
		return false
	}
	if limitValue == 0 {
		return false
	}
	return float64(currValue) > (float64(limitValue) * ratio)
}

func SendReportMsgToFeishu(msg, url string) error {
	nowTime := time.Now()
	msg = fmt.Sprintf("%v\n", nowTime.Format("2006-01-02 15:04:05")) + msg
	rmsg := &ReportMsg{
		Title:   "风控服务阻塞告警",
		MsgType: "text",
		Context: &ContextT{Text: msg},
	}
	bmsg, _ := json.Marshal(rmsg)
	buff := bytes.NewBuffer(bmsg)

	var err error
	for i := 0; i < 16; i++ {
		rsp, err := httpCli.Post(url, "application/json", buff)
		if nil == err {
			if rsp != nil && rsp.Body != nil {
				rsp.Body.Close()
			}
			break
		} else {
			time.Sleep(time.Second * 2)
		}
	}

	log.InfoWithCtx(context.Background(), "SendReportMsgToFeishu msg:%v, err:%v", msg, err)

	return err
}

func DaemonFunc(f func() error, intervals time.Duration) {
	go func() {
		t := time.NewTicker(intervals)
		for range t.C {
			func() {
				defer func() {
					if r := recover(); r != nil {
						log.ErrorWithCtx(context.Background(), "LoopFunc recover r:%v", r)
					}
				}()
				f()
			}()
		}
	}()
}

func LoopFunc(f func() error) {
	go func() {
		for {
			func() {
				defer func() {
					if r := recover(); r != nil {
						log.ErrorWithCtx(context.Background(), "LoopFunc recover r:%v", r)
					}
				}()
				f()
			}()
		}
	}()
}

// 填充16倍数
func Padding(org []byte, blockSize int) []byte {
	pad := blockSize - len(org)%blockSize
	padArr := bytes.Repeat([]byte{byte(pad)}, pad)
	return append(org, padArr...)
}

// 去掉填充
func UnPadding(org []byte) []byte {
	l := len(org)
	if l <= 0 {
		return org
	}
	pad := int(org[l-1])
	if l < pad {
		return org
	}
	return org[:l-pad]
}

// AES解密
func AESDecrypt(cipherTxt []byte, key []byte) []byte {

	log.InfoWithCtx(context.Background(), "AESDecrypt %v %v \n", cipherTxt, key)

	block, _ := aes.NewCipher(key)
	blockMode := cipher.NewCBCDecrypter(block, key)
	org := make([]byte, len(cipherTxt))
	blockMode.CryptBlocks(org, cipherTxt)
	org = UnPadding(org)
	return org
}

// AES加密
func AESEncrypt(org []byte, key []byte) []byte {
	block, _ := aes.NewCipher(key)
	org = Padding(org, block.BlockSize())
	blockMode := cipher.NewCBCEncrypter(block, key)
	cryted := make([]byte, len(org))
	blockMode.CryptBlocks(cryted, org)
	return cryted
}

// 随机KEY
func GenAESKey() []byte {
	rand.Seed(time.Now().UnixNano())

	result := make([]byte, SecretKeyLen)
	for i := 0; i < SecretKeyLen; i++ {
		result[i] = byte(rand.Int()%26 + 97) //#nosec
	}
	return result
}

func CheckAuth(cipherTxt []byte, key []byte, orderID string) bool {
	valid := true
	//1、直接当密钥检查
	if len(cipherTxt) == len(key) {
		for i, v := range cipherTxt {
			if key[i] != v {
				valid = false
				break
			}
		}
		return valid
	}

	//2、解密出来然后在比较
	tmpOrderID := AESDecrypt(cipherTxt, key)

	log.DebugfWithCtx(context.Background(), "CheckAuth AESDecrypt tmpOrderID:%v orderID:%v", string(tmpOrderID), orderID)

	valid = string(tmpOrderID) == orderID

	return valid
}

func SendEmail(content, subject string, to []string) {
	MyCluster := os.Getenv("MY_CLUSTER")
	if strings.HasPrefix(MyCluster, "dev") || strings.HasPrefix(MyCluster, "test") {
		subject = "【测试环境】" + subject
	} else {
		subject = "【生产环境】" + subject
	}
	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")

	m.SetHeader("To", to...)
	m.SetHeader("Subject", subject)

	m.SetBody("text/html", content)

	d := gomail.NewDialer("mail.52tt.com", 465, "<EMAIL>", "V7.D.sTy@%bDB50t#n.r$A4h+-FnXA")

	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		log.Errorf("SendMail: %v, subject:%v err:%v", to, subject, err)
		return
	}
	log.Infof("SendMail: %v, ok", to)
}
