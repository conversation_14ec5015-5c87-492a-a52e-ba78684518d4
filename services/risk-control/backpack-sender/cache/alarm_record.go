package cache

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
	"strings"
	"time"
)

// 记录风控告警，便于恢复

func KeyAlarmRecord(isHour bool, t time.Time) string {
	if isHour {
		return fmt.Sprintf("RiskAlarmRecordH_%s_%d", t.Format("20060102"), t.Hour())
	}
	return fmt.Sprintf("RiskAlarmRecordH_%s", t.Format("20060102"))
}

func (r *BackpackSenderCache) SetAlarmRecord(ctx context.Context, bid, bgid uint32, t time.Time, isHour bool, scheduleUser, scheduleMail string) error {
	key := KeyAlarmRecord(isHour, t)
	hkey := fmt.Sprintf("%d,%d", bid, bgid)
	value := fmt.Sprintf("%s,%s", scheduleUser, scheduleMail)
	err := r.redisClient.HSet(key, hkey, value).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetAlarmRecord redis hset key:%v,hkey:%v, error: %v", key, hkey, err)
		return err
	}
	expireT := time.Hour * 24 * 3
	if isHour {
		expireT = time.Hour * 24
	}
	err = r.redisClient.Expire(key, expireT).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetAlarmRecord redis expire key:%v, error: %v", key, err)
	}
	return nil
}

func (r *BackpackSenderCache) GetAlarmRecord(ctx context.Context, bid, bgid uint32, isHour bool, t time.Time) (bool, string, string, error) {
	key := KeyAlarmRecord(isHour, t)
	hkey := fmt.Sprintf("%d,%d", bid, bgid)
	value, err := r.redisClient.HGet(key, hkey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return false, "", "", nil
		}
		log.ErrorWithCtx(ctx, "GetAlarmRecord redis get key:%v, hkey:%v, error: %v", key, hkey, err)
		return false, "", "", err
	}
	arr := strings.Split(value, ",")
	return true, arr[0], arr[1], nil
}

func (r *BackpackSenderCache) DelAlarmRecord(ctx context.Context, bid, bgid uint32, isHour bool, t time.Time) error {
	key := KeyAlarmRecord(isHour, t)
	hkey := fmt.Sprintf("%d,%d", bid, bgid)
	_, err := r.redisClient.HDel(key, hkey).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelAlarmRecord redis del key:%v,hkey:%v error: %v", key, hkey, err)
		return err
	}
	return nil
}

func (r *BackpackSenderCache) GetAlarmRecordAll(ctx context.Context, isHour bool, t time.Time) (map[string]string, error) {
	key := KeyAlarmRecord(isHour, t)
	result, err := r.redisClient.HGetAll(key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetAlarmRecordAll redis get key:%v error: %v", key, err)
		return nil, err
	}
	return result, nil
}
