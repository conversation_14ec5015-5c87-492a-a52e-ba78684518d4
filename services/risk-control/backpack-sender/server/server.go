package server

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/coroutine"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/timer"
	"golang.52tt.com/protocol/common/status"
	userpresent_go "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/risk-control/backpack-sender/manager"
	"time"

	"github.com/pkg/errors"
	"golang.52tt.com/clients/backpack-base"
	backpackPb "golang.52tt.com/protocol/services/backpack-base"
	pb "golang.52tt.com/protocol/services/backpacksender"
	"golang.52tt.com/services/risk-control/backpack-sender/conf"
)

type BackpackSenderServer struct {
	mgr             *manager.Manager
	backpackBaseCli backpack_base.IClient
	presentCli      userpresent_go.UserPresentGOClient
	timerD          *timer.Timer
}

func NewBackpackSenderServer(ctx context.Context) (*BackpackSenderServer, error) {

	sc := &conf.ServiceConfigT{}

	cfgPath := ctx.Value("configfile").(string)
	if cfgPath == "" {
		return nil, errors.New("configfile not exist")
	}
	err := sc.Parse(cfgPath)
	if err != nil {
		return nil, err
	}

	mgr, err := manager.NewManager(ctx, sc)
	if nil != err {
		log.ErrorWithCtx(ctx, "NewManager fail err:%v", err)
		return nil, err
	}

	timerD, err := timer.NewTimerD(ctx, "backpack-sender", timer.WithTTL(10*time.Second))
	if nil != err {
		log.ErrorWithCtx(ctx, "init NewTimerD fail, err: %v", err)
		return nil, err
	}

	// 每小时检查一次告警恢复
	err = timerD.AddTask("4 1 * * * *", "CheckAndReconverAlarmTimer", timer.BuildFromLambda(func(ctx context.Context) {
		mgr.CheckAndReconverAlarmTimer()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init CheckAndReconverAlarmTimer fail, err: %v", err)
		return nil, err
	}
	timerD.Start()

	//先更新缓存
	mgr.UpdateLocalCache()

	coroutine.FixIntervalExec(mgr.UpdateLocalCache, time.Second*time.Duration(conf.GetUpdateLocalCacheInterval()))

	coroutine.FixIntervalExec(mgr.MonitorServer, time.Minute)
	coroutine.FixIntervalExec(mgr.MonitorBalance, time.Minute)
	coroutine.FixIntervalExec(mgr.TableCreater, time.Hour)

	for i := 0; i < 2; i++ {
		coroutine.FixIntervalExec(mgr.Producer, time.Second*5)
	}

	for i := 0; i < 32; i++ {
		coroutine.FixIntervalExec(mgr.Consumer, time.Millisecond)
	}

	bpBaseCli := backpack_base.NewIClient()
	presentCli, _ := userpresent_go.NewClient(ctx)
	return &BackpackSenderServer{
		mgr:             mgr,
		backpackBaseCli: bpBaseCli,
		presentCli:      presentCli,
		timerD:          timerD,
	}, nil
}

// 检查时间戳合法性
func CheckTimeStampValid(ts int64) bool {
	now := time.Now().Unix()
	if ts == 0 {
		return true
	}
	diff := int64(0)
	if now > ts {
		diff = now - ts
	} else {
		diff = ts - now
	}
	return diff < 8*24*3600
}

func (s *BackpackSenderServer) SendBackpackWithRiskControl(ctx context.Context, in *pb.SendBackpackWithRiskControlReq) (out *pb.SendBackpackWithRiskControlResp, err error) {
	log.DebugWithCtx(ctx, "SendBackpackWithRiskControl in:%v", in)

	out = &pb.SendBackpackWithRiskControlResp{}

	//dealtoken校验
	err = s.mgr.CheckDealToken(ctx, in.DealToken, in.BusinessId)
	if nil != err {
		log.ErrorWithCtx(ctx, "SendBackpackWithRiskControl CheckDealToken in:%v err:%v", in, err)
		return out, err
	}

	//用于统一时间，必填
	if 0 == in.ServerTime {
		log.ErrorWithCtx(ctx, "SendBackpackWithRiskControl para invalid in:%+v err:%v", in, err)
		in.ServerTime = time.Now().Unix()
	}

	if !CheckTimeStampValid(in.ServerTime) {
		log.ErrorWithCtx(ctx, "SendBackpackWithRiskControl check in:%+v, ts err", in)
		return out, protocol.NewExactServerError(nil, status.ErrBackpackTimestampInvalid)
	}

	orderInfo := &pb.SendBackpackOrderInfo{
		BusinessId:     in.BusinessId,
		BackpackId:     in.BackpackId,
		ReceiveUid:     in.ReceiveUid,
		BackpackCnt:    in.BackpackCnt,
		OutsideTime:    in.ServerTime,
		ServerTime:     time.Now().Unix(),
		OrderId:        in.OrderId,
		Ciphertext:     in.Ciphertext,
		ExpireDuration: in.ExpireDuration,
		SourceAppId:    in.SourceAppId,
	}

	//风控检查 & 订单入库
	err = s.mgr.HandlerOrder(ctx, orderInfo)
	log.DebugWithCtx(ctx, "SendBackpackWithRiskControl in:%v out:%v err:%v", in, out, err)
	return out, err
}

func (s *BackpackSenderServer) SendOneBackpackWithRiskControl(ctx context.Context, in *pb.SendOneBackpackWithRiskControlReq) (out *pb.SendOneBackpackWithRiskControlResp, err error) {
	log.DebugWithCtx(ctx, "SendOneBackpackWithRiskControl in:%v", in)

	out = &pb.SendOneBackpackWithRiskControlResp{}

	//dealtoken校验
	err = s.mgr.CheckDealToken(ctx, in.DealToken, in.BusinessId)
	if nil != err {
		log.ErrorWithCtx(ctx, "SendOneBackpackWithRiskControl CheckDealToken in:%v err:%v", in, err)
		return out, err
	}

	//用于统一时间，必填
	if 0 == in.ServerTime {
		log.ErrorWithCtx(ctx, "SendOneBackpackWithRiskControl para invalid in:%+v err:%v", in, err)
		in.ServerTime = time.Now().Unix()
	}

	orderInfo := &pb.SendBackpackOrderInfo{
		BusinessId:     in.BusinessId,
		BackpackId:     in.BackpackId,
		ReceiveUid:     in.ReceiveUid,
		BackpackCnt:    1,
		OutsideTime:    in.ServerTime,
		ServerTime:     time.Now().Unix(),
		OrderId:        in.OrderId,
		Ciphertext:     in.Ciphertext,
		ExpireDuration: in.ExpireDuration,
		SourceAppId:    in.SourceAppId,
	}

	//风控检查 & 订单入库
	err = s.mgr.HandlerOrder(ctx, orderInfo)

	log.DebugWithCtx(ctx, "SendOneBackpackWithRiskControl in:%v out:%v err:%v", in, out, err)

	return out, err
}

func (s *BackpackSenderServer) DeductUserBackpackWithRiskControl(ctx context.Context, in *pb.DeductUserBackpackWithRiskControlReq) (out *pb.DeductUserBackpackWithRiskControlResp, err error) {
	log.DebugWithCtx(ctx, "SendSingleBackpackWithRiskControl in:%v", in)

	out = &pb.DeductUserBackpackWithRiskControlResp{}
	orderInfo := &pb.SendBackpackOrderInfo{
		BusinessId: in.BusinessId,
		ServerTime: in.ServerTime,
		Ciphertext: in.Ciphertext,
		OrderId:    in.OrderId,
	}

	//订单格式检查
	perr := s.mgr.CheckOrderIdFormat(orderInfo)
	if nil != perr {
		log.ErrorWithCtx(ctx, "HandlerOrder CheckOrderIdFormat fail orderInfo:%v err:%v", orderInfo, perr)
		return out, perr
	}

	//检查business是否合法
	_, perr = s.mgr.CheckBusinessPermission(ctx, orderInfo)

	if nil != perr {
		log.ErrorWithCtx(ctx, "HandlerOrder CheckBusiness fail orderInfo:%v err:%v", orderInfo, perr)
		return out, perr
	}

	//发起请求
	detailList := make([]*backpackPb.DeductDetail, 0)
	for _, item := range in.DeductList {
		deductItemList := make([]*backpackPb.DeductItem, 0)
		for _, it := range item.ItemList {
			deductItem := &backpackPb.DeductItem{Count: it.Count, ItemType: it.ItemType, SourceId: it.SourceId}
			deductItemList = append(deductItemList, deductItem)
		}
		reqItem := &backpackPb.DeductDetail{Uid: item.Uid, Count: item.Count, SourceType: item.SourceType, IsAllSource: item.IsAllSource, ItemList: deductItemList}
		detailList = append(detailList, reqItem)
	}

	resp, err := s.backpackBaseCli.BatchDeductUserItem(ctx, &backpackPb.BatchDeductUserItemReq{OrderId: in.OrderId, Oper: in.Oper, DeductType: in.DeductType,
		DeductList: detailList})
	if nil != err {
		log.ErrorWithCtx(ctx, "BatchDeductUserItem fail orderInfo:%v err:%v", orderInfo, err)
		return out, err
	}

	resultList := make([]*pb.DeductResult, 0)
	log.DebugWithCtx(ctx, "BatchDeductUserItem resp %v", resp)
	for _, item := range resp.DeductList {
		sucItemList := make([]*pb.DeductItem, 0)
		for _, it := range item.SuccessItemList {
			sucItem := &pb.DeductItem{Count: it.Count, ItemType: it.ItemType, SourceId: it.SourceId}
			sucItemList = append(sucItemList, sucItem)
		}
		failItemList := make([]*pb.DeductItem, 0)
		for _, it := range item.FailItemList {
			failItem := &pb.DeductItem{Count: it.Count, ItemType: it.ItemType, SourceId: it.SourceId}
			failItemList = append(failItemList, failItem)
		}
		reqItem := &pb.DeductResult{Uid: item.Uid, FailType: item.FailType, SuccessItemList: sucItemList, FailItemList: failItemList}
		resultList = append(resultList, reqItem)
	}
	out.DeductList = resultList

	log.InfoWithCtx(ctx, "SendSingleBackpackWithRiskControl in:%v out:%v , err : %v", in, out, err)

	return out, err
}

func (s *BackpackSenderServer) BatchSendBackpackWithRiskControl(ctx context.Context, req *pb.BatchSendBackpackWithRiskControlReq) (out *pb.BatchSendBackpackWithRiskControlResp, err error) {

	out = &pb.BatchSendBackpackWithRiskControlResp{}

	log.InfoWithCtx(ctx, "BatchSendBackpackWithRiskControl req:%v", req)
	//log.DebugWithCtx(

	for _, item := range req.SendPackList {
		orderInfo := &pb.SendBackpackOrderInfo{
			BusinessId:     req.BusinessId,
			BackpackId:     item.BgId,
			ReceiveUid:     item.Uid,
			BackpackCnt:    item.BgCnt,
			OutsideTime:    req.ServerTime,
			ServerTime:     time.Now().Unix(),
			OrderId:        fmt.Sprintf("%v_%v_%v_%v", req.BusinessId, req.OrderId, item.Uid, item.BgId),
			Ciphertext:     req.Ciphertext,
			ExpireDuration: item.ExpireDuration,
		}

		//风控检查 & 订单入库
		serr := s.mgr.HandlerOrder(ctx, orderInfo)
		if nil != serr {
			log.ErrorWithCtx(ctx, "BatchSendBackpackWithRiskControl HandlerOrder serr:%v", serr)
			if serr.Code() != status.ErrRiskControlBackpackDuplicateOrderid {
				err = serr
				break
			}
		}
	}

	if nil != err {
		log.ErrorWithCtx(ctx, "BatchSendBackpackWithRiskControl req:%v err:%v", req, err)
	}

	log.DebugWithCtx(ctx, "BatchSendBackpackWithRiskControl req:%v", req)

	return out, err
}

func (s *BackpackSenderServer) ShutDown() {
	log.InfoWithCtx(context.Background(), "ShutDown")
}

func (s *BackpackSenderServer) ReSendSecretKeyEmail(ctx context.Context, req *pb.ReSendSecretKeyEmailReq) (*pb.ReSendSecretKeyEmailResp, error) {
	rsp := &pb.ReSendSecretKeyEmailResp{}
	if req.BusinessId == 0 {
		log.ErrorWithCtx(ctx, "ReSendSecretKeyEmail req:%v", req.String())
		return rsp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	err := s.mgr.ResendBusinessSecret(ctx, req.BusinessId, req.IsRecreate)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReSendSecretKeyEmail failed req:%v, err:%v", req.String(), err)
		return rsp, err
	}
	log.InfoWithCtx(ctx, "ReSendSecretKeyEmail ok req:%v", req.String())
	return rsp, nil
}

// PreCheckBussinessRiskControl 前置检查业务风控
func (s *BackpackSenderServer) PreCheckBussinessRiskControl(ctx context.Context, req *pb.PreCheckBussinessRiskControlReq) (*pb.PreCheckBussinessRiskControlResp, error) {
	rsp := &pb.PreCheckBussinessRiskControlResp{}
	if req.BusinessId == 0 || len(req.BgidCnts) == 0 {
		log.ErrorWithCtx(ctx, "PreCheckBussinessRiskControl req:%v", req.String())
		return rsp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	err := s.mgr.OnlyCheckRisk(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "PreCheckBussinessRiskControl failed req:%v, err:%v", req.String(), err)
		return rsp, err
	}
	log.InfoWithCtx(ctx, "PreCheckBussinessRiskControl ok req:%v", req.String())
	return rsp, nil
}
