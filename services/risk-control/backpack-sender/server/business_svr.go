package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/backpacksender"
	userpresent_go "golang.52tt.com/protocol/services/userpresent-go"
)

func (s *BackpackSenderServer) AddBusiness(ctx context.Context, in *pb.AddBusinessReq) (out *pb.AddBusinessResp, err error) {

	log.InfoWithCtx(ctx, "AddBusinessReq in:%v", in)

	out = &pb.AddBusinessResp{}

	if in.BusinessConf.SourceId == 0 {
		log.ErrorWithCtx(ctx, "AddBusiness fail invalid para sourceId in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "invalid SourceId")
	}

	if in.BusinessConf.OperaUser == "" {
		log.ErrorWithCtx(ctx, "AddBusiness fail invalid para OperaUser in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "invalid para OperaUser")
	}

	if in.BusinessConf.BeginTime == "" {
		in.BusinessConf.BeginTime = "2021-01-01 01:00:00"
	}

	if in.BusinessConf.EndTime == "" {
		in.BusinessConf.EndTime = "2038-01-01 01:00:00"
	}

	businessId, err := s.mgr.AddBusiness(ctx, in.BusinessConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddBusiness failed in:%v err:%v", in, err)
		return out, err
	}

	bsConfs, err := s.mgr.GetBusiness(ctx, businessId)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddBusiness GetBusiness failed id:%v err:%v", in, err)
	}

	for _, conf := range bsConfs {
		if conf.BusinessId == businessId {
			out.BusinessConf = &pb.BusinessConf{
				Name:        conf.Name,
				Desc:        conf.Desc,
				CallbackUrl: conf.CallbackUrl,
				SourceId:    conf.SourceId,
				OperaUser:   conf.OperaUser,
				BeginTime:   conf.BeginTime,
				EndTime:     conf.EndTime,
				BusinessId:  conf.BusinessId,
				//SecretKey:      conf.SecretKey,
				WarningPrecent: conf.WarningPrecent,
			}
			break
		}
	}

	_, err = s.presentCli.LinkItemToActivity(ctx, &userpresent_go.LinkItemToActivityReq{
		ActivityId:   in.GetBusinessConf().GetActivityId(),
		LinkItemType: userpresent_go.LinkItemType_LINK_ITEM_TYPE_PACKAGE,
		ItemId:       businessId,
		OperateUser:  in.GetBusinessConf().GetOperateUser(),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "AddBusiness LinkItemToActivity error: %v", err)
	}

	log.InfoWithCtx(ctx, "AddBusinessReq in:%v out:%v err:%v", in, out, err)

	return out, err
}

func (s *BackpackSenderServer) GetAllBusiness(ctx context.Context, _ *pb.GetAllBusinessReq) (out *pb.GetAllBusinessResp, err error) {
	log.DebugWithCtx(ctx, "GetAllBusiness")

	out = &pb.GetAllBusinessResp{}

	businessList, err := s.mgr.GetBusiness(ctx, 0)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetBusiness err:%v", err)
		return out, err
	}

	out.BusinessList = businessList
	log.DebugWithCtx(ctx, "GetAllBusiness out:%v", out)

	return out, nil
}

func (s *BackpackSenderServer) GetBusinessByIds(ctx context.Context, in *pb.GetBusinessByIdsReq) (*pb.GetBusinessByIdsResp, error) {
	out := &pb.GetBusinessByIdsResp{}

	log.InfoWithCtx(ctx, "GetBusinessByIds begin in %v", in)

	businessList, err := s.mgr.GetBusiness(ctx, 0)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetBusiness err:%v", err)
		return out, err
	}

	for _, info := range businessList {
		for _, id := range in.GetIdList() {
			if id == info.GetBusinessId() {
				out.BusinessList = append(out.BusinessList, info)
				break
			}
		}
	}
	log.InfoWithCtx(ctx, "GetBusinessByIds end out:%v", out)
	return out, nil
}

func (s *BackpackSenderServer) AddBusinessRiskControlConf(ctx context.Context, in *pb.AddBusinessRiskControlConfReq) (out *pb.AddBusinessRiskControlConfResp, err error) {

	log.InfoWithCtx(ctx, "AddBusinessRiskControlConf in:%v", in)

	out = &pb.AddBusinessRiskControlConfResp{}

	if in.BusinessRiskConf.OperaUser == "" {
		log.ErrorWithCtx(ctx, "AddBusinessRiskControlConf fail invalid OperaUser in:%v err:%v", in, err)
		return out, err
	}

	_, err = s.mgr.GetBusiness(ctx, in.BusinessRiskConf.BusinessId)
	if nil != err {
		log.ErrorWithCtx(ctx, "AddBusinessRiskControlConf fail in:%v err:%v", in, err)
		return out, err
	}

	err = s.mgr.AddBusinessRiskControlConf(ctx, in.BusinessRiskConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddBusinessRiskControlConf failed in:%v err:%v", in, err)
		return out, err
	}

	confList, err := s.mgr.GetBusinessRiskControlConf(ctx, in.GetBusinessRiskConf().GetBusinessId())
	if nil != err {
		log.ErrorWithCtx(ctx, "AddBusinessRiskControlConf GetBusinessRiskControlConf in;%v err:%v", in, err)
	}

	for _, conf := range confList {
		if conf.BgId == in.GetBusinessRiskConf().GetBgId() {
			out.BusinessRiskConf = conf
			break
		}
	}

	s.mgr.UpdateLocalCache()

	log.InfoWithCtx(ctx, "AddBusinessRiskControlConf in:%v out:%v err:%v", in, out, err)
	return out, nil
}

func (s *BackpackSenderServer) BatchAddOrModBusinessRiskControlConf(ctx context.Context, in *pb.BatchAddOrModBusinessRiskControlConfReq) (out *pb.BatchAddOrModBusinessRiskControlConfResp, err error) {
	log.InfoWithCtx(ctx, "BatchAddOrModBusinessRiskControlConf in:%v", in)
	out = &pb.BatchAddOrModBusinessRiskControlConfResp{}
	err = s.mgr.BatchAddOrModBusinessRiskControlConf(ctx, in.AddRiskList, in.ModRiskList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchAddOrModBusinessRiskControlConf failed in:%v err:%v", in, err)
		return out, err
	}
	log.InfoWithCtx(ctx, "BatchAddOrModBusinessRiskControlConf ok in:%v", in)
	return out, nil
}

func (s *BackpackSenderServer) ModBusinessRiskControlConf(ctx context.Context, in *pb.ModBusinessRiskControlConfReq) (out *pb.ModBusinessRiskControlConfResp, err error) {
	log.InfoWithCtx(ctx, "ModBusinessRiskControlConf in:%v", in)

	out = &pb.ModBusinessRiskControlConfResp{}
	err = s.mgr.ModBusinessRiskControlConf(ctx, in.BusinessRiskConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "ModBusinessRiskControlConf failed in:%v err:%v", in, err)
		return out, err
	}
	log.InfoWithCtx(ctx, "ModBusinessRiskControlConf in:%v out:%v err:%v", in, out, err)

	return out, nil
}

func (s *BackpackSenderServer) GetBusinessRiskControlConf(ctx context.Context, in *pb.GetBusinessRiskControlConfReq) (out *pb.GetBusinessRiskControlConfResp, err error) {
	log.DebugWithCtx(ctx, "GetBusinessRiskControlConf in:%v", in)

	out = &pb.GetBusinessRiskControlConfResp{}

	if in.GetBusinessId() > 0 {
		out.BusinessRiskConfList, err = s.mgr.GetBusinessRiskControlConf(ctx, in.GetBusinessId())
		if nil != err {
			log.ErrorWithCtx(ctx, "GetBusinessRiskControlConf err:%v", err)
		}
	} else {
		out.Business_2RiskConfList = make(map[uint32]*pb.BusinessRiskControlConfList)
		for _, bid := range in.GetBusinessIdList() {
			cfg, err := s.mgr.GetBusinessRiskControlConf(ctx, bid)
			if nil != err {
				continue
			}
			out.Business_2RiskConfList[bid] = &pb.BusinessRiskControlConfList{
				BusinessRiskConfList: cfg,
			}
		}
	}

	log.DebugWithCtx(ctx, "GetBusinessRiskControlConf out:%v", out)
	return out, nil
}

func (s *BackpackSenderServer) GetBusinessRiskControlConfByBgId(ctx context.Context, in *pb.GetBusinessRiskControlConfByBgIdReq) (out *pb.GetBusinessRiskControlConfByBgIdResp, err error) {
	out = &pb.GetBusinessRiskControlConfByBgIdResp{}
	riskList, err := s.mgr.GetRiskControlConfByBgId(ctx, in.BgId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBusinessRiskControlConfByBgId failed in:%v err:%v", in, err)
		return out, err
	}
	out.BusinessRiskConfList = riskList
	log.InfoWithCtx(ctx, "GetBusinessRiskControlConfByBgId ok in:%v, out:%v", in, out)
	return out, nil
}

func (s *BackpackSenderServer) ModifyBusinessRiskWarningPercent(ctx context.Context, in *pb.ModifyBusinessRiskWarningPercentReq) (out *pb.ModifyBusinessRiskWarningPercentResp, err error) {
	log.InfoWithCtx(ctx, "ModifyBusinessRiskWarningPercent in:%v", in)

	out = &pb.ModifyBusinessRiskWarningPercentResp{}

	err = s.mgr.ModRiskWarningPrecent(ctx, in.BusinessId, in.WarningPercent)
	if err != nil {
		log.ErrorWithCtx(ctx, "ModifyBusinessRiskWarningPercent failed in:%v err:%v", in, err)
		return out, err
	}
	log.InfoWithCtx(ctx, "ModifyBusinessRiskWarningPercent in:%v out:%v err:%v", in, out, err)
	return out, nil
}
