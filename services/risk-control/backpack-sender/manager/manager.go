package manager

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"golang.52tt.com/clients/backpack-base"
	"golang.52tt.com/clients/userpresent"
	"golang.52tt.com/pkg/deal_token"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	basePb "golang.52tt.com/protocol/services/backpack-base"
	"golang.52tt.com/services/risk-control/backpack-sender/event"
	"golang.52tt.com/services/risk-control/backpack-sender/utils"
	"google.golang.org/grpc/metadata"
	"time"

	//"crypto/aes"
	"github.com/go-redis/redis"
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/backpacksender"
	"golang.52tt.com/services/risk-control/backpack-sender/cache"
	"golang.52tt.com/services/risk-control/backpack-sender/conf"
	"golang.52tt.com/services/risk-control/backpack-sender/mysql"
)

const (
	TryCnt             = 3
	RedisLockKey       = "rick-control-backpack-sender-producer"
	RedisTaskQueueKey  = "rick-control-backpack-sender-task-queue"
	RedisTaskZRangeKey = "rick-control-backpack-sender-task-zrange"
)

var M *Manager

var backpackBaseCli backpack_base.IClient
var userpresentCli *userPresent.Client

type Manager struct {
	sc              *conf.ServiceConfigT
	cacheClient     *cache.BackpackSenderCache
	mysqlStore      *mysql.Store
	kfkProd         *event.KafkaProduce
	presentEventSub *event.PresentSub
}

func NewManager(ctx context.Context, sc *conf.ServiceConfigT) (*Manager, error) {

	redisClient := redis.NewClient(&redis.Options{
		Network:            sc.GetRedisConfig().Protocol,
		Addr:               sc.GetRedisConfig().Addr(),
		PoolSize:           sc.GetRedisConfig().PoolSize,
		IdleCheckFrequency: sc.GetRedisConfig().IdleCheckFrequency(),
		DB:                 sc.GetRedisConfig().DB,
	})

	log.DebugWithCtx(ctx, "Initialized redis connection pool to %s://%s/%d", sc.GetRedisConfig().Protocol, sc.GetRedisConfig().Addr(), sc.GetRedisConfig().DB)

	cacheClient := cache.NewBackpackSenderCache(redisClient)

	mysqlDb, err := sqlx.Connect("mysql", sc.GetMysqlConnectionString())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to connect mysql %v", err)
		return nil, err
	}

	mysqlDb.SetMaxOpenConns(128)
	mysqlDb.SetMaxIdleConns(128)
	mysqlDb.SetConnMaxLifetime(time.Minute * 5)

	backpackBaseCli = backpack_base.NewIClient()
	userpresentCli = userPresent.NewClient()

	mysqlStore := mysql.NewMysql(mysqlDb)

	err = mysqlStore.CreateTables(ctx)
	if nil != err {
		log.ErrorWithCtx(ctx, "Failed to create mysql table %v", err)
		return nil, err
	}

	kfkProd, err := event.NewKafkaProduce(ctx, sc.GetKafkaConfig().BrokerList(), sc.GetKafkaConfig().ClientID, sc.GetKafkaConfig().Topics)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewKafkaProduce Failed to %v", err)
		return nil, err
	}
	M = &Manager{
		sc:          sc,
		cacheClient: cacheClient,
		mysqlStore:  mysqlStore,
		kfkProd:     kfkProd,
	}

	presentEventSub, err := event.NewPresentSub(ctx, sc.PresentKfkConfig, M)
	if err != nil {
		log.ErrorWithCtx(ctx, "event.NewPresentSub Failed err :%v", err)
		return nil, err
	}
	M.presentEventSub = presentEventSub

	log.InfoWithCtx(ctx, "NewManager ok")
	return M, nil
}

// 生产
func (m *Manager) Producer() {
	lk := m.cacheClient.Lock(RedisLockKey, time.Minute)
	if !lk {
		return
	}
	ctx := context.Background()
	endTs := time.Now()
	beginTs := time.Unix(endTs.Unix()-conf.OrderExpireSec, 0)
	orders, err := m.mysqlStore.GetSendBackpackOrderByTimeRange(beginTs, endTs)
	if nil != err {
		log.ErrorWithCtx(ctx, "%v ProducerGetSendBackpackOrderByTimeRange err:%v", utils.SvrName, err)
		return
	}

	log.InfoWithCtx(ctx, "%v ProducerGetSendBackpackOrderByTimeRange sz:%v", utils.SvrName, len(orders))

	for _, order := range orders {
		pbOrder := mysql.TransfromOrder(order)
		err = m.cacheClient.PushQ(RedisTaskQueueKey, pbOrder)
		if nil != err {
			log.ErrorWithCtx(ctx, "%v Producer PushQ pbOrder:%v err:%v", utils.SvrName, pbOrder, err)
		}
	}
}

// 消费
func (m *Manager) Consumer() {

	ctx := context.Background()
	orderInfo, err := m.cacheClient.PopQ(RedisTaskQueueKey)
	if nil != err {
		if redis.Nil == err {
			time.Sleep(time.Millisecond * 100)
		} else {
			log.ErrorWithCtx(ctx, "%v Consumer PopQ err:%v", utils.SvrName, err)
		}
		return
	}

	//isTestPackID := conf.IsTestPackID(int32(orderInfo.BackpackId))

	sendSucess := false
	if len(orderInfo.TrafficMark) > 0 { //传递流量标记
		ctx = metadata.AppendToOutgoingContext(ctx, kTrafficMark, orderInfo.TrafficMark)
	}

	//取包裹价值
	singleBackpackTbeanVal, err := getPackValue(ctx, orderInfo.BackpackId)
	if nil != err {
		log.ErrorWithCtx(ctx, "Consumer GetBackpackConfTbeanValue orderInfo:%v err:%v", orderInfo, err)
		return
	}
	businessCfg, err := m.getBusinessCfg(ctx, orderInfo.BusinessId)
	if nil != err {
		log.ErrorWithCtx(ctx, "Consumer getBusinessCfg orderInfo:%v err:%v", orderInfo, err)
		return
	}

	totalPrice := singleBackpackTbeanVal * orderInfo.BackpackCnt

	//if !isTestPackID {
	var sErr protocol.ServerError
	giveReq := &basePb.GiveUserPackageReq{
		Uid:            orderInfo.ReceiveUid,
		BgId:           orderInfo.BackpackId,
		Num:            orderInfo.BackpackCnt,
		Source:         orderInfo.SourceId,
		OrderId:        orderInfo.OrderId,
		OutsideTime:    uint32(orderInfo.OutsideTime),
		ExpireDuration: orderInfo.ExpireDuration,
		SourceAppId:    orderInfo.SourceAppId,
		TotalPrice:     totalPrice,
		BusinessType:   businessCfg.BusinessType,
	}
	giveReq.Sign = m.GiveSign(giveReq)
	for i := 0; i < TryCnt; i++ {
		_, sErr = backpackBaseCli.GiveUserPackage(ctx, giveReq)
		if nil != sErr {
			log.ErrorWithCtx(ctx, "%v Consumer GiveUserPackage orderInfo:%v err:%v", utils.SvrName, orderInfo, sErr)
			if sErr.Code() == status.ErrBackpackOrderExist {
				sendSucess = true
				break
			}
			if i < TryCnt-1 {
				time.Sleep(time.Second)
			}
		} else {
			sendSucess = true
			break
		}
	}
	//}

	//发送成功，更新DB中的订单状态
	if sendSucess { //|| isTestPackID
		for i := 0; i < TryCnt; i++ {
			err = m.mysqlStore.UpdateOrderStatus(orderInfo, int(pb.OrderStatus_SEND))
			if nil != err {
				log.ErrorWithCtx(ctx, "%v Consumer UpdateOrderStatus orderInfo:%v err:%v", utils.SvrName, orderInfo, err)
				if i < TryCnt-1 {
					time.Sleep(time.Second)
				}
			} else {
				break
			}
		}

		//发送获得包裹kfk
		if nil == err {
			m.SendReceiveEvent(ctx, orderInfo)
		}

		//从统计失败列表删除
		err = m.cacheClient.ZRem(RedisTaskZRangeKey, orderInfo.OrderId)
		if nil != err {
			log.ErrorWithCtx(ctx, "Consumer ZRem orderId:%v err:%v", orderInfo.OrderId, err)
		}
	}

	log.DebugWithCtx(ctx, "Consumer orderInfo:%+v totalPrice:%v err:%v", orderInfo, totalPrice, err)
}

func (m *Manager) SendReceiveEvent(ctx context.Context, orderInfo *pb.SendBackpackOrderInfo) {
	itemList, err := getBackPackItemList(ctx, orderInfo.GetBackpackId())
	if nil == err {
		items := make([]*pb.PackageItemCfg, 0)
		for _, item := range itemList {
			items = append(items, &pb.PackageItemCfg{
				ItemType: item.ItemType,
				ItemId:   item.SourceId,
				ItemCnt:  item.ItemCount,
			})
		}
		m.kfkProd.ProduceNewBackpackEvent(ctx, &pb.BackpackReceiveEvent{
			OrderId:    orderInfo.GetOrderId(),
			Uid:        orderInfo.GetReceiveUid(),
			BgId:       orderInfo.GetBackpackId(),
			Cnt:        orderInfo.GetBackpackCnt(),
			BusinessId: orderInfo.GetBusinessId(),
			CreateTime: orderInfo.GetServerTime(),
			ItemList:   items,
		})
	} else {
		log.ErrorWithCtx(ctx, "SendReceiveEvent GetBackPackItemList orderInfo:%v err:%v", orderInfo, err)
	}
}

// TableCreater 建表，提前insert
func (m *Manager) TableCreater() {
	now := time.Now()
	if now.Hour() != 1 {
		return
	}
	ctx := context.Background()
	log.InfoWithCtx(ctx, "TableCreater start ...")
	_ = m.mysqlStore.CreateTables(ctx)

	log.InfoWithCtx(ctx, "TableCreater end ...")
}

// MonitorServer 监控风控服务处理订单速度
func (m *Manager) MonitorServer() {
	nowTs := time.Now()
	monitorServerKey := fmt.Sprintf("Monitor-Server-%v", nowTs.Format("2006-01-02-15-04"))
	newNum, _ := m.cacheClient.IncrBy(monitorServerKey, 1, time.Minute*2)
	if newNum != 1 {
		return
	}
	ctx := context.Background()
	sz, err := m.cacheClient.LenQ(RedisTaskQueueKey)
	if nil != err {
		log.ErrorWithCtx(ctx, "MonitorServer LenQ err:%v", err)
	}

	//未处理完的订单数量
	if nil == err && sz >= int64(conf.MonitorCnt) {
		intervalKey := "risk_control_traffic_order"
		reportNum, _ := m.cacheClient.IncrBy(intervalKey, 1, time.Minute*2)
		if reportNum == 1 {
			log.InfoWithCtx(ctx, "%v Monitor order traffic count:%v", utils.SvrName, sz)
			msg := fmt.Sprintf("风控服务订单阻塞 %v分钟内未处理订单数:%v", conf.SvrCheckMin, sz)
			go utils.SendReportMsgToFeishu(msg, conf.SvrReportUrl)
		}
	}

	log.DebugWithCtx(ctx, "MonitorServer 未处理订单数:%v\n", sz)

	//超过两个小时未发放成功的订单数量
	beginTs := time.Unix(nowTs.Unix()-5*3600, 0)
	endTs := time.Unix(nowTs.Unix()-conf.OrderExpireSec, 0)
	count, err := m.cacheClient.ZLEXCOUNT(RedisTaskZRangeKey, fmt.Sprintf("%v", beginTs.Unix()), fmt.Sprintf("%v", endTs.Unix()))
	if nil != err {
		return
	}

	if count > 0 {
		intervalKey := "risk_control_fail_order"
		reportNum, _ := m.cacheClient.IncrBy(intervalKey, 1, time.Minute*15)
		if reportNum == 1 {
			log.InfoWithCtx(ctx, "%v Monitor order traffic count:%v", utils.SvrName, count)
			msg := fmt.Sprintf("风控服务订单失败告警，累计发放失败订单数:%v，请及时处理。", count)
			go utils.SendReportMsgToFeishu(msg, conf.SvrReportUrl)
		}
	}

	log.DebugWithCtx(ctx, "MonitorServer 发放失败订单数:%v\n", count)

}

func (m *Manager) getConfigList() (map[uint32]*mysql.BusinessConf, []*mysql.RiskControlConf, error) {
	ctx := context.Background()

	//业务列表
	businessConfList, err := m.mysqlStore.GetBusiness(ctx, 0)
	if nil != err {
		log.ErrorWithCtx(ctx, "%v MonitorBalance GetBusinessRiskControlConf err:%v", utils.SvrName, err)
		return nil, nil, err
	}
	mapBusiness := make(map[uint32]*mysql.BusinessConf)
	for _, bc := range businessConfList {
		mapBusiness[bc.BusinessID] = bc
	}

	//风控配置列表
	riskConfList, err := m.mysqlStore.GetBusinessRiskControlConf(ctx, 0)
	if nil != err {
		log.ErrorWithCtx(ctx, "%v MonitorBalance GetBusinessRiskControlConf err:%v", utils.SvrName, err)
		return nil, nil, err
	}

	return mapBusiness, riskConfList, nil
}

func (m *Manager) checkBalance(riskCfg *mysql.RiskControlConf, mapBusiness map[uint32]*mysql.BusinessConf) bool {
	nowTs := time.Now()
	hourWarningRatio := conf.GetHourWarningRatio()

	businessConf, ok := mapBusiness[riskCfg.BusinessID]
	if !ok {
		return false
	}
	if businessConf.WarningPrecent > 0 {
		hourWarningRatio = float64(businessConf.WarningPrecent) / 100
	}

	isLock := m.cacheClient.Lock(fmt.Sprintf("b_h_c_v2_%v_%v", businessConf.BusinessID, riskCfg.BackpackID), time.Minute*15)
	if !isLock {
		return false
	}

	ret := false
	limitResult := m.cacheClient.GetBusinessVal(0, businessConf.BusinessID, riskCfg.BackpackID, nowTs)
	oprUser := fmt.Sprintf("[%s]", riskCfg.OperUser)
	alertEmail := riskCfg.OperEmail
	alertUser := riskCfg.OperUser
	if riskCfg.OperUser != businessConf.OperUser {
		oprUser = fmt.Sprintf("[%s,%s]", riskCfg.OperUser, businessConf.OperUser)
	}
	//检查小时时间
	hourCnt := limitResult[pb.LimitTimeType_HOUR_CNT]
	res := utils.CheckRatioLimit(hourCnt, riskCfg.HourCntLimit, hourWarningRatio)
	if res {
		waringMsg := fmt.Sprintf("%v(%v) 业务单位小时(%v)发放包裹ID为:%v 的数量额度已经超过 %v%%,请调整额度 %s",
			businessConf.Name, businessConf.BusinessID, utils.GetHourStr(nowTs), riskCfg.BackpackID, int(hourWarningRatio*100), oprUser)
		go utils.SendReportMsgToFeishu(waringMsg, conf.DataReportUrl)
		go m.SendAlarm(waringMsg, riskCfg.BusinessID, riskCfg.BackpackID, alertEmail, alertUser, nowTs, true, false)
		ret = true
	}

	hourVal := limitResult[pb.LimitTimeType_HOUR_VAL]
	res = utils.CheckRatioLimit(hourVal, riskCfg.HourTbeanValueLimit, hourWarningRatio)
	if res {
		waringMsg := fmt.Sprintf("%v(%v) 业务单位小时(%v)发放包裹T豆价值额度已经超过 %v%%,请调整额度 %s",
			businessConf.Name, businessConf.BusinessID, utils.GetDayStr(nowTs), int(hourWarningRatio*100), oprUser)
		go utils.SendReportMsgToFeishu(waringMsg, conf.DataReportUrl)
		go m.SendAlarm(waringMsg, riskCfg.BusinessID, riskCfg.BackpackID, alertEmail, alertUser, nowTs, true, false)
		ret = true
	}

	//检查天数据
	dayCnt := limitResult[pb.LimitTimeType_DAY_CNT]
	res = utils.CheckRatioLimit(dayCnt, riskCfg.DayCntLimit, hourWarningRatio)
	if res {
		waringMsg := fmt.Sprintf("%v(%v) 业务天(%v)发放包裹ID为:%v 的数量额度已经超过 %v%%,请调整额度 %s", businessConf.Name, businessConf.BusinessID,
			utils.GetDayStr(nowTs), riskCfg.BackpackID, int(hourWarningRatio*100), oprUser)
		go utils.SendReportMsgToFeishu(waringMsg, conf.DataReportUrl)
		go m.SendAlarm(waringMsg, riskCfg.BusinessID, riskCfg.BackpackID, alertEmail, alertUser, nowTs, false, false)
		ret = true
	}

	dayVal := limitResult[pb.LimitTimeType_DAY_VAL]
	res = utils.CheckRatioLimit(dayVal, riskCfg.DayTbeanValueLimit, hourWarningRatio)
	if res {
		waringMsg := fmt.Sprintf("%v(%v) 业务天(%v)发放的包裹T豆价值额度已经超过 %v%%,请调整额度 %s", businessConf.Name, businessConf.BusinessID,
			utils.GetDayStr(nowTs), int(hourWarningRatio*100), oprUser)
		go utils.SendReportMsgToFeishu(waringMsg, conf.DataReportUrl)
		go m.SendAlarm(waringMsg, riskCfg.BusinessID, riskCfg.BackpackID, alertEmail, alertUser, nowTs, false, false)
		ret = true
	}
	return ret
}

// checkUserBalance 用户维度的检查
func (m *Manager) checkUserBalance(uid uint32, riskCfgs []*mysql.RiskControlConf, businessConf *mysql.BusinessConf) {
	for _, riskCfg := range riskCfgs {
		if riskCfg.BackpackID != 0 {
			continue
		}
		if m.checkUserBalanceOne(uid, riskCfg, businessConf) {
			return
		}
	}
}
func (m *Manager) checkUserBalanceOne(uid uint32, riskCfg *mysql.RiskControlConf, businessConf *mysql.BusinessConf) bool {
	nowTs := time.Now()
	hourWarningRatio := conf.GetHourWarningRatio()

	if businessConf.WarningPrecent > 0 {
		hourWarningRatio = float64(businessConf.WarningPrecent) / 100
	}
	userGlobalLimitSec := conf.GetUserRickLimitSec()
	if userGlobalLimitSec > 0 {
		isLock := m.cacheClient.Lock(fmt.Sprintf("b_h_c_v3_%v_%v", businessConf.BusinessID, riskCfg.BackpackID), time.Duration(userGlobalLimitSec)*time.Second)
		if !isLock {
			return false
		}
	}
	isUserLock := m.cacheClient.Lock(fmt.Sprintf("b_h_c_v2_%v_%v_%v", uid, businessConf.BusinessID, riskCfg.BackpackID), time.Minute*3)
	if !isUserLock {
		return false
	}

	ret := false
	limitResult := m.cacheClient.GetBusinessVal(uid, businessConf.BusinessID, riskCfg.BackpackID, nowTs)
	oprUser := fmt.Sprintf("[%s]", riskCfg.OperUser)
	alertEmail := riskCfg.OperEmail
	alertUser := riskCfg.OperUser
	if riskCfg.OperUser != businessConf.OperUser {
		oprUser = fmt.Sprintf("[%s,%s]", riskCfg.OperUser, businessConf.OperUser)
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
	defer cancel()
	// 获取投入
	costPrice, err := m.cacheClient.GetUserCostPriceToday(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserCostPriceToday uid:%v, err:%v", uid, err)
	}
	gainPrice, err := m.cacheClient.GetUserGainPriceToday(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserGainPriceToday uid:%v, err:%v", uid, err)
	}

	gainCostPercent := float64(0)
	if costPrice != 0 {
		gainCostPercent = float64(gainPrice) / float64(costPrice) * 100
	}

	costGainDesc := fmt.Sprintf("\nuid:%d\n当天消费金额：%0.2f元\n当天获得包裹金额：%0.2f元\n当天返奖比：%0.2f%%", uid, float64(costPrice)/100, float64(gainPrice)/100, gainCostPercent)

	//检查小时时间
	hourCnt := limitResult[pb.LimitTimeType_HOUR_CNT]
	res := utils.CheckRatioLimit(hourCnt, riskCfg.UserHourCntLimit, hourWarningRatio)
	if res {
		waringMsg := fmt.Sprintf("%v(%v) 业务-用户维度(uid:%d)-单位小时(%v)发放包裹的数量额度已经超过 %v%%,请调整额度 %s",
			businessConf.Name, businessConf.BusinessID, uid, utils.GetHourStr(nowTs), int(hourWarningRatio*100), oprUser)
		waringMsg += costGainDesc
		go utils.SendReportMsgToFeishu(waringMsg, conf.DataReportUrl)
		go m.SendAlarm(waringMsg, riskCfg.BusinessID, riskCfg.BackpackID, alertEmail, alertUser, nowTs, true, false)
		ret = true
	}

	hourVal := limitResult[pb.LimitTimeType_HOUR_VAL]
	res = utils.CheckRatioLimit(hourVal, riskCfg.UserHourTbeanValueLimit, hourWarningRatio)
	if res {
		waringMsg := fmt.Sprintf("%v(%v) 业务-用户维度(uid:%d)-单位小时(%v)发放包裹T豆价值额度已经超过 %v%%,请调整额度 %s",
			businessConf.Name, businessConf.BusinessID, uid, utils.GetDayStr(nowTs), int(hourWarningRatio*100), oprUser)
		waringMsg += costGainDesc
		go utils.SendReportMsgToFeishu(waringMsg, conf.DataReportUrl)
		go m.SendAlarm(waringMsg, riskCfg.BusinessID, riskCfg.BackpackID, alertEmail, alertUser, nowTs, true, false)
		ret = true
	}

	//检查天数据
	dayCnt := limitResult[pb.LimitTimeType_DAY_CNT]
	res = utils.CheckRatioLimit(dayCnt, riskCfg.UserDayCntLimit, hourWarningRatio)
	if res {
		waringMsg := fmt.Sprintf("%v(%v) 业务-用户维度(uid:%d)-天(%v)发放包裹的数量额度已经超过 %v%%,请调整额度 %s", businessConf.Name, businessConf.BusinessID, uid,
			utils.GetDayStr(nowTs), int(hourWarningRatio*100), oprUser)
		waringMsg += costGainDesc
		go utils.SendReportMsgToFeishu(waringMsg, conf.DataReportUrl)
		go m.SendAlarm(waringMsg, riskCfg.BusinessID, riskCfg.BackpackID, alertEmail, alertUser, nowTs, false, false)
		ret = true
	}

	dayVal := limitResult[pb.LimitTimeType_DAY_VAL]
	res = utils.CheckRatioLimit(dayVal, riskCfg.UserDayTbeanValueLimit, hourWarningRatio)
	if res {
		waringMsg := fmt.Sprintf("%v(%v) 业务-用户维度(uid:%d)-天(%v)发放的包裹T豆价值额度已经超过 %v%%,请调整额度 %s", businessConf.Name, businessConf.BusinessID, uid,
			utils.GetDayStr(nowTs), int(hourWarningRatio*100), oprUser)
		waringMsg += costGainDesc
		go utils.SendReportMsgToFeishu(waringMsg, conf.DataReportUrl)
		go m.SendAlarm(waringMsg, riskCfg.BusinessID, riskCfg.BackpackID, alertEmail, alertUser, nowTs, false, false)
		ret = true
	}
	log.DebugWithCtx(ctx, "checkUserBalanceOne uid:%v, limitResult:%v, businessId:%v", uid, limitResult, businessConf.BusinessID)
	return ret
}

// 检测业务余额
func (m *Manager) MonitorBalance() {

	monitorBalanceKey := "Risk-Backpack-Sender-Monitor-Balance"
	isLock := m.cacheClient.Lock(monitorBalanceKey, time.Minute*5)
	if !isLock {
		return
	}

	mapBusiness, riskConfList, err := m.getConfigList()
	if nil != err {
		return
	}

	bussinessHasWarning := map[uint32]struct{}{} //一次同一个风控只告警一次
	for _, riskCfg := range riskConfList {
		if _, ok := bussinessHasWarning[riskCfg.BusinessID]; ok {
			continue
		}
		warning := m.checkBalance(riskCfg, mapBusiness)
		if warning {
			bussinessHasWarning[riskCfg.BusinessID] = struct{}{}
		}
	}

}

// SendSecretKeyEmail 发送密钥邮件
func (m *Manager) SendSecretKeyEmail(ctx context.Context, bussinessId uint32) (string, error) {
	bsConfigs, err := m.mysqlStore.GetBusiness(ctx, bussinessId)
	if nil != err {
		log.ErrorWithCtx(ctx, "SendSecretKeyEmail GetBusiness err:%v", err)
		return "", err
	}
	if len(bsConfigs) == 0 {
		log.ErrorWithCtx(ctx, "SendSecretKeyEmail GetBusiness not cfg bussid:%v", bussinessId)
		return "", errors.New("business id not cfg")

	}
	bsConfig := bsConfigs[0]
	subject := fmt.Sprintf("包裹风控 业务:%v 业务ID:%v 的密钥", bsConfig.Name, bsConfig.BusinessID)
	content := fmt.Sprintf("包裹风控 业务:%v 业务ID:%v 的密钥:%v", bsConfig.Name, bsConfig.BusinessID, bsConfig.SecretKey)
	addrs := make([]string, 0)
	if len(bsConfig.OperEmail) > 0 {
		addrs = append(addrs, bsConfig.OperEmail)
	} else {
		addrs = append(addrs, "<EMAIL>")
	}
	go utils.SendEmail(content, subject, addrs)
	return bsConfig.SecretKey, nil
}

func (m *Manager) AddBusiness(ctx context.Context, bsConf *pb.BusinessConf) (uint32, error) {
	secretKey := utils.GenAESKey()
	businessId, err := m.mysqlStore.AddBusiness(ctx, string(secretKey), bsConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddBusiness bs:%+v, err:%v", bsConf, err)
		return businessId, err
	}
	_, _ = m.SendSecretKeyEmail(ctx, businessId)
	return businessId, err
}

func (m *Manager) GetBusiness(ctx context.Context, businessID uint32) ([]*pb.BusinessConf, error) {
	bs, err := m.mysqlStore.GetBusiness(ctx, businessID)
	bList := make([]*pb.BusinessConf, 0)
	if nil == err {
		for _, b := range bs {
			wp := b.WarningPrecent
			if wp == 0 {
				wp = uint32(conf.GetHourWarningRatio() * 100)
			}
			bList = append(bList, &pb.BusinessConf{
				Name:        b.Name,
				Desc:        b.Desc,
				CallbackUrl: b.CallbackUrl,
				SourceId:    b.SourceID,
				OperaUser:   b.OperUser,
				BeginTime:   utils.GetTimeStr(time.Unix(b.BeginTime, 0)),
				EndTime:     utils.GetTimeStr(time.Unix(b.EndTime, 0)),
				BusinessId:  b.BusinessID,
				//SecretKey:      b.SecretKey,
				WarningPrecent: wp,
				OperEmail:      b.OperEmail,
				BusinessType:   b.BusinessType,
			})
		}
	}
	return bList, err
}

func (m *Manager) GetBusinessRiskControlConf(ctx context.Context, businessID uint32) ([]*pb.BusinessRiskControlConf, error) {
	//rList, err := m.mysqlStore.GetBusinessRiskControlConf(ctx, businessID)
	rList, err := m.GetControlConfList(ctx, businessID)
	riskConfList := make([]*pb.BusinessRiskControlConf, 0)
	if nil != err {
		log.ErrorWithCtx(ctx, "%v GetBusinessRiskControlConf err:%v", utils.SvrName, err)
		return riskConfList, err
	}

	nowTs := time.Now()
	//业务当前已经发放数据
	hourStr := utils.GetHourStr(nowTs)
	dayStr := utils.GetDayStr(nowTs)

	log.DebugWithCtx(ctx, "GetBusinessRiskControlConf id:%d hourStr:%v dayStr:%v", businessID, hourStr, dayStr)

	for _, r := range rList {
		pbRisk := m.convertRiskControlConf2Pb(ctx, r, nowTs)
		riskConfList = append(riskConfList, pbRisk)
	}

	return riskConfList, nil
}

// GetRiskControlConfByBgId 根据包裹ID获取风控项
func (m *Manager) GetRiskControlConfByBgId(ctx context.Context, bgId uint32) ([]*pb.BusinessRiskControlConf, error) {
	riskConfs, err := m.mysqlStore.GetBusinessRiskControlConf(ctx, 0)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetRiskControlConfByBgId bgId:%d err:%v", bgId, err)
		return nil, err
	}
	var riskConfList []*pb.BusinessRiskControlConf
	nowT := time.Now()
	for _, r := range riskConfs {
		if r.BackpackID != bgId {
			continue
		}
		pbRisk := m.convertRiskControlConf2Pb(ctx, r, nowT)
		riskConfList = append(riskConfList, pbRisk)
	}
	return riskConfList, nil
}

func (m *Manager) ModRiskWarningPrecent(ctx context.Context, businessID uint32, warningPrecent uint32) error {
	err := m.mysqlStore.ModRiskWarningPrecent(ctx, businessID, warningPrecent)
	return err
}

func (m *Manager) convertRiskControlConf2Pb(ctx context.Context, r *mysql.RiskControlConf, nowT time.Time) *pb.BusinessRiskControlConf {
	results := m.cacheClient.GetBusinessVal(0, r.BusinessID, r.BackpackID, nowT)
	return &pb.BusinessRiskControlConf{
		BusinessId:              r.BusinessID,
		BgId:                    r.BackpackID,
		HourCntLimit:            r.HourCntLimit,
		DayCntLimit:             r.DayCntLimit,
		SingleCntLimit:          r.SingleCntLimit,
		SingleValueLimit:        r.SingleValueLimit,
		HourTbeanValueLimit:     r.HourTbeanValueLimit,
		DayTbeanValueLimit:      r.DayTbeanValueLimit,
		OperaUser:               r.OperUser,
		OperEmail:               r.OperEmail,
		Id:                      r.Id,
		UsedHourTbeanValue:      results[pb.LimitTimeType_HOUR_VAL],
		UsedDayTbeanValue:       results[pb.LimitTimeType_DAY_VAL],
		UsedDayCnt:              results[pb.LimitTimeType_DAY_CNT],
		UsedHourCnt:             results[pb.LimitTimeType_HOUR_CNT],
		RestHourTbeanValue:      int64(r.HourTbeanValueLimit) - int64(results[pb.LimitTimeType_HOUR_VAL]),
		RestDayTbeanValue:       int64(r.DayTbeanValueLimit) - int64(results[pb.LimitTimeType_DAY_VAL]),
		RestHourCnt:             int64(r.HourCntLimit) - int64(results[pb.LimitTimeType_HOUR_CNT]),
		RestDayCnt:              int64(r.DayCntLimit) - int64(results[pb.LimitTimeType_DAY_CNT]),
		UserHourCntLimit:        r.UserHourCntLimit,
		UserDayCntLimit:         r.UserDayCntLimit,
		UserHourTbeanValueLimit: r.UserHourTbeanValueLimit,
		UserDayTbeanValueLimit:  r.UserDayTbeanValueLimit,
		IsCurAllBg:              r.IsCurAllBg == 1,
	}
}

func (m *Manager) AddBusinessRiskControlConf(ctx context.Context, riskConf *pb.BusinessRiskControlConf) error {
	err := m.mysqlStore.AddBusinessRiskControlConf(ctx, nil, riskConf)
	if err == nil {
		_, err = m.updateControlConfList(ctx, riskConf.BusinessId)
	}
	return err
}

func (m *Manager) BatchAddOrModBusinessRiskControlConf(ctx context.Context, addRiskList []*pb.BusinessRiskControlConf, updateRiskList []*pb.BusinessRiskControlConf) error {
	if len(addRiskList) == 0 && len(updateRiskList) == 0 {
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackConfigNotFound, "参数错误")
	}
	businessId := uint32(0)
	for _, riskConf := range addRiskList {
		if businessId != 0 && riskConf.BusinessId != businessId {
			return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackConfigNotFound, "参数错误-不同的业务ID")
		}
		businessId = riskConf.BusinessId
	}
	for _, riskConf := range updateRiskList {
		if riskConf.Id == 0 {
			log.ErrorWithCtx(ctx, "BatchAddOrModBusinessRiskControlConf riskConf.Id is 0")
			return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackConfigNotFound, "风控ID错误")
		}
		if businessId != 0 && riskConf.BusinessId != businessId {
			return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackConfigNotFound, "参数错误-不同的业务ID")
		}
		businessId = riskConf.BusinessId
	}
	_, _ = m.updateControlConfList(ctx, businessId) //更新缓存
	rcMap, err := m.GetControlConfMap(ctx, businessId)
	if nil != err {
		log.ErrorWithCtx(ctx, "BatchAddOrModBusinessRiskControlConf GetControlConfMap business:%d,  err:%v", businessId, err)
		return err
	}

	tx, xerr := m.mysqlStore.DB.Beginx()
	if xerr != nil {
		log.ErrorWithCtx(ctx, "BatchAddOrModBusinessRiskControlConf Beginx err:%v", xerr)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackSysFail, xerr.Error())
	}
	defer func() {
		if r := recover(); nil != r {
			log.ErrorWithCtx(ctx, "BatchAddOrModBusinessRiskControlConf recover r:%v", r)
			_ = tx.Rollback()
		}
	}()

	for _, riskConf := range addRiskList {
		err := m.mysqlStore.AddBusinessRiskControlConf(ctx, tx, riskConf)
		if err != nil {
			xerr = tx.Rollback()
			log.ErrorWithCtx(ctx, "BatchAddOrModBusinessRiskControlConf Add err:%v, xerr:%v", err, xerr)
			return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackSysFail, err.Error())
		}
	}

	for _, riskConf := range updateRiskList {
		rc, ok := rcMap[riskConf.Id]
		if !ok {
			xerr = tx.Rollback()
			log.ErrorWithCtx(ctx, "BatchAddOrModBusinessRiskControlConf not find riskControlConf id:%v", riskConf.Id)
			return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackConfigNotFound, "风控配置ID错误")
		}
		if riskConf.BgId == 0 && rc.BackpackID != 0 || riskConf.BgId != 0 && rc.BackpackID == 0 {
			xerr = tx.Rollback()
			log.ErrorWithCtx(ctx, "ModBusinessRiskControlConf  不允许修改旧所有包裹为单个包裹 id:%v", riskConf.Id)
			return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackConfigNotFound, "不允许修改所有包裹为单个包裹")
		}
		err := m.mysqlStore.ModBusinessRiskControlConf(ctx, tx, riskConf)
		if err != nil {
			xerr = tx.Rollback()
			log.ErrorWithCtx(ctx, "BatchAddOrModBusinessRiskControlConf Add err:%v, xerr:%v", err, xerr)
			return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackSysFail, err.Error())
		}

		// 修改了风控配置，检查是否需要恢复告警
		go m.CheckAndRecoverAlarm(businessId, riskConf.BgId)
	}

	err = tx.Commit()
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchAddOrModBusinessRiskControlConf Commit err:%v", err)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackSysFail, err.Error())
	}
	_, err = m.updateControlConfList(ctx, businessId)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchAddOrModBusinessRiskControlConf updateControlConfList err:%v", err)
	}

	return nil
}

func (m *Manager) ModBusinessRiskControlConf(ctx context.Context, riskConf *pb.BusinessRiskControlConf) error {
	_, _ = m.updateControlConfList(ctx, riskConf.BusinessId) //更新缓存
	rcMap, err := m.GetControlConfMap(ctx, riskConf.BusinessId)
	if nil != err {
		log.ErrorWithCtx(ctx, "ModBusinessRiskControlConf GetControlConfList err:%v", err)
		return err
	}

	rc, ok := rcMap[riskConf.Id]
	if !ok {
		log.ErrorWithCtx(ctx, "ModBusinessRiskControlConf not find riskControlConf id:%v", riskConf.Id)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackConfigNotFound, "风控配置ID错误")
	}
	if riskConf.BgId == 0 && rc.BackpackID != 0 || riskConf.BgId != 0 && rc.BackpackID == 0 {
		log.ErrorWithCtx(ctx, "ModBusinessRiskControlConf  不允许修改旧所有包裹为单个包裹 id:%v", riskConf.Id)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackConfigNotFound, "不允许修改所有包裹为单个包裹")
	}

	err = m.mysqlStore.ModBusinessRiskControlConf(ctx, nil, riskConf)
	if err == nil {
		_, err = m.updateControlConfList(ctx, riskConf.BusinessId)
		// 修改了风控配置，检查是否需要恢复告警
		go m.CheckAndRecoverAlarm(riskConf.BusinessId, riskConf.BgId)
	}
	return err
}

func (m *Manager) CheckBusinessPermission(ctx context.Context, orderInfo *pb.SendBackpackOrderInfo) (*mysql.BusinessConf, protocol.ServerError) {

	bc, err := m.getBusinessCfg(ctx, orderInfo.GetBusinessId())
	if nil != err {
		return nil, protocol.NewExactServerError(nil, status.ErrRiskControlBackpackBusinessNotFound, "找不到对应业务配置")
	}
	//业务可用状态检查
	if bc.IsValid == 0 {
		return nil, protocol.NewExactServerError(nil, status.ErrRiskControlBackpackBusinessNotFound, "业务不可用")
	}

	//业务有效时间检查
	ts := time.Unix(orderInfo.ServerTime, 0)
	if bc.BeginTime > ts.Unix() || ts.Unix() > bc.EndTime {
		return nil, protocol.NewExactServerError(nil, status.ErrRiskControlBackpackBusinessNotFound, "业务已经过期")
	}

	if len(orderInfo.Ciphertext) == 0 {
		log.ErrorWithCtx(ctx, "CheckBusinessPermission ciphertext sz:%v", len(orderInfo.Ciphertext))
		return nil, protocol.NewExactServerError(nil, status.ErrRiskControlBackpackAuthCheckFail, "密文长度错误")
	}

	if len(orderInfo.Ciphertext)%utils.SecretKeyLen != 0 {
		log.ErrorWithCtx(ctx, "CheckBusinessPermission ciphertext sz:%v", len(orderInfo.Ciphertext))
		return nil, protocol.NewExactServerError(nil, status.ErrRiskControlBackpackAuthCheckFail, "密文长度错误")
	}

	//密钥检查
	if !utils.CheckAuth(orderInfo.Ciphertext, []byte(bc.SecretKey), orderInfo.OrderId) {
		log.ErrorWithCtx(ctx, "CheckBusinessPermission CheckAuth orderInfo+%v bc:%v Ciphertext %v", orderInfo, bc, orderInfo.Ciphertext)
		return nil, protocol.NewExactServerError(nil, status.ErrRiskControlBackpackAuthCheckFail, "密钥检查错误")
	}

	return bc, nil
}

func (m *Manager) CheckOrderIdFormat(orderInfo *pb.SendBackpackOrderInfo) protocol.ServerError {
	orderPrefix := fmt.Sprintf("%v_", orderInfo.BusinessId)

	if len(orderPrefix) >= len(orderInfo.OrderId) {
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackOrderidInvalid, "订单长度错误")
	}

	if orderPrefix != orderInfo.OrderId[:len(orderPrefix)] {
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackOrderidInvalid, "订单格式错误，必须以businessID_开头")
	}
	return nil
}

// 单次送礼数量检查
func (m *Manager) CheckSigleValue(ctx context.Context, rc *mysql.RiskControlConf,
	orderInfo *pb.SendBackpackOrderInfo, tbeanValue uint32) protocol.ServerError {

	if rc.SingleValueLimit > 0 && rc.SingleValueLimit < tbeanValue {
		log.ErrorWithCtx(ctx, "CheckSingleRiskControl 单次T豆价值限制 tbeanValue:%v orderInfo:%v", tbeanValue, orderInfo)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackSignalTbeanLimit, "单次T豆价值限制")
	}

	if rc.SingleCntLimit > 0 && rc.SingleCntLimit < orderInfo.BackpackCnt {
		log.ErrorWithCtx(ctx, "CheckSingleRiskControl 单次包裹数量限制 BackpackCnt:%v SingleCntLimit:%v", orderInfo.BackpackCnt, rc.SingleCntLimit)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackSignalCountLimit, "单次包裹数量限制")
	}

	if rc.DayTbeanValueLimit > 0 && rc.DayTbeanValueLimit < uint64(tbeanValue) {
		log.ErrorWithCtx(ctx, "CheckSingleRiskControl T豆天余额不足 tbeanValue:%v orderInfo:%v", tbeanValue, orderInfo)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackTbeanLimit, "T豆天余额不足")
	}

	if rc.DayCntLimit > 0 && rc.DayCntLimit < uint64(orderInfo.BackpackCnt) {
		log.ErrorWithCtx(ctx, "CheckSingleRiskControl 包裹天发放数量限制 BackpackCnt:%v orderInfo:%v", orderInfo.BackpackCnt, orderInfo)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackCountLimit, "包裹天发放数量限制")
	}
	if rc.HourTbeanValueLimit > 0 && rc.HourTbeanValueLimit < uint64(tbeanValue) {
		log.ErrorWithCtx(ctx, "CheckSingleRiskControl T豆小时余额不足 tbeanValue:%v orderInfo:%v", tbeanValue, orderInfo)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackTbeanLimit, "T豆小时余额不足")
	}
	if rc.HourCntLimit > 0 && rc.HourCntLimit < uint64(orderInfo.BackpackCnt) {
		log.ErrorWithCtx(ctx, "CheckSingleRiskControl 包裹小时发放数量限制 BackpackCnt:%v orderInfo:%v", orderInfo.BackpackCnt, orderInfo)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackCountLimit, "包裹小时发放数量限制")
	}

	if rc.BackpackID != 0 {
		return nil
	}
	// 用户维度的检查
	if rc.UserDayTbeanValueLimit > 0 && rc.UserDayTbeanValueLimit < uint64(tbeanValue) {
		log.ErrorWithCtx(ctx, "CheckSingleRiskControl T豆天余额不足 tbeanValue:%v orderInfo:%v", tbeanValue, orderInfo)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackTbeanLimit, "T豆天余额不足")
	}

	if rc.UserDayCntLimit > 0 && rc.UserDayCntLimit < uint64(orderInfo.BackpackCnt) {
		log.ErrorWithCtx(ctx, "CheckSingleRiskControl 包裹天发放数量限制 BackpackCnt:%v orderInfo:%v", orderInfo.BackpackCnt, orderInfo)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackCountLimit, "包裹天发放数量限制")
	}
	if rc.UserHourTbeanValueLimit > 0 && rc.UserHourTbeanValueLimit < uint64(tbeanValue) {
		log.ErrorWithCtx(ctx, "CheckSingleRiskControl T豆小时余额不足 tbeanValue:%v orderInfo:%v", tbeanValue, orderInfo)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackTbeanLimit, "T豆小时余额不足")
	}
	if rc.UserHourCntLimit > 0 && rc.UserHourCntLimit < uint64(orderInfo.BackpackCnt) {
		log.ErrorWithCtx(ctx, "CheckSingleRiskControl 包裹小时发放数量限制 BackpackCnt:%v orderInfo:%v", orderInfo.BackpackCnt, orderInfo)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackCountLimit, "包裹小时发放数量限制")
	}
	return nil
}

func (m *Manager) CheckRiskControl(ctx context.Context, ts time.Time, orderInfo *pb.SendBackpackOrderInfo, tbeanValue uint32, rcList []*mysql.RiskControlConf, businessCfg *mysql.BusinessConf, pipe redis.Pipeliner) protocol.ServerError {

	//ctx := context.Background()
	//逐个检查多个风控配置
	var serr protocol.ServerError
	var allBgRc *mysql.RiskControlConf
	var singleBgRc *mysql.RiskControlConf
	for _, rc := range rcList {
		//backpackID == 0 业务整体的T豆价值和包裹数量限制。 ！= 0 具体的包裹ID数量限制
		if rc.BackpackID != 0 && (rc.BackpackID != orderInfo.BackpackId) {
			continue
		}
		if rc.BackpackID == 0 { //所有包裹
			allBgRc = rc
		} else {
			singleBgRc = rc
		}
		serr = m.CheckSigleValue(ctx, rc, orderInfo, tbeanValue)
		if nil != serr {
			break
		}
		serr = m.cacheClient.CheckSingleRiskControl(ctx, ts, rc, orderInfo, tbeanValue, pipe)
		if nil != serr {
			break
		}
	}

	// 用户维度的风控检查
	go m.checkUserBalance(orderInfo.ReceiveUid, rcList, businessCfg)

	if serr != nil {
		log.ErrorWithCtx(ctx, "CheckRiskControl CheckSingleRiskControl serr:%v", serr)
		return serr
	}

	if allBgRc == nil && singleBgRc == nil { //未配置所有包裹、单个包裹的风控配置项
		log.ErrorWithCtx(ctx, "CheckRiskControl not risk conf businessID:%v backpackID:%v", orderInfo.BusinessId, orderInfo.BackpackId)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackBusinessNotFound, "该包裹没有风控配置")
	}

	// 新版所有包裹配置项，如果单个包裹ID风控项未配置，不允许发放
	// https://q9jvw0u5f5.feishu.cn/wiki/PPnbwRqAJiNbGKklCT7cpj6Znzc
	if allBgRc != nil && allBgRc.IsCurAllBg == 1 {
		if singleBgRc == nil {
			log.ErrorWithCtx(ctx, "CheckRiskControl not risk conf businessID:%v backpackID:%v", orderInfo.BusinessId, orderInfo.BackpackId)
			return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackBusinessNotFound, "该包裹没有风控配置")
		}
	}

	return nil
}

func (m *Manager) CheckDealToken(ctx context.Context, dealToken string, bid uint32) error {

	if !conf.IsNeedToCheckToken(bid) {
		return nil
	}

	err := deal_token.Check(dealToken)
	if nil != err {
		log.ErrorWithCtx(ctx, "CheckDealToken fail bid:%v err:%v", bid, err)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackOrderidInvalid, "dealtoken校验错误")
	}

	return nil
}

func (m *Manager) checkPackCfg(ctx context.Context, bgId uint32) protocol.ServerError {
	packCfg, err := getBackpackCfg(ctx, bgId)
	if nil != err {
		return err
	}

	if packCfg.GetIsDel() {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("包裹id:%v已经下架", bgId))
		return err
	}

	itemList, err := getBackPackItemList(ctx, bgId)
	if nil != err {
		return err
	}

	isEmpty := true
	nowTs := uint32(time.Now().Unix())
	for _, item := range itemList {
		if item.GetIsDel() {
			continue
		}
		if item.DynamicFinTime == 0 && item.FinTime != 0 && item.FinTime <= nowTs { //绝对时间过期的道具
			continue
		}
		isEmpty = false
		break
	}
	if isEmpty {
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackSysFail, fmt.Sprintf("不能发放空包裹id:%v", bgId))
	}
	return nil
}

const kTrafficMark = "x-qw-traffic-mark"

func GetTrafficMark(ctx context.Context) string {
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if v, ok := md[kTrafficMark]; ok && len(v) > 0 {
			return v[0]
		}
	}
	return ""
}

func (m *Manager) HandlerOrder(ctx context.Context, orderInfo *pb.SendBackpackOrderInfo) protocol.ServerError {

	//单次发放数量限制
	if orderInfo.GetBackpackCnt() == 0 || orderInfo.GetBackpackCnt() > conf.GetMaxSingleCntLimit() {
		log.ErrorWithCtx(ctx, "HandlerOrder MaxSingleCntLimit Check fail orderInfo:%v", orderInfo)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackSysFail, fmt.Sprintf("超出单次发放数量限制%v", conf.GetMaxSingleCntLimit()))
	}

	err := m.checkPackCfg(ctx, orderInfo.GetBackpackId())
	if nil != err {
		log.ErrorWithCtx(ctx, "HandlerOrder checkPackCfg fail orderInfo:%v err:%v", orderInfo, err)
		return err
	}
	orderInfo.TrafficMark = GetTrafficMark(ctx)

	//Redis订单去重，避免短时间一个订单多次调用
	isLock := m.cacheClient.Lock(orderInfo.OrderId, time.Second)
	if !isLock {
		log.ErrorWithCtx(ctx, "HandlerOrder DUPLICATE ORDERID orderInfo:%v", orderInfo)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackSysFail, "相同订单发放频率限制")
	}

	//订单格式检查
	err = m.CheckOrderIdFormat(orderInfo)
	if nil != err {
		log.ErrorWithCtx(ctx, "HandlerOrder CheckOrderIdFormat fail orderInfo:%v err:%v", orderInfo, err)
		return err
	}

	//业务配置检查 & 权限检查
	businessCf, err := m.CheckBusinessPermission(ctx, orderInfo)
	if nil != err {
		log.ErrorWithCtx(ctx, "HandlerOrder CheckBusiness fail orderInfo:%v err:%v", orderInfo, err)
		return err
	}

	//取包裹价值
	packVal, err := getPackValue(ctx, orderInfo.BackpackId)
	if nil != err {
		log.ErrorWithCtx(ctx, "HandlerOrder GetBackpackConfTbeanValue orderInfo:%v err:%v", orderInfo, err)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackSysFail)
	}

	tbeanVal := packVal * orderInfo.BackpackCnt

	//风控项缓存

	notfoundErr := protocol.NewExactServerError(nil, status.ErrRiskControlBackpackConfigNotFound, "找不到对应业务风控配置")

	rcList, err := m.GetControlConfList(ctx, orderInfo.BusinessId)
	if nil != err {
		log.ErrorWithCtx(ctx, "HandlerOrder GetControlConfList not found orderInfo:%v err:%v", err, orderInfo)
		return notfoundErr
	}

	//还没配置风险控制
	if len(rcList) == 0 {
		log.ErrorWithCtx(ctx, "HandlerOrder getBusinessRiskControlConfList empty orderInfo:%v err:%v", err, orderInfo)
		return notfoundErr
	}

	pipe := m.cacheClient.GetPipe()

	//风控检查
	ts := time.Unix(orderInfo.ServerTime, 0)
	err = m.CheckRiskControl(ctx, ts, orderInfo, tbeanVal, rcList, businessCf, pipe)
	if nil != err {
		pipe.Exec()
		log.ErrorWithCtx(ctx, "HandlerOrder CheckRiskControl orderInfo:%v err:%v", orderInfo, err)
		return err
	}

	tx, xerr := m.mysqlStore.DB.Beginx()
	if nil != xerr {
		pipe.Exec()
		log.ErrorWithCtx(ctx, "HandlerOrder Beginx orderInfo:%v err:%v", orderInfo, xerr)
		return protocol.NewServerError(status.ErrRiskControlBackpackSysFail, xerr.Error())
	}

	defer func() {
		if r := recover(); nil != r {
			log.ErrorWithCtx(ctx, "HandlerOrder recover r:%v", r)
			tx.Rollback()
			pipe.Exec()
		}
	}()

	orderInfo.SourceId = businessCf.SourceID

	//订单入库
	err = m.mysqlStore.AddSendBackpackOrder(ctx, tx, ts, orderInfo)
	if nil != err {
		pipe.Exec()
		tx.Rollback()
		log.ErrorWithCtx(ctx, "HandlerOrder AddSendBackpackOrder orderInfo:%v err:%v", orderInfo, err)
		return err
	}

	pipe.Close()
	xerr = tx.Commit()
	if nil != xerr {
		log.ErrorWithCtx(ctx, "HandlerOrder Commit orderInfo:%v err:%v", orderInfo, xerr)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackSysFail, xerr.Error())
	}

	//统计用户获得T豆总价值
	_ = m.cacheClient.AddUserGainPriceStat(ctx, orderInfo.ReceiveUid, int64(tbeanVal), orderInfo.ServerTime)

	//实时入库
	xerr = m.cacheClient.PushQ(RedisTaskQueueKey, orderInfo)
	if nil != xerr {
		log.ErrorWithCtx(ctx, "HandlerOrder PushQ orderInfo:%v err:%v", orderInfo, xerr)
	}

	xerr = m.cacheClient.ZAdd(RedisTaskZRangeKey, redis.Z{
		Score:  float64(orderInfo.ServerTime),
		Member: orderInfo.OrderId,
	})
	if nil != xerr {
		log.ErrorWithCtx(ctx, "HandlerOrder ZAdd orderId:%v err:%v", orderInfo.OrderId, xerr)
	}

	return nil
}

func (m *Manager) GiveSign(req *basePb.GiveUserPackageReq) string {
	signStr := fmt.Sprintf("uid=%d&bg_id=%d&num=%d&source=%d&order_id=%s&expire_duration=%d&outside_time=d%d&total_price=%d",
		req.GetUid(), req.GetBgId(), req.GetNum(), req.GetSource(), req.GetOrderId(), req.GetExpireDuration(), req.GetOutsideTime(), req.GetTotalPrice())

	signBytes := md5.Sum([]byte(signStr + conf.COMMUNICATION_SIGN_KEY))
	return hex.EncodeToString(signBytes[:])
}

// OnlyCheckRisk 仅仅检查风控
func (m *Manager) OnlyCheckRisk(ctx context.Context, req *pb.PreCheckBussinessRiskControlReq) error {
	bc, err := m.getBusinessCfg(ctx, req.BusinessId)
	if nil != err {
		log.ErrorWithCtx(ctx, "OnlyCheckRisk getBusinessCfg not found orderInfo:%v err:%v", req.String(), err)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackBusinessNotFound, "找不到对应业务配置")
	}
	//业务可用状态检查
	if bc.IsValid == 0 {
		log.ErrorWithCtx(ctx, "OnlyCheckRisk business not valid orderInfo:%v", req.String())
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackBusinessNotFound, "业务不可用")
	}

	//业务有效时间检查
	ts := time.Now()
	if bc.BeginTime > ts.Unix() || ts.Unix() > bc.EndTime {
		log.ErrorWithCtx(ctx, "OnlyCheckRisk business time out orderInfo:%v", req.String())
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackBusinessNotFound, "业务已经过期")
	}
	rcList, err := m.GetControlConfList(ctx, req.BusinessId)
	if nil != err {
		log.ErrorWithCtx(ctx, "HandlerOrder getBusinessRiskControlConfList not found orderInfo:%v err:%v", req.String(), err)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackConfigNotFound, "找不到对应业务风控配置")
	}
	if len(rcList) == 0 {
		log.ErrorWithCtx(ctx, "HandlerOrder getBusinessRiskControlConfList not found orderInfo:%v err:%v", req.String(), err)
		return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackConfigNotFound, "找不到对应业务风控配置")
	}

	rcMap := make(map[uint32]*mysql.RiskControlConf, len(rcList))
	for _, rc := range rcList {
		rcMap[rc.BackpackID] = rc
	}

	//所有包裹项-特殊处理
	allBgRc := rcMap[0]
	allTbeanVal := uint32(0)
	allBgNum := uint32(0)
	for bgId, bgNum := range req.BgidCnts {
		//检查包裹是否存在
		err = m.checkPackCfg(ctx, bgId)
		if nil != err {
			log.ErrorWithCtx(ctx, "OnlyCheckRisk checkPackCfg business_id:%d, bg_id:%d, bg_num:%d, err:%v", req.BusinessId, bgId, bgNum, err)
			return err
		}

		//取包裹价值
		packVal, err := getPackValue(ctx, bgId)
		if nil != err {
			log.ErrorWithCtx(ctx, "OnlyCheckRisk GetBackpackConfTbeanValue business_id:%d, bg_id:%d, bg_num:%d, err:%v", req.BusinessId, bgId, bgNum, err)
			return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackSysFail)
		}
		tbeanVal := packVal * bgNum
		allTbeanVal += tbeanVal
		allBgNum += bgNum
		var serr protocol.ServerError
		rc := rcMap[bgId]
		if allBgRc == nil && rc == nil {
			log.ErrorWithCtx(ctx, "OnlyCheckRisk not risk conf businessID:%v backpackID:%v", req.BusinessId, bgId)
			return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackBusinessNotFound, "该包裹没有风控配置")
		}
		// 新版所有包裹配置项，如果单个包裹ID风控项未配置，不允许发放
		// https://q9jvw0u5f5.feishu.cn/wiki/PPnbwRqAJiNbGKklCT7cpj6Znzc
		if allBgRc != nil && allBgRc.IsCurAllBg == 1 {
			if rc == nil {
				log.ErrorWithCtx(ctx, "OnlyCheckRisk not risk conf businessID:%v backpackID:%v", req.BusinessId, bgId)
				return protocol.NewExactServerError(nil, status.ErrRiskControlBackpackBusinessNotFound, "该包裹没有风控配置")
			}
		}

		orderInfo := &pb.SendBackpackOrderInfo{
			BusinessId:  req.BusinessId,
			BackpackId:  bgId,
			BackpackCnt: bgNum,
		}
		if rc != nil {
			serr = m.CheckSigleValue(ctx, rc, orderInfo, tbeanVal)
			if nil != serr {
				log.ErrorWithCtx(ctx, "OnlyCheckRisk CheckSigleValue business_id:%d, bg_id:%d, bg_num:%d, err:%v", req.BusinessId, bgId, bgNum, serr)
				return serr
			}
			serr = m.cacheClient.OnlyCheckSingleRiskControl(ctx, ts, rc, orderInfo, tbeanVal)
			if nil != serr {
				log.ErrorWithCtx(ctx, "OnlyCheckRisk OnlyCheckSingleRiskControl business_id:%d, bg_id:%d, bg_num:%d, err:%v", req.BusinessId, bgId, bgNum, serr)
				return serr
			}
		}
	}

	if allBgRc != nil {
		//所有包裹项-特殊处理
		orderInfo := &pb.SendBackpackOrderInfo{
			BusinessId:  req.BusinessId,
			BackpackCnt: allBgNum,
		}
		serr := m.CheckSigleValue(ctx, allBgRc, orderInfo, allTbeanVal)
		if nil != serr {
			log.ErrorWithCtx(ctx, "OnlyCheckRisk CheckSigleValue all business_id:%d, bg_num:%d, tbeanVal:%d, err:%v", req.BusinessId, allBgNum, allTbeanVal, serr)
			return serr
		}
		serr = m.cacheClient.OnlyCheckSingleRiskControl(ctx, ts, allBgRc, orderInfo, allTbeanVal)
		if nil != serr {
			log.ErrorWithCtx(ctx, "OnlyCheckRisk OnlyCheckSingleRiskControl all business_id:%d, bg_num:%d, tbeanVal:%d, err:%v", req.BusinessId, allBgNum, allTbeanVal, serr)
			return serr
		}
	}
	log.InfoWithCtx(ctx, "OnlyCheckRisk business_id:%d, bg_num:%d, tbeanVal:%d, success", req.BusinessId, allBgNum, allTbeanVal)
	return nil
}

func (m *Manager) ResendBusinessSecret(ctx context.Context, businessId uint32, isRecreate bool) error {
	if isRecreate {
		secretKey := utils.GenAESKey()
		err := m.mysqlStore.UpdateBusinessSecret(ctx, string(secretKey), businessId)
		if err != nil {
			log.ErrorWithCtx(ctx, "ResendBusinessSecret UpdateBusinessSecret err:%v", err)
			return err
		}
		log.InfoWithCtx(ctx, "ResendBusinessSecret businessId:%v secretKey:%v", businessId, string(secretKey))
	}
	secretKey, err := m.SendSecretKeyEmail(ctx, businessId)
	log.InfoWithCtx(ctx, "ResendBusinessSecret businessId:%v, secretKey:%v, err:%v", businessId, secretKey, err)
	return err
}
