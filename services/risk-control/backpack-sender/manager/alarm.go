package manager

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/risk-control/backpack-sender/conf"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

type LoginDataReq struct {
	ProjectId     string `json:"projectid"`
	ProjectSecret string `json:"projectsecret"`
}

type LoginData struct {
	AccessToken string `json:"AccessToken"`
}

type LoginDataRes struct {
	Code int       `json:"code"`
	Msg  string    `json:"msg"`
	Data LoginData `json:"data"`
}

type ObjectDataType struct {
	CmdbType   string `json:"cmdb_type"`
	BusinessId string `json:"business_id"`
	BgId       string `json:"bg_id"`
	Hour       string `json:"hour,omitempty"`
}

type AlertDataType struct {
	AlertName         string `json:"alertname"`
	ScheduleUser      string `json:"schedule_user,omitempty"`
	ScheduleUserEmail string `json:"schedule_user_email,omitempty"`
}

type FlyBookType struct {
	Groups []string `json:"group_ids"`
	Emails []string `json:"emails"`
}

type NotifyDataType struct {
	FlyBook FlyBookType `json:"flybook"`
}

type EventReq struct {
	ObjectData  ObjectDataType `json:"ObjectData"`
	AlertData   AlertDataType  `json:"AlertData"`
	NotifyData  NotifyDataType `json:"NotifyData"`
	Severity    string         `json:"Severity"`
	AlertStatus string         `json:"AlertStatus"`
	StartTime   string         `json:"StartTime,omitempty"`
	EndTime     string         `json:"EndTime,omitempty"`
	AlertText   string         `json:"AlertText"`
}

type EventRes struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

const loginUrl = "/api/v1/login"
const eventUrl = "/api/v1/event/"
const successCode = 2000

func (m *Manager) SendAlarm(alterText string, businessId, bgId uint32, email string, opUser string, now time.Time, isHour bool, isResolved bool) {
	alarmConf := conf.GetAlarmConf()
	if alarmConf == nil || !alarmConf.Enable {
		return
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	client := http.Client{}

	loginData := LoginDataReq{
		ProjectId:     alarmConf.ProjectId,
		ProjectSecret: alarmConf.ProjectSecret,
	}
	b, err := json.Marshal(loginData)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendAlarm json.Marshal(loginData) failed, :%v, err:%v", alterText, err)
		return
	}
	body := bytes.NewBuffer(b)

	request, err := http.NewRequest("POST", alarmConf.Url+loginUrl, body)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendAlarm http.NewRequest failed, :%v, err:%v", alterText, err)
		return
	}
	request.Header.Add("Content-Type", "application/json")
	response, err := client.Do(request)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendAlarm client.Do failed, :%v, err:%v", alterText, err)
		return
	}
	defer response.Body.Close()
	if response.StatusCode != http.StatusOK {
		log.ErrorWithCtx(ctx, "SendAlarm response.StatusCode=%d", response.StatusCode)
		return
	}

	data, err := ioutil.ReadAll(response.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendAlarm ioutil.ReadAll failed, %v, %v, err:%v", response.Body, alterText, err)
		return
	}

	var loginDataRes LoginDataRes
	err = json.Unmarshal(data, &loginDataRes)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendAlarm json.Unmarshal failed, %v, %v, err:%v", data, alterText, err)
		return
	}

	if loginDataRes.Code != successCode {
		msg := fmt.Sprintf("code err=%d", loginDataRes.Code)
		log.ErrorWithCtx(ctx, "SendAlarm loginDataRes.Code != successCode, %v, %v", msg, alterText)
		return
	}

	log.InfoWithCtx(ctx, "SendAlarm login data :%v", string(data))

	var event EventReq
	event.ObjectData.CmdbType = "包裹发放风控"
	event.ObjectData.BusinessId = fmt.Sprintf("%v", businessId)
	event.ObjectData.BgId = fmt.Sprintf("%v", bgId)
	if isHour {
		event.ObjectData.Hour = fmt.Sprintf("%02d", now.Hour())
	}
	event.AlertData.AlertName = "包裹发放风控告警"
	if len(alarmConf.Group) > 0 {
		event.NotifyData.FlyBook.Groups = []string{alarmConf.Group}
	}
	if alarmConf.NeedMail && email != "" {
		event.NotifyData.FlyBook.Emails = []string{email}
		if alarmConf.NeedPhone {
			event.AlertData.ScheduleUser = opUser
			event.AlertData.ScheduleUserEmail = email
		}
	}
	event.Severity = alarmConf.Severity
	if isResolved {
		event.AlertStatus = "resolved"
		event.EndTime = fmt.Sprintf("%d", time.Now().Unix())
	} else {
		event.AlertStatus = "firing"
		event.StartTime = fmt.Sprintf("%d", time.Now().Unix())
	}
	event.AlertText = alterText

	b, err = json.Marshal(event)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendAlarm json.Marshal failed, %v, %v, err:%v", event, alterText, err)
		return
	}
	body = bytes.NewBuffer(b)

	request, err = http.NewRequest("POST", alarmConf.Url+eventUrl+alarmConf.ProjectId, body)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendAlarm http.NewRequest failed, %v, %v, err:%v", event, alterText, err)
		return
	}
	request.Header.Add("Content-Type", "application/json")
	request.Header.Add("Authorization", "Bearer "+loginDataRes.Data.AccessToken)

	response, err = client.Do(request)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendAlarm client.Do failed, %v, %v, err:%v", event, alterText, err)
		return
	}
	defer response.Body.Close()
	if response.StatusCode != http.StatusOK {
		log.ErrorWithCtx(ctx, "SendAlarm response.StatusCode != http.StatusOK, %v, %v", event, alterText)
		return
	}

	data, err = ioutil.ReadAll(response.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendAlarm ioutil.ReadAll failed, %v, %v, err:%v", response.Body, alterText, err)
		return
	}
	if !isResolved {
		err = m.cacheClient.SetAlarmRecord(ctx, businessId, bgId, now, isHour, opUser, email)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendAlarm SetAlarmRecord failed, %v, %v, err:%v", event, alterText, err)
		}
	}
	log.InfoWithCtx(ctx, "SendAlarm event req:%v, email:%v, hour:%v, isResolved:%v response ok data :%v", alterText, email, isHour, isResolved, string(data))
}

func (m *Manager) CheckAndRecoverAlarm(businessId, bgId uint32) {
	alarmConf := conf.GetAlarmConf()
	if alarmConf == nil || !alarmConf.Enable {
		return
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	log.InfoWithCtx(ctx, "CheckAndRecoverAlarm check alarm record, bid:%v, bgId:%v", businessId, bgId)
	now := time.Now()
	// 当天日维度的告警
	isAlarmed, scheduleUser, scheduleMail, err := m.cacheClient.GetAlarmRecord(ctx, businessId, bgId, false, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAndRecoverAlarm GetAlarmRecord failed, bid:%v, err:%v", businessId, err)
		return
	}
	if isAlarmed {
		m.SendAlarm("", businessId, bgId, scheduleMail, scheduleUser, now, false, true)
		err = m.cacheClient.DelAlarmRecord(ctx, businessId, bgId, false, now)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckAndRecoverAlarm DelAlarmRecord failed, bid:%v, err:%v", businessId, err)
		}
	}
	// 前一天日维度的告警
	now = now.AddDate(0, 0, -1)
	isAlarmed, scheduleUser, scheduleMail, err = m.cacheClient.GetAlarmRecord(ctx, businessId, bgId, false, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAndRecoverAlarm GetAlarmRecord failed, bid:%v, err:%v", businessId, err)
		return
	}
	if isAlarmed {
		m.SendAlarm("", businessId, bgId, scheduleMail, scheduleUser, now, false, true)
		err = m.cacheClient.DelAlarmRecord(ctx, businessId, bgId, false, now)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckAndRecoverAlarm DelAlarmRecord failed, bid:%v, err:%v", businessId, err)
		}
	}
	now = time.Now()
	// 当天小时维度的告警
	isAlarmed, scheduleUser, scheduleMail, err = m.cacheClient.GetAlarmRecord(ctx, businessId, bgId, true, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAndRecoverAlarm GetAlarmRecord failed, bid:%v, err:%v", businessId, err)
		return
	}
	if isAlarmed {
		m.SendAlarm("", businessId, bgId, scheduleMail, scheduleUser, now, true, true)
		err = m.cacheClient.DelAlarmRecord(ctx, businessId, bgId, true, now)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckAndRecoverAlarm DelAlarmRecord failed, bid:%v, err:%v", businessId, err)
		}
	}
	// 前一小时维度的告警
	now = now.Add(-1 * time.Hour)
	isAlarmed, scheduleUser, scheduleMail, err = m.cacheClient.GetAlarmRecord(ctx, businessId, bgId, true, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAndRecoverAlarm GetAlarmRecord failed, bid:%v, err:%v", businessId, err)
		return
	}
	if isAlarmed {
		m.SendAlarm("", businessId, bgId, scheduleMail, scheduleUser, now, true, true)
		err = m.cacheClient.DelAlarmRecord(ctx, businessId, bgId, true, now)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckAndRecoverAlarm DelAlarmRecord failed, bid:%v, err:%v", businessId, err)
		}
	}

	log.InfoWithCtx(ctx, "CheckAndRecoverAlarm alarm recovered, bid:%v, bgid:%d", businessId, bgId)
}

// CheckAndReconverAlarmTimer 定时检查恢复上一小时的告警和每天0点检查恢复前一天的告警
func (m *Manager) CheckAndReconverAlarmTimer() {
	alarmConf := conf.GetAlarmConf()
	if alarmConf == nil || !alarmConf.Enable {
		return
	}
	ctx := context.Background()

	// 检查恢复上一小时的告警
	now := time.Now()
	lastHourT := now.Add(-1 * time.Hour)
	allAlarmRecord, err := m.cacheClient.GetAlarmRecordAll(ctx, true, lastHourT)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAndReconverAlarmTimer GetAlarmRecordAll failed, err:%v", err)
		return
	}
	log.InfoWithCtx(ctx, "CheckAndReconverAlarmTimer check last hour alarm record:%v", allAlarmRecord)
	for k, v := range allAlarmRecord {
		arr := strings.Split(k, ",")
		var bid, bgid uint32
		_, err := fmt.Sscanf(arr[0], "%d", &bid)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckAndReconverAlarmTimer fmt.Sscanf bid failed, k:%v, err:%v", k, err)
			continue
		}
		_, err = fmt.Sscanf(arr[1], "%d", &bgid)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckAndReconverAlarmTimer fmt.Sscanf bgid failed, k:%v, err:%v", k, err)
			continue
		}
		scheduleArr := strings.Split(v, ",")
		opUser := scheduleArr[0]
		email := scheduleArr[1]
		m.SendAlarm("", bid, bgid, email, opUser, lastHourT, true, true)
		err = m.cacheClient.DelAlarmRecord(ctx, bid, bgid, true, lastHourT)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckAndReconverAlarmTimer DelAlarmRecord failed, bid:%v, err:%v", bid, err)
		}
	}

	log.InfoWithCtx(ctx, "CheckAndReconverAlarmTimer alarm recovered, last hour time:%v", lastHourT)

	if now.Hour() != 0 {
		return
	}

	// 每天0点检查恢复前一天的告警
	allAlarmRecord, err = m.cacheClient.GetAlarmRecordAll(ctx, false, lastHourT)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAndReconverAlarmTimer GetAlarmRecordAll failed, err:%v", err)
		return
	}
	log.InfoWithCtx(ctx, "CheckAndReconverAlarmTimer check last day alarm record:%v", allAlarmRecord)
	for k, v := range allAlarmRecord {
		arr := strings.Split(k, ",")
		var bid, bgid uint32
		_, err := fmt.Sscanf(arr[0], "%d", &bid)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckAndReconverAlarmTimer fmt.Sscanf bid failed, k:%v, err:%v", k, err)
			continue
		}
		_, err = fmt.Sscanf(arr[1], "%d", &bgid)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckAndReconverAlarmTimer fmt.Sscanf bgid failed, k:%v, err:%v", k, err)
			continue
		}
		scheduleArr := strings.Split(v, ",")
		opUser := scheduleArr[0]
		email := scheduleArr[1]
		m.SendAlarm("", bid, bgid, email, opUser, lastHourT, false, true)
		err = m.cacheClient.DelAlarmRecord(ctx, bid, bgid, false, lastHourT)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckAndReconverAlarmTimer DelAlarmRecord failed, bid:%v, err:%v", bid, err)
		}
	}
	log.InfoWithCtx(ctx, "CheckAndReconverAlarmTimer alarm recovered, last day time:%v", lastHourT)
}
