package main

import (
    "os"

    grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
    "golang.org/x/net/context"
    "google.golang.org/grpc"
    "google.golang.org/grpc/grpclog"

    "golang.52tt.com/pkg/config"
    grpcEx "golang.52tt.com/pkg/foundation/grpc/server"
    "golang.52tt.com/pkg/tracing"
    traceGRPC "golang.52tt.com/pkg/tracing/grpc"
    "golang.52tt.com/pkg/tracing/jaeger"
    pb "golang.52tt.com/protocol/app/api/channel_live"
    oldPb "golang.52tt.com/protocol/services/logicsvr-go/channel-live-logic"

    "golang.52tt.com/services/channel-live-logic/server"
    "golang.52tt.com/services/runtime"

    _ "golang.52tt.com/pkg/hub/tyr/compatible/logic" // 兼容tyr公共库
)

func main() {
    flags := grpcEx.ParseServerFlags(os.Args)

    tracer := jaeger.Init("channel-live-logic")

    grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))

    var (
        svr *server.ChannelLiveLogic_
        err error
    )
    initializer := func(ctx context.Context, s *grpc.Server, sc *config.ServerConfig) error {
        svr, err = server.NewChannelLiveLogic_(sc.Configer, tracer)
        if err != nil {
            return err
        }
        pb.RegisterChannelLiveLogicServer(s, svr)
        oldPb.RegisterChannelLiveLogicServer(s, svr)
        return nil
    }

    closer := func(ctx context.Context, s *grpc.Server) error {
        if svr != nil {
            svr.ShutDown()
        }
        return nil
    }

    unaryInt := grpc_middleware.ChainUnaryServer(
        runtime.LogicServerUnaryInterceptor(flags.LogRequests, flags.LogResponses),
        traceGRPC.TracedUnaryServerInterceptor(
            tracing.UsingTracer(tracer), tracing.LogPayloads(true),
        ),
    )

    s := grpcEx.NewServer(
        flags,
        grpcEx.WithGRPCServerOptions(grpc.UnaryInterceptor(unaryInt)),
        grpcEx.WithGRPCServerInitializer(initializer),
        grpcEx.WithGRPCServerCloser(closer),
        grpcEx.WithDefaultConfig("channel-live-logic.json", grpcEx.AdapterJSON),
    )
    s.Serve()
    //8
}
