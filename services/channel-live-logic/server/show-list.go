package server

import (
    channel_live_logic "golang.52tt.com/protocol/app/channel-live-logic"
    "context"
    "golang.52tt.com/pkg/protocol"
    "google.golang.org/grpc/codes"
    "golang.52tt.com/protocol/common/status"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    channel_live_show_list "golang.52tt.com/protocol/services/channel-live-show-list"
)

var (
    errParamInvalid = protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
)

func (s *ChannelLiveLogic_) ReportLiveShowScore(ctx context.Context, request *channel_live_logic.ReportLiveShowScoreRequest) (*channel_live_logic.ReportLiveShowScoreResponse, error) {
    out := &channel_live_logic.ReportLiveShowScoreResponse{}

    // 参数检查
    if request.GetShowId() == 0 || request.GetScore() <= 0 || request.GetScore() > 5 {
        return out, errParamInvalid
    }

    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok || serviceInfo.UserID == 0 {
        log.ErrorWithCtx(ctx, "GetChannelActivityEntry ServiceInfoFromContext fail. serviceInfo.UserID:%d", serviceInfo.UserID)
        return out, errParamInvalid
    }

    _, err := s.channelLiveShowCli.RateChannelLiveShow(ctx, &channel_live_show_list.RateChannelLiveShowRequest{
        Uid:    serviceInfo.UserID,
        ShowId: request.GetShowId(),
        Score:  request.GetScore(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "RateChannelLiveShow fail. err:%v", err)
        return out, err
    }

    log.InfoWithCtx(ctx, "ReportLiveShowScore success.uid:%d request:%+v", serviceInfo.UserID, request)
    return out, nil
}
