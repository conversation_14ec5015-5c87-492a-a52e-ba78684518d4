package mgr

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/config"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/store"
)

func getBoxId(ctx context.Context, boxList []*config.BoxConfig) (*config.BoxConfig, error) {
	if len(boxList) == 0 {
		return &config.BoxConfig{}, protocol.NewExactServerError(ctx, status.ErrRequestParamInvalid, "box list is empty")
	}
	// 先算概率
	var ratioSum uint32
	for _, box := range boxList {
		ratioSum += box.OpenRatio
	}

	randInt := rand.Intn(int(ratioSum))
	for _, box := range boxList {
		if randInt < int(box.OpenRatio) {
			log.DebugWithCtx(ctx, "chooseWealthGodBoxId selected boxId: %d, randInt: %d, ratioSum: %d", box.BoxId, randInt, ratioSum)
			return box, nil
		}
		randInt -= int(box.OpenRatio)
	}
	// 不应该走到这里，除非概率配置有问题
	log.ErrorWithCtx(ctx, "chooseWealthGodBoxId failed to select boxId, randInt: %d, ratioSum: %d", randInt, ratioSum)
	return &config.BoxConfig{}, protocol.NewExactServerError(ctx, status.ErrRequestParamInvalid, "failed to select boxId")
}

func (m *Mgr) chooseWealthGodBoxId(ctx context.Context, godId string) (uint32, error) {
	boxList := config.GetDynamicConfig().BoxList
	var box *config.BoxConfig
	var err error

	box, err = getBoxId(ctx, boxList)
	if err != nil {
		log.ErrorWithCtx(ctx, "chooseWealthGodBoxId err: %v, godId: %s", err, godId)
		return 0, err
	}

	if box == nil {
		log.WarnWithCtx(ctx, "chooseWealthGodBoxId no box selected, using bottom box, godId: %s", godId)
		return config.GetBottomBoxId(), nil
	} else if box.IsBottomBox || !box.NeedCheckStock { // 检查是否需要扣库存,不需要直接返回
		log.DebugWithCtx(ctx, "chooseWealthGodBoxId no need check stock, box: %+v, godId: %s", box, godId)
		return box.BoxId, nil
	}

	nowValue := time.Now().Unix()
	// 准备加锁扣库存
	err = m.cache.TryLockBoxCntChange(ctx, box.BoxType, nowValue)
	if err != nil {
		log.ErrorWithCtx(ctx, "chooseWealthGodBoxId TryLockBoxCntChange err: %v, boxType: %d, godId: %s", err, box.BoxType, godId)
		return 0, protocol.NewExactServerError(ctx, status.ErrRequestParamInvalid, "failed to lock box divide")
	}
	defer func() {
		err = m.cache.UnLockBoxDivide(ctx, box.BoxType, nowValue)
		if err != nil {
			log.ErrorWithCtx(ctx, "chooseWealthGodBoxId UnLockBoxDivide err: %v, boxType: %d, godId: %s", err, box.BoxType, godId)
		}
	}()

	// 检查这个box的库存
	currentCnt, err := m.store.GetGodBoxCnt(ctx, box.BoxType)
	if err != nil {
		log.ErrorWithCtx(ctx, "chooseWealthGodBoxId GetGodBoxCnt err: %v, boxType: %d, godId: %s", err, box.BoxType, godId)
		return 0, err
	}

	if currentCnt > 0 {
		// 有库存，扣除库存并返回
		err = m.store.InsertGodBoxCntLogWithTxn(ctx, &store.GodBoxCntLog{
			GodBoxType: box.BoxType,
			ChangeCnt:  -1,
			Reason:     "财神降临宝箱发放",
			GodId:      godId,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "chooseWealthGodBoxId InsertGodBoxCntLog err: %v, boxType: %d, godId: %s", err, box.BoxType, godId)
			return 0, err
		}
		log.InfoWithCtx(ctx, "chooseWealthGodBoxId selected boxId: %d, currentCnt: %d, godId: %s", box.BoxId, currentCnt, godId)
		return box.BoxId, nil
	} else {
		log.InfoWithCtx(ctx, "chooseWealthGodBoxId no stock, using backup box, boxType: %d, godId: %s", box.BoxType, godId)
		return config.GetBottomBoxId(), nil
	}

}

func (m *Mgr) handleTimeReleaseBox(ctx context.Context) {
	now := time.Now()
	if now.Unix() < config.GetDynamicConfig().ActivityStartTs || now.Unix() > config.GetDynamicConfig().ActivityEndTs {
		log.DebugWithCtx(ctx, "handleTimeReleaseBox not in activity time, now: %v, start: %v, end: %v", now, config.GetDynamicConfig().ActivityStartTs, config.GetDynamicConfig().ActivityEndTs)
		return // 不在活动时间内
	}
	boxList := config.GetDynamicConfig().BoxList
	for _, box := range boxList {
		for _, timeRange := range box.TimeStockReleaseScopes {
			// 开始时间不能大于结束时间
			if timeRange.StartHour >= timeRange.EndHour {
				log.ErrorWithCtx(ctx, "handleTimeReleaseBox invalid time range for box type %d: start %d >= end %d", box.BoxType, timeRange.StartHour, timeRange.EndHour)
				continue // 跳过无效的时间范围
			}

			// 判断当前时间是否处于这个时间范围内
			currentHour := time.Now().Hour()
			if currentHour >= int(timeRange.StartHour) && currentHour < int(timeRange.EndHour) {
				// 获取上次释放时间
				lastReleaseTime, err := m.cache.GetLastBoxReleaseTime(ctx, box.BoxType)
				if err != nil {
					log.ErrorWithCtx(ctx, "handleTimeReleaseBox GetLastBoxReleaseTime err: %v", err)
					return
				}
				if time.Now().Unix()-lastReleaseTime < int64(box.TimeStockReleaseStep) {
					log.DebugWithCtx(ctx, "handleTimeReleaseBox not time to release box type %d, last release time: %d, step: %d", box.BoxType, lastReleaseTime, box.TimeStockReleaseStep)
					continue // 还没到释放时间
				}

				if err = m.releaseBoxByTime(ctx, box, timeRange, now); err != nil {
					log.ErrorWithCtx(ctx, "handleTimeReleaseBox releaseBoxByTime err: %v, boxType: %d, currentHour: %d, timeRange: %+v", err, box.BoxType, currentHour, timeRange)
					return
				}
			}
		}
	}
	log.DebugWithCtx(ctx, "handleTimeReleaseBox completed at %v", now)
	return
}

func (m *Mgr) changeBoxCntWithLock(ctx context.Context, boxType uint32, changeCnt int64, reason string) error {
	if changeCnt == 0 {
		return nil
	}
	log.InfoWithCtx(ctx, "changeBoxCntWithLock begin, boxType: %d, changeCnt: %d", boxType, changeCnt)
	nowValue := time.Now().Unix()
	err := m.cache.TryLockBoxCntChange(ctx, boxType, nowValue)
	if err != nil {
		log.ErrorWithCtx(ctx, "changeBoxCntWithLock TryLockBoxCntChange err: %v, boxType: %d", err, boxType)
		return err
	}
	defer func() {
		_ = m.cache.UnLockBoxDivide(ctx, boxType, nowValue)
	}()

	err = m.store.InsertGodBoxCntLogWithTxn(ctx, &store.GodBoxCntLog{
		GodBoxType: boxType,
		ChangeCnt:  changeCnt,
		Reason:     reason,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "changeBoxCntWithLock InsertGodBoxCntLog err: %v", err)
		return err
	}

	log.InfoWithCtx(ctx, "changeBoxCntWithLock completed, boxType: %d, changeCnt: %d", boxType, changeCnt)
	return nil
}

func (m *Mgr) releaseBoxByTime(ctx context.Context, box *config.BoxConfig, timeRange config.HourScope, now time.Time) error {
	err := m.changeBoxCntWithLock(ctx, box.BoxType, int64(box.TimeStockReleaseCnt), "定时释放宝箱")
	if err != nil {
		log.ErrorWithCtx(ctx, "handleTimeReleaseBox changeBoxCntWithLock err: %v, boxType: %d", err, box.BoxType)
		return err
	}

	// 更新上次释放时间
	err = m.cache.SetLastBoxReleaseTime(ctx, box.BoxType, time.Now().Unix())
	if err != nil {
		log.ErrorWithCtx(ctx, "handleTimeReleaseBox SetLastBoxReleaseTime err: %v", err)
		return err
	}

	log.InfoWithCtx(ctx, "handleTimeReleaseBox trigger box type %d, start: %d, end: %d, now: %v",
		box.BoxType, timeRange.StartHour, timeRange.EndHour, now)
	return nil
}

func (m *Mgr) ChangeBoxStock(ctx context.Context, boxType uint32, changeCnt int64, reason string) error {
	return m.changeBoxCntWithLock(ctx, boxType, changeCnt, reason)
}

func (m *Mgr) handleAllGiftValueReleaseBox(ctx context.Context) {
	// 检查是否在活动时间内
	if now := time.Now().Unix(); now < config.GetDynamicConfig().ActivityStartTs || now > config.GetDynamicConfig().ActivityEndTs {
		log.DebugWithCtx(ctx, "handleAllGiftValueReleaseBox not in activity time, now: %v, start: %v, end: %v", now, config.GetDynamicConfig().ActivityStartTs, config.GetDynamicConfig().ActivityEndTs)
		return // 不在活动时间内
	}

	// 获取当前的全网流水
	newValue, err := m.cache.GetAllGiftValue(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAllGiftEvent GetAllGiftValue err: %v", err)
		return
	}

	// 判断新旧值是否刚刚超过了全网流水的阈值(比如20w)， 多个不同的宝箱配置可能会有不同的阈值
	boxCfgList := config.GetDynamicConfig().BoxList
	for _, boxCfg := range boxCfgList {
		// 两个值除step， 看结果是否相差1
		step := boxCfg.IncomeStockReleaseStep
		// 有些兜底宝箱不需要库存
		if step <= 0 {
			continue // 跳过无效的配置
		}

		if err = m.handleReleaseBoxWithBoxType(ctx, boxCfg, step, newValue); err != nil {
			log.ErrorWithCtx(ctx, "handleAllGiftEvent handleReleaseBoxWithBoxType err: %v, boxType: %d", err, boxCfg.BoxType)
			return
		}
	}
}

func (m *Mgr) handleReleaseBoxWithBoxType(ctx context.Context, boxCfg *config.BoxConfig, step uint32, newValue int64) error {
	// 获取上次释放的全网流水值
	lastReleaseValue, err := m.cache.GetLastReleaseValue(ctx, boxCfg.BoxType)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAllGiftEvent GetLastReleaseValue err: %v, boxType: %d", err, boxCfg.BoxType)
		return err
	}
	lastDivide := uint32(lastReleaseValue / int64(step))
	newDivide := uint32(newValue / int64(step))
	if diff := newDivide - lastDivide; diff > 0 {
		// 触发了全网流水的阈值，发放财富神宝箱
		log.InfoWithCtx(ctx, "handleAllGiftEvent trigger currentDivide: %d, newDivide: %d, diff: %d, box cfg: %+v", lastDivide, newDivide, diff, boxCfg)

		err = m.changeBoxCntWithLock(ctx, boxCfg.BoxType, int64(boxCfg.IncomeStockReleaseCnt)*int64(diff), fmt.Sprintf("全网流水达到%d增加宝箱库存", int64(boxCfg.IncomeStockReleaseStep)*int64(diff)))
		if err != nil {
			log.ErrorWithCtx(ctx, "handleAllGiftEvent changeBoxCntWithLock err: %v, boxType: %d, diff: %d", err, boxCfg.BoxType)
			return err
		}

		// 更新上次释放的全网流水值
		err = m.cache.SetLastReleaseValue(ctx, boxCfg.BoxType, newValue)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleAllGiftEvent SetLastReleaseValue err: %v, boxType: %d", err, boxCfg.BoxType)
			return err
		}
		log.InfoWithCtx(ctx, "handleAllGiftEvent completed, boxType: %d, newValue: %d, diff: %d", boxCfg.BoxType, newValue, diff)
	}
	return err
}
