package mgr

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app/wealth_god_logic"
	wealth_god "golang.52tt.com/protocol/services/wealth-god"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/config"
	"sort"
	"time"
)

func (m *Mgr) FinishWealthGodMission(ctx context.Context, uid uint32, godId string, missionType wealth_god_logic.WealthMissionType) (bool, bool, error) {
	// 检查用户是否在黑名单中(取反)
	needReward := !m.CheckUserIsInBlackList(uid)

	if missionType == wealth_god_logic.WealthMissionType_WEALTH_MISSION_TYPE_STAY_IN_GAME {
		// 校验用户是否真的呆在这个房间
		godInfo := m.localCache.GetGodInfo(godId)
		if godInfo == nil {
			log.WarnWithCtx(ctx, "GetGodInfo godId %s is nil", godId)
			return false, false, nil
		}
		cid, err1 := m.rpcCli.ChannelolCli.GetUserChannelId(ctx, uid, uid)
		if err1 != nil {
			log.ErrorWithCtx(ctx, "GetUserChannelId err: %v, uid: %d", err1, uid)
			return false, false, err1
		}
		if cid != godInfo.Cid {
			log.WarnWithCtx(ctx, "FinishWealthGodMission cid mismatch, report=%d, actual=%d, uid=%d", godInfo.Cid, cid, uid)
			return false, false, nil
		}

		// lock，短时间内上报两次，说明客户端异常
		err := m.cache.TryLockStayRoomMission(ctx, uid, time.Duration(config.GetNeedStayRoomMissionSecond()-5)*time.Second)
		if err != nil {
			log.WarnWithCtx(ctx, "TryLockStayRoomMission err: %v, uid: %d", err, uid)
			return false, false, err
		}
		// 增加用户在财神房间停留的次数
		cnt, err := m.cache.IncrStayRoomMissionCnt(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "IncrStayRoomMissionCnt err: %v, uid: %d", err, uid)
			return false, false, err
		}
		cfgCnt := config.GetDynamicConfig().StayRoomMissionLimitCnt
		if cfgCnt != 0 && uint32(cnt) > cfgCnt {
			log.WarnWithCtx(ctx, "IncrStayRoomMissionCnt cnt %d > limit %d, uid: %d", cnt, cfgCnt, uid)
			return false, false, nil
		}
	}

	insertSuccess, err := m.store.FinishMission(ctx, uid, godId, missionType, needReward)
	if err != nil {
		log.ErrorWithCtx(ctx, "FinishWealthGodMission err: %v, uid: %d, godId: %s, missionType: %d", err, uid, godId, missionType)
		return false, false, err
	}

	log.InfoWithCtx(ctx, "FinishWealthGodMission success, uid: %d, godId: %s, missionType: %d", uid, godId, missionType)
	return insertSuccess, needReward, nil
}

func (m *Mgr) CheckUserIsInBlackList(uid uint32) bool {
	// 黑产检查
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	hitUid, err := m.rpcCli.DarkCli.UserBehaviorCheck(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckUserIsInBlackList UserBehaviorCheck Failed uid %d err %v", uid, err)
	}

	if hitUid != 0 {
		log.InfoWithCtx(ctx, "CheckUserIsInBlackList uid %d hitUid %d is in black list", uid, hitUid)
		return true
	}
	return false
}

func (m *Mgr) IsReachStayRoomMissionLimit(ctx context.Context, uid uint32) (bool, error) {
	cnt, err := m.cache.GetStayRoomMissionCnt(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "IsReachStayRoomMissionLimit err: %v, uid: %d", err, uid)
		return false, err
	}
	if cntCfg := config.GetDynamicConfig().StayRoomMissionLimitCnt; cntCfg != 0 {
		return cnt >= int64(cntCfg), nil
	}
	return false, nil
}

func (m *Mgr) GetWealthMissionInfo(ctx context.Context, uid uint32, godId string) ([]*wealth_god.WealthMissionInfo, error) {

	missionMap, err := m.store.GetWealthMissionInfoMap(ctx, uid, godId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWealthMissionInfo err:%v, uid:%d, godId:%s", err, uid, godId)
		return []*wealth_god.WealthMissionInfo{}, err
	}

	missionCfgMap := config.GetDynamicConfig().MissionCfgMap
	out := make([]*wealth_god.WealthMissionInfo, 0, len(missionCfgMap))

	cnt, err := m.cache.GetStayRoomMissionCnt(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "IsReachStayRoomMissionLimit err: %v, uid: %d", err, uid)
		return out, err
	}
	var isReachLimit bool
	if cntCfg := config.GetDynamicConfig().StayRoomMissionLimitCnt; cntCfg != 0 {
		if cnt > int64(cntCfg) {
			isReachLimit = true
		} else {
			if cnt == int64(cntCfg) {
				// 如果是本财神达到上限，则暂时不返回true
				if missionMap[wealth_god_logic.WealthMissionType_WEALTH_MISSION_TYPE_STAY_IN_GAME] == nil {
					isReachLimit = true
				}
			} else {
				isReachLimit = false
				// log
				log.WarnWithCtx(ctx, "GetWealthMissionInfo cnt %d < limit %d, uid: %d", cnt, cntCfg, uid)
			}
		}
	}

	for missionType, missionCfg := range missionCfgMap {
		currentCnt := uint32(0)
		info := &wealth_god.WealthMissionInfo{
			MissionType:    missionType,
			MissionIcon:    missionCfg.MissionIcon,
			MissionName:    missionCfg.MissionName,
			MissionDesc:    missionCfg.MissionDesc,
			MissionCntUint: missionCfg.MissionCntUnit,
			NeedMissionCnt: missionCfg.NeedMissionCnt,
		}
		if missionType == uint32(wealth_god_logic.WealthMissionType_WEALTH_MISSION_TYPE_STAY_IN_GAME) {
			info.IsReachLimit = isReachLimit
		}
		// 填充次数
		if missionMap[wealth_god_logic.WealthMissionType(missionType)] != nil {
			if missionType == uint32(wealth_god_logic.WealthMissionType_WEALTH_MISSION_TYPE_STAY_IN_GAME) {
				currentCnt = config.GetNeedStayRoomMissionSecond()
			} else {
				currentCnt = 1
			}
		}
		info.CurrentCnt = currentCnt
		out = append(out, info)
	}

	// missionType为在房任务时，排在前边，其他顺序不变
	sort.Slice(out, func(i, j int) bool {
		if out[i].MissionType == uint32(wealth_god_logic.WealthMissionType_WEALTH_MISSION_TYPE_STAY_IN_GAME) {
			return true
		}
		if out[j].MissionType == uint32(wealth_god_logic.WealthMissionType_WEALTH_MISSION_TYPE_STAY_IN_GAME) {
			return false
		}
		return false
	})

	return out, nil
}
