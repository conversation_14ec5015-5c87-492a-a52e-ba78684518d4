package util

import "time"

// GetNatureWeekStart 获取自然周的开始时间
func GetNatureWeekStart() time.Time {
    now := time.Now()
    offset := int(time.Monday - now.Weekday())
    if offset > 0 {
        offset = -6
    }
    monday := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).AddDate(0, 0, offset)
    return monday
}

// GetOneNatureWeekStart 获取指定时间的自然周的开始时间
func GetOneNatureWeekStart(datetime time.Time) time.Time {
    offset := int(time.Monday - datetime.Weekday())
    if offset > 0 {
        offset = -6
    }
    monday := time.Date(datetime.Year(), datetime.Month(), datetime.Day(), 0, 0, 0, 0, datetime.Location()).AddDate(0, 0, offset)
    return monday
}
