package server

import (
	"context"
	"encoding/binary"
	"fmt"
	fellow_level_award "golang.52tt.com/protocol/services/fellow-level-award"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	publicNoticePb "golang.52tt.com/protocol/services/public-notice"
	timelinesvr "golang.52tt.com/protocol/services/timelinesvr"

	"github.com/globalsign/mgo"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/golang/gudetama/oss/datacenter"
	"golang.52tt.com/clients/account"
	pushclient "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/seqgen/v2"
	unifiedPay "golang.52tt.com/clients/unified_pay"
	"golang.52tt.com/pkg/deal_token"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	fellow_logic "golang.52tt.com/protocol/app/fellow-logic"
	imPB "golang.52tt.com/protocol/app/im"
	pushPb "golang.52tt.com/protocol/app/push"
	syncPB "golang.52tt.com/protocol/app/sync"
	"golang.52tt.com/protocol/app/ugc"
	"golang.52tt.com/protocol/common/status"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	pb "golang.52tt.com/protocol/services/fellow-svr"
	present_middleware "golang.52tt.com/protocol/services/present-middleware"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/protocol/services/ugc/friendship"
	unifyPayPB "golang.52tt.com/protocol/services/unified_pay"
	"golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/helper-from-cpp/immsghelper"
	"golang.52tt.com/services/notify"
	"golang.52tt.com/services/present-middleware/utils"
	"golang.52tt.com/services/tt-rev/fellow-svr/internal/store"
	"gopkg.in/mgo.v2/bson"
)

const FellowAppId = "TT_ZY"

func (s *Server) GetPendingInviteCount(ctx context.Context, uid uint32) (uint32, error) {
	resp, ok, err := s.store.GetPendingInviteCountCache(ctx, uid)

	if err != nil {
		log.ErrorWithCtx(ctx, "GetPendingInviteCount - GetPendingInviteCountCache fail ,uid: %d , err : %v", uid, err)
		return 0, err
	}

	if !ok {
		count, err := s.store.GetPendingInviteCountByTargetUid(uid, uint32(pb.InviteStatus_ENUM_INVITE_STATUS_INVITED))
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPendingInviteCount - GetFellowInviteInfoByTargetUid fail ,uid: %d , err : %v", uid, err)
			return 0, err
		}
		_ = s.store.SetPendingInviteCountCache(ctx, uid, count)
		return count, nil
	}

	return resp, nil
}

func (s *Server) SendInviteMsg(ctx context.Context, inviteUser *account.User, targetUser *account.User,
	inviteServerMsgId, targetServerMsgId uint64, inviteMsg *imPB.FellowInviteMsg) error {

	if inviteMsg.FromBindType != uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_UNKNOWN) {
		inviteMsg.FromFellowName = s.bc.GetUnbindFellowMapByType(inviteMsg.FromFellowType)
	}

	ext, mErr := proto.Marshal(inviteMsg)
	if mErr != nil {
		log.ErrorWithCtx(ctx, "SendInviteMsg - Marshal proto fail , msg: %v , err: %v", ext, mErr)
		return mErr
	}

	msg := &timelinesvr.ImMsg{
		FromId:        inviteUser.GetUid(),
		ToId:          targetUser.GetUid(),
		FromName:      inviteUser.GetUsername(),
		ToName:        targetUser.GetUsername(),
		FromNick:      inviteUser.GetNickname(),
		ToNick:        targetUser.GetNickname(),
		Content:       "",
		Type:          uint32(imPB.IM_MSG_TYPE_FELLOW_INVITE_MSG),
		ClientMsgTime: uint32(time.Now().Unix()),
		Status:        uint32(syncPB.NewMessageSync_UN_READ),
		ServerMsgTime: uint32(time.Now().Unix()),
		HasAttachment: false,
		MsgSourceType: uint32(imPB.MsgSourceType_MSG_SOURCE_FROM_FELLOW_INVITE),
		Platform:      uint32(timelinesvr.Platform_UNSPECIFIED), // 没有平台限制必须显式指定
		Ext:           ext,
	}

	msg.ServerMsgId = uint32(inviteServerMsgId)

	imErr := immsghelper.WriteMsgToUidWithId(ctx, inviteUser.GetUid(), msg, s.seqgenCli, s.timelineCli)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendInviteMsg - WriteMsgToUidWithId fail , msg: %v , err: %v", msg, imErr)
		return imErr
	}

	msg.ServerMsgId = uint32(targetServerMsgId)

	imErr = immsghelper.WriteMsgToUidWithId(ctx, targetUser.GetUid(), msg, s.seqgenCli, s.timelineCli)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendInviteMsg - WriteMsgToUidWithId fail , msg: %v , err: %v", msg, imErr)
		return imErr
	}

	imErr = notify.NotifySyncX(ctx, []uint32{inviteUser.GetUid(), targetUser.GetUid()}, notify.ImMsg)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendInviteMsg - NotifySyncX fail , msg: %v , err: %v", msg, imErr)
	}

	s.sendInviteMsgToDataCenter(inviteUser.GetUid(), targetUser.GetUid(), "")

	log.DebugWithCtx(ctx, "SendInviteMsg , inviteMsg: %v ", inviteMsg)

	return nil
}

func (s *Server) checkVersionAndSite(ctx context.Context, uid uint32, inviteResp store.FellowInviteInfo) error {
	if !s.checkNewFellowTypeAllowed(ctx, inviteResp.FellowType) {
		s.sendOldAndroidUpdateMsg(ctx, uid)
		return protocol.NewExactServerError(nil, status.ErrParam, "当前关系需要更新版本才可绑定～")
	}

	checkErr := s.handleFellowSiteCheck(ctx, inviteResp.TargetUid, inviteResp.InviteUid,
		inviteResp.BindType, inviteResp.FromBindType, inviteResp.WithUnlock, true)
	if checkErr != nil {
		if checkErr.Code() == status.ErrFellowInviteMaxSite {
			return protocol.NewExactServerError(nil, status.ErrFellowInviteMaxSite, "挚友坑位已满")
		}

		// 自己没位置且带了解锁，可以继续,其他都报错
		if !(checkErr.Code() == status.ErrFellowInviteNoSiteSelf && inviteResp.WithUnlock) {
			log.ErrorWithCtx(ctx, "checkVersionAndSite - fellowSiteCheck fail ,uid: %d , err : %v", uid, checkErr)
			return checkErr
		}
	}

	fellowInfo, err := s.store.GetFellowByIdPair(inviteResp.InviteUid, inviteResp.TargetUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkVersionAndSite - GetFellowByIdPair fail ,uid: %d , err : %v", uid, err)
		return err
	}

	if inviteResp.IsNewBind() {
		//非换绑关系  双方不能是挚友关系
		if fellowInfo.BindType != uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_UNKNOWN) {
			return protocol.NewExactServerError(nil, status.ErrFellowInviteExistingFellowBind)
		}
	} else {
		//换绑关系  双方不能是已经解绑的
		if fellowInfo.BindType == uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_UNKNOWN) {
			return protocol.NewExactServerError(nil, status.ErrFellowNotFellow)
		}
	}
	return nil
}

func (s *Server) handleAcceptPay(ctx context.Context, req *pb.HandleChannelFellowInviteReq, inviteResp store.FellowInviteInfo,
	inviteUser *accountPB.UserResp, presentConfig *userpresent.GetPresentConfigByIdResp, nowTime time.Time) error {
	// 检查坑位 版本限制
	// 拒绝的话不管
	uid := req.GetUid()
	serverInfo := req.GetServiceInfo()

	err := s.checkVersionAndSite(ctx, uid, inviteResp)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAcceptPay checkVersionAndSite uid:%d, err:%v", uid, err)
		return err
	}

	//新绑定先初始化挚友关系
	if inviteResp.IsNewBind() {
		_, err = s.store.AddFellowPoint(inviteResp.InviteUid, inviteResp.TargetUid, 0, "bind")
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - AddFellowPoint fail ,uid: %d , err : %v", uid, err)
			return err
		}
	}

	//提交支付订单
	ctime := ""
	dealToken := ""
	var sErr error
	var isPayFailed bool
	if uint32(unifyPayPB.FeeType_TBEAN) == presentConfig.GetItemConfig().PriceType {
		err := s.store.UpdateFellowTbeanOrderStatus(inviteResp.OutTradeNo, uint32(store.Status_Commited), ctime, "", nowTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - UpdateFellowTbeanOrderStatus fail ,OutTradeNo: %d , err : %v", inviteResp.OutTradeNo, err)
			return err
		}

		// 支付失败，仍然继续 这里交给订单扫描去处理
		ctime, dealToken, sErr = s.commitFellowOrder(ctx, inviteUser, inviteResp.OutTradeNo, presentConfig, serverInfo.GetClientType())
		if sErr != nil {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - commitFellowOrder fail ,OutTradeNo: %d , err : %v", inviteResp.OutTradeNo, sErr)
			isPayFailed = true
		}

		log.InfoWithCtx(ctx, "ctime is %s , deal_token is %s", ctime, dealToken)

		if sErr == nil {
			updateTime, err := time.ParseInLocation("2006-01-02 15:04:05", ctime, time.Local)
			if err != nil {
				updateTime = nowTime
			}
			err = s.store.UpdateFellowTbeanOrderStatus(inviteResp.OutTradeNo, uint32(store.Status_Finised), ctime, dealToken, updateTime)
			if err != nil {
				log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - UpdateFellowTbeanOrderStatus fail ,OutTradeNo: %d , err : %v", inviteResp.OutTradeNo, err)
			}
		}
	}

	// 提交解锁的订单
	if inviteResp.WithUnlock {
		err := s.CommitPayUnlock(ctx, inviteResp.UnlockOutTradeNo, nowTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - CommitPayUnlock fail ,uid: %d , err : %v", uid, err)
			return err
		}
	}

	// 赠送信物
	// 新版本没有deal_token会失败，交给补单工具处理
	if !isPayFailed {
		serverCtrlInfo := &present_middleware.ServiceCtrlInfo{
			ClientIp:      serverInfo.GetClientIp(),
			ClientType:    serverInfo.GetClientType(),
			ClientId:      serverInfo.GetClientId(),
			ClientPort:    serverInfo.GetClientPort(),
			ClientVersion: serverInfo.GetClientVersion(),
			DeviceId:      serverInfo.GetDeviceId(),
			TerminalType:  serverInfo.GetTerminalType(),
		}

		_, err = s.presentMiddleCli.FellowSendPresent(ctx, &present_middleware.FellowSendPresentReq{
			OrderId:     inviteResp.OutTradeNo,
			TargetUid:   inviteResp.TargetUid,
			ItemId:      inviteResp.PresentId,
			Count:       1,
			SendSource:  uint32(present_middleware.PresentSendSourceType_E_SEND_SOURCE_FELLOW),
			ItemSource:  uint32(present_middleware.PresentSourceType_PRESENT_SOURCE_FELLOW),
			SourceId:    inviteResp.PresentId,
			SendType:    uint32(present_middleware.PresentSendType_PRESENT_SEND_NORMAL),
			AppId:       req.GetAppId(),
			MarketId:    req.GetMarketId(),
			ServiceInfo: serverCtrlInfo,
			IsOptValid:  true,
			SendUid:     inviteResp.InviteUid,
			Ctime:       ctime,
			ChannelId:   inviteResp.ChannelId,
			DealToken:   dealToken,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - FellowSendPresent fail , invite: %s , err : %v", inviteResp.ID.Hex(), err)
			//return err
		}
	}

	// 写信物历史
	sErr = s.store.AddFellowPresentHistory(ctx, inviteResp.InviteUid, inviteResp.TargetUid, inviteResp.PresentId)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - AddFellowPresentHistory fail , invite: %s , err : %v", inviteResp.ID.Hex(), err)
		//return out, err
	}

	// t豆信物增加挚友值
	if presentConfig.GetItemConfig().PriceType == uint32(unifyPayPB.FeeType_TBEAN) {
		err := s.handleInitialFellowVal(ctx, inviteResp.InviteUid, inviteResp.TargetUid, presentConfig)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc fail to handleInitialFellowVal, uid: %d, targetid: %d, err: %v", inviteResp.InviteUid, inviteResp.TargetUid, err)
			return err
		}
	}

	err = s.handleFellowSiteCheck(ctx, inviteResp.TargetUid, inviteResp.InviteUid, inviteResp.BindType, inviteResp.FellowType, inviteResp.WithUnlock, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - handleFellowSiteCheck fail ,uid: %d , err : %v", uid, sErr)
		return err
	}

	//新绑定的绑定挚友
	if inviteResp.IsNewBind() {

		_, sErr = s.store.SetFellowBindType(ctx, inviteResp.InviteUid, inviteResp.TargetUid, inviteResp.BindType, inviteResp.FellowType)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - SetFellowBindType fail ,uid:%d,%d-%d,err:%v", uid, inviteResp.InviteUid, inviteResp.TargetUid, sErr)
			return sErr
		}
	} else {
		_, sErr = s.store.UpdateFellowNewBindType(ctx, inviteResp.InviteUid, inviteResp.TargetUid, inviteResp.BindType, inviteResp.FellowType)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - UpdateFellowNewBindType fail ,uid: %d , err : %v", uid, sErr)
			return sErr
		}

		s.store.IncrChangeCountCached(ctx, inviteResp.InviteUid, inviteResp.TargetUid)
	}

	// 如果为房内邀请,需要判断进行全服公告推送
	if inviteResp.IsChannelInvite() {
		s.pushFellowPresentBreakingNews(ctx, inviteResp.ChannelId, inviteResp.InviteUid, inviteResp.TargetUid, inviteResp.PresentId)
	}
	return nil
}

var (
	EnumFellowTaskSingleMsg          = 1
	EnumFellowTaskMic                = 2
	EnumFellowTaskMutualMsg          = 3
	EnumFellowTaskSendPresent        = 4
	EnumFellowTaskInteraction        = 5
	EnumFellowTaskSendPresentUnLimit = 6

	MIN_PRESENT_PRICE      = 100 //送礼低于100T豆  不加分
	MIN_PRESENT_TASK_PRICE = 10
)


// 接受挚友邀请时候, 保证初始化挚友值合法, 规则如下:
// 用户本身挚友值小于520时，赠送信物绑定挚友，当信物本身价格+当日首次收礼100T豆以上+10+当前挚友值>=520时就不额外加挚友值，如果当前挚友值+信物本身增加的挚友值小于520就直接变成520
func (s *Server) handleInitialFellowVal(ctx context.Context, uid, targetUid uint32, presentConfig *userpresent.GetPresentConfigByIdResp) error {
	nowFellowPoint, err := s.mgr.GetFellowPoint(ctx, uid, targetUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleInitialFellowVal GetFellowPoint fail ,uid: %d , err : %v", uid, err)
		return err
	}
	unlimitPresentCfg, ok := s.bc.GetFellowDailyTaskMap()[uint32(EnumFellowTaskSendPresentUnLimit)]
	if !ok {
		return fmt.Errorf("handleInitialFellowVal no cfg EnumFellowTaskSendPresentUnLimit")
	}

	send100TPresentCfg, ok := s.bc.GetFellowDailyTaskMap()[uint32(EnumFellowTaskSendPresent)]
	if !ok {
		return fmt.Errorf("handleInitialFellowVal no cfg EnumFellowTaskSendPresent")
	}

	// 计算这次礼物会加的分
	mustAddVal := float32(presentConfig.GetItemConfig().GetPrice()/10) * unlimitPresentCfg.AddPoint // 每10豆+0.1分
	// 接受邀请时候挚友礼物都大于100, 额外加10分,
	completeCnt, err := s.store.GetDailyTaskCnt(ctx, uid, targetUid, EnumFellowTaskSendPresent)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleInitialFellowVal fail to handleInitialFellowVal, uid: %d, targetid: %d", uid, targetUid)
		return err
	}
	if uint32(completeCnt) < send100TPresentCfg.MaxCnt {
		mustAddVal += send100TPresentCfg.AddPoint
	}

	log.InfoWithCtx(ctx, "handleInitialFellowVal uid: %d, targetid: %d, nowPoint: %d, mustAddVal: %f", uid, targetUid, nowFellowPoint, mustAddVal)

	if float32(nowFellowPoint) + mustAddVal >= 520 {  // 如果本身/加上当此礼物加分后大于520, 不用处理
		log.DebugWithCtx(ctx, "handleInitialFellowVal no need to add point ,uid: %d , point : %v", uid, nowFellowPoint)
		return nil
	}

	// 直接设置挚友值, 加分逻辑按原来的
	err = s.store.SetFellowPoint(uid, targetUid, 520 - uint32(mustAddVal))
	if err != nil {
		log.ErrorWithCtx(ctx, "handleInitialFellowVal fail to SetFellowPoint, uid: %d, targetid: %d, setVal: %d", uid, targetUid, 520 - uint32(mustAddVal))
		return err
	}

	return nil
}

func (s *Server) handleAfterAccept(ctx context.Context, req *pb.HandleChannelFellowInviteReq, inviteResp store.FellowInviteInfo,
	inviteUser, targetUser *accountPB.UserResp, presentConfig *userpresent.GetPresentConfigByIdResp, nowTime time.Time, finalStatus uint32) error {
	// 发送接受邀请的im/push
	uid := req.GetUid()
	inviteId := req.GetInviteId()

	err := s.SendHandleInviteMessage(ctx, inviteResp, inviteId, presentConfig.ItemConfig.IconUrl, finalStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - SendHandleInviteMessage fail ,uid: %d , err : %v", uid, err)
	}

	// 成为玩伴
	sErr := s.HandleFriendFollow(ctx, &inviteResp)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - HandleFriendFollow fail ,uid: %d , err : %v", uid, err)
	}

	// 推送关系变动
	s.FellowChangeEventPush(ctx, inviteUser, targetUser, inviteResp.BindType, inviteResp.FellowType)
	log.InfoWithCtx(ctx, "HandleFellowInviteFunc uid:%d, BindType:%d", uid, inviteResp.BindType)

	// 如果是唯一关系，拒绝用户其他唯一邀请
	// 如果是换绑关系，拒绝这两个用户的其他邀请函
	if inviteResp.BindType == uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_UNIQUE) || !inviteResp.IsNewBind() {
		uidList := make([]uint32, 0)
		uidList = append(uidList, inviteUser.GetUid(), targetUser.GetUid())
		for _, handleUid := range uidList {
			inviteList, err := s.store.GetAllFellowInviteInfoByUid(handleUid)
			if err != nil {
				log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - GetFellowInviteInfoByTargetUid fail ,uid: %d , err : %v", uid, err)
				return err
			}

			log.InfoWithCtx(ctx, "HandleFellowInviteFunc uid:%d, size:%d", handleUid, len(inviteList))

			wg := sync.WaitGroup{}
			for _, invite := range inviteList {
				log.InfoWithCtx(ctx, "HandleFellowInviteFunc uid:%d,  inviteId:%+v", uid, invite)
				if invite.ID == inviteResp.ID || !invite.IsUniqueInvite() {
					continue
				}

				wg.Add(1)
				// 小心套娃，确认一下拒绝不会走到这里
				go func() {
					defer wg.Done()
					_, err = s.HandleFellowInviteFunc(ctx, &pb.HandleChannelFellowInviteReq{
						Uid:            handleUid,
						InviteId:       invite.ID.Hex(),
						IsAcceptInvite: false,
						ChannelId:      invite.ChannelId,
						MarketId:       0,
						AppId:          0,
						ServiceInfo:    &pb.ServiceCtrlInfo{}},
						uint32(store.Handle_Type_Timeout))
					if err != nil {
						log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - Handle other invite fail , invite: %s , err : %v", invite.ID.Hex(), err)
					}
					log.InfoWithCtx(ctx, "HandleFellowInviteFunc uid:%d, reject:%s", uid, inviteId)

				}()
				// sleep 防止堵塞
				time.Sleep(50 * time.Millisecond)
				wg.Wait()
			}
		}
	}
	return nil
}

func (s *Server) handleRollbackPay(ctx context.Context, req *pb.HandleChannelFellowInviteReq, inviteResp store.FellowInviteInfo,
	presentConfig *userpresent.GetPresentConfigByIdResp, nowTime time.Time) (tbeanChange bool, currencyRemain int32, err error) {
	uid := req.GetUid()
	// rollback失败还可以靠回调解决，所以不用急着处理
	if presentConfig.GetItemConfig().GetPriceType() == uint32(unifyPayPB.FeeType_TBEAN) {
		err := s.RollbackPayPresent(ctx, inviteResp.OutTradeNo, nowTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - RollbackPayPresent fail ,uid: %d , err : %v", uid, err)
		}

		tbeanChange = true
	}

	if inviteResp.WithUnlock {
		err := s.RollbackPayUnlock(ctx, inviteResp.UnlockOutTradeNo, nowTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - RollbackPayUnlock fail ,uid: %d , err : %v", uid, err)
		}
		tbeanChange = true
	}

	// 红钻没有回滚，手动加吧
	if presentConfig.GetItemConfig().GetPriceType() == uint32(unifyPayPB.FeeType_RED_DIAMOND) {
		orderId := genCurrencyOrderId(inviteResp.InviteUid, inviteResp.TargetUid)
		err := s.currencyCli.AddUserCurrency(ctx, inviteResp.InviteUid, int32(presentConfig.GetItemConfig().GetPrice()), orderId, "亲密关系邀请被拒绝回滚红钻", 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - GetUserCurrency fail ,uid: %d , err : %v", uid, err)
			return tbeanChange, currencyRemain, err
		}
		_ = s.missionHelper.UpdateUserGrowTimeline(ctx, inviteResp.InviteUid)
		//6 对应 grow，sync.proto中定义
		_ = s.notifyClient(ctx, []uint32{inviteResp.InviteUid}, 6)
	}

	currencyRemain, err = s.currencyCli.GetUserCurrency(ctx, inviteResp.InviteUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - GetUserCurrency fail ,uid: %d , err : %v", uid, err)
		return tbeanChange, currencyRemain, err
	}

	return tbeanChange, currencyRemain, err
}

// HandleFellowInviteFunc 处理邀请
func (s *Server) HandleFellowInviteFunc(ctx context.Context, req *pb.HandleChannelFellowInviteReq, handleType uint32) (*pb.HandleFellowInviteResp, error) {
	out := &pb.HandleFellowInviteResp{}
	log.InfoWithCtx(ctx, "HandleFellowInviteFunc req:%+v， handleType:%d", req, handleType)

	timeCollect := utils.InitTimeCollect(true)
	uid := req.Uid
	inviteId := req.GetInviteId()

	if !bson.IsObjectIdHex(inviteId) {
		log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - invaild inviteId ,uid: %d ", uid)
		return out, protocol.NewExactServerError(nil, status.ErrFellowInviteNotExist)
	}

	defer func() {
		timeCollect.AddTimeEvent("end")
		timeCollect.ShowTimeCost(1000)
	}()

	// 获取邀请函状态
	inviteResp, sErr := s.store.GetFellowInviteInfoById(inviteId)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - GetFellowInviteInfoById fail ,uid: %d , err : %v", uid, sErr)
		return out, sErr
	}

	if sErr == mgo.ErrNotFound || inviteResp.InviteStatus != uint32(pb.InviteStatus_ENUM_INVITE_STATUS_INVITED) {
		return out, protocol.NewExactServerError(nil, status.ErrFellowInviteNotExist)
	}

	presentConfig, err := s.presentCli.GetPresentConfigById(ctx, inviteResp.PresentId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - GetPresentConfigById fail ,uid: %d , err : %v", uid, err)
		return out, err
	}

	timeCollect.AddTimeEvent("FellowCheck")

	tbeanChange := false
	currencyRemain := int32(0)
	nowTime := time.Now()

	bindLimit, err := s.store.GetBindFellowLimit(ctx, inviteResp.InviteUid, inviteResp.TargetUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - GetBindFellowLimit fail ,uid: %d , err : %v", uid, err)
		return out, err
	}
	if bindLimit {
		log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - GetBindFellowLimit fail ,uid: %d , err : %v", uid, err)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, bindLimitMsg)
	}

	usersMap, err := s.accountCli.GetUsersMap(ctx, []uint32{inviteResp.InviteUid, inviteResp.TargetUid})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendFellowInviteFunc - GetUser fail , uid: %d , err: %v", inviteResp.InviteUid, err)
		return out, err
	}

	inviteUser := usersMap[inviteResp.InviteUid]
	targetUser := usersMap[inviteResp.TargetUid]

	timeCollect.AddTimeEvent("GetUser")

	if inviteResp.IsUniqueInvite() && handleType == uint32(store.Handle_Type_Accept) {
		if !s.store.Lock(ctx, inviteResp.InviteUid, time.Second*5) {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc uid:%d get lock failed, invite:%d", uid, inviteResp.InviteUid)
			return out, protocol.NewExactServerError(nil, status.ErrStillWaitingResponse, "操作失败，请稍后重试")
		}
		if !s.store.Lock(ctx, inviteResp.TargetUid, time.Second*5) {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc uid:%d get lock failed,target:%d", uid, inviteResp.TargetUid)
			return out, protocol.NewExactServerError(nil, status.ErrStillWaitingResponse, "操作失败，请稍后重试")
		}
	}

	defer s.store.Unlock(ctx, inviteResp.InviteUid)
	defer s.store.Unlock(ctx, inviteResp.TargetUid)

	timeCollect.AddTimeEvent("UpdateFellowInviteInfo")

	if handleType == uint32(store.Handle_Type_Accept) {
		err = s.handleAcceptPay(ctx, req, inviteResp, inviteUser, presentConfig, nowTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleAcceptPay failed uid:%d, err:%v", uid, err)
			return out, err
		}
	} else {
		tbeanChange, currencyRemain, err = s.handleRollbackPay(ctx, req, inviteResp, presentConfig, nowTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleRollbackPay fail ,uid: %d , err : %v", uid, err)
			return out, err
		}
	}

	finalStatus := uint32(pb.InviteStatus_ENUM_INVITE_STATUS_FAILED)
	if handleType == uint32(store.Handle_Type_Timeout) {
		finalStatus = uint32(pb.InviteStatus_ENUM_INVITE_STATUS_TIMEOUT)
	} else if handleType == uint32(store.Handle_Type_Reject) {
		finalStatus = uint32(pb.InviteStatus_ENUM_INVITE_STATUS_FAILED)
	} else if handleType == uint32(store.Handle_Type_Accept) {
		finalStatus = uint32(pb.InviteStatus_ENUM_INVITE_STATUS_SUCCESS)
	}

	newInviteInfo, err := s.store.UpdateFellowInviteInfo(inviteId, finalStatus, nowTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - UpdateFellowInviteInfo fail ,uid: %d , err : %v", uid, err)
		return out, err
	}

	// 删除主态红点缓存
	s.store.DelFellowImInviteCountByInviteUidCache(ctx, inviteResp.InviteUid, uint32(pb.InviteStatus_ENUM_INVITE_STATUS_INVITED))
	// 删除相关cache
	s.store.DelCache(ctx, inviteResp.InviteUid, inviteResp.TargetUid)
	timeCollect.AddTimeEvent("DelCache")

	//房间邀请  发送房间广播
	if inviteResp.IsChannelInvite() {
		s.ChannelFellowEventPush(ctx, inviteUser, targetUser, inviteResp.BindType, inviteResp.FellowType, inviteResp.ChannelId, 0,
			presentConfig.GetItemConfig(), inviteId, nowTime, inviteResp.CreateTime, inviteResp.EndTime, inviteResp.WithUnlock, finalStatus)
	}

	if handleType == uint32(store.Handle_Type_Accept) {
		s.handleAfterAccept(ctx, req, inviteResp, inviteUser, targetUser, presentConfig, nowTime, finalStatus)
	} else if handleType == uint32(store.Handle_Type_Reject) {
		// 发送拒绝邀请的im
		if inviteResp.InviteType == uint32(store.Invite_Type_Normal) {
			err = s.SendHandleInviteMessage(ctx, inviteResp, inviteId, presentConfig.ItemConfig.IconUrl, finalStatus)
			if err != nil {
				log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - SendHandleInviteMessage fail ,uid: %d , err : %v", uid, err)
			}
			_ = s.PushRemainMsg(ctx, int64(currencyRemain), tbeanChange, inviteResp.InviteUid)
		}
	} else if handleType == uint32(store.Handle_Type_Timeout) {
		if inviteResp.InviteType == uint32(store.Invite_Type_Normal) {
			err = s.SendCancelInviteMessage(ctx, newInviteInfo, inviteId, presentConfig.ItemConfig.IconUrl, uint32(pb.InviteStatus_ENUM_INVITE_STATUS_TIMEOUT))
			if err != nil {
				log.ErrorWithCtx(ctx, "CancelInvite - SendHandleInviteMessage fail ,uid: %d , err : %v", uid, err)
			}
			_ = s.PushRemainMsg(ctx, int64(currencyRemain), tbeanChange, inviteResp.InviteUid)
		}
	}

	log.InfoWithCtx(ctx, "HandleFellowInviteFunc end %v", inviteResp)

	return out, nil
}

func (s *Server) pushFellowPresentBreakingNews(ctx context.Context, channelId, uid, toUid uint32, giftId uint32) error {
	if !s.bc.IsBreakingNewsGift(giftId) {
		return nil
	}
	userProfile, err := s.userProfileApiCli.BatchGetUserProfile(ctx, []uint32{uid, toUid})
	if err != nil {
		log.ErrorWithCtx(ctx, "pushFellowPresentBreakingNews.BatchGetUserProfile, channelId: %d, uid: %d, toUid: %d, err: %v", channelId, uid, toUid, err)
		return err
	}
	if userProfile[uid] == nil || userProfile[toUid] == nil {
		log.ErrorWithCtx(ctx, "pushFellowPresentBreakingNews.BatchGetUserProfile, channelId: %d, uid: %d, toUid: %d, err: not found userprofile", channelId, uid, toUid)
		return nil
	}

	presentCfg, tErr := s.presentCli.GetPresentConfigById(ctx, giftId)
	if tErr != nil {
		log.ErrorWithCtx(ctx, "pushFellowPresentBreakingNews.GetPresentConfigById, channelId: %d, uid: %d, toUid: %d, err: err", channelId, uid, toUid, tErr)
		return err
	}

	cSimpleInfo, err := s.channelCli.GetChannelSimpleInfo(ctx, uid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushFellowPresentBreakingNews.GetChannelSimpleInfo, channelId: %d, uid: %d, toUid: %d, err: %v", channelId, uid, toUid, err)
		return err
	}
	BreakingNewsBaseOpt := &publicNoticePb.CommBreakingNewsBaseOpt{
		TriggerType:      uint32(pushPb.CommBreakingNewsBaseOpt_FELLOW_PRESENT_SEND),
		RollingCount:     1,
		RollingTime:      10,
		AnnounceScope:    uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL),
		AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_TO_CHANNEL_CLICK),
		JumpType:         uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_ClICK_OUTSIDE),
		JumpPosition:     uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
	}

	newsPrefix := "情真意切！"
	oldNewsContent := fmt.Sprintf("在%s【%s】送出价值%d豆的%s！",
		cSimpleInfo.GetName(), cSimpleInfo.GetChannelViewId(), presentCfg.GetItemConfig().GetPrice(), presentCfg.GetItemConfig().GetName())

	breakingNews := &publicNoticePb.CommonBreakingNewsV3{
		FromUid: uid,
		FromUserInfo: &publicNoticePb.UserInfo{
			Account: userProfile[uid].Account,
			Nick:    userProfile[uid].Nickname,
		},
		TargetUid: toUid,
		TargetUserInfo: &publicNoticePb.UserInfo{
			Account: userProfile[toUid].Account,
			Nick:    userProfile[toUid].Nickname,
		},
		ChannelId: channelId,
		ChannelInfo: &publicNoticePb.ChannelInfo{
			ChannelDisplayid: cSimpleInfo.GetDisplayId(),
			ChannelBindid:    cSimpleInfo.GetBindId(),
			ChannelType:      cSimpleInfo.GetChannelType(),
			ChannelName:      cSimpleInfo.GetName(),
		},
		BreakingNewsBaseOpt: BreakingNewsBaseOpt,
		PresentNewsBaseOpt: &publicNoticePb.PresentBreakingNewsBaseOpt{
			GiftName:    presentCfg.GetItemConfig().GetName(),
			GiftId:      giftId,
			GiftCount:   1,
			GiftIconUrl: presentCfg.GetItemConfig().GetIconUrl(),
			GiftWorth:   presentCfg.GetItemConfig().GetPrice(),
		},
		IsOldDeal:      1,
		NewsPrefix:     newsPrefix,
		OldNewsContent: oldNewsContent,
	}

	_, pErr := s.publicNoticeCli.PushBreakingNews(ctx, &publicNoticePb.PushBreakingNewsReq{
		CommonBreakingNews: breakingNews,
	})
	if pErr != nil {
		log.ErrorWithCtx(ctx, "pushFellowPresentBreakingNews.PushBreakingNews, channelId: %d, uid: %d, toUid: %d, err: %v", channelId, uid, toUid, err)
	}

	log.InfoWithCtx(ctx, "pushFellowPresentBreakingNews.PushBreakingNews, channelId: %d, uid: %d, toUid: %d, news: %+v", channelId, uid, toUid, breakingNews)
	return nil
}

// 生成orderId
func genOrderId(uid uint32, targetUid uint32, presentId uint32) string {
	return fmt.Sprintf("fellow_%v_%v_%v_%v", uid, targetUid, presentId, time.Now().UnixNano())
}

// 生成解锁orderId
func genUnlockOrderId(uid uint32, targetUid uint32) string {
	return fmt.Sprintf("fellow_unlock_%v_%v_%v", uid, targetUid, time.Now().Unix())
}

// 生成orderId
func genCurrencyOrderId(uid uint32, targetUid uint32) string {
	return fmt.Sprintf("fellow_rollback_%v_%v_%v", uid, targetUid, time.Now().Unix())
}

// 获取唯一绑定关系人数
func (s *Server) GetUniqueBindCount(ctx context.Context, uid uint32) (uint32, error) {
	resp, ok, err := s.store.GetUniqueBindCountCache(ctx, uid)

	if err != nil {
		log.ErrorWithCtx(ctx, "GetUniqueBindCount - GetUniqueBindCountCache fail ,uid: %d , err : %v", uid, err)
		return 0, err
	}

	if !ok {
		count, err := s.store.GetBindFellowCnt(uid, uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_UNIQUE))
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUniqueBindCount - GetBindFellowCnt fail ,uid: %d , err : %v", uid, err)
			return 0, err
		}
		_ = s.store.AddUniqueBindCountCache(ctx, uid, count)
		return count, nil
	}

	return resp, nil
}

// 获取非唯一绑定关系人数
func (s *Server) GetMultiBindCount(ctx context.Context, uid uint32) (uint32, error) {
	resp, ok, err := s.store.GetMultiBindCountCache(ctx, uid)

	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiBindCount - GetMultiBindCountCache fail ,uid: %d , err : %v", uid, err)
		return 0, err
	}

	if !ok {
		count, err := s.store.GetBindFellowCnt(uid, uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_MULTI))
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMultiBindCount - GetBindFellowCnt fail ,uid: %d , err : %v", uid, err)
			return 0, err
		}
		_ = s.store.AddMultiBindCountCache(ctx, uid, count)
		return count, nil
	}

	return resp, nil
}

// 获取非唯一绑定关系人数
func (s *Server) GetMultiBindCountNoCache(ctx context.Context, uid uint32) (uint32, error) {

	count, err := s.store.GetBindFellowCnt(uid, uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_MULTI))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiBindCount - GetBindFellowCnt fail ,uid: %d , err : %v", uid, err)
		return 0, err
	}

	_ = s.store.AddMultiBindCountCache(ctx, uid, count)
	return count, nil
}

// 获取非唯一绑定坑位数
func (s *Server) GetMultiBindSiteCount(ctx context.Context, uid uint32) (unBindSite, unlockPrice, unlockLevel uint32, err error) {

	levelSite, site, cacheOk, err := s.store.GetFellowSiteFromCache(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiBindCount - GetMultiBindCountCache fail ,uid: %d , err : %v", uid, err)
		return unBindSite, unlockPrice, unlockLevel, err
	}

	if !cacheOk {
		levelSite, site, err = s.store.GetFellowSite(uid)
		if err != nil && err != mgo.ErrNotFound {
			log.ErrorWithCtx(ctx, "GetMultiBindCount - GetFellowSite fail ,uid: %d , err : %v", uid, err)
			return unBindSite, unlockPrice, unlockLevel, err
		}

		if err == mgo.ErrNotFound {
			levelSite, site = 0, 0
		}
	}

	_, level, err := s.expCli.GetUserExp(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiBindCount - GetUserExp fail ,uid: %d , err : %v", uid, err)
		return unBindSite, unlockPrice, unlockLevel, err
	}

	// 这里有个关于等级解锁坑位的计算：
	// 如果存储中保存的等级坑位数小于目前应有的等级坑位数（(level-15)/5,即20级后每5级解锁一个，50级封顶），以目前为准；
	// 如果大于，以存储中保存的为准，但等级解锁坑位最多为7个
	levelSite = max(levelSite, getSiteByLevel(level))
	unlockPrice = 990

	if levelSite >= 7 {
		levelSite = 7
		unlockPrice = 990
	}

	// 加个缓存
	if !cacheOk {
		_ = s.store.AddFellowSiteToCache(ctx, uid, levelSite, site)
	}

	return levelSite + site, unlockPrice, getLevelBySite(levelSite), nil
}

func getSiteByLevel(level uint32) uint32 {
	if level < 15 {
		return 0
	} else if level >= 50 {
		return 7
	}
	return (level - 15) / 5
}

func getLevelBySite(site uint32) uint32 {
	level := site*5 + 20
	if level > 50 {
		return 0
	}
	return level
}

// 邀请函附带的解锁坑位
func (s *Server) UnlockBindSiteByInvite(ctx context.Context, uid uint32) (err error) {

	levelSite, _, err := s.store.GetFellowSite(uid)
	if err != nil && err != mgo.ErrNotFound {
		log.ErrorWithCtx(ctx, "UnlockBindSite - GetFellowSite fail ,uid: %d , err : %v", uid, err)
		return err
	}

	_, level, err := s.expCli.GetUserExp(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "UnlockBindSite - GetUserExp fail ,uid: %d , err : %v", uid, err)
		return err
	}

	nowLevelSite := getSiteByLevel(level)
	trueLevelSit := max(nowLevelSite, levelSite)

	// 如小于50级且已解锁的levelSite小于7 直接解锁下一个levelSite
	// 否则，解锁非level的site

	if trueLevelSit < 7 && level < 50 {
		err = s.store.UnlockLevelFellowSite(uid, trueLevelSit+1)
		if err != nil {
			log.ErrorWithCtx(ctx, "UnlockBindSite - UnlockLevelFellowSite fail ,uid: %d , err : %v", uid, err)
			return err
		}
	} else {
		err = s.store.UnlockFellowSite(uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "UnlockBindSite - UnlockFellowSite fail ,uid: %d , err : %v", uid, err)
			return err
		}
	}

	_ = s.store.DelFellowSiteToCache(ctx, uid)

	return nil
}

func (s *Server) FreezePayUnlock(ctx context.Context, uid, targetUid uint32) (OutTradeNo string, remain int64, err error) {
	OutTradeNo = genUnlockOrderId(uid, targetUid)
	nowTime := time.Now()

	err = s.store.AddFellowTbeanOrder(&store.FellowTbeanOrder{
		OutTradeNo:  OutTradeNo,
		FromUid:     uid,
		ToUid:       targetUid,
		ConsumeType: uint32(store.Consume_Unlock),
		TotalPrice:  990,
		Status:      uint32(store.Status_Inited),
		CreateTime:  nowTime,
		UpdateTime:  nowTime,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "payForUnlock - AddFellowTbeanOrder fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return OutTradeNo, remain, err
	}

	balance, err := s.payCli.PresetFreeze(ctx, uid, 990, "TT_ZYW", OutTradeNo, nowTime.Format("2006-01-02 15:04:05"), "申请挚友关系时为他人解锁挚友位")
	if err != nil {
		log.ErrorWithCtx(ctx, "payForUnlock - DirectPay fail ,uid: %d , err : %v", uid, err)
		return OutTradeNo, remain, err
	}

	err = s.store.UpdateFellowTbeanOrderStatus(OutTradeNo, uint32(store.Status_Freezed), "", "", nowTime)

	if err != nil {
		log.ErrorWithCtx(ctx, "payForUnlock - UpdateFellowTbeanOrderStatus fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return OutTradeNo, remain, err
	}

	log.InfoWithCtx(ctx, "payForUnlock uid:%d, targetUid:%d", uid, targetUid)
	return OutTradeNo, int64(balance), err
}

func (s *Server) DirectPayUnlock(ctx context.Context, uid uint32, isLevel bool) (outTradeNo string, remain int64, err error) {
	nowTime := time.Now()
	outTradeNo = genUnlockOrderId(uid, uid)
	cost := uint32(990)

	err = s.store.AddFellowTbeanOrder(&store.FellowTbeanOrder{
		OutTradeNo:  outTradeNo,
		FromUid:     uid,
		ToUid:       uid,
		ConsumeType: uint32(store.Consume_Unlock),
		TotalPrice:  cost,
		Status:      uint32(store.Status_Inited),
		CreateTime:  nowTime,
		UpdateTime:  nowTime,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "DirectPayUnlock - AddFellowTbeanOrder fail ,OutTradeNo: %s , err : %v", outTradeNo, err)
		return outTradeNo, remain, err
	}

	balance, err := s.payCli.PresetFreeze(ctx, uid, 990, "TT_ZYW", outTradeNo, nowTime.Format("2006-01-02 15:04:05"), "解锁挚友位")
	if err != nil {
		log.ErrorWithCtx(ctx, "DirectPayUnlock - DirectPay fail ,uid: %d , err : %v", uid, err)
		return outTradeNo, remain, err
	}

	remain = int64(balance)

	err = s.store.UpdateFellowTbeanOrderStatus(outTradeNo, uint32(store.Status_Freezed), "", "", nowTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "DirectPayUnlock - UpdateFellowTbeanOrderStatus fail ,OutTradeNo: %s , err : %v", outTradeNo, err)
		return outTradeNo, remain, err
	}

	// 这里不要读订单信息，可能主从不一致
	err = s.DirectCommitPayUnlock(ctx, outTradeNo, uid, nowTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "DirectPayUnlock - FreezePayUnlock fail ,OutTradeNo: %s , err : %v", outTradeNo, err)
		return outTradeNo, remain, err
	}

	return outTradeNo, remain, err
}

func (s *Server) CommitPayUnlock(ctx context.Context, OutTradeNo string, nowTime time.Time) (err error) {
	resp, exist, err := s.store.GetFellowTbeanOrderByOutTradeNo(OutTradeNo)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommitPayUnlock - GetFellowTbeanOrderByOutTradeNo fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return err
	}

	if !exist {
		log.ErrorWithCtx(ctx, "CommitPayUnlock - GetFellowTbeanOrderByOutTradeNo fail ,OutTradeNo: %s , err : no order", OutTradeNo)
		return protocol.NewExactServerError(nil, status.ErrFellowInviteNotExist, "解锁订单不存在")
	}

	if resp.Status == uint32(store.Status_Finised) {
		log.InfoWithCtx(ctx, "CommitPayUnlock - order has been commited ,OutTradeNo: %s", OutTradeNo)
		return nil
	}

	user, err := s.accountCli.GetUser(ctx, resp.FromUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommitPayUnlock - GetUser fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return err
	}

	err = s.store.UpdateFellowTbeanOrderStatus(resp.OutTradeNo, uint32(store.Status_Commited), "", "", nowTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommitPayUnlock - UpdateFellowTbeanOrderStatus fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return err
	}

	ctime, dealToken, err := s.payCli.UnfreezeAndConsume(ctx, &unifyPayPB.UnfreezeAndConsumeReq{
		AppId:        "TT_ZYW",
		Uid:          user.Uid,
		UserName:     user.Username,
		ItemId:       100000,
		ItemName:     "解锁挚友位",
		ItemNum:      1,
		ItemPrice:    990,
		TotalPrice:   990,
		Platform:     "0",
		OutTradeNo:   OutTradeNo,
		Notes:        "FellowSvr::CommitPayUnlock",
		OutOrderTime: time.Now().Format("2006-01-02 15:04:05"),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CommitPayUnlock - Commit fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return err
	}

	err = s.store.UpdateFellowTbeanOrderStatus(resp.OutTradeNo, uint32(store.Status_Finised), ctime, dealToken, nowTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommitPayUnlock - UpdateFellowTbeanOrderStatus fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return err
	}

	err = s.UnlockBindSiteByInvite(ctx, resp.ToUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommitPayUnlock - UnlockBindSiteByInvite fail ,OutTradeNo: %d , err : %v", OutTradeNo, err)
		return err
	}

	return err
}

func (s *Server) DirectCommitPayUnlock(ctx context.Context, OutTradeNo string, fromUid uint32, nowTime time.Time) (err error) {

	user, err := s.accountCli.GetUser(ctx, fromUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommitPayUnlock - GetUser fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return err
	}

	err = s.store.UpdateFellowTbeanOrderStatus(OutTradeNo, uint32(store.Status_Commited), "", "", nowTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommitPayUnlock - UpdateFellowTbeanOrderStatus fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return err
	}

	ctime, dealToken, err := s.payCli.UnfreezeAndConsume(ctx, &unifyPayPB.UnfreezeAndConsumeReq{
		AppId:        "TT_ZYW",
		Uid:          user.Uid,
		UserName:     user.Username,
		ItemId:       100000,
		ItemName:     "解锁挚友位",
		ItemNum:      1,
		ItemPrice:    990,
		TotalPrice:   990,
		Platform:     "0",
		OutTradeNo:   OutTradeNo,
		Notes:        "FellowSvr::CommitPayUnlock",
		OutOrderTime: time.Now().Format("2006-01-02 15:04:05"),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CommitPayUnlock - Commit fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return err
	}

	err = s.store.UpdateFellowTbeanOrderStatus(OutTradeNo, uint32(store.Status_Finised), ctime, dealToken, nowTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommitPayUnlock - UpdateFellowTbeanOrderStatus fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return err
	}

	err = s.UnlockBindSiteByInvite(ctx, fromUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommitPayUnlock - UnlockBindSiteByInvite fail ,OutTradeNo: %d , err : %v", OutTradeNo, err)
		return err
	}

	return err
}

func (s *Server) RollbackPayUnlock(ctx context.Context, OutTradeNo string, nowTime time.Time) (err error) {
	resp, exist, err := s.store.GetFellowTbeanOrderByOutTradeNo(OutTradeNo)
	if err != nil {
		log.ErrorWithCtx(ctx, "RollbackPayUnlock - RollbackPayUnlock fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return err
	}

	if !exist {
		log.ErrorWithCtx(ctx, "CommitPayUnlock - GetFellowTbeanOrderByOutTradeNo fail ,OutTradeNo: %s , err : no order", OutTradeNo)
		return err
	}

	err = s.payCli.UnFreezeAndRefund(ctx, resp.FromUid, "TT_ZYW", OutTradeNo)
	if err != nil {
		log.ErrorWithCtx(ctx, "RollbackPayUnlock - Rollback fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return err
	}

	err = s.store.UpdateFellowTbeanOrderStatus(resp.OutTradeNo, uint32(store.Status_Rollbacked), "", "", nowTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "RollbackPayUnlock - UpdateFellowTbeanOrderStatus fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return err
	}

	return err
}

func (s *Server) FreezePayPresent(ctx context.Context, inviteUser, targetUser *account.User,
	presentConfig *userpresent.GetPresentConfigByIdResp, outTime time.Time) (OutTradeNo, orderId string, remain int64, err error) {

	OutTradeNo = genOrderId(inviteUser.GetUid(), targetUser.GetUid(), presentConfig.GetItemConfig().GetItemId())

	if presentConfig.GetItemConfig().GetPriceType() == uint32(unifyPayPB.FeeType_TBEAN) {
		// 先初始化一份订单进去
		err = s.store.AddFellowTbeanOrder(&store.FellowTbeanOrder{
			OutTradeNo:  OutTradeNo,
			FromUid:     inviteUser.GetUid(),
			ToUid:       targetUser.GetUid(),
			ConsumeType: uint32(store.Consume_Present),
			TotalPrice:  presentConfig.GetItemConfig().GetPrice(),
			Status:      uint32(store.Status_Inited),
			GiftId:      presentConfig.GetItemConfig().GetItemId(),
			CreateTime:  outTime,
			UpdateTime:  outTime,
		})

		if err != nil {
			log.ErrorWithCtx(ctx, "payForUnlock - AddFellowTbeanOrder fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
			return OutTradeNo, orderId, remain, err
		}

		// 新的流程不用unified-pay的order_id和token
		reason := fmt.Sprintf("使用信物「%s」申请挚友关系", presentConfig.GetItemConfig().GetName())
		resp, err := s.payCli.PresetFreeze(ctx, inviteUser.GetUid(), presentConfig.ItemConfig.Price, FellowAppId, OutTradeNo, outTime.Format("2006-01-02 15:04:05"), reason)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendFellowInviteFunc - Freeze fail , OutTradeNo: %d , err: %v", OutTradeNo, err)
			return OutTradeNo, "", remain, err
		}
		remain = int64(resp)
	} else {
		balance, orderId, _, err := s.payCli.Freeze(ctx, &unifiedPay.PayOrder{
			AppId:      FellowAppId,
			OutTradeNo: OutTradeNo,
			Uid:        inviteUser.GetUid(),
			UserName:   inviteUser.GetUsername(),
			FeeType:    unifyPayPB.FeeType(presentConfig.ItemConfig.PriceType),
			TotalFee:   presentConfig.ItemConfig.Price,
			Body:       "邀请信物",
			ItemId:     strconv.Itoa(int(presentConfig.GetItemConfig().GetItemId())),
			ClientInfo: &unifyPayPB.ClientInfo{},
		})
		remain = balance
		if err != nil {
			log.ErrorWithCtx(ctx, "FreezePayPresent - Freeze fail , OutTradeNo: %d , err: %v", OutTradeNo, err)
			return OutTradeNo, orderId, remain, err
		}
	}

	if presentConfig.GetItemConfig().PriceType == uint32(unifyPayPB.FeeType_TBEAN) {
		err = s.store.UpdateFellowTbeanOrderStatus(OutTradeNo, uint32(store.Status_Freezed), "", "", outTime)

		if err != nil {
			log.ErrorWithCtx(ctx, "FreezePayPresent - UpdateFellowTbeanOrderStatus fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
			return OutTradeNo, orderId, remain, err
		}
	}

	return OutTradeNo, orderId, remain, err
}

func (s *Server) RollbackPayPresent(ctx context.Context, OutTradeNo string, nowTime time.Time) (err error) {
	resp, exist, err := s.store.GetFellowTbeanOrderByOutTradeNo(OutTradeNo)
	if err != nil {
		log.ErrorWithCtx(ctx, "RollbackPayUnlock - RollbackPayUnlock fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return err
	}

	if !exist {
		log.ErrorWithCtx(ctx, "CommitPayUnlock - GetFellowTbeanOrderByOutTradeNo fail ,OutTradeNo: %s , err : no order", OutTradeNo)
		return err
	}

	err = s.payCli.UnFreezeAndRefund(ctx, resp.FromUid, FellowAppId, OutTradeNo)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - Rollback payorder fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return err
	}

	err = s.store.UpdateFellowTbeanOrderStatus(resp.OutTradeNo, uint32(store.Status_Rollbacked), "", "", nowTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "RollbackPayUnlock - UpdateFellowTbeanOrderStatus fail ,OutTradeNo: %s , err : %v", OutTradeNo, err)
		return err
	}

	return err
}

func (s *Server) PushRemainMsg(ctx context.Context, currency int64, tbeanChange bool, targetUid uint32) error {
	balanceMsg := pushPb.BalanceChangeMessage{
		Reason:        uint32(pushPb.BalanceChangeMessage_FELLOW_REFUND),
		Currency:      currency,
		IsTbeanChange: tbeanChange,
	}

	balanceMsgContent, _ := proto.Marshal(&balanceMsg)
	pushMessage := pushPb.PushMessage{Cmd: uint32(pushPb.PushMessage_BALANCE_CHANGE), Content: balanceMsgContent}

	pushMessageContent, _ := proto.Marshal(&pushMessage)
	notification := &pushPB.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		AppId:              uint32(protocol.TT),
		TerminalTypePolicy: pushclient.DefaultPolicy,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageContent,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 86400,
		},
	}

	err := s.pushCli.PushToUsers(ctx, []uint32{targetUid}, notification)
	log.InfoWithCtx(ctx, "procPushEvent -- targetUids. uid:%v , content:%v , msg:%v ", targetUid, notification, balanceMsg)

	return err
}

func (s *Server) fellowCandidateCheck(ctx context.Context, uid, targetUid, bindType uint32, blackMap map[uint32]uint32) (bool, error) {
	// 如果在黑名单里，不显示
	if _, ok := blackMap[targetUid]; ok {
		return false, nil
	}
	return true, nil
}

func (s *Server) fillCandidateListResp(userMap map[uint32]*account.User, bindType uint32, inviteMap map[uint32]*store.FellowInviteInfo, out *[]*pb.FellowInviteUser, fellowMap map[uint32]*store.FellowInfo) error {
	for _, user := range userMap {
		// 如果是申请中，填申请中的状态
		status, point := uint32(0), uint32(0)
		inviteId := ""
		if _, ok := inviteMap[user.Uid]; ok {
			status = inviteMap[user.Uid].InviteStatus
			inviteId = inviteMap[user.Uid].ID.Hex()
			// 有申请，但跟请求不是一类，不显示
			if inviteMap[user.Uid].BindType != bindType {
				continue
			}
		}
		if val, ok := fellowMap[user.Uid]; ok {
			point = val.Point
		}

		*out = append(*out, &pb.FellowInviteUser{
			Uid:          int64(user.Uid),
			Account:      user.Username,
			NickName:     user.Nickname,
			FellowPoint:  point,
			InviteStatus: status,
			Sex:          uint32(user.Sex),
			InviteId:     inviteId,
		})
	}
	return nil
}

// 处理时坑位检查
func (s *Server) handleFellowSiteCheck(ctx context.Context, uid, targetUid, bindType, fromBindType uint32, withUnlock, isAccept bool) (err protocol.ServerError) {
	//从非唯一到非唯一 换绑  坑位都够
	if bindType == uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_MULTI) &&
		fromBindType == uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_MULTI) {
		return nil
	}

	//检查唯一一个坑位
	if bindType == uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_UNIQUE) {
		res, err := s.GetUniqueBindCount(ctx, uid)
		if err != nil {
			return protocol.ToServerError(err)
		}
		if res >= 1 {
			return protocol.NewExactServerError(nil, status.ErrFellowInviteNoSiteSelf)
		}
		res, err = s.GetUniqueBindCount(ctx, targetUid)
		if err != nil {
			return protocol.ToServerError(err)
		}
		if res >= 1 {
			return protocol.NewExactServerError(nil, status.ErrFellowInviteNoSiteTarget)
		}
	} else if bindType == uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_MULTI) {
		res, err := s.CheckUnbindFellowSite(ctx, uid, isAccept && withUnlock)
		if err != nil {
			return protocol.ToServerError(err)
		}
		if !res {
			return protocol.NewExactServerError(nil, status.ErrFellowInviteNoSiteSelf)
		}

		res, err = s.CheckUnbindFellowSite(ctx, targetUid, !isAccept && withUnlock)
		if err != nil {
			return protocol.ToServerError(err)
		}
		if !res {
			return protocol.NewExactServerError(nil, status.ErrFellowInviteNoSiteTarget)
		}
	}

	return err
}

func (s *Server) notifyClient(ctx context.Context, uidList []uint32, syncType uint32) (err error) {

	var b [4]byte
	binary.BigEndian.PutUint32(b[:], uint32(syncType)) // Network Order
	sequence := uint32(time.Now().UnixNano())
	seq := atomic.AddUint32(&sequence, 1)
	err = s.pushCli.PushToUsers(ctx, uidList, &pushPB.CompositiveNotification{
		Sequence:           seq,
		TerminalTypePolicy: pushclient.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:    uint32(pushPB.ProxyNotification_NOTIFY),
			Payload: b[:],
		},
	})

	if err != nil {
		log.DebugWithCtx(ctx, "NotifySyncX - users=%v(%d) type=%v seq=%d err=%s", uidList, len(uidList), syncType, seq, err.Error())
	} else {
		log.DebugWithCtx(ctx, "NotifySyncX - users=%v(%d) type=%v seq=%d", uidList, len(uidList), syncType, seq)
	}

	return err

}

func (s *Server) SendHandleInviteMessage(ctx context.Context, inviteResp store.FellowInviteInfo, inviteId string, icon string, status uint32) error {
	userMap, err := s.accountCli.GetUsersMap(ctx, []uint32{inviteResp.InviteUid, inviteResp.TargetUid})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendFellowInviteFunc - GetUser fail , uid: %d , err: %v", inviteResp.InviteUid, err)
		return err
	}

	inviteUser := userMap[inviteResp.InviteUid]
	targetUser := userMap[inviteResp.TargetUid]
	inviteSvrMsgID, err := s.seqgenCli.GenerateSequence(ctx, inviteUser.GetUid(), seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to generate svr msg id: %v", err)
		return err
	}

	targetSvrMsgID, err := s.seqgenCli.GenerateSequence(ctx, targetUser.GetUid(), seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to generate svr msg id: %v", err)
		return err
	}

	inviteMsg := &imPB.FellowInviteMsg{
		InviteId:       inviteId,
		InviteStatus:   status,
		PresentId:      inviteResp.PresentId,
		IsUnlock:       inviteResp.WithUnlock,
		FellowType:     inviteResp.FellowType,
		FellowBindType: inviteResp.BindType,
		FellowName:     s.bc.GetUnbindFellowMapByType(inviteResp.FellowType),
		PresentUrl:     icon,
		FromFellowType: inviteResp.FromFellowType,
		FromBindType:   inviteResp.FromBindType,
	}

	_ = s.SendInviteMsg(ctx, targetUser, inviteUser, targetSvrMsgID, inviteSvrMsgID, inviteMsg)
	return nil
}

func (s *Server) SendCancelInviteMessage(ctx context.Context, inviteResp store.FellowInviteInfo, inviteId string, icon string, status uint32) error {
	inviteUser, err := s.accountCli.GetUser(ctx, inviteResp.InviteUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendFellowInviteFunc - GetUser fail , uid: %d , err: %v", inviteResp.InviteUid, err)
		return err
	}

	targetUser, err := s.accountCli.GetUser(ctx, inviteResp.TargetUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendFellowInviteFunc - GetUser fail , targetId: %d , err: %v", inviteResp.TargetUid, err)
		return err
	}

	inviteMsg := &imPB.FellowInviteMsg{
		InviteId:       inviteId,
		InviteStatus:   status,
		PresentId:      inviteResp.PresentId,
		IsUnlock:       inviteResp.WithUnlock,
		FellowType:     inviteResp.FellowType,
		FellowBindType: inviteResp.BindType,
		FellowName:     s.bc.GetUnbindFellowMapByType(inviteResp.FellowType),
		PresentUrl:     icon,
		FromFellowType: inviteResp.FromFellowType,
		FromBindType:   inviteResp.FromBindType,
	}

	_ = s.SendInviteMsg(ctx, inviteUser, targetUser, inviteResp.InviteServerMsgId, inviteResp.TargetServerMsgId, inviteMsg)
	return nil
}

func (s *Server) HandleFriendFollow(ctx context.Context, inviteResp *store.FellowInviteInfo) error {
	// 成为玩伴
	atob, btoa, err := s.friendshipClient.GetBiFollowing(ctx, inviteResp.InviteUid, inviteResp.TargetUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - GetBiFollowing fail , invite: %s , err : %v", inviteResp.ID.Hex(), err)
		return err
	}
	if atob == nil || atob.Dropped {
		log.InfoWithCtx(ctx, "HandleFellowInviteFunc - FollowUser atob , Uid: %d , FollowingUid : %d", inviteResp.InviteUid, inviteResp.TargetUid)
		seq, err := s.seqgenCli.GenerateSequence(ctx, inviteResp.InviteUid, seqgen.NamespaceUser, seqgen.KeyUgc, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - GenerateSequence fail , invite: %s , err : %v", inviteResp.ID.Hex(), err)
			return err
		}
		_, _, err = s.friendshipClient.FollowUser(ctx, &friendship.FollowUserReq{
			Uid:                inviteResp.InviteUid,
			FollowingUid:       inviteResp.TargetUid,
			SequenceId:         seq,
			Source:             friendship.Source_USER_OPERATE,
			ClientSource:       uint32(ugc.FriendshipOperationReq_SOURCE_46), // 挚友关系自动关注
			ClientCustomSource: "",
			IsRobot:            false,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - FollowUser fail , invite: %s , err : %v", inviteResp.ID.Hex(), err)
			return err
		}
	}
	if btoa == nil || btoa.Dropped {
		log.InfoWithCtx(ctx, "HandleFellowInviteFunc - FollowUser btoa , Uid: %d , FollowingUid : %d", inviteResp.TargetUid, inviteResp.InviteUid)
		seq, err := s.seqgenCli.GenerateSequence(ctx, inviteResp.TargetUid, seqgen.NamespaceUser, seqgen.KeyUgc, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - GenerateSequence fail , invite: %s , err : %v", inviteResp.ID.Hex(), err)
			return err
		}
		_, _, err = s.friendshipClient.FollowUser(ctx, &friendship.FollowUserReq{
			Uid:                inviteResp.TargetUid,
			FollowingUid:       inviteResp.InviteUid,
			SequenceId:         seq,
			Source:             friendship.Source_USER_OPERATE,
			ClientSource:       uint32(ugc.FriendshipOperationReq_SOURCE_46), // 挚友关系自动关注
			ClientCustomSource: "",
			IsRobot:            false,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleFellowInviteFunc - FollowUser fail , invite: %s , err : %v", inviteResp.ID.Hex(), err)
			return err
		}
	}
	return nil
}

func (s *Server) CheckUnbindFellowSite(ctx context.Context, uid uint32, withUnlock bool) (bool, error) {
	count, err := s.GetMultiBindCountNoCache(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckFellowSite - GetMultiBindCount fail , uid: %s , err : %v", uid, err)
		return false, err
	}

	site, _, _, err := s.GetMultiBindSiteCount(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckFellowSite - GetMultiBindSiteCount fail , uid: %s , err : %v", uid, err)
		return false, err
	}

	// 如果带解锁坑位，且坑位大于等于100，就到上限了，不应该再给解锁
	if withUnlock && site+MultiBoundDefault >= s.bc.GetMaxSiteCount() {
		return false, protocol.NewExactServerError(nil, status.ErrFellowInviteMaxSite)
	}

	log.DebugWithCtx(ctx, "CheckUnbindFellowSite uid , count , site %d %d %d", uid, count, site)

	if site+MultiBoundDefault > count {
		return true, nil
	}

	return false, nil
}

func (s *Server) fillFellowPresentConfig(item *userpresent.StPresentItemConfig) *pb.FellowPresentInfo {
	if item == nil {
		return &pb.FellowPresentInfo{}
	}

	it := &pb.FellowPresentInfo{
		ItemId:    item.ItemId,
		Name:      item.Name,
		PriceType: item.PriceType,
		Icon:      item.IconUrl,
		Value:     item.Price,
	}

	if present, ok := s.mgr.GetPresent(item.ItemId); ok {
		it.MultiBackground = &pb.FellowBackground{
			BackgroundUrl: present.MultiBackground,
			Md5:           present.MultiMd5,
			SourceType:    present.MultiSourceType,
		}

		it.UniqueBackground = &pb.FellowBackground{
			BackgroundUrl: present.UniqueBackground,
			Md5:           present.UniqueMd5,
			SourceType:    present.UniqueSourceType,
		}

		if it.UniqueBackground.SourceType == 2 {
			it.MultiBackground.BackgroundImg = present.MultiBackgroundImg
			it.UniqueBackground.BackgroundImg = present.UniqueBackgroundImg
		}
	}
	return it
}

func (s *Server) fillFellowPresentConfigLogic(item *userpresent.StPresentItemConfig) *fellow_logic.FellowPresentInfo {
	it := &fellow_logic.FellowPresentInfo{
		ItemId:    item.ItemId,
		Name:      item.Name,
		PriceType: item.PriceType,
		Icon:      item.IconUrl,
		Value:     item.Price,
	}
	if present, ok := s.mgr.GetPresent(item.ItemId); ok {
		it.MultiBackground = &fellow_logic.FellowBackground{
			BackgroundUrl: present.MultiBackground,
			Md5:           present.MultiMd5,
			SourceType:    present.MultiSourceType,
		}

		it.UniqueBackground = &fellow_logic.FellowBackground{
			BackgroundUrl: present.UniqueBackground,
			Md5:           present.UniqueMd5,
			SourceType:    present.UniqueSourceType,
		}

		if it.UniqueBackground.SourceType == 2 {
			it.MultiBackground.BackgroundImg = present.MultiBackgroundImg
			it.UniqueBackground.BackgroundImg = present.UniqueBackgroundImg
		}
	}
	return it
}

func (s *Server) commitFellowOrder(ctx context.Context, user *accountPB.UserResp, orderId string, present *userpresent.GetPresentConfigByIdResp, clientType uint32) (ctime, dealToken string, err error) {

	ctime, dealToken, err = s.payCli.UnfreezeAndConsume(ctx, &unifyPayPB.UnfreezeAndConsumeReq{
		AppId:        "TT_ZY",
		Uid:          user.GetUid(),
		UserName:     user.GetUsername(),
		ItemId:       present.GetItemConfig().GetItemId(),
		ItemName:     present.GetItemConfig().GetName(),
		ItemNum:      1,
		ItemPrice:    present.GetItemConfig().GetPrice(),
		TotalPrice:   present.GetItemConfig().GetPrice(),
		Platform:     strconv.Itoa(int(clientType)),
		OutTradeNo:   orderId,
		Notes:        "FellowSvr::commitFellowOrder",
		OutOrderTime: time.Now().Format("2006-01-02 15:04:05"),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "commitPayOrder -- unified_pay::Client::Default()->UnfreezeAndConsume() failed. uid:%v order_id:%s,  ret %v",
			user.Uid, orderId, err)
	}

	// 额外包装一层deal-token
	newToken, err := updateDealTokenInfo(ctx, dealToken, orderId, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "updateDealTokenInfo fail , order: %s , err: %v", orderId, err)
		return ctime, newToken, err
	}

	return ctime, newToken, nil
}

// sendToDataCenter 上报数据
func (s *Server) sendInviteMsgToDataCenter(uid, targetUid uint32, content string) {
	now := time.Now()

	const timeLayout = "2006-01-02 15:04:05"

	data := map[string]interface{}{
		"appId":      "ttvoice",
		"totalDate":  now.Format(timeLayout),
		"sendUid":    uid,
		"targetUid":  targetUid,
		"platform":   "",
		"meType":     imPB.IM_MSG_TYPE_FELLOW_INVITE_MSG,
		"createTime": now.Format(timeLayout),
		"content":    content,
		"source":     imPB.MsgSourceType_MSG_SOURCE_FROM_FELLOW_INVITE,
	}

	datacenter.StdReportKV("************", data)
}

func (s *Server) FellowChangeEventPush(ctx context.Context, fromUser, toUser *accountPB.UserResp, bindType, fellowType uint32) {
	//发送绑定通知
	fellowInfo, err := s.store.GetFellowByIdPair(fromUser.GetUid(), toUser.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "FellowChangeEventPush - GetFellowByIdPair fail , uid: %d , err: %v", fromUser.GetUid(), err)
		return
	}

	s.mgr.SendFellowBindEvent(ctx, &fellowInfo)

	rareInfo, _ := s.mgr.GetBindingRare(ctx, fromUser.GetUid(), toUser.GetUid())
	log.InfoWithCtx(ctx, "FellowChangeEventPush uid:%d, touid:%d, rare:%+v", fromUser.GetUid(), toUser.GetUid(), rareInfo)
	ligatureUrl := s.bc.GetFellowLigature(bindType, fellowType)
	if bindType != uint32(pb.FellowBindType_ENUM_FELLOW_BIND_TYPE_UNKNOWN) {
		award, err := s.levelAwardCli.GetUserCurrentAwardInfo(ctx, &fellow_level_award.GetUserCurrentAwardInfoReq{
			MyUid:      fromUser.GetUid(),
			FellowUid:  toUser.GetUid(),
			FellowType: fellowType,
			AwardType:  uint32(fellow_level_award.LevelAwardItemType_LEVEL_AWARD_ITEM_LIGATURE),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "FellowChangeEventPush - GetUserCurrentAwardInfo fail , uid: %d , err: %v", fromUser.GetUid(), err)
		}

		if award != nil {
			if len(award.GetResource()) > 0 {
				ligatureUrl = award.GetResource()
				log.InfoWithCtx(ctx, "FellowChangeEventPush - GetUserCurrentAwardInfo ligatureUrl , uid: %d , ligatureUrl: %s", fromUser.GetUid(), ligatureUrl)
			}
		}
	}

	fromFellowMsg := &fellow_logic.FellowInfo{
		Uid:        int64(fromUser.GetUid()),
		Account:    fromUser.GetUsername(),
		NickName:   fromUser.GetNickname(),
		FellowType: fellowType,
		BindType:   bindType,
		FellowName: s.bc.GetUnbindFellowMapByType(fellowType),
		BindRare: &fellow_logic.RareInfo{
			RareId:    rareInfo.RareId,
			SubRareId: rareInfo.SubRareId,
		},
		FellowIcon:    s.bc.GetFellowIcon(bindType, fellowType),
		CardColour:    s.bc.GetCardColour(bindType, fellowType),
		RoomMsgColour: s.bc.GetMsgColour(bindType, fellowType),
		LigatureUrl:   ligatureUrl,
	}

	_ = s.mgr.PushFellowChangeMsgToUsers(ctx, fromFellowMsg, []uint32{toUser.GetUid()})

	otherSubRareId := 0
	if rareInfo.SubRareId == 1 {
		otherSubRareId = 2
	} else if rareInfo.SubRareId == 2 {
		otherSubRareId = 1
	}
	toFellowMsg := &fellow_logic.FellowInfo{
		Uid:        int64(toUser.GetUid()),
		Account:    toUser.GetUsername(),
		NickName:   toUser.GetNickname(),
		FellowType: fellowType,
		BindType:   bindType,
		FellowName: s.bc.GetUnbindFellowMapByType(fellowType),
		BindRare: &fellow_logic.RareInfo{
			RareId:    rareInfo.RareId,
			SubRareId: uint32(otherSubRareId),
		},
		FellowIcon:    s.bc.GetFellowIcon(bindType, fellowType),
		CardColour:    s.bc.GetCardColour(bindType, fellowType),
		RoomMsgColour: s.bc.GetMsgColour(bindType, fellowType),
		LigatureUrl:   ligatureUrl,
	}

	_ = s.mgr.PushFellowChangeMsgToUsers(ctx, toFellowMsg, []uint32{fromUser.GetUid()})

	channelInfo, err := s.channelOlCli.GetUsersChannelId(ctx, 0, fromUser.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "SendFellowInviteFunc - GetUsersChannelId fail , uid: %d , err: %v", fromUser.GetUid(), err)
		return
	}

	s.mgr.PushOnMicFellowChange(ctx, fromUser.GetUid(), channelInfo.ChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "get mic fellow list err:%v", err)
		return
	}
}

func updateDealTokenInfo(ctx context.Context, dealToken string, orderId string, isBatch bool) (string, error) {
	dealTokenContent, err := deal_token.Decode(dealToken)
	if err != nil {
		log.ErrorWithCtx(ctx, "deal_token.Decode fail , err %v , order_id %s", err, orderId)
		return dealToken, err
	}

	if isBatch {
		tbeanDealTokenContent, err := deal_token.Decode(dealTokenContent.PrevToken)
		if err != nil {
			log.ErrorWithCtx(ctx, "deal_token.Decode fail , err %v , order_id %s", err, orderId)
			return dealToken, nil
		}
		tbeanDealTokenContent.OrderID = orderId
		newTbeanDealToken, _ := deal_token.Encode(tbeanDealTokenContent)
		dealTokenContent.PrevToken = newTbeanDealToken
	}

	newDt := deal_token.NewDealTokenData(dealTokenContent.TradeNo, orderId, "fellow-svr", dealTokenContent.BuyerID, dealTokenContent.TotalPrice)
	newDealToken, err := deal_token.AddDealToken(dealToken, newDt)
	if err != nil {
		log.ErrorWithCtx(ctx, "deal_token.AddDealToken fail , err %v , order_id %s", err, orderId)
		return dealToken, nil
	}

	return newDealToken, nil
}
