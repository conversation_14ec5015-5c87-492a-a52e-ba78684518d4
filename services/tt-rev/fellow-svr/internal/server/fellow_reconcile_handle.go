package server

import (
	"context"
	"fmt"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	present_middleware "golang.52tt.com/protocol/services/present-middleware"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	"golang.52tt.com/protocol/services/unified_pay"
	goctx "golang.org/x/net/context"
)

func (s *Server) FixFellowOrder(c goctx.Context, req *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	out := &reconcile_v2.EmptyResp{}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	order, exist, err := s.store.GetFellowTbeanOrderByOutTradeNo(req.GetOrderId())
	if err != nil {
		log.ErrorWithCtx(c, "FixFellowOrder - GetFellowTbeanOrderByOutTradeNo fail , order: %s , err : %v", req.OrderId, err)
		return out, err
	}

	if !exist {
		log.ErrorWithCtx(c, "FixFellowOrder - GetFellowTbeanOrderByOutTradeNo order not exist , order: %s , err : %v", req.OrderId, err)
		return out, err
	}

	resp, err := s.store.GetFellowInviteInfoByOutTradeNo(req.GetOrderId())
	if err != nil {
		log.ErrorWithCtx(c, "FixFellowOrder - GetFellowInviteInfoById fail , order: %s , err : %v", req.OrderId, err)
		return out, err
	}

	sendUser, err := s.accountCli.GetUser(c, resp.InviteUid)
	if err != nil {
		log.Errorf("FixFellowOrder - GetUser fail , order: %s , err : %v", order.OutTradeNo, err)
		return out, err
	}

	presentCfg, err := s.presentCli.GetPresentConfigById(c, resp.PresentId)
	if err != nil {
		log.Errorf("FixSendFellowPresent - GetPresentConfigById fail , order: %s , err : %v", order.OutTradeNo, err)
		return out, err
	}

	ctime, dealToken, err := s.payCli.UnfreezeAndConsume(c, &unified_pay.UnfreezeAndConsumeReq{
		AppId:        "TT_ZY",
		Uid:          sendUser.GetUid(),
		UserName:     sendUser.GetUsername(),
		ItemId:       resp.PresentId,
		ItemName:     presentCfg.GetItemConfig().GetName(),
		ItemNum:      1,
		ItemPrice:    presentCfg.GetItemConfig().GetPrice(),
		TotalPrice:   presentCfg.GetItemConfig().GetPrice(),
		Platform:     "0",
		OutTradeNo:   resp.OutTradeNo,
		Notes:        "checkAndFixFellowPresent",
		OutOrderTime: time.Now().Format("2006-01-02 15:04:05"),
	})
	if err != nil {
		log.Errorf("FixSendFellowPresent - UnfreezeAndConsume fail , order: %s , err : %v", order.OutTradeNo, err)
	}

	if order.DealToken != "" {
		dealToken = order.DealToken
		ctime = order.CTime
	} else {
		dealToken, err = updateDealTokenInfo(c, dealToken, resp.OutTradeNo, false)
		if err != nil {
			log.ErrorWithCtx(c, "updateDealTokenInfo fail , order: %s , err: %v", resp.OutTradeNo, err)
			return out, err
		}
	}

	fmt.Println(ctime, dealToken)
	_, err = s.presentMiddleCli.FellowSendPresent(c, &present_middleware.FellowSendPresentReq{
		OrderId:     resp.OutTradeNo,
		TargetUid:   resp.TargetUid,
		ItemId:      resp.PresentId,
		Count:       1,
		ChannelId:   resp.ChannelId,
		SendSource:  uint32(present_middleware.PresentSendSourceType_E_SEND_SOURCE_FELLOW),
		ItemSource:  uint32(present_middleware.PresentSourceType_PRESENT_SOURCE_FELLOW),
		SourceId:    resp.PresentId,
		SendType:    uint32(present_middleware.PresentSendType_PRESENT_SEND_NORMAL),
		AppId:       0,
		MarketId:    0,
		ServiceInfo: &present_middleware.ServiceCtrlInfo{},
		IsOptValid:  true,
		SendUid:     resp.InviteUid,
		Ctime:       order.CTime,
		DealToken:   dealToken,
	})

	if err != nil {
		log.Errorf("FixSendFellowPresent - FellowSendPresent fail , order: %s , err : %v", order.OutTradeNo, err)
		return out, err
	}

	// 写信物历史，因为信物历史消费礼物kafka时会用到，只能先更新了
	sErr := s.store.AddFellowPresentHistory(ctx, order.FromUid, order.ToUid, order.GiftId)
	if sErr != nil {
		log.ErrorWithCtx(c, "FixSendFellowPresent - AddFellowPresentHistory fail , req: %v , err : %v", req, err)
		return out, protocol.ToServerError(sErr)
	}
	_ = s.store.DelPresentHistoryCache(ctx, order.FromUid, order.ToUid)

	return out, nil
}
