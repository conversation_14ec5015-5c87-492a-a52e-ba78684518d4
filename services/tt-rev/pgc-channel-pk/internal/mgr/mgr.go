package mgr

import (
	"context"
	"github.com/google/wire"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"golang.52tt.com/clients/channel"
	channelscheme "golang.52tt.com/clients/channel-scheme"
	channelstats "golang.52tt.com/clients/channel-stats"
	"golang.52tt.com/clients/channelguild"
	"golang.52tt.com/clients/channelim"
	"golang.52tt.com/clients/channelmic"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/entertainmentrecommendback"
	"golang.52tt.com/clients/guild"
	im_api "golang.52tt.com/clients/im-api"
	maskedPkSvr "golang.52tt.com/clients/masked-pk-svr"
	publicnotice "golang.52tt.com/clients/public-notice"
	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/seqgen/v2"
	tClient "golang.52tt.com/clients/timeline"
	ttrevoperationcli "golang.52tt.com/clients/tt-rev-operation"
	userprofileapi "golang.52tt.com/clients/user-profile-api"
	youknowwho "golang.52tt.com/clients/you-know-who"
	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/log"
	channel_live_show_list "golang.52tt.com/protocol/services/channel-live-show-list"
	pgcchannelgame "golang.52tt.com/protocol/services/pgc-channel-game"
	switch_scheme_checker "golang.52tt.com/protocol/services/switch-scheme-checker"
	comKafka "golang.52tt.com/services/common/kafka"
	"golang.52tt.com/services/tt-rev/pgc-channel-pk/internal/cache"
	"golang.52tt.com/services/tt-rev/pgc-channel-pk/internal/conf"
	"golang.52tt.com/services/tt-rev/pgc-channel-pk/internal/store"
	"golang.52tt.com/services/ugc/common/kafka_produce"
	"google.golang.org/grpc"
)

var ProviderSetForMgr = wire.NewSet()

type Mgr struct {
	sc                *conf.StartConfig
	bc                conf.IBusinessConfManager
	store             store.IStore
	cache             cache.ICache
	PushCli           push.IClient
	channelCli        channel.IClient
	channelMicCli     channelmic.IClient
	channelSchemeCli  channelscheme.IClient
	channelGuildCli   channelguild.IClient
	userProfileCli    userprofileapi.IClient
	channelStatsCli   channelstats.IClient
	entertaimentCli   entertainmentrecommendback.IClient
	SeqGenClient      seqgen.IClient
	ImApiClient       im_api.IClient
	TimelineClient    tClient.IClient
	GuildCli          guild.IClient
	channelOlCli      channelol.IClient
	maskedPKCli       maskedPkSvr.IClient
	publicNoticeCli   publicnotice.IClient
	ukwCli            youknowwho.IClient
	pkEventKfkProd    *kafka_produce.KafkaProduce
	pgcChannelGameCli pgcchannelgame.PgcChannelGameClient
	channelimClient   channelim.IClient
	ttRevOperationCli ttrevoperationcli.IClient
	offerRoomCli switch_scheme_checker.SwitchSchemeCheckerClient
	channelLiveShowListCli channel_live_show_list.ChannelLiveShowListClient
	timerD       	  *timer.Timer
}

func NewMgr(sc *conf.StartConfig, bc *conf.BusinessConfManager, st *store.Store, cache *cache.Cache) *Mgr {
	pushCli, _ := push.NewClient()
	channelCli := channel.NewClient()
	channelMicCli := channelmic.NewClient()
	channelSchemeCli := channelscheme.NewClient()
	channelGuildCli := channelguild.NewClient()
	userProfileCli, _ := userprofileapi.NewClient()
	channelStatsCli, _ := channelstats.NewClient()
	entertaimentCli := entertainmentrecommendback.NewClient()
	seqGenClient := seqgen.NewIClient()
	imApiClient := im_api.NewIClient()
	timelineClient := tClient.NewClient()
	guildCli := guild.NewClient()
	channelOlCli := channelol.NewClient()
	maskedPKCli := maskedPkSvr.NewIClient()
	publicNoticeCli, _ := publicnotice.NewClient()
	ukwCli, _ := youknowwho.NewClient()
	pgcChannelGameCli, _ := pgcchannelgame.NewClient(context.Background())
	channelimClient := channelim.NewIClient()
	ttrevoperationcli, _ := ttrevoperationcli.NewClient()
	offerRoomCli := client.NewInsecureGRPCClient(
		"offer-room",
		func(cc *grpc.ClientConn) interface{} {
			return switch_scheme_checker.NewSwitchSchemeCheckerClient(cc)
		},
		grpc.WithInsecure(),
		grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor),
	)
	channelLiveShowListCli, err := channel_live_show_list.NewClient(context.Background())
	if err != nil {
		log.Errorf("NewClient err:%v", err)
		return nil
	}

	pkKfkProd, err := comKafka.NewKFKProducer(sc.PkEventKFK)
	if err != nil {
		log.Errorf("NewKFKProducer producer failed,config:%v err:%v", sc.PkEventKFK, err)
		return nil
	}
	mgr := &Mgr{
		sc:                sc,
		bc:                bc,
		store:             st,
		cache:             cache,
		PushCli:           pushCli,
		channelCli:        channelCli,
		channelMicCli:     channelMicCli,
		channelSchemeCli:  channelSchemeCli,
		channelGuildCli:   channelGuildCli,
		userProfileCli:    userProfileCli,
		channelStatsCli:   channelStatsCli,
		entertaimentCli:   entertaimentCli,
		SeqGenClient:      seqGenClient,
		ImApiClient:       imApiClient,
		TimelineClient:    timelineClient,
		GuildCli:          guildCli,
		channelOlCli:      channelOlCli,
		maskedPKCli:       maskedPKCli,
		publicNoticeCli:   publicNoticeCli,
		ukwCli:            ukwCli,
		pkEventKfkProd:    pkKfkProd,
		pgcChannelGameCli: pgcChannelGameCli,
		channelimClient:   channelimClient,
		ttRevOperationCli: ttrevoperationcli,
		offerRoomCli:      offerRoomCli.Stub().(switch_scheme_checker.SwitchSchemeCheckerClient),
		channelLiveShowListCli:  channelLiveShowListCli,
	}

	// 百灵数据统计 初始化
	bylinkCollect, err := bylink.NewKfkCollector()
	if err != nil {
		log.Errorf("Failed to new kfk collector: %v", err)
	}
	bylink.InitGlobalCollector(bylinkCollect)

	// 启动定时器
	if err := mgr.setupTimer(); err != nil {
		log.ErrorWithCtx(nil, "NewMgr setupTimer failed", err)
		return nil
	}

	return mgr
}
