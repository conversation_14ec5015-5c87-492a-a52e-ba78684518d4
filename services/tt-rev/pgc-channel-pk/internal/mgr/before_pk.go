package mgr

import (
	"context"
	"fmt"
	channel_live_show_list "golang.52tt.com/protocol/services/channel-live-show-list"
	"sort"
	"strconv"
	"strings"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pbApp "golang.52tt.com/protocol/app"
	channelPb "golang.52tt.com/protocol/app/channel"
	logicpb "golang.52tt.com/protocol/app/pgc_channel-pk-logic"
	pgcchannelgamelogic "golang.52tt.com/protocol/app/pgc_channel_game_logic"
	"golang.52tt.com/protocol/common/status"
	channelschemeconfmgr "golang.52tt.com/protocol/services/channel-scheme-conf-mgr"
	channel "golang.52tt.com/protocol/services/channelsvr"
	entertainmentRecommendBack "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	pgcchannelgamepb "golang.52tt.com/protocol/services/pgc-channel-game"
	pb "golang.52tt.com/protocol/services/pgc-channel-pk"
	switch_scheme_checker "golang.52tt.com/protocol/services/switch-scheme-checker"
	"golang.52tt.com/services/tt-rev/pgc-channel-pk/internal/common"
	"golang.org/x/sync/errgroup"
)

var ServiceInfoError = protocol.NewExactServerError(nil, status.ErrPgcChannelPkInvalidParam, "get serviceInfo fail")

// GetPKEntry 房间pk入口
func (mgr *Mgr) GetPKEntry(ctx context.Context, channelId uint32) (bool, error) {
	// 运营后台配置权限
	permMap, err := mgr.ttRevOperationCli.HasChannelPKPerm(ctx, []uint32{channelId})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPKEntry.HasChannelPKPerm, channelId: %d, err: %v", channelId, err)
		return false, err
	}
	log.Debugf("GetPKEntry.HasChannelPKPerm, permMap: %+v", permMap)

	if !mgr.bc.GetPKEntry() || !permMap[channelId] {
		return false, nil
	}

	// 仅娱乐模式可见
	if isPkScheme, err := mgr.isPKChannelScheme(ctx, channelId); !isPkScheme || err != nil {
		log.ErrorWithCtx(ctx, "GetPKEntry.isPKChannelScheme, channelId: %d, err: %v", channelId, err)
		return false, nil
	}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return false, ServiceInfoError
	}

	// 主持人可见
	isCompere, err := mgr.isCompere(ctx, serviceInfo.UserID, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPKEntry.isCompere, channelId: %d, err: %v", channelId, err)
		return false, err
	}
	// 房主,超管可见
	isPKSwitchOperator, err := mgr.isPKSwitchOperator(ctx, serviceInfo.UserID, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPKEntry.isPKSwitchOperator, channelId: %d, err: %v", channelId, err)
		return false, err
	}
	if !isCompere && !isPKSwitchOperator {
		return false, nil
	}

	return true, nil
}

// GetPKTimeLimitToast 获取风控时段pk限制文案
func (mgr *Mgr) GetPKTimeLimitToast() string {
	lfs, lfe := mgr.getLimitTimeFrame()
	return fmt.Sprintf("注意：每厅每天%02d:%02d-%02d:%02d期间仅可pk%d次", lfs.Hour(), lfs.Minute(), lfe.Hour(), lfe.Minute(), mgr.bc.GetLimitPKTimes())
}

// IsChannelLimitPK 是否限制pk
func (mgr *Mgr) IsChannelLimitPK(ctx context.Context, channelId uint32) bool {
	lts, lte := mgr.getLimitTimeFrame()
	if lts.Before(time.Now()) && lte.After(time.Now()) {
		pkTimes, err := mgr.cache.BatchGetLimitTimeFramePKTimes(ctx, []uint32{channelId})
		if err != nil {
			log.ErrorWithCtx(ctx, "IsChannelLimitPK, channelId: %d, err: %v", channelId, err)
			return false
		}

		if pkTimes[channelId] >= mgr.bc.GetLimitPKTimes() {
			return true
		}
	}
	return false
}

func (mgr *Mgr) getGuildLongId(ctx context.Context, uid, channelId, guildId uint32) (uint32, error) {
	guildLongId := uint32(0)

	// 查找工会id
	if guildId == 0 {
		channelInfo, err := mgr.channelCli.GetChannelSimpleInfo(ctx, uid, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "getGuildId.GetChannelSimpleInfo, channelId: %d, err: %v", channelId, err)
			return 0, err
		}
		guildLongId = channelInfo.GetBindId()

	} else { // 确保工会id为长号
		guildInfo, err := mgr.GuildCli.GetGuild(ctx, guildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "getGuildId.GetGuild, channelId: %d, guildId: %d, err: %v", channelId, guildId, err)
			return 0, protocol.NewExactServerError(nil, status.ErrPgcChannelPkGuildidError)
		}
		guildLongId = guildInfo.GetGuildId()
	}

	return guildLongId, nil
}

func (mgr *Mgr) GetPKChannelList(ctx context.Context, channelId, guildId uint32) ([]*pb.PgcChannelPKChannelInfo, uint32, uint32, error) {
	start := time.Now()
	rs := make([]*pb.PgcChannelPKChannelInfo, 0, 8)
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return rs, channelId, guildId, ServiceInfoError
	}

	// 查找发起用户所在房间
	if channelId == 0 {
		userChannel, err := mgr.channelOlCli.GetUsersChannelId(ctx, serviceInfo.UserID, serviceInfo.UserID)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPKChannelList.GetUsersChannelId, channelId: %d, err: %v", channelId, err)
			return rs, channelId, guildId, err
		}
		channelId = userChannel.GetChannelId()
	}

	var err error
	guildId, err = mgr.getGuildLongId(ctx, serviceInfo.UserID, channelId, guildId)
	if err != nil || guildId == 0 { // 如果找不到工会id也提前返回
		log.ErrorWithCtx(ctx, "GetPKChannelList.GetChannelSimpleInfo, channelId: %d, guildId: %d, err: %v", channelId, guildId, err)
		return rs, channelId, guildId, err
	}

	log.InfoWithCtx(ctx, "GetPKChannelList.GetGuildLongId, channelId: %d,  cost: %v", channelId, time.Since(start))
	// 获取工会房间列表
	channelIds, err := mgr.channelGuildCli.GetChannelGuildList(ctx, guildId, uint32(channelPb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPKChannelList.GetChannelGuildList, channelId: %d, guildId: %d, err: %v", channelId, guildId, err)
		return rs, channelId, guildId, err
	}
	if len(channelIds) == 0 {
		return rs, channelId, guildId, protocol.NewExactServerError(nil, status.ErrPgcChannelPkGuildidError)
	}
	// 过滤白名单房间
	channelIds = mgr.filterPKChannelWhiteList(ctx, channelIds)
	if len(channelIds) == 0 {
		return rs, channelId, guildId, nil
	}

	log.InfoWithCtx(ctx, "GetPKChannelList.filterPKChannelWhiteList, channelId: %d,  cost: %v", channelId, time.Since(start))

	// 过滤可用房间
	channelIds, err = mgr.filterNotApplicableChannelBatch(ctx, channelIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPKChannelList.filterNotApplicableChannelBatch, channelId: %d, guildId: %d, err: %v", channelId, guildId, err)
	}
	if len(channelIds) == 0 {
		return rs, channelId, guildId, nil
	}

	log.InfoWithCtx(ctx, "GetPKChannelList.filterNotApplicableChannelBatch, channelId: %d, channelIds:%v  cost: %v", channelId, channelIds, time.Since(start))

	var hotMap map[uint32]int64
	eg, eCtx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		// 获取房间热度
		hotMap, err = mgr.channelStatsCli.BatchGetChannelHotValue(eCtx, channelIds)
		if err != nil {
			log.ErrorWithCtx(eCtx, "GetPKChannelList.BatchGetChannelHotValue, channelId: %d, "+
				"channelIds: %+v, err: %v", channelId, channelIds, err)
			return err
		}
		return nil
	})

	// 查询房间标签
	tagMap := make(map[uint32]*entertainmentRecommendBack.ChannelTagConfigInfo)
	eg.Go(func() error {
		tagResp, err := mgr.entertaimentCli.BatchGetChannelTag(eCtx, 0, &entertainmentRecommendBack.BatchGetChannelTagReq{
			ChannelIdList: channelIds,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPKChannelList.BatchGetChannelTag, channelId: %d, channelIds: %v, err: %v",
				channelId, channelIds)
			return err
		}

		for i, item := range tagResp.GetChannelTagList() {
			tagMap[channelIds[i]] = item
		}
		return nil
	})

	pkStatusMap := make(map[uint32]uint32)
	eg.Go(func() error {
		// 判断pk状态
		pkStatusMap, err = mgr.isPKMap(eCtx, channelIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPKChannelList.isPKMap, channelId: %d, channelIds: %v, err: %v", channelId, channelIds, err)
			return err
		}
		return nil
	})

	channelInfos := make(map[uint32]*channel.ChannelSimpleInfo)
	eg.Go(func() error {
		// 查询房间信息
		channelInfos, err = mgr.channelCli.BatchGetChannelSimpleInfo(eCtx, 0, channelIds)
		if err != nil {
			log.ErrorWithCtx(eCtx, "GetPKChannelList.BatchGetChannelSimpleInfo, channelId: %d, channelIds: %v, err: %v",
				channelId, channelIds, err)
			return err
		}
		return nil
	})

	channelAnchorUserprofileMap := make(map[uint32]*pbApp.UserProfile)
	eg.Go(func() error {
		// 获取房间主持人userprofile
		channelAnchorUserprofileMap, err = mgr.getChannelAnchorUserprofileMap(eCtx, channelIds)
		if err != nil {
			log.ErrorWithCtx(eCtx, "GetPKChannelList.getChannelAnchorUserprofileMap, channelId: %d, channelIds: %v, err: %v",
				channelId, channelIds, err)
			return err
		}
		return nil
	})

	err = eg.Wait()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPKChannelList.Wait, channelId: %d, channelIds: %v, err: %v", channelId, channelIds, err)
		return rs, channelId, guildId, err
	}

	// 按热度排序
	sortSli := make([]*ChannelHotSortItem, 0, len(channelIds))
	for k, v := range hotMap {
		sortSli = append(sortSli, &ChannelHotSortItem{
			ChannelId: k,
			Hot:       uint32(v),
		})
	}
	sort.Slice(sortSli, func(i, j int) bool {
		return sortSli[i].Hot > sortSli[j].Hot
	})
	sortChannelIds := make([]uint32, 0, len(sortSli))
	for _, item := range sortSli {
		sortChannelIds = append(sortChannelIds, item.ChannelId)
	}

	if len(sortChannelIds) == 0 {
		return rs, channelId, guildId, nil
	}

	log.InfoWithCtx(ctx, "GetPKChannelList.BatchGetChannelSimpleInfo, channelId: %d,  cost: %v", channelId, time.Since(start))

	for _, cid := range sortChannelIds {
		if cid == channelId {
			continue // 排除自己房间
		}
		cInfo, hasInfo := channelInfos[cid]
		tagInfo, hasTag := tagMap[cid]
		// 如果没配置渐变色, 使用bkcolor
		if len(tagInfo.GetMultiColor()) == 0 {
			tagInfo.MultiColor = []string{tagInfo.GetBkColor(), tagInfo.GetBkColor()}
		}
		if !hasInfo || !hasTag {
			continue
		}
		rs = append(rs, &pb.PgcChannelPKChannelInfo{
			ChannelId:         cid,
			ChannelType:       cInfo.GetChannelType(),
			ChannelIcon:       cInfo.GetIconMd5(),
			ChannelName:       cInfo.GetName(),
			BindGuildId:       cInfo.GetBindId(),
			CreatorUid:        cInfo.GetCreaterUid(),
			TabName:           tagInfo.GetName(),
			TabColor:          tagInfo.GetMultiColor(),
			Hot:               uint32(hotMap[cid]),
			PkStatus:          pb.PgcChannelPKChannelInfo_Status(pkStatusMap[cid]),
			AnchorUserProfile: userprofileTransform(channelAnchorUserprofileMap[cid]),
		})
	}

	return rs, channelId, guildId, nil
}

// 过滤白名单房间
func (mgr *Mgr) filterPKChannelWhiteList(ctx context.Context, channelIds []uint32) []uint32 {
	permMap, err := mgr.ttRevOperationCli.HasChannelPKPerm(ctx, channelIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "filterPKChannelWhiteList.HasChannelPKPerm, channelIds: %+v, err: %v", channelIds, err)
		return []uint32{}
	}

	retCids := make([]uint32, 0, 8)
	for _, cid := range channelIds {
		if permMap[cid] {
			retCids = append(retCids, cid)
		}
	}
	return retCids
}

func userprofileTransform(up *pbApp.UserProfile) *pb.UserProfile {
	return &pb.UserProfile{
		Uid:          up.GetUid(),
		Account:      up.GetAccount(),
		Nickname:     up.GetNickname(),
		AccountAlias: up.GetAccountAlias(),
		Sex:          up.GetSex(),
		Privilege:    privilegeTransform(up.GetPrivilege()),
	}
}

func privilegeTransform(pr *pbApp.UserPrivilege) *pb.UserPrivilege {
	return &pb.UserPrivilege{
		Account:  pr.GetAccount(),
		Nickname: pr.GetNickname(),
		Type:     pr.GetType(),
		Options:  pr.GetOptions(),
	}
}

func (mgr *Mgr) getChannelAnchorUserprofileMap(ctx context.Context, channelIds []uint32) (map[uint32]*pbApp.UserProfile, error) {
	rs := make(map[uint32]*pbApp.UserProfile)
	// 获取用户信息
	cidAnchorMap, err := mgr.getChannelAnchors(ctx, channelIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "getChannelAnchorUserprofileMap.GetChannelAnchorMap, channelIds: %+v err: %v", channelIds, err)
		return rs, err
	}
	anchorCidMap := make(map[uint32]uint32)
	uids := make([]uint32, 0, len(cidAnchorMap))
	for k, v := range cidAnchorMap {
		anchorCidMap[v] = k
		uids = append(uids, v)
	}

	anchorUserprofile, err := mgr.userProfileCli.BatchGetUserProfile(ctx, uids)
	if err != nil {
		log.ErrorWithCtx(ctx, "getChannelAnchorUserprofileMap.BatchGetUserProfile, channelIds: %+v err: %v", channelIds, err)
		return rs, err
	}

	for k, v := range anchorUserprofile {
		rs[anchorCidMap[k]] = v
	}
	return rs, nil
}

type ChannelHotSortItem struct {
	ChannelId uint32
	Hot       uint32
}

func (mgr *Mgr) isPKMap(ctx context.Context, channelIds []uint32) (map[uint32]uint32, error) {
	rs := make(map[uint32]uint32)
	pkInvites, err := mgr.cache.BatchGetPKInvite(ctx, channelIds)
	if err != nil {
		return rs, err
	}
	pkIds, err := mgr.cache.BatchGetChannelPKId(ctx, channelIds)
	if err != nil {
		return rs, err
	}
	for _, cid := range channelIds {
		if pkInvites[cid] != 0 || pkIds[cid] != 0 {
			rs[cid] = uint32(pb.PgcChannelPKChannelInfo_InPK)
		}
	}
	return rs, nil
}

func (mgr *Mgr) GetChannelPKId(ctx context.Context, channelId uint32) (uint32, error) {
	pkId, err := mgr.cache.GetChannelPKId(ctx, channelId)
	if err != nil {
		return 0, err
	}
	return pkId, nil
}

const (
	channelFilterOptionEqual    = 1
	channelFilterOptionNotEqual = 2
	channelFilterOptionLt       = 3
)

func filterChannel(channelIds []uint32, sifter map[uint32]uint32, option uint32, val uint32) []uint32 {
	tmpChannelIds := make([]uint32, 0)
	for _, cid := range channelIds {
		switch option {
		case channelFilterOptionEqual:
			if sifter[cid] == val {
				tmpChannelIds = append(tmpChannelIds, cid)
			}
		case channelFilterOptionNotEqual:
			if sifter[cid] != val {
				tmpChannelIds = append(tmpChannelIds, cid)
			}
		case channelFilterOptionLt:
			if sifter[cid] < val {
				tmpChannelIds = append(tmpChannelIds, cid)
			}
		default:
			continue
		}
	}
	return tmpChannelIds
}

func (mgr *Mgr) filterNotApplicableChannelBatch(ctx context.Context, channelIds []uint32) ([]uint32, error) {
	// 过滤主持麦无人
	anchorIds, err := mgr.getChannelAnchors(ctx, channelIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "filterNotApplicableChannelBatch.GetChannelAnchorMap, channeIds: %+v, err: %v", channelIds, err)
		return channelIds, err
	}
	channelIds = filterChannel(channelIds, anchorIds, channelFilterOptionNotEqual, 0)
	if len(channelIds) == 0 {
		log.ErrorWithCtx(ctx, "filterNotApplicableChannelBatch.GetChannelAnchorMap, channeIds: %+v, err: no anchor", channelIds, err)
		return channelIds, nil
	}

	// 房间关闭pk
	pkSwitchs, err := mgr.cache.GetChannelPKSwitchMap(ctx, channelIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "filterNotApplicableChannelBatch.GetChannelPKSwitchMap, channeIds: %+v, err: %v", channelIds, err)
		return channelIds, err
	}
	channelIds = filterChannel(channelIds, pkSwitchs, channelFilterOptionEqual, 0)
	if len(channelIds) == 0 {
		log.ErrorWithCtx(ctx, "filterNotApplicableChannelBatch.GetChannelPKSwitchMap, channeIds: %+v, err: channel switch colse", channelIds, err)
		return channelIds, nil
	}

	// 是否神秘人
	tmpAnchorIds := make([]uint32, len(channelIds))
	anchor2Cid := make(map[uint32]uint32)
	for i, cid := range channelIds {
		tmpAnchorIds[i] = anchorIds[cid]
		anchor2Cid[anchorIds[cid]] = cid
	}
	anchorProfiles, err := mgr.userProfileCli.BatchGetUserProfile(ctx, tmpAnchorIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "filterNotApplicableChannelBatch.BatchGetUserProfile, channeIds: %+v, err: %v", channelIds, err)
	}
	isUkw := make(map[uint32]uint32)
	for k, v := range anchorProfiles {
		if v.GetPrivilege().GetType() == uint32(pbApp.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) {
			isUkw[anchor2Cid[k]] = 1
		}
	}
	channelIds = filterChannel(channelIds, isUkw, channelFilterOptionEqual, 0)
	if len(channelIds) == 0 {
		log.ErrorWithCtx(ctx, "filterNotApplicableChannelBatch.BatchGetUserProfile, channeIds: %+v, err: is ukw", channelIds, err)
		return channelIds, nil
	}

	// 玩法判断(当前没有批量接口, 后续优化, 过滤到这层符合条件的cid应该挺少了)
	rsChannelIds := make([]uint32, 0)
	for _, cid := range channelIds {
		flag, err := mgr.isPKChannelScheme(ctx, cid)
		if err != nil {
			log.ErrorWithCtx(ctx, "filterNotApplicableChannelBatch.BatchGetUserProfile, cid: %v, err: is ukw", cid, err)
			continue
		}
		if flag {
			rsChannelIds = append(rsChannelIds, cid)
		}
	}
	if len(rsChannelIds) == 0 {
		log.ErrorWithCtx(ctx, "filterNotApplicableChannelBatch.isPKChannelScheme, channeIds: %+v, err: is not pk scheme", channelIds, err)
	}

	return rsChannelIds, nil
}

const (
	timeFrameLimitPKErrorFormat = "该厅已到达%02d:%02d~%02d:%02d期间的%d次pk限额了哦，请稍后再试"
)

// isApplicableChannel 不符合pk条件
func (mgr *Mgr) isApplicableChannel(ctx context.Context, fromChannelId, toChannelId uint32, isAccept bool) (bool, error) {
	// 房间关闭pk
	if err := mgr.checkPKSwitch(ctx, fromChannelId, toChannelId, isAccept); err != nil {
		return false, err
	}
	// 主持麦无人
	if err := mgr.checkAnchor(ctx, fromChannelId, toChannelId, isAccept); err != nil {
		return false, err
	}

	// 最近五分钟pk被拒绝
	hasRefuse, err := mgr.cache.HasRefusePKInvite(ctx, fromChannelId, toChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "isApplicableChannel.HasRefusePKInvite, "+
			"fromChannelId: %d, toChannelId: %d, err: %v", fromChannelId, toChannelId, err)
		return false, err
	}
	if hasRefuse {
		log.ErrorWithCtx(ctx, "isApplicableChannel.HasRefusePKInvite, "+
			"fromChannelId: %d, toChannelId: %d, err: has refuse invite", fromChannelId, toChannelId)
		return false, protocol.NewExactServerError(nil, status.ErrPgcChannelPkInviteHadRefuse)
	}

	// 时间段次数限制
	pkTimes, err := mgr.cache.BatchGetLimitTimeFramePKTimes(ctx, []uint32{fromChannelId, toChannelId})
	if err != nil {
		log.ErrorWithCtx(ctx, "isApplicableChannel.BatchGetLimitTimeFramePKTimes, "+
			"fromChannelId: %d, toChannelId: %d, err: %v", fromChannelId, toChannelId, err)
		return false, err
	}
	if (pkTimes[fromChannelId] >= mgr.bc.GetLimitPKTimes() && !isAccept) || (pkTimes[toChannelId] >= mgr.bc.GetLimitPKTimes() && isAccept) { // 主态限制次数超了
		log.ErrorWithCtx(ctx, "isApplicableChannel.BatchGetLimitTimeFramePKTimes, fromChannelId: %d, toChannelId: %d,"+
			" pkTimes: %+v, err: the main state pk times limit", fromChannelId, toChannelId, pkTimes)
		return false, nil
	}
	if (pkTimes[fromChannelId] >= mgr.bc.GetLimitPKTimes() && isAccept) || (pkTimes[toChannelId] >= mgr.bc.GetLimitPKTimes() && !isAccept) {
		log.ErrorWithCtx(ctx, "isApplicableChannel.BatchGetLimitTimeFramePKTimes, fromChannelId: %d, toChannelId: %d,"+
			" pkTimes: %+v, err: custom status pk times limit", fromChannelId, toChannelId, pkTimes)
		lfs, lfe := mgr.getLimitTimeFrame()
		errMsg := fmt.Sprintf(timeFrameLimitPKErrorFormat, lfs.Hour(), lfs.Minute(), lfe.Hour(), lfe.Minute(), mgr.bc.GetLimitPKTimes())
		return false, protocol.NewExactServerError(nil, status.ErrPgcChannelPkLimitPkError, errMsg)
	}

	eg := errgroup.Group{}
	eg.Go(func() error {
		// 蒙面pk中
		return mgr.checkPking(ctx, fromChannelId, toChannelId, isAccept)
	})
	 eg.Go(func() error {
		 // 拍卖中
		 return mgr.checkOfferRoom(ctx, fromChannelId, toChannelId, isAccept)
	 })
	eg.Go(func() error {
		if !isAccept && mgr.checkPgcChannelGame(ctx, fromChannelId) { // 发起邀请时自己不能处于小游戏中
			return protocol.NewExactServerError(nil,
				status.ErrPgcChannelPkNotApplicable, "当前玩法过程中不可发起跨房PK")
		}
		return nil
	})
	err = eg.Wait()
	if err != nil {
		return false, err
	}




	cids, err := mgr.filterNotApplicableChannelBatch(ctx, []uint32{fromChannelId, toChannelId})
	if err != nil {
		return false, err
	}
	if len(cids) == 2 {
		return true, nil
	}

	// 正在pk中
	if err := mgr.checkPking(ctx, fromChannelId, toChannelId, isAccept); err != nil {
		return false, err
	}

	// 已有pk邀请
	if err := mgr.checkPKInvite(ctx, fromChannelId, toChannelId, isAccept); err != nil {
		return false, err
	}
	return false, nil
}

func (mgr *Mgr) checkMaskedPK(ctx context.Context, fromChannelId, toChannelId uint32, isAccept bool) error {
	isMaskedPking, err := mgr.isMaskedPking(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "isApplicableChannel.isMaskedPking, fromChannelId: %d, toChannelId: %d, err: %v", fromChannelId, toChannelId, err)
		return nil
	}
	if isMaskedPking {
		return protocol.NewExactServerError(nil, status.ErrPgcChannelPkLimitPkWhenMaskedPk)
	}
	return nil
}

func (mgr *Mgr) checkChannelLiveShowing(ctx context.Context, anchorUid uint32) error {
	showList, err := mgr.channelLiveShowListCli.GetChannelLiveShowInfo(ctx, &channel_live_show_list.GetChannelLiveShowInfoRequest{
		AnchorUid: anchorUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "isApplicableChannel.GetChannelLiveShowInfo, anchorUid: %d, err: %v", anchorUid, err)
		return nil
	}
	now  := time.Now().Unix()
	if showList.GetLiveShowInfo().GetBeginTs() >= now && showList.GetLiveShowInfo().GetEndTs() <= now {
		return protocol.NewExactServerError(nil, status.ErrPgcChannelPkNotApplicable)
	}
	return nil
}

func (mgr *Mgr) checkOfferRoom(ctx context.Context, fromChannelId, toChannelId uint32, isAccept bool) error {
	// from 和 to 都要检查判断，任意一个不满足都不允许跨房pk
	eg, eCtx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		// fromChannelId
		resp, err := mgr.offerRoomCli.Check(eCtx, &switch_scheme_checker.CheckReq{
			Cid: fromChannelId,
			FromScheme: &switch_scheme_checker.SchemeInfo{
				SchemeId:            0,
				SchemeName:          "",
				SchemeSvrDetailType: 0,
			},
			ToScheme: &switch_scheme_checker.SchemeInfo{
				SchemeId:            0,
				SchemeName:          "拍卖房",
				SchemeSvrDetailType: 28,
			},
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "isApplicableChannel.GetOfferRoom, fromChannelId: %d, err: %v", fromChannelId, err)
		}
		if resp.GetCode() != 0 {
			log.InfoWithCtx(ctx, "isApplicableChannel.GetOfferRoom, fromChannelId: %d, resp: %+v", fromChannelId, resp)
			return protocol.NewExactServerError(nil, int(resp.GetCode()), resp.GetMsg())
		}
		return nil
	})
	eg.Go(func() error {
		// toChannelId
		resp, err := mgr.offerRoomCli.Check(eCtx, &switch_scheme_checker.CheckReq{
			Cid: toChannelId,
			FromScheme: &switch_scheme_checker.SchemeInfo{
				SchemeId:            0,
				SchemeName:          "",
				SchemeSvrDetailType: 0,
			},
			ToScheme: &switch_scheme_checker.SchemeInfo{
				SchemeId:            0,
				SchemeName:          "拍卖房",
				SchemeSvrDetailType: 28,
			},
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "isApplicableChannel.GetOfferRoom, toChannelId: %d, err: %v", fromChannelId, err)
		}
		if resp.GetCode() != 0 {
			log.InfoWithCtx(ctx, "isApplicableChannel.GetOfferRoom, toChannelId: %d, resp: %+v", fromChannelId, resp)
			return protocol.NewExactServerError(nil, int(resp.GetCode()), resp.GetMsg())
		}
		return nil
	})
	err := eg.Wait()
	if err != nil {
		log.ErrorWithCtx(ctx, "isApplicableChannel.GetOfferRoom, fromChannelId: %d, toChannelId: %d, err: %v", fromChannelId, toChannelId, err)
	}
	return nil
}

// checkPgcChannelGame 检查公会房小游戏是否进行中
func (mgr *Mgr) checkPgcChannelGame(ctx context.Context, channelId uint32) bool {
	resp, err := mgr.pgcChannelGameCli.GetChannelGameInfo(ctx, &pgcchannelgamepb.GetChannelGameInfoReq{ChannelId: channelId})
	if err != nil {
		log.ErrorWithCtx(ctx, "checkPgcChannelGame.GetChannelGameInfo, channelId: %d, err: %v", channelId, err)
		return false
	}

	if resp.GetPhase() == uint32(pgcchannelgamelogic.SetGamePhaseReq_GAME_PHASE_START) {
		return true
	}

	return false
}

func (mgr *Mgr) checkPKSwitch(ctx context.Context, fromChannelId, toChannelId uint32, isAccept bool) error {
	pkSwitchs, err := mgr.cache.GetChannelPKSwitchMap(ctx, []uint32{fromChannelId, toChannelId})
	if err != nil {
		log.ErrorWithCtx(ctx, "isApplicableChannel.GetChannelPKSwitchMap, fromChannelId: %d, toChannelId: %d, err: %v", fromChannelId, toChannelId, err)
		return err
	}
	if (pkSwitchs[fromChannelId] != 0 && !isAccept) || (pkSwitchs[toChannelId] != 0 && isAccept) { // 区分发起/接受的操作方, 我方开关关闭
		log.ErrorWithCtx(ctx, "isApplicableChannel.GetChannelPKSwitchMap, fromChannelId: %d, toChannelId: %d, err: our switch close", fromChannelId, toChannelId, err)
		return protocol.NewExactServerError(nil, status.ErrPgcChannelPkOurPkSwitchClose)
	}
	if (pkSwitchs[fromChannelId] != 0 && isAccept) || (pkSwitchs[toChannelId] != 0 && !isAccept) { // 区分发起/接受的操作方, 对方pk关闭
		log.ErrorWithCtx(ctx, "isApplicableChannel.GetChannelPKSwitchMap, fromChannelId: %d, toChannelId: %d, err: their switch close", fromChannelId, toChannelId, err)
		return protocol.NewExactServerError(nil, status.ErrPgcChannelPkNotApplicable)
	}
	return nil
}

func (mgr *Mgr) checkAnchor(ctx context.Context, fromChannelId, toChannelId uint32, isAccept bool) error {
	anchorIds, err := mgr.getChannelAnchors(ctx, []uint32{fromChannelId, toChannelId})
	if err != nil {
		log.ErrorWithCtx(ctx, "isApplicableChannel.GetChannelAnchorMap, fromChannelId: %d, toChannelId: %d, err: %v", fromChannelId, toChannelId, err)
		return err
	}
	if (anchorIds[fromChannelId] == 0 && !isAccept) || (anchorIds[toChannelId] == 0 && isAccept) {
		return protocol.NewExactServerError(nil, status.ErrPgcChannelPkOurPkSwitchClose)
	}
	if (anchorIds[fromChannelId] == 0 && isAccept) || (anchorIds[toChannelId] == 0 && !isAccept) {
		return protocol.NewExactServerError(nil, status.ErrPgcChannelPkNotApplicable)
	}
	return nil
}

func (mgr *Mgr) checkPKInvite(ctx context.Context, fromChannelId, toChannelId uint32, isAccept bool) error {
	pkInvites, err := mgr.cache.BatchGetPKInvite(ctx, []uint32{fromChannelId, toChannelId})
	if err != nil {
		log.ErrorWithCtx(ctx, "isApplicableChannel.BatchGetPKInvite, fromChannelId: %d, toChannelId: %d, err: %v", fromChannelId, toChannelId, err)
		return err
	}
	if pkInvites[fromChannelId] != 0 && pkInvites[fromChannelId] != toChannelId {
		return protocol.NewExactServerError(nil, status.ErrPgcChannelPkLimitMultiInvite)
	}
	if pkInvites[toChannelId] != 0 && pkInvites[toChannelId] != fromChannelId {
		return protocol.NewExactServerError(nil, status.ErrPgcChannelPkHadOtherInvite)
	}
	return nil
}

func (mgr *Mgr) checkPking(ctx context.Context, fromChannelId, toChannelId uint32, isAccept bool) error {
	pkIds, err := mgr.cache.BatchGetChannelPKId(ctx, []uint32{fromChannelId, toChannelId})
	if err != nil {
		log.ErrorWithCtx(ctx, "isApplicableChannel.GetChannelPKId, fromChannelId: %d, toChannelId: %d, err: %v", fromChannelId, toChannelId, err)
		return err
	}
	if pkIds[fromChannelId] != 0 {
		return protocol.NewExactServerError(nil, status.ErrPgcChannelPkLimitMultiPkFrom)
	}
	if pkIds[toChannelId] != 0 {
		log.ErrorWithCtx(ctx, "isApplicableChannel.BatchGetChannelPKId, fromChannelId: %d, toChannelId: %d, pkIds: %+v,"+
			" err: is pking", fromChannelId, toChannelId, pkIds)
		return nil
	}
	return nil
}

// StartPgcChannelPK 发送pk邀请
func (mgr *Mgr) StartPgcChannelPK(ctx context.Context, fromCid, toCid uint32) error {
	validChannel := mgr.filterPKChannelWhiteList(ctx, []uint32{fromCid, toCid}) // 白名单开启时, 防止非白名单的房间pk
	if len(validChannel) < 2 {
		return nil
	}

	startTime := time.Now().UnixNano() / 1e6
	applicable, err := mgr.isApplicableChannel(ctx, fromCid, toCid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartPgcChannelPK.isApplicableChannel, fromCid: %d, toCid: %d, err: %v", fromCid, toCid, err)
		return err
	}
	if !applicable {
		return protocol.NewExactServerError(nil, status.ErrPgcChannelPkNotApplicable)
	}
	log.Infof("StartPgcChannelPK.isApplicableChannel, cast: %dms", time.Now().UnixNano()/1e6-startTime)
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return ServiceInfoError
	}
	cErr := mgr.cache.SetPKInvite(ctx, fromCid, toCid, serviceInfo.UserID)
	if cErr != nil {
		log.ErrorWithCtx(ctx, "StartPgcChannelPK.SetPKInvite, fromCid: %d, toCid, err: %v",
			fromCid, toCid, cErr)
		return cErr
	}

	senderProfile, err := mgr.userProfileCli.GetUserProfile(ctx, serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartPgcChannelPK.GetUserProfile, fromCid: %d, toCid: %d, err: %v", fromCid, toCid, err)
		return err
	}

	receiverId, err := mgr.getChannelAnchor(ctx, toCid)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartPgcChannelPK.GetChannelAnchor, fromCid: %d, toCid: %d, err: %v", fromCid, toCid, err)
		return err
	}
	if receiverId == 0 {
		log.ErrorWithCtx(ctx, "StartPgcChannelPK.GetChannelAnchor, fromCid: %d, toCid: %d, err: target no anchor", fromCid, toCid, err)
		return protocol.NewExactServerError(nil, status.ErrPgcChannelPkNotApplicable)
	}
	receiveProfile, err := mgr.userProfileCli.GetUserProfile(ctx, receiverId)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartPgcChannelPK.GetUserProfile, fromCid: %d, toCid: %d, err: %v", fromCid, toCid, err)
		return err
	}

	go mgr.pushPKInvite(ctx, fromCid, toCid, senderProfile, receiveProfile)
	return nil
}

func (mgr *Mgr) getLimitTimeFrame() (time.Time, time.Time) {
	timeFrame := mgr.bc.GetLimitTimeFrame()
	today := time.Now()
	todayZero := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, time.Local)
	timeFrameStart := todayZero.Add(time.Duration(timeFrame[0]) * time.Second)
	timeFrameEnd := todayZero.Add(time.Duration(timeFrame[1]) * time.Second)
	return timeFrameStart, timeFrameEnd
}

func (mgr *Mgr) AcceptPgcChannelPK(ctx context.Context, myCid, fromCid uint32, accept bool) error {
	applicable, err := mgr.isApplicableChannel(ctx, fromCid, myCid, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptPgcChannelPK.AddTimeFrameTimes, myCid: %d, err: %v", myCid, err)
		return err
	}
	if !applicable {
		return protocol.NewExactServerError(nil, status.ErrPgcChannelPkNotApplicable)
	}

	if accept && mgr.checkPgcChannelGame(ctx, myCid) { // 接受邀请时自己不能处于甩雷游戏中
		return protocol.NewExactServerError(nil, status.ErrPgcChannelPkNotApplicable, "当前玩法过程中不可进行跨房PK")
	}

	if accept {
		// 记录pk次数
		tfs, tfe := mgr.getLimitTimeFrame()
		if time.Now().After(tfs) && time.Now().Before(tfe) { // 在限制时段内
			expire := time.Until(tfe)
			myPKTimes, err := mgr.cache.AddLimitTimeFramePKTimes(ctx, myCid, expire)
			if err != nil {
				log.ErrorWithCtx(ctx, "AcceptPgcChannelPK.AddTimeFrameTimes, myCid: %d, err: %v", myCid, err)
			}
			fromPKTimes, err := mgr.cache.AddLimitTimeFramePKTimes(ctx, fromCid, expire)
			if err != nil {
				log.ErrorWithCtx(ctx, "AcceptPgcChannelPK.AddTimeFrameTimes, fromCid: %d, err: %v", fromCid, err)
			}
			if myPKTimes > mgr.bc.GetLimitPKTimes() || fromPKTimes > mgr.bc.GetLimitPKTimes() {
				return fmt.Errorf("限时时段内pk次数限制")
			}
		}
		log.Infof("AcceptPgcChannelPK fromCid:%d, myCid:%d", fromCid, myCid)
		// 通知pk开始
		err := mgr.cache.SetChannelPKSuccess(ctx, fromCid, myCid)
		if err != nil {
			log.ErrorWithCtx(ctx, "AcceptPgcChannelPK.SetChannelPKSuccess, myCid: %d, fromCid: %d, err: %v",
				myCid, fromCid, err)
			return err
		}
	} else {
		// 记录拒绝pk邀请5min
		err := mgr.cache.SetRefusePKInvite(ctx, fromCid, myCid)
		if err != nil {
			log.ErrorWithCtx(ctx, "AcceptPgcChannelPK.SetRefusePKInvite, myCid: %d, fromCid: %d, err: %v",
				myCid, fromCid, err)
			return err
		}
	}
	// 推送邀请处理结果
	mgr.pushHandlePKInvite(ctx, fromCid, myCid, accept)

	// 删除邀请
	mgr.cache.DelPKInvite(ctx, fromCid, myCid, accept)
	return nil
}

// pushPKInvite 推送pk邀请
func (mgr *Mgr) pushPKInvite(ctx context.Context, fromCid, toCid uint32, senderProfile, receiverProfile *pbApp.UserProfile) error {
	ctx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, 5*time.Second)
	defer cancel()

	channelInfo, err := mgr.channelCli.GetChannelSimpleInfo(ctx, 0, fromCid)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushPKInvite, fromCid: %d, toCid: %d, err: %v", fromCid, toCid, err)
		return err
	}
	msg := &logicpb.PgcChannelPKInviteOpt{
		FromChannel: &logicpb.PgcChannelPKChannelInfo{
			ChannelId:         fromCid,
			ChannelType:       channelInfo.GetChannelType(),
			ChannelIcon:       channelInfo.GetIconMd5(),
			ChannelName:       channelInfo.GetName(),
			BindGuildId:       channelInfo.GetBindId(),
			CreatorUid:        channelInfo.GetCreaterUid(),
			AnchorUserProfile: senderProfile,
		},
		ExpiredSec: mgr.bc.GetInviteExpireSec(),
	}

	msgBytes, _ := proto.Marshal(msg)

	pErr := mgr.pushMsgToChannelWithUser(ctx, toCid, uint32(channelPb.ChannelMsgType_PGC_CHANNEL_PK_INVITE_MSG), "", msgBytes, receiverProfile)
	if pErr != nil {
		log.ErrorWithCtx(ctx, "pushPKInvite.pushMsgToChannelWithUser, fromCid: %d, toCid: %d, err: %v", fromCid, toCid, pErr)
		return pErr
	}

	log.Infof("pushPKInvite.pushMsgToChannelWithUser, fromCid: %d, toCid: %d, msg: %+v", fromCid, toCid, msg)
	return nil
}

func (mgr *Mgr) pushHandlePKInvite(ctx context.Context, fromCid, toCid uint32, accept bool) error {
	channelInfos, err := mgr.channelCli.BatchGetChannelSimpleInfo(ctx, 0, []uint32{fromCid, toCid})
	if err != nil {
		log.ErrorWithCtx(ctx, "pushHandlePKInvite.BatchGetChannelSimpleInfo, fromCid: %d, toCid: %d, err: %v", fromCid, toCid, err)
		return err
	}
	if channelInfos[fromCid] == nil || channelInfos[toCid] == nil {
		return fmt.Errorf("pushHandlePKInvite.BatchGetChannelSimpleInfo, fromCid: %d, toCid: %d, err: missing channelInfo", fromCid, toCid)
	}

	fromChannelInfo := channelInfos[fromCid]
	fromChannel := &logicpb.PgcChannelPKChannelInfo{
		ChannelId:   fromCid,
		ChannelType: fromChannelInfo.GetChannelType(),
		ChannelIcon: fromChannelInfo.GetIconMd5(),
		ChannelName: fromChannelInfo.GetName(),
		BindGuildId: fromChannelInfo.GetBindId(),
		CreatorUid:  fromChannelInfo.GetCreaterUid(),
	}

	toChannelInfo := channelInfos[toCid]
	toChannel := &logicpb.PgcChannelPKChannelInfo{
		ChannelId:   toCid,
		ChannelType: toChannelInfo.GetChannelType(),
		ChannelIcon: toChannelInfo.GetIconMd5(),
		ChannelName: toChannelInfo.GetName(),
		BindGuildId: toChannelInfo.GetBindId(),
		CreatorUid:  toChannelInfo.GetCreaterUid(),
	}

	msg := &logicpb.PgcChannelPKInviteRespOpt{
		FromChannel: fromChannel,
		ToChannel:   toChannel,
		Accept:      accept,
	}

	receiverId := mgr.cache.GetPKInviteSender(ctx, fromCid)
	if receiverId == 0 {
		return fmt.Errorf("pushHandlePKInvite.GetPKInviteSender, err: get serderId 0")
	}
	receiverProfile, err := mgr.userProfileCli.GetUserProfile(ctx, receiverId)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushHandlePKInvite.GetUserProfile, fromCid: %d, toCid: %d, err: %v", fromCid, toCid, err)
		return err
	}
	if !accept {
		msg.RejectedText = status.MessageFromCode(status.ErrPgcChannelPkInviteHadRefuse) //拒绝文案
	}
	msgBytes, _ := proto.Marshal(msg)
	if !accept { // 拒绝推给发起者
		pErr := mgr.pushMsgToChannelWithUser(ctx, fromCid, uint32(channelPb.ChannelMsgType_PGC_CHANNEL_PK_INVITE_RESP), "", msgBytes, receiverProfile)
		if pErr != nil {
			log.ErrorWithCtx(ctx, "pushHandlePKInvite.pushMsgToChannelWithUser, fromCid: %d, toCid: %d, err: %v", fromCid, toCid, pErr)
			return pErr
		}
	} else { // 接受房间广播
		pErr := mgr.pushMsgToChannel(ctx, fromCid, uint32(channelPb.ChannelMsgType_PGC_CHANNEL_PK_INVITE_RESP), "", msgBytes)
		if pErr != nil {
			log.ErrorWithCtx(ctx, "pushHandlePKInvite.pushMsgToChannel, fromCid: %d, toCid: %d, err: %v", fromCid, toCid, pErr)
			return pErr
		}
		pErr = mgr.pushMsgToChannel(ctx, toCid, uint32(channelPb.ChannelMsgType_PGC_CHANNEL_PK_INVITE_RESP), "", msgBytes)
		if pErr != nil {
			log.ErrorWithCtx(ctx, "pushHandlePKInvite.pushMsgToChannel, fromCid: %d, toCid: %d, err: %v", fromCid, toCid, pErr)
			return pErr
		}
	}

	log.Infof("pushHandlePKInvite. fromCid: %d, toCid: %d, msg: %+v", fromChannel, toChannel, msg)
	return nil
}

func (mgr *Mgr) SetPKSwitch(ctx context.Context, channelId uint32, pkSwitch uint32) error {
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return fmt.Errorf("SetPKSwitch, channelId: %d, error: get serviceInfo fali", channelId)
	}

	isPKScheme, err := mgr.isPKChannelScheme(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetPKSwitch.isPKChannelScheme, channelId: %d, err; %v", channelId, err)
		return err
	}
	if !isPKScheme {
		return protocol.NewExactServerError(nil, status.ErrPgcChannelPkNotApplicable)
	}

	isPKSwitchOperator, err := mgr.isPKSwitchOperator(ctx, serviceInfo.UserID, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetPKSwitch.isPKSwitchOperator, channelId: %d, err: %v", channelId, err)
		return err
	}
	if !isPKSwitchOperator {
		return fmt.Errorf("no premission")
	}

	err = mgr.store.UpdateChannelPKSwitch(ctx, channelId, pkSwitch)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetPKSwitch.UpdateChannelPKSwitch, channelId: %d, pkSwitch: %d, err: %v", channelId, pkSwitch, err)
		return err
	}
	// 同步设置缓存
	mgr.cache.SetChannelPKSwitch(ctx, channelId, pkSwitch)

	return nil
}

func (mgr *Mgr) GetPKSwitch(ctx context.Context, channelId uint32) (uint32, error) {
	pkSwitch, err := mgr.cache.GetChannelPKSwitch(ctx, channelId)
	if err != nil {
		pkSwitch, err = mgr.store.GetChannelPKSwitch(ctx, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPKSwitch.store.GetChannelPKSwitch, channelId: %d, err: %v", channelId, err)
			return 0, err
		}
		mgr.cache.SetChannelPKSwitch(ctx, channelId, pkSwitch)
	}
	return pkSwitch, nil
}

func (mgr *Mgr) isPKChannelScheme(ctx context.Context, channelId uint32) (bool, error) {
	channelScheme, err := mgr.channelSchemeCli.GetCurChannelSchemeInfo(ctx, channelId, uint32(channel.ChannelBindType_GUILD_PUBLIC_FUN_BIND_CHANNEL))
	if err != nil {
		return false, err
	}
	if channelScheme.GetSchemeInfo().GetSchemeSvrDetailType() !=
		uint32(channelschemeconfmgr.SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_FUN) &&
		channelScheme.GetSchemeInfo().GetSchemeSvrDetailType() != uint32(channelschemeconfmgr.SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_PIA_XI) &&
		channelScheme.GetSchemeInfo().GetSchemeSvrDetailType() != uint32(channelschemeconfmgr.SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_PIA_V2) {
		return false, fmt.Errorf("isPKChannelScheme, channelId: %d, err: unsupport channel scheme", channelId)
	}
	return true, nil
}

func (mgr *Mgr) isCompere(ctx context.Context, uid, channelId uint32) (bool, error) {
	micList, err := mgr.channelMicCli.GetMicrList(ctx, channelId, uid)
	if err != nil {
		return false, err
	}
	for _, mic := range micList.AllMicList {
		if mic.GetMicId() == 1 {
			if mic.GetMicUid() == uid {
				return true, nil
			}
			break
		}
	}
	return false, nil
}

func (mgr *Mgr) isPKSwitchOperator(ctx context.Context, uid, channelId uint32) (bool, error) {
	adminList, err := mgr.channelCli.GetChannelAdmin(ctx, uid, channelId)
	if err != nil {
		return false, err
	}
	for _, admin := range adminList {
		if (admin.GetAdminRole() == uint32(channel.ChannelAdminRole_CHANNEL_OWNER) ||
			admin.GetAdminRole() == uint32(channel.ChannelAdminRole_CHANNEL_ADMIN_SUPER)) &&
			admin.GetUid() == uid {
			return true, nil
		}
	}
	return false, nil
}

func splitCid(key string) (uint32, uint32) {
	parts := strings.Split(key, "_")
	if len(parts) < 2 {
		return 0, 0
	}
	fromCid, _ := strconv.Atoi(parts[0])
	toCid, _ := strconv.Atoi(parts[1])
	return uint32(fromCid), uint32(toCid)
}

func (mgr *Mgr) AutoRefuseExpirePKInvite() {
	log.Infof("AutoRefuseExpirePKInvite start")
	ctx, cancel := common.GenAsyncCtx(5 * time.Second)
	defer cancel()

	pairs, err := mgr.cache.PKInviteTimerSetPop(ctx, mgr.bc.GetInviteExpireSec())
	if err != nil {
		log.Errorf("AutoRefuseExpirePKInvite, err: %v", err)
		return
	}
	for _, pair := range pairs {
		fromCid, toCid := splitCid(pair)
		if fromCid == 0 || toCid == 0 {
			continue
		}

		mgr.cache.SetRefusePKInvite(ctx, fromCid, toCid)                       // 设置拒绝缓存
		go mgr.pushHandlePKInvite(context.Background(), fromCid, toCid, false) // 推送拒绝消息
	}

}

func (mgr *Mgr) isMaskedPking(ctx context.Context) (bool, error) {
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return false, fmt.Errorf("isMaskedPking, err: get serviceInfo fail")
	}
	maskedPkConf, err := mgr.maskedPKCli.GetChannelMaskedPKCurrConf(ctx, serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "isMaskedPking, err: %v", err)
		return false, err
	}
	nowTs := uint32(time.Now().Unix())
	if nowTs >= maskedPkConf.GetConf().GetBeginTs() && nowTs <= maskedPkConf.GetConf().GetEndTs() {
		return true, nil
	}
	return false, nil
}

func (mgr *Mgr) getChannelAnchors(ctx context.Context, channelIds []uint32) (map[uint32]uint32, error) {
	rs := make(map[uint32]uint32)
	for _, cid := range channelIds {
		micList, err := mgr.channelMicCli.GetMicrList(ctx, cid, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "getChannelAnchors, cid: %d, err: %v", cid, err)
			continue
		}
		for _, item := range micList.GetAllMicList() {
			if item.GetMicId() == 1 {
				rs[cid] = item.GetMicUid()
				break
			}
		}
	}
	return rs, nil
}

func (mgr *Mgr) getChannelAnchor(ctx context.Context, channelId uint32) (uint32, error) {
	rs, err := mgr.getChannelAnchors(ctx, []uint32{channelId})
	if err != nil {
		return 0, err
	}
	return rs[channelId], nil
}
