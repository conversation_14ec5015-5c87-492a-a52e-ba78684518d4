package mgr

import (
	"github.com/golang/mock/gomock"
	mockchannel "golang.52tt.com/clients/mocks/channel"
	mockChannelScheme "golang.52tt.com/clients/mocks/channel-scheme"
	mockchannelstats "golang.52tt.com/clients/mocks/channel-stats"
	mockchannelguild "golang.52tt.com/clients/mocks/channelguild"
	mockchannelmic "golang.52tt.com/clients/mocks/channelmic"
	mockChannelOl "golang.52tt.com/clients/mocks/channelol"
	mockEntertainmentRB "golang.52tt.com/clients/mocks/entertainmentrecommendback"
	mockGuild "golang.52tt.com/clients/mocks/guild"
	mockMaskedPkSvr "golang.52tt.com/clients/mocks/masked-pk-svr"
	mockpush "golang.52tt.com/clients/mocks/push-notification/v2"
	mockseqgen "golang.52tt.com/clients/mocks/seqgen/v2"
	mockswitchschemechecker "golang.52tt.com/clients/mocks/switch-scheme-checker"
	mocktimeline "golang.52tt.com/clients/mocks/timeline"
	mockttrevoperation "golang.52tt.com/clients/mocks/tt-rev-operation"
	mockUserProfile "golang.52tt.com/clients/mocks/user-profile-api"
	channel_live_show_list "golang.52tt.com/protocol/services/channel-live-show-list"
	mockPgcChannelGame "golang.52tt.com/protocol/services/mocks"

	"golang.52tt.com/services/tt-rev/pgc-channel-pk/internal/conf"
	"golang.52tt.com/services/tt-rev/pgc-channel-pk/internal/mocks"
)

var (
	sc                   *conf.StartConfig
	bc                   *mocks.MockIBusinessConfManager
	mockStore            *mocks.MockIStore
	mockcache            *mocks.MockICache
	mockPushCli          *mockpush.MockIClient
	mockchannelCli       *mockchannel.MockIClient
	mockchannelMicCli    *mockchannelmic.MockIClient
	mockchannelSchemeCli *mockChannelScheme.MockIClient
	mockchannelGuildCli  *mockchannelguild.MockIClient
	mockuserProfileCli   *mockUserProfile.MockIClient
	mockchannelStatsCli  *mockchannelstats.MockIClient
	mockentertaimentCli  *mockEntertainmentRB.MockIClient
	mockSeqGenClient     *mockseqgen.MockIClient
	mockTimelineClient   *mocktimeline.MockIClient
	mockGuildCli         *mockGuild.MockIClient
	mockChannelOlCli     *mockChannelOl.MockIClient
	mockMaskedPKCli      *mockMaskedPkSvr.MockIClient

	mockPgcChannelGameCli *mockPgcChannelGame.MockPgcChannelGameClient
	mockTTRevOperationCli *mockttrevoperation.MockIClient
	mockOfferRoomCli      *mockswitchschemechecker.MockSwitchSchemeCheckerClient
	mockChannelLiveShowListCli *channel_live_show_list.MockChannelLiveShowListClient
)

func NewTestMgr(ctrl *gomock.Controller) *Mgr {
	sc = &conf.StartConfig{}
	bc = mocks.NewMockIBusinessConfManager(ctrl)

	mockStore = mocks.NewMockIStore(ctrl)
	mockcache = mocks.NewMockICache(ctrl)
	mockchannelCli = mockchannel.NewMockIClient(ctrl)
	mockchannelMicCli = mockchannelmic.NewMockIClient(ctrl)
	mockchannelSchemeCli = mockChannelScheme.NewMockIClient(ctrl)
	mockchannelGuildCli = mockchannelguild.NewMockIClient(ctrl)
	mockuserProfileCli = mockUserProfile.NewMockIClient(ctrl)
	mockchannelStatsCli = mockchannelstats.NewMockIClient(ctrl)
	mockentertaimentCli = mockEntertainmentRB.NewMockIClient(ctrl)
	mockSeqGenClient = mockseqgen.NewMockIClient(ctrl)
	mockTimelineClient = mocktimeline.NewMockIClient(ctrl)
	mockGuildCli = mockGuild.NewMockIClient(ctrl)
	mockPushCli = mockpush.NewMockIClient(ctrl)
	mockChannelOlCli = mockChannelOl.NewMockIClient(ctrl)
	mockMaskedPKCli = mockMaskedPkSvr.NewMockIClient(ctrl)
	mockPgcChannelGameCli = mockPgcChannelGame.NewMockPgcChannelGameClient(ctrl)
	mockTTRevOperationCli = mockttrevoperation.NewMockIClient(ctrl)
	mockOfferRoomCli = mockswitchschemechecker.NewMockSwitchSchemeCheckerClient(ctrl)
	mockChannelLiveShowListCli = channel_live_show_list.NewMockChannelLiveShowListClient(ctrl)

	return &Mgr{
		sc:                sc,
		bc:                bc,
		store:             mockStore,
		cache:             mockcache,
		PushCli:           mockPushCli,
		channelCli:        mockchannelCli,
		channelMicCli:     mockchannelMicCli,
		channelSchemeCli:  mockchannelSchemeCli,
		channelGuildCli:   mockchannelGuildCli,
		userProfileCli:    mockuserProfileCli,
		channelStatsCli:   mockchannelStatsCli,
		entertaimentCli:   mockentertaimentCli,
		SeqGenClient:      mockSeqGenClient,
		TimelineClient:    mockTimelineClient,
		GuildCli:          mockGuildCli,
		channelOlCli:      mockChannelOlCli,
		maskedPKCli:       mockMaskedPKCli,
		pgcChannelGameCli: mockPgcChannelGameCli,
		ttRevOperationCli: mockTTRevOperationCli,
		offerRoomCli:      mockOfferRoomCli,
		channelLiveShowListCli: mockChannelLiveShowListCli,
	}
}
