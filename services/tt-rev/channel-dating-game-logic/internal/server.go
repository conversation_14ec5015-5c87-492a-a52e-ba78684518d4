package internal

import (
	"context"
	riskMngApi "golang.52tt.com/clients/risk-mng-api"
	basePb "golang.52tt.com/protocol/app"
	riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
	"google.golang.org/grpc/codes"

	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	channelCli "golang.52tt.com/clients/channel"
	"golang.52tt.com/clients/channel-dating-game"
	channelmsgexpress "golang.52tt.com/clients/channel-msg-express"
	channelapi_go "golang.52tt.com/clients/channelapi-go"
	"golang.52tt.com/clients/channelmic"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/darkserver"
	"golang.52tt.com/clients/greenbaba"
	"golang.52tt.com/clients/guild"
	headImage "golang.52tt.com/clients/headimage"
	"golang.52tt.com/clients/nobility"
	"golang.52tt.com/clients/officialcert"
	push "golang.52tt.com/clients/push-notification/v2"
	revenueapigo "golang.52tt.com/clients/revenue-api-go"
	userprofileapi "golang.52tt.com/clients/user-profile-api"
	youknowwho "golang.52tt.com/clients/you-know-who"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	pbSvr "golang.52tt.com/protocol/services/channel-dating-game"
	pbChannelApi "golang.52tt.com/protocol/services/channelapi-go"
	channelmicPB "golang.52tt.com/protocol/services/channelmicsvr"
	chSvrPb "golang.52tt.com/protocol/services/channelsvr"
	"golang.52tt.com/protocol/services/demo/echo"
	gpb "golang.52tt.com/protocol/services/greenBaba"
	offcialCert "golang.52tt.com/protocol/services/officialcert"
	revenue_api_go "golang.52tt.com/protocol/services/revenue-api-go"
	"golang.52tt.com/services/tt-rev/channel-dating-game-logic/internal/conf"
)

type StartConfig struct {
	// [optional] from startup arguments

	// from config file

}

const (
	VIP_MIC_ID                 = 10
	CHANNEL_OWNER_MIC_ID       = 1
	PERMISSION2_CAHNNEL_MANAGE = 1 << 1
)

var DisAbleMicList = []uint32{2, 3, 4, 5, 6, 7, 8, 9}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	channelDatingGameClient := channeldatinggame.NewClient()
	userProfileCli, _ := userprofileapi.NewClient()
	ukwCli, _ := youknowwho.NewClient()
	channelClient := channelCli.NewClient()
	channelOLClient := channelol.NewClient()
	darkClient, _ := darkserver.NewClient()
	channelMicClient := channelmic.NewIClient()
	pushClient := push.NewIClient()
	channelApiCli := channelapi_go.NewIClient()
	greenBaBaClient := greenbaba.NewIClient()
	channelMsgClient := channelmsgexpress.NewIClient()
	guildCli := guild.NewIClient()
	headImageCli := headImage.NewIClient()
	officialCertCli := officialcert.NewIClient()
	nobilityCli := nobility.NewIClient()
	revenueApiGoCli := revenueapigo.NewIClient()
	riskMngApiCli, _ := riskMngApi.NewClient()

	bc, err := conf.NewBusinessConfManager()
	if err != nil {
		log.ErrorWithCtx(ctx, "biz config new fail: %v", err)
	}

	return &Server{
		channelDatingGameClient: channelDatingGameClient,
		userProfileCli:          userProfileCli,
		ukwCli:                  ukwCli,
		channelClient:           channelClient,
		channelOLClient:         channelOLClient,
		darkClient:              darkClient,
		channelMicClient:        channelMicClient,
		pushClient:              pushClient,
		channelApiCli:           channelApiCli,
		greenBaBaClient:         greenBaBaClient,
		channelMsgClient:        channelMsgClient,
		guildCli:                guildCli,
		headImageCli:            headImageCli,
		officialCertCli:         officialCertCli,
		nobilityCli:             nobilityCli,
		revenueApiGoCli:         revenueApiGoCli,
		riskMngApiCli:           riskMngApiCli,

		bc: bc,
	}, nil
}

type Server struct {
	channelDatingGameClient channeldatinggame.IClient
	userProfileCli          userprofileapi.IClient
	ukwCli                  youknowwho.IClient
	channelClient           channelCli.IClient
	channelOLClient         channelol.IClient
	darkClient              darkserver.IClient
	channelMicClient        channelmic.IClient
	pushClient              push.IClient
	channelApiCli           channelapi_go.IClient
	greenBaBaClient         greenbaba.IClient
	channelMsgClient        channelmsgexpress.IClient
	guildCli                guild.IClient
	headImageCli            headImage.IClient
	officialCertCli         officialcert.IClient
	nobilityCli             nobility.IClient
	revenueApiGoCli         revenueapigo.IClient
	riskMngApiCli           riskMngApi.IClient

	bc conf.IBusinessConfManager
}

func (s *Server) ShutDown() {
	log.Infof("ShutDown...")
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) SelectLikeDatingUser(ctx context.Context, req *channel.SelectLikeDatingUserReq) (*channel.SelectLikeDatingUserResp, error) {
	out := &channel.SelectLikeDatingUserResp{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "SelectLikeDatingUser ServiceInfoFromContext fail. req:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	targetUid := req.GetTargetUid()
	if targetUid > 0 {
		tureUid, serr := s.ukwCli.GetTrueUidByFake(ctx, req.GetTargetUid())
		if serr != nil {
			log.ErrorWithCtx(ctx, "SelectLikeDatingUser GetTrueUidByFake fail. req:%+v, err:%v", req, serr)
			return out, serr
		}
		targetUid = tureUid
	}
	
	cid, serr := s.channelOLClient.GetUserChannelId(ctx, svrInfo.UserID, svrInfo.UserID)
	if serr != nil {
		log.ErrorWithCtx(ctx, "SelectLikeDatingUser GetUserChannelId fail. req:%+v, err:%v", req, serr)
		return out, serr
	}
	
	_, err := s.channelDatingGameClient.SetUserLikeBeatObj(ctx, cid, svrInfo.UserID, targetUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SelectLikeDatingUser SetUserLikeBeatObj fail. req:%+v, err:%v", req, err)
		return out, err
	}
	out.TargetUid = req.GetTargetUid()
	log.InfoWithCtx(ctx, "SelectLikeDatingUser uid:%d, req:%+v", svrInfo.UserID, req)
	return out, nil
}

func (s *Server) OpenLikeDatingUser(ctx context.Context, req *channel.OpenLikeDatingUserReq) (*channel.OpenLikeDatingUserResp, error) {
	out := &channel.OpenLikeDatingUserResp{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "OpenLikeDatingUser ServiceInfoFromContext fail. req:%+v, uid:%d", req, svrInfo.UserID)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	targetUid, serr := s.ukwCli.GetTrueUidByFake(ctx, req.GetOpenUid())
	if serr != nil {
		log.ErrorWithCtx(ctx, "OpenLikeDatingUser GetTrueUidByFake fail. req:%+v, err:%v", req, serr)
		return out, serr
	}

	_, err := s.channelDatingGameClient.OpenUserLikeBeatObj(ctx, req.GetChannelId(), targetUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "OpenLikeDatingUser OpenUserLikeBeatObj fail. req:%+v, err:%v", req, err)
		return out, err
	}
	log.InfoWithCtx(ctx, "OpenLikeDatingUser uid:%d, req:%v", svrInfo.UserID, req)
	return out, nil
}

func (s *Server) ApplyDatingMic(ctx context.Context, req *channel.ApplyDatingMicReq) (*channel.ApplyDatingMicResp, error) {
	out := &channel.ApplyDatingMicResp{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "ApplyDatingMic ServiceInfoFromContext fail. in:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	uid := svrInfo.UserID
	channelId, serr := s.channelOLClient.GetUserChannelId(ctx, svrInfo.UserID, svrInfo.UserID)
	if serr != nil {
		log.ErrorWithCtx(ctx, "SelectLikeDatingUser GetUserChannelId fail. req:%+v, err:%v", req, serr)
		return out, serr
	}
	
	serr = s.channelDatingGameClient.UserApplyMic(ctx, channelId, uid, req.GetIsCancel())
	if serr != nil {
		log.ErrorWithCtx(ctx, "ApplyDatingMic UserApplyMic uid:%d, channelId:%d err:%v", uid, channelId, serr)
		return out, serr
	}

	resp, err := s.channelDatingGameClient.GetApplyMicUserList(ctx, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyDatingMic uid:%d, channelId:%d err:%v", uid, channelId, err)
		return out, err
	}

	userMap, err := s.userProfileCli.BatchGetUserProfileV2(ctx, resp.GetUidList(), true)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyDatingMic GetUsersMap fail. in:%+v, err:%v", req, err)
		return out, err
	}

	for _, user := range userMap {
		out.ApplyUserList = append(out.ApplyUserList, &channel.ApplyDatingMicUserInfo{
			Uid:         user.GetUid(),
			Account:     user.GetAccount(),
			Sex:         user.GetSex(),
			NickName:    user.GetNickname(),
			UserProfile: user,
		})
	}

	out.IsCancel = req.IsCancel
	log.InfoWithCtx(ctx, "ApplyDatingMic uid:%d, req:%+v, resp:%+v", svrInfo.UserID, req, out)
	return out, nil
}

func (s *Server) GetApplyDatingMicUserList(ctx context.Context, req *channel.GetApplyDatingMicUserListReq) (*channel.GetApplyDatingMicUserListResp, error) {
	out := &channel.GetApplyDatingMicUserListResp{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetApplyDatingMicUserList ServiceInfoFromContext fail. req:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	
	channelId, serr := s.channelOLClient.GetUserChannelId(ctx, svrInfo.UserID, svrInfo.UserID)
	if serr != nil {
		log.ErrorWithCtx(ctx, "SelectLikeDatingUser GetUserChannelId fail. req:%+v, err:%v", req, serr)
		return out, serr
	}
	
	resp, err := s.channelDatingGameClient.GetApplyMicUserList(ctx, channelId, svrInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetApplyDatingMicUserList GetApplyMicUserList fail. req:%+v, err:%v", req, err)
		return out, err
	}

	userMap, err := s.userProfileCli.BatchGetUserProfileV2(ctx, resp.GetUidList(), true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetApplyDatingMicUserList ServiceInfoFromContext fail. req:%+v, err:%v", req, err)
		return out, err
	}

	for _, user := range userMap {
		out.ApplyUserList = append(out.ApplyUserList, &channel.ApplyDatingMicUserInfo{
			Uid:         user.GetUid(),
			Account:     user.GetAccount(),
			Sex:         user.GetSex(),
			NickName:    user.GetNickname(),
			UserProfile: user,
		})
	}
	return out, nil
}

func (s *Server) SetChannelDatingGamePhase(ctx context.Context, req *channel.SetDatingGamePhaseReq) (*channel.SetDatingGamePhaseResp, error) {
	out := &channel.SetDatingGamePhaseResp{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetApplyDatingMicUserList ServiceInfoFromContext fail. req:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	uid := svrInfo.UserID
	channelId, serr := s.channelOLClient.GetUserChannelId(ctx, svrInfo.UserID, svrInfo.UserID)
	if serr != nil {
		log.ErrorWithCtx(ctx, "SelectLikeDatingUser GetUserChannelId fail. req:%+v, err:%v", req, serr)
		return out, serr
	}
	
	err := s.checkCanSetPhase(ctx, req.GetChannelId(), svrInfo.UserID, req.GetPhase())
	if err != nil {
		log.ErrorWithCtx(ctx, "checkCanSetPhase uid:%d, channelId:%d err:%v", uid, channelId, err)
		return out, err
	}

	resp, err := s.channelDatingGameClient.SetGamePhase(ctx, channelId, uid, req.GetPhase())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetGamePhase uid:%d, channelId:%d err:%v", uid, channelId, err)
		return out, err
	}

	if req.GetPhase() == uint32(channel.DatingGamePhaseType_DATING_GAME_PHASE_FIN) {
		go s.handleFinPhase(ctx, channelId, uid)
	} else if req.GetPhase() == uint32(channel.DatingGamePhaseType_DATING_GAME_PHASE_DISCUSSION) &&
		resp.GetFromPhase() == uint32(channel.DatingGamePhaseType_DATING_GAME_PHASE_FIN) {
		err = s.handleInitPhase(ctx, channelId, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetGamePhase handleInitPhase uid:%d, channelId:%d err:%v", uid, channelId, err)
			return out, err
		}
	} else {
		log.DebugWithCtx(ctx, "SetGamePhase channelId:%d, uid:%d", channelId, uid)
	}

	err = s.notifyPhaseChange(ctx, channelId, resp.GetFromPhase(), req.GetPhase())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetGamePhase notifyPhaseChange uid:%d, channelId:%d err:%v", uid, channelId, err)
		return out, err
	}

	out.ChannelId = req.GetChannelId()
	out.FromPhase = resp.GetFromPhase()
	out.TargetPhase = req.GetPhase()
	log.InfoWithCtx(ctx, "SetChannelDatingGamePhase uid:%d, req:%+v, resp:%+v", svrInfo.UserID, req, out)
	return out, nil
}

func (s *Server) notifyPhaseChange(ctx context.Context, channelId, fromPhase, toPhase uint32) error {
	opt := &channel.DatingGamePhasePushNotifyOpt{
		ChannelId: channelId,
		FromPhase: fromPhase,
		ToPhase:   toPhase,
	}

	msgByte, err := proto.Marshal(opt)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetGamePhase uid:%d, channelId:%d err:%v", channelId, err)
		return err
	}

	return s.pushMsgToChannelWithUser(ctx, channelId, uint32(channel.ChannelMsgType_CHANNEL_DATING_PHASE_CHANGE), "相亲游戏阶段变化", msgByte, nil)
}

func (s *Server) handleFinPhase(ctx context.Context, channelId, uid uint32) error {
	ctx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Second*3)
	defer cancel()

	micResp, err := s.channelMicClient.GetMicrList(ctx, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleFinPhase GetMicrList uid:%d err:%v", uid, err)
		return err
	}

	kickUidList := make([]uint32, 0)
	for _, mic := range micResp.GetAllMicList() {
		if mic.GetMicUid() == 0 {
			continue
		}

		if mic.GetMicId() == CHANNEL_OWNER_MIC_ID {
			continue
		}
		kickUidList = append(kickUidList, mic.GetMicUid())
	}

	//踢麦上用户
	_, serr := s.channelApiCli.KickOutMicSpace(ctx, &pbChannelApi.KickOutMicReq{
		OpUid:         uid,
		ChannelId:     channelId,
		TargetUidList: kickUidList,
		BanSecond:     0,
		SwitchFlag:    0,
		Source:        "被设置为旁听",
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "kickOutChannelMic uid:%d err:%v", uid, serr)
		return serr
	}

	//锁麦
	resp, serr := s.channelMicClient.BatchSetChannelMicSpaceStatus(ctx, uid, uid, channelId,
		uint32(channel.MicrSpace_MIC_SPACE_DISABLE), DisAbleMicList)
	if serr != nil {
		log.ErrorWithCtx(ctx, "BatchSetChannelMicSpaceStatus uid:%d err:%v", uid, serr)
		return serr
	}

	log.DebugWithCtx(ctx, "BatchSetChannelMicSpaceStatus kick:%+v, all:%+v", resp.GetKickedMicrList(), resp.GetAllMicList())
	for _, val := range resp.GetAllMicList() {
		if val.GetMicId() == CHANNEL_OWNER_MIC_ID {
			continue
		}
		s.pushMicListStatus(ctx, resp.GetAllMicList(), channelId, val.GetMicUid(), val.GetMicId())
	}
	return nil
}

func (s *Server) handleInitPhase(ctx context.Context, channelId, uid uint32) error {
	micResp, err := s.channelMicClient.GetMicrList(ctx, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleInitPhase GetMicrList uid:%d err:%v", uid, err)
		return err
	}

	var initMemberList []uint32
	for _, mic := range micResp.GetAllMicList() {
		if mic.GetMicUid() > 0 {
			initMemberList = append(initMemberList, mic.GetMicUid())
		}
	}

	if len(initMemberList) == 0 {
		log.WarnWithCtx(ctx, "handleInitPhase GetAllMicList is empty, channel:%d, uid:%d", channelId, uid)
		return nil
	}

	userMap, err := s.userProfileCli.BatchGetUserProfileV2(ctx, initMemberList, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserProfileV2 uid:%d err:%v", uid, err)
		return err
	}

	req := &pbSvr.InitDatingMemberReq{
		ChannelId:  channelId,
		MemberList: nil,
	}

	for _, mic := range micResp.GetAllMicList() {
		if mic.GetMicUid() > 0 {
			req.MemberList = append(req.MemberList, &pbSvr.DatingMember{
				Uid:   mic.GetMicUid(),
				MicId: mic.GetMicId(),
				Sex:   userMap[mic.GetMicUid()].GetSex(),
			})
		}
	}

	_, serr := s.channelDatingGameClient.InitDatingMember(ctx, req)
	if serr != nil {
		log.ErrorWithCtx(ctx, "InitDatingMember uid:%d err:%v", uid, serr)
		return serr
	}
	return nil
}

func (s *Server) checkCanSetPhase(ctx context.Context, channelId, uid, targetPhase uint32) error {
	// 检查房间的类型是否是公会公开厅
	channelInfo, err := s.channelClient.GetChannelSimpleInfo(ctx, uid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkCanSetPhase uid:%d, channelId:%d err:%v", uid, channelId, err)
		return err
	}

	if channelInfo.GetChannelType() != uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该类型房间不支持相亲模式")
	}

	// 检查房间的当前麦位模式是否是 相亲模式
	micResp, err := s.channelMicClient.GetMicrList(ctx, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkCanSetPhase GetMicrList uid:%d err:%v", uid, err)
		return err
	}

	if micResp.GetMicrMode() != uint32(channel.EChannelMicMode_DATING_MIC_SPACE_MODE) {
		return protocol.NewExactServerError(nil, status.ErrChannelDatinggameNotVip, "只能在相亲模式中进行阶段设置")
	}

	// 检查操作者是否在1号麦
	onMicUidList := make([]uint32, 0)
	for _, mic := range micResp.GetAllMicList() {
		if mic.GetMicId() == CHANNEL_OWNER_MIC_ID && uid != mic.GetMicUid() {
			return protocol.NewExactServerError(nil, status.ErrChannelNoPermission, "只有主持人有权限进行阶段设置")
		}

		if mic.GetMicId() != CHANNEL_OWNER_MIC_ID || mic.GetMicId() != VIP_MIC_ID {
			onMicUidList = append(onMicUidList, mic.GetMicUid())
		}
	}

	//各阶段要求
	game, serr := s.channelDatingGameClient.GetDatingGameCurInfo(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkCanSetPhase get game failed channelId:%d, err:%v", channelId, err)
		return serr
	}

	if game.GetPhase() == uint32(channel.DatingGamePhaseType_DATING_GAME_PHASE_DISCUSSION) &&
		targetPhase == uint32(channel.DatingGamePhaseType_DATING_GAME_PHASE_CHOICE) &&
		len(onMicUidList) < 2 {
		return protocol.NewExactServerError(nil, status.ErrChannelDatinggamePhaseError, "至少需要有两名玩家才能进入心动选择阶段")
	}

	if game.GetPhase() == uint32(channel.DatingGamePhaseType_DATING_GAME_PHASE_PUBLISH) &&
		targetPhase == uint32(channel.DatingGamePhaseType_DATING_GAME_PHASE_FIN) {
		var likeBeatCount uint32
		var openLikeCount uint32
		for _, user := range game.GetLikeBeatList() {
			if user.GetSelectStatus() && user.GetUid() > 0 {
				likeBeatCount++
			}
		}

		for _, user := range game.GetOpenLikeUserList() {
			if user.GetOpenUid() > 0 {
				openLikeCount++
			}
		}

		if likeBeatCount != openLikeCount {
			return protocol.NewExactServerError(nil, status.ErrChannelDatinggamePhaseError, "还有用户的心动对象没有公布")
		}
	}
	return nil
}

func (s *Server) CheckChannelDatingGameEntry(ctx context.Context, req *channel.CheckChannelDatingGameEntryReq) (*channel.CheckChannelDatingGameEntryResp, error) {
	out := &channel.CheckChannelDatingGameEntryResp{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "CheckChannelDatingGameEntry ServiceInfoFromContext fail. req:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	uid := svrInfo.UserID
	channelId, serr := s.channelOLClient.GetUserChannelId(ctx, svrInfo.UserID, svrInfo.UserID)
	if serr != nil {
		log.ErrorWithCtx(ctx, "SelectLikeDatingUser GetUserChannelId fail. req:%+v, err:%v", req, serr)
		return out, serr
	}
	
	if channelId == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "房间ID不能为0")
	}

	isOpen, level, err := s.channelDatingGameClient.CheckDatingGameEntry(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckChannelDatingGameEntry uid:%d, channelId:%d err:%v", uid, channelId, err)
		return out, err
	}

	out.IsOpen = isOpen
	out.Level = level
	return out, nil
}

func (s *Server) checkChannelBanned(ctx context.Context, uid, cid uint32) (bool, error) {
	bannedStat, err := s.greenBaBaClient.GetCurrBannedStatById(ctx, uid, uint32(gpb.ENUM_TARGET_TYPE_E_TARGET_TYPE_CHANNEL), cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkChannelBanned failed channelId(%v) userId(%v) err(%v)", cid, uid, err)
		//greenbaba 服务错误，不影响业务使用
		return false, nil
	}
	for _, elem := range bannedStat {
		if elem.GetTargetType() != uint32(gpb.ENUM_TARGET_TYPE_E_TARGET_TYPE_CHANNEL) || elem.Id != cid {
			continue
		}
		if elem.GetBannedType() == uint32(gpb.ENUM_BANNED_TYPE_E_BANNED_CHANNEL) && elem.RemainSecond > 0 {
			return true, nil
		}
	}

	return false, nil
}

func (s *Server) checkCanHoldVipMic(ctx context.Context, uid, channelId uint32, req *channel.DatingGameHoldVipMicReq) (out *channel.DatingGameHoldVipMicResp, err error) {
	out = &channel.DatingGameHoldVipMicResp{
		BaseResp: &basePb.BaseResp{},
	}
	banned, _ := s.checkChannelBanned(ctx, uid, channelId)
	if banned {
		log.ErrorWithCtx(ctx, "DatingGameHoldVipMic ChannelBanned  uid:%d, channelId:%d", uid, channelId)
		return out, protocol.NewExactServerError(nil, status.ErrChannelUserChannelMuted)
	}

	// 检查是否被禁言 被禁言者 不能上麦
	mute, err := s.channelClient.CheckUserIsMute(ctx, uid, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "DatingGameHoldVipMic uid:%d, channelId:%d err:%v", uid, channelId, err)
		return out, err
	}

	if mute {
		log.ErrorWithCtx(ctx, "DatingGameHoldVipMic muted  uid:%d, channelId:%d err:%v", uid, channelId, err)
		return out, protocol.NewExactServerError(nil, status.ErrChannelUserChannelMuted)
	}

	checkReq := &riskMngApiPb.CheckReq{
		Scene: "CHANNEL_MIC_HOLD_SIMPLE",
		SourceEntity: &riskMngApiPb.Entity{
			// 必选
			Uid: uid,
		},
	}
	checkResp, err := s.riskMngApiCli.CheckHelper(ctx, checkReq, req.GetBaseReq())
	if err != nil {
		// 系统错误，风控非关键路径，可忽略系统错误
		log.ErrorWithCtx(ctx, "checkCanHoldVipMic risk-mng-api.Check failed, req:%+v, err: %v", checkReq, err)
	}

	// 命中风控不拦截
	if checkResp.GetErrCode() < 0 {
		log.InfoWithCtx(ctx, "checkCanHoldVipMic risk-mng-api.Check hit, req:%+v, resp:%+v", checkReq, checkResp)
		out.BaseResp.ErrInfo = checkResp.GetErrInfo()
		return out, protocol.NewExactServerError(codes.OK, int(checkResp.GetErrCode()))
	}

	//判断是否是黑产,如果是黑产不能上麦
	hit, err := s.darkClient.UserBehaviorCheck(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "UserBehaviorCheck err:%v", err)
		return out, err
	}
	if 0 != hit {
		return out, protocol.NewExactServerError(nil, status.ErrBlacklistCheckNotPass)
	}
	return out, nil
}
func (s *Server) DatingGameHoldVipMic(ctx context.Context, req *channel.DatingGameHoldVipMicReq) (*channel.DatingGameHoldVipMicResp, error) {
	out := &channel.DatingGameHoldVipMicResp{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "DatingGameHoldVipMic ServiceInfoFromContext fail. req:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	uid := svrInfo.UserID
	channelId, serr := s.channelOLClient.GetUserChannelId(ctx, svrInfo.UserID, svrInfo.UserID)
	if serr != nil {
		log.ErrorWithCtx(ctx, "SelectLikeDatingUser GetUserChannelId fail. req:%+v, err:%v", req, serr)
		return out, serr
	}
	
	log.DebugWithCtx(ctx, "DatingGameHoldVipMic uid:%d, channelId:%d", uid, channelId)
	//上VIP麦前置条件检测
	out, err := s.checkCanHoldVipMic(ctx, uid, channelId, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkCanHoldVipMic GetMicrList uid:%d err:%v", uid, err)
		return out, err
	}

	micResp, err := s.channelMicClient.GetMicrList(ctx, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "DatingGameHoldVipMic GetMicrList uid:%d err:%v", uid, err)
		return out, err
	}

	// 检查房间的当前麦位模式是否是 相亲模式
	if micResp.GetMicrMode() != uint32(channel.EChannelMicMode_DATING_MIC_SPACE_MODE) {
		return out, protocol.NewExactServerError(nil, status.ErrChannelDatinggameNotVip, "只能在相亲模式中上麦VIP坐席")
	}

	// 检查10号vip麦是否已经有人
	var needKickMicUid uint32
	var myMicId uint32
	for _, mic := range micResp.GetAllMicList() {
		if mic.GetMicId() == VIP_MIC_ID {
			if mic.GetMicUid() == uid {
				return out, nil
			} else {
				needKickMicUid = mic.GetMicUid()
			}
		}

		if mic.GetMicUid() == uid {
			myMicId = mic.GetMicId()
		}
	}

	_, err = s.channelDatingGameClient.ConfirmVipHoldMic(ctx, req.GetChannelId(), uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "DatingGameHoldVipMic GetMicrList uid:%d err:%v", uid, err)
		return out, err
	}

	userMap, err := s.userProfileCli.BatchGetUserProfileV2(ctx, []uint32{uid, needKickMicUid}, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "DatingGameHoldVipMic BatchGetUserProfileV2 uid:%d err:%v", uid, err)
		return out, err
	}

	//有人在麦上,踢下去
	if needKickMicUid > 0 {
		_, err = s.channelApiCli.KickOutMicSpace(ctx, &pbChannelApi.KickOutMicReq{
			OpUid:         uid,
			ChannelId:     channelId,
			TargetUidList: []uint32{needKickMicUid},
			Source:        "被换下VIP坐席",
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "KickOutMicSpace uid:%d err:%v", uid, err)
		}
		log.InfoWithCtx(ctx, "KickOutMicSpace opUid:%d, kickUid:%d, channelId:%d", uid, needKickMicUid, channelId)
	}

	//自动上麦
	if myMicId == 0 {
		_ = s.userHoldMic(ctx, uid, channelId, VIP_MIC_ID, userMap[uid])
	}

	//VIP变更
	//if !req.GetIsManual() {
	_ = s.pushMsgToChannelWithUser(ctx, channelId, uint32(channel.ChannelMsgType_CHANNEL_DATING_GAME_VIP_HOLD_MIC), "VIP坐席用户变化", nil, userMap[uid])
	//}

	log.InfoWithCtx(ctx, "DatingGameHoldVipMic uid:%d, req:%+v, myMicId:%d,resp:%+v", svrInfo.UserID, req, myMicId, out)
	return out, nil
}

func (s *Server) userHoldMic(ctx context.Context, opUid, channelId, micId uint32, profile *app.UserProfile) error {
	// 查看房间的类型消息
	log.InfoWithCtx(ctx, "userHoldMic uid:%d,  channelId:%d", opUid, channelId)
	objSimpleInfo, err := s.channelClient.GetChannelSimpleInfo(ctx, 0, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UserHoldMic fail to GetChannelSimpleInfo, opUid:%d, channelId:%d, micId:%d, err:%v",
			opUid, channelId, micId, err)
		return err
	}

	var needKickMicUid uint32 // 可能需要踢下麦的人
	var MicStatus uint32      // 麦位状态
	var needChangeMicStatus uint32

	// 检查mvp麦位上是否有人
	MicListResp, err := s.channelMicClient.GetMicrList(ctx, channelId, opUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyToHoldMic fail to GetMicrList, opUid:%d, channelId:%d, micId:%d, err:%v",
			opUid, channelId, micId, err)
		return err
	}

	micListInfo := MicListResp.GetAllMicList()
	for i := 0; i < len(micListInfo); i++ {
		if micListInfo[i].GetMicId() == micId {
			needKickMicUid = micListInfo[i].GetMicUid()
			MicStatus = micListInfo[i].GetMicState()
		}
	}
	if MicStatus == uint32(channel.MicrSpace_MIC_SPACE_DISABLE) {
		// 麦位不可锁，如果自动锁上了，那就再次开启麦位
		needChangeMicStatus = uint32(channel.MicrSpace_MIC_SPACE_NOMAL)
		_, err := s.channelMicClient.SetChannelMicSpaceStatus(ctx, 0, opUid, channelId, micId, needChangeMicStatus)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyToHoldMic fail to GetChannelSimpleInfo, opUid:%d, channelId:%d,  micId:%d, err:%v",
				opUid, channelId, micId, err)
		}
	}

	if needKickMicUid != 0 && needKickMicUid == opUid {
		log.DebugWithCtx(ctx, "already on mic uid：%d, channelId:%d", opUid, channelId)
		return nil //已在麦上
	}

	holdMicResp, err := s.channelMicClient.SimpleHoldMicrSpace(ctx, 0, &channelmicPB.SimpleHoldMicrSpaceReq{
		ChannelId:        channelId,
		Uid:              opUid,
		MicPosId:         micId,
		IsForce:          true, //强制用户上麦，如果麦上有人，强制该用户下麦
		ChannelDisplayId: objSimpleInfo.GetDisplayId(),
		ChannelType:      objSimpleInfo.GetChannelType(),
		UserSex:          profile.GetSex(),
		OpUid:            0,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyToHoldMic fail to SimpleHoldMicrSpace, opUid:%d, channelId:%d, micId:%d, err:%v",
			opUid, channelId, micId, err)
		return err
	}

	// 上麦push
	s.AutoHoldMicPushMsg(ctx, opUid, channelId, objSimpleInfo, MicListResp.GetMicrMode(), holdMicResp, profile)
	return nil
}

func (s *Server) GetDatingGameInitInfo(ctx context.Context, req *channel.GetDatingGameInitInfoReq) (*channel.GetDatingGameInitInfoResp, error) {
	out := &channel.GetDatingGameInitInfoResp{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "DatingGameHoldVipMic ServiceInfoFromContext fail. req:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	
	channelId, serr := s.channelOLClient.GetUserChannelId(ctx, svrInfo.UserID, svrInfo.UserID)
	if serr != nil {
		log.ErrorWithCtx(ctx, "SelectLikeDatingUser GetUserChannelId fail. req:%+v, err:%v", req, serr)
		return out, serr
	}
	
	resp, err := s.channelDatingGameClient.GetDatingGameCurInfo(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDatingGameCurInfo channelId:%d, err:%v", req.GetChannelId(), err)
		return out, err
	}

	uidList := make([]uint32, 0)
	if resp.GetVipUid() > 0 {
		uidList = append(uidList, resp.GetVipUid())
	}

	for _, user := range resp.GetHatUserList() {
		uidList = append(uidList, user.GetUid())
	}

	for _, user := range resp.GetLikeBeatList() {
		uidList = append(uidList, user.GetUid())
	}

	for _, user := range resp.GetOpenLikeUserList() {
		uidList = append(uidList, user.GetLikeUid())
		uidList = append(uidList, user.GetOpenUid())
	}

	userMap, err := s.userProfileCli.BatchGetUserProfileV2(ctx, uidList, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDatingGameCurInfo BatchGetUserProfileV2 channelId:%d, err:%v", req.GetChannelId(), err)
		return out, err
	}

	// 帽子
	for _, hat := range resp.GetHatUserList() {
		user := userMap[hat.GetUid()]
		out.HatUserList = append(out.HatUserList,
			&channel.DatingGameHatUser{
				Uid: hat.GetUid(),
				UrlSource: &app.DownloadSourceInfo{
					SourceType: uint32(app.DownloadSourceInfo_DOWNLOAD_SOURCE_DATING_GAME_HAT),
					SourceId:   hat.GetHatCfg().GetHatId(),
					Url:        hat.GetHatCfg().GetUrl(),
					Md5:        hat.GetHatCfg().GetMd5(),
				},
				IsMale:      hat.GetHatCfg().GetIsMale(),
				Account:     user.GetAccount(),
				Nickname:    user.GetNickname(),
				UserProfile: user,
			})
	}

	// 心动值和心动状态
	for _, beat := range resp.GetLikeBeatList() {
		user := userMap[beat.GetUid()]
		out.LikeBeatList = append(out.LikeBeatList, &channel.UserLikeBeatInfo{
			Uid:          user.GetUid(),
			LikeBeatVal:  beat.GetLikeBeatVal(),
			SelectStatus: beat.GetSelectStatus(),
		})
	}

	// 已经公布心动的用户
	for _, open := range resp.GetOpenLikeUserList() {
		openUser := userMap[open.GetOpenUid()]
		likeUser := userMap[open.GetLikeUid()]
		out.OpenLikeUserList = append(out.OpenLikeUserList, &channel.AlreadyOpenLikeUserInfo{
			OpenUid: openUser.GetUid(),
			LikeUid: likeUser.GetUid(),
		})
	}

	// vip麦位
	if resp.GetVipUid() > 0 {
		vipUser := userMap[resp.GetVipUid()]
		out.VipUser = &channel.DatingGameVipUser{
			Uid:         vipUser.GetUid(),
			Account:     vipUser.GetAccount(),
			Nickname:    vipUser.GetNickname(),
			UserProfile: vipUser,
		}
	}

	// 填充规则页入口资源配置
	rulesEntryList := make([]*channel.RuleEntryStyle, 0)
	for _, v := range s.bc.GetRuleEntryStyleList() {
		rulesEntryList = append(rulesEntryList, &channel.RuleEntryStyle{
			GameLevel:     v.Level,
			RulesEntryUrl: v.URL,
			StyleColor:    v.Color,
		})
	}
	out.StyleList = rulesEntryList

	// 连麦人数
	out.ApplyMicLen = resp.GetApplyMicLen()
	out.Phase = resp.GetPhase()
	return out, nil
}

// 用户自动上麦推送
func (s *Server) AutoHoldMicPushMsg(ctx context.Context, uid, channelId uint32, objSimpleInfo *chSvrPb.ChannelSimpleInfo, micMode uint32,
	objHoldMicResp *channelmicPB.SimpleHoldMicrSpaceResp, profile *app.UserProfile) error {
	headWareOpt := &channel.CPHeadwareOpt{
		CpHeadwareImg: "",
		CpUid:         0,
		CpHeadwareUrl: "",
	} // MVP用户不佩戴麦位框

	opt := &channel.ChannelMicOpt{
		AllMicList: make([]*channel.SimpleMicrSpace, 0),
	}

	micUids := make([]uint32, 0)
	for _, micInfo := range objHoldMicResp.GetAllMicList() {
		if micInfo.GetMicUid() > 0 {
			micUids = append(micUids, micInfo.GetMicUid())
		}
	}

	userMap, err := s.userProfileCli.BatchGetUserProfileV2(ctx, micUids, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "AutoHoldMicPushMsg GetUsersMap fail. in:%+v, err:%v", err)
		return err
	}

	for _, micInfo := range objHoldMicResp.GetAllMicList() {
		micSpace := &channel.SimpleMicrSpace{
			MicId:    micInfo.GetMicId(),
			MicState: micInfo.GetMicState(),
		}

		if user, ok := userMap[micInfo.GetMicUid()]; ok {
			micSpace.Uid = user.GetUid()
		}
		opt.AllMicList = append(opt.AllMicList, micSpace)
	}

	//获取用户头像信息
	headImageMap, severErr := s.headImageCli.BatchGetHeadImageMd5(ctx, uid, []string{profile.GetAccount()})
	if severErr != nil {
		log.ErrorWithCtx(ctx, "AutoHoldMicPushMsg fail at BatchGetHeadImageMd5. return err: %v, userId:%d", severErr, uid)
		return severErr
	}

	// 认证
	officialResp, severErr := s.officialCertCli.GetUserOfficialCert(ctx, &offcialCert.GetUserOfficialCertReq{Uid: uid})
	if severErr != nil {
		log.ErrorWithCtx(ctx, "AutoHoldMicPushMsg fail at GetUserOfficialCert. return err: %v, userId:%d", severErr, uid)
		return severErr
	}

	// 贵族信息
	noResp, severErr := s.nobilityCli.GetNobilityInfo(ctx, uid, false)
	if severErr != nil {
		log.ErrorWithCtx(ctx, "AutoHoldMicPushMsg fail at GetNobilityInfo. return err: %v, userId:%d", severErr, uid)
		return severErr
	}

	opt.OpMicUid = profile.GetUid()
	opt.OpMicFacemd5 = headImageMap[profile.GetAccount()]
	opt.OpMicid = VIP_MIC_ID
	opt.OpTimeMs = objHoldMicResp.GetServerTimeMs()
	opt.CurrMicmode = micMode
	opt.OpMicSex = int32(profile.GetSex())
	opt.IsGuildChannelPermission = false
	opt.CertifyTitle = officialResp.GetInfo().GetCert().GetTitle()
	opt.CertifyIntro = officialResp.GetInfo().GetCert().GetIntro()
	opt.OpCpHeadwareInfo = headWareOpt
	opt.NobilityLevel = noResp.GetLevel()
	opt.CertifyStyle = officialResp.GetInfo().GetCert().GetStyle()

	// 公会主房间填充相关信息
	if objSimpleInfo.GetChannelType() == uint32(channel.ChannelType_GUILD_HOME_CHANNEL_TYPE) {
		if objSimpleInfo.GetCreaterUid() == uid {
			opt.GuildOfficialName = "会长"
			opt.IsGuildChannelPermission = true
		} else {
			guildOfficial, severErr := s.guildCli.GetGuildOfficialByUid(ctx, uid, objSimpleInfo.GetBindId(), uid)
			if severErr != nil {
				log.ErrorWithCtx(ctx, "AutoHoldMicPushMsg fail at GetGuildOfficialByUid. return err: %v, userId:%d, guildId:%d", severErr, uid, objSimpleInfo.GetBindId())
				return severErr
			}
			opt.GuildOfficialName = guildOfficial.GetInfo().GetOfficialName()
			hasPermission := (guildOfficial.GetInfo().GetPermission() & PERMISSION2_CAHNNEL_MANAGE) > 0
			opt.IsGuildChannelPermission = hasPermission
		}
	}

	// 获取 revenue_mic_extern_data opt
	revenueMicExternResp, e := s.revenueApiGoCli.GetRevenueMicInfo(ctx, &revenue_api_go.GetRevenueMicInfoReq{
		Uid:       uid,
		ChannelId: channelId,
		Scene:     uint32(revenue_api_go.RevenueSceneType_RevenueSceneTypeOnMic),
	})
	if e != nil {
		log.ErrorWithCtx(ctx, "AutoHoldMicPushMsg fail to BatchGetRevenueMicInfo, channelId:%d, uid:%d, err:%v", channelId, uid, e)
		// 忽略错误
	}
	opt.RevenueMicExternData = revenueMicExternResp.GetRevenueInfo()
	log.DebugWithCtx(ctx, "AutoHoldMicPushMsg channelId:%d, uid:%d, opt.RevenueMicExternData:%+v", channelId, uid, opt.RevenueMicExternData)

	// 推送 房间内推送 、 个人推送 toast：您已上麦~
	serr := s.pushChannelAutoHoldMic(ctx, channelId, profile, opt)
	if serr != nil {
		log.ErrorWithCtx(ctx, "AutoHoldMicPushMsg fail to pushChannelAutoHoldMic, channelId:%d, uid:%d, msg:%+v, err:%v", channelId, uid, opt, serr)
		return serr
	}
	return nil
}
