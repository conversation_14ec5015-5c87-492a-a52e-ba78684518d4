package internal

import (
	"context"
	risk_mng_api_mock "golang.52tt.com/clients/mocks/risk-mng-api"
	risk_mng_api "golang.52tt.com/clients/risk-mng-api"
	riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
	"reflect"
	"testing"

	"fmt"
	"github.com/golang/mock/gomock"
	channelCli "golang.52tt.com/clients/channel"
	channeldatinggame "golang.52tt.com/clients/channel-dating-game"
	channel_msg_express "golang.52tt.com/clients/channel-msg-express"
	channelapi_go "golang.52tt.com/clients/channelapi-go"
	"golang.52tt.com/clients/channelmic"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/darkserver"
	"golang.52tt.com/clients/greenbaba"
	"golang.52tt.com/clients/guild"
	headImage "golang.52tt.com/clients/headimage"
	mockChannel "golang.52tt.com/clients/mocks/channel"
	mockChannelDatingGame "golang.52tt.com/clients/mocks/channel-dating-game"
	channel_msg_express_mock "golang.52tt.com/clients/mocks/channel-msg-express"
	mockChannelApi "golang.52tt.com/clients/mocks/channelapi-go"
	mockChannelMic "golang.52tt.com/clients/mocks/channelmic"
	mockChannelOl "golang.52tt.com/clients/mocks/channelol"
	mockDark "golang.52tt.com/clients/mocks/darkserver"
	mockGreenBaba "golang.52tt.com/clients/mocks/greenbaba"
	guildMock "golang.52tt.com/clients/mocks/guild"
	headImageMock "golang.52tt.com/clients/mocks/headimage"
	nobilityMock "golang.52tt.com/clients/mocks/nobility"
	officialCertMock "golang.52tt.com/clients/mocks/officialcert"
	pushMock "golang.52tt.com/clients/mocks/push-notification/v2"
	mockRevenueApiGo "golang.52tt.com/clients/mocks/revenue-api-go"
	mockUserProfile "golang.52tt.com/clients/mocks/user-profile-api"
	mockYouKnowWho "golang.52tt.com/clients/mocks/you-know-who"
	"golang.52tt.com/clients/nobility"
	"golang.52tt.com/clients/officialcert"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	revenueApiGo "golang.52tt.com/clients/revenue-api-go"
	user_profile_api "golang.52tt.com/clients/user-profile-api"
	youknowwho "golang.52tt.com/clients/you-know-who"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	appPb "golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/services/channel-dating-game"
	channelapi_go_pb "golang.52tt.com/protocol/services/channelapi-go"
	ChannelMic "golang.52tt.com/protocol/services/channelmicsvr"
	channelsvrPb "golang.52tt.com/protocol/services/channelsvr"
	"golang.52tt.com/protocol/services/demo/echo"
	greenBabaPB "golang.52tt.com/protocol/services/greenBaba"
	Nobility "golang.52tt.com/protocol/services/nobilitysvr"
	"golang.52tt.com/services/tt-rev/channel-dating-game-logic/internal/conf"
	"golang.52tt.com/services/tt-rev/channel-dating-game-logic/internal/mocks"
	"time"
)

//go:generate  mockgen -destination=./mocks/mock_bc.go -mock_names IBcConf=MockBc -package=mocks golang.52tt.com/services/tt-rev/channel-dating-game-logic/internal/conf IBusinessConfManager

var (
	ctx   context.Context
	uid   = uint32(2333)
	cid   = uint32(6666)
	ctype = uint32(4)
)

func init() {
	ctx = protogrpc.WithServiceInfo(context.TODO(), &protogrpc.ServiceInfo{UserID: uid})
}

func TestNewServer(t *testing.T) {
	type args struct {
		ctx context.Context
		cfg *StartConfig
	}
	tests := []struct {
		name    string
		args    args
		want    *Server
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewServer(tt.args.ctx, tt.args.cfg)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewServer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewServer() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_ApplyDatingMic(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	channelOlCli := mockChannelOl.NewMockIClient(ctl)
	userProfileCli := mockUserProfile.NewMockIClient(ctl)
	channelDatingGameCli := mockChannelDatingGame.NewMockIClient(ctl)

	gomock.InOrder(
		channelOlCli.EXPECT().GetUserChannelId(ctx, gomock.Any(), gomock.Any()).Return(uint32(1), nil).AnyTimes(),
		channelDatingGameCli.EXPECT().UserApplyMic(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes(),
		channelDatingGameCli.EXPECT().GetApplyMicUserList(ctx, gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
		userProfileCli.EXPECT().BatchGetUserProfileV2(ctx, gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
	)

	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient
	}

	type args struct {
		ctx context.Context
		req *channel.ApplyDatingMicReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *channel.ApplyDatingMicResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "ApplyDatingMic",
			fields: fields{
				channelDatingGameClient: channelDatingGameCli,
				userProfileCli:          userProfileCli,
				channelOLClient : channelOlCli,
			},
			args: args{
				ctx: ctx,
				req: &channel.ApplyDatingMicReq{
					BaseReq:   nil,
					ChannelId: cid,
					IsCancel:  false,
				},
			},
			want:    &channel.ApplyDatingMicResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
			}
			got, err := s.ApplyDatingMic(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ApplyDatingMic() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ApplyDatingMic() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_CheckChannelDatingGameEntry(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	channelOlCli := mockChannelOl.NewMockIClient(ctl)
	channelDatingGameCli := mockChannelDatingGame.NewMockIClient(ctl)

	gomock.InOrder(
		channelOlCli.EXPECT().GetUserChannelId(ctx, gomock.Any(), gomock.Any()).Return(uint32(1), nil).AnyTimes(),
		channelDatingGameCli.EXPECT().CheckDatingGameEntry(ctx, gomock.Any()).Return(false, uint32(0), nil).AnyTimes(),
	)

	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient
	}
	type args struct {
		ctx context.Context
		req *channel.CheckChannelDatingGameEntryReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *channel.CheckChannelDatingGameEntryResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "ApplyDatingMic",
			fields: fields{
				channelDatingGameClient: channelDatingGameCli,
				channelOLClient:         channelOlCli,
			},
			args: args{
				ctx: ctx,
				req: &channel.CheckChannelDatingGameEntryReq{
					BaseReq:   nil,
					ChannelId: cid,
				},
			},
			want:    &channel.CheckChannelDatingGameEntryResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
			}
			got, err := s.CheckChannelDatingGameEntry(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckChannelDatingGameEntry() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckChannelDatingGameEntry() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_DatingGameHoldVipMic(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	channelDatingGameCli := mockChannelDatingGame.NewMockIClient(ctl)
	greenBaBaClient := mockGreenBaba.NewMockIClient(ctl)
	channelClient := mockChannel.NewMockIClient(ctl)
	channelOlClient := mockChannelOl.NewMockIClient(ctl)
	darkClient := mockDark.NewMockIClient(ctl)
	channelMicClient := mockChannelMic.NewMockIClient(ctl)
	userProfileCli := mockUserProfile.NewMockIClient(ctl)
	channelApiCli := mockChannelApi.NewMockIClient(ctl)
	mockPushClient := PushNotification.NewMockIClient(ctl)
	guildMockCli := guildMock.NewMockIClient(ctl)
	headImageMockCli := headImageMock.NewMockIClient(ctl)
	officialCertMockCli := officialCertMock.NewMockIClient(ctl)
	nobilityMockCli := nobilityMock.NewMockIClient(ctl)
	revenueApiGoCli := mockRevenueApiGo.NewMockIClient(ctl)
	riskMngCli := risk_mng_api_mock.NewMockIClient(ctl)

	channelSimpleInfo := &channelsvrPb.ChannelSimpleInfo{
		ChannelId:   &cid,
		ChannelType: &cid,
		CreaterUid:  &uid,
	}
	checkResp := &riskMngApiPb.CheckResp{ErrCode: 1}

	gomock.InOrder(
		channelOlClient.EXPECT().GetUserChannelId(ctx, gomock.Any(), gomock.Any()).Return(uint32(1), nil).AnyTimes(),
		greenBaBaClient.EXPECT().GetCurrBannedStatById(ctx, uid, gomock.Any(), gomock.Any()).Return([]*greenBabaPB.GreenBabaSanctionInfo{}, nil).AnyTimes(),
		channelClient.EXPECT().CheckUserIsMute(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes(),
		//channelOlClient.EXPECT().GetUserChannelId(ctx, uid, uid).Return(cid, nil).AnyTimes(),
		riskMngCli.EXPECT().CheckHelper(gomock.Any(), gomock.Any(), gomock.Any()).Return(checkResp, nil).AnyTimes(),
		darkClient.EXPECT().UserBehaviorCheck(ctx, uid).Return(uint32(0), nil).AnyTimes(),
		channelMicClient.EXPECT().GetMicrList(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ChannelMic.GetMicrListResp{
			ChannelId: cid,
			AllMicList: []*ChannelMic.MicrSpaceInfo{{
				MicId:    0,
				MicState: 0,
				MicUid:   1,
				MicTs:    0,
			}},
			MicrMode:     7,
			ServerTimeMs: 0,
		}, nil).AnyTimes(),
		channelDatingGameCli.EXPECT().ConfirmVipHoldMic(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
		userProfileCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
		channelClient.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelSimpleInfo, nil).AnyTimes(),
		channelMicClient.EXPECT().GetMicrList(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ChannelMic.GetMicrListResp{
			ChannelId: cid,
			AllMicList: []*ChannelMic.MicrSpaceInfo{{
				MicId:    0,
				MicState: 0,
				MicUid:   1,
				MicTs:    0,
			}},
			MicrMode:     7,
			ServerTimeMs: 0,
		}, nil).AnyTimes(),
		channelMicClient.EXPECT().SimpleHoldMicrSpace(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
		userProfileCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*appPb.UserProfile{
			uid: &appPb.UserProfile{
				Privilege: &appPb.UserPrivilege{
					Type: uint32(appPb.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW),
				},
			},
		}, nil).AnyTimes(),
		headImageMockCli.EXPECT().BatchGetHeadImageMd5(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[string]string{
			fmt.Sprintf("%d", uid): "sssssss",
		}, nil).AnyTimes(),
		officialCertMockCli.EXPECT().GetUserOfficialCert(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
		nobilityMockCli.EXPECT().GetNobilityInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&Nobility.NobilityInfo{
			Invisible: false,
		}, nil).AnyTimes(),
		guildMockCli.EXPECT().GetGuildOfficialByUid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
		//channelApiCli.EXPECT().HoldMicPush(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes(),
		revenueApiGoCli.EXPECT().GetRevenueMicInfo(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
		mockPushClient.EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes(),
	)

	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient
		guildCli                guild.IClient
		headImageCli            headImage.IClient
		officialCertCli         officialcert.IClient
		nobilityCli             nobility.IClient
		revenueApiGoCli         revenueApiGo.IClient
		riskMngApiCli           risk_mng_api.IClient
	}
	type args struct {
		ctx context.Context
		req *channel.DatingGameHoldVipMicReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *channel.DatingGameHoldVipMicResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "DatingGameHoldVipMic",
			fields: fields{
				channelDatingGameClient: channelDatingGameCli,
				userProfileCli:          userProfileCli,
				ukwCli:                  nil,
				channelClient:           channelClient,
				channelOLClient:         channelOlClient,
				darkClient:              darkClient,
				channelMicClient:        channelMicClient,
				pushClient:              mockPushClient,
				channelApiCli:           channelApiCli,
				greenBaBaClient:         greenBaBaClient,
				channelMsgClient:        nil,
				guildCli:                guildMockCli,
				headImageCli:            headImageMockCli,
				officialCertCli:         officialCertMockCli,
				nobilityCli:             nobilityMockCli,
				revenueApiGoCli:         revenueApiGoCli,
				riskMngApiCli:           riskMngCli,
			},
			args: args{
				ctx: ctx,
				req: &channel.DatingGameHoldVipMicReq{
					BaseReq:   nil,
					ChannelId: cid,
					IsManual:  false,
				},
			},
			want:    &channel.DatingGameHoldVipMicResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
				guildCli:                tt.fields.guildCli,
				headImageCli:            tt.fields.headImageCli,
				officialCertCli:         tt.fields.officialCertCli,
				nobilityCli:             tt.fields.nobilityCli,
				revenueApiGoCli:         tt.fields.revenueApiGoCli,
				riskMngApiCli:           tt.fields.riskMngApiCli,
			}
			_, err := s.DatingGameHoldVipMic(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DatingGameHoldVipMic() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

		})
	}
}

//func TestServer_handleFinPhase1(t *testing.T) {
//	testCases := []struct {
//		name          string
//		channelId     uint32
//		uid           uint32
//		micResp       *pbChannelMic.GetMicrListResp
//		kickUidList   []uint32
//		kickErr       error
//		lockMicResp   *pbChannelMic.BatchSetChannelMicSpaceStatusResp
//		lockMicErr    error
//		expectedError error
//	}{
//		{
//			name:      "Success case",
//			channelId: 123,
//			uid:       456,
//			micResp: &pbChannelMic.GetMicrListResp{
//				MicList: []*pbChannelMic.MicInfo{
//					{MicUid: 789, MicId: 1},
//					{MicUid: 101, MicId: 2},
//				},
//			},
//			kickUidList: []uint32{789, 101},
//			kickErr:     nil,
//			lockMicResp: &pbChannelMic.BatchSetChannelMicSpaceStatusResp{
//				KickedMicrList: []*pbChannelMic.MicInfo{
//					{MicUid: 789, MicId: 1},
//					{MicUid: 101, MicId: 2},
//				},
//				AllMicList: []*pbChannelMic.MicInfo{
//					{MicUid: 789, MicId: 1},
//					{MicUid: 101, MicId: 2},
//				},
//			},
//			lockMicErr:    nil,
//			expectedError: nil,
//		},
//		// Add more test cases for edge cases, error cases, etc.
//	}
//
//	for _, tc := range testCases {
//		t.Run(tc.name, func(t *testing.T) {
//			ctrl := gomock.NewController(t)
//			defer ctrl.Finish()
//
//			mockChannelMicClient := mockChannelMic.NewMockIClient(ctrl)
//			mockChannelApiCli := mockChannelApi.NewMockIClient(ctrl)
//
//			s := &Server{
//				channelMicClient: mockChannelMicClient,
//				channelApiCli:    mockChannelApiCli,
//			}
//
//			mockChannelMicClient.EXPECT().
//				GetMicrList(gomock.Any(), tc.channelId, tc.uid).
//				Return(tc.micResp, nil)
//
//			if tc.micResp != nil {
//				mockChannelApiCli.EXPECT().
//					KickOutMicSpace(gomock.Any(), gomock.Any()).
//					Return(&pbChannelApi.KickOutMicResp{}, tc.kickErr)
//
//				mockChannelMicClient.EXPECT().
//					BatchSetChannelMicSpaceStatus(gomock.Any(), tc.uid, tc.uid, tc.channelId, gomock.Any()).
//					Return(tc.lockMicResp, tc.lockMicErr)
//			}
//
//			err := s.handleFinPhase(context.Background(), tc.channelId, tc.uid)
//
//			// Add assertions using "reflect" package
//			if !reflect.DeepEqual(err, tc.expectedError) {
//				t.Errorf("Error does not match. Expected: %v, but got: %v", tc.expectedError, err)
//			}
//		})
//	}
//}

func TestServer_Echo(t *testing.T) {
	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient
	}
	type args struct {
		ctx context.Context
		req *echo.StringMessage
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *echo.StringMessage
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
			}
			got, err := s.Echo(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Echo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Echo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_GetApplyDatingMicUserList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
 	channelDatingGameCli := mockChannelDatingGame.NewMockIClient(ctl)
	userProfileCli := mockUserProfile.NewMockIClient(ctl)
	channelOlClient := mockChannelOl.NewMockIClient(ctl)

	gomock.InOrder(
		channelOlClient.EXPECT().GetUserChannelId(ctx, gomock.Any(), gomock.Any()).Return(uint32(1), nil).AnyTimes(),
		channelDatingGameCli.EXPECT().GetApplyMicUserList(ctx, gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
		userProfileCli.EXPECT().BatchGetUserProfileV2(ctx, gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
	)
	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient
	}
	type args struct {
		ctx context.Context
		req *channel.GetApplyDatingMicUserListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *channel.GetApplyDatingMicUserListResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetApplyDatingMicUserList",
			fields: fields{
				channelDatingGameClient: channelDatingGameCli,
				userProfileCli:          userProfileCli,
				channelOLClient : channelOlClient,
			},
			args: args{
				ctx: ctx,
				req: &channel.GetApplyDatingMicUserListReq{
					BaseReq:   nil,
					ChannelId: cid,
				},
			},
			want:    &channel.GetApplyDatingMicUserListResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
			}
			got, err := s.GetApplyDatingMicUserList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetApplyDatingMicUserList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetApplyDatingMicUserList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_GetDatingGameInitInfo(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	
	channelOlCli := mockChannelOl.NewMockIClient(ctl)
	channelDatingGameCli := mockChannelDatingGame.NewMockIClient(ctl)
	userProfileCli := mockUserProfile.NewMockIClient(ctl)
	bcMockCli := mocks.NewMockIBusinessConfManager(ctl)

	currentInfo := &pb.GetDatingGameCurInfoResp{
		Phase:  1,
		VipUid: uid,
		HatUserList: []*pb.HatUser{{
			Uid: uid,
			HatCfg: &pb.DatingGameHatCfg{
				HatId:      1,
				Url:        "",
				Md5:        "",
				TbeanLimit: 0,
				IsMale:     false,
				Level:      0,
			},
		}},
		LikeBeatList: []*pb.UserLikeBeatInfo{{
			Uid:          uid,
			LikeBeatVal:  1,
			SelectStatus: false,
		}},
		OpenLikeUserList: []*pb.OpenLikeUserInfo{},
		ApplyMicLen:      1,
	}
	gomock.InOrder(
		channelOlCli.EXPECT().GetUserChannelId(ctx, gomock.Any(), gomock.Any()).Return(uint32(1), nil).AnyTimes(),
		channelDatingGameCli.EXPECT().GetDatingGameCurInfo(ctx, gomock.Any()).Return(currentInfo, nil).AnyTimes(),
		userProfileCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
		bcMockCli.EXPECT().GetRuleEntryStyleList().Return([]*conf.RuleEntryStyle{
			{
				Level: 0, // 免费
				URL:   "https://obs-cdn.52tt.com/tt/fe-moss/tt-server/DATING_GAME_LEVEL_FREE.png",
				Color: "#FFFF7F",
			},
		}).AnyTimes(),
	)

	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient

		bcCli conf.IBusinessConfManager
	}
	type args struct {
		ctx context.Context
		req *channel.GetDatingGameInitInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *channel.GetDatingGameInitInfoResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetDatingGameInitInfo",
			fields: fields{
				channelDatingGameClient: channelDatingGameCli,
				userProfileCli:          userProfileCli,
				channelOLClient:         channelOlCli,
			},
			args: args{
				ctx: ctx,
				req: &channel.GetDatingGameInitInfoReq{
					BaseReq:   nil,
					ChannelId: cid,
				},
			},
			want: &channel.GetDatingGameInitInfoResp{
				BaseResp:         nil,
				Phase:            1,
				VipUser:          nil,
				HatUserList:      nil,
				LikeBeatList:     nil,
				OpenLikeUserList: nil,
				ApplyMicLen:      0,
				StyleList: []*channel.RuleEntryStyle{
					{
						GameLevel:     0,
						RulesEntryUrl: "https://obs-cdn.52tt.com/tt/fe-moss/tt-server/DATING_GAME_LEVEL_FREE.png",
						StyleColor:    "#FFFF7F",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
				bc:                      bcMockCli,
			}
			got, err := s.GetDatingGameInitInfo(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDatingGameInitInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got.Phase, tt.want.Phase) {
				t.Errorf("GetDatingGameInitInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_OpenLikeDatingUser(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	channelDatingGameCli := mockChannelDatingGame.NewMockIClient(ctl)
	ukwCli := mockYouKnowWho.NewMockIClient(ctl)

	gomock.InOrder(
		ukwCli.EXPECT().GetTrueUidByFake(ctx, gomock.Any()).Return(uid, nil).AnyTimes(),
		channelDatingGameCli.EXPECT().OpenUserLikeBeatObj(ctx, gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
	)

	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient
	}
	type args struct {
		ctx context.Context
		req *channel.OpenLikeDatingUserReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *channel.OpenLikeDatingUserResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "OpenLikeDatingUser",
			fields: fields{
				channelDatingGameClient: channelDatingGameCli,
				ukwCli:                  ukwCli,
			},
			args: args{
				ctx: ctx,
				req: &channel.OpenLikeDatingUserReq{
					BaseReq:   nil,
					ChannelId: cid,
					OpenUid:   uid,
				},
			},
			want:    &channel.OpenLikeDatingUserResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
			}
			got, err := s.OpenLikeDatingUser(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("OpenLikeDatingUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OpenLikeDatingUser() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_SelectLikeDatingUser(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	channelDatingGameCli := mockChannelDatingGame.NewMockIClient(ctl)
	ukwCli := mockYouKnowWho.NewMockIClient(ctl)
	channelOlCli := mockChannelOl.NewMockIClient(ctl)
	gomock.InOrder(
		ukwCli.EXPECT().GetTrueUidByFake(ctx, gomock.Any()).Return(uid, nil).AnyTimes(),
		channelOlCli.EXPECT().GetUserChannelId(ctx, gomock.Any(), gomock.Any()).Return(cid, nil).AnyTimes(),
		channelDatingGameCli.EXPECT().SetUserLikeBeatObj(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
	)

	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient
	}
	type args struct {
		ctx context.Context
		req *channel.SelectLikeDatingUserReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *channel.SelectLikeDatingUserResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "SelectLikeDatingUser",
			fields: fields{
				channelDatingGameClient: channelDatingGameCli,
				ukwCli:                  ukwCli,
				channelOLClient:         channelOlCli,
			},
			args: args{
				ctx: ctx,
				req: &channel.SelectLikeDatingUserReq{
					BaseReq:   nil,
					ChannelId: cid,
					TargetUid: uid,
				},
			},
			want: &channel.SelectLikeDatingUserResp{
				TargetUid: uid,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
			}
			got, err := s.SelectLikeDatingUser(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SelectLikeDatingUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SelectLikeDatingUser() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_SetChannelDatingGamePhase(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	channelDatingGameCli := mockChannelDatingGame.NewMockIClient(ctl)
	ukwCli := mockYouKnowWho.NewMockIClient(ctl)
	channelMockCli := mockChannel.NewMockIClient(ctl)
	channelMicCli := mockChannelMic.NewMockIClient(ctl)
	channelApiMockCli := mockChannelApi.NewMockIClient(ctl)
	channelMsgCli := channel_msg_express_mock.NewMockIClient(ctl)
	pushCli := pushMock.NewMockIClient(ctl)
	channelOlCli := mockChannelOl.NewMockIClient(ctl)
	channelId := uint32(22)
	ChannelType := uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE)
	createUid := uint32(2)
	channelInfo := &channelsvrPb.ChannelSimpleInfo{
		ChannelId:   &channelId,
		ChannelType: &ChannelType,
		CreaterUid:  &createUid,
	}
	currentInfo := &pb.GetDatingGameCurInfoResp{
		Phase:  uint32(channel.DatingGamePhaseType_DATING_GAME_PHASE_PUBLISH),
		VipUid: uid,
		HatUserList: []*pb.HatUser{{
			Uid: uid,
			HatCfg: &pb.DatingGameHatCfg{
				HatId:      1,
				Url:        "",
				Md5:        "",
				TbeanLimit: 0,
				IsMale:     false,
				Level:      0,
			},
		}},
		LikeBeatList: []*pb.UserLikeBeatInfo{{
			Uid:          uid,
			LikeBeatVal:  1,
			SelectStatus: true,
		}},
		OpenLikeUserList: []*pb.OpenLikeUserInfo{
			{
				OpenUid: uid,
				LikeUid: 1,
			},
		},
		ApplyMicLen: 1,
	}

	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient
	}
	type args struct {
		ctx context.Context
		req *channel.SetDatingGamePhaseReq
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		initFunc func()
		want     *channel.SetDatingGamePhaseResp
		wantErr  bool
	}{
		{
			name: "common",
			fields: fields{
				channelDatingGameClient: channelDatingGameCli,
				userProfileCli:          nil,
				ukwCli:                  ukwCli,
				channelClient:           channelMockCli,
				channelOLClient:         channelOlCli,
				darkClient:              nil,
				channelMicClient:        channelMicCli,
				pushClient:              pushCli,
				channelApiCli:           channelApiMockCli,
				greenBaBaClient:         nil,
				channelMsgClient:        channelMsgCli,
				
			},
			args: args{
				ctx: ctx,
				req: &channel.SetDatingGamePhaseReq{
					ChannelId: channelId,
					Phase:     uint32(channel.DatingGamePhaseType_DATING_GAME_PHASE_FIN),
				},
			},
			initFunc: func() {
				channelOlCli.EXPECT().GetUserChannelId(ctx, gomock.Any(), gomock.Any()).Return(cid, nil)
				
				// 检查
				channelMockCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo, nil)
				channelMicCli.EXPECT().GetMicrList(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&ChannelMic.GetMicrListResp{MicrMode: uint32(channel.EChannelMicMode_DATING_MIC_SPACE_MODE),
						AllMicList: []*ChannelMic.MicrSpaceInfo{{MicId: 1, MicUid: uid}, {MicId: 2, MicUid: uid + 1}}}, nil)
				channelDatingGameCli.EXPECT().GetDatingGameCurInfo(gomock.Any(), gomock.Any()).Return(currentInfo, nil)

				// 切换
				channelDatingGameCli.EXPECT().SetGamePhase(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&pb.SetGamePhaseResp{
					FromPhase: uint32(channel.DatingGamePhaseType_DATING_GAME_PHASE_PUBLISH),
				}, nil)

				// 切换后续处理
				channelMicCli.EXPECT().GetMicrList(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&ChannelMic.GetMicrListResp{MicrMode: uint32(channel.EChannelMicMode_DATING_MIC_SPACE_MODE),
						AllMicList: []*ChannelMic.MicrSpaceInfo{{MicId: 1, MicUid: uid}, {MicId: 2, MicUid: uid + 1}}}, nil).AnyTimes()
				channelApiMockCli.EXPECT().KickOutMicSpace(gomock.Any(), gomock.Any()).Return(&channelapi_go_pb.KickOutMicResp{}, nil).AnyTimes()
				channelMicCli.EXPECT().BatchSetChannelMicSpaceStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ChannelMic.BatchSetChannelMicSpaceStatusResp{
					AllMicList: []*ChannelMic.MicrSpaceInfo{{MicId: 1, MicUid: uid}, {MicId: 2, MicUid: uid + 1}},
				}, nil).AnyTimes()
				channelMsgCli.EXPECT().SendChannelBroadcastMsg(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				// 阶段变化推送
				pushCli.EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				time.Sleep(2 * time.Second)
			},
			want: &channel.SetDatingGamePhaseResp{
				ChannelId:   channelId,
				FromPhase:   uint32(channel.DatingGamePhaseType_DATING_GAME_PHASE_PUBLISH),
				TargetPhase: uint32(channel.DatingGamePhaseType_DATING_GAME_PHASE_FIN),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := s.SetChannelDatingGamePhase(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetChannelDatingGamePhase() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SetChannelDatingGamePhase() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_ShutDown(t *testing.T) {
	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
			}
			s.ShutDown()
		})
	}
}

func TestServer_checkCanHoldVipMic(t *testing.T) {
	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient
	}
	type args struct {
		ctx       context.Context
		uid       uint32
		channelId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
			}
			if _, err := s.checkCanHoldVipMic(tt.args.ctx, tt.args.uid, tt.args.channelId, nil); (err != nil) != tt.wantErr {
				t.Errorf("checkCanHoldVipMic() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestServer_checkCanSetPhase(t *testing.T) {
	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient
	}
	type args struct {
		ctx         context.Context
		channelId   uint32
		uid         uint32
		targetPhase uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
			}
			if err := s.checkCanSetPhase(tt.args.ctx, tt.args.channelId, tt.args.uid, tt.args.targetPhase); (err != nil) != tt.wantErr {
				t.Errorf("checkCanSetPhase() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestServer_checkChannelBanned(t *testing.T) {
	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient
	}
	type args struct {
		ctx context.Context
		uid uint32
		cid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
			}
			got, err := s.checkChannelBanned(tt.args.ctx, tt.args.uid, tt.args.cid)
			if (err != nil) != tt.wantErr {
				t.Errorf("checkChannelBanned() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("checkChannelBanned() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_handleFinPhase(t *testing.T) {
	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient
	}
	type args struct {
		ctx       context.Context
		channelId uint32
		uid       uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
			}
			if err := s.handleFinPhase(tt.args.ctx, tt.args.channelId, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("handleFinPhase() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestServer_handleInitPhase(t *testing.T) {
	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient
	}
	type args struct {
		ctx       context.Context
		channelId uint32
		uid       uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
			}
			if err := s.handleInitPhase(tt.args.ctx, tt.args.channelId, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("handleInitPhase() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestServer_notifyPhaseChange(t *testing.T) {
	type fields struct {
		channelDatingGameClient channeldatinggame.IClient
		userProfileCli          user_profile_api.IClient
		ukwCli                  youknowwho.IClient
		channelClient           channelCli.IClient
		channelOLClient         channelol.IClient
		darkClient              darkserver.IClient
		channelMicClient        channelmic.IClient
		pushClient              PushNotification.IClient
		channelApiCli           channelapi_go.IClient
		greenBaBaClient         greenbaba.IClient
		channelMsgClient        channel_msg_express.IClient
	}
	type args struct {
		ctx       context.Context
		channelId uint32
		fromPhase uint32
		toPhase   uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				channelDatingGameClient: tt.fields.channelDatingGameClient,
				userProfileCli:          tt.fields.userProfileCli,
				ukwCli:                  tt.fields.ukwCli,
				channelClient:           tt.fields.channelClient,
				channelOLClient:         tt.fields.channelOLClient,
				darkClient:              tt.fields.darkClient,
				channelMicClient:        tt.fields.channelMicClient,
				pushClient:              tt.fields.pushClient,
				channelApiCli:           tt.fields.channelApiCli,
				greenBaBaClient:         tt.fields.greenBaBaClient,
				channelMsgClient:        tt.fields.channelMsgClient,
			}
			if err := s.notifyPhaseChange(tt.args.ctx, tt.args.channelId, tt.args.fromPhase, tt.args.toPhase); (err != nil) != tt.wantErr {
				t.Errorf("notifyPhaseChange() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
