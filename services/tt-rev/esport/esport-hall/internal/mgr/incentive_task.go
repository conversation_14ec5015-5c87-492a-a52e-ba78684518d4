package mgr

import (
    "context"
    "encoding/json"
    "fmt"
    "golang.52tt.com/pkg/log"
    esport_statistics "golang.52tt.com/protocol/services/esport-statistics"
    esport_trade "golang.52tt.com/protocol/services/esport-trade"
    pb "golang.52tt.com/protocol/services/esport_hall"
    "golang.52tt.com/protocol/services/esport_score"
    impb "golang.52tt.com/protocol/services/im-api"
    comctx "golang.52tt.com/services/tt-rev/common/ctx"
    "golang.52tt.com/services/tt-rev/common/util"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/store"
    "sync"
    "time"
)

func (m *Mgr) GetCoachIncentiveTaskInfo(ctx context.Context, req *pb.GetCoachIncentiveTaskInfoRequest) (*pb.GetCoachIncentiveTaskInfoResponse, error) {
    resp := &pb.GetCoachIncentiveTaskInfoResponse{}
    uid := GetUidFromCtx(ctx)

    lastViewTime, err := m.cache.GetIncentiveTaskLastViewTime(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetCoachIncentiveTask, cache.GetIncentiveTaskLastViewTime", err)
        return resp, err
    }
    resp.LastViewTime = uint32(lastViewTime)

    resp.TaskList, err = m.getCoachIncentiveTaskProgressV2(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetCoachIncentiveTask, getCoachIncentiveTaskProgress", err)
        return resp, err
    }

    err = m.cache.SetIncentiveTaskLastViewTime(ctx, uid, time.Now().Unix())
    if err != nil {
        log.WarnWithCtx(ctx, "GetCoachIncentiveTask, cache.SetIncentiveTaskLastViewTime", err)
    }

    return resp, nil
}

func (m *Mgr) HandleCoachIncentiveAdditionUp(ctx context.Context, uid uint32) error {
    if len(m.bc.GetIncentiveTaskConf().PushWhiteList) > 0 {
        match := false
        for _, wuid := range m.bc.GetIncentiveTaskConf().PushWhiteList {
            if wuid == uid {
                match = true
            }
        }
        if !match {
            return nil // 非白名单用户不推送
        }
    }

    preAddition, err := m.cache.GetIncentiveTaskAddition(ctx, util.GetNatureWeekStart(), uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleCoachIncentiveAdditionUp, cache.GetIncentiveTaskAddition", err)
        return err
    }

    taskList, err := m.getCoachIncentiveTaskProgressV2(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleCoachIncentiveAdditionUp, getCoachIncentiveTaskProgress", err)
        return err
    }

    nowAddition := uint32(0)
    for _, task := range taskList {
        if task.IsReach {
            nowAddition = task.Addition
        }
    }

    if nowAddition > preAddition {
        // 发送IM提醒
        err = m.esportImCli.SendOfficialAccountMsg(ctx, "esport_hall", uid, &impb.Text{
            Content:   fmt.Sprintf("恭喜！您已解锁%d%%的分成，快去看看本周分成任务吧~ 点击查看本周任务情况>>", nowAddition),
            Highlight: "点击查看本周任务情况>>",
            Url:       m.bc.GetIncentiveTaskConf().PageUrl,
        })
        if err != nil {
            log.WarnWithCtx(ctx, "AutoInitAndSendUserGameCard sendSingleSystemImWithHighLine, uid: %d, err: %v", uid, err)
        }

        // 更新分成缓存
        err = m.cache.SetIncentiveTaskAddition(ctx, util.GetNatureWeekStart(), uid, nowAddition)
        if err != nil {
            log.WarnWithCtx(ctx, "HandleCoachIncentiveAdditionUp, cache.SetIncentiveTaskAddition", err)
        }

        // 记录分成变化
        progress, _ := json.Marshal(taskList)
        err = m.store.AddCoachIncentiveAddition(ctx, &store.CoachIncentiveAddition{
            CoachUid:      uid,
            Addition:      nowAddition,
            Progress:      string(progress),
            WeekTimeStamp: util.GetNatureWeekStart().Format("20060102"),
            CreateTime:    time.Now(),
        })
    }
    log.InfoWithCtx(ctx, "HandleCoachIncentiveAdditionUp, uid: %d, preAddition: %d, nowAddition: %d", uid, preAddition, nowAddition)
    return nil
}

func (m *Mgr) getCoachIncentiveTaskProgress(ctx context.Context, uid uint32) ([]*pb.IncentiveTask, error) {
    weekStartStart := util.GetNatureWeekStart()
    statTimeEnd := time.Now().Unix() + 5 // 放大5s, mongo时间戳精度问题

    // 本周期订单数量
    totalOrderStatResp, err := m.rpcCli.ESportTradeCli.GetCoachOrderStat(ctx, &esport_trade.GetCoachOrderStatRequest{
        Uid:       uid,
        StartTime: weekStartStart.Unix(),
        EndTime:   statTimeEnd,
    })
    if err != nil {
        log.WarnWithCtx(ctx, "getCoachIncentiveTaskProgress.GetCoachOrderStat, uid: %d, err: %v", uid, err)
    }
    commitAmount := totalOrderStatResp.GetCommitAmount()

    qrDay, qrHour, err := m.StatQuickReceive(ctx, uid, weekStartStart.Unix(), statTimeEnd)
    if err != nil {
        log.WarnWithCtx(ctx, "getCoachIncentiveTaskProgress.StatQuickReceive, uid: %d, err: %v", uid, err)
    }

    qrHour = qrHour + (float32(commitAmount) * m.bc.GetIncentiveTaskConf().OrderReceiveTimeFactor) // 接单时长换算 局数 * 0.33

    incentiveTaskConf := m.bc.GetIncentiveTaskConf()
    taskList := make([]*pb.IncentiveTask, 0, len(incentiveTaskConf.TaskList))
    for _, item := range incentiveTaskConf.TaskList {
        conditionRoundReach := commitAmount >= item.ConditionRound
        conditionQuickReceiveHourReach := float32(qrHour) >= item.ConditionQuickReceiveHour
        conditionQuickReceiveDayReach := qrDay >= item.ConditionQuickReceiveDay
        taskList = append(taskList, &pb.IncentiveTask{
            Addition: item.Addition,
            IsReach:  conditionRoundReach && conditionQuickReceiveHourReach && conditionQuickReceiveDayReach,
            TaskNodeList: []*pb.IncentiveTaskNode{
                {
                    TaskName: "完单局数",
                    Progress: fmt.Sprintf("%d", commitAmount),
                    Target:   fmt.Sprintf("%d", item.ConditionRound),
                    Unit:     "局",
                    IsReach:  conditionRoundReach,
                },
                {
                    TaskName: "接单时长",
                    Progress: fmt.Sprintf("%.2f", qrHour),
                    Target:   fmt.Sprintf("%.2f", item.ConditionQuickReceiveHour),
                    Unit:     "小时",
                    IsReach:  conditionQuickReceiveHourReach,
                },
                {
                    TaskName: "接单天数",
                    Progress: fmt.Sprintf("%d", qrDay),
                    Target:   fmt.Sprintf("%.d", item.ConditionQuickReceiveDay),
                    Unit:     "天",
                    IsReach:  conditionQuickReceiveDayReach,
                },
            },
        })
    }

    return taskList, nil
}

func (m *Mgr) GetCoachIncentiveTaskProgressV2(ctx context.Context, uid uint32) ([]*pb.IncentiveTask, error) {
    return m.getCoachIncentiveTaskProgressV2(ctx, uid)
}

func (m *Mgr) getCoachIncentiveTaskProgressV2(ctx context.Context, uid uint32) ([]*pb.IncentiveTask, error) {
    weekStartStart := util.GetNatureWeekStart()
    statTimeEnd := time.Now().Unix()
    taskList := make([]*pb.IncentiveTask, 0)

    orderAmountOrCount := uint32(0) // 完单流水 or 局数
    customerNum := uint32(0)
    wg := sync.WaitGroup{}
    isUseV2 := m.bc.GetIncentiveTaskConf().UseV2

    var unit = "元"
    var taskName = "完单流水"
    if isUseV2 {
        taskName = "完单局数"
        unit = "局"
        wg.Add(1)
        go func() {
            defer wg.Done()
            // 本周订单数量
            orderCountResp, err := m.rpcCli.ESportTradeCli.GetCoachOrderStat(ctx, &esport_trade.GetCoachOrderStatRequest{
                Uid:       uid,
                StartTime: weekStartStart.Unix(),
                EndTime:   statTimeEnd,
            })
            if err != nil {
                log.WarnWithCtx(ctx, "getCoachIncentiveTaskProgress.GetCoachOrderStat, uid: %d, err: %v", uid, err)
            }
            orderAmountOrCount = orderCountResp.GetCommitAmount()
        }()
    } else {
        wg.Add(1)
        go func() {
            defer wg.Done()
            // 本周订单流水
            orderRunningValResp, err := m.rpcCli.ESportStatCli.GetCoachOrderRunningValue(ctx, &esport_statistics.GetCoachOrderRunningValueRequest{
                CoachUid:  uid,
                StartTime: weekStartStart.Unix(),
                EndTime:   statTimeEnd,
            })
            if err != nil {
                log.WarnWithCtx(ctx, "getCoachIncentiveTaskProgress.GetCoachOrderRunningValue, uid: %d, err: %v", uid, err)
            }
            orderAmountOrCount = orderRunningValResp.GetRunningValue() / 100 // 转为以元为单位
        }()
    }

    wg.Add(1)
    go func() {
        defer wg.Done()
        // 本周老板数量
        customerNumResp, err := m.rpcCli.ESportStatCli.GetCoachCustomerNum(ctx, &esport_statistics.GetCoachCustomerNumRequest{
            CoachUid:  uid,
            StartTime: weekStartStart.Unix(),
            EndTime:   statTimeEnd,
        })
        if err != nil {
            log.WarnWithCtx(ctx, "getCoachIncentiveTaskProgress.GetCoachCustomerNum, uid: %d, err: %v", uid, err)
        }
        customerNum = customerNumResp.GetCustomerNum()
    }()

    wg.Wait()

    incentiveTaskConf := m.bc.GetIncentiveTaskConf()
    var cfgTaskList = incentiveTaskConf.TaskList
    if isUseV2 {
        cfgTaskList = incentiveTaskConf.TaskListV2
    }
    for _, item := range cfgTaskList {
        conditionOrderRunningValReach := orderAmountOrCount >= item.ConditionOrderRunningVal
        conditionCustomerNumReach := customerNum >= item.ConditionOrderCustomerNum

        taskList = append(taskList, &pb.IncentiveTask{
            Addition: item.Addition,
            IsReach:  conditionOrderRunningValReach && conditionCustomerNumReach,
            TaskNodeList: []*pb.IncentiveTaskNode{
                {
                    TaskName: taskName,
                    Progress: fmt.Sprintf("%d", orderAmountOrCount),
                    Target:   fmt.Sprintf("%d", item.ConditionOrderRunningVal),
                    Unit:     unit,
                    IsReach:  conditionOrderRunningValReach,
                },
                {
                    TaskName: "完单老板量",
                    Progress: fmt.Sprintf("%d", customerNum),
                    Target:   fmt.Sprintf("%d", item.ConditionOrderCustomerNum),
                    Unit:     "人",
                    IsReach:  conditionCustomerNumReach,
                },
            },
        })
    }

    return taskList, nil
}

// PushCoachMissionProgressDailyNotify 推送教练任务进度每日提醒
// 每日只推送一次
func (m *Mgr) PushCoachMissionProgressDailyNotify(ctx context.Context, coachUid uint32) error {
    // 获取教练最近一次的登陆时间
    lastLoginTime, err := m.cache.GetCoachLeastOnlineTime(ctx, coachUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "PushCoachMissionProgressDailyNotify, cache.GetCoachLastLoginTime coachUid: %d, err: %v", coachUid, err)
        return err
    }

    // 获取今天零时的时间戳
    todayZeroTime := time.Now().Truncate(24 * time.Hour).Unix()

    // 如果登陆时间在今天的零时之前，则推送消息
    if lastLoginTime < todayZeroTime {
        err = m.esportImCli.SendOfficialAccountMsg(ctx, "esport_hall", coachUid, &impb.Text{
            Content:   "本周完成接单转化流水任务额外奖励分成，点击查看政策与本周任务完成进度",
            Highlight: "点击查看政策与本周任务完成进度",
            Url:       m.bc.GetIncentiveTaskConf().PageUrl,
        })
        if err != nil {
            log.WarnWithCtx(ctx, "PushCoachMissionProgressDailyNotify, SendOfficialAccountMsg", err)
        }

        // 更新登陆时间
        err = m.cache.SetCoachLeastOnlineTime(ctx, coachUid, time.Now().Unix())
        if err != nil {
            log.WarnWithCtx(ctx, "PushCoachMissionProgressDailyNotify, cache.SetCoachLastLoginTime", err)
        }

        // 记录日志，内容包括是否推送成功，和推送的时间（登陆时间）
        log.InfoWithCtx(ctx, "PushCoachMissionProgressDailyNotify, coachUid: %d, lastLoginTime: %d, pushed: true", coachUid, lastLoginTime)
    } else {
        // 记录日志，内容包括最近一次的登陆时间
        log.InfoWithCtx(ctx, "PushCoachMissionProgressDailyNotify, coachUid: %d, lastLoginTime: %d, pushed: false", coachUid, lastLoginTime)
    }

    return nil
}

func (m *Mgr) ReportCoachExtraPointLastWeek() {
    nowCoach := m.GetNowCoachUid()
    ctx, cancel := comctx.WithTimeout(10*time.Minute)
    defer cancel()

    // 上周时间
    thisWeekStart := util.GetNatureWeekStart()
    statStart := thisWeekStart.Add(-7*24*time.Hour)
    statEnd := thisWeekStart


    st := time.Now()
    defer func() {
        log.InfoWithCtx(ctx, "ReportCoachExtraPointLastWeek, start: %v, end: %v, cost: %d ms",
            statStart.Format("20060102"), statEnd.Format("20060102"), time.Since(st).Milliseconds())
    }()
    for _, coachUid := range nowCoach {
        // 查询当前大神积分
        esportScoreResp, err := m.rpcCli.EsportScoreCLi.GetEsportScore(ctx, &esport_score.GetEsportScoreReq{
            Uid: coachUid,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "ReportCoachExtraPointLastWeek GetEsportScore, uid: %d, err: %v", coachUid, err)
            continue
        }

        // 查询上周新增大神积分
        esportScoreDetailResp, err := m.rpcCli.EsportScoreCLi.GetEsportScoreDetail(ctx, &esport_score.GetEsportScoreDetailReq{
            Uid:            coachUid,
            BeginTime:      statStart.Unix(),
            EndTime:        statEnd.Unix(),
            Offset:         0,
            Limit:          10000,
            ReasonTypeList: []esport_score.ReasonType{1},
        })
        lastWeekTotalScore := int64(0)
        for _, item := range esportScoreDetailResp.GetDetailList() {
            lastWeekTotalScore += item.GetChangeScore()
        }
        // 查新上周最新加成
        lastweekTimeStamp := statStart.Format("20060102")
        lastweekAddition, err := m.store.GetLeastCoachIncentiveAdditionByTime(ctx, coachUid, lastweekTimeStamp)
        if err != nil {
            log.ErrorWithCtx(ctx, "ReportCoachExtraPointLastWeek GetLeastCoachIncentiveAdditionByTime, uid: %d, err: %v", coachUid, err)
            continue
        }

        nowScore := uint32(esportScoreResp.GetScore())
        extraScore := uint32(float32(lastWeekTotalScore)*float32(lastweekAddition)/100)
        ReportESportCoachPointLog(ctx, coachUid, nowScore, extraScore)
        log.InfoWithCtx(ctx, "ReportCoachExtraPointLastWeek, uid: %d, nowScore: %d, extraScore: %d, lastweekAddition: %d", coachUid, nowScore, extraScore, lastweekAddition)
    }

}