package server

import (
    "context"
    "encoding/json"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    "golang.52tt.com/protocol/services/demo/echo"
    "golang.52tt.com/services/magic-spirit/internal/event/producer"
    "sort"
    "time"

    "golang.52tt.com/pkg/log"
    pb "golang.52tt.com/protocol/services/magic-spirit"
    UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
    "golang.52tt.com/services/magic-spirit/internal/cache"
    "golang.52tt.com/services/magic-spirit/internal/conf"
    "golang.52tt.com/services/magic-spirit/internal/manager"
    "golang.52tt.com/services/magic-spirit/internal/mysql"
    "golang.52tt.com/services/magic-spirit/internal/define"
)

type MagicSpirit struct {
    mgr manager.IMagicSpiritMgr
}

func NewServer(ctx context.Context, sc *conf.ServiceConfigT) (*MagicSpirit, error) {
    //sc := &conf.ServiceConfigT{}
    //cfgPath := ctx.Value("configfile").(string)
    //if cfgPath == "" {
    //	return nil, errors.New("configfile not exist")
    //}
    //err := sc.Parse(cfgPath)
    //if err != nil {
    //	log.Errorf("config Parse fail err:%v", err)
    //	return nil, err
    //}

    cache_, err := cache.NewCache(ctx, sc.GetRedisConfig())
    if nil != err {
        log.ErrorWithCtx(ctx, "init redis fail, err: %v", err)
        return nil, err
    }

    mysqlStore, err := mysql.NewMysql(sc.GetMysqlConfig(), sc.GetMysqlReadOnlyConfig())
    if err != nil {
        log.Errorf("NewMysql fail %+v, %+v, err:%v", sc.GetMysqlConfig(), sc.GetMysqlReadOnlyConfig(), err)
        return nil, err
    }

    magicSpiritKfk, err := producer.NewMagicSpiritEventProducer(sc.MagicSpiritPresentKFK)
    if err != nil {
        log.Errorf("NewMagicSpiritEventProducer fail %+v, err:%v", sc.MagicSpiritPresentKFK, err)
        return nil, err
    }

    mgr, err := manager.NewMagicSpiritMgr(mysqlStore, cache_, sc, magicSpiritKfk)
    if err != nil {
        log.Errorf("NewMagicSpiritMgr fail %+v, %+v, err:%v", sc.GetMysqlConfig(), sc.GetMysqlReadOnlyConfig(), err)
        return nil, err
    }

    return &MagicSpirit{
        mgr: mgr,
    }, nil
}

func (m *MagicSpirit) ShutDown() {
    m.mgr.ShutDown()
}

func (m *MagicSpirit) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
    return req, nil
}

func (m *MagicSpirit) AddMagicSpiritBlacklist(c context.Context, req *pb.AddMagicSpiritBlacklistReq) (*pb.AddMagicSpiritBlacklistResp, error) {
    resp := &pb.AddMagicSpiritBlacklistResp{}

    data := make([]*mysql.MagicSpiritBlacklist, 0, 8)
    for _, item := range req.Blacklist {
        data = append(data, &mysql.MagicSpiritBlacklist{
            ChannelId: item.ChannelId,
            RoomId:    item.RoomId,
            RoomName:  item.RoomName,
            Ttid:      item.Ttid,
            RoomOwner: item.Owner,
        })
    }
    ids, err := m.mgr.AddMagicSpiritBlacklist(c, data)
    if err != nil {
        log.ErrorWithCtx(c, "AddMagicSpiritBlacklist error: %v", err)
        return resp, err
    }

    resp.BlacklistId = ids
    return resp, nil
}

func (m *MagicSpirit) GetMagicSpiritBlacklist(c context.Context, req *pb.GetMagicSpiritBlackListReq) (*pb.GetMagicSpiritBlackListResp, error) {
    resp := &pb.GetMagicSpiritBlackListResp{}

    total, data, err := m.mgr.GetMagicSpiritBlacklist(c, req.ChannelId, req.PageNum, req.PageSize)
    if err != nil {
        log.ErrorWithCtx(c, "GetMagicSpiritBlacklist error: %v", err)
        return resp, err
    }

    rsData := make([]*pb.MagicSpiritBlacklist, 0, 8)
    for _, item := range data {
        rsData = append(rsData, &pb.MagicSpiritBlacklist{
            BlacklistId: item.BlacklistId,
            ChannelId:   item.ChannelId,
            RoomId:      item.RoomId,
            RoomName:    item.RoomName,
            Ttid:        item.Ttid,
            Owner:       item.RoomOwner,
            CreateTime:  uint32(item.CreateTime.Unix()),
        })
    }
    resp.Total = total
    resp.Blacklist = rsData

    return resp, nil
}

func (m *MagicSpirit) DelMagicSpiritBlacklist(c context.Context, req *pb.DelMagicSpiritBlacklistReq) (*pb.DelMagicSpiritBlacklistResp, error) {
    resp := &pb.DelMagicSpiritBlacklistResp{}

    err := m.mgr.DelMagicSpiritBlacklist(c, req.ChannelIds)
    if err != nil {
        log.ErrorWithCtx(c, "DelMagicSpiritBlacklist error: %v", err)
        return resp, err
    }

    return resp, nil
}

func (m *MagicSpirit) SendMagicSpirit(ctx context.Context, in *pb.SendMagicSpiritReq) (*pb.SendMagicSpiritResp, error) {
    out := &pb.SendMagicSpiritResp{}
    defer func() {
        log.InfoWithCtx(ctx, "SendMagicSpirit in:%+v, out:%+v", in, out)
    }()
    var err error
    out, err = m.mgr.SendMagicSpirit(ctx, in)
    return out, err
}

func (m *MagicSpirit) Notify(ctx context.Context, req *UnifiedPayCallback.PayNotify) (*UnifiedPayCallback.PayNotifyResponse, error) {
    out := &UnifiedPayCallback.PayNotifyResponse{}
    op, err := m.mgr.Callback(ctx, req.GetOutTradeNo())
    if err != nil {
        log.Errorf("Notify fail to Callback. in:%+v, err:%v", req, err)
        return out, err
    }

    out.Confirmed = true
    out.Op = op

    return out, nil
}

func (m *MagicSpirit) GetMagicSpiritForCli(ctx context.Context, in *pb.GetMagicSpiritForCliReq) (*pb.GetMagicSpiritForCliResp, error) {
    resp := &pb.GetMagicSpiritForCliResp{}

    //lastUpdateTime, err := m.mgr.GetMagicSpiritVersion(ctx)
    //if err != nil {
    //    log.ErrorWithCtx(ctx, "GetMagicSpiritForCli get last version error: %v", err)
    //    // 如果版本缓存失效， 默认拉取
    //    lastUpdateTime = uint32(time.Now().Unix())
    //}

    resp.CurrentVersion = uint32(time.Now().Unix())

    data, err := m.mgr.GetMagicSpiritWithCache(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetMagicSpiritForCli find db error: %v", err)
        return nil, err
    }

    // 排序, 先按rankFloat排, rankFloat相同updateTime倒序
    sort.Slice(data, func(i, j int) bool {
        if data[i].RankFloat == data[j].RankFloat {
            return data[i].UpdateTime.Unix() > data[j].UpdateTime.Unix()
        }

        return data[i].RankFloat < data[j].RankFloat
    })

    respMagicSpiritForCLi := make([]*pb.MagicSpiritForCli, 0, 8)
    nowUnix := time.Now().Unix()
    for _, item := range data {
        effectTimeList := conf.Str2TimeRangeListPb(ctx, item.MagicTimeList)
        effectTime := conf.GetEffectTimeRange(effectTimeList, nowUnix)

        if effectTime == nil { // 没有获取到时间段
            if len(effectTimeList) == 0 {
                effectTime = &pb.TimeRange{
                    StartTime: int64(item.EffectBegin),
                    EndTime:   int64(item.EffectEnd),
                }
            } else {
                // 取最后一次生效的时间段
                effectTime = effectTimeList[len(effectTimeList)-1]
            }
        }

        // 判断上下架时间, 只要为下架的都查出
        if effectTime.GetEndTime() < nowUnix {
            continue
        }

        respMagicSpiritForCLiItem := &pb.MagicSpiritForCli{
            MagicSpiritId:    item.MagicSpiritId,
            Name:             item.Name,
            IconUrl:          item.IconUrl,
            Price:            item.Price,
            Rank:             item.Rank,
            EffectBegin:      uint32(effectTime.GetStartTime()),
            EffectEnd:        uint32(effectTime.GetEndTime()),
            DescribeImageUrl: item.DescribeImageUrl,
            DescribeJumpUrl:  item.Describe,
            //JuniorLighting:   item.JuniorLighting,
            //MiddleLighting:   item.MiddleLighting,
            VfxResource:     item.VfxResource,
            VfxResourceMd5:  item.VfxResourceMd5,
            RankFloat:       item.RankFloat,
            DescActivityUrl: item.ActivityJumpUrl,
            ShowEffectEnd:   item.ShowEffectEnd,
            ActivityCfg: &pb.MagicSpiritActivityCfg{
                ActivityName:       item.ActName,
                BeginTime:          item.ActBeginTime.Unix(),
                EndTime:            item.ActEndTime.Unix(),
                ImageUrl:           item.ActImageUrl,
                JumpUrlTt:          item.ActivityJumpUrl,
                JumpUrlHcAndroid:   item.ActJumpUrlHcAndroid,
                JumpUrlHcIos:       item.ActJumpUrlHcIos,
                JumpUrlMikeAndroid: item.ActJumpUrlMikeAndroid,
                JumpUrlMikeIos:     item.ActJumpUrlMikeIos,
            },
        }

        var checkSpecialPond bool
        if item.SpPondTimeList != "" {
            timeList := conf.Str2TimeRangeListPb(ctx, item.SpPondTimeList)
            for _, v := range timeList {
                if nowUnix > v.GetStartTime() && nowUnix < v.GetEndTime() {
                    checkSpecialPond = true
                    break
                }
            }
        }

        var pondData []*mysql.MagicSpiritPond
        if checkSpecialPond {
            pondData, err = m.mgr.GetMagicSpiritPondWithCache(ctx, item.MagicSpiritId, define.SpecialPondType)
            if err != nil {
                log.ErrorWithCtx(ctx, "GetMagicSpiritForCli get pond error: %v", err)
                return nil, err
            }

            if len(pondData) > 0 {
                // 对应的奖励介绍浮层和详情需要替换为特殊奖池的
                respMagicSpiritForCLiItem.DescribeImageUrl = item.SpDescImg
                respMagicSpiritForCLiItem.DescribeJumpUrl = item.SpDescJumpUrl
            }
        }

        if len(pondData) == 0 {
            // 获取普通池
            pondData, err = m.mgr.GetMagicSpiritPondWithCache(ctx, item.MagicSpiritId, define.NormalPondType)
            if err != nil {
                log.ErrorWithCtx(ctx, "GetMagicSpiritForCli get pond error: %v", err)
                return nil, err
            }
        }

        if len(pondData) == 0 {
            continue
        }

        presentIds := make([]uint32, 0, 8)
        for _, pondItem := range pondData {
            presentIds = append(presentIds, pondItem.PresentId)
        }

        respMagicSpiritForCLiItem.PresentIds = presentIds
        respMagicSpiritForCLi = append(respMagicSpiritForCLi, respMagicSpiritForCLiItem)
    }

    resp.MagicSpirits = respMagicSpiritForCLi
    return resp, nil
}

func (m *MagicSpirit) GetMagicSpiritUsable(ctx context.Context, in *pb.GetMagicSpiritUsableReq) (*pb.GetMagicSpiritUsableResp, error) {
    resp, err := m.mgr.GetMagicSpiritUsable(ctx, in)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetMagicSpiritUsable error: %v", err)
        return resp, err
    }

    log.Debugf("GetMagicSpiritUsable in:%+v, out:%+v", in, resp)
    return resp, nil
}

func (m *MagicSpirit) GetMagicSpiritOrderTotal(ctx context.Context, req *pb.GetMagicSpiritOrderTotalReq) (*pb.GetMagicSpiritOrderTotalResp, error) {
    out := &pb.GetMagicSpiritOrderTotalResp{}
    if req.GetBeginTime() == 0 || req.GetEndTime() == 0 {
        return out, nil
    }

    beginTime := time.Unix(int64(req.GetBeginTime()), 0)
    endTime := time.Unix(int64(req.GetEndTime()), 0)

    orderCnt, totalPrice, totalNum, err := m.mgr.GetMagicOrderTotal(ctx, beginTime, endTime, 0)
    if err != nil {
        log.Errorf("GetMagicSpiritOrderTotal fail to GetMagicOrderTotal. in:%+v, err:%v", req, err)
        return out, err
    }

    out.OrderCnt = orderCnt
    out.TotalPrice = totalPrice
    out.TotalNum = totalNum

    return out, nil
}

func (m *MagicSpirit) GetMagicSpiritAwardTotal(ctx context.Context, req *pb.GetMagicSpiritAwardTotalReq) (*pb.GetMagicSpiritAwardTotalResp, error) {
    out := &pb.GetMagicSpiritAwardTotalResp{}
    if req.GetBeginTime() == 0 || req.GetEndTime() == 0 {
        return out, nil
    }

    beginTime := time.Unix(int64(req.GetBeginTime()), 0)
    endTime := time.Unix(int64(req.GetEndTime()), 0)

    orderCnt, totalPrice, totalNum, err := m.mgr.GetMagicAwardTotal(ctx, beginTime, endTime)
    if err != nil {
        log.Errorf("GetMagicSpiritAwardTotal fail to GetMagicOrderTotal. in:%+v, err:%v", req, err)
        return out, err
    }

    out.OrderCnt = orderCnt
    out.TotalPrice = totalPrice
    out.TotalNum = totalNum

    return out, nil
}

type ReconcileParams struct {
    Source uint32 `json:"source"`
}

func unmarshalReconcileParams(str string) *ReconcileParams {
    param := &ReconcileParams{}
    if str == "" {
        return param
    }

    err := json.Unmarshal([]byte(str), param)
    if err != nil {
        log.Errorf("unmarshalReconcileParams fail. str:%s, err:%v", str, err)
    }

    return param
}

func (m *MagicSpirit) SendUnpackGift(ctx context.Context, in *pb.SendUnpackGiftReq) (*pb.SendUnpackGiftResp, error) {
    out := &pb.SendUnpackGiftResp{}

    if in.GetUid() == 0 || in.GetChannelId() == 0 || in.GetItemOrderId() == "" {
        return out, protocol.NewExactServerError(nil, status.ErrMagicSpiritSendLimit, "参数有误")
    }

    opt, err := m.mgr.OpenUnpackGift(ctx, in.GetUid(), in.GetChannelId(), in.GetItemOrderId(), false)
    if err != nil {
        log.Errorf("SendUnpackGift fail to OpenUnpackGift. in:%+v, err:%v", in, err)
        return out, err
    }

    out.SendOpt = opt

    log.Infof("SendUnpackGift in:%+v, out:%+v", in, out)
    return out, nil
}

func (m *MagicSpirit) CheckIfSendMagicWithSource(ctx context.Context, in *pb.CheckIfSendMagicWithSourceReq) (*pb.CheckIfSendMagicWithSourceResp, error) {
    out := &pb.CheckIfSendMagicWithSourceResp{}
    var err error
    _, err = m.mgr.CheckIfSendMagicWithSource(ctx, in)
    if err != nil {
        log.Errorf("CheckIfSendMagicWithSource fail to CheckIfSendMagicWithSource. in:%+v, err:%v", in, err)
        return out, err
    }

    return out, nil
}

func (m *MagicSpirit) SendMagicWithSource(ctx context.Context, in *pb.SendMagicWithSourceReq) (*pb.SendMagicWithSourceResp, error) {
    out := &pb.SendMagicWithSourceResp{}
    defer func() {
        log.InfoWithCtx(ctx, "SendMagicWithSource in:%+v, out:%+v", in, out)
    }()
    var err error
    out, err = m.mgr.SendMagicWithSource(ctx, in)
    return out, err
}

func (m *MagicSpirit) GetChannelAllUnpackGift(ctx context.Context, in *pb.GetChannelAllUnpackGiftReq) (*pb.GetChannelAllUnpackGiftResp, error) {
    return m.mgr.GetChannelAllUnpackGift(ctx, in.GetUid(), in.GetChannelId())
}

func (m *MagicSpirit) GetMagicSpiritExemptValue(ctx context.Context, req *pb.GetMagicSpiritExemptValueReq) (*pb.GetMagicSpiritExemptValueResp, error) {
    out := &pb.GetMagicSpiritExemptValueResp{}
    magicSendFlagList := make([]*pb.MagicSpiritExemptValue, 0)

    magicIdMap, err := m.mgr.GetUserExemptCondVal(ctx, req.GetUid(), req.GetMagicSpiritId()...)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetMagicSpiritExemptValue fail to GetUserExemptCondVal,err:%v", err)
        return out, err
    }

    for magicId, value := range magicIdMap {
        magicSendFlagList = append(magicSendFlagList, &pb.MagicSpiritExemptValue{
            MagicSpiritId: magicId,
            SendFlag:      value,
        })
    }

    out.ValueList = magicSendFlagList
    return out, err
}
