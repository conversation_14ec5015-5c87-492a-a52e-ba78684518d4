package server

import (
	"context"
	pb "golang.52tt.com/protocol/services/magic-spirit"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"strings"
	"strconv"
	"golang.52tt.com/services/magic-spirit/internal/mysql"
	logicPb "golang.52tt.com/protocol/app/magic-spirit-logic"
	"golang.52tt.com/pkg/protocol"
	"time"
	"golang.52tt.com/services/magic-spirit/internal/manager"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/services/magic-spirit/internal/conf"
	"golang.52tt.com/services/magic-spirit/internal/define"
)

func (m *MagicSpirit) AddMagicSpirit(c context.Context, req *pb.AddMagicSpiritReq) (*pb.AddMagicSpiritResp, error) {
	resp := &pb.AddMagicSpiritResp{}
	defer func() {
		log.InfoWithCtx(c, "AddMagicSpirit req:%+v, resp: %+v", req, resp)
	}()
	var err error

	err = validateAddMagicSpiritReq(req)
	if err != nil {
		log.ErrorWithCtx(c, "SetMagicSpirit invalid params: %v", err)
		return resp, err
	}

	data := req.MagicSpirit[0]
	id, err := m.mgr.AddMagicSpirit(c, data)
	if err != nil {
		log.ErrorWithCtx(c, "SetMagicSpirit add db fail, req:%+v, error:%v", req, err)
		return resp, err
	}
	resp.MagicSpiritId = []uint32{id}

	return resp, nil
}

func (m *MagicSpirit) DelMagicSpirit(c context.Context, req *pb.DelMagicSpiritReq) (*pb.DelMagicSpiritResp, error) {
	resp := &pb.DelMagicSpiritResp{}

	err := m.mgr.DelMagicSpirit(c, req.MagicSpiritIds)
	if err != nil {
		log.ErrorWithCtx(c, "DelMagicSpirit error: %v", err)
		return resp, err
	}

	return resp, nil
}

func (m *MagicSpirit) GetMagicSpirit(c context.Context, req *pb.GetMagicSpiritReq) (*pb.GetMagicSpiritResp, error) {
	resp := &pb.GetMagicSpiritResp{}

	data, err := m.mgr.GetMagicSpirit(c)
	if err != nil {
		log.ErrorWithCtx(c, "GetMagicSpirit get data fail, error: %v", err)
		return resp, err
	}

	respMagicSpirit := make([]*pb.MagicSpirit, 0, 8)
	for _, item := range data {
		cfg := &pb.MagicSpirit{
			MagicSpiritId:    item.MagicSpiritId,
			Name:             item.Name,
			IconUrl:          item.IconUrl,
			Price:            item.Price,
			Rank:             item.Rank,
			EffectBegin:      item.EffectBegin,
			EffectEnd:        item.EffectEnd,
			DescribeImageUrl: item.DescribeImageUrl,
			Describe:         item.Describe,
			JuniorLighting:   item.JuniorLighting,
			MiddleLighting:   item.MiddleLighting,
			VfxResource:      item.VfxResource,
			VfxResourceMd5:   item.VfxResourceMd5,
			UpdateTime:       uint32(item.UpdateTime.Unix()),
			RankFloat:        item.RankFloat,
			DescActivityUrl:  item.ActivityJumpUrl,
			ShowEffectEnd:    item.ShowEffectEnd,
			ActivityCfg: &pb.MagicSpiritActivityCfg{
				ActivityName:       item.ActName,
				BeginTime:          item.ActBeginTime.Unix(),
				EndTime:            item.ActEndTime.Unix(),
				ImageUrl:           item.ActImageUrl,
				JumpUrlTt:          item.ActivityJumpUrl,
				JumpUrlHcAndroid:   item.ActJumpUrlHcAndroid,
				JumpUrlHcIos:       item.ActJumpUrlHcIos,
				JumpUrlMikeAndroid: item.ActJumpUrlMikeAndroid,
				JumpUrlMikeIos:     item.ActJumpUrlMikeIos,
			},
		}

		if item.ChannelTypeList == "" {
			cfg.ChannelTypeList = nil
			respMagicSpirit = append(respMagicSpirit, cfg)
			continue
		}
		splitElements := strings.Split(item.ChannelTypeList, ",")
		channelTypeList := make([]uint32, 0, len(splitElements))
		for _, element := range splitElements {
			channelType, err := strconv.ParseUint(element, 10, 32)
			if err != nil {
				log.WarnWithCtx(c, "GetMagicSpirit convert channelType error: %v", err)
				continue
			}
			channelTypeList = append(channelTypeList, uint32(channelType))
		}
		cfg.ChannelTypeList = channelTypeList
		respMagicSpirit = append(respMagicSpirit, cfg)

		// 礼物上下架时间段
		if item.MagicTimeList == "" {
			if item.EffectBegin != 0 && item.EffectEnd != 0 {
				cfg.MagicEffectTimeList = []*pb.TimeRange{
					{
						StartTime: int64(item.EffectBegin),
						EndTime:   int64(item.EffectEnd),
					},
				}
			} else {
				log.WarnWithCtx(c, "GetMagicSpirit invalid magicTimeList: %v", item.MagicTimeList)
			}
		} else {
			cfg.MagicEffectTimeList = conf.Str2TimeRangeListPb(c, item.MagicTimeList)
		}

		// 特殊池配置
		cfg.SpecialPondConf = &pb.SpecialPondConf{
			DescribeImageUrl: item.SpDescImg,
			DescribeJumpUrl:  item.SpDescJumpUrl,
			EffectTimeList:   conf.Str2TimeRangeListPb(c, item.SpPondTimeList),
		}

	}
	resp.MagicSpirit = respMagicSpirit

	return resp, nil
}

func (m *MagicSpirit) UpdateMagicSpirit(c context.Context, req *pb.UpdateMagicSpiritReq) (*pb.UpdateMagicSpiritResp, error) {
	resp := &pb.UpdateMagicSpiritResp{}
	defer func() {
		log.InfoWithCtx(c, "UpdateMagicSpirit req:%+v, resp: %+v", req, resp)
	}()
	var err error

	err = validateUpdateMagicSpiritReq(req)
	if err != nil {
		log.ErrorWithCtx(c, "UpdateMagicSpirit invalid params: %v", err)
		return resp, err
	}
	magicSpirit := req.GetMagicSpirit()[0]
	err = m.mgr.SetMagicSpiritTemporary(c, magicSpirit)
	if err != nil {
		log.ErrorWithCtx(c, "UpdateMagicSpirit error: %v", err)
		return resp, err
	}

	return resp, nil
}

func (m *MagicSpirit) AddMagicSpiritPond(c context.Context, req *pb.AddMagicSpiritPondReq) (*pb.AddMagicSpiritPondResp, error) {
	resp := &pb.AddMagicSpiritPondResp{}
	defer func() {
		log.InfoWithCtx(c, "AddMagicSpiritPond req:%+v, resp: %+v", req, resp)
	}()

	effectTime := func() time.Time {
		if req.GetEffectTime() == 0 {
			return time.Now()
		}
		return time.Unix(req.GetEffectTime(), 0)
	}()

	data := make([]*mysql.MagicSpiritPond, 0)
	for _, item := range req.MagicSpiritPondItems {
		if item.GetPrizeLevel() == uint32(logicPb.MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_LV4) && item.GetBreakingNewId() == 0 {
			log.ErrorWithCtx(c, "AddMagicSpiritPond error: 全服公告大奖Id不能为0")
			return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "全服公告大奖Id不能为0")
		}
		data = append(data, &mysql.MagicSpiritPond{
			MagicSpiritId:  item.MagicSpiritId,
			Weight:         item.Weight,
			PresentId:      item.PresentId,
			Price:          item.ItemId,
			PrizeLevel:     item.PrizeLevel,
			BeginTime:      effectTime,
			EndTime:        time.Date(2038, 01, 01, 0, 0, 0, 0, time.Local), // 先设置成一个较大的数
			BreakingNewsId: item.GetBreakingNewId(),
		})
	}
	var err error
	var isSpecialPool bool
	if req.GetPondType() == uint32(pb.AddMagicSpiritPondReq_SPECIAL_POND) {
		isSpecialPool = true
	}

	_, err = m.mgr.AddMagicSpiritPond(c, data, isSpecialPool)
	if err != nil {
		log.ErrorWithCtx(c, "AddMagicSpiritPond error: %v", err)
		return resp, err
	}

	log.InfoWithCtx(c, "AddMagicSpiritPond succes isSpecial:%v", isSpecialPool)
	return resp, nil
}

func (m *MagicSpirit) GetMagicSpiritPond(c context.Context, req *pb.GetMagicSpiritPondReq) (*pb.GetMagicSpiritPondResp, error) {
	resp := &pb.GetMagicSpiritPondResp{}

	data, err := m.mgr.GetMagicSpiritPond(c, req.MagicSpiritId, define.NormalPondType)
	if err != nil {
		log.ErrorWithCtx(c, "GetMagicSpiritPond error: %v", err)
		return resp, nil
	}

	resp.MagicSpiritPond = storePondData2Pb(data)

	// 特殊奖池
	specialData, err := m.mgr.GetMagicSpiritPond(c, req.MagicSpiritId, define.SpecialPondType)
	if err != nil {
		log.ErrorWithCtx(c, "GetMagicSpiritPond error: %v", err)
		return resp, nil
	}
	resp.SpecialMagicSpiritPond = storePondData2Pb(specialData)
	return resp, nil
}

func (m *MagicSpirit) GetCommonConf(c context.Context, req *pb.GetCommonConfReq) (*pb.GetCommonConfResp, error) {
	resp := &pb.GetCommonConfResp{}

	conf, err := m.mgr.GetCommonConf(c)
	if err != nil {
		log.ErrorWithCtx(c, "GetCommonConf mgr get error: %v", err)
		return resp, err
	}

	rsConf := make([]*pb.CommonConf, 0, 8)
	for _, item := range conf {
		val := uint32(0)
		if item.ValueType == manager.CONF_VAL_TYPE_INT {
			tmpVal, err := strconv.ParseInt(item.Value, 10, 64)
			if err != nil {
				log.ErrorWithCtx(c, "GetCommonConf convert conf type error: %v, confId: %v", item.ConfId)
				continue
			}
			val = uint32(tmpVal)
		}

		rsConf = append(rsConf, &pb.CommonConf{
			ConfType: item.ConfId,
			Value:    val,
		})
	}

	resp.CommonConf = rsConf
	return resp, nil
}

func (m *MagicSpirit) SetCommonConf(c context.Context, req *pb.SetCommonConfReq) (*pb.SetCommonConfResp, error) {
	resp := &pb.SetCommonConfResp{}

	data := make([]*mysql.MagicSpiritCommonConf, 0, 8)
	for _, item := range req.CommonConf {
		data = append(data, &mysql.MagicSpiritCommonConf{
			ConfId:    item.ConfType,
			Value:     strconv.FormatInt(int64(item.Value), 10),
			ValueType: manager.CONF_VAL_TYPE_INT,
		})
	}
	err := m.mgr.SetCommonConf(c, data)
	if err != nil {
		log.ErrorWithCtx(c, "SetCommonConf error: %v", err)
		return resp, err
	}

	return resp, nil
}

func (m *MagicSpirit) GetMagicSpiritConfTmp(ctx context.Context, req *pb.GetMagicSpiritConfTmpReq) (*pb.GetMagicSpiritConfTmpResp, error) {
	out := &pb.GetMagicSpiritConfTmpResp{}

	// 获取幸运礼物配置
	magicTmps, err := m.mgr.GetMagicSpiritTmp(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritConfTmp fail to GetMagicSpiritTmp,err:%v", err)
		return out, err
	}

	magicInfoMap := make(map[uint32]*pb.MagicSpiritTmp)
	for _, v := range magicTmps {
		magicInfoMap[v.GetConf().GetMagicSpiritId()] = v
	}

	// 获取奖池配置
	magicTmpNormalPondMap, err := m.mgr.GetMagicSpiritPondTmp(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritConfTmp fail to GetMagicSpiritPondTmp,err:%v", err)
		return out, err
	}
	magicIds := make([]uint32, 0)
	for k := range magicTmpNormalPondMap {
		magicIds = append(magicIds, k)
	}

	log.DebugWithCtx(ctx, "GetMagicSpiritConfTmp magicIds:%v", magicIds)
	mapMagicConf := make(map[uint32]*pb.MagicSpirit, len(magicIds))
	if len(magicIds) > 0 {
		// 获取幸运礼物by magicSpiritIds
		mapMagicConf, err = m.mgr.GetMagicSpiritByIds(ctx, magicIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMagicSpiritConfTmp fail to GetMagicSpiritByIds,err:%v, magicIds:%v", err, magicIds)
			return out, err
		}
	}

	addFlagMap := make(map[uint32]struct{})
	tmpList := make([]*pb.MagicSpiritConfTmp, 0, len(magicIds)+len(magicTmps))
	for _, v := range magicTmps {
		pendingConfType := []uint32{uint32(pb.PendingConfType_PENDING_CONF_MAGIC_SPIRIT)}
		info := &pb.MagicSpiritConfTmp{
			MagicSpiritTmp: v,
		}
		// 是否有未生效奖池
		normalPond := magicTmpNormalPondMap[v.GetConf().GetMagicSpiritId()]
		if normalPond == nil {
			// 没有未生效奖池，获取普通奖池
			normalPond, err = m.mgr.GetMagicSpiritPond(ctx, v.GetConf().GetMagicSpiritId(), define.NormalPondType)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMagicSpiritPond error: %v", err)
				return nil, err
			}
		} else {
			pendingConfType = append(pendingConfType, uint32(pb.PendingConfType_PENDING_CONF_MAGIC_SPIRIT_POND))
		}
		info.Pool = storePondData2Pb(normalPond)

		// 获取特殊奖池
		specialData, err := m.mgr.GetMagicSpiritPond(ctx, v.GetConf().GetMagicSpiritId(), define.SpecialPondType)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMagicSpiritPond error: %v", err)
		}
		info.SpecialPool = storePondData2Pb(specialData)
		info.HasPool = len(info.Pool) > 0 || len(info.SpecialPool) > 0

		info.PendingConfTypeList = pendingConfType
		tmpList = append(tmpList, info)
		addFlagMap[v.GetConf().GetMagicSpiritId()] = struct{}{}
	}

	for _, v := range magicIds {
		if _, ok := addFlagMap[v]; ok {
			continue
		}
		info := &pb.MagicSpiritConfTmp{
			MagicSpiritTmp: &pb.MagicSpiritTmp{
				Id:   v,
				Conf: mapMagicConf[v],
			},
			Pool:                storePondData2Pb(magicTmpNormalPondMap[v]),
			PendingConfTypeList: []uint32{uint32(pb.PendingConfType_PENDING_CONF_MAGIC_SPIRIT_POND)},
		}

		// 获取特殊奖池
		specialData, err := m.mgr.GetMagicSpiritPond(ctx, v, define.SpecialPondType)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMagicSpiritPond error: %v", err)
		}
		info.SpecialPool = storePondData2Pb(specialData)

		info.HasPool = len(info.Pool) > 0 || len(info.SpecialPool) > 0
		tmpList = append(tmpList, info)
		addFlagMap[v] = struct{}{}
	}

	out.ConfList = tmpList
	return out, nil
}

func storePondData2Pb(pondData []*mysql.MagicSpiritPond) []*pb.MagicSpiritPondItem {
	out := make([]*pb.MagicSpiritPondItem, 0, len(pondData))
	for _, v := range pondData {
		out = append(out, &pb.MagicSpiritPondItem{
			ItemId:        v.ItemId,
			MagicSpiritId: v.MagicSpiritId,
			PresentId:     v.PresentId,
			Weight:        v.Weight,
			PrizeLevel:    v.PrizeLevel,
			UpdateTime:    uint32(v.CreateTime.Unix()),
			EffectTime:    uint32(v.BeginTime.Unix()),
			BreakingNewId: v.BreakingNewsId,
		})
	}
	return out
}
