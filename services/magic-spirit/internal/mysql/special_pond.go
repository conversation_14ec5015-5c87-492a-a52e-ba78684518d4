package mysql

import (
	"fmt"
	"database/sql"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"context"
)

const (
	SpecialPondTableName = "magic_spirit_pond_special"
)

// 特殊奖池，每次覆盖更新
var specialPondCreate = `CREATE TABLE magic_spirit_pond_special (
  item_id int(10) unsigned NOT NULL AUTO_INCREMENT,
  magic_spirit_id int(10) unsigned NOT NULL DEFAULT '0' COMMENT '礼物id',
  weight int(10) unsigned NOT NULL DEFAULT '0' COMMENT '礼物权重',
  present_id int(10) unsigned NOT NULL DEFAULT '0' COMMENT '原礼物id',
  price int(10) unsigned DEFAULT '0' COMMENT '礼物价值',
  prize_level int(10) unsigned NOT NULL DEFAULT '0',
  breaking_news_id int(10) unsigned NOT NULL DEFAULT '0' COMMENT '全服公告id',
  create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  begin_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',
  end_time timestamp NOT NULL DEFAULT '2038-01-01 00:00:00' COMMENT '失效时间',
  is_del tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (item_id),
  UNIQUE KEY idx_magic_present_begin (magic_spirit_id,present_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
`

// CreateSpecialPondTable 建表
func (s *Store) CreateSpecialPondTable() error {
	return s.db.Exec(specialPondCreate).Error
}

// UpdateSpecialPond 覆盖更新特殊奖池：删除原特殊奖池，插入新特殊奖池
func (s *Store) UpdateSpecialPond(ctx context.Context, magicId uint32, list []*MagicSpiritPond) error {
	tx := s.db.BeginTx(ctx, &sql.TxOptions{})
	if tx.Error != nil {
		log.ErrorWithCtx(ctx, "UpdateSpecialPond tx.Begin err: %v", tx.Error)
		return tx.Error
	}
	// 删除原特殊奖池
	err := tx.Table(SpecialPondTableName).Where("magic_spirit_id = ?", magicId).Delete(&MagicSpiritPond{}).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateSpecialPond delete %d err: %v", magicId, err)
		tx.Rollback()
	}

	// 使用gorm
	for _, item := range list {
		err = tx.Table(SpecialPondTableName).Create(item).Error
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateSpecialPond insert err: %v", err)
			tx.Rollback()
		}
	}

	tx.Commit()
	log.InfoWithCtx(ctx, "UpdateSpecialPond success list:%v", list)
	return nil
}

// GetSpecialPondByMagicId 获取特殊奖池by magicId
func (s *Store) GetSpecialPondByMagicId(ctx context.Context, magicId uint32) ([]*MagicSpiritPond, error) {
	query := fmt.Sprintf(`SELECT item_id, magic_spirit_id, weight, present_id, price, prize_level, breaking_news_id, create_time, update_time, is_del FROM %s WHERE magic_spirit_id = ?`, SpecialPondTableName)
	var list []*MagicSpiritPond
	err := s.db.Raw(query, magicId).Scan(&list).Error
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetSpecialPondByMagicId err: %v", err)
		return nil, err
	}
	return list, nil
}

// GetSpecialPondMaxUpdateTime 获取特殊奖池版本
func (s *Store) GetSpecialPondMaxUpdateTime(ctx context.Context) (int64, error) {
	var maxUpdateTime int64

	// 使用 Row() 获取单个字段
	row := s.db.Table(SpecialPondTableName).
		Select("UNIX_TIMESTAMP(MAX(update_time))").
		Row()

	err := row.Scan(&maxUpdateTime)
	if err != nil {
		return 0, err
	}
	return maxUpdateTime, nil
}
