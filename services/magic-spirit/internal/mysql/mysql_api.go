package mysql

import(
	context "context"
	time "time"
	gorm "github.com/jinzhu/gorm"
)

type IStore interface {
	AddBlacklist(ctx context.Context, blacklist []*MagicSpiritBlacklist) ([]uint32,error)
	AddMagicSpirit(ctx context.Context, spirit *MagicSpirit) (uint32,error)
	AddMagicSpiritPond(ctx context.Context, tx *gorm.DB, pond []*MagicSpiritPond) ([]uint32,error)
	AddMagicSpiritTmp(ctx context.Context, spirit *MagicSpiritTemporary) (uint32,error)
	AddNormalPondTmp(ctx context.Context, pond []*MagicSpiritPondTmp) error
	BatchGetMagicSpiritLimitChannelTypeList(ctx context.Context, magicSpiritIds []uint32) (map[uint32][]uint32,error)
	CreateMagicSpiritOrder(tx *gorm.DB, order *MagicSpiritOrder) error
	CreatePondTmpTable() error
	CreateSpecialPondTable() error
	CreateTable() 
	DelBlacklist(ctx context.Context, blacklistIds []uint32) error
	DelMagicSpirit(ctx context.Context, magicIds []uint32) error
	DelMagicSpiritPond(ctx context.Context, magicId uint32, itemIds []uint32) error
	DelMagicSpiritTmp(ctx context.Context, ids []uint32) error
	GetAwardCntSumById(giftIdList []uint32, beginTime, endTime time.Time) (*SumInfo,error)
	GetAwardStatistics(beginTime, endTime time.Time) (*Statistics,error)
	GetAwardTotalInfo(queryMonthTime, beginTime, endTime time.Time) (*TotalInfo,error)
	GetBlacklist(ctx context.Context, channelId, pageNum, pageSize uint32) (uint32,[]*MagicSpiritBlacklist,error)
	GetBlacklistCIds(ctx context.Context) ([]uint32,error)
	GetChannelOrderListByStatus(tx *gorm.DB, channelId, status uint32, queryMonthTime time.Time) ([]*MagicSpiritOrder,error)
	GetCommonConf(ctx context.Context) ([]*MagicSpiritCommonConf,error)
	GetEffectivePondTmp(ctx context.Context) ([]*MagicSpiritPondTmp,error)
	GetMagicAwardOrderIdList(queryTime, begin, end time.Time) ([]string,error)
	GetMagicConsumeOrderIdList(queryTime, begin, end time.Time, status, source uint32) ([]string,error)
	GetMagicSpirit(ctx context.Context) ([]*MagicSpirit,error)
	GetMagicSpiritAwardLog(tx *gorm.DB, orderId string, queryMonthTime time.Time) (*MagicSpiritAwardLog,bool,error)
	GetMagicSpiritAwardLogs(tx *gorm.DB, beginTime, endTime time.Time, limit uint32) ([]*MagicSpiritAwardLog,error)
	GetMagicSpiritById(ctx context.Context, magicSpiritId uint32) (*MagicSpirit,error)
	GetMagicSpiritByIds(ctx context.Context, magicSpiritIds []uint32) (map[uint32]*MagicSpirit,error)
	GetMagicSpiritMaxUpdateTime(ctx context.Context) (int64,error)
	GetMagicSpiritOrder(tx *gorm.DB, orderId string, queryMonthTime time.Time) (*MagicSpiritOrder,bool,error)
	GetMagicSpiritPond(ctx context.Context, magicSpiritId uint32) ([]*MagicSpiritPond,error)
	GetMagicSpiritTmp(ctx context.Context, beginTime time.Time) ([]*MagicSpiritTemporary,error)
	GetMagicSpiritTmpEffective(ctx context.Context, cur time.Time) ([]*MagicSpiritTemporary,error)
	GetNormalPondMaxUpdateTime(ctx context.Context) (int64,error)
	GetNormalPondTmp(ctx context.Context) ([]*MagicSpiritPondTmp,error)
	GetOrderTotalInfo(queryMonthTime, beginTime, endTime time.Time, status, source uint32) (*TotalInfo,error)
	GetReconcileSumStats(begin, end time.Time) ([]*ReconcileDataLog,error)
	GetSpecialPondByMagicId(ctx context.Context, magicId uint32) ([]*MagicSpiritPond,error)
	GetSpecialPondMaxUpdateTime(ctx context.Context) (int64,error)
	RecordMagicSpiritAwardLog(tx *gorm.DB, info *MagicSpiritAwardLog) error
	RecordMagicSpiritAwardLogs(tx *gorm.DB, list []*MagicSpiritAwardLog) error
	RecordReconcileDataLogs(list []*ReconcileDataLog) error
	SetCommonConf(ctx context.Context, conf []*MagicSpiritCommonConf) error
	SetMagicSpiritUpdateFlag(ctx context.Context, tx *gorm.DB, magicId uint32, beginTime time.Time) error
	Transaction(ctx context.Context, f func(tx *gorm.DB) error) error
	UpdateMagicSpirit(ctx context.Context, tx *gorm.DB, spirit *MagicSpirit) error
	UpdateMagicSpiritAwardDone(tx *gorm.DB, orderId string, queryMonthTime, awardTime time.Time) (bool,error)
	UpdateMagicSpiritAwardTBeanTime(tx *gorm.DB, magicOrderId, tBeanTime, dealToken string, queryMonthTime time.Time) (bool,error)
	UpdateMagicSpiritOrderStatus(tx *gorm.DB, orderId string, queryMonthTime time.Time, sourceStatusList []uint32, targetStatus uint32) (bool,error)
	UpdateMagicSpiritOrderTBeanTime(tx *gorm.DB, orderId, tBeanTime string, queryMonthTime time.Time) (bool,error)
	UpdatePondTmpFlag(ctx context.Context, tx *gorm.DB, ids []uint32) (int64,error)
	UpdateSpecialPond(ctx context.Context, magicId uint32, list []*MagicSpiritPond) error
}


type IMagicSpiritCommonConf interface {
	TableName() string
}


type IMagicSpiritPond interface {
	TableName() string
}


type IReconcileDataLog interface {
	TableName() string
}


type IMagicSpiritAwardLog interface {
	TableName() string
}


type IMagicSpiritBlacklist interface {
	TableName() string
}


type IMagicSpiritOrder interface {
	TableName() string
}


type IMagicSpirit interface {
	TableName() string
}

