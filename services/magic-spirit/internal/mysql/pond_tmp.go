package mysql

import (
	"time"
	"database/sql"
	"context"
	"errors"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"fmt"
	"github.com/jinzhu/gorm"
)

const (
	TmpPondTableName = "magic_spirit_pond_tmp"
)

// 未生效的普通奖池表
var pondStoreTmpCreate = `CREATE TABLE magic_spirit_pond_tmp (
  item_id int(10) unsigned NOT NULL AUTO_INCREMENT,
  magic_spirit_id int(10) unsigned NOT NULL DEFAULT '0' COMMENT '礼物id',
  weight int(10) unsigned NOT NULL DEFAULT '0' COMMENT '礼物权重',
  present_id int(10) unsigned NOT NULL DEFAULT '0' COMMENT '原礼物id',
  price int(10) unsigned DEFAULT '0' COMMENT '礼物价值',
  prize_level int(10) unsigned NOT NULL DEFAULT '0',
  breaking_news_id int(10) unsigned NOT NULL DEFAULT '0' COMMENT '全服公告id',
  
  begin_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',
  update_flag int(10) unsigned DEFAULT '0' COMMENT '更新标记',
  is_del tinyint(1) NOT NULL DEFAULT '0',
  
  create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  
  PRIMARY KEY (item_id),
  KEY idx_begin_time(begin_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
`

// CreatePondTmpTable 建表
func (s *Store) CreatePondTmpTable() error {
	return s.db.Exec(pondStoreTmpCreate).Error
}

type MagicSpiritPondTmp struct {
	ItemId         uint32 `gorm:"primary_key;autoIncrement"`
	MagicSpiritId  uint32 `gorm:"index:magic_id_idx;type:int(10) unsigned not null;default:0;comment:'礼物id'"`
	Weight         uint32 `gorm:"type:int(10) unsigned not null;default:0;comment:'礼物权重'"`
	PresentId      uint32 `gorm:"type:int(10) unsigned not null;default:0;comment:'原礼物id'"`
	Price          uint32 `gorm:"type:int(10) unsigned not null;default:0;comment:'原礼物价值'"`
	PrizeLevel     uint32 `gorm:"type:int(10) unsigned not null;default:0;comment:'中奖特效等级'"`
	BreakingNewsId uint32 `gorm:"type:int(10) unsigned not null;default:0;comment:'全服公告id'"`

	BeginTime  time.Time `gorm:"type:timestamp not null;default:current_timestamp;comment:'生效时间'"`
	UpdateFlag uint32    `gorm:"type:int(10) unsigned not null;default:0;comment:'更新标记'"`
	IsDel      uint32    `gorm:"type:tinyint(1) NOT NULL;default:0;comment:'已删除'"`

	CreateTime time.Time `gorm:"type:timestamp not null;default:current_timestamp;"`
	UpdateTime time.Time `gorm:"type:timestamp not null;default:current_timestamp;comment:'修改时间'"`
}

// AddNormalPondTmp 新增记录
// 使用gorm事务，首先将未生效的对应奖池记录都置为is_del=1
// 然后将新记录插入到数据库中,提交记录
func (s *Store) AddNormalPondTmp(ctx context.Context, pond []*MagicSpiritPondTmp) error {
	if len(pond) == 0 {
		return errors.New("add pond is nil")
	}

	magicSpiritId := pond[0].MagicSpiritId

	tx := s.db.BeginTx(ctx, &sql.TxOptions{})
	var err error
	// 首先将未生效的对应奖池记录都置为is_del=1
	err = tx.Table(TmpPondTableName).Where("magic_spirit_id=?", pond[0].MagicSpiritId).Update("is_del", 1).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "Update magic_spirit is_del error: %v,pond:%v", err, pond)
		tx.Rollback()
		return err
	}

	for _, v := range pond {
		err = tx.Table(TmpPondTableName).Create(v).Error
		if err != nil {
			log.ErrorWithCtx(ctx, "Create magic_spirit error: %v,pond:%v", err, pond)
			tx.Rollback()
			return err
		}
	}

	err = tx.Commit().Error
	if err != nil {
		log.ErrorWithCtx(ctx, "Commit magic_spirit error: %v,pond:%v", err, pond)
		tx.Rollback()
		return err
	}

	var addLog string
	addLog += fmt.Sprintf("magic_id:%d,add_pond_tmp success,begin_time:%v", magicSpiritId, pond[0].BeginTime)
	for _, v := range pond {
		addLog += fmt.Sprintf("w:%d,present_id:%d,price:%d,prize_level:%d,news_id:%d,\n", v.Weight, v.PresentId, v.Price, v.PrizeLevel, v.BreakingNewsId)
	}

	log.InfoWithCtx(ctx, addLog)
	return nil
}

// GetNormalPondTmp 根据幸运礼物id获取待生效的配置
func (s *Store) GetNormalPondTmp(ctx context.Context) ([]*MagicSpiritPondTmp, error) {
	pondList := make([]*MagicSpiritPondTmp, 0)
	err := s.db.Table(TmpPondTableName).Where("begin_time>? and is_del=0", time.Now()).Find(&pondList).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNormalPondTmp error: %v", err)
		return nil, err
	}

	return pondList, nil
}

// GetEffectivePondTmp 获取已生效的奖池配置更新到最终奖池表中
func (s *Store) GetEffectivePondTmp(ctx context.Context) ([]*MagicSpiritPondTmp, error) {
	pondList := make([]*MagicSpiritPondTmp, 0)
	now := time.Now()
	err := s.db.Table(TmpPondTableName).Where("begin_time<=? and update_flag=0 and is_del=0", now).Find(&pondList).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEffectivePondTmp error: %v", err)
		return nil, err
	}
	return pondList, nil
}

// UpdatePondTmpFlag 更新生效标记
func (s *Store) UpdatePondTmpFlag(ctx context.Context, tx *gorm.DB, ids []uint32) (int64, error) {
	res := tx.Table(TmpPondTableName).Where("item_id in (?)", ids).Update("update_flag", 1)
	if res.Error != nil {
		log.ErrorWithCtx(ctx, "UpdatePondTmpFlag error: %v", res.Error)
		return 0, res.Error
	}

	return res.RowsAffected, nil
}
