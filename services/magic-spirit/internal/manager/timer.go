package manager

import (
	"bytes"
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	pb "golang.52tt.com/protocol/services/magic-spirit"
	"golang.52tt.com/services/magic-spirit/internal/mysql"
	"golang.52tt.com/services/tt-rev/common/feishu"
	"reflect"
	"runtime"
	"runtime/debug"
	"strconv"
	"strings"
	"time"
	"golang.52tt.com/services/magic-spirit/internal/define"
	"golang.52tt.com/services/magic-spirit/internal/conf"
	"github.com/jinzhu/gorm"
	"errors"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
)

var lastWarnTime time.Time

func (m *MagicSpiritMgr) StartTimer() {
	// 未开箱礼物队列
	go m.TimerHandle(100*time.Millisecond, m.handleTimeoutUnpackGift)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	timerD, err := timer.NewTimerD(ctx, "magic-spirit", timer.WithV8RedisCmdable(m.cache.GetRedisClient()))
	if err != nil {
		log.Errorf("NewTimerD fail to NewTimerD. err:%v", err)
		return
	}

	// 亏损监控
	timerD.AddIntervalTask("CheckProfit", 2*time.Second, timer.BuildFromLambda(m.CheckProfit))
	// 小时数据播报
	timerD.AddIntervalTask("HourReport", time.Minute, timer.BuildFromLambda(m.HourReport))
	// 日数据播报
	timerD.AddIntervalTask("DayReport", time.Minute, timer.BuildFromLambda(m.DayReport))
	// 更新幸运礼物配置
	//timerD.AddIntervalTask("CheckAndUpdateMagicSpiritV3", 3*time.Second, timer.BuildFromLambda(m.CheckAndUpdateMagicSpirit))
	timerD.AddLocalIntervalTask(1*time.Second, tasks.FuncTask(func(ctx context.Context) {
		m.CheckAndUpdateMagicSpirit()
	}))

	// 更新奖池
	timerD.AddLocalIntervalTask(2*time.Second, tasks.FuncTask(func(ctx context.Context) {
		m.CheckMagicPondUpdate()
	}))

	// 更新待生效奖池
	timerD.AddLocalIntervalTask(1*time.Second, tasks.FuncTask(func(ctx context.Context) {
		m.checkUpdateNormalPond()
	}))

	timerD.Start()

	m.timeD = timerD
}

func (m *MagicSpiritMgr) CheckProfit(c context.Context) {
	now := time.Now()
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	inFusing, err := m.cache.CheckIfFusing(ctx)
	if err != nil {
		log.Errorf("CheckProfit fail to CheckIfFusing. err:%v", err)
	}
	if inFusing {
		return
	}

	profit, err := m.cache.GetHourProfit(ctx, now.Hour())
	if err != nil {
		log.Errorf("CheckProfit fail to GetHourProfit. err:%v", err)
		return
	}

	log.Debugf("CheckProfit profit:%v", profit)
	timeStr := now.Format("2006年01月02日 15:04:05")

	if profit+m.bc.GetProfitFusingMin() <= 0 {
		// 亏损停服
		err = m.cache.SetFusing(ctx)
		if err != nil {
			log.Errorf("CheckProfit fail to SetFusing. err:%v", err)
			return
		}

		_ = m.sendFeiShuMsg("停服通知", []string{timeStr, "亏损已高于停服值，停服啦！！！"}, "all")

	} else if profit+m.bc.GetProfitWarnMin() <= 0 {
		// 亏损警告
		err = m.cache.SetWarning(ctx)
		if err != nil {
			log.Errorf("CheckProfit fail to GetProfitWarnMin. err:%v", err)
			return
		}

		// 一分钟告警一次
		if lastWarnTime.Add(time.Minute).After(now) {
			return
		}
		lastWarnTime = now

		_ = m.sendFeiShuMsg("亏损告警", []string{timeStr, fmt.Sprintf("本小时已亏损%0.2f元", float64(profit)/100)}, "")

	} else {
		// 亏损未达警告值
		inWarning, err := m.cache.CheckIfWarning(ctx)
		if err != nil {
			log.Errorf("CheckProfit fail to CheckIfWarning. err:%v", err)
		}

		if inWarning {
			// 告警解除
			err = m.cache.DelWarning(ctx)
			if err != nil {
				log.Errorf("CheckProfit fail to DelWarning. err:%v", err)
			}

			_ = m.sendFeiShuMsg("亏损报警解除", []string{timeStr, "亏损已低于告警值，恢复正常"}, "")
		}
	}
}

type Stats struct {
	Profit       int64
	ProfitRatio  float64
	ConsumeCnt   int64
	ConsumePrice int64
	AwardPrice   int64
	TotalNum     int64
}

func (m *MagicSpiritMgr) HourReport(c context.Context) {
	if !m.bc.GetLotteryDataReportEnable() {
		return
	}

	now := time.Now()
	if now.Minute() != 0 {
		return
	}

	lastHour := now.Add(-time.Hour)
	cycleBegin := time.Date(lastHour.Year(), lastHour.Month(), lastHour.Day(), lastHour.Hour(), 0, 0, 0, time.Local)
	cycleEnd := cycleBegin.Add(time.Hour)
	widerCycleBegin := time.Date(lastHour.Year(), lastHour.Month(), lastHour.Day(), 0, 0, 0, 0, time.Local)

	cycleStats := m.getAwardStatistics(cycleBegin, cycleEnd)
	lastCycleStats := m.getAwardStatistics(cycleBegin.Add(-time.Hour), cycleBegin)
	lastWiderCycleStats := m.getAwardStatistics(cycleBegin.Add(-24*time.Hour), cycleEnd.Add(-24*time.Hour))
	widerCycleStats := m.getAwardStatistics(widerCycleBegin, cycleEnd)

	giftIdList, err := m.getBigAwardGiftIds(context.Background())
	if err != nil {
		log.Errorf("HourReport fail to getBigAwardGiftIds. err:%v", err)
		return
	}

	hourBigRatio, hourBigCnt, hourConsumeCnt := m.getBigAwardRatio(cycleBegin, cycleEnd, giftIdList, cycleStats.TotalNum)
	dayBigRatio, dayBigCnt, dayConsumeCnt := m.getBigAwardRatio(widerCycleBegin, cycleEnd, giftIdList, widerCycleStats.TotalNum)

	strList := []string{
		lastHour.Format("2006年01月02日 15点"),
		fmt.Sprintf("本小时利润%d元， 利润率为%0.4f%%，同比%+0.2f%%，环比%+0.2f%%",
			cycleStats.Profit, cycleStats.ProfitRatio*100, getPercentage(cycleStats.Profit, lastWiderCycleStats.Profit)-100, getPercentage(cycleStats.Profit, lastCycleStats.Profit)-100),
		fmt.Sprintf("本小时购买人数%d，同比%+0.2f%%，环比%+0.2f%%",
			cycleStats.ConsumeCnt, getPercentage(cycleStats.ConsumeCnt, lastWiderCycleStats.ConsumeCnt)-100, getPercentage(cycleStats.ConsumeCnt, lastCycleStats.ConsumeCnt)-100),
		fmt.Sprintf("本小时购买金额%d元，同比%+0.2f%%，环比%+0.2f%%",
			cycleStats.ConsumePrice, getPercentage(cycleStats.ConsumePrice, lastWiderCycleStats.ConsumePrice)-100, getPercentage(cycleStats.ConsumePrice, lastCycleStats.ConsumePrice)-100),
		fmt.Sprintf("本小时爆出奖励金额%d元，同比%+0.2f%%，环比%+0.2f%%",
			cycleStats.AwardPrice, getPercentage(cycleStats.AwardPrice, lastWiderCycleStats.AwardPrice)-100, getPercentage(cycleStats.AwardPrice, lastCycleStats.AwardPrice)-100),

		fmt.Sprintf("本小时开出大奖个数%d", hourBigCnt),
		fmt.Sprintf("本小时开出大奖人数%d", hourConsumeCnt),
		fmt.Sprintf("本小时大奖出奖率%0.2f%%", hourBigRatio*100),

		fmt.Sprintf("今日利润%d元， 利润率为%0.4f%%", widerCycleStats.Profit, widerCycleStats.ProfitRatio*100),
		fmt.Sprintf("今日购买人数%d", widerCycleStats.ConsumeCnt),
		fmt.Sprintf("今日购买金额%d", widerCycleStats.ConsumePrice),
		fmt.Sprintf("今日爆出奖励金额%d", widerCycleStats.AwardPrice),
		fmt.Sprintf("今日开出大奖个数%d", dayBigCnt),
		fmt.Sprintf("今日开出大奖人数%d", dayConsumeCnt),
		fmt.Sprintf("今日大奖出奖率%0.2f%%", dayBigRatio*100),
	}

	_ = m.sendFeiShuMsg("小时数据", strList, "")
}

func (m *MagicSpiritMgr) DayReport(c context.Context) {
	if !m.bc.GetLotteryDataReportEnable() {
		return
	}

	now := time.Now()
	if now.Hour() != 0 || now.Minute() != 0 {
		return
	}

	lastDay := now.Add(-24 * time.Hour)
	cycleBegin := time.Date(lastDay.Year(), lastDay.Month(), lastDay.Day(), 0, 0, 0, 0, time.Local)
	cycleEnd := cycleBegin.Add(24 * time.Hour)
	widerCycleBegin := time.Date(lastDay.Year(), lastDay.Month(), 1, 0, 0, 0, 0, time.Local)

	cycleStats := m.getAwardStatistics(cycleBegin, cycleEnd)
	lastCycleStats := m.getAwardStatistics(cycleBegin.Add(-24*time.Hour), cycleBegin)
	lastWiderCycleStats := m.getAwardStatistics(cycleBegin.AddDate(0, -1, 0), cycleEnd.AddDate(0, -1, 0))
	widerCycleStats := m.getAwardStatistics(widerCycleBegin, cycleEnd)

	giftIdList, err := m.getBigAwardGiftIds(context.Background())
	if err != nil {
		log.Errorf("DayReport fail to getBigAwardGiftIds. err:%v", err)
		return
	}

	hourBigRatio, hourBigCnt, hourConsumeCnt := m.getBigAwardRatio(cycleBegin, cycleEnd, giftIdList, cycleStats.TotalNum)
	dayBigRatio, dayBigCnt, dayConsumeCnt := m.getBigAwardRatio(widerCycleBegin, cycleEnd, giftIdList, widerCycleStats.TotalNum)

	strList := []string{
		lastDay.Format("2006年01月02日"),
		fmt.Sprintf("日利润%d元， 利润率为%0.4f%%，同比%+0.2f%%，环比%+0.2f%%",
			cycleStats.Profit, cycleStats.ProfitRatio*100, getPercentage(cycleStats.Profit, lastWiderCycleStats.Profit)-100, getPercentage(cycleStats.Profit, lastCycleStats.Profit)-100),
		fmt.Sprintf("日购买人数%d，同比%+0.2f%%，环比%+0.2f%%",
			cycleStats.ConsumeCnt, getPercentage(cycleStats.ConsumeCnt, lastWiderCycleStats.ConsumeCnt)-100, getPercentage(cycleStats.ConsumeCnt, lastCycleStats.ConsumeCnt)-100),
		fmt.Sprintf("日购买金额%d元，同比%+0.2f%%，环比%+0.2f%%",
			cycleStats.ConsumePrice, getPercentage(cycleStats.ConsumePrice, lastWiderCycleStats.ConsumePrice)-100, getPercentage(cycleStats.ConsumePrice, lastCycleStats.ConsumePrice)-100),
		fmt.Sprintf("日爆出奖励金额%d元，同比%+0.2f%%，环比%+0.2f%%",
			cycleStats.AwardPrice, getPercentage(cycleStats.AwardPrice, lastWiderCycleStats.AwardPrice)-100, getPercentage(cycleStats.AwardPrice, lastCycleStats.AwardPrice)-100),

		fmt.Sprintf("日开出大奖个数%d", hourBigCnt),
		fmt.Sprintf("日开出大奖人数%d", hourConsumeCnt),
		fmt.Sprintf("日大奖出奖率%0.2f%%", hourBigRatio*100),

		fmt.Sprintf("本月利润%d元， 利润率为%0.4f%%", widerCycleStats.Profit, widerCycleStats.ProfitRatio*100),
		fmt.Sprintf("本月购买人数%d", widerCycleStats.ConsumeCnt),
		fmt.Sprintf("本月购买金额%d", widerCycleStats.ConsumePrice),
		fmt.Sprintf("本月爆出奖励金额%d", widerCycleStats.AwardPrice),
		fmt.Sprintf("本月开出大奖个数%d", dayBigCnt),
		fmt.Sprintf("本月开出大奖人数%d", dayConsumeCnt),
		fmt.Sprintf("本月大奖出奖率%0.2f%%", dayBigRatio*100),
	}

	_ = m.sendFeiShuMsg("昨日数据", strList, "")
}

// 获取百分比
func getPercentage(divisor, base int64) float64 {
	if base == 0 {
		return 100
	}

	return float64(divisor) / float64(base) * 100
}

func (m *MagicSpiritMgr) getAwardStatistics(beginTime, endTime time.Time) *Stats {
	out := &Stats{}

	stats, err := m.mysql.GetAwardStatistics(beginTime, endTime)
	if err != nil {
		log.Errorf("GetHourReport fail to GetAwardStatistics. err:%v", err)
		return out
	}

	out.TotalNum = stats.TotalNum
	out.ConsumeCnt = stats.ConsumeCnt
	out.AwardPrice = stats.GiftTotalPrice / 100
	out.ConsumePrice = stats.MagicTotalPrice / 100
	out.Profit = (stats.MagicTotalPrice - stats.GiftTotalPrice) / 100
	if stats.MagicTotalPrice > 0 {
		out.ProfitRatio = float64(out.Profit) / float64(out.ConsumePrice)
	}

	return out
}

func (m *MagicSpiritMgr) getBigAwardRatio(beginTime, endTime time.Time, bigGiftIdList []uint32, total int64) (ratio float64, bigCnt uint64, consumeCnt uint64) {
	info, err := m.mysql.GetAwardCntSumById(bigGiftIdList, beginTime, endTime)
	if err != nil {
		log.Errorf("getBigAwardRatio fail to GetAwardCntSumById. err:%v", err)
		return
	}

	if total > 0 {
		ratio = float64(info.Cnt) / float64(total)
	}
	return ratio, info.Cnt, info.ConsumeCnt
}

// 获取会出超级大奖的魔法精灵
func (m *MagicSpiritMgr) getBigAwardGiftIds(ctx context.Context) ([]uint32, error) {
	list := make([]uint32, 0)

	magicList, err := m.GetMagicSpiritWithCache(ctx)
	if err != nil {
		log.Errorf("getBigAwardGiftIds fail to GetMagicSpiritWithCache. err:%v", err)
		return list, err
	}

	for _, magic := range magicList {
		magicId := magic.MagicSpiritId
		pondList, err := m.GetMagicSpiritPondWithCache(ctx, magicId, define.NormalPondType)
		if err != nil {
			log.Errorf("getBigAwardGiftIds fail to GetMagicSpiritPondWithCache. err:%v", err)
			continue
		}

		pondList2, err := m.GetMagicSpiritPondWithCache(ctx, magicId, define.SpecialPondType)
		if err != nil {
			log.Errorf("getBigAwardGiftIds fail to GetMagicSpiritPondWithCache. err:%v", err)
			continue
		}
		pondList = append(pondList, pondList2...)

		for _, pond := range pondList {
			if pond.PrizeLevel == uint32(pb.MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_LV4) {
				list = append(list, pond.PresentId)
				break
			}
		}
	}

	return list, nil
}

func (m *MagicSpiritMgr) CheckAndUpdateMagicSpirit() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	lockKey := "CheckAndUpdateMagicSpirit"
	// 上锁
	if ok, err := m.cache.Lock(ctx, lockKey, 5*time.Second); !ok || err != nil {
		if err != nil {
			log.Errorf("timer CheckAndUpdateMagicSpirit fail at Lock,err:%v", err)
		}
		return
	}
	defer func() {
		_ = m.cache.Unlock(ctx, lockKey)
	}()

	magicList, err := m.mysql.GetMagicSpiritTmpEffective(ctx, time.Now())
	if err != nil {
		log.Errorf("timer CheckAndUpdateMagicSpirit fail at GetMagicSpiritTmpEffective,err:%v", err)
		return
	}

	for _, v := range magicList {
		log.InfoWithCtx(ctx, "CheckAndUpdateMagicSpirit GetMagicSpiritTmpEffective,magic:%+v", v)
		err := m.UpdateMagicSpirit(ctx, fillMagicSpirit2PB(v), v.BeginTime)
		if err != nil {
			log.Errorf("timer CheckAndUpdateMagicSpirit fail at UpdateMagicSpirit,magicList:%+v, err:%v", magicList, err)
			return
		}
	}

}

func (m *MagicSpiritMgr) checkUpdateNormalPond() {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	lockKey := "checkUpdateNormalPond"
	// 上锁
	if ok, err := m.cache.Lock(ctx, lockKey, 10*time.Second); !ok || err != nil {
		if err != nil {
			log.Errorf("timer checkUpdateNormalPond fail at Lock,err:%v", err)
		}
		return
	}
	defer func() {
		_ = m.cache.Unlock(ctx, lockKey)
	}()

	pondList, e := m.mysql.GetEffectivePondTmp(ctx)
	if e != nil {
		log.Errorf("timer checkUpdateNormalPond fail at GetEffectivePondTmp,err:%v", e)
		return
	}

	if len(pondList) == 0 {
		return
	}

	ids := make([]uint32, 0, len(pondList))
	for _, v := range pondList {
		ids = append(ids, v.ItemId)
	}

	log.DebugWithCtx(ctx, "checkUpdateNormalPond GetEffectivePondTmp,ids:%+v", ids)
	err := m.mysql.Transaction(ctx, func(tx *gorm.DB) error {
		rowsEffected, err := m.mysql.UpdatePondTmpFlag(ctx, tx, ids)
		if err != nil {
			log.Errorf("timer checkUpdateNormalPond fail at UpdatePondTmpFlag,err:%v", err)
			return err
		}

		if rowsEffected != int64(len(pondList)) {
			log.Errorf("timer checkUpdateNormalPond fail, rowsEffected(%d) != int64(len(pondList)(%d)", rowsEffected, len(pondList))
			return errors.New("timer checkUpdateNormalPond fail")
		}

		_, err = m.mysql.AddMagicSpiritPond(ctx, tx, tmpPondStore2PondStore(pondList))
		if err != nil {
			log.Errorf("timer checkUpdateNormalPond fail at AddMagicSpiritPond,err:%v", err)
			return err
		}

		return nil
	})
	if err != nil {
		log.Errorf("timer checkUpdateNormalPond fail at Transaction,err:%v", err)
		return
	}

	err = m.cache.DelMagicSpiritCache(ctx)
	if err != nil {
		log.Errorf("timer checkUpdateNormalPond fail at DelMagicSpiritCache,err:%v", err)
		return
	}

	err = m.cache.UpdateMagicSpiritVersion(ctx)
	if err != nil {
		log.Errorf("timer checkUpdateNormalPond fail at UpdateMagicSpiritVersion,err:%v", err)
		return
	}

	return
}

func (m *MagicSpiritMgr) CheckMagicPondUpdate() {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	lockKey := "CheckMagicPondUpdate"
	// 上锁
	if ok, err := m.cache.Lock(ctx, lockKey, 3*time.Second); !ok || err != nil {
		if err != nil {
			log.Errorf("timer checkUpdateNormalPond fail at Lock,err:%v", err)
		}
		return
	}
	defer func() {
		_ = m.cache.Unlock(ctx, lockKey)
	}()

	versionDB, err := m.mysql.GetNormalPondMaxUpdateTime(ctx)
	if err != nil {
		log.Errorf("timer CheckMagicPondUpdate fail at GetMagicPondUpdateVersion,err:%v", err)
		return
	}

	versionCache, err := m.cache.GetMagicPondVersion(ctx, false)
	if err != nil {
		log.Errorf("timer CheckMagicPondUpdate fail at GetMagicPondVersion,err:%v", err)
		return
	}

	if versionDB > versionCache {
		log.InfoWithCtx(ctx, "CheckMagicPondUpdate versionDB:%d,versionCache:%d", versionDB, versionCache)
		err = m.cache.DelMagicSpiritPond(ctx)
		if err != nil {
			log.Errorf("timer CheckMagicPondUpdate fail at DelMagicSpiritPond,err:%v", err)
			return
		}

		err = m.cache.SetMagicPondVersion(ctx, versionDB, false)
		if err != nil {
			log.Errorf("timer CheckMagicPondUpdate fail at SetMagicPondVersion,err:%v,versionDB:%d", err, versionDB)
			return
		}

		err = m.cache.DelMagicSpiritCache(ctx)
		if err != nil {
			log.Errorf("timer CheckMagicPondUpdate fail at DelMagicSpiritCache,err:%v,versionDB:%d", err, versionDB)
			return
		}

		err = m.cache.UpdateMagicSpiritVersion(ctx)
		if err != nil {
			log.Errorf("timer checkUpdateNormalPond fail at UpdateMagicSpiritVersion,err:%v", err)
			return
		}
	}

	// 特殊奖池缓存检查
	m.checkSpecialMagicPondUpdate(ctx)
}

func (m *MagicSpiritMgr) checkSpecialMagicPondUpdate(ctx context.Context) {

	versionDB, err := m.mysql.GetSpecialPondMaxUpdateTime(ctx)
	if err != nil {
		log.Errorf("timer CheckSpecialMagicPondUpdate fail at GetSpecialPondMaxUpdateTime,err:%v", err)
		return
	}

	versionCache, err := m.cache.GetMagicPondVersion(ctx, true)
	if err != nil {
		log.Errorf("timer CheckSpecialMagicPondUpdate fail at GetMagicPondVersion,err:%v", err)
		return
	}

	if versionDB > versionCache {
		log.InfoWithCtx(ctx, "CheckSpecialMagicPondUpdate versionDB:%d,versionCache:%d", versionDB, versionCache)
		err = m.cache.DelMagicSpiritPond(ctx)
		if err != nil {
			log.Errorf("timer CheckMagicPondUpdate fail at DelMagicSpiritPond,err:%v", err)
			return
		}

		err = m.cache.SetMagicPondVersion(ctx, versionDB, true)
		if err != nil {
			log.Errorf("timer CheckMagicPondUpdate fail at SetMagicPondVersion, special. err:%v,versionDB:%d", err, versionDB)
			return
		}

		err = m.cache.DelMagicSpiritCache(ctx)
		if err != nil {
			log.Errorf("timer CheckMagicPondUpdate fail at DelMagicSpiritCache,err:%v,versionDB:%d", err, versionDB)
			return
		}

		err = m.cache.UpdateMagicSpiritVersion(ctx)
		if err != nil {
			log.Errorf("timer checkUpdateNormalPond fail at UpdateMagicSpiritVersion,err:%v", err)
			return
		}
	}
}

func fillMagicSpirit2PB(info *mysql.MagicSpiritTemporary) *pb.MagicSpirit {
	if info == nil {
		return &pb.MagicSpirit{}
	}
	var channelTypeList []uint32
	if info.ChannelTypeList != "" {
		splitList := strings.Split(info.ChannelTypeList, ",")
		channelTypeList = make([]uint32, 0, len(splitList))
		for _, v := range splitList {
			num, err := strconv.ParseUint(v, 10, 32)
			if err != nil {
				log.Errorf("fillMagicSpirit2PB fail at ParseUint,err:%v", err)
				continue
			}
			channelTypeList = append(channelTypeList, uint32(num))
		}
	}

	magicInfo := &pb.MagicSpirit{
		MagicSpiritId:    info.MagicSpiritId,
		Name:             info.Name,
		IconUrl:          info.IconUrl,
		Price:            info.Price,
		Rank:             info.Ranking,
		EffectBegin:      info.EffectBegin,
		EffectEnd:        info.EffectEnd,
		DescribeImageUrl: info.DescribeImageUrl,
		Describe:         info.GiftDescribe,
		JuniorLighting:   info.JuniorLighting,
		MiddleLighting:   info.MiddleLighting,
		VfxResource:      info.VfxResource,
		VfxResourceMd5:   info.VfxResourceMd5,
		UpdateTime:       uint32(info.UpdateTime.Unix()),
		RankFloat:        info.RankFloat,
		DescActivityUrl:  info.ActivityJumpUrl,
		ChannelTypeList:  channelTypeList,
		ShowEffectEnd:    info.ShowEffectEnd,
		ActivityCfg: &pb.MagicSpiritActivityCfg{
			ActivityName:       info.ActName,
			BeginTime:          info.ActBeginTime.Unix(),
			EndTime:            info.ActEndTime.Unix(),
			ImageUrl:           info.ActImageUrl,
			JumpUrlTt:          info.ActivityJumpUrl,
			JumpUrlHcAndroid:   info.ActJumpUrlHcAndroid,
			JumpUrlHcIos:       info.ActJumpUrlHcIos,
			JumpUrlMikeAndroid: info.ActJumpUrlMikeAndroid,
			JumpUrlMikeIos:     info.ActJumpUrlMikeIos,
		},
	}

	magicInfo.MagicEffectTimeList = conf.Str2TimeRangeListPb(context.Background(), info.MagicTimeList)
	if info.MagicTimeList == "" {
		magicInfo.MagicEffectTimeList = []*pb.TimeRange{
			{
				StartTime: int64(info.EffectBegin),
				EndTime:   int64(info.EffectEnd),
			},
		}
	}

	magicInfo.SpecialPondConf = &pb.SpecialPondConf{
		DescribeImageUrl: info.SpDescImg,
		DescribeJumpUrl:  info.SpDescJumpUrl,
		EffectTimeList:   conf.Str2TimeRangeListPb(context.Background(), info.SpPondTimeList),
	}

	return magicInfo
}

func (m *MagicSpiritMgr) TimerHandle(d time.Duration, handle func()) {
	m.wg.Add(1)
	delay := time.NewTicker(d)
	for {
		select {
		case <-m.shutDown:
			m.wg.Done()
			return
		case <-delay.C:
			m.handleWithPanicCatch(handle)
		}
	}
}

func (m *MagicSpiritMgr) handleWithPanicCatch(handle func()) {
	defer func() {
		if err := recover(); err != nil {
			var stack string
			var buf bytes.Buffer
			buf.Write(debug.Stack())
			stack = buf.String()

			nowTime := time.Now().Format("2006-01-02 15:04:05")
			fmt.Printf("%s %v %s %s", nowTime, err, "\n", stack)

			funcName := runtime.FuncForPC(reflect.ValueOf(handle).Pointer()).Name()
			log.Errorf("handleWithPanicCatch panic func:%s, err:%v", funcName, err)
		}
	}()

	handle()
}

func (m *MagicSpiritMgr) sendFeiShuMsg(title string, textLines []string, atUser string) error {
	lineList := make([][]*feishu.LineMem, 0, len(textLines))
	for _, text := range textLines {
		line := []*feishu.LineMem{
			{Tag: "text", Text: text},
		}
		lineList = append(lineList, line)
	}

	if atUser != "" {
		line := []*feishu.LineMem{
			{Tag: "at", UserId: atUser},
		}
		lineList = append(lineList, line)
	}

	return feishu.SendFeiShuRichMsg(m.bc.GetFeiShuRobotUrl(), title, lineList)
}
