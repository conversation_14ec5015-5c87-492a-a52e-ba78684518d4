package main

import (
    "context"
    "errors"
    "fmt"
    "github.com/golang/protobuf/proto"
    "github.com/tealeg/xlsx"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    backpack_base "golang.52tt.com/protocol/services/backpack-base"
    userPresent "golang.52tt.com/protocol/services/userpresent-go"
    "golang.52tt.com/services/tt-rev/common/feishu"
    "sort"
    "strings"
    "time"
)

type Gift struct {
	Id    uint32
	Name  string
	Count uint32
	Worth uint32
	Type  uint32
}

type Statistics struct {
	dao *Dao

	backpack backpack_base.BackpackBaseServiceClient
	present  userPresent.UserPresentGOClient
}

var statistics *Statistics

func (s *Statistics) GenExelFile(path, feiShuUrl string, begin, end time.Time) (string, error) {
	start := time.Now()

	ctx := context.Background()

	// 获取数据
	var date2Winnings map[string][]*packData
	//var darkGiftBonus []*giftData
	var recharges []rechargeData

	if t, err := s.queryWinningData(ctx, begin, end); err != nil {
		log.Errorf("queryWinningData fail, err: %v\n", err)
		return "", err
	} else {
		date2Winnings = t.(map[string][]*packData)
	}

	/*if t, err := s.queryDarkGiftBonusData(ctx, begin, end); err != nil {
		log.Errorf("queryWinningData fail, err: %v\n", err)
		return "", err
	} else {
		darkGiftBonus = t.([]*giftData)
	}*/

	if t, err := s.queryRechargeData(ctx, begin, end); err != nil {
		log.Errorf("queryRechargeData fail, err: %v\n", err)
		return "", err
	} else {
		recharges = t.([]rechargeData)
	}

	expireInfo, err := s.queryExpirePropData(ctx, begin, end)
	if err != nil {
		log.Errorf("queryPropRemainData fail, err: %v\n", err)
		return "", err
	}

	beginPropRemain, endPropRemain, err := s.queryPropRemainData(ctx, begin, end)
	if err != nil {
		log.Errorf("queryPropRemainData fail, err: %v\n", err)
		return "", err
	}

	// 创建报表
	file := xlsx.NewFile()

	checkPass, err := s.genTotalSheet(file, date2Winnings, recharges, expireInfo, beginPropRemain, endPropRemain)
	if err != nil {
		log.Errorf("genTotalSheet fail, err: %v\n", err)
		return "", err
	}

	err = s.genMonthlyWinningSheet(file, date2Winnings)
	if err != nil {
		log.Errorf("genTotalSheet fail, err: %v\n", err)
		return "", err
	}

	err = s.genDailyWinningSheet(file, date2Winnings)
	if err != nil {
		log.Errorf("genTotalSheet fail, err: %v\n", err)
		return "", err
	}

	fileName := fmt.Sprintf("%s财务数据_%s.xlsx", actName, time.Now().Format("20060102"))
	filePath := fmt.Sprintf("%s/%s", path, fileName)

	err = file.Save(filePath)
	if err != nil {
		log.Errorf("file save fail, err: %v\n", err)
		return "", err
	}

	log.Infof("all gen complete, takes: %v\n", time.Since(start))

	if !checkPass {
		log.Errorf("genTotalSheet checkFail.")
		FeiShuWarn(feiShuUrl)
		//return "", errors.New("checkFail")
	}

	return filePath, nil
}

func FeiShuWarn(url string) {
	lineList := make([][]*feishu.LineMem, 0)
	line := []*feishu.LineMem{
		{Tag: "text", Text: "check 项异常"},
	}
	lineList = append(lineList, line)

	line = []*feishu.LineMem{
		{Tag: "at", UserId: "6788876840648851726"},
	}
	lineList = append(lineList, line)

	feishu.SendFeiShuRichMsg(url, "转转财务数据对账异常", lineList)
}

func (s *Statistics) genTotalSheet(file *xlsx.File, date2Winnings map[string][]*packData, recharges []rechargeData, expireInfo *expireData, beginPropRemain, endPropRemain int64) (bool, error) {
	// 生成对账报表
	name := actName + "月汇总"
	sheet, err := file.AddSheet(name)
	if err != nil {
		log.Errorf("AddSheet fail, name: %s, err: %v\n", name, err)
		return false, err
	}

	head := sheet.AddRow()

	col, _ := head.AddCell(), head.AddCell()
	col.HMerge = 1
	col.Value = time.Now().AddDate(0, -1, 0).Format("2006-01") + " / 单位：T豆"

	row := sheet.AddRow()
	row.AddCell().SetString("抽奖道具名称")
	row.AddCell().SetString(propName)

	row = sheet.AddRow()
	row.AddCell().SetString("单价")
	row.AddCell().SetInt64(propPrice)

	row = sheet.AddRow()
	row.AddCell().SetString("期初价值")
	row.AddCell().SetInt64(beginPropRemain * propPrice)

	rechargeCostTotalCnt := int64(0)
	for _, recharge := range recharges {
        rechargeCostTotalCnt += recharge.Num
	}
	row = sheet.AddRow()
	row.AddCell().SetString("销售价值")
	row.AddCell().SetInt64(rechargeCostTotalCnt * propPrice)

	winningCostTotalCnt := int64(0)
	winningWorthTotal := int64(0)
	for _, winnings := range date2Winnings {
		for _, winning := range winnings {
            winningCostTotalCnt += winning.CostPropNum
			winningWorthTotal += winning.TbeanGiftPrice * winning.PackCount
		}
	}

	row = sheet.AddRow()
	row.AddCell().SetString("消耗价值")
	row.AddCell().SetInt64(winningCostTotalCnt*propPrice)

	row = sheet.AddRow()
	row.AddCell().SetString("过期价值")
	row.AddCell().SetInt64(expireInfo.PropNum * propPrice)

	row = sheet.AddRow()
	row.AddCell().SetString("返奖价值")
	row.AddCell().SetInt64(winningWorthTotal)

	row = sheet.AddRow()
	row.AddCell().SetString("期末价值")
	row.AddCell().SetInt64(endPropRemain * propPrice)

    log.Infof("beginPropRemain: %d, rechargeCostTotalCnt: %d, winningCostTotalCnt: %d, expireInfo.PropNum: %d, endPropRemain: %d",
        beginPropRemain, rechargeCostTotalCnt, winningCostTotalCnt, expireInfo.PropNum, endPropRemain)
	check := beginPropRemain + rechargeCostTotalCnt - winningCostTotalCnt - expireInfo.PropNum - endPropRemain
	//if check != 0 {
	//	return false, nil
	//}

	row = sheet.AddRow()
	row.AddCell().SetString("check")
	row.AddCell().SetInt64(check)

    winningCostTotal := winningCostTotalCnt*propPrice
	row = sheet.AddRow()
	row.AddCell().SetString("抽奖总利润")
	row.AddCell().SetInt64(winningCostTotal - winningWorthTotal)

	row = sheet.AddRow()
	row.AddCell().SetString("利润率")
	row.AddCell().SetString(fmt.Sprintf("%.4f%%", float64(winningCostTotal-winningWorthTotal)*100/float64(winningCostTotal)))

	return true, nil
}

func (s *Statistics) genDailyWinningSheet(file *xlsx.File, date2Winnings map[string][]*packData) error {
	// 生成中奖数据报表
	name := actName + "日抽奖数据"
	sheet, err := file.AddSheet(name)
	if err != nil {
		log.Errorf("AddSheet fail, name: %s, err: %v\n", name, err)
		return err
	}

	// 添加标题
	var colNames = []string{"日期", "抽奖模式", "中奖来源", "包裹id", "包裹名称", "包裹总数量", "发放T豆价值物品总数量", "发放T豆价值物品总价值（T豆）",
		"发放装扮碎片总数量", "抽奖总次数", "消耗数量", "消耗价值"}

	row := sheet.AddRow()
	for _, colName := range colNames {
		row.AddCell().SetValue(colName)
	}

	for date, winnings := range date2Winnings {
		for _, winning := range winnings {

			mode := "普通"
			if 0 != winning.PlayMode {
				mode = "高级"
			}

			bingoTy := "概率中奖"
			if 0 != winning.BingoType && 1 != winning.BingoType {
				bingoTy = "N值中奖"
			}

			row = sheet.AddRow()
			row.AddCell().SetValue(date)
			row.AddCell().SetValue(mode)
			row.AddCell().SetValue(bingoTy)
			row.AddCell().SetInt64(int64(winning.PackId))
			row.AddCell().SetString(winning.PackName)
			row.AddCell().SetInt64(winning.PackCount)

			row.AddCell().SetInt64(winning.TbeanGiftCount * winning.PackCount)
			row.AddCell().SetInt64(winning.TbeanGiftPrice * winning.PackCount)
			row.AddCell().SetInt64(winning.DressFragment * winning.PackCount)

			row.AddCell().SetInt64(winning.PackCount)
			row.AddCell().SetInt64(winning.CostPropNum)
			row.AddCell().SetInt64(winning.CostPropNum * propPrice)
		}
	}
	return nil
}

func (s *Statistics) genMonthlyWinningSheet(file *xlsx.File, date2Winnings map[string][]*packData) error {
	mode2BingoTy2ID2Gift := make(map[uint32]map[uint32]map[uint32]*packData)
	for mode := uint32(0); mode < 2; mode++ {
		mode2BingoTy2ID2Gift[mode] = make(map[uint32]map[uint32]*packData)
		for bingoTy := uint32(0); bingoTy < 4; bingoTy++ {
			mode2BingoTy2ID2Gift[mode][bingoTy] = make(map[uint32]*packData)
		}
	}

	for _, winnings := range date2Winnings {
		for _, winning := range winnings {
			if info, ok := mode2BingoTy2ID2Gift[winning.PlayMode][winning.BingoType][uint32(winning.PackId)]; !ok {
				mode2BingoTy2ID2Gift[winning.PlayMode][winning.BingoType][uint32(winning.PackId)] = &packData{
					PackId:         winning.PackId,
					PackName:       winning.PackName,
					CostPropNum:    winning.CostPropNum,
					PlayMode:       winning.PlayMode,
					BingoType:      winning.BingoType,
					PackCount:      winning.PackCount,
					DressFragment:  winning.DressFragment,
					TbeanGiftCount: winning.TbeanGiftCount,
					TbeanGiftPrice: winning.TbeanGiftPrice,
				}
			} else {
				info.CostPropNum += winning.CostPropNum
				info.PackCount += winning.PackCount
			}
		}
	}

	winnings := make([]*packData, 0)
	for _, v := range mode2BingoTy2ID2Gift {
		for _, v2 := range v {
			for _, v3 := range v2 {
				winnings = append(winnings, v3)
			}
		}
	}

	sort.Slice(winnings, func(i, j int) bool {
		return winnings[i].PackId > winnings[j].PackId
	})

	// 生成中奖数据报表
	name := actName + "月抽奖数据"
	sheet, err := file.AddSheet(name)
	if err != nil {
		log.Errorf("AddSheet fail, name: %s, err: %v\n", name, err)
		return err
	}

	// 添加标题
	var colNames = []string{"月份", "抽奖模式", "中奖来源", "包裹id", "包裹名称", "包裹总数量", "发放T豆价值物品总数量", "发放T豆价值物品总价值（T豆）",
		"发放装扮碎片总数量", "抽奖总次数", "消耗数量", "消耗价值"}

	row := sheet.AddRow()
	for _, colName := range colNames {
		row.AddCell().SetValue(colName)
	}

	// 添加数据
	date := time.Now().AddDate(0, -1, 0).Format("2006-01")
	for _, winning := range winnings {

		mode := "普通"
		if 0 != winning.PlayMode {
			mode = "高级"
		}

		bingoTy := "概率中奖"
		if 0 != winning.BingoType && 1 != winning.BingoType {
			bingoTy = "N值中奖"
		}

		row = sheet.AddRow()
		row.AddCell().SetValue(date)
		row.AddCell().SetValue(mode)
		row.AddCell().SetValue(bingoTy)
		row.AddCell().SetInt64(int64(winning.PackId))
		row.AddCell().SetString(winning.PackName)
		row.AddCell().SetInt64(winning.PackCount)

		row.AddCell().SetInt64(winning.TbeanGiftCount * winning.PackCount)
		row.AddCell().SetInt64(winning.TbeanGiftPrice * winning.PackCount)
		row.AddCell().SetInt64(winning.DressFragment * winning.PackCount)

		row.AddCell().SetInt64(winning.PackCount)
		row.AddCell().SetInt64(winning.CostPropNum)
		row.AddCell().SetInt64(winning.CostPropNum * propPrice)
	}
	return nil
}

type packData struct {
	PackId         uint32 `json:"pack_id"`
	PackName       string `json:"pack_name"`
	CostPropNum    int64  `json:"cost_prop_num"`
	PlayMode       uint32 `json:"play_mode"`
	BingoType      uint32 `json:"bingo_type"`
	PackCount      int64  `json:"pack_count"`       // 包裹总数量
	DressFragment  int64  `json:"dress_fragment"`   // 装扮碎片数量
	TbeanGiftCount int64  `json:"tbean_gift_count"` // t豆价值礼物数量
	TbeanGiftPrice int64  `json:"tbean_gift_price"` // t豆价值礼物价值
}

func (s *Statistics) queryWinningData(ctx context.Context, begin, end time.Time) (interface{}, error) {

	date2Winnings, err := s.dao.GetWinningDataV2(ctx, begin, end.Add(time.Second))
	if err != nil {
		return nil, err
	}

	// 加载礼物配置
	gifts := make(map[uint32][]*Gift)
	for _, winnings := range date2Winnings {
		for _, winning := range winnings {
			packId := winning.Pack
			if _, ok := gifts[packId]; !ok {
				giftList, err := s.loadGift(ctx, uint32(packId))
				if err != nil {
					return nil, err
				}
				gifts[packId] = giftList
			}
		}
	}

	date2Mode2BingoTy2Gid2Pack := make(map[string]map[uint32]map[uint32]map[uint32]*packData)
	for date := range date2Winnings {
		date2Mode2BingoTy2Gid2Pack[date] = make(map[uint32]map[uint32]map[uint32]*packData)
		for mode := uint32(0); mode < 2; mode++ {
			date2Mode2BingoTy2Gid2Pack[date][mode] = make(map[uint32]map[uint32]*packData)
			for bingoTy := uint32(0); bingoTy < 4; bingoTy++ {
				date2Mode2BingoTy2Gid2Pack[date][mode][bingoTy] = make(map[uint32]*packData)
			}
		}
	}

	for date, winning := range date2Winnings {
		for _, winning := range winning {
			mode := winning.Mode
			packId := winning.Pack
			bingoTy := winning.BingoType

			if data, ok := date2Mode2BingoTy2Gid2Pack[date][mode][bingoTy][winning.Pack]; !ok {
				giftList := gifts[packId]
				nameList := make([]string, 0)

				tbeanGiftPrice, tbeanGiftNum, dressFragment := int64(0), int64(0), int64(0)
				for _, gift := range giftList {
					if gift.Worth > 0 {
						tbeanGiftPrice += int64(gift.Count) * int64(gift.Worth)
						tbeanGiftNum += int64(gift.Count)
					}

					if gift.Type == uint32(backpack_base.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) && gift.Id == 1 {
						// 装扮碎片
						dressFragment += int64(gift.Count)
					}

					nameList = append(nameList, fmt.Sprintf("%s*%d", gift.Name, gift.Count))
				}

				t := &packData{
					PackId:         winning.Pack,
					PackName:       strings.Join(nameList, ","),
					PlayMode:       mode,
					BingoType:      bingoTy,
					TbeanGiftCount: tbeanGiftNum,
					TbeanGiftPrice: tbeanGiftPrice,
					DressFragment:  dressFragment,
					PackCount:      winning.Num,
					CostPropNum:    winning.CostPropNum,
				}

				date2Mode2BingoTy2Gid2Pack[date][mode][bingoTy][winning.Pack] = t

			} else {
				//data.GiftCount += winning.Num * int64(gift.Count)
				//data.GiftWorth += winning.Num * int64(gift.Count) * int64(gift.Worth)
				data.PackCount += winning.Num
				data.CostPropNum += winning.CostPropNum
			}

		}
	}

	date2List := make(map[string][]*packData, 0)
	for date, mode2BingoTy2Gid2Pack := range date2Mode2BingoTy2Gid2Pack {
		date2List[date] = make([]*packData, 0)
		for _, bingoTy2Gid2Pack := range mode2BingoTy2Gid2Pack {
			for _, gid2Pack := range bingoTy2Gid2Pack {
				for _, pack := range gid2Pack {
					date2List[date] = append(date2List[date], pack)
				}
			}
		}
	}

	return date2List, nil
}

//
//// 黑暗礼物-保底奖励
//func (s *Statistics) queryDarkGiftBonusData(ctx context.Context, begin, end time.Time) (interface{}, error) {
//
//	winnings, err := s.dao.GetDarkGiftBonusSummary(ctx, begin, end)
//	if err != nil {
//		return nil, err
//	}
//
//	var sum int64
//	result := make([]*giftData, 0)
//	for _, winning := range winnings {
//		t := &giftData{
//			GiftId:    int64(winning.GiftId),
//			GiftName:  winning.GiftName,
//			GiftCount: winning.TotalCnt,
//			GiftWorth: winning.TotalPrice,
//			//PlayCount: 0, // 保底奖励不消耗魔力球次数
//			PlayMode:  1,
//		}
//		result = append(result, t)
//		sum += t.GiftWorth
//	}
//
//	log.Infof("queryDarkGiftBonusData get total: %d\n", sum)
//	return result, nil
//}

func (s *Statistics) queryRechargeData(ctx context.Context, begin, end time.Time) (interface{}, error) {
	recharges, err := s.dao.GetRechargeData(ctx, begin, end)
	if err == nil {
		var sum int64
		for _, recharge := range recharges {
			sum += recharge.Num
		}
		log.Infof("queryRechargeData get total: %d\n", sum)
	}
	return recharges, err
}

func (s *Statistics) queryRemainData(ctx context.Context, begin, end time.Time) (interface{}, error) {
	remains, err := s.dao.GetRemainData(ctx, end.Add(time.Second))
	if err == nil {
		var sum int64
		for _, remain := range remains {
			sum += remain.Num
		}
		log.Infof("queryRemainData get total: %d\n", sum)
	}
	return remains, err
}

func (s *Statistics) queryPropRemainData(ctx context.Context, begin, end time.Time) (beginNum, endNum int64, err error) {
	beginNum, err = s.dao.GetPropRemainTotal(ctx, begin)
	if err != nil {
		log.Errorf("GetPropRemainTotal fail, begin: %v, err: %v\n", begin, err)
		return
	}

	endNum, err = s.dao.GetPropRemainTotal(ctx, end.Add(time.Second))
	if err != nil {
		log.Errorf("GetPropRemainTotal fail, end: %v, err: %v\n", end, err)
		return
	}
	return
}

func (s *Statistics) queryExpirePropData(ctx context.Context, begin, end time.Time) (info *expireData, err error) {
	info, err = s.dao.GetExpirePropTotal(ctx, begin, end)
	if err != nil {
		log.Errorf("queryExpirePropData fail, begin: %v, err: %v\n", begin, err)
		return
	}

	return
}

func (s *Statistics) loadGift(ctx context.Context, packId uint32) (gifts []*Gift, err error) {
	ctxN, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	cfg, err := s.backpack.GetPackageItemCfg(ctxN, &backpack_base.GetPackageItemCfgReq{
		BgId: packId,
	})
	if err != nil {
		log.Errorf("GetPackageItemCfg fail, packId:%d", packId)
		return nil, err
	}

	gifts = make([]*Gift, 0, len(cfg.GetItemCfgList()))
	for _, pkgItemCfg := range cfg.GetItemCfgList() {
		if pkgItemCfg.IsDel {
			continue
		}

		var gift *Gift
		switch backpack_base.PackageItemType(pkgItemCfg.ItemType) {
		case backpack_base.PackageItemType_BACKPACK_PRESENT:
			presentCfg, err := s.present.GetPresentConfigById(ctx, &userPresent.GetPresentConfigByIdReq{
                ItemId: pkgItemCfg.SourceId,
            })
			if err != nil {
				return nil, err
			}
            itemCfg := presentCfg.GetItemConfig().GetBaseConfig()
			gift = &Gift{
				Id:    itemCfg.ItemId,
				Name:  itemCfg.Name,
				Count: pkgItemCfg.ItemCount,
				Worth: itemCfg.Price,
				Type:  pkgItemCfg.ItemType,
			}
		case backpack_base.PackageItemType_BACKPACK_LOTTERY_FRAGMENT:
			log.Infof("try to GetItemCfg, source_id:%d", pkgItemCfg.SourceId)
			itemCfgs, err := s.backpack.GetItemCfg(ctxN, &backpack_base.GetItemCfgReq{
				ItemType:         uint32(backpack_base.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
				ItemSourceIdList: []uint32{pkgItemCfg.SourceId},
				GetAll:           false,
			})
			if err != nil {
				log.Errorf("try to GetItemCfg fail, source_id:%d", pkgItemCfg.SourceId)
				return nil, err
			}
			if nil == itemCfgs.ItemCfgList || 1 != len(itemCfgs.ItemCfgList) {
				return nil, errors.New("invalid fragment cfg")
			}

			fragment := backpack_base.LotteryFragmentCfg{}
            err = proto.Unmarshal(itemCfgs.ItemCfgList[0], &fragment)
			if err != nil {
				return nil, err
			}
			gift = &Gift{
				Id:    fragment.FragmentId,
				Name:  fragment.FragmentName,
				Count: pkgItemCfg.ItemCount,
				Worth: fragment.FragmentPrice,
				Type:  pkgItemCfg.ItemType,
			}
		default:
			return nil, errors.New("invalid package item type")
		}

		gifts = append(gifts, gift)
	}
	return gifts, nil
}
