package producer

import(
	context "context"
	userpresent_go "golang.52tt.com/protocol/services/userpresent-go"
	kafkapresent "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
)

type IKafkaProduceMgr interface {
	PublishPresentEvent(ctx context.Context, event *kafkapresent.PresentEvent) error
	PublishPresentEventV2(ctx context.Context, event *kafkapresent.PresentEvent) error
}


type IAiKafkaProduceMgr interface {
	PublishAiPresentEvent(ctx context.Context, event *userpresent_go.AiPresentEvent) error
}

