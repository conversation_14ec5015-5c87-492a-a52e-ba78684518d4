package manager

import (
	"context"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/timer"
	"golang.52tt.com/protocol/common/status"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	pb "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/userpresent-go/internal/cache"
	config "golang.52tt.com/services/userpresent-go/internal/config/ttconfig/userpresent_go"
	"golang.52tt.com/services/userpresent-go/internal/manager/feishu"
	"golang.52tt.com/services/userpresent-go/internal/producer"
	"golang.52tt.com/services/userpresent-go/internal/rpc"
	"golang.52tt.com/services/userpresent-go/internal/store"
	"time"
)

const UserDetailListLimit = 100

type UserPresentGoMgr struct {
	store           store.IStore
	sceneStore      store.ISceneStore
	detailStore     store.IDetailStore
	cache           cache.ICache
	sceneCache      cache.ISceneCache
	presentCache    cache.IPresentMemCache
	config          config.UserPresentGoConf
	kafkaProducer   producer.IKafkaProduceMgr
	aiKafkaProducer *producer.AiKafkaProduceMgr
	feishuReporter  *feishu.FeishuReporterV2

	mTimer *timer.Timer
}

func NewUserPresentGoMgr(ctx context.Context, pwStore store.IStore, sceneStore store.ISceneStore, detailStore store.IDetailStore, pwCache cache.ICache,
	sceneCache cache.ISceneCache, presentCache cache.IPresentMemCache, presentConfig config.UserPresentGoConf, kafkaProducer producer.IKafkaProduceMgr, aiKafkaProducer *producer.AiKafkaProduceMgr) (*UserPresentGoMgr, error) {
	mgr := &UserPresentGoMgr{
		store:           pwStore,
		cache:           pwCache,
		sceneStore:      sceneStore,
		sceneCache:      sceneCache,
		detailStore:     detailStore,
		presentCache:    presentCache,
		config:          presentConfig,
		kafkaProducer:   kafkaProducer,
		aiKafkaProducer: aiKafkaProducer,
	}

	log.InfoWithCtx(ctx, "NewUserPresentGoMgr start mgr %+v", mgr)

	// 百灵数据统计 初始化
	bylinkCollect, err := bylink.NewKfkCollector()
	if err != nil {
		log.ErrorWithCtx(ctx, "bylink.NewKfkCollector() failed err:%v", err)
		return nil, err
	}
	bylink.InitGlobalCollector(bylinkCollect)

	_ = rpc.Setup()

	// 建一下表

	err = mgr.store.CreatePresentBaseConfigTable(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewUserPresentGoMgr CreatePresentBaseConfigTable failed,err:%v", err)
	}

	err = mgr.store.CreatePresentEnterConfigTable(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewUserPresentGoMgr CreatePresentEnterConfigTable failed,err:%v", err)
	}

	err = mgr.store.CreatePresentEffectConfigTable(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewUserPresentGoMgr CreatePresentEffectConfigTable failed,err:%v", err)
	}

	err = mgr.store.CreatePresentConfigLogTable(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewUserPresentGoMgr CreatePresentEffectConfigTable failed,err:%v", err)
	}

	err = mgr.store.CreateChanceItemSourceTable(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewUserPresentGoMgr CreateChanceItemSourceTable failed,err:%v", err)
	}

	err = mgr.store.CreateMarkTable(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewUserPresentGoMgr CreateMarkTable failed,err:%v", err)
	}

	err = mgr.store.CreatePresentMarkIconTable(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewUserPresentGoMgr CreatePresentMarkIconTable failed,err:%v", err)
	}

	err = mgr.store.CreatePresentActivityTable(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewUserPresentGoMgr CreatePresentActivityTable failed,err:%v", err)
	}

	err = mgr.store.CreatePresentActivityItemTable(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewUserPresentGoMgr CreatePresentActivityItemTable failed,err:%v", err)
	}

	mgr.store.CreateAiMonthlyPresentHistoryTable(ctx, time.Now().Year(), int(time.Now().Month()))

	for i := 0; i < 12; i++ {
		for j := 0; j < 100; j++ {
			err = mgr.detailStore.CreateAiPresentSendDetailTable(ctx, uint32(i), uint32(j))
			if err != nil {
				log.ErrorWithCtx(ctx, "NewUserPresentGoMgr CreatePresentActivityItemTable failed,err:%v", err)
			}
		}
	}

	for i := 0; i < 100; i++ {
		err = mgr.store.CreateAiPresentSummaryTable(ctx, uint32(i))
		if err != nil {
			log.ErrorWithCtx(ctx, "NewUserPresentGoMgr CreatePresentActivityItemTable failed,err:%v", err)
		}
	}

	log.InfoWithCtx(ctx, "NewUserPresentGoMgr start mTimer %+v", mgr)
	mgr.mTimer, err = timer.NewTimerD(ctx, "UserPresentGoMgr", timer.WithV8RedisCmdable(pwCache.GetRedisClient()))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewUserPresentGoMgr NewTimerD failed,err:%v", err)
	}
	err = mgr.mTimer.AddTask("@every 1m", "genNamingPresentInfo", timer.BuildFromLambda(func(ctx context.Context) {
		mgr.genNamingPresentInfo()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init genNamingPresentInfo fail, err: %v", err)
		return nil, err
	}

	err = mgr.mTimer.AddTask("@every 1h", "CreateTableNextMonth", timer.BuildFromLambda(func(ctx context.Context) {
		mgr.CreatePresentMonthlyTable(ctx)
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init CreatePresentMonthlyTable fail, err: %v", err)
		return nil, err
	}

	mgr.mTimer.Start()

	// 订阅各种配置更新
	pwSub := pwCache.GetPresentConfigUpdateSub(ctx)
	go mgr.SubConfigUpdate(pwSub)

	mgr.feishuReporter = feishu.NewFeiShuReporterV2(mgr.config.GetFeishuUrl(), mgr.config.GetFeishuEnv())

	return mgr, nil
}

func (m *UserPresentGoMgr) ShutDown() {
	_ = m.store.Close()
	_ = m.cache.Close()
	m.presentCache.Stop()
	m.mTimer.Stop()
}

func (m *UserPresentGoMgr) SubConfigUpdate(pwSub *redis.PubSub) {
	for {
		select {
		case msg := <-pwSub.Channel():
			log.InfoWithCtx(context.Background(), "SubConfigUpdate msg:%v", msg)
			if msg.String() == string(cache.PresentConfigUpdate) {
				m.presentCache.UpdateConfig()
			} else if msg.String() == string(cache.FlowConfigUpdate) {
				m.presentCache.UpdateFlowConfig()
			} else if msg.String() == string(cache.DynamicConfigUpdate) {
				m.presentCache.UpdateDynamicEffectTemplate()
			} else {
				continue
			}
		}
	}
}

// GetPresentConfigList 获取礼物配置，旧版接口，直接拿全量
func (m *UserPresentGoMgr) GetPresentConfigList(updateTime uint32) (*pb.GetPresentConfigListResp, error) {
	out := &pb.GetPresentConfigListResp{}

	out.UpdateTime, out.ItemList = m.presentCache.GetConfigList(updateTime)

	markMap, err := m.GetPresentMarkList(context.Background())
	if err != nil {
		log.ErrorWithCtx(context.Background(), "GetPresentMarkList err , err %v", err)
		return out, err
	}

	for _, item := range out.ItemList {
		if mark, ok := markMap[item.ItemId]; ok {
			item.Extend.MarkId = uint64(mark.MarkId)
			item.Extend.OriginIconUrl = mark.IconUrl
			item.Extend.MarkName = mark.MarkName
		} else {
			item.Extend.MarkId = 0
			item.Extend.OriginIconUrl = item.IconUrl
			item.Extend.MarkName = ""
		}
	}

	return out, nil
}

// GetPresentConfigListV3 获取礼物配置，新版接口，拿增量
func (m *UserPresentGoMgr) GetPresentConfigListV3(updateTime, uid uint32) (*pb.GetPresentConfigListV3Resp, error) {
	out := &pb.GetPresentConfigListV3Resp{}

	if m.config.CheckGetAllUid(uid) {
		updateTime = 0
	}

	out.LastUpdateTime, out.ItemList = m.presentCache.GetConfigListNew(updateTime)

	// 如果更新时间小于配置更新时间，说明有更新，需要返回黑名单
	if m.config.GetLastUpdateTime() > out.LastUpdateTime {
		out.LastUpdateTime = m.config.GetLastUpdateTime()
	}
	if m.config.GetLastUpdateTime() > updateTime || out.LastUpdateTime > updateTime {
		out.EnterBlackList = make([]*pb.PresentEnterBlacklist, 0)
		blackList := m.config.GetEnterBlackList()

		for typ, item := range blackList {
			out.EnterBlackList = append(out.EnterBlackList, &pb.PresentEnterBlacklist{
				EnterType:    typ,
				GiftItemList: item,
			})
		}
	}

	return out, nil
}

// AddPresentConfig 添加礼物配置
func (m *UserPresentGoMgr) AddPresentConfig(ctx context.Context, req *pb.AddPresentConfigReq) (*pb.AddPresentConfigResp, error) {
	out := &pb.AddPresentConfigResp{}

	resp, err := m.store.AddPresentConfigNew(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentConfig failed,req:%v err:%v", req, err)
		return out, err
	}

	out.ItemConfig = resp

	// 更新缓存
	_ = m.cache.NotifyPresentConfigUpdate(ctx, cache.PresentConfigUpdate)
	_ = m.cache.SetPresentConfig(ctx, resp)
	_ = m.cache.DelPresentConfigUpdateTime(ctx)
	m.presentCache.UpdateConfig()

	if req.GetExtend().GetOriginIconUrl() != "" {
		err = m.AddPresentMark(ctx, resp.GetItemId(), uint32(req.GetExtend().GetMarkId()), req.GetExtend().GetOriginIconUrl())
		if err != nil {
			log.ErrorWithCtx(ctx, "AddPresentConfig SavePresentMarkIcon failed,req:%v err:%v", req, err)
			return out, err
		}
	}

	log.DebugWithCtx(ctx, "AddPresentConfig %v", out)
	return out, nil
}

// UpdatePresentConfig 更新礼物配置
func (m *UserPresentGoMgr) UpdatePresentConfig(ctx context.Context, req *pb.UpdatePresentConfigReq) (*pb.UpdatePresentConfigResp, error) {
	out := &pb.UpdatePresentConfigResp{}

	resp, err := m.store.UpdatePresentConfigNew(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdatePresentConfigNew failed,req:%v err:%v", req, err)
		return out, err
	}

	// 更新缓存
	_ = m.cache.NotifyPresentConfigUpdate(ctx, cache.PresentConfigUpdate)
	_ = m.cache.SetPresentConfig(ctx, resp)
	_ = m.cache.DelPresentConfigUpdateTime(ctx)
	m.presentCache.UpdateConfig()

	if req.GetItemConfig().GetExtend().GetOriginIconUrl() != "" {
		err = m.AddPresentMark(ctx, resp.GetItemId(), uint32(req.GetItemConfig().GetExtend().GetMarkId()), req.GetItemConfig().GetExtend().GetOriginIconUrl())
		if err != nil {
			log.ErrorWithCtx(ctx, "AddPresentConfig SavePresentMarkIcon failed,req:%v err:%v", req, err)
			return out, err
		}
	}

	log.DebugWithCtx(ctx, "UpdatePresentConfig %v", out)
	return out, nil
}

// DeletePresentConfig 删除礼物配置
func (m *UserPresentGoMgr) DeletePresentConfig(ctx context.Context, req *pb.DelPresentConfigReq) (*pb.DelPresentConfigResp, error) {
	out := &pb.DelPresentConfigResp{}

	err := m.store.DeletePresentConfigNew(ctx, req.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DeletePresentConfigNew failed,req:%v err:%v", req, err)
		return out, err
	}

	// 更新缓存
	_ = m.cache.NotifyPresentConfigUpdate(ctx, cache.PresentConfigUpdate)
	_ = m.cache.DelPresentConfigById(ctx, req.GetItemId())
	_ = m.cache.DelPresentConfigUpdateTime(ctx)
	m.presentCache.UpdateConfig()

	log.DebugWithCtx(ctx, "DeletePresentConfig %v", out)
	return out, nil
}

// GetPresentConfigById 获取礼物配置，单查
func (m *UserPresentGoMgr) GetPresentConfigById(itemId uint32) (*pb.GetPresentConfigByIdResp, error) {
	out := &pb.GetPresentConfigByIdResp{}

	out.ItemConfig = m.presentCache.GetConfigByIdNew(itemId)

	if out.GetItemConfig() == nil {
		// 获取不到直接报错不存在
		return out, protocol.NewExactServerError(nil, status.ErrUserPresentConfigNotExist)
	}

	log.DebugWithCtx(context.Background(), "GetPresentConfigById %v", out)
	return out, nil
}

// GetPresentConfigListByIdList 获取礼物配置，批量查
func (m *UserPresentGoMgr) GetPresentConfigListByIdList(itemList []uint32) (*pb.GetPresentConfigListByIdListResp, error) {
	out := &pb.GetPresentConfigListByIdListResp{}
	out.ItemConfig = make([]*pb.PresentConfigNew, 0)

	resp := m.presentCache.GetConfigByIdListNew(itemList)
	for _, item := range resp {
		out.ItemConfig = append(out.ItemConfig, item)
	}

	log.DebugWithCtx(context.Background(), "GetPresentConfigListByIdList %v", out)

	return out, nil
}

func (m *UserPresentGoMgr) GetPresentConfigUpdateTime(ctx context.Context, req *pb.GetPresentConfigUpdateTimeReq) (*pb.GetPresentConfigUpdateTimeResp, error) {
	out := &pb.GetPresentConfigUpdateTimeResp{}
	out.UpdateTime = m.presentCache.GetConfigUpdateTime()

	log.DebugWithCtx(ctx, "GetPresentConfigUpdateTime %v", out)
	return out, nil
}

// GetLivePresentOrderList 获取直播间礼物订单
func (m *UserPresentGoMgr) GetLivePresentOrderList(ctx context.Context, req *pb.GetLivePresentOrderListReq) (*pb.GetLivePresentOrderListResp, error) {
	out := &pb.GetLivePresentOrderListResp{}

	resp, err := m.store.GetLivePresentOrderList(ctx, req.GetToUid(), req.GetChannelId(), req.GetBeginTime(), req.GetEndTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLivePresentOrderList failed,req:%v err:%v", req, err)
		return out, err
	}

	out.OrderList = make([]*pb.LivePresentOrder, 0)
	for _, item := range resp {
		out.OrderList = append(out.OrderList, &pb.LivePresentOrder{
			FromUid:    item.FromUid,
			ItemId:     item.ItemId,
			Count:      item.ItemCount,
			ToUid:      item.ToUid,
			CreateTime: item.CreateTime,
		})
	}

	log.DebugWithCtx(ctx, "GetLivePresentOrderList %v", out)

	return out, nil
}

func (m *UserPresentGoMgr) GetPresentDetailList(ctx context.Context, req *pb.GetUserPresentDetailListReq) (*pb.GetUserPresentDetailListResp, error) {
	out := &pb.GetUserPresentDetailListResp{}

	resp, err := m.cache.GetUserPresentDetail(ctx, req.GetUid(), UserDetailListLimit)
	if len(resp) == 0 || err != nil {
		resp, err = m.reloadUserPresentDetailList2Redis(ctx, req.GetUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserPresentDetailList failed,req:%v err:%v", req, err)
			return nil, err
		}
	}

	log.DebugWithCtx(ctx, "GetPresentDetailList %v", out)

	out.DetailList = resp
	return out, nil
}

func (m *UserPresentGoMgr) reloadUserPresentDetailList2Redis(ctx context.Context, uid uint32) ([]*pb.StUserPresentDetail, error) {
	resp := make([]*pb.StUserPresentDetail, 0)
	resp, err := m.detailStore.GetUserPresentReceiveDetailList(ctx, uid, 0, UserDetailListLimit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserPresentDetailList failed,uid:%v err:%v", uid, err)
		return resp, err
	}

	if len(resp) == 0 {
		_ = m.cache.AddUserPresentDetail(ctx, &pb.StUserPresentDetail{TargetUid: uid, ReceiveTime: 0})
		return resp, nil
	}

	_ = m.cache.AddUserPresentDetailList(ctx, uid, resp)
	return resp, nil
}

func (m *UserPresentGoMgr) GetUserPresentSendDetailList(ctx context.Context, req *pb.GetUserPresentSendDetailListReq) (*pb.GetUserPresentSendDetailListResp, error) {
	out := &pb.GetUserPresentSendDetailListResp{}

	resp, err := m.cache.GetUserPresentSendDetail(ctx, req.GetUid(), UserDetailListLimit)
	if len(resp) == 0 || err != nil {
		resp, err = m.reloadUserPresentSendDetailList2Redis(ctx, req.GetUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserPresentSendDetailList failed,req:%v err:%v", req, err)
			return nil, err
		}
	}

	out.DetailList = resp

	log.DebugWithCtx(ctx, "GetUserPresentSendDetailList %v", out)
	return out, nil
}

func (m *UserPresentGoMgr) reloadUserPresentSendDetailList2Redis(ctx context.Context, uid uint32) ([]*pb.StUserPresentDetail, error) {
	resp := make([]*pb.StUserPresentDetail, 0)
	resp, err := m.detailStore.GetUserPresentSendDetailList(ctx, uid, 0, UserDetailListLimit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserPresentDetailList failed,uid:%v err:%v", uid, err)
		return resp, err
	}

	if len(resp) == 0 {
		// 添加空值
		_ = m.cache.AddUserPresentSendDetail(ctx, &pb.StUserPresentDetail{TargetUid: uid, ReceiveTime: 0})
		return resp, nil
	}

	_ = m.cache.AddUserPresentSendDetailList(ctx, uid, resp)
	return resp, nil
}

func (m *UserPresentGoMgr) GetUserPresentSummary(ctx context.Context, req *pb.GetUserPresentSummaryReq) (*pb.GetUserPresentSummaryResp, error) {
	out := &pb.GetUserPresentSummaryResp{}

	tx, err := m.store.GetTx(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentSummary failed,req:%v err:%v", req, err)
		return out, err
	}
	defer store.HandleTx(ctx, tx, err)

	resp, err := m.store.GetUserPresentSummary(ctx, tx, req.GetUid(), req.GetIsSend(), nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentSummary failed,req:%v err:%v", req, err)
		return out, err
	}

	totalCnt, totalValue, err := m.store.GetUserPresentInfo(ctx, tx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentSummary failed,req:%v err:%v", req, err)
		return out, err
	}

	out.SummaryList = resp
	out.TotalCount = totalCnt
	out.TotalValue = totalValue

	log.DebugWithCtx(ctx, "GetPresentSummary %v", out)

	return out, nil
}

func (m *UserPresentGoMgr) GetUserPresentSummaryByItemList(ctx context.Context, req *pb.GetUserPresentSummaryByItemListReq) (*pb.GetUserPresentSummaryByItemListResp, error) {
	out := &pb.GetUserPresentSummaryByItemListResp{}

	resp, err := m.store.GetUserPresentSummary(ctx, nil, req.GetUid(), req.GetIsSend(), req.GetItemList())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentSummary failed,req:%v err:%v", req, err)
		return nil, err
	}

	out.SummaryList = resp

	log.DebugWithCtx(ctx, "GetPresentSummary %v", out)

	return out, nil
}

func (m *UserPresentGoMgr) GetPresentOrderStatus(ctx context.Context, req *pb.GetPresentOrderStatusReq) (*pb.GetPresentOrderStatusResp, error) {
	out := &pb.GetPresentOrderStatusResp{}

	resp, err := m.store.GetPresentOrderStatus(ctx, req.GetUid(), req.GetOrderId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentOrderStatus failed,req:%v err:%v", req, err)
		return nil, err
	}

	if resp {
		out.OrderStatus = 1
	} else {
		out.OrderStatus = 0
	}

	log.DebugWithCtx(ctx, "GetPresentOrderStatus %v", out)

	return out, nil
}

func (m *UserPresentGoMgr) AddChanceItemSource(ctx context.Context, itemSource uint32, playType uint32) (err error) {
	err = m.store.AddChanceItemSource(ctx, itemSource, playType)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChanceItemSource failed,err:%v", err)
	}

	return
}

func (m *UserPresentGoMgr) DeleteChanceItemSource(ctx context.Context, itemSource uint32) (err error) {
	err = m.store.DeleteChanceItemSource(ctx, itemSource)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChanceItemSource failed,err:%v", err)
	}

	return
}

func (m *UserPresentGoMgr) CreatePresentMonthlyTable(ctx context.Context) {
	dateTime := time.Now().AddDate(0, 1, 0)

	m.store.CreatePresentMonthlyTable(ctx, dateTime.Year(), int(dateTime.Month()))
	m.store.CreatePresentTicketMonthlyTable(ctx, dateTime.Year(), int(dateTime.Month()))
	m.detailStore.CreateDealTokenMonthlyTable(ctx, dateTime.Year(), int(dateTime.Month()))
	m.store.CreateAiMonthlyPresentHistoryTable(ctx, dateTime.Year(), int(dateTime.Month()))

	return
}

func (m *UserPresentGoMgr) RunWithTransaction(ctx context.Context, t store.ITransaction, isReadOnly bool, f func(txx mysql.Txx) error) (err error) {
	var tx mysql.Txx
	if isReadOnly {
		tx, err = t.GetReadOnlyTx(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "RunWithTransaction GetReadOnlyTx failed,err:%v", err)
			return
		}
	} else {
		tx, err = t.GetTx(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "RunWithTransaction GetTx failed,err:%v", err)
			return err
		}
	}
	defer store.HandleTx(ctx, tx, err)

	err = f(tx)

	return
}

func (m *UserPresentGoMgr) GetOrderLogByOrderIds(ctx context.Context, req *pb.GetOrderLogByOrderIdsReq) (resp *pb.GetOrderLogByOrderIdsResp, err error) {
	resp = &pb.GetOrderLogByOrderIdsResp{}

	tmpResp, err := m.store.GetOrderLogByOrderIds(ctx, req.GetOrderIds())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderLogByOrderIds failed,err:%v", err)
		return resp, err
	}

	dealTokenMap, err := m.detailStore.BatchGetPresentDealToken(ctx, req.GetOrderIds())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetPresentDealToken failed,err:%v", err)
		return resp, err
	}

	for _, item := range tmpResp {
		tmp := &pb.StUserPresentOrderLog{
			OrderId:     item.OrderId,
			FromUid:     item.FromUid,
			TargetUid:   item.ToUid,
			ChangeScore: item.TotalScore,
			CreateTime:  item.CreateTime,
			DealToken:   dealTokenMap[item.OrderId],
			ScoreType:   item.ScoreType,
		}

		resp.OrderLogList = append(resp.OrderLogList, tmp)
	}

	return
}

// GetAiPresentDetail 获取用户针对指定AI角色+伙伴的送礼详情列表（近31天）
func (m *UserPresentGoMgr) GetAiPresentDetail(ctx context.Context, req *pb.GetAiPresentDetailReq) (*pb.GetAiPresentDetailResp, error) {
	out := &pb.GetAiPresentDetailResp{AiPresentDetailList: make([]*pb.AiPresentDetail, 0)}

	uid := req.GetFromUid()
	if uid == 0 {
		// 没有提供用户ID，返回空列表
		return out, nil
	}

	// 读取近31天内的发送明细（分页取固定上限以控制开销）
	list, err := m.detailStore.GetAiPresentSendDetailList(ctx, uid, 0, UserDetailListLimit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAiPresentDetail GetAiPresentSendDetailList failed, uid:%d, err:%v", uid, err)
		return out, err
	}

	targetRoleId := req.GetTargetRoleId()
	targetPartnerId := req.GetTargetPartnerId()

	for _, it := range list {
		// 按目标角色和伙伴过滤
		if targetRoleId != 0 && it.TargetRoleId != targetRoleId {
			continue
		}
		if targetPartnerId != 0 && it.TargetPartnerId != targetPartnerId {
			continue
		}

		// 计算总价：礼物单价 * 数量
		var totalPrice uint32 = 0
		if cfg := m.presentCache.GetConfigById(it.ItemId); cfg != nil {
			totalPrice = cfg.GetPrice() * it.ItemCount
		}

		out.AiPresentDetailList = append(out.AiPresentDetailList, &pb.AiPresentDetail{
			FromUid:         uid,
			TargetRoleId:    it.TargetRoleId,
			TargetPartnerId: it.TargetPartnerId,
			ItemId:          it.ItemId,
			ItemCount:       it.ItemCount,
			TotalPrice:      totalPrice,
			SendTime:        it.CreateTime,
			BusinessType:    it.BusinessType,
			AddRich:         it.AddRich,
		})
	}

	return out, nil
}

type PresentParam struct {
	PresentSource uint32 `json:"present_source"`
}

// GetAiPresentTotalCount 汇总某时间段的AI礼物订单数和总价值（TBEAN）
func (m *UserPresentGoMgr) GetAiPresentTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	out := &reconcile_v2.CountResp{}
	param := &PresentParam{}
	err := json.Unmarshal([]byte(req.GetParams()), param)
	if err != nil {
		log.Errorf("GetAiPresentTotalCount err , in %v, err %v", req, err)
		param.PresentSource = 0
	}

	log.DebugWithCtx(ctx, "GetAiPresentTotalCount req:%v", req)

	cnt, val, err := m.store.GetAiPresentTotalCount(ctx, req.GetBeginTime(), req.GetEndTime(), param.PresentSource)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAiPresentTotalCount failed, req:%v err:%v", req, err)
		return out, err
	}
	out.Count = cnt
	out.Value = val

	if param.PresentSource == 1 {
		out.Value = cnt
	}
	return out, nil
}

// GetAiPresentOrderIds 获取某时间段内的AI礼物订单ID列表及其对应价值与用户ID
func (m *UserPresentGoMgr) GetAiPresentOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	out := &reconcile_v2.OrderIdsResp{}
	param := &PresentParam{}
	err := json.Unmarshal([]byte(req.GetParams()), param)
	if err != nil {
		log.Errorf("GetAiPresentOrderIds err , in %v, err %v", req, err)
		param.PresentSource = 0
	}

	log.DebugWithCtx(ctx, "GetAiPresentOrderIds req:%v", req)
	ids, values, uids, err := m.store.GetAiPresentOrderIds(ctx, req.GetBeginTime(), req.GetEndTime(), param.PresentSource)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAiPresentOrderIds failed, req:%v err:%v", req, err)
		return out, err
	}
	out.OrderIds = ids
	out.Values = values
	out.Uids = uids
	return out, nil
}

// ReissueAiPresentOrder 补发prepare后未成功finish的AI礼物订单（不改协议）
func (m *UserPresentGoMgr) ReissueAiPresentOrder(ctx context.Context, req *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	out := &reconcile_v2.EmptyResp{}
	info, err := m.store.GetAiPresentByOrderId(ctx, req.GetOrderId())
	if err != nil || info == nil || info.OrderId == "" {
		log.ErrorWithCtx(ctx, "ReissueAiPresentOrder GetAiPresentByOrderId failed, orderId:%s err:%v", req.GetOrderId(), err)
		return out, err
	}

	// 仅处理 status=1(Prepare) 的订单；其它状态直接返回
	if info.Status != 1 {
		log.InfoWithCtx(ctx, "ReissueAiPresentOrder skip, orderId:%s status:%d", req.GetOrderId(), info.Status)
		return out, nil
	}

	// 拉取礼物配置
	itemConfig := m.presentCache.GetConfigById(info.ItemId)
	if itemConfig == nil {
		return out, protocol.NewExactServerError(nil, status.ErrUserPresentConfigNotExist, "礼物配置不存在")
	}
	itemCount := info.ItemCount
	if itemCount == 0 {
		itemCount = 1
	}

	// 计算是否首次（用于事件上报）
	isFirst := false
	if txRO, e := m.store.GetReadOnlyTx(ctx); e == nil {
		if summaryList, e2 := m.store.GetAiPresentSummary(ctx, txRO, info.FromUid, info.TargetRoleId, info.TargetPartnerId, []uint32{}); e2 == nil {
			if len(summaryList) == 0 || (summaryList[0] != nil && summaryList[0].ReceiveCount == 0) {
				isFirst = true
			}
		} else {
			log.ErrorWithCtx(ctx, "ReissueAiPresentOrder GetAiPresentSummary failed, err: %v", e2)
		}
	} else {
		log.ErrorWithCtx(ctx, "ReissueAiPresentOrder GetReadOnlyTx failed, err: %v", e)
	}

	// 1) 更新汇总
	err = m.RunWithTransaction(ctx, m.store, false, func(tx mysql.Txx) error {
		if err := m.store.UpdateAiPresentSummary(ctx, tx, info.FromUid, info.TargetRoleId, info.TargetPartnerId, info.ItemId, itemCount); err != nil {
			log.ErrorWithCtx(ctx, "ReissueAiPresentOrder UpdateAiPresentSummary failed, err: %v", err)
			return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
		}
		return nil
	})
	if err != nil {
		return out, err
	}

	// 2) 记录明细与deal token（deal token 无法重现时，写入占位符，避免再次补发难以判断）
	err = m.RunWithTransaction(ctx, m.detailStore, false, func(tx mysql.Txx) error {
		fakeReq := &pb.SendPresentToAiReq{
			Uid:             info.FromUid,
			TargetRoleId:    info.TargetRoleId,
			TargetPartnerId: info.TargetPartnerId,
			OrderId:         info.OrderId,
			ItemId:          info.ItemId,
			ItemCount:       itemCount,
			ChannelId:       info.ChannelId,
			GuildId:         info.GuildId,
			UserFromIp:      info.UserFromIp,
			ItemSource:      info.PresentSource,
			SendTime:        info.CreateTime,
			ChannelType:     info.ChannelType,
			BindChannelId:   info.BindChannelId,
			BusinessType:    info.BusinessType,
		}
		if err := m.detailStore.RecordAiPresentSendDetail(ctx, tx, fakeReq, *itemConfig, itemCount, true); err != nil {
			log.ErrorWithCtx(ctx, "ReissueAiPresentOrder RecordAiPresentSendDetail failed, err: %v", err)
			return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
		}
		// 使用占位符deal token，标记为reissue
		if err := m.detailStore.RecordPresentDealToken(ctx, tx, req.GetOrderId(), "reissue", info.CreateTime); err != nil {
			log.ErrorWithCtx(ctx, "ReissueAiPresentOrder RecordPresentDealToken failed, err: %v", err)
			return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
		}
		return nil
	})
	if err != nil {
		return out, err
	}

	// 3) 更新状态为Finish(2)
	_ = m.store.UpdateAiMonthlyPresentStatus(ctx, req.GetOrderId(), 2, info.CreateTime)

	// 4) 投递事件
	if m.aiKafkaProducer != nil {
		evt := &pb.AiPresentEvent{
			FromUid:         info.FromUid,
			TargetRoleId:    info.TargetRoleId,
			TargetPartnerId: info.TargetPartnerId,
			ItemId:          info.ItemId,
			ItemCount:       itemCount,
			TotalPrice:      itemConfig.GetPrice() * uint32(itemCount),
			IsFirst:         isFirst,
		}
		if e := m.aiKafkaProducer.PublishAiPresentEvent(ctx, evt); e != nil {
			log.ErrorWithCtx(ctx, "ReissueAiPresentOrder PublishAiPresentEvent failed, orderId:%s err:%v", req.GetOrderId(), e)
			// 事件发送失败不再回滚前面流程
		}
	} else {
		log.ErrorWithCtx(ctx, "ReissueAiPresentOrder aiKafkaProducer is nil, orderId:%s", req.GetOrderId())
	}

	return out, nil
}
