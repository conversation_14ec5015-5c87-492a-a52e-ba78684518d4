package manager

import (
	"context"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/pkg/timer"
	pb "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/userpresent-go/internal/cache"
	config "golang.52tt.com/services/userpresent-go/internal/config/ttconfig/userpresent_go"
	"golang.52tt.com/services/userpresent-go/internal/mock"
	"golang.52tt.com/services/userpresent-go/internal/producer"
	"golang.52tt.com/services/userpresent-go/internal/store"
	"reflect"
	"testing"
)

//func TestNewUserPresentGoMgr(t *testing.T) {
//	ctrl := gomock.NewController(t)
//	storeMock := store.NewMockIStore(ctrl)
//	sceneStoreMock := mock.NewMockISceneStore(ctrl)
//	cacheMock := mock.NewMockICache(ctrl)
//	sceneCacheMock := mock.NewMockISceneCache(ctrl)
//	presentCacheMock := mock.NewMockIPresentMemCache(ctrl)
//	detailStoreMock := mock.NewMockIDetailStore(ctrl)
//	produceMock := mock.NewMockIKafkaProduceMgr(ctrl)
//
//	storeMock.EXPECT().CreatePresentEnterConfigTable(gomock.Any())
//	storeMock.EXPECT().CreatePresentConfigLogTable(gomock.Any())
//	storeMock.EXPECT().CreatePresentEffectConfigTable(gomock.Any())
//	storeMock.EXPECT().CreatePresentBaseConfigTable(gomock.Any())
//	// mock一个redis server
//	s, err := miniredis.Run()
//	if err != nil {
//		t.Error(err)
//	}
//
//	// 连接mock的redis server
//	port, _ := strconv.Atoi(s.Port())
//	redisConf := &redisConnect.RedisConfig{
//		Host: s.Host(),
//		Port: uint32(port),
//	}
//	rdb, err := redisConnect.NewClient(context.Background(), redisConf)
//	if err != nil {
//		t.Error(err)
//	}
//	cacheMock.EXPECT().GetRedisClient().Return(rdb)
//
//	type args struct {
//		ctx           context.Context
//		pwStore       store.IStore
//		pwCache       cache.ICache
//		presentCache  cache.IPresentMemCache
//		presentConfig config.UserPresentGoConf
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    *UserPresentGoMgr
//		wantErr bool
//	}{
//		{
//			name: "test1",
//			args: args{
//				ctx:           context.Background(),
//				pwStore:       storeMock,
//				pwCache:       cacheMock,
//				presentCache:  presentCacheMock,
//				presentConfig: &config.UserPresentGoConfig{},
//			},
//			wantErr: false,
//			want: &UserPresentGoMgr{
//				store:        storeMock,
//				cache:        cacheMock,
//				sceneStore:   sceneStoreMock,
//				sceneCache:   sceneCacheMock,
//				presentCache: presentCacheMock,
//				config:       &config.UserPresentGoConfig{},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := NewUserPresentGoMgr(tt.args.ctx, tt.args.pwStore, sceneStoreMock, detailStoreMock, tt.args.pwCache, sceneCacheMock, tt.args.presentCache, tt.args.presentConfig, produceMock)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("NewUserPresentGoMgr() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("NewUserPresentGoMgr() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestUserPresentGoMgr_AddPresentConfig(t *testing.T) {
	ctrl := gomock.NewController(t)
	storeMock := store.NewMockIStore(ctrl)
	cacheMock := mock.NewMockICache(ctrl)
	presentCacheMock := mock.NewMockIPresentMemCache(ctrl)

	storeMock.EXPECT().AddPresentConfigNew(gomock.Any(), gomock.Any()).Return(&pb.StPresentItemConfig{Name: "111"}, nil).AnyTimes()
	//presentCacheMock.EXPECT().UpdateConfig()
	//presentCacheMock.EXPECT().GetConfigList(gomock.Any())
	cacheMock.EXPECT().SetPresentConfig(gomock.Any(), gomock.Any())
	cacheMock.EXPECT().NotifyPresentConfigUpdate(gomock.Any(), gomock.Any())
	cacheMock.EXPECT().DelPresentConfigUpdateTime(gomock.Any())
	presentCacheMock.EXPECT().UpdateConfig()

	type fields struct {
		store        store.IStore
		cache        cache.ICache
		presentCache cache.IPresentMemCache
		config       config.UserPresentGoConf
	}
	type args struct {
		ctx context.Context
		req *pb.AddPresentConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.AddPresentConfigResp
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				ctx: context.Background(),
				req: &pb.AddPresentConfigReq{
					Name: "111",
				},
			},
			fields: fields{
				store:        storeMock,
				cache:        cacheMock,
				presentCache: presentCacheMock,
				config:       &config.UserPresentGoConfig{},
			},
			want: &pb.AddPresentConfigResp{
				ItemConfig: &pb.StPresentItemConfig{
					Name: "111",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:        tt.fields.store,
				cache:        tt.fields.cache,
				presentCache: tt.fields.presentCache,
				config:       tt.fields.config,
			}
			got, err := m.AddPresentConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddPresentConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddPresentConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserPresentGoMgr_DeletePresentConfig(t *testing.T) {
	ctrl := gomock.NewController(t)
	storeMock := store.NewMockIStore(ctrl)
	cacheMock := mock.NewMockICache(ctrl)
	presentCacheMock := mock.NewMockIPresentMemCache(ctrl)

	storeMock.EXPECT().DeletePresentConfigNew(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	//presentCacheMock.EXPECT().UpdateConfig()
	//presentCacheMock.EXPECT().GetConfigList(gomock.Any())
	cacheMock.EXPECT().DelPresentConfigById(gomock.Any(), gomock.Any())
	cacheMock.EXPECT().NotifyPresentConfigUpdate(gomock.Any(), gomock.Any())
	cacheMock.EXPECT().DelPresentConfigUpdateTime(gomock.Any())
	presentCacheMock.EXPECT().UpdateConfig()

	type fields struct {
		store        store.IStore
		cache        cache.ICache
		presentCache cache.IPresentMemCache
		config       config.UserPresentGoConf
	}
	type args struct {
		ctx context.Context
		req *pb.DelPresentConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.DelPresentConfigResp
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				ctx: context.Background(),
				req: &pb.DelPresentConfigReq{
					ItemId: 1,
				},
			},
			fields: fields{
				store:        storeMock,
				cache:        cacheMock,
				presentCache: presentCacheMock,
				config:       &config.UserPresentGoConfig{},
			},
			want:    &pb.DelPresentConfigResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:        tt.fields.store,
				cache:        tt.fields.cache,
				presentCache: tt.fields.presentCache,
				config:       tt.fields.config,
			}
			got, err := m.DeletePresentConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeletePresentConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DeletePresentConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserPresentGoMgr_GetPresentConfigById(t *testing.T) {
	ctrl := gomock.NewController(t)
	storeMock := store.NewMockIStore(ctrl)
	cacheMock := mock.NewMockICache(ctrl)
	presentCacheMock := mock.NewMockIPresentMemCache(ctrl)

	presentCacheMock.EXPECT().GetConfigByIdNew(gomock.Any()).Return(&pb.PresentConfigNew{}).AnyTimes()

	type fields struct {
		store        store.IStore
		cache        cache.ICache
		presentCache cache.IPresentMemCache
		config       config.UserPresentGoConf
	}
	type args struct {
		itemId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPresentConfigByIdResp
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				itemId: 1,
			},
			fields: fields{
				store:        storeMock,
				cache:        cacheMock,
				presentCache: presentCacheMock,
				config:       &config.UserPresentGoConfig{},
			},
			want:    &pb.GetPresentConfigByIdResp{ItemConfig: &pb.PresentConfigNew{}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:        tt.fields.store,
				cache:        tt.fields.cache,
				presentCache: tt.fields.presentCache,
				config:       tt.fields.config,
			}
			got, err := m.GetPresentConfigById(tt.args.itemId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentConfigById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentConfigById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserPresentGoMgr_GetPresentConfigList(t *testing.T) {
	ctrl := gomock.NewController(t)
	storeMock := store.NewMockIStore(ctrl)
	cacheMock := mock.NewMockICache(ctrl)
	presentCacheMock := mock.NewMockIPresentMemCache(ctrl)

	presentCacheMock.EXPECT().GetConfigList(uint32(10000)).Return(uint32(10000), []*pb.StPresentItemConfig{}).AnyTimes()
	storeMock.EXPECT().GetMarkList(gomock.Any()).Return([]store.PresentMark{}, nil).AnyTimes()
	storeMock.EXPECT().GetPresentMarkIconList(gomock.Any()).Return([]store.PresentMarkIcon{}, nil).AnyTimes()

	type fields struct {
		store        store.IStore
		cache        cache.ICache
		presentCache cache.IPresentMemCache
		config       config.UserPresentGoConf
	}
	type args struct {
		updateTime uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPresentConfigListResp
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				updateTime: 10000,
			},
			fields: fields{
				store:        storeMock,
				cache:        cacheMock,
				presentCache: presentCacheMock,
				config:       &config.UserPresentGoConfig{},
			},
			want: &pb.GetPresentConfigListResp{
				UpdateTime: uint32(10000),
				ItemList:   []*pb.StPresentItemConfig{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:        tt.fields.store,
				cache:        tt.fields.cache,
				presentCache: tt.fields.presentCache,
				config:       tt.fields.config,
			}
			got, err := m.GetPresentConfigList(tt.args.updateTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentConfigList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentConfigList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserPresentGoMgr_GetPresentConfigListByIdList(t *testing.T) {
	ctrl := gomock.NewController(t)
	storeMock := store.NewMockIStore(ctrl)
	cacheMock := mock.NewMockICache(ctrl)
	presentCacheMock := mock.NewMockIPresentMemCache(ctrl)

	presentCacheMock.EXPECT().GetConfigByIdListNew(gomock.Any()).Return(map[uint32]*pb.PresentConfigNew{}).AnyTimes()

	type fields struct {
		store        store.IStore
		cache        cache.ICache
		presentCache cache.IPresentMemCache
		config       config.UserPresentGoConf
	}
	type args struct {
		itemList []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPresentConfigListByIdListResp
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				itemList: []uint32{1},
			},
			fields: fields{
				store:        storeMock,
				cache:        cacheMock,
				presentCache: presentCacheMock,
				config:       &config.UserPresentGoConfig{},
			},
			want:    &pb.GetPresentConfigListByIdListResp{ItemConfig: []*pb.PresentConfigNew{}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:        tt.fields.store,
				cache:        tt.fields.cache,
				presentCache: tt.fields.presentCache,
				config:       tt.fields.config,
			}
			got, err := m.GetPresentConfigListByIdList(tt.args.itemList)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentConfigListByIdList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentConfigListByIdList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserPresentGoMgr_GetPresentConfigListV3(t *testing.T) {
	ctrl := gomock.NewController(t)
	storeMock := store.NewMockIStore(ctrl)
	cacheMock := mock.NewMockICache(ctrl)
	presentCacheMock := mock.NewMockIPresentMemCache(ctrl)
	configMock := mock.NewMockUserPresentGoConf(ctrl)

	presentCacheMock.EXPECT().GetConfigListNew(gomock.Any()).Return(uint32(10000), []*pb.PresentConfigNew{}).AnyTimes()
	configMock.EXPECT().GetEnterBlackList().Return(map[pb.PresentEnterType][]uint32{}).AnyTimes()
	configMock.EXPECT().GetLastUpdateTime().Return(uint32(0)).AnyTimes()
	configMock.EXPECT().CheckGetAllUid(gomock.Any()).Return(false)

	type fields struct {
		store        store.IStore
		cache        cache.ICache
		presentCache cache.IPresentMemCache
		config       config.UserPresentGoConf
	}
	type args struct {
		updateTime uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPresentConfigListV3Resp
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				updateTime: uint32(10000),
			},
			fields: fields{
				store:        storeMock,
				cache:        cacheMock,
				presentCache: presentCacheMock,
				config:       configMock,
			},
			want:    &pb.GetPresentConfigListV3Resp{ItemList: []*pb.PresentConfigNew{}, LastUpdateTime: uint32(10000)},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:        tt.fields.store,
				cache:        tt.fields.cache,
				presentCache: tt.fields.presentCache,
				config:       tt.fields.config,
			}
			got, err := m.GetPresentConfigListV3(tt.args.updateTime, 0)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentConfigListV3() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentConfigListV3() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserPresentGoMgr_UpdatePresentConfig(t *testing.T) {

	ctrl := gomock.NewController(t)
	storeMock := store.NewMockIStore(ctrl)
	cacheMock := mock.NewMockICache(ctrl)
	presentCacheMock := mock.NewMockIPresentMemCache(ctrl)

	storeMock.EXPECT().UpdatePresentConfigNew(gomock.Any(), gomock.Any()).Return(&pb.StPresentItemConfig{}, nil).AnyTimes()
	//presentCacheMock.EXPECT().UpdateConfig()
	cacheMock.EXPECT().SetPresentConfig(gomock.Any(), gomock.Any())
	cacheMock.EXPECT().NotifyPresentConfigUpdate(gomock.Any(), gomock.Any())
	cacheMock.EXPECT().DelPresentConfigUpdateTime(gomock.Any())
	presentCacheMock.EXPECT().UpdateConfig()

	type fields struct {
		store        store.IStore
		cache        cache.ICache
		presentCache cache.IPresentMemCache
		config       config.UserPresentGoConf
	}
	type args struct {
		ctx context.Context
		req *pb.UpdatePresentConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.UpdatePresentConfigResp
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				ctx: context.Background(),
				req: &pb.UpdatePresentConfigReq{
					ItemConfig: &pb.StPresentItemConfig{
						ItemId: 1,
					},
				},
			},
			fields: fields{
				store:        storeMock,
				cache:        cacheMock,
				presentCache: presentCacheMock,
				config:       &config.UserPresentGoConfig{},
			},
			want:    &pb.UpdatePresentConfigResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:        tt.fields.store,
				cache:        tt.fields.cache,
				presentCache: tt.fields.presentCache,
				config:       tt.fields.config,
			}
			got, err := m.UpdatePresentConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdatePresentConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdatePresentConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

//
//func TestUserPresentGoMgr_ShutDown(t *testing.T) {
//	ctrl := gomock.NewController(t)
//	storeMock := store.NewMockIStore(ctrl)
//	cacheMock := mock.NewMockICache(ctrl)
//	presentCacheMock := mock.NewMockIPresentMemCache(ctrl)
//
//	storeMock.EXPECT().Close()
//	cacheMock.EXPECT().Close()
//	presentCacheMock.EXPECT().Stop()
//
//	type fields struct {
//		store        store.IStore
//		cache        cache.ICache
//		presentCache cache.IPresentMemCache
//		config       config.UserPresentGoConf
//	}
//	tests := []struct {
//		name   string
//		fields fields
//	}{
//		{
//			"test1",
//			fields{
//				store:        storeMock,
//				cache:        cacheMock,
//				presentCache: presentCacheMock,
//				config:       &config.UserPresentGoConfig{},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			m := &UserPresentGoMgr{
//				store:        tt.fields.store,
//				cache:        tt.fields.cache,
//				presentCache: tt.fields.presentCache,
//				config:       tt.fields.config,
//			}
//			m.ShutDown()
//		})
//	}
//}

func TestUserPresentGoMgr_GetLivePresentOrderList(t *testing.T) {
	ctrl := gomock.NewController(t)
	storeMock := store.NewMockIStore(ctrl)
	cacheMock := mock.NewMockICache(ctrl)
	presentCacheMock := mock.NewMockIPresentMemCache(ctrl)

	storeMock.EXPECT().GetLivePresentOrderList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*store.LivePresentOrder{{ItemId: 1}}, nil).AnyTimes()

	type fields struct {
		store        store.IStore
		cache        cache.ICache
		presentCache cache.IPresentMemCache
		config       config.UserPresentGoConf
	}
	type args struct {
		ctx context.Context
		req *pb.GetLivePresentOrderListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetLivePresentOrderListResp
		wantErr bool
	}{
		{
			name: "test1",
			fields: fields{
				store:        storeMock,
				cache:        cacheMock,
				presentCache: presentCacheMock,
				config:       &config.UserPresentGoConfig{},
			},
			args: args{
				ctx: context.Background(),
				req: &pb.GetLivePresentOrderListReq{
					ToUid:     1,
					ChannelId: 1,
					BeginTime: 1,
					EndTime:   1,
				},
			},
			want:    &pb.GetLivePresentOrderListResp{OrderList: []*pb.LivePresentOrder{{ItemId: 1}}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:        tt.fields.store,
				cache:        tt.fields.cache,
				presentCache: tt.fields.presentCache,
				config:       tt.fields.config,
			}
			got, err := m.GetLivePresentOrderList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLivePresentOrderList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLivePresentOrderList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserPresentGoMgr_CreatePresentMonthlyTable(t *testing.T) {
	ctrl := gomock.NewController(t)
	storeMock := store.NewMockIStore(ctrl)
	detailStoreMock := store.NewMockIDetailStore(ctrl)

	storeMock.EXPECT().CreatePresentMonthlyTable(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	storeMock.EXPECT().CreatePresentTicketMonthlyTable(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	detailStoreMock.EXPECT().CreateDealTokenMonthlyTable(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	storeMock.EXPECT().CreateAiMonthlyPresentHistoryTable(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()

	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "test1",
			fields: fields{
				store:       storeMock,
				detailStore: detailStoreMock,
			},
			args: args{
				ctx: context.Background(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			m.CreatePresentMonthlyTable(tt.args.ctx)
		})
	}
}

func TestUserPresentGoMgr_GetPresentConfigUpdateTime(t *testing.T) {
	ctrl := gomock.NewController(t)
	presentCacheMock := mock.NewMockIPresentMemCache(ctrl)
	presentCacheMock.EXPECT().GetConfigUpdateTime()

	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx context.Context
		req *pb.GetPresentConfigUpdateTimeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPresentConfigUpdateTimeResp
		wantErr bool
	}{
		{
			name: "test1",
			fields: fields{
				presentCache: presentCacheMock,
			},
			args: args{
				ctx: context.Background(),
			},
			want: &pb.GetPresentConfigUpdateTimeResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			got, err := m.GetPresentConfigUpdateTime(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentConfigUpdateTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentConfigUpdateTime() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserPresentGoMgr_GetPresentDetailList(t *testing.T) {
	ctrl := gomock.NewController(t)
	cacheMock := mock.NewMockICache(ctrl)
	detailStoreMock := store.NewMockIDetailStore(ctrl)
	cacheMock.EXPECT().GetUserPresentDetail(gomock.Any(), gomock.Any(), gomock.Any())
	cacheMock.EXPECT().AddUserPresentDetail(gomock.Any(), gomock.Any())
	detailStoreMock.EXPECT().GetUserPresentReceiveDetailList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx context.Context
		req *pb.GetUserPresentDetailListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetUserPresentDetailListResp
		wantErr bool
	}{
		{
			name: "test1",
			fields: fields{
				cache:       cacheMock,
				detailStore: detailStoreMock,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.GetUserPresentDetailListReq{
					Uid: 1,
				},
			},
			want: &pb.GetUserPresentDetailListResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			got, err := m.GetPresentDetailList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentDetailList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentDetailList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserPresentGoMgr_GetPresentOrderStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	storeMock := store.NewMockIStore(ctrl)
	storeMock.EXPECT().GetPresentOrderStatus(gomock.Any(), gomock.Any(), gomock.Any())
	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx context.Context
		req *pb.GetPresentOrderStatusReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPresentOrderStatusResp
		wantErr bool
	}{
		{
			name: "test1",
			fields: fields{
				store: storeMock,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.GetPresentOrderStatusReq{
					OrderId: "1",
				},
			},
			want: &pb.GetPresentOrderStatusResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			got, err := m.GetPresentOrderStatus(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentOrderStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentOrderStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserPresentGoMgr_GetUserPresentSendDetailList(t *testing.T) {
	ctrl := gomock.NewController(t)
	detailStoreMock := store.NewMockIDetailStore(ctrl)
	cacheMock := mock.NewMockICache(ctrl)
	detailStoreMock.EXPECT().GetUserPresentSendDetailList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	cacheMock.EXPECT().GetUserPresentSendDetail(gomock.Any(), gomock.Any(), gomock.Any())
	cacheMock.EXPECT().AddUserPresentSendDetail(gomock.Any(), gomock.Any())

	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx context.Context
		req *pb.GetUserPresentSendDetailListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetUserPresentSendDetailListResp
		wantErr bool
	}{
		{
			name: "test1",
			fields: fields{
				detailStore: detailStoreMock,
				cache:       cacheMock,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.GetUserPresentSendDetailListReq{
					Uid: 1,
				},
			},
			want: &pb.GetUserPresentSendDetailListResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			got, err := m.GetUserPresentSendDetailList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserPresentSendDetailList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserPresentSendDetailList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserPresentGoMgr_reloadUserPresentDetailList2Redis(t *testing.T) {
	ctrl := gomock.NewController(t)
	detailStore := store.NewMockIDetailStore(ctrl)
	cacheMock := mock.NewMockICache(ctrl)

	detailStore.EXPECT().GetUserPresentReceiveDetailList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*pb.StUserPresentDetail{}, nil)
	cacheMock.EXPECT().AddUserPresentDetail(gomock.Any(), gomock.Any())
	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.StUserPresentDetail
		wantErr bool
	}{
		{
			name: "test1",
			fields: fields{
				detailStore: detailStore,
				cache:       cacheMock,
			},
			args: args{
				ctx: context.Background(),
				uid: 1,
			},
			want: []*pb.StUserPresentDetail{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			got, err := m.reloadUserPresentDetailList2Redis(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("reloadUserPresentDetailList2Redis() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("reloadUserPresentDetailList2Redis() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserPresentGoMgr_reloadUserPresentSendDetailList2Redis(t *testing.T) {
	ctrl := gomock.NewController(t)
	detailStore := store.NewMockIDetailStore(ctrl)
	cacheMock := mock.NewMockICache(ctrl)

	detailStore.EXPECT().GetUserPresentSendDetailList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*pb.StUserPresentDetail{}, nil)
	cacheMock.EXPECT().AddUserPresentSendDetail(gomock.Any(), gomock.Any())

	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.StUserPresentDetail
		wantErr bool
	}{
		{
			name: "test1",
			fields: fields{
				detailStore: detailStore,
				cache:       cacheMock,
			},
			args: args{
				ctx: context.Background(),
				uid: 1,
			},
			want: []*pb.StUserPresentDetail{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			got, err := m.reloadUserPresentSendDetailList2Redis(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("reloadUserPresentSendDetailList2Redis() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("reloadUserPresentSendDetailList2Redis() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserPresentGoMgr_GetUserPresentSummary(t *testing.T) {
	ctrl := gomock.NewController(t)
	storeMock := store.NewMockIStore(ctrl)

	storeMock.EXPECT().GetTx(gomock.Any())
	storeMock.EXPECT().GetUserPresentSummary(gomock.Any(), nil, gomock.Any(), gomock.Any(), gomock.Any())
	storeMock.EXPECT().GetUserPresentInfo(gomock.Any(), nil, gomock.Any())

	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx context.Context
		req *pb.GetUserPresentSummaryReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetUserPresentSummaryResp
		wantErr bool
	}{
		{
			name: "test1",
			fields: fields{
				store: storeMock,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.GetUserPresentSummaryReq{},
			},
			want: &pb.GetUserPresentSummaryResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			got, err := m.GetUserPresentSummary(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserPresentSummary() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserPresentSummary() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUserPresentGoMgr_GetAiPresentDetail(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	detailStoreMock := store.NewMockIDetailStore(ctrl)
	presentCacheMock := mock.NewMockIPresentMemCache(ctrl)

	detailStoreMock.EXPECT().GetAiPresentSendDetailList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*store.AiPresentSendDetail{
		{TargetRoleId: 1, TargetPartnerId: 1, ItemId: 1, ItemCount: 1},
		{TargetRoleId: 1, TargetPartnerId: 2, ItemId: 2, ItemCount: 2},
	}, nil)
	presentCacheMock.EXPECT().GetConfigById(uint32(1)).Return(&pb.StPresentItemConfig{Price: 10})
	presentCacheMock.EXPECT().GetConfigById(uint32(2)).Return(&pb.StPresentItemConfig{Price: 20})

	type fields struct {
		detailStore  store.IDetailStore
		presentCache cache.IPresentMemCache
	}
	type args struct {
		ctx context.Context
		req *pb.GetAiPresentDetailReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetAiPresentDetailResp
		wantErr bool
	}{
		{
			name: "Success",
			fields: fields{
				detailStore:  detailStoreMock,
				presentCache: presentCacheMock,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.GetAiPresentDetailReq{FromUid: 123},
			},
			want: &pb.GetAiPresentDetailResp{
				AiPresentDetailList: []*pb.AiPresentDetail{
					{FromUid: 123, TargetRoleId: 1, TargetPartnerId: 1, ItemId: 1, ItemCount: 1, TotalPrice: 10},
					{FromUid: 123, TargetRoleId: 1, TargetPartnerId: 2, ItemId: 2, ItemCount: 2, TotalPrice: 40},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				detailStore:  tt.fields.detailStore,
				presentCache: tt.fields.presentCache,
			}
			got, err := m.GetAiPresentDetail(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAiPresentDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAiPresentDetail() got = %v, want %v", got, tt.want)
			}
		})
	}
}
