package wedding_process

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/clients/account"
    "golang.52tt.com/pkg/protocol"
    protocolgrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    appChannelPB "golang.52tt.com/protocol/app/channel"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    imPB "golang.52tt.com/protocol/app/im"
    "golang.52tt.com/protocol/app/virtual_image_logic"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
    anti_corruption_layer "golang.52tt.com/services/channel-wedding/internal/model/anti-corruption-layer"
    "golang.52tt.com/services/channel-wedding/internal/model/comm"
    "golang.52tt.com/services/channel-wedding/internal/model/wedding-process/cache"
    "golang.52tt.com/services/channel-wedding/internal/model/wedding-process/stages"
    "golang.52tt.com/services/channel-wedding/internal/model/wedding-process/store"
    "google.golang.org/grpc/codes"
    "time"
)

// ReserveWedding 预约婚礼
func (m *WeddingProcess) ReserveWedding(ctx context.Context, opUid uint32, req *pb.ReserveWeddingReq) (uint32, error) {
    now := time.Now()
    beginTs := req.GetStartTime()
    endTs := req.GetEndTime()
    cid := req.GetCid()

    if now.Unix() > beginTs+300 || beginTs >= endTs || cid == 0 || req.GetBrideUid() == 0 || req.GetGroomUid() == 0 {
        log.ErrorWithCtx(ctx, "ReserveWedding failed. opUid:%v, req:%+v, now:%d",
            opUid, req, now.Unix())
        return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "参数错误")
    }

    lv := uint32(1)
    lvCfg, err := m.getWeddingThemeLevelCfg(ctx, req.GetWeddingThemeId(), lv)
    if err != nil {
        log.ErrorWithCtx(ctx, "ReserveWedding failed to GetWeddingThemeCfg. opUid:%v, req:%+v, err:%v",
            opUid, req, err)
        return 0, err
    }
    if lvCfg == nil {
        log.ErrorWithCtx(ctx, "ReserveWedding failed to GetWeddingThemeCfg. opUid:%v, req:%+v", opUid, req)
        return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "主题配置不存在")
    }

    userMap, err := m.acLayerMgr.GetUserProfileMap(ctx, req.GetBridesmaidUidList(), false)
    if err != nil {
        log.ErrorWithCtx(ctx, "ReserveWedding failed to genUserSuitMap. opUid:%v, req:%+v, err:%v",
            opUid, req, err)
        return 0, err
    }

    uid2SuitMap := genUserSuitMap(userMap, lvCfg, req.GetGroomUid(), req.GetBrideUid(), req.GetBridesmaidUidList())

    var remainDaysLimit uint32
    if req.GetThemeType() == uint32(pb.WeddingThemeType_WEDDING_THEME_TYPE_FREE) {
        remainDaysLimit = m.bc.GetBridesmaidSuitRemainDaysLimit()
    }
    var weddingId uint32
    err = m.store.Transaction(ctx, func(tx mysql.Txx) error {
        var err error
        weddingId, err = m.store.AddWeddingSchedule(ctx, tx, &store.WeddingSchedule{
            Cid:              cid,
            ThemeId:          req.GetWeddingThemeId(),
            BrideUid:         req.GetBrideUid(),
            GroomUid:         req.GetGroomUid(),
            BeginTime:        time.Unix(beginTs, 0),
            EndTime:          time.Unix(endTs, 0),
            Status:           store.WeddingStatusNotStart,
            PlanId:           req.GetPlanId(),
            ThemeType:        req.GetThemeType(),
            CTime:            now,
            InitHappinessVal: req.GetInitHappinessValue(),
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "ReserveWedding failed to AddWeddingSchedule. opUid:%v, req:%+v, err:%v",
                opUid, req, err)
            return err
        }

        awardList := make([]*store.AwardLog, 0)
        for uid, suitCfg := range uid2SuitMap {
            if suitCfg == nil {
                continue
            }

            daysLimit := uint32(0)
            if uid == req.GetBrideUid() || uid == req.GetGroomUid() {
                // 新人婚服额外赠送时长
                suitCfg.DurationSec += req.GetSuitExtraSendSec()
            } else {
                // 伴娘伴郎，设置剩余天数限制
                daysLimit = remainDaysLimit
            }

            orderId := genSuitOrderId(cid, int64(weddingId), uid, lv)
            r := fillSuitAwardLog(orderId, uid, weddingId, lv, req.GetThemeType(), daysLimit, suitCfg, now)
            if r == nil {
                continue
            }
            awardList = append(awardList, r)
        }

        if len(awardList) > 0 {
            err = m.store.BatchInsertAwardLog(ctx, tx, now, awardList)
            if err != nil {
                log.ErrorWithCtx(ctx, "ReserveWedding failed to BatchInsertAwardLog. opUid:%v, req:%+v, err:%v",
                    opUid, req, err)
                return err
            }
        }

        return nil
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "ReserveWedding failed to Transaction. opUid:%v, req:%+v, err:%v",
            opUid, req, err)
        return weddingId, err
    }

    imCfg := m.bc.GetSuitSendImCfg()
    if imCfg != nil && imCfg.Content != "" {
        // 发送im消息
        toUidList := make([]uint32, 0)
        for uid := range uid2SuitMap {
            if req.GetUid() == uid {
                // 不发给自己
                continue
            }
            toUidList = append(toUidList, uid)
        }
        err = m.acLayerMgr.SendIMMsgAsync(ctx, opUid, toUidList, imCfg.Content, imCfg.Highlight, imCfg.JumpUrl)
        if err != nil {
            log.WarnWithCtx(ctx, "ReserveWedding failed to SendIMMsgAsync. opUid:%v, req:%+v, err:%v", opUid, req, err)
        }
    }

    log.InfoWithCtx(ctx, "ReserveWedding success. opUid:%v, req:%+v, weddingId:%d", opUid, req, weddingId)
    return weddingId, nil
}

func genUserSuitMap(userMap map[uint32]*app.UserProfile, cfg *channel_wedding_conf.ThemeLevelCfg, groomUid, brideUid uint32, bridesmaidUidList []uint32) map[uint32]*channel_wedding_conf.GuestSuitCfg {
    uid2Suit := make(map[uint32]*channel_wedding_conf.GuestSuitCfg)
    if cfg == nil {
        return uid2Suit
    }

    if brideUid != 0 {
        guestTy := uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDE)
        uid2Suit[brideUid] = cfg.GetGuestDressCfgMap()[guestTy].GetSuitCfg()
    }
    if groomUid != 0 {
        guestTy := uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_GROOM)
        uid2Suit[groomUid] = cfg.GetGuestDressCfgMap()[guestTy].GetSuitCfg()
    }

    for _, uid := range bridesmaidUidList {
        user, ok := userMap[uid]
        if !ok {
            continue
        }

        if user.GetSex() == uint32(account.Male) {
            guestTy := uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES)
            uid2Suit[uid] = cfg.GetGuestDressCfgMap()[guestTy].GetSuitCfg()
        } else {
            guestTy := uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID)
            uid2Suit[uid] = cfg.GetGuestDressCfgMap()[guestTy].GetSuitCfg()
        }
    }

    return uid2Suit
}

func fillSuitAwardLog(orderId string, uid, weddingId, level, themeTy, remainDaysLimit uint32, suit *channel_wedding_conf.GuestSuitCfg, awardTime time.Time) *store.AwardLog {
    if suit == nil || len(suit.GetItemIds()) == 0 {
        return nil
    }

    out := &store.AwardLog{
        Uid:              uid,
        OrderId:          orderId,
        WeddingId:        weddingId,
        WeddingLv:        level,
        SuitName:         suit.GetName(),
        SuitIcon:         suit.GetIcon(),
        AwardDurationSec: suit.GetDurationSec(),
        AwardTime:        awardTime,
        ThemeType:        themeTy,
        RemainDaysLimit:  remainDaysLimit,
    }

    out.SetSuitItemList(suit.GetItemIds())
    return out
}

// TryBeginWedding 尝试开始婚礼
func (m *WeddingProcess) TryBeginWedding(c context.Context, opUid uint32, req *store.WeddingSchedule) error {
    if req == nil || req.Cid == 0 || req.Id == 0 {
        log.ErrorWithCtx(c, "TryBeginWedding invalid params. opUid:%v, req:%v", opUid, req)
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    ctx, cancel := protocolgrpc.NewContextWithInfoTimeout(c, time.Second*10)
    defer cancel()

    cid := req.Cid
    info, exist, err := m.cache.GetWeddingInfo(ctx, cid)
    if err != nil {
        log.ErrorWithCtx(ctx, "TryBeginWedding failed to GetWeddingInfo. opUid:%v, req:%+v, err:%v",
            opUid, req, err)
        return err
    }

    if exist {
        if info.EndTs > time.Now().Unix() {
            log.ErrorWithCtx(ctx, "TryBeginWedding wedding exist. opUid:%v, req:%+v", opUid, req)
            return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "已存在进行中婚礼")
        }

        if info.WeddingId != req.Id {
            // 开启了新一场婚礼，需要结束之前的婚礼
            err = m.SwitchWeddingStage(ctx, opUid, cid, uint32(pb.WeddingStage_WEDDING_STAGE_UNSPECIFIED), 0)
            if err != nil {
                log.ErrorWithCtx(ctx, "TryBeginWedding failed to SwitchWeddingStage. opUid:%v, req:%+v, err:%v",
                    opUid, req, err)
                return err
            }
        }
    }

    themeCfg, err := m.acLayerMgr.GetWeddingThemeCfg(ctx, req.ThemeId)
    if err != nil {
        log.ErrorWithCtx(ctx, "TryBeginWedding failed to GetWeddingThemeCfg. opUid:%v, req:%+v, err:%v",
            opUid, req, err)
        return err
    }

    isFreeWedding := req.ThemeType == uint32(pb.WeddingThemeType_WEDDING_THEME_TYPE_FREE)
    var firstStage uint32
    for _, v := range themeCfg.GetStageCfgList() {
        //if v.NotSupportFree && isFreeWedding {
        //    // 免费婚礼不支持的阶段
        //    continue
        //}
        firstStage = v.GetStage()
        break
    }

    now := time.Now()
    newInfo := &cache.WeddingInfo{
        Cid:          cid,
        WeddingId:    req.Id,
        ThemeId:      req.ThemeId,
        BeginTs:      req.BeginTime.Unix(),
        EndTs:        req.EndTime.Unix(),
        BrideUid:     req.BrideUid,
        GroomUid:     req.GroomUid,
        Stage:        firstStage,
        StageBeginTs: now.Unix(),
        ReserveTime:  req.CTime.Unix(),
        PlanId:       req.PlanId,
        ThemeType:    req.ThemeType,
    }

    // 初始化新人婚礼服装
    err = m.initGroomBrideClothes(ctx, newInfo)
    if err != nil {
        log.ErrorWithCtx(ctx, "TryBeginWedding failed to initGroomBrideClothes. opUid:%v, req:%+v, err:%v",
            opUid, req, err)
        return err
    }

    // 事务操作
    err = m.store.Transaction(ctx, func(tx mysql.Txx) error {
        err := m.store.UpdateWeddingStatus(ctx, tx, req.Id, store.WeddingStatusProcessing)
        if err != nil {
            log.ErrorWithCtx(ctx, "TryBeginWedding failed to UpdateWeddingStatus. opUid:%v, req:%+v, err:%v",
                opUid, req, err)
            return err
        }

        userWeddingInfo := &cache.UserWeddingInfo{
            Cid:       cid,
            WeddingId: req.Id,
            BeginTs:   req.BeginTime.Unix(),
            EndTs:     req.EndTime.Unix(),
        }

        err = m.cache.SetUserWeddingInfo(ctx, req.BrideUid, userWeddingInfo)
        if err != nil {
            log.ErrorWithCtx(ctx, "TryBeginWedding failed to SetUserWeddingInfo. opUid:%v, req:%+v, err:%v",
                opUid, req, err)
            return err
        }

        err = m.cache.SetUserWeddingInfo(ctx, req.GroomUid, userWeddingInfo)
        if err != nil {
            log.ErrorWithCtx(ctx, "TryBeginWedding failed to SetUserWeddingInfo. opUid:%v, req:%+v, err:%v",
                opUid, req, err)
            return err
        }

        if isFreeWedding {
            // 免费婚礼，加入阶段队列
            err = m.cache.AddStageCidQueue(ctx, cid, now.Unix())
            if err != nil {
                log.ErrorWithCtx(ctx, "TryBeginWedding failed to AddStageCidQueue. opUid:%v, req:%+v, err:%v",
                    opUid, req, err)
                return err
            }
        }

        err = m.cache.SetWeddingInfo(ctx, cid, newInfo)
        if err != nil {
            log.ErrorWithCtx(ctx, "TryBeginWedding failed to SetWeddingInfo. opUid:%v, req:%+v, err:%v",
                opUid, req, err)
            return err
        }

        return nil
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "TryBeginWedding failed to Transaction. opUid:%v, req:%+v, err:%v",
            opUid, req, err)
        return err
    }

    // 通知婚礼阶段变更
    pbInfo, err := m.WeddingStageChangeNotify(ctx, opUid, cid)
    if err != nil {
        log.WarnWithCtx(ctx, "TryBeginWedding failed to WeddingStageChangeNotify. opUid:%v, req:%+v, err:%v",
            opUid, req, err)
    }

    // 婚礼开始的后续操作
    m.afterStartWeddingHandle(ctx, pbInfo, req.InitHappinessVal)

    log.InfoWithCtx(ctx, "TryBeginWedding success. opUid:%v, req:%+v", opUid, req)
    return nil
}

// afterStartWeddingHandle 处理婚礼开始的后续操作
func (m *WeddingProcess) afterStartWeddingHandle(c context.Context, info *pb.WeddingInfo, initHappinessVal uint32) {
    go func() {
        ctx, cancel := protocolgrpc.NewContextWithInfoTimeout(c, time.Minute)
        defer cancel()
        // 初始化婚礼服装
        err := m.initGuestClothes(ctx, info)
        if err != nil {
            log.WarnWithCtx(ctx, "afterStartWeddingHandle failed to initGuestClothes. info:%+v, err:%v",
                info, err)
        }
    }()

    go func() {
        ctx, cancel := protocolgrpc.NewContextWithInfoTimeout(c, time.Second*10)
        defer cancel()
        // 踢人下麦
        err := m.acLayerMgr.KickCommUserOutMicSpace(ctx, info)
        if err != nil {
            log.WarnWithCtx(ctx, "afterStartWeddingHandle failed to KickCommUserOutMicSpace. info:%+v, err:%v",
                info, err)
        }
    }()

    go func() {
        ctx, cancel := protocolgrpc.NewContextWithInfoTimeout(c, time.Second*10)
        defer cancel()
        // 重置所有麦位
        micList := []uint32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, comm.MvpMicId}
        err := m.acLayerMgr.SetMicStatus(ctx, info.GetCid(), uint32(appChannelPB.MicrSpace_MIC_SPACE_NOMAL), micList)
        if err != nil {
            log.WarnWithCtx(ctx, "afterStartWeddingHandle failed to SetMicStatus. info:%+v, err:%v",
                info, err)
        }
    }()

    go func() {
        ctx, cancel := protocolgrpc.NewContextWithInfoTimeout(c, time.Second*10)
        defer cancel()

        // 新娘朝左
        err := m.weddingPose.SetUserWeddingPoseOrientation(ctx, info.Cid, info.GetBride().GetUid(),
            uint32(virtual_image_logic.VirtualImageOrientation_VIRTUAL_IMAGE_ORIENTATION_LEFT))
        if err != nil {
            log.WarnWithCtx(ctx, "afterStartWeddingHandle failed to SetUserWeddingPoseOrientation. info:%+v, err:%v",
                info, err)
        }

        // 新郎朝右
        err = m.weddingPose.SetUserWeddingPoseOrientation(ctx, info.Cid, info.GetGroom().GetUid(),
            uint32(virtual_image_logic.VirtualImageOrientation_VIRTUAL_IMAGE_ORIENTATION_RIGHT))
        if err != nil {
            log.WarnWithCtx(ctx, "afterStartWeddingHandle failed to SetUserWeddingPoseOrientation. info:%+v, err:%v",
                info, err)
        }
    }()

    if initHappinessVal > 0 {
        // 初始化幸福值
        go func() {
            ctx, cancel := protocolgrpc.NewContextWithInfoTimeout(c, time.Second*10)
            defer cancel()

            err := m.AddHappiness(ctx, info, initHappinessVal)
            if err != nil {
                log.WarnWithCtx(ctx, "afterStartWeddingHandle failed to AddHappiness. info:%+v, err:%v",
                    info, err)
            }
        }()
    }
}

// initGuestClothes 初始化新人婚礼服装
func (m *WeddingProcess) initGroomBrideClothes(ctx context.Context, info *cache.WeddingInfo) error {
    lv := uint32(1)
    level1Config, err := m.getWeddingThemeLevelCfg(ctx, info.ThemeId, lv)
    if err != nil {
        log.ErrorWithCtx(ctx, "initGroomBrideClothes failed to getWeddingThemeLevelCfg. info:%+v, err:%v", info, err)
        return err
    }

    if level1Config == nil {
        return nil
    }

    allowUseSubCateMap := make(map[uint32]bool)
    for _, sb := range m.bc.GetAllowChangeSubCategoryList() {
        allowUseSubCateMap[sb] = true
    }

    dressMap := level1Config.GetGuestDressCfgMap()

    // 穿戴新娘服装
    //brideSuitInfo := fillUpgradeSuitInfo(info.BrideUid, level1Config.BrideSuit, genSuitOrderId(info.Cid, int64(info.WeddingId), info.BrideUid, lv), true)
    brideItemIds := dressMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDE)].GetSuitCfg().GetItemIds()
    err = m.acLayerMgr.UseVirtualImageSuit(ctx, info.BrideUid, brideItemIds, allowUseSubCateMap)
    if err != nil {
        log.ErrorWithCtx(ctx, "initGroomBrideClothes failed to giveGuestClothes. bride, info:%+v, err:%v", info, err)
        return err
    }

    // 穿戴新郎服装
    //groomSuitInfo := fillUpgradeSuitInfo(info.GroomUid, level1Config.GroomSuit, genSuitOrderId(info.Cid, int64(info.WeddingId), info.GroomUid, lv), true)
    groomItemIds := dressMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_GROOM)].GetSuitCfg().GetItemIds()
    err = m.acLayerMgr.UseVirtualImageSuit(ctx, info.GroomUid, groomItemIds, allowUseSubCateMap)
    if err != nil {
        log.ErrorWithCtx(ctx, "initGroomBrideClothes failed to giveGuestClothes. groom, info:%+v, err:%v", info, err)
        return err
    }

    log.InfoWithCtx(ctx, "initGroomBrideClothes success. info:%+v", info)
    return nil
}

// initGuestClothes 初始化婚礼伴郎伴娘服装
func (m *WeddingProcess) initGuestClothes(ctx context.Context, info *pb.WeddingInfo) error {
    lv := uint32(1)
    level1Config, err := m.getWeddingThemeLevelCfg(ctx, info.GetThemeCfg().GetThemeId(), lv)
    if err != nil {
        log.ErrorWithCtx(ctx, "initGuestClothes failed to getWeddingThemeLevelCfg. info:%+v, err:%v", info, err)
        return err
    }
    if level1Config == nil {
        return nil
    }

    if len(info.GetBridesmaidManList()) == 0 {
        return nil
    }

    userMap, err := m.acLayerMgr.GetUserProfileMap(ctx, info.GetBridesmaidManList(), false)
    if err != nil {
        log.ErrorWithCtx(ctx, "initGuestClothes failed to GetUserProfileMap. info:%+v, err:%v", info, err)
        return err
    }

    dressMap := level1Config.GetGuestDressCfgMap()
    for _, uid := range info.GetBridesmaidManList() {
        if uid == info.GetBride().GetUid() || uid == info.GetGroom().GetUid() {
            continue
        }

        guestTy := uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID)
        if userMap[uid].GetSex() == uint32(account.Male) {
            guestTy = uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES)
        }
        itemIds := dressMap[guestTy].GetSuitCfg().GetItemIds()

        //friendSuitInfo := fillUpgradeSuitInfo(uid, suit, genSuitOrderId(info.GetCid(), info.GetWeddingId(), uid, lv), true)
        err = m.acLayerMgr.UseVirtualImageSuit(ctx, uid, itemIds, nil)
        if err != nil {
            log.ErrorWithCtx(ctx, "initGuestClothes failed to giveGuestClothes. groom, info:%+v, err:%v", info, err)
            return err
        }
    }

    log.InfoWithCtx(ctx, "initGuestClothes success. info:%+v", info)
    return nil
}

// SwitchWeddingStage 切换婚礼阶段
func (m *WeddingProcess) SwitchWeddingStage(ctx context.Context, opUid, cid, stage, subStage uint32) error {
    info, exist, err := m.cache.GetWeddingInfo(ctx, cid)
    if err != nil {
        log.ErrorWithCtx(ctx, "SwitchWeddingStage failed to GetWeddingInfo. opUid:%v, cid:%d, stage:%d, subStage:%d, err:%v",
            opUid, cid, stage, subStage, err)
        return err
    }

    if !exist {
        log.ErrorWithCtx(ctx, "SwitchWeddingStage wedding not exist. opUid:%v, cid:%d, stage:%d, subStage:%d",
            opUid, cid, stage, subStage)
        return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingStageSwitchFail, "婚礼信息不存在")
    }

    now := time.Now().Unix()
    if now < info.BeginTs {
        log.ErrorWithCtx(ctx, "SwitchWeddingStage wedding not in time. opUid:%v, cid:%d, stage:%d, subStage:%d, beginTs:%d, endTs:%d",
            opUid, cid, stage, subStage, info.BeginTs, info.EndTs)
        return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingStageSwitchFail, "婚礼未开始，无法切换阶段")
    }
    if info.Stage == stage && info.SubStage == subStage {
        log.WarnWithCtx(ctx, "SwitchWeddingStage stage not change. opUid:%v, cid:%d, stage:%d, subStage:%d, beginTs:%d, endTs:%d",
            opUid, cid, stage, subStage, info.BeginTs, info.EndTs)
        return nil
    }
    if info.Stage != stage && info.StageBeginTs+int64(m.bc.GetStageSwitchIntervalSec()) > now {
        return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingStageSwitchFail, "手速太快了，稍后再试试~")
    }

    if stage == uint32(channel_wedding_logic.WeddingStage_WEDDING_STAGE_GROUP_PHOTO) &&
        subStage == uint32(channel_wedding_logic.GroupPhotoSubStage_GROUP_PHOTO_SUB_STAGE_PHOTOGRAPH) {
        // 拍照阶段，检查是否可以进入拍照
        err = m.groupPhoto.CheckIfCanEnterGroupPhoto(ctx, info.WeddingId)
        if err != nil {
            log.ErrorWithCtx(ctx, "SwitchWeddingStage failed to CheckIfCanEnterGroupPhoto. opUid:%v, cid:%d, stage:%d, subStage:%d, beginTs:%d, endTs:%d, err:%v",
                opUid, cid, stage, subStage, info.BeginTs, info.EndTs, err)
            return err
        }
    }

    stageMgr, err := m.stageMgr.GetStage(stage)
    if err != nil {
        log.ErrorWithCtx(ctx, "SwitchWeddingStage failed to GetStage. opUid:%v, cid:%d, stage:%d, subStage:%d, beginTs:%d, endTs:%d, err:%v",
            opUid, cid, stage, subStage, info.BeginTs, info.EndTs, err)
        return err
    }

    isFreeWedding := info.ThemeType == uint32(pb.WeddingThemeType_WEDDING_THEME_TYPE_FREE)
    err = stageMgr.CheckBeforeChange(ctx, &stages.CheckBeforeChangeReq{
        OriginStage:    info.Stage,
        OriginSubStage: info.SubStage,
        IsFreeTheme:    isFreeWedding,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SwitchWeddingStage failed to CheckBeforeChange. opUid:%v, cid:%d, stage:%d, subStage:%d, beginTs:%d, endTs:%d, err:%v",
            opUid, cid, stage, subStage, info.BeginTs, info.EndTs, err)
        return err
    }

    if stage != uint32(pb.WeddingStage_WEDDING_STAGE_UNSPECIFIED) {
        // update stage
        info.Stage = stage
        info.SubStage = subStage
        info.StageBeginTs = now

        err = m.cache.SetWeddingInfo(ctx, cid, info)
        if err != nil {
            log.ErrorWithCtx(ctx, "SwitchWeddingStage failed to SetWeddingInfo. opUid:%v, cid:%d, stage:%d, subStage:%d, err:%v",
                opUid, cid, stage, subStage, err)
            return err
        }

        if isFreeWedding {
            // 免费婚礼，加入阶段队列
            err = m.cache.AddStageCidQueue(ctx, cid, now)
            if err != nil {
                log.WarnWithCtx(ctx, "SwitchWeddingStage failed to AddStageCidQueue. opUid:%v, cid:%d, stage:%d, subStage:%d, err:%v",
                    opUid, cid, stage, subStage, err)
            }
        }

    } else {
        // 结束婚礼
        err = m.endWeddingHandle(ctx, opUid, info)
        if err != nil {
            log.WarnWithCtx(ctx, "SwitchWeddingStage failed to endWeddingHandle. opUid:%v, cid:%d, stage:%d, subStage:%d, err:%v",
                opUid, cid, stage, subStage, err)
        }
    }

    // 通知婚礼阶段变更
    weddingInfo, err := m.WeddingStageChangeNotify(ctx, opUid, cid)
    if err != nil {
        log.WarnWithCtx(ctx, "SwitchWeddingStage failed to WeddingStageChangeNotify. opUid:%v, cid:%d, stage:%d, subStage:%d, err:%v",
            opUid, cid, stage, subStage, err)
    }

    if stage == uint32(pb.WeddingStage_WEDDING_STAGE_LOVE_DECLARATION) {
        go func() {
            newCtx, cancel := protocolgrpc.NewContextWithInfoTimeout(ctx, time.Second*10)
            defer cancel()
            <-time.After(100 * time.Millisecond)

            // 设置新人面对面站立
            m.setBrideGroomFaceToFace(newCtx, cid, info.BrideUid, info.GroomUid)
        }()
    }

    sceneId := stageMgr.GetSceneAfterChange()
    if sceneId > 0 {
        go func() {
            newCtx, cancel := protocolgrpc.NewContextWithInfoTimeout(ctx, time.Second*10)
            defer cancel()
            <-time.After(100 * time.Millisecond)

            // 购买者礼物推送  初始化礼物信息

            var highlightPresentId uint32
            var buyerUid uint32
            var buyerPresentId uint32
            levelConfig, err := m.getWeddingThemeLevelCfg(newCtx, weddingInfo.GetThemeCfg().GetThemeId(), weddingInfo.GetCurrLevel())
            if err != nil {
                log.ErrorWithCtx(newCtx, "SendSceneNotifyMsg failed to GetMyWeddingInfo. opUid:%d, err:%v", opUid, err)
            }

            if stage == uint32(channel_wedding_logic.WeddingStage_WEDDING_STAGE_HIGHLIGHT) {
                highlightPresentId = levelConfig.GetPresentId()
            } else {
                playWedding, err := m.acLayerMgr.GetWeddingPlanInfo(newCtx, info.PlanId)
                if err != nil {
                    log.ErrorWithCtx(newCtx, "SendSceneNotifyMsg failed to GetMyWeddingInfo. opUid:%d, err:%v", opUid, err)
                }

                log.DebugWithCtx(newCtx, "SendSceneNotifyMsg. opUid:%v, in:%+v, playWedding:%+v", opUid, info, playWedding)

                if playWedding.GetBuyerUid() > 0 {
                    record, found, err := m.store.GetReservePresentRecordByUidWeddingId(newCtx, playWedding.BuyerUid, info.WeddingId, info.BeginTs)
                    if err != nil {
                        log.ErrorWithCtx(newCtx, "SendSceneNotifyMsg failed to GetReservePresentRecordByUidWeddingId. opUid:%d, err:%v", opUid, err)
                    }

                    if !found || record.Status == store.AwardNotDone {
                        err = m.store.AddReservePresentRecord(newCtx, &store.ReservePresentRecord{
                            OrderId:   fmt.Sprintf("wedding_%d_%d_%d_%d", info.Cid, info.WeddingId, info.BrideUid, info.GroomUid),
                            Uid:       playWedding.GetBuyerUid(),
                            Cid:       info.Cid,
                            WeddingId: info.WeddingId,
                            GiftId:    playWedding.GetGiftId(),
                            Ctime:     time.Unix(info.BeginTs, 0),
                            Status:    store.AwardNotDone,
                            Mtime:     time.Now(),
                        })
                        if err != nil {
                            log.ErrorWithCtx(newCtx, "SendSceneNotifyMsg failed to AddReservePresentRecord. opUid:%d, err:%v", opUid, err)
                        }

                        //未送礼或者未触发 发送礼物弹窗信息 && 两人都在房间
                        online, err := m.acLayerMgr.CheckUsersInChannel(newCtx, []uint32{info.BrideUid, info.GroomUid}, info.Cid)
                        if err != nil {
                            log.ErrorWithCtx(newCtx, "SendSceneNotifyMsg failed to CheckUsersInChannel. opUid:%d, err:%v", opUid, err)
                        }
                        log.DebugWithCtx(newCtx, "SendSceneNotifyMsg. online:%v, buyerUid:%d, online:%d", online, buyerUid, online)

                        if online {
                            buyerUid = playWedding.GetBuyerUid()
                            buyerPresentId = playWedding.GetGiftId()
                        }
                    }
                }
            }

            log.InfoWithCtx(ctx, "SendSceneNotifyMsg. opUid:%d, sceneId:%d, levelConfig:%+v", opUid, sceneId, levelConfig)

            // 推送场景动画
            err = m.acLayerMgr.SendSceneNotifyMsg(newCtx, &anti_corruption_layer.SceneNotifyMsgReq{
                Cid:                cid,
                OpUid:              opUid,
                Level:              weddingInfo.GetCurrLevel(),
                ThemeId:            info.ThemeId,
                SceneId:            sceneId,
                HighlightPresentId: highlightPresentId,
                BuyerUid:           buyerUid,
                BuyerPresentId:     buyerPresentId,
            })
            if err != nil {
                log.WarnWithCtx(newCtx, "SwitchWeddingStage failed to DoAfterChange. opUid:%v, cid:%d, stage:%d, subStage:%d, err:%v",
                    opUid, cid, stage, subStage, err)
            }

            // 新郎新娘发送高光时刻礼物
            if highlightPresentId > 0 {
                _ = m.SendGroomBrideHighLightPresent(newCtx, info, weddingInfo, highlightPresentId)
            }
        }()
    }

    log.InfoWithCtx(ctx, "SwitchWeddingStage success. opUid:%v, cid:%d, stage:%d, subStage:%d", opUid, cid, stage, subStage)
    return nil
}

// 设置新人面对面站立
func (m *WeddingProcess) setBrideGroomFaceToFace(ctx context.Context, cid, brideUid, groomUid uint32) {
    var err error
    poseCfg := m.bc.GetWeddingPoseCfg()
    if poseCfg == nil {
        return
    }

    err = m.weddingPose.SetUserWeddingPose(ctx, cid, brideUid, poseCfg.BrideLovePose)
    if err != nil {
        log.WarnWithCtx(ctx, "setNewPeopleFaceToFace failed to SetUserWeddingPose. brideUid:%v, err:%v", brideUid, err)
    }

    err = m.weddingPose.SetUserWeddingPoseOrientation(ctx, cid, brideUid,
        uint32(virtual_image_logic.VirtualImageOrientation_VIRTUAL_IMAGE_ORIENTATION_LEFT))
    if err != nil {
        log.WarnWithCtx(ctx, "setNewPeopleFaceToFace failed to SetUserWeddingPoseOrientation. brideUid:%v, err:%v", brideUid, err)
    }

    err = m.weddingPose.SetUserWeddingPose(ctx, cid, groomUid, poseCfg.GroomLovePose)
    if err != nil {
        log.WarnWithCtx(ctx, "setNewPeopleFaceToFace failed to SetUserWeddingPose. brideUid:%v, err:%v", brideUid, err)
    }

    err = m.weddingPose.SetUserWeddingPoseOrientation(ctx, cid, groomUid,
        uint32(virtual_image_logic.VirtualImageOrientation_VIRTUAL_IMAGE_ORIENTATION_RIGHT))
    if err != nil {
        log.WarnWithCtx(ctx, "setNewPeopleFaceToFace failed to SetUserWeddingPoseOrientation. groomUid:%v, err:%v", groomUid, err)
    }

    log.DebugWithCtx(ctx, "setNewPeopleFaceToFace success. brideUid:%v, groomUid:%v", brideUid, groomUid)
    return
}

func (m *WeddingProcess) endWeddingHandle(ctx context.Context, opUid uint32, info *cache.WeddingInfo) error {
    // 结束婚礼
    err := m.store.Transaction(ctx, func(tx mysql.Txx) error {
        err := m.store.UpdateWeddingStatus(ctx, tx, info.WeddingId, store.WeddingStatusEnd)
        if err != nil {
            log.ErrorWithCtx(ctx, "endWeddingHandle failed to UpdateWeddingStatus. info:%+v, err:%v", info, err)
            return err
        }

        err = m.cache.DelWeddingInfo(ctx, info.Cid)
        if err != nil {
            log.ErrorWithCtx(ctx, "endWeddingHandle failed to DelWeddingInfo. info:%+v, err:%v", info, err)
            return err
        }

        err = m.cache.DelStageCidQueue(ctx, info.Cid)
        if err != nil {
            log.ErrorWithCtx(ctx, "endWeddingHandle failed to DelStageCidQueue. info:%+v, err:%v", info, err)
            return err
        }

        return nil
    })
    if err != nil {
        log.WarnWithCtx(ctx, "endWeddingHandle failed to DelWeddingInfo. info:%+v, err:%v", info, err)
    }

    _ = m.cache.DelUserWeddingInfo(ctx, info.BrideUid)
    _ = m.cache.DelUserWeddingInfo(ctx, info.GroomUid)

    if info.ThemeType == uint32(pb.WeddingThemeType_WEDDING_THEME_TYPE_PAY) {
        currentScore, _ := m.cache.GetHappiness(ctx, info.Cid, info.WeddingId)
        lv := m.bc.GetHappinessLevelByScore(currentScore)
        err = m.weddingRecords.AddWeddingRecord(ctx, opUid, &comm.WeddingRecordInfo{
            WeddingId:    info.WeddingId,
            PlanId:       info.PlanId,
            Cid:          info.Cid,
            ThemeId:      info.ThemeId,
            BrideUid:     info.BrideUid,
            GroomUid:     info.GroomUid,
            WeddingTime:  time.Now(),
            WeddingLv:    lv,
            HappinessVal: currentScore,
        })
        if err != nil {
            log.WarnWithCtx(ctx, "endWeddingHandle failed to AddWeddingRecord. info:%+v, err:%v", info, err)
        }
    }

    go func() {
        newCtx, cancel := protocolgrpc.NewContextWithInfoTimeout(ctx, time.Second*10)
        defer cancel()

        err = m.weddingPose.SetUserWeddingPose(newCtx, info.Cid, info.BrideUid, 0)
        if err != nil {
            log.WarnWithCtx(newCtx, "endWeddingHandle failed to SetUserWeddingPose. info:%+v, err:%v", info, err)
        }

        err = m.weddingPose.SetUserWeddingPose(newCtx, info.Cid, info.GroomUid, 0)
        if err != nil {
            log.WarnWithCtx(newCtx, "endWeddingHandle failed to SetUserWeddingPose. info:%+v, err:%v", info, err)
        }

        // 更新婚礼方案状态
        err = m.acLayerMgr.UpdateWeddingPlanStatus(newCtx, info.PlanId)
        if err != nil {
            log.WarnWithCtx(newCtx, "endWeddingHandle failed to EndWeddingChairGame. info:%+v, err:%v", info, err)
        }
    }()

    go func() {
        newCtx, cancel := protocolgrpc.NewContextWithInfoTimeout(ctx, time.Second*3)
        defer cancel()

        if info.ThemeType == uint32(pb.WeddingThemeType_WEDDING_THEME_TYPE_FREE) {
            err = m.SendAfterFreeWeddingImMsg(newCtx, info.BrideUid, info.GroomUid)
        }
    }()

    go func() {
        newCtx, cancel := protocolgrpc.NewContextWithInfoTimeout(ctx, time.Second*5)
        defer cancel()

        // 结束抢椅子游戏
        err = m.acLayerMgr.EndWeddingChairGame(newCtx, info.Cid, info.WeddingId)
        if err != nil {
            log.WarnWithCtx(newCtx, "endWeddingHandle failed to EndWeddingChairGame. info:%+v, err:%v", info, err)
        }
    }()

    go func() {
        newCtx, cancel := protocolgrpc.NewContextWithInfoTimeout(ctx, time.Second*5)
        defer cancel()
        err = m.acLayerMgr.EndGameKickOutMic(newCtx, info.Cid)
        if err != nil {
            log.WarnWithCtx(newCtx, "endWeddingHandle failed to EndGameKickOutMic. info:%+v, err:%v", info, err)
        }
    }()

    if info.ThemeType == uint32(pb.WeddingThemeType_WEDDING_THEME_TYPE_PAY) {
        go func() {
            newCtx, cancel := protocolgrpc.NewContextWithInfoTimeout(ctx, time.Second*5)
            defer cancel()

            err = m.weddingPresentVal.OnWeddingFinish(newCtx, info.Cid, info.WeddingId, info.ThemeId)
            if err != nil {
                log.WarnWithCtx(newCtx, "endWeddingHandle failed to OnWeddingFinish. info:%+v, err:%v", info, err)
            }
        }()

        go func() {
            newCtx, cancel := protocolgrpc.NewContextWithInfoTimeout(ctx, time.Second*5)
            defer cancel()

            // 发送婚礼结束调查问卷
            err = m.acLayerMgr.SendWeddingAfterSurvey(newCtx, info.WeddingId, info.Cid, opUid, info.BrideUid, info.GroomUid)
            if err != nil {
                log.WarnWithCtx(newCtx, "endWeddingHandle failed to SendWeddingAfterSurvey. info:%+v, err:%v", info, err)
            }
        }()
    }

    return nil
}

// WeddingStageChangeNotify 婚礼阶段变更通知
func (m *WeddingProcess) WeddingStageChangeNotify(ctx context.Context, opUid, cid uint32) (*pb.WeddingInfo, error) {
    if cid == 0 {
        log.ErrorWithCtx(ctx, "WeddingStageChangeNotify invalid params. opUid:%v, cid:%v", opUid, cid)
        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    info, err := m.GetChannelWeddingInfo(ctx, opUid, cid)
    if err != nil {
        log.ErrorWithCtx(ctx, "WeddingStageChangeNotify failed to GetChannelWeddingInfo. opUid:%v, info:%v, err:%v",
            opUid, info, err)
        return info, err
    }

    err = m.acLayerMgr.SendStageChangeMsg(ctx, opUid, info)
    if err != nil {
        log.ErrorWithCtx(ctx, "WeddingStageChangeNotify failed to SendStageChangeMsg. opUid:%v, info:%v, err:%v",
            opUid, info, err)
        return info, err
    }

    log.InfoWithCtx(ctx, "WeddingStageChangeNotify success. opUid:%v, info:%v", opUid, info)
    return info, nil
}

// SendAfterFreeWeddingImMsg 免费婚礼付费引流IM消息
func (m *WeddingProcess) SendAfterFreeWeddingImMsg(ctx context.Context, groomUid, brideUid uint32) error {
    if groomUid == 0 || brideUid == 0 || m.bc.GetAfterFreeWeddingImXml() == "" {
        return nil
    }

    err := m.acLayerMgr.SendIMCommonXmlMsg(ctx, groomUid, []uint32{brideUid}, "去预约", &imPB.IMCommonXmlMsg{
        XmlContent: m.bc.GetAfterFreeWeddingImXml(),
    }, uint32(imPB.IM_MSG_TYPE_XML_MSG_CENTER))
    if err != nil {
        log.ErrorWithCtx(ctx, "SendAfterFreeWeddingImMsg failed. fromUid:%d, toUid:%d,  err:%v", groomUid, brideUid, err)
        return err
    }
    log.InfoWithCtx(ctx, "SendAfterFreeWeddingImMsg success. fromUid:%d, toUid:%d", groomUid, brideUid)
    return nil
}
