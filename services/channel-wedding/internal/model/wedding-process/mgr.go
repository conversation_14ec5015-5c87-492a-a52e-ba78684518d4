package wedding_process

//go:generate quicksilver-cli test interface ../wedding-process
//go:generate mockgen -destination=../mocks/wedding-process.go -package=mocks golang.52tt.com/services/channel-wedding/internal/model/wedding-process IWeddingProcess

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/pkg/protocol"
    protocolgrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
    "golang.52tt.com/services/channel-wedding/internal/conf"
    anti_corruption_layer "golang.52tt.com/services/channel-wedding/internal/model/anti-corruption-layer"
    "golang.52tt.com/services/channel-wedding/internal/model/comm"
    group_photo "golang.52tt.com/services/channel-wedding/internal/model/group-photo"
    user_present_value "golang.52tt.com/services/channel-wedding/internal/model/user-present-value"
    wedding_pose "golang.52tt.com/services/channel-wedding/internal/model/wedding-pose"
    "golang.52tt.com/services/channel-wedding/internal/model/wedding-process/cache"
    "golang.52tt.com/services/channel-wedding/internal/model/wedding-process/stages"
    "golang.52tt.com/services/channel-wedding/internal/model/wedding-process/store"
    wedding_records "golang.52tt.com/services/channel-wedding/internal/model/wedding-records"
    "golang.52tt.com/services/tt-rev/esport/common/collection/transform"
    "google.golang.org/grpc/codes"
    "sync"
    "time"
)

type WeddingProcess struct {
    store          store.IStore
    cache          cache.ICache
    bc             conf.IBusinessConfManager
    acLayerMgr     anti_corruption_layer.IACLayer
    weddingPose    wedding_pose.IWeddingPose
    groupPhoto     group_photo.IGroupPhoto
    weddingRecords wedding_records.IWeddingRecords
    stageMgr       *stages.Stages

    weddingPresentVal user_present_value.IUserPresentVal

    timerD   *timer.Timer
    wg       sync.WaitGroup
    shutDown chan struct{}
}

func NewMgr(s mysql.DBx, cacheClient redis.Cmdable, bc conf.IBusinessConfManager,
    acLayerMgr anti_corruption_layer.IACLayer, weddingPose wedding_pose.IWeddingPose,
    groupPhoto group_photo.IGroupPhoto, weddingRecords wedding_records.IWeddingRecords,
    weddingPresentVal user_present_value.IUserPresentVal) (*WeddingProcess, error) {
    mysqlStore := store.NewStore(s)
    redisCli := cache.NewCache(cacheClient)
    stageMgr := stages.NewStages()

    m := &WeddingProcess{
        store:          mysqlStore,
        cache:          redisCli,
        stageMgr:       stageMgr,
        bc:             bc,
        shutDown:       make(chan struct{}),
        acLayerMgr:     acLayerMgr,
        weddingPose:    weddingPose,
        groupPhoto:     groupPhoto,
        weddingRecords: weddingRecords,

        weddingPresentVal: weddingPresentVal,
    }

    err := m.startTimer()
    if err != nil {
        log.Errorf("NewMgr startTimer err:%v", err)
        return m, err
    }

    return m, nil
}

func (m *WeddingProcess) Stop() {
    m.timerD.Stop()
    close(m.shutDown)
    m.wg.Wait()
    _ = m.cache.Close()
    _ = m.store.Close()
}

// GetChannelWeddingInfo 获取房间的婚礼信息
func (m *WeddingProcess) GetChannelWeddingInfo(ctx context.Context, uid, cid uint32) (*pb.WeddingInfo, error) {
    out := &pb.WeddingInfo{
        Cid: cid,
    }

    info, exist, err := m.cache.GetWeddingInfo(ctx, cid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelWeddingInfo failed to GetWeddingInfo. uid:%v, cid:%d, err:%v",
            uid, cid, err)
        return out, err
    }

    if !exist || info.Stage == uint32(pb.WeddingStage_WEDDING_STAGE_UNSPECIFIED) {
        return out, nil
    }

    currentScore, err := m.cache.GetHappiness(ctx, cid, info.WeddingId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelWeddingInfo failed to GetHappiness. cid:%d, err:%v", cid, err)
    }

    themeCfg, err := m.acLayerMgr.GetWeddingThemeCfg(ctx, info.ThemeId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelWeddingInfo failed to GetWeddingThemeCfg. cid:%d, err:%v", cid, err)
        return out, err
    }

    log.DebugWithCtx(ctx, "GetChannelWeddingInfo uid:%d, cid:%d, weddind:%d, themeId:%d, themeCfg:%+v", uid, cid, info.WeddingId, info.ThemeId, themeCfg)

    out.WeddingId = int64(info.WeddingId)
    out.StartTime = info.BeginTs
    out.EndTime = info.EndTs
    out.ReserveTime = info.ReserveTime
    out.Bride = &pb.WeddingCpMemInfo{Uid: info.BrideUid}
    out.Groom = &pb.WeddingCpMemInfo{Uid: info.GroomUid}

    isFreeWedding := info.ThemeType == uint32(pb.WeddingThemeType_WEDDING_THEME_TYPE_FREE)
    out.StageInfo = &pb.WeddingStageInfo{
        CurrStage:    info.Stage,
        SubStage:     info.SubStage,
        StageStartTs: info.StageBeginTs,
        StageCfgList: fillWeddingStageCfgListV2(themeCfg.GetStageCfgList()),
    }
    if isFreeWedding {
        out.StageInfo.StageEndTs = out.StageInfo.StageStartTs + int64(m.bc.GetFreeWeddingStageSec())
    }

    out.ThemeCfg = fillWeddingRoomThemeCfg(themeCfg, isFreeWedding)
    out.CurrLevel = m.bc.GetHappinessLevelByScore(currentScore)

    planInfo, err := m.acLayerMgr.GetWeddingPlanInfo(ctx, info.PlanId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelWeddingInfo failed to GetWeddingPlanInfo. cid:%d, err:%v", cid, err)
        return out, err
    }

    out.BridesmaidManList = append(planInfo.GetBridesmaidList(), planInfo.GetGroomsmanList()...)

    out.ChairGameEntry = !isFreeWedding && getWeddingStageCfg(info.Stage, themeCfg.GetStageCfgList()).GetChairGameEnable() // 免费婚礼不支持抢椅子游戏
    out.HappinessConfig = m.getHappinessConfigInfo()
    out.CurrHappinessValue = currentScore

    singleBoneCfg := m.bc.GetSingleBoneCfg()
    out.BoneCfg = &pb.WeddingBoneCfg{
        MaleBoneId:       singleBoneCfg.MaleBoneId,
        FemaleBoneId:     singleBoneCfg.FemaleBoneId,
        BaseMaleBoneId:   singleBoneCfg.BaseMaleBoneId,
        BaseFemaleBoneId: singleBoneCfg.BaseFemaleBoneId,
    }

    if info.Stage == uint32(channel_wedding_logic.WeddingStage_WEDDING_STAGE_WELCOME_GUEST) {
        out.WeddingMemorialVideo = &pb.WeddingMemorialVideo{
            ResourceUrl:  themeCfg.GetMemorialVideoResource().GetResourceUrl(),
            ResourceMd5:  themeCfg.GetMemorialVideoResource().GetResourceMd5(),
            UserPictures: planInfo.GetBigScreenList(),
        }
    }
    out.PlanId = info.PlanId

    return out, nil
}

//func (m *WeddingProcess) CheckIfChairGameEnable(stage uint32) bool {
//    cfg := m.bc.GetStageCfg(stage)
//    if cfg == nil {
//        return false
//    }
//
//    return cfg.ChairGameEnable
//}

func (m *WeddingProcess) getHappinessConfigInfo() *pb.HappinessConfigInfo {
    out := &pb.HappinessConfigInfo{}
    cfgList := m.bc.GetHappinessConf()

    for _, v := range cfgList {
        out.Config = append(out.Config, &pb.HappinessLevelInfo{
            Level:      v.Level,
            LevelValue: v.Score,
        })
    }
    return out
}
func fillWeddingStageCfgList(cfgList []*conf.StageCfg, isFreeWedding bool) []*pb.WeddingStageCfg {
    list := make([]*pb.WeddingStageCfg, 0, len(cfgList))
    for _, v := range cfgList {
        if isFreeWedding && v.NotSupportFree {
            continue
        }
        list = append(list, &pb.WeddingStageCfg{
            Stage:     v.StageId,
            StageName: v.Name,
        })
    }
    return list
}

func fillWeddingStageCfgListV2(cfgList []*channel_wedding_conf.ThemeStageCfg) []*pb.WeddingStageCfg {
    list := make([]*pb.WeddingStageCfg, 0, len(cfgList))
    for _, v := range cfgList {
        list = append(list, &pb.WeddingStageCfg{
            Stage:     v.GetStage(),
            StageName: v.GetStageName(),
        })
    }
    return list
}

func getWeddingStageCfg(stage uint32, cfgList []*channel_wedding_conf.ThemeStageCfg) *channel_wedding_conf.ThemeStageCfg {
    for _, v := range cfgList {
        if v.GetStage() == stage {
            return v
        }
    }
    return nil
}

func fillWeddingResource(resource *channel_wedding_conf.WeddingPreviewCfg) *pb.WeddingResource {
    res := resource.GetResource()
    return &pb.WeddingResource{
        ResourceUrl:  res.GetResourceUrl(),
        ResourceMd5:  res.GetResourceMd5(),
        CpBoneId:     resource.GetCpBoneId(),
        ItemIds:      resource.GetItemIds(),
        BaseCpBoneId: resource.GetBaseCpBoneId(),
    }
}

func fillWeddingLevelClothes(cfg *channel_wedding_conf.ThemeLevelCfg) *pb.WeddingLevelClothes {
    if cfg == nil {
        return nil
    }

    suitMap := cfg.GetGuestDressCfgMap()
    out := &pb.WeddingLevelClothes{
        Level:             cfg.Level,
        GroomClothes:      suitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_GROOM)].GetSuitCfg().GetItemIds(),
        BrideClothes:      suitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDE)].GetSuitCfg().GetItemIds(),
        GroomsmanClothes:  suitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES)].GetSuitCfg().GetItemIds(),
        BridesmaidClothes: suitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID)].GetSuitCfg().GetItemIds(),
    }

    return out
}

func fillWeddingRoomThemeCfg(cfg *channel_wedding_conf.ThemeCfg, isFreeWedding bool) *pb.WeddingRoomThemeCfg {
    if cfg == nil {
        return nil
    }

    out := &pb.WeddingRoomThemeCfg{
        ThemeId:                cfg.ThemeId,
        ThemeResource:          cfg.GetThemeRoomResource().GetResourceUrl(),
        ThemeResourceMd5:       cfg.GetThemeRoomResource().GetResourceMd5(),
        LevelClothesList:       make([]*pb.WeddingLevelClothes, 0),
        LevelBackgroundList:    make([]*pb.WeddingLevelBackgroundCfg, 0),
        SceneCfgList:           make([]*pb.WeddingSceneCfg, 0, len(cfg.SceneCfgList)),
        WeddingPreviewResource: fillWeddingResource(cfg.GetWeddingPreviewCfg()),
        //ChairResourceId:        cfg.ChairResourceId,
        IsFreeTheme: isFreeWedding,
    }

    for _, v := range cfg.GetThemeLevelCfgList() {
        log.Debugf("fillWeddingRoomThemeCfg level:%d, BackgroundMp4Url:%s, v.GetSpecialBackgroundPicture():%s", v.GetLevel(), v.GetRoomBackgroundMp4Url(), v.GetSpecialBackgroundPicture())
        out.LevelClothesList = append(out.LevelClothesList, fillWeddingLevelClothes(v))

        out.LevelBackgroundList = append(out.LevelBackgroundList, &pb.WeddingLevelBackgroundCfg{
            Level:                    v.GetLevel(),
            BackgroundPicture:        v.GetRoomBackgroundPicture(),
            BackgroundMp4Url:         v.GetRoomBackgroundMp4Url(),
            SpecialBackgroundPicture: v.GetSpecialBackgroundPicture(),
            SpecialBackgroundMp4Url:  v.GetSpecialBackgroundMp4Url(),
        })
    }

    for _, v := range cfg.SceneCfgList {
        //bonsList := make([]*pb.WeddingSceneBoneCfg, 0)
        //for _, bone := range v.BoneCfgList {
        //    bonsList = append(bonsList, &pb.WeddingSceneBoneCfg{
        //        Level:         bone.Level,
        //        SeqIndex:      bone.SeqIndex,
        //        AnimationName: bone.AnimationName,
        //        BoneId:        bone.BoneId,
        //        BaseBoneId:    bone.GetBaseBoneId(),
        //        CpItemIdList:  bone.GetCpItemIdList(),
        //    })
        //}

        out.SceneCfgList = append(out.SceneCfgList, &pb.WeddingSceneCfg{
            Scene:            v.GetScene(),
            SceneResource:    v.GetResource().GetResourceUrl(),
            SceneResourceMd5: v.GetResource().GetResourceMd5(),
            BoneCfgList:      fillSceneBoneCfgList(v.GetBoneCfgList()),
        })
    }

    if cfg.GetChairResCfg() != nil {
        res := cfg.GetChairResCfg()
        // 椅子资源
        out.ChairResCfg = &pb.ChairGameResourceCfg{
            ChairPic:            res.ChairPic,
            SittingPoseFemaleId: res.SittingPoseFemaleId,
            SittingPoseMaleId:   res.SittingPoseMaleId,
            StandbyFemaleId:     res.StandbyFemaleId,
            StandbyMaleId:       res.StandbyMaleId,
            FailFemaleIds:       res.FailFemaleIds,
            FailMaleIds:         res.FailMaleIds,
        }
    }

    return out
}

func fillSceneBoneCfgList(list []*channel_wedding_conf.WeddingSceneBoneCfg) []*pb.WeddingSceneBoneCfg {
    out := make([]*pb.WeddingSceneBoneCfg, 0, len(list))
    for _, v := range list {
        out = append(out, &pb.WeddingSceneBoneCfg{
            Level:            v.GetLevel(),
            SeqIndex:         v.GetSeqIndex(),
            AnimationName:    v.GetAnimationName(),
            BoneId:           v.GetBoneId(),
            BaseBoneId:       v.GetBaseBoneId(),
            CpItemIdList:     v.GetCpItemIdList(),
            MaleItemIdList:   v.GetMaleItemIdList(),
            FemaleItemIdList: v.GetFemaleItemIdList(),
        })
    }
    return out
}

func (m *WeddingProcess) AddWeddingBridesmaidMan(ctx context.Context, weddingId, cid uint32, uidList []uint32) error {
    //if weddingId == 0 || cid == 0 || len(uidList) == 0 {
    //    log.ErrorWithCtx(ctx, "AddWeddingBridesmaidMan invalid params. weddingId:%d, cid:%d, uidList:%v",
    //        weddingId, cid, uidList)
    //    return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    //}
    //
    //err := m.cache.AddWeddingGrooms(ctx, weddingId, uidList)
    //if err != nil {
    //    log.ErrorWithCtx(ctx, "AddWeddingBridesmaidMan failed to AddWeddingGrooms. weddingId:%d, cid:%d, uidList:%v, err:%v",
    //        weddingId, cid, uidList, err)
    //    return err
    //}
    //
    //log.InfoWithCtx(ctx, "AddWeddingBridesmaidMan success. weddingId:%d, cid:%d, uidList:%v", weddingId, cid, uidList)
    return nil
}

func (m *WeddingProcess) GetUserWeddingCid(ctx context.Context, uid uint32) (uint32, error) {
    info, exist, err := m.cache.GetUserWeddingInfo(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserWeddingCid failed to GetUserWeddingInfo. uid:%d, err:%v", uid, err)
        return 0, err
    }
    if !exist {
        return 0, nil
    }

    return info.Cid, nil
}

func (m *WeddingProcess) BatchGetWeddingSimpleInfo(ctx context.Context, cidList []uint32) ([]*pb.SimpleWeddingInfo, error) {
    pbList := make([]*pb.SimpleWeddingInfo, 0, len(cidList))
    if len(cidList) == 0 {
        return pbList, nil
    }

    list, err := m.cache.BatchGetWeddingInfo(ctx, cidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetWeddingSimpleInfo failed to BatchGetWeddingInfo. cidList:%v, err:%v", cidList, err)
        return pbList, err
    }

    for _, info := range list {
        pbInfo := &pb.SimpleWeddingInfo{
            Cid:       info.Cid,
            WeddingId: int64(info.WeddingId),
            StartTime: info.BeginTs,
            EndTime:   info.EndTs,
            CurrStage: info.Stage,
            PlanId:    info.PlanId,
        }
        pbList = append(pbList, pbInfo)
    }

    return pbList, nil
}

func (m *WeddingProcess) ReportWeddingBridesmaidMan(ctx context.Context, req *pb.ReportWeddingBridesmaidManReq) error {
    if len(req.GetBridesmaidUidList()) == 0 {
        return nil
    }
    info, exist, err := m.store.GetWeddingScheduleByPlanId(ctx, req.GetPlanId())
    if err != nil {
        log.ErrorWithCtx(ctx, "ReportWeddingBridesmaidMan failed to GetWeddingScheduleByPlanId. req:%+v, err:%v", req, err)
        return err
    }

    if !exist || info.Status == uint32(store.WeddingStatusEnd) {
        log.ErrorWithCtx(ctx, "ReportWeddingBridesmaidMan not found. req:%+v", req)
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "婚礼不存在")
    }

    maxLv := uint32(1)
    cid := uint32(0)
    bridesmaidManList := req.BridesmaidUidList
    weddingInfo := &pb.WeddingInfo{
        Cid:         info.Cid,
        WeddingId:   int64(info.Id),
        ReserveTime: info.CTime.Unix(),
        PlanId:      info.PlanId,
        CurrLevel:   maxLv,
    }
    if info.Status == uint32(store.WeddingStatusProcessing) {
        weddingInfo, err = m.GetChannelWeddingInfo(ctx, 0, req.GetCid())
        if err != nil {
            log.ErrorWithCtx(ctx, "ReportWeddingBridesmaidMan failed to GetChannelWeddingInfo. req:%+v, err:%v", req, err)
            return err
        }

        if weddingInfo.GetWeddingId() == int64(info.Id) {
            maxLv = weddingInfo.GetCurrLevel()
            cid = weddingInfo.GetCid()
            bridesmaidManList = weddingInfo.GetBridesmaidManList()
        }
    }

    themeCfg, err := m.acLayerMgr.GetWeddingThemeCfg(ctx, info.ThemeId)
    if err != nil {
        log.ErrorWithCtx(ctx, "ReportWeddingBridesmaidMan failed to GetWeddingThemeCfg. req:%+v, err:%v", req, err)
        return err
    }

    userMap, err := m.acLayerMgr.GetUserProfileMap(ctx, req.GetBridesmaidUidList(), false)
    if err != nil {
        log.ErrorWithCtx(ctx, "ReportWeddingBridesmaidMan failed to GetWeddingThemeCfg. req:%+v", req)
        return err
    }

    now := time.Now()
    awardList := make([]*store.AwardLog, 0)

    var remainDaysLimit uint32
    if info.ThemeType == uint32(pb.WeddingThemeType_WEDDING_THEME_TYPE_FREE) {
        remainDaysLimit = m.bc.GetBridesmaidSuitRemainDaysLimit()
    }

    var uid2SuitMap map[uint32]*channel_wedding_conf.GuestSuitCfg
    for lv := uint32(1); lv <= maxLv; lv++ {
        var lvCfg *channel_wedding_conf.ThemeLevelCfg
        for _, v := range themeCfg.GetThemeLevelCfgList() {
            if v.Level == lv {
                lvCfg = v
                break
            }
        }
        if lvCfg == nil {
            continue
        }

        uid2SuitMap = genUserSuitMap(userMap, lvCfg, 0, 0, req.GetBridesmaidUidList())
        for uid, suitCfg := range uid2SuitMap {
            if suitCfg == nil {
                continue
            }
            orderId := genSuitOrderId(info.Cid, int64(info.Id), uid, lv)
            r := fillSuitAwardLog(orderId, uid, info.Id, lv, info.ThemeType, remainDaysLimit, suitCfg, now)
            if r == nil {
                continue
            }
            awardList = append(awardList, r)
        }
    }

    err = m.store.Transaction(ctx, func(tx mysql.Txx) error {
        err = m.store.BatchInsertAwardLog(ctx, tx, info.CTime, awardList)
        if err != nil {
            log.ErrorWithCtx(ctx, "ReportWeddingBridesmaidMan failed to BatchInsertAwardLog. req:%+v, err:%v", req, err)
            return err
        }
        return nil
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "ReportWeddingBridesmaidMan failed to Transaction. req:%+v, err:%v", req, err)
        return err
    }

    asyncCtx, asyncCancel := protocolgrpc.NewContextWithInfoTimeout(ctx, 10*time.Second)
    go func(ctx context.Context) {
        defer asyncCancel()
        imCfg := m.bc.GetSuitSendImCfg()
        if imCfg != nil && imCfg.Content != "" {
            // 发送im消息
            err = m.acLayerMgr.SendIMMsgAsync(ctx, req.GetBuyerUid(), req.BridesmaidUidList, imCfg.Content, imCfg.Highlight, imCfg.JumpUrl)
            if err != nil {
                log.WarnWithCtx(ctx, "ReportWeddingBridesmaidMan failed to SendIMMsgAsync. opUid:%v, req:%+v, err:%v", req.GetBuyerUid(), req, err)
            }
        }

        // 伴郎伴娘推送
        err = m.acLayerMgr.SendWeddingBridesmaidUpdateMsg(ctx, cid, bridesmaidManList)
        if err != nil {
            log.ErrorWithCtx(ctx, "ReportWeddingBridesmaidMan failed to SendWeddingBridesmaidUpdateMsg. req:%+v, err:%v", req, err)
        }

        // 穿上服装
        uid := req.GetBridesmaidUidList()[0]
        upgradeClothes := fillUpgradeSuitInfo(uid, remainDaysLimit, uid2SuitMap[uid], genSuitOrderId(weddingInfo.GetCid(), weddingInfo.GetWeddingId(), uid, maxLv), true)
        weddingTime := time.Unix(weddingInfo.GetReserveTime(), 0)
        err = m.sendUserSuitWithUse(ctx, uid, upgradeClothes, weddingTime, nil)
        if err != nil {
            log.ErrorWithCtx(ctx, "ReportWeddingBridesmaidMan failed to sendUserSuitWithUse, req: %+v, err: %v", req, err)
        }
    }(asyncCtx)

    log.InfoWithCtx(ctx, "ReportWeddingBridesmaidMan success. req:%+v", req)
    return nil
}

func fillUpgradeSuitInfo(uid, remainDaysLimit uint32, suit *channel_wedding_conf.GuestSuitCfg, orderId string, inuse bool) *comm.UpgradeSuitInfo {
    if suit == nil {
        return &comm.UpgradeSuitInfo{}
    }

    out := &comm.UpgradeSuitInfo{
        Uid:             uid,
        Name:            suit.GetName(),
        ClothesList:     suit.GetItemIds(),
        Icon:            suit.GetIcon(),
        InUse:           inuse,
        Duration:        suit.GetDurationSec(),
        OrderId:         orderId,
        RemainDaysLimit: remainDaysLimit,
    }
    return out
}

func genSuitOrderId(cid uint32, weddingId int64, uid, lv uint32) string {
    return fmt.Sprintf("wedding_%d_%d_%d_%d", cid, weddingId, uid, lv)
}

func (m *WeddingProcess) getWeddingThemeLevelCfg(ctx context.Context, themeId, lv uint32) (*channel_wedding_conf.ThemeLevelCfg, error) {
    themeCfg, err := m.acLayerMgr.GetWeddingThemeCfg(ctx, themeId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelWeddingInfo failed to GetWeddingThemeCfg. themeId:%d, lv:%v, err:%v", themeId, lv, err)
        return nil, err
    }

    for _, v := range themeCfg.GetThemeLevelCfgList() {
        if v.Level == lv {
            return v, nil
        }
    }

    return nil, nil
}

func (m *WeddingProcess) GetWeddingScheduleList(ctx context.Context, req *pb.GetWeddingScheduleListReq) ([]*pb.WeddingSchedule, error) {
    pbList := make([]*pb.WeddingSchedule, 0)
    themeTy := uint32(pb.WeddingThemeType_WEDDING_THEME_TYPE_PAY)
    minTime := time.Unix(req.GetMinBeginTime(), 0)
    maxTime := time.Unix(req.GetMaxBeginTime(), 0)

    list, err := m.store.GetWeddingScheduleList(ctx, req.GetUid(), themeTy, minTime, maxTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingScheduleList failed. req:%+v, err:%v", req, err)
        return pbList, err
    }

    for _, v := range list {
        pbList = append(pbList, &pb.WeddingSchedule{
            PlanId:    v.Id,
            WeddingId: v.Id,
            StartTime: v.BeginTime.Unix(),
            EndTime:   v.EndTime.Unix(),
            GroomUid:  v.GroomUid,
            BrideUid:  v.BrideUid,
        })
    }

    return pbList, nil
}

func (m *WeddingProcess) PageGetGoingWeddingList(ctx context.Context, in *pb.PageGetGoingWeddingListReq) (out *pb.PageGetGoingWeddingListResp, err error) {
    out = &pb.PageGetGoingWeddingListResp{}

    limit := int64(in.PageSize)
    offset := int64((in.PageNum - 1) * in.PageSize)
    goingList, err := m.store.PageGetGoingWeddingList(ctx, limit+1, offset)
    if err != nil {
        log.ErrorWithCtx(ctx, "PageGetGoingWeddingList err: %v", err)
        return
    }
    if len(goingList) > int(limit) {
        out.HasMore = true
        goingList = goingList[:limit]
    }
    if len(goingList) == 0 {
        return
    }

    // 拿幸福值
    happinessKeys := transform.Map(goingList, func(item *store.WeddingSchedule) cache.HappinessKey {
        return cache.HappinessKey{
            ChannelId: item.Cid,
            WeddingId: item.Id,
        }
    })
    happinessVals, err := m.cache.BatGetHappiness(ctx, happinessKeys)
    if err != nil {
        log.ErrorWithCtx(ctx, "PageGetGoingWeddingList BatGetHappiness err: %v", err)
        return
    }

    // 拿当前阶段
    infoKeys := transform.Map(goingList, func(item *store.WeddingSchedule) uint32 {
        return item.Cid
    })
    infoVals, err := m.cache.BatchGetWeddingInfo(ctx, infoKeys)
    if err != nil {
        log.ErrorWithCtx(ctx, "PageGetGoingWeddingList BatchGetWeddingInfo err: %v", err)
        return
    }
    infoMap := make(map[uint32]*cache.WeddingInfo, len(infoVals))
    for _, v := range infoVals {
        infoMap[v.Cid] = v
    }

    // 组装返回
    out.WeddingList = make([]*pb.GoingWeddingInfo, 0, len(goingList))
    for i, item := range goingList {
        info, ok := infoMap[item.Cid]
        if !ok {
            continue
        }
        out.WeddingList = append(out.WeddingList, &pb.GoingWeddingInfo{
            WeddingId:     item.Id,
            WeddingPlanId: item.PlanId,
            ChannelId:     item.Cid,
            ThemeId:       item.ThemeId,
            ThemeType:     item.ThemeType,
            CurrLevel:     m.bc.GetHappinessLevelByScore(happinessVals[i]),
            CurrStage:     info.Stage,
        })
    }
    return
}

func (m *WeddingProcess) GetWeddingHighLightPresent(ctx context.Context, request *pb.GetWeddingHighLightPresentRequest) (*pb.GetWeddingHighLightPresentResponse, error) {
    out := &pb.GetWeddingHighLightPresentResponse{
        Toast: "很遗憾，本场的婚礼礼物已经发放完毕~",
    }
    svrInfo, ok := protocolgrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetWeddingHighLightPresent failed to get service info")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    info, err := m.GetChannelWeddingInfo(ctx, svrInfo.UserID, request.GetCid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingHighLightPresent failed to GetWeddingInfo. cid:%d, err:%v",
            request, err)
        return out, err
    }

    sendCount, err := m.cache.GetHighLightPresentCount(ctx, request.GetCid(), request.GetWeddingId())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingHighLightPresent failed to GetHighLightPresentCount. uid:%d, cid:%d, err:%v",
            svrInfo.UserID, request.GetCid(), info.GetWeddingId(), err)
        return out, nil
    }

    if sendCount >= m.bc.GetMaxHighLightPresentCount() {
        return out, nil
    }

    currentLv := info.GetCurrLevel()
    themeCfg, err := m.acLayerMgr.GetWeddingThemeCfg(ctx, info.GetThemeCfg().GetThemeId())
    if currentLv == 0 {
        log.ErrorWithCtx(ctx, "GetWeddingHighLightPresent failed to GetWeddingThemeCfg. themeId:%d, lv:%d",
            info.GetThemeCfg().GetThemeId(), currentLv)
        return out, nil
    }

    var presentId uint32
    for _, cfg := range themeCfg.GetThemeLevelCfgList() {
        if cfg.GetLevel() == currentLv {
            presentId = cfg.GetPresentId()
        }
    }

    if presentId == 0 {
        log.ErrorWithCtx(ctx, "GetWeddingHighLightPresent failed to GetWeddingThemeCfg. themeId:%d, lv:%d",
            info.GetThemeCfg().GetThemeId(), currentLv)
        return out, nil
    }

    err = m.acLayerMgr.SendPresentToUser(ctx, svrInfo.UserID, request.GetCid(), presentId, info.GetWeddingId())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingHighLightPresent failed to SendPresentToUser. uid:%d, cid:%d, presentId:%d, err:%v",
            svrInfo.UserID, request.GetCid(), presentId, info.GetWeddingId(), err)
        return out, nil
    }

    userMap, err := m.acLayerMgr.GetUserProfileMap(ctx, []uint32{info.GetGroom().GetUid(), info.GetBride().GetUid()}, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingHighLightPresent failed to GetUserProfileMap. uid:%d, cid:%d, presentId:%d, weddingId:%d",
            svrInfo.UserID, request.GetCid(), presentId, info.GetWeddingId())
        return out, err
    }

    content := fmt.Sprintf("恭喜～在%s和%s的婚礼中抢到婚礼礼物，礼物已放进你的背包，去送出吧~", userMap[info.GetGroom().GetUid()].GetNickname(), userMap[info.GetBride().GetUid()].GetNickname())

    _ = m.acLayerMgr.SimpleSendTTAssistantText(ctx, svrInfo.UserID, content, "", "")
    out.Toast = "婚礼礼物已放进你的背包，去送出吧~"

    _ = m.cache.IncrHighLightPresentCount(ctx, info.Cid, uint32(info.WeddingId))
    return out, nil
}

func (m *WeddingProcess) SendGroomBrideHighLightPresent(ctx context.Context, info *cache.WeddingInfo, wedding *pb.WeddingInfo, presentId uint32) error {
    if presentId == 0 {
        log.WarnWithCtx(ctx, "SendGroomBrideHighLightPresent presentId is 0. weddingId:%d", info.WeddingId)
        return nil
    }
    err := m.acLayerMgr.SendPresentToUser(ctx, info.GroomUid, info.Cid, presentId, wedding.GetWeddingId())
    if err != nil {
        log.ErrorWithCtx(ctx, "SendGroomBrideHighLightPresent failed to SendPresentToUser. uid:%d, err:%v", info.GroomUid, err)
        return err
    }

    err = m.acLayerMgr.SendPresentToUser(ctx, info.BrideUid, info.Cid, presentId, wedding.GetWeddingId())
    if err != nil {
        log.ErrorWithCtx(ctx, "SendGroomBrideHighLightPresent failed to SendPresentToUser. uid:%d, err:%v", info.BrideUid, err)
        return err
    }

    userMap, err := m.acLayerMgr.GetUserProfileMap(ctx, []uint32{info.GroomUid, info.BrideUid}, false)
    if err != nil {
        return err
    }

    content := fmt.Sprintf("恭喜～在%s和%s的婚礼中抢到婚礼礼物，礼物已放进你的背包，去送出吧~", userMap[info.GroomUid].GetNickname(), userMap[info.BrideUid].GetNickname())
    _ = m.acLayerMgr.SimpleSendTTAssistantText(ctx, info.GroomUid, content, "", "")
    _ = m.acLayerMgr.SimpleSendTTAssistantText(ctx, info.BrideUid, content, "", "")
    log.InfoWithCtx(ctx, "SendGroomBrideHighLightPresent.success   weddingId:%+v", info)
    return nil
}

func (m *WeddingProcess) SendWeddingReservePresent(ctx context.Context, request *pb.SendWeddingReservePresentReq) (*pb.SendWeddingReservePresentResp, error) {
    out := &pb.SendWeddingReservePresentResp{}
    info, exist, err := m.cache.GetWeddingInfo(ctx, request.GetCid())
    if err != nil {
        log.ErrorWithCtx(ctx, "SendWeddingReservePresent failed to GetWeddingInfo. uid:%v, cid:%d, err:%v",
            request.GetCid(), request.GetCid(), err)
        return out, err
    }

    if !exist {
        log.ErrorWithCtx(ctx, "SendWeddingReservePresent failed to GetWeddingInfo. uid:%v, cid:%d, err:%v",
            request.GetCid(), request.GetCid(), err)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "婚礼信息不存在")
    }

    record, _, err := m.store.GetReservePresentRecordByUidWeddingId(ctx, request.GetUid(), info.WeddingId, info.BeginTs)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendWeddingReservePresent failed to GetReservePresentRecordByUidWeddingId. uid:%v, cid:%d, err:%v",
            request.GetUid(), request.GetCid(), err)
        return out, err
    }

    if record.Status == uint32(store.AwardDone) {
        log.ErrorWithCtx(ctx, "SendWeddingReservePresent failed to GetReservePresentRecordByUidWeddingId. request:%v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "已领取")
    }

    var toUid uint32
    if record.Uid == info.BrideUid {
        toUid = info.GroomUid
    } else {
        toUid = info.BrideUid
    }

    // 送礼
    sendGiftTIme := time.Now().Unix()
    err = m.acLayerMgr.SendCommonShelfPresent(ctx, &anti_corruption_layer.SendPresentReq{
        FromUid:   record.Uid,
        FromCid:   record.Cid,
        ToUid:     toUid,
        GiftId:    record.GiftId,
        Amount:    1,
        OrderId:   record.OrderId,
        DealToken: "",
        AwardTs:   sendGiftTIme,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SendWeddingReservePresent failed to SendCommonShelfPresent. uid:%v, cid:%d, err:%v",
            request.GetUid(), request.GetCid(), err)
        return out, err
    }

    err = m.store.SetReservePresentAwardDone(ctx, record.OrderId, info.BeginTs, sendGiftTIme)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendWeddingReservePresent failed to SetReservePresentAwardDone. uid:%v, cid:%d, err:%v", record)
    }

    log.InfoWithCtx(ctx, "SendWeddingReservePresent.success   record:%+v", record)
    return out, nil
}
