package wedding_process

import (
    "bytes"
    "context"
    "fmt"
    "github.com/golang/protobuf/proto"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/clients/account"
    push "golang.52tt.com/clients/push-notification/v2"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    pushPb "golang.52tt.com/protocol/app/push"
    pb "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
    push_notification "golang.52tt.com/protocol/services/push-notification/v2"
    pbCommon "golang.52tt.com/protocol/services/rcmd/common"
    "golang.52tt.com/services/channel-wedding/internal/conf"
    "golang.52tt.com/services/channel-wedding/internal/model/comm"
    "golang.52tt.com/services/channel-wedding/internal/model/wedding-process/store"
    "text/template"
    "time"
)

// AddHappiness wedding game happiness
func (m *WeddingProcess) AddHappiness(ctx context.Context, weddingInfo *pb.WeddingInfo, score uint32) error {
    channelId := weddingInfo.GetCid()
    weddingId := weddingInfo.GetWeddingId()

    newScore, err := m.cache.AddHappiness(ctx, channelId, uint32(weddingId), score)
    if err != nil {
        log.ErrorWithCtx(ctx, "AddHappiness fail. channelId:%d, weddingId:%d, score:%d, err:%v", channelId, weddingId, score, err)
        return err
    }

    //notify happiness change
    var nextLevelTipsValue uint32
    currentCfg := m.bc.GetHappinessConfByScore(newScore)
    if currentCfg != nil && currentCfg.NextLevelTipsValue != 0 {
        nextLevelTipsValue = currentCfg.NextLevelTipsValue
    }

    _ = m.acLayerMgr.SendHappinessChangeMsg(ctx, 0, channelId, newScore, nextLevelTipsValue)

    //notify if happiness upgrade
    preScore := newScore - score
    if m.bc.GetHappinessUpgrade(preScore, newScore) {
        // 幸福值等级升级
        err = m.happinessLevelUpgrade(ctx, weddingInfo, newScore)
        if err != nil {
            log.ErrorWithCtx(ctx, "AddHappiness happinessLevelUpgrade fail. channelId:%d, weddingId:%d, score:%d, err:%v", channelId, weddingId, newScore, err)
            return err
        }
        log.InfoWithCtx(ctx, "AddHappiness level upgrade. channelId:%d, weddingId:%d, preScore:%d, newScore:%d", channelId, weddingId, preScore, newScore)
    }

    // 额外服装时长奖励
    err = m.happinessExtraSuitAward(ctx, weddingInfo, currentCfg, preScore, newScore)
    if err != nil {
        log.WarnWithCtx(ctx, "AddHappiness happinessExtraSuitAward fail. channelId:%d, weddingId:%d, score:%d, err:%v", channelId, weddingId, score, err)
    }

    log.InfoWithCtx(ctx, "AddHappiness success. channelId:%d, weddingId:%d, score:%d, newScore:%d", channelId, weddingId, score, newScore)
    return nil
}

// happinessLevelUpgrade 升级幸福值等级
func (m *WeddingProcess) happinessLevelUpgrade(ctx context.Context, weddingInfo *pb.WeddingInfo, newScore uint32) error {
    channelId, weddingId := weddingInfo.GetCid(), weddingInfo.GetWeddingId()
    newLevel := m.bc.GetHappinessLevelByScore(newScore)
    upgradeCloths, err := m.genUpgradeClothes(ctx, weddingInfo, newLevel)
    if err != nil {
        log.ErrorWithCtx(ctx, "happinessLevelUpgrade fail to genUpgradeClothes. channelId:%d, weddingId:%d, score:%d, err:%v",
            channelId, weddingId, newScore, err)
        return err
    }

    // 写入奖励记录
    err = m.addUpgradeAwardLog(ctx, uint32(weddingId), newLevel, time.Unix(weddingInfo.GetReserveTime(), 0), upgradeCloths)
    if err != nil {
        log.ErrorWithCtx(ctx, "happinessLevelUpgrade fail to addUpgradeAwardLog. channelId:%d, weddingId:%d, score:%d, err:%v",
            channelId, weddingId, newScore, err)
        return err
    }

    uidList := make([]uint32, 0)
    uidToUseClothes := make(map[uint32][]uint32)
    for uid, upgrade := range upgradeCloths {
        uidList = append(uidList, uid)
        if upgrade.InUse {
            // 升级换装数据
            uidToUseClothes[uid] = upgrade.ClothesList
        }
    }

    poseList, err := m.weddingPose.BatchGetUserWeddingPose(ctx, channelId, uidList)
    if err != nil {
        log.WarnWithCtx(ctx, "happinessLevelUpgrade fail to BatchGetUserWeddingPose. channelId:%d, weddingId:%d, score:%d, err:%v",
            channelId, weddingId, newScore, err)
    }

    err = m.acLayerMgr.SendLevelChangeMsg(ctx, weddingInfo, newLevel, uidToUseClothes, poseList)
    if err != nil {
        log.WarnWithCtx(ctx, "happinessLevelUpgrade fail to SendLevelChangeMsg. channelId:%d, weddingId:%d, score:%d, err:%v",
            channelId, weddingId, newScore, err)
    }

    err = m.SendUpgradeClothesMsg(ctx, weddingInfo, upgradeCloths)
    if err != nil {
        log.ErrorWithCtx(ctx, "happinessLevelUpgrade fail. channelId:%d, weddingId:%d, score:%d, err:%v", channelId, weddingId, newScore, err)
        return err
    }

    // 发送全服公告
    newConfig := m.bc.GetHappinessConfByScore(newScore)
    if newConfig != nil && newConfig.BreakingNewsId != 0 {
        _ = m.acLayerMgr.PushBreakingNews(ctx, channelId, newConfig.BreakingNewsId, weddingInfo)
    }

    // 发送TT助手
    go func() {
        for _, uid := range uidList {
            text := fmt.Sprintf("婚礼升级，你的%s服装升级为新套装，可以在“我的形象-“我的”查看~", m.getWeddingRoleStr(context.Background(), uid, weddingInfo))
            _ = m.acLayerMgr.SimpleSendTTAssistantText(context.Background(), uid, text, "", "")
        }
    }()

    // 发送服装升级弹窗
    go func() {
        ctx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Duration(m.bc.GetUpgradeClothesPopupDelay()+5)*time.Second)
        defer cancel()
        time.Sleep(time.Duration(m.bc.GetUpgradeClothesPopupDelay()) * time.Second)
        nowWeddingInfo, err := m.GetChannelWeddingInfo(ctx, 0, channelId)
        if nowWeddingInfo.WeddingId != weddingId {
            return
        }

        err = m.sendUpgradeClothesPopup(context.Background(), weddingInfo, newLevel)
        if err != nil {
            log.ErrorWithCtx(ctx, "happinessLevelUpgrade fail to sendUpgradeClothesPopup. channelId:%d, weddingId:%d, score:%d, err:%v",
                channelId, weddingId, newScore, err)
        }
    }()

    // 发送房间公屏消息
    content := "恭喜~ 幸福值升级，本场婚礼升级至下一等级啦！"
    if newLevel == 3 {
        content = "恭喜~ 幸福值升级，本场婚礼升级至最高等级啦！"
    }
    _ = m.acLayerMgr.SendChannelMsg(ctx, 0, channelId, content)

    return nil
}

func (m *WeddingProcess) getWeddingRoleStr(ctx context.Context, uid uint32, weddingInfo *pb.WeddingInfo) string {
    if uid == weddingInfo.GetGroom().GetUid() {
        return "新郎"
    } else if uid == weddingInfo.GetBride().GetUid() {
        return "新娘"
    } else {
        userprofile, err := m.acLayerMgr.GetUserProfile(ctx, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "getWeddingRoleStr GetUserProfile err(%v)", err)
            return ""
        }
        if userprofile.GetSex() == uint32(account.Female) {
            return "伴娘"
        }
        return "伴郎"
    }
    return ""
}

type UpgradeClothesPopupInfo struct {
    WeddingRole string
    ClothesImg  string
}

func (m *WeddingProcess) sendUpgradeClothesPopup(ctx context.Context, weddingInfo *pb.WeddingInfo, newLevel uint32) error {
    // 待确认是否需要根据等级变化
    themeCfgResp, err := m.acLayerMgr.GetWeddingThemeCfg(ctx, weddingInfo.GetThemeCfg().GetThemeId())
    if err != nil {
        log.ErrorWithCtx(ctx, "sendUpgradeClothesPopup GetWeddingThemeCfg, themeId: %d, err: %v", weddingInfo.GetThemeCfg().GetThemeId(), err)
        return err
    }
    GroomClothesImg := ""
    BrideClothesImg := ""
    GroomsmanClothesImg := ""
    BridesmaidClothesImg := ""
    for _, item := range themeCfgResp.GetThemeLevelCfgList() {
        if item.Level == newLevel {
            GroomClothesImg = item.GetGuestDressCfgMap()[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_GROOM)].GetClothesUpgradePopupPng()
            BrideClothesImg = item.GetGuestDressCfgMap()[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDE)].GetClothesUpgradePopupPng()
            GroomsmanClothesImg = item.GetGuestDressCfgMap()[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES)].GetClothesUpgradePopupPng()
            BridesmaidClothesImg = item.GetGuestDressCfgMap()[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID)].GetClothesUpgradePopupPng()
            break
        }
    }

    uidList := weddingInfo.GetBridesmaidManList()
    uidList = append(uidList, weddingInfo.GetGroom().GetUid(), weddingInfo.GetBride().GetUid())
    userChannelIdResp, err := m.acLayerMgr.BatchGetUserChannelId(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "sendUpgradeClothesPopup BatchGetUserChannelId err(%v)", err)
        return err
    }

    // 新郎服装升级弹窗
    xmlTpl := m.bc.GetUpgradeClothesPopupXml()
    data := &UpgradeClothesPopupInfo{WeddingRole: "新郎", ClothesImg: GroomClothesImg}
    xmlContent, err := renderTmpl(ctx, "GroomClothesPopup", xmlTpl, data)
    if err != nil {
        log.ErrorWithCtx(ctx, "sendUpgradeClothesPopup renderTmpl err(%v)", err)
        return err
    }
    upgradeClothesMsg := &pushPb.CommonRichTextPopup{
        Content:       xmlContent,
        AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL),
        Scene:         "channel_wedding",
    }
    if userChannelIdResp[weddingInfo.GetGroom().GetUid()] == weddingInfo.GetCid() {
        pushMsg := buildNotification(upgradeClothesMsg, uint32(pushPb.PushMessage_COMMON_RICH_TEXT_POPUP), "婚礼升级弹窗")
        err = m.acLayerMgr.PushToUsers(ctx, []uint32{weddingInfo.GetGroom().GetUid()}, pushMsg)
        if err != nil {
            log.ErrorWithCtx(ctx, "sendUpgradeClothesPopup fail to PushToUser, groom: %d, err: %+v",
                weddingInfo.GetGroom().GetUid(), err)
            return err
        }
    }

    // 新娘服装升级弹窗
    data = &UpgradeClothesPopupInfo{WeddingRole: "新娘", ClothesImg: BrideClothesImg}
    xmlContent, err = renderTmpl(ctx, "BrideClothesPopup", xmlTpl, data)
    if err != nil {
        log.ErrorWithCtx(ctx, "sendUpgradeClothesPopup renderTmpl err(%v)", err)
        return err
    }
    upgradeClothesMsg.Content = xmlContent
    if userChannelIdResp[weddingInfo.GetBride().GetUid()] == weddingInfo.GetCid() {
        pushMsg2 := buildNotification(upgradeClothesMsg, uint32(pushPb.PushMessage_COMMON_RICH_TEXT_POPUP), "婚礼升级弹窗")
        err = m.acLayerMgr.PushToUsers(ctx, []uint32{weddingInfo.GetBride().GetUid()}, pushMsg2)
        if err != nil {
            log.ErrorWithCtx(ctx, "sendUpgradeClothesPopup fail to PushToUser, bride: %d, err: %+v")
            return err
        }
    }

    userprofile, err := m.acLayerMgr.GetUserProfileMap(ctx, weddingInfo.GetBridesmaidManList(), false)
    if err != nil {
        log.ErrorWithCtx(ctx, "sendUpgradeClothesPopup fail to GetUserProfileMap, uidList: %d, err: %+v", weddingInfo.GetBridesmaidManList(), err)
        return err
    }

    // 发送给伴郎伴娘
    for _, uid := range weddingInfo.GetBridesmaidManList() {
        if userChannelIdResp[uid] != weddingInfo.GetCid() {
            log.WarnWithCtx(ctx, "sendUpgradeClothesPopup uid:%d not in channel", uid)
            continue
        }

        weddingRole := "伴郎"
        clothesImg := GroomsmanClothesImg
        if userprofile[uid].GetSex() == 0 {
            clothesImg = BridesmaidClothesImg
            weddingRole = "伴娘"
        }

        data = &UpgradeClothesPopupInfo{WeddingRole: weddingRole, ClothesImg: clothesImg}
        xmlContent, err = renderTmpl(ctx, "BridesmaidClothesPopup", xmlTpl, data)
        if err != nil {
            log.ErrorWithCtx(ctx, "sendUpgradeClothesPopup renderTmpl, uid: %d, err: %v", uid, err)
            return err
        }
        upgradeClothesMsg.Content = xmlContent
        pushMsg3 := buildNotification(upgradeClothesMsg, uint32(pushPb.PushMessage_COMMON_RICH_TEXT_POPUP), "婚礼升级弹窗")
        err = m.acLayerMgr.PushToUsers(ctx, []uint32{uid}, pushMsg3)
        if err != nil {
            log.ErrorWithCtx(ctx, "sendUpgradeClothesPopup fail to PushToUser, uid: %d, err: %+v", uid, err)
            return err
        }
        log.InfoWithCtx(ctx, "sendUpgradeClothesPopup uid:%d", uid)
    }
    log.DebugWithCtx(ctx, "sendUpgradeClothesPopup success, weddingId: %d, xmlContent: %s", weddingInfo.GetWeddingId(), xmlContent)

    return nil
}

func renderTmpl(ctx context.Context, name, tpl string, data interface{}) (string, error) {
    var rsBuf bytes.Buffer
    tplI := template.Must(template.New(name).Parse(tpl))
    err := tplI.Execute(&rsBuf, data)
    if err != nil {
        log.ErrorWithCtx(ctx, "RenderCouponInfo", "err", err)
        return "", err
    }

    return rsBuf.String(), nil
}

func buildNotification(inputMsg proto.Message, cmd uint32, labelString string) *push_notification.CompositiveNotification {
    msg, _ := proto.Marshal(inputMsg)

    pushMessage := &pushPb.PushMessage{
        Cmd:     cmd,
        Content: msg,
    }
    pushMessageBytes, _ := proto.Marshal(pushMessage)

    return &push_notification.CompositiveNotification{
        Sequence:           uint32(time.Now().Unix()),
        TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
        TerminalTypePolicy: push.DefaultPolicy,
        AppId:              0,
        ProxyNotification: &push_notification.ProxyNotification{
            Type:      uint32(push_notification.ProxyNotification_PUSH),
            Payload:   pushMessageBytes,
            PushLabel: labelString,
        },
    }
}

func (m *WeddingProcess) addUpgradeAwardLog(ctx context.Context, weddingId, lv uint32, weddingReserveTime time.Time, upgradeCloths map[uint32]*comm.UpgradeSuitInfo) error {
    now := time.Now()
    awardList := make([]*store.AwardLog, 0)
    for uid, suitInfo := range upgradeCloths {
        if suitInfo.OrderId == "" || len(suitInfo.ClothesList) == 0 {
            log.WarnWithCtx(ctx, "addUpgradeAwardLog suitInfo.ClothesList is empty. weddingId:%d, lv:%d, uid:%d", weddingId, lv, uid)
            continue
        }
        award := &store.AwardLog{
            OrderId:          suitInfo.OrderId,
            Uid:              uid,
            WeddingId:        weddingId,
            WeddingLv:        lv,
            SuitName:         suitInfo.Name,
            SuitIcon:         suitInfo.Icon,
            AwardDurationSec: suitInfo.Duration,
            AwardTime:        now,
            ThemeType:        uint32(pb.WeddingThemeType_WEDDING_THEME_TYPE_PAY),
            RemainDaysLimit:  suitInfo.RemainDaysLimit,
        }
        award.SetSuitItemList(suitInfo.ClothesList)
        awardList = append(awardList, award)
    }

    err := m.store.Transaction(ctx, func(tx mysql.Txx) error {
        err := m.store.BatchInsertAwardLog(ctx, tx, weddingReserveTime, awardList)
        if err != nil {
            log.ErrorWithCtx(ctx, "addUpgradeAwardLog fail to BatchInsertAwardLog. weddingId:%d, lv:%d, err:%v", weddingId, lv, err)
            return err
        }
        return nil
    })

    return err
}

// 判断A是否全部包含在B中
func containsAllIDs(partList, allList []uint32) bool {
    idMap := make(map[uint32]struct{})
    for _, id := range allList {
        idMap[id] = struct{}{}
    }
    for _, id := range partList {
        if _, exists := idMap[id]; !exists {
            return false
        }
    }
    return true
}

func (m *WeddingProcess) sendUserSuitWithUse(ctx context.Context, uid uint32, suitInfo *comm.UpgradeSuitInfo, weddingTime time.Time, allowUseSubCateMap map[uint32]bool) error {
    sendLimit, err := m.acLayerMgr.SendVirtualImageSuitWithUse(ctx, uid, suitInfo, allowUseSubCateMap)
    if err != nil {
        log.ErrorWithCtx(ctx, "sendUserSuitWithUse SendVirtualImageItem err(%v)", err)
        return err
    }

    awardStatus := store.AwardDone
    if sendLimit {
        awardStatus = store.AwardLimit
    }

    err = m.store.UpdateAwardLogStatus(ctx, suitInfo.OrderId, uint32(awardStatus), weddingTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "sendUserSuitWithUse UpdateAwardLogStatus err(%v)", err)
        return err
    }

    return nil
}

func (m *WeddingProcess) SendUpgradeClothesMsg(ctx context.Context, weddingInfo *pb.WeddingInfo, upgradeCloths map[uint32]*comm.UpgradeSuitInfo) error {
    weddingTime := time.Unix(weddingInfo.GetReserveTime(), 0)
    allowUseSubCateMap := make(map[uint32]bool)
    for _, sb := range m.bc.GetAllowChangeSubCategoryList() {
        allowUseSubCateMap[sb] = true
    }

    //新郎新娘直接发放&&使用新套装
    err := m.sendUserSuitWithUse(ctx, weddingInfo.GetGroom().GetUid(), upgradeCloths[weddingInfo.GetGroom().GetUid()], weddingTime, allowUseSubCateMap)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendUpgradeClothesMsg SendVirtualImageItem err(%v)", err)
        return err
    }

    err = m.sendUserSuitWithUse(ctx, weddingInfo.GetBride().GetUid(), upgradeCloths[weddingInfo.GetBride().GetUid()], weddingTime, allowUseSubCateMap)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendUpgradeClothesMsg SendVirtualImageItem err(%v)", err)
        return err
    }

    //伴郎伴娘发放新套装，不在麦上或者没有佩戴低级套装不使用
    for _, uid := range weddingInfo.GetBridesmaidManList() {

        err = m.sendUserSuitWithUse(ctx, uid, upgradeCloths[uid], weddingTime, nil)
        if err != nil {
            log.ErrorWithCtx(ctx, "handlerPresentEvent SendVirtualImageItem err(%v)", err)
            return err
        }
        log.InfoWithCtx(ctx, "SendUpgradeClothesMsg uid:%d,   newConfigCloths:%v", uid, upgradeCloths[uid])
    }
    return nil
}

func (m *WeddingProcess) genUpgradeClothes(ctx context.Context, weddingInfo *pb.WeddingInfo, newLevel uint32) (map[uint32]*comm.UpgradeSuitInfo, error) {
    mapUpgradeClothes := make(map[uint32]*comm.UpgradeSuitInfo)
    newLevelConfig, err := m.getWeddingThemeLevelCfg(ctx, weddingInfo.GetThemeCfg().GetThemeId(), newLevel)
    if err != nil {
        log.ErrorWithCtx(ctx, "genUpgradeClothes getWeddingThemeLevelCfg err(%v)", err)
        return mapUpgradeClothes, err
    }
    if newLevelConfig == nil {
        log.DebugWithCtx(ctx, "genUpgradeClothes levelConfig is nil")
        return mapUpgradeClothes, nil
    }

    lastLevelConfig, err := m.getWeddingThemeLevelCfg(ctx, weddingInfo.GetThemeCfg().GetThemeId(), newLevel-1)
    if err != nil {
        log.ErrorWithCtx(ctx, "genUpgradeClothes getWeddingThemeLevelCfg err(%v)", err)
        return mapUpgradeClothes, err
    }
    if lastLevelConfig == nil {
        log.DebugWithCtx(ctx, "SendUpgradeClothesMsg lastLevelConfig is nil")
        return mapUpgradeClothes, nil
    }

    //新郎新娘直接发放新套装
    newLvSuitMap := newLevelConfig.GetGuestDressCfgMap()
    groomUid := weddingInfo.GetGroom().GetUid()
    groomSuit := newLvSuitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_GROOM)].GetSuitCfg()
    mapUpgradeClothes[groomUid] = fillUpgradeSuitInfo(groomUid, 0, groomSuit,
        genSuitOrderId(weddingInfo.GetCid(), weddingInfo.GetWeddingId(), groomUid, newLevel), true)

    brideUid := weddingInfo.GetBride().GetUid()
    brideSuit := newLvSuitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDE)].GetSuitCfg()
    mapUpgradeClothes[brideUid] = fillUpgradeSuitInfo(brideUid, 0, brideSuit,
        genSuitOrderId(weddingInfo.GetCid(), weddingInfo.GetWeddingId(), brideUid, newLevel), true)

    //伴郎伴娘如果佩戴低级套装，并且在麦上，则发放新套装，否则不发放
    currentUse, err := m.acLayerMgr.GetUserVirtualImageInuseMap(ctx, weddingInfo.GetBridesmaidManList())
    if err != nil {
        log.ErrorWithCtx(ctx, "SendUpgradeClothesMsg GetUserVirtualImageInuseMap err(%v)", err)
        return mapUpgradeClothes, err
    }

    userMap, err := m.acLayerMgr.GetUserProfileMap(ctx, weddingInfo.GetBridesmaidManList(), true)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendUpgradeClothesMsg GetUserProfileMap err(%v)", err)
        return mapUpgradeClothes, err
    }

    onMicMap, err := m.acLayerMgr.GetOnMicUserList(ctx, weddingInfo.GetCid())
    if err != nil {
        log.ErrorWithCtx(ctx, "SendUpgradeClothesMsg fail to GetMicrList. channelId:%d, err:%v", weddingInfo.GetCid(), err)
        return mapUpgradeClothes, err
    }

    //remainDaysLimit := m.bc.GetBridesmaidSuitRemainDaysLimit()
    for _, uid := range weddingInfo.GetBridesmaidManList() {
        inUse, ok := currentUse[uid]
        if !ok {
            log.WarnWithCtx(ctx, "uid:%d, not inUse", uid)
            continue
        }

        userInfo, ok := userMap[uid]
        if !ok {
            log.WarnWithCtx(ctx, "uid:%d, not in userMap", uid)
            continue
        }

        //用户当前穿戴的id列表
        userItemList := make([]uint32, 0)
        for _, item := range inUse {
            userItemList = append(userItemList, item.GetCfgId())
        }

        lastLvSuitMap := lastLevelConfig.GetGuestDressCfgMap()

        //升级后的服装列表
        lastSuit := lastLvSuitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES)].GetSuitCfg()
        newSuit := newLvSuitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES)].GetSuitCfg()
        if userInfo.GetSex() == uint32(pbCommon.Sex_Female) {
            lastSuit = lastLvSuitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID)].GetSuitCfg()
            newSuit = newLvSuitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID)].GetSuitCfg()
        }

        mapUpgradeClothes[uid] = fillUpgradeSuitInfo(uid, 0, newSuit, genSuitOrderId(weddingInfo.GetCid(), weddingInfo.GetWeddingId(), uid, newLevel), false)

        if _, ok := onMicMap[uid]; !ok {
            continue
        }

        //如果佩戴低级套装，则发放新套装
        if lastSuit != nil && containsAllIDs(lastSuit.GetItemIds(), userItemList) {
            //发放新套装
            mapUpgradeClothes[uid].InUse = true
        } else {
            log.DebugWithCtx(ctx, "uid:%d, not containsAllIDs, weddingId:%d, lastSuit:%v, userItemList:%v", uid, weddingInfo.GetWeddingId(), lastSuit, userItemList)
        }
    }
    log.InfoWithCtx(ctx, "wedding id:%d, genUpgradeClothes mapClothes:%+v", weddingInfo.GetWeddingId(), mapUpgradeClothes)
    return mapUpgradeClothes, nil
}

func (m *WeddingProcess) getWeddingThemeLevel(ctx context.Context, cid, weddingId uint32) (uint32, error) {
    currentScore, err := m.cache.GetHappiness(ctx, cid, weddingId)
    if err != nil {
        log.ErrorWithCtx(ctx, "getWeddingThemeLevel fail to GetHappiness. channelId:%d, weddingId:%d, err:%v", cid, weddingId, err)
        return 0, err
    }
    lv := m.bc.GetHappinessLevelByScore(currentScore)
    return lv, nil
}


// happinessExtraSuitAward 额外服装时长奖励
func (m *WeddingProcess) happinessExtraSuitAward(ctx context.Context, weddingInfo *pb.WeddingInfo, cfg *conf.HappinessConfig, preScore, newScore uint32) error {
    if cfg == nil || len(cfg.ExtraSuitAwardList) == 0 {
        return nil
    }

    channelId := weddingInfo.GetCid()
    weddingId := weddingInfo.GetWeddingId()
    var groomBrideExtraSec, otherExtraSec, minVal uint32

    for _, award := range cfg.ExtraSuitAwardList {
        if preScore < award.MinVal && newScore >= award.MinVal {
            groomBrideExtraSec += award.GroomBrideExtraSec
            otherExtraSec += award.OtherExtraSec
            minVal = award.MinVal
        }
    }

    if groomBrideExtraSec == 0 && otherExtraSec == 0 {
        return nil
    }

    // 生成额外服装
    mapClothes, err := m.genExtraClothes(ctx, weddingInfo, cfg.Level, minVal, groomBrideExtraSec, otherExtraSec)
    if err != nil {
        log.ErrorWithCtx(ctx, "happinessExtraSuitAward genExtraClothes err(%v)", err)
        return err
    }

    // 写入奖励记录
    err = m.addUpgradeAwardLog(ctx, uint32(weddingId), cfg.Level, time.Unix(weddingInfo.GetReserveTime(), 0), mapClothes)
    if err != nil {
        log.ErrorWithCtx(ctx, "happinessExtraSuitAward fail to addUpgradeAwardLog. channelId:%d, weddingId:%d, score:%d, err:%v",
            channelId, weddingId, newScore, err)
        return err
    }

    // 发送tt助手消息
    go func() {
        newCtx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, 5*time.Second)
        defer cancel()

        for uid, suitInfo := range mapClothes {
            if suitInfo.Duration == 0 || len(suitInfo.ClothesList) == 0 {
                log.WarnWithCtx(newCtx, "happinessExtraSuitAward suitInfo.ClothesList is empty. channelId:%d, weddingId:%d, uid:%d", channelId, weddingId, uid)
                continue
            }
            text := fmt.Sprintf("婚礼幸福值提升至%d，此次婚礼套装「%s」有效期延长%d天~ 去“虚拟形象”-“我的物品”看看吧", minVal, suitInfo.Name, suitInfo.Duration/86400)
            _ = m.acLayerMgr.SimpleSendTTAssistantText(newCtx, uid, text, "", "")
        }
    }()

    log.InfoWithCtx(ctx, "happinessExtraSuitAward success. channelId:%d, weddingId:%d, score:%d, groomBrideExtraSec:%d, otherExtraSec:%d",
        channelId, weddingId, newScore, groomBrideExtraSec, otherExtraSec)
    return nil
}

func (m *WeddingProcess) genExtraClothes(ctx context.Context, weddingInfo *pb.WeddingInfo, level, minVal, groomBrideExtraSec, otherExtraSec uint32) (map[uint32]*comm.UpgradeSuitInfo, error) {
    mapClothes := make(map[uint32]*comm.UpgradeSuitInfo)
    levelConfig, err := m.getWeddingThemeLevelCfg(ctx, weddingInfo.GetThemeCfg().GetThemeId(), level)
    if err != nil {
        log.ErrorWithCtx(ctx, "genUpgradeClothes getWeddingThemeLevelCfg err(%v)", err)
        return mapClothes, err
    }
    if levelConfig == nil {
        log.DebugWithCtx(ctx, "genUpgradeClothes levelConfig is nil")
        return mapClothes, nil
    }

    //新郎新娘套装
    newLvSuitMap := levelConfig.GetGuestDressCfgMap()
    groomUid := weddingInfo.GetGroom().GetUid()
    groomSuit := newLvSuitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_GROOM)].GetSuitCfg()
    mapClothes[groomUid] = fillExtraSuitInfo(groomUid, 0, groomBrideExtraSec, groomSuit,
        genExtraSuitOrderId(weddingInfo.GetCid(), weddingInfo.GetWeddingId(), groomUid, level, minVal))

    brideUid := weddingInfo.GetBride().GetUid()
    brideSuit := newLvSuitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDE)].GetSuitCfg()
    mapClothes[brideUid] = fillExtraSuitInfo(brideUid, 0, groomBrideExtraSec, brideSuit,
        genExtraSuitOrderId(weddingInfo.GetCid(), weddingInfo.GetWeddingId(), brideUid, level, minVal))

    if len(weddingInfo.GetBridesmaidManList()) == 0 {
        return mapClothes, nil
    }

    userMap, err := m.acLayerMgr.GetUserProfileMap(ctx, weddingInfo.GetBridesmaidManList(), true)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendUpgradeClothesMsg GetUserProfileMap err(%v)", err)
        return mapClothes, err
    }

    //伴郎伴娘套装
    //remainDaysLimit := m.bc.GetBridesmaidSuitRemainDaysLimit()
    for _, uid := range weddingInfo.GetBridesmaidManList() {
        suit := newLvSuitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES)].GetSuitCfg()
        if userMap[uid].GetSex() == uint32(pbCommon.Sex_Female) {
            suit = newLvSuitMap[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID)].GetSuitCfg()
        }

        mapClothes[uid] = fillExtraSuitInfo(uid, 0, otherExtraSec, suit,
            genExtraSuitOrderId(weddingInfo.GetCid(), weddingInfo.GetWeddingId(), uid, level, minVal))
    }

    return mapClothes, nil
}

func genExtraSuitOrderId(cid uint32, weddingId int64, uid, lv, minVal uint32) string {
    return fmt.Sprintf("wedding_%d_%d_%d_%d_%d", cid, weddingId, uid, lv, minVal)
}

func fillExtraSuitInfo(uid, remainDaysLimit, extraSec uint32, suit *channel_wedding_conf.GuestSuitCfg, orderId string) *comm.UpgradeSuitInfo {
    if suit == nil || extraSec == 0 {
        return &comm.UpgradeSuitInfo{}
    }

    out := &comm.UpgradeSuitInfo{
        Uid:             uid,
        Name:            suit.GetName(),
        ClothesList:     suit.GetItemIds(),
        Icon:            suit.GetIcon(),
        InUse:           false,
        Duration:        extraSec,
        OrderId:         orderId,
        RemainDaysLimit: remainDaysLimit,
    }
    return out
}