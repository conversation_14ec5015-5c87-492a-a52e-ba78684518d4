module golang.52tt.com

go 1.23.0

replace (
	github.com/Shopify/sarama => github.com/Shopify/sarama v1.27.2
	github.com/apache/thrift => github.com/apache/thrift v0.13.0
	github.com/gin-gonic/gin => github.com/gin-gonic/gin v1.8.1
	github.com/go-sql-driver/mysql => github.com/go-sql-driver/mysql v1.5.0
	github.com/gogo/protobuf => github.com/gogo/protobuf v1.3.1

	github.com/jmoiron/sqlx => github.com/jmoiron/sqlx v1.2.0
	//github.com/golang/protobuf => github.com/golang/protobuf v1.3.5
	//github.com/prometheus/client_golang => github.com/prometheus/client_golang v1.5.1
	go.etcd.io/bbolt => github.com/etcd-io/bbolt v1.3.2
	go.mongodb.org/mongo-driver => github.com/mongodb/mongo-go-driver v1.5.3
	golang.org/x/crypto => github.com/golang/crypto v0.0.0-20190308221718-c2843e01d9a2
	//golang.org/x/exp => github.com/golang/exp v0.0.0-20190121172915-509febef88a4
	golang.org/x/image => github.com/golang/image v0.0.0-20190802002840-cff245a6509b
	golang.org/x/lint => github.com/golang/lint v0.0.0-20181026193005-c67002cb31c3
	//golang.org/x/net => github.com/golang/net v0.0.0-20190320064053-1272bf9dcd53
	golang.org/x/oauth2 => github.com/golang/oauth2 v0.0.0-20180821212333-d2e6202438be
	golang.org/x/sync => github.com/golang/sync v0.0.0-20181108010431-42b317875d0f
	//golang.org/x/sys => github.com/golang/sys v0.0.0-20190219203350-90b0e4468f99 // indirect
	//golang.org/x/text => github.com/golang/text v0.3.0
	golang.org/x/time => github.com/golang/time v0.0.0-20190308202827-9d24e82272b4
	//golang.org/x/tools => github.com/golang/tools v0.0.0-20180221164845-07fd8470d635

	google.golang.org/grpc => google.golang.org/grpc v1.73.0
	//google.golang.org/genproto => github.com/google/go-genproto v0.0.0-20190307195333-5fe7a883aa19
	//google.golang.org/grpc => github.com/grpc/grpc-go v1.26.0
	gopkg.in/yaml.v2 => github.com/go-yaml/yaml v0.0.0-20181115110504-51d6538a90f8
	purifiers/cybros => gitlab.ttyuyin.com/purifiers/cybros v1.0.7
	push => gitlab.ttyuyin.com/avengers/push v1.20.4-24
)

require (
	gitlab.ttyuyin.com/avengers/tyr v1.3.0
	gitlab.ttyuyin.com/tt-infra/tyr v1.2.0-alpha.16
	gitlab.ttyuyin.com/tyr/grpc-plugin v1.1.3-alpha.1 // indirect
	gitlab.ttyuyin.com/tyr/proto-alias v1.0.6 // indirect
	gitlab.ttyuyin.com/tyr/tt-ecosystem v1.1.5-alpha.8
	gitlab.ttyuyin.com/tyr/x v1.1.3
)

require (
	github.com/IBM/sarama v1.43.2
	github.com/buger/jsonparser v1.1.1
	github.com/coocood/freecache v1.2.4
	github.com/disintegration/imaging v1.6.2
	github.com/fullstorydev/grpcurl v1.9.3
	github.com/h2non/filetype v1.1.3
	github.com/jhump/protoreflect v1.17.0
	github.com/joho/godotenv v1.5.1
	github.com/mark3labs/mcp-go v0.38.0
	github.com/vingarcia/ksql v1.12.3
	github.com/vingarcia/ksql/adapters/kmysql v1.12.3
	gitlab.ttyuyin.com/bizFund/bizFund v1.0.17-alpha.audit.1
	gitlab.ttyuyin.com/bylink_data/bylink-sdk-go v0.0.0-**************-565bc31b06da
	gitlab.ttyuyin.com/discovery/apollo_sdk v1.1.0
	gitlab.ttyuyin.com/gengo/bizplatform-ttinfra v0.0.0-**************-ee8314075cd3
	gitlab.ttyuyin.com/gengo/t-bank v0.0.0-**************-e3def68500ad
	gitlab.ttyuyin.com/golang/gudetama v1.0.1
	gitlab.ttyuyin.com/golang/hyperion v1.2.3
	gitlab.ttyuyin.com/golang/structs v1.1.1
	gitlab.ttyuyin.com/golang/svrkit v1.2.5
	gitlab.ttyuyin.com/hyperion-ecosystem/carpool v1.0.7
	gitlab.ttyuyin.com/tt-infra/middleware v1.2.2
	gitlab.ttyuyin.com/tt-infra/moirae v1.0.7
	gitlab.ttyuyin.com/tyr/http-plugin v1.1.3
	go.etcd.io/etcd/client/v3 v3.5.14
	purifiers/cybros v0.0.0-**************-************
	push v0.0.0-**************-************
)

require (
	github.com/99designs/keyring v1.1.6 // indirect
	github.com/AthenZ/athenz v1.10.39 // indirect
	github.com/DataDog/zstd v1.5.0 // indirect
	github.com/Microsoft/go-winio v0.6.2 // indirect
	github.com/TarsCloud/TarsGo v1.1.5 // indirect
	github.com/alicebob/gopher-json v0.0.0-**************-a9ecdc9d1d3a // indirect
	github.com/apache/pulsar-client-go/oauth2 v0.0.0-**************-25e59572242e // indirect
	github.com/ardielle/ardielle-go v1.5.2 // indirect
	github.com/bahlo/generic-list-go v0.2.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bradfitz/gomemcache v0.0.0-20230905024940-24af94b03874 // indirect
	github.com/bufbuild/protocompile v0.14.1 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/cncf/xds/go v0.0.0-20250326154945-ae57f3c0d45f // indirect
	github.com/containerd/containerd v1.5.8 // indirect
	github.com/coreos/go-semver v0.3.0 // indirect
	github.com/coreos/go-systemd/v22 v22.3.2 // indirect
	github.com/cpuguy83/go-md2man/v2 v2.0.3 // indirect
	github.com/danieljoos/wincred v1.0.2 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/docker/distribution v2.7.1+incompatible // indirect
	github.com/docker/docker v20.10.12+incompatible // indirect
	github.com/docker/go-connections v0.4.0 // indirect
	github.com/docker/go-units v0.4.0 // indirect
	github.com/dvsekhvalnov/jose2go v0.0.0-20200901110807-248326c1351b // indirect
	github.com/eapache/go-resiliency v1.6.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20230731223053-c322873962e3 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/envoyproxy/go-control-plane/envoy v1.32.4 // indirect
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/go-jose/go-jose/v4 v4.0.5 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.19.0 // indirect
	github.com/go-stack/stack v1.8.0 // indirect
	github.com/goccy/go-json v0.10.2 // indirect
	github.com/godbus/dbus v0.0.0-20190726142602-4481cbc300e2 // indirect
	github.com/golang/freetype v0.0.0-20170609003504-e2365dfdc4a0 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/gomodule/redigo v2.0.0+incompatible // indirect
	github.com/google/go-cmp v0.7.0 // indirect
	github.com/google/gofuzz v1.2.0 // indirect
	github.com/googleapis/gnostic v0.5.5 // indirect
	github.com/gopherjs/gopherjs v0.0.0-20181017120253-0766667cb4d1 // indirect
	github.com/gorilla/websocket v1.5.0 // indirect
	github.com/gsterjov/go-libsecret v0.0.0-20161001094733-a6f4afe4910c // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/go-uuid v1.0.3 // indirect
	github.com/hashicorp/golang-lru v0.5.4 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/imdario/mergo v0.3.12 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/invopop/jsonschema v0.13.0 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.7.6 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.4 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/keybase/go-keychain v0.0.0-20190712205309-48d3d31d256d // indirect
	github.com/klauspost/compress v1.17.10 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/lib/pq v1.10.9 // indirect
	github.com/linkedin/goavro/v2 v2.9.8 // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.4 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/mtibben/percent v0.2.1 // indirect
	github.com/nxadm/tail v1.4.8 // indirect
	github.com/opencontainers/go-digest v1.0.0 // indirect
	github.com/opencontainers/image-spec v1.0.2 // indirect
	github.com/openzipkin/zipkin-go v0.4.0 // indirect
	github.com/pelletier/go-toml/v2 v2.2.0 // indirect
	github.com/pierrec/lz4 v2.6.1+incompatible // indirect
	github.com/pierrec/lz4/v4 v4.1.21 // indirect
	github.com/planetscale/vtprotobuf v0.6.1-0.20240319094008-0393e58bdf10 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.44.0 // indirect
	github.com/prometheus/procfs v0.11.1 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.3 // indirect
	github.com/rifflock/lfshook v0.0.0-20180920164130-b9218ef580f5 // indirect
	github.com/russross/blackfriday/v2 v2.1.0 // indirect
	github.com/sagikazarmark/locafero v0.3.0 // indirect
	github.com/sagikazarmark/slog-shim v0.1.0 // indirect
	github.com/shiena/ansicolor v0.0.0-20151119151921-a422bbe96644 // indirect
	github.com/shirou/gopsutil v3.20.11+incompatible // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/smartystreets/assertions v1.2.0 // indirect
	github.com/sourcegraph/conc v0.3.0 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spf13/afero v1.10.0 // indirect
	github.com/spf13/cast v1.7.1 // indirect
	github.com/spiffe/go-spiffe/v2 v2.5.0 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	github.com/tidwall/gjson v1.14.1 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/uber/jaeger-lib v2.4.1+incompatible // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	github.com/valyala/fastrand v1.0.0 // indirect
	github.com/wk8/go-ordered-map/v2 v2.1.8 // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	github.com/xuri/efp v0.0.0-20220603152613-6918739fd470 // indirect
	github.com/xuri/nfp v0.0.0-20220409054826-5e722a1d9e22 // indirect
	github.com/yosida95/uritemplate/v3 v3.0.2 // indirect
	github.com/youmark/pkcs8 v0.0.0-20201027041543-1326539a0a0a // indirect
	github.com/yuin/gopher-lua v1.1.1 // indirect
	github.com/zeebo/errs v1.4.0 // indirect
	gitlab.ttyuyin.com/discovery/agollo v1.0.19 // indirect
	gitlab.ttyuyin.com/discovery/discoveryagent_sdk v1.2.3 // indirect
	gitlab.ttyuyin.com/gengo/bizplatform-eventlink v0.0.1 // indirect
	gitlab.ttyuyin.com/gengo/infra-base v0.0.0-20240530014602-a1039a042b17 // indirect
	gitlab.ttyuyin.com/golang/hyperion/v2 v2.3.2 // indirect
	gitlab.ttyuyin.com/hyperion-ecosystem/psr-http v1.1.0 // indirect
	gitlab.ttyuyin.com/tt-infra/middleware/event-link v0.2.3 // indirect
	gitlab.ttyuyin.com/tyr/admin-plugin v1.1.1 // indirect
	go.etcd.io/etcd/api/v3 v3.5.14 // indirect
	go.etcd.io/etcd/client/pkg/v3 v3.5.14 // indirect
	go.opentelemetry.io/auto/sdk v1.1.0 // indirect
	go.opentelemetry.io/otel/metric v1.35.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/oauth2 v0.28.0 // indirect
	golang.org/x/sys v0.31.0 // indirect
	golang.org/x/term v0.30.0 // indirect
	golang.org/x/text v0.23.0 // indirect
	golang.org/x/time v0.5.0 // indirect
	google.golang.org/appengine v1.6.8 // indirect
	google.golang.org/genproto v0.0.0-20240213162025-012b6fc9bca9 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20250324211829-b45e905df463 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250324211829-b45e905df463 // indirect
	gopkg.in/inf.v0 v0.9.1 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/jcmturner/aescts.v1 v1.0.1 // indirect
	gopkg.in/jcmturner/dnsutils.v1 v1.0.1 // indirect
	gopkg.in/jcmturner/gokrb5.v7 v7.5.0 // indirect
	gopkg.in/jcmturner/rpc.v1 v1.1.0 // indirect
	gopkg.in/tomb.v1 v1.0.0-20141024135613-dd632973f1e7 // indirect
	gorm.io/driver/sqlite v1.4.4 // indirect
	k8s.io/klog/v2 v2.30.0 // indirect
	k8s.io/kube-openapi v0.0.0-20211115234752-e816edb12b65 // indirect
	k8s.io/utils v0.0.0-20210930125809-cb0fa318a74b // indirect
	sigs.k8s.io/json v0.0.0-20211020170558-c049b76a60c6 // indirect
	sigs.k8s.io/structured-merge-diff/v4 v4.1.2 // indirect
	sigs.k8s.io/yaml v1.2.0 // indirect
)

require (
	bou.ke/monkey v1.0.2
	github.com/360EntSecGroup-Skylar/excelize v1.4.1
	github.com/DATA-DOG/go-sqlmock v1.5.2
	github.com/Shopify/sarama v1.34.1
	github.com/agiledragon/gomonkey v2.0.2+incompatible
	github.com/alibaba/sentinel-golang v1.0.2
	github.com/alicebob/miniredis v2.5.0+incompatible
	github.com/alicebob/miniredis/v2 v2.33.0
	github.com/allegro/bigcache v1.2.1
	github.com/allegro/bigcache/v3 v3.1.0
	github.com/apache/pulsar-client-go v0.8.1
	github.com/apache/thrift v0.17.0
	github.com/astaxie/beego v1.12.3
	github.com/bearbin/go-age v0.0.0-20140407072555-316d0c1e7cd1
	github.com/bitly/go-simplejson v0.5.0
	github.com/bsm/sarama-cluster v2.1.15+incompatible
	github.com/deckarep/golang-set v1.8.0
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/elastic/go-elasticsearch/v6 v6.8.10
	github.com/elastic/go-elasticsearch/v7 v7.16.0
	github.com/envoyproxy/protoc-gen-validate v1.2.1
	github.com/faabiosr/cachego v0.16.3
	github.com/fogleman/gg v1.3.0
	github.com/forgoer/openssl v1.6.0
	github.com/fsnotify/fsnotify v1.7.0
	github.com/ghodss/yaml v1.0.0
	github.com/gin-gonic/gin v1.9.1
	github.com/globalsign/mgo v0.0.0-20181015135952-eeefdecb41b8
	github.com/go-echarts/go-echarts v1.0.0
	github.com/go-gomail/gomail v0.0.0-20160411212932-81ebce5c23df
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/go-redis/redis/v7 v7.4.1
	github.com/go-redis/redis/v8 v8.11.5
	github.com/go-sql-driver/mysql v1.8.1
	github.com/gogo/protobuf v1.3.2
	github.com/golang-jwt/jwt v3.2.2+incompatible
	github.com/golang/glog v1.2.4
	github.com/golang/mock v1.6.0
	github.com/golang/protobuf v1.5.4
	github.com/google/uuid v1.6.0
	github.com/google/wire v0.5.0
	github.com/gorilla/mux v1.8.1
	github.com/grpc-ecosystem/go-grpc-middleware v1.4.0
	github.com/grpc-ecosystem/grpc-gateway v1.16.0
	github.com/jarcoal/httpmock v1.2.0
	github.com/jinzhu/gorm v1.9.16
	github.com/jmoiron/sqlx v1.4.0
	github.com/json-iterator/go v1.1.12
	github.com/larksuite/botframework-go v0.0.0-20210409135442-ff14b99e324b
	github.com/larksuite/oapi-sdk-gin v1.0.0
	github.com/larksuite/oapi-sdk-go v1.1.48
	github.com/larksuite/oapi-sdk-go/v3 v3.4.7
	github.com/marspere/goencrypt v1.0.7
	github.com/mozillazg/go-pinyin v0.19.0
	github.com/onsi/ginkgo v1.16.5
	github.com/onsi/gomega v1.19.0
	github.com/opentracing/opentracing-go v1.2.0
	github.com/orlangure/gnomock v0.19.0
	github.com/panjf2000/ants v1.3.0
	github.com/patrickmn/go-cache v2.1.0+incompatible
	github.com/pingcap/goleveldb v0.0.0-20171020122428-b9ff6c35079e
	github.com/pingcap/tidb v0.0.0-20190910092858-603f0b95d391
	github.com/pkg/errors v0.9.1
	github.com/prometheus/client_golang v1.17.0
	github.com/qiniu/api.v7 v7.2.5+incompatible
	github.com/rafaeldias/async v1.0.0
	github.com/renstrom/go-jump-consistent-hash v1.0.1
	github.com/robfig/cron v1.2.0
	github.com/robfig/cron/v3 v3.0.1
	github.com/scylladb/go-set v1.0.2
	github.com/seiflotfy/cuckoofilter v0.0.0-20190302225222-764cb5258d9b
	github.com/shopspring/decimal v1.2.0
	github.com/smartystreets/goconvey v1.7.2
	github.com/spf13/cobra v1.8.0
	github.com/spf13/pflag v1.0.5
	github.com/spf13/viper v1.17.0
	github.com/stretchr/testify v1.10.0
	github.com/tealeg/xlsx v1.0.5
	github.com/uber/jaeger-client-go v2.30.0+incompatible
	github.com/urfave/cli v1.22.2
	github.com/urfave/cli/v2 v2.3.0
	github.com/wendal/errors v0.0.0-20130201093226-f66c77a7882b
	github.com/xuri/excelize/v2 v2.7.1
	github.com/zegoim/zego_server_assistant/token/go/src v0.0.0-20221125110009-900720e86da1
	github.com/zhashkevych/go-sqlxmock v1.5.1
	gitlab.ttyuyin.com/discovery/tarsgo v1.0.0
	gitlab.ttyuyin.com/mp-engineering-public/cs-sdk v1.1.2 // indirec
	go.mongodb.org/mongo-driver v1.15.0
	go.opentelemetry.io/otel v1.35.0
	go.opentelemetry.io/otel/exporters/zipkin v1.7.0
	go.opentelemetry.io/otel/sdk v1.35.0
	go.opentelemetry.io/otel/trace v1.35.0
	go.uber.org/atomic v1.11.0
	go.uber.org/zap v1.27.0
	golang.org/x/crypto v0.36.0
	golang.org/x/exp v0.0.0-20231006140011-7918f672742d
	golang.org/x/net v0.38.0
	golang.org/x/sync v0.12.0
	google.golang.org/grpc v1.73.0
	google.golang.org/protobuf v1.36.6
	gopkg.in/errgo.v2 v2.1.0
	gopkg.in/fatih/set.v0 v0.2.1
	gopkg.in/gomail.v2 v2.0.0-20160411212932-81ebce5c23df // indirect
	gopkg.in/mgo.v2 v2.0.0-20190816093944-a6b53ec6cb22
	gopkg.in/redis.v5 v5.2.9
	gopkg.in/stretchr/testify.v1 v1.2.2
	gopkg.in/yaml.v2 v2.4.0
	gopkg.in/yaml.v3 v3.0.1
	gorm.io/driver/mysql v1.5.6
	gorm.io/gorm v1.25.10
	k8s.io/api v0.23.1
	k8s.io/apimachinery v0.23.1
	k8s.io/client-go v0.23.1
	qiniupkg.com/x v7.0.8+incompatible
)

require (
	bitbucket.org/creachadair/shell v0.0.8
	github.com/Knetic/govaluate v3.0.0+incompatible
	github.com/StackExchange/wmi v0.0.0-20210224194228-fe8f1750fd46 // indirect
	github.com/aws/aws-sdk-go v1.49.22 // indirect
	github.com/dgryski/go-metro v0.0.0-20211217172704-adc40b04c140 // indirect
	github.com/galaxy-book/feishu-sdk-golang v0.4.6
	github.com/go-ole/go-ole v1.2.5 // indirect
	github.com/jinzhu/copier v0.4.0
	github.com/qiniu/x v7.0.8+incompatible // indirect
	github.com/silenceper/wechat/v2 v2.1.6
	github.com/thinkeridea/go-extend v1.3.2
	golang.org/x/image v0.5.0
	gopkg.in/alexcesaro/quotedprintable.v3 v3.0.0-20150716171945-2caba252f4dc // indirect
	gorm.io/datatypes v1.2.1
)
